import { NzMessageModule } from 'ng-zorro-antd/message';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule, TranslateLoader, TranslateService } from '@ngx-translate/core';
import { FlcComponentsModule, FlCommonLibService } from 'fl-common-lib';
import { FlUiAngularModule } from 'fl-ui-angular';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzIconModule, NzIconService } from 'ng-zorro-antd/icon';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import { GraphTableModule } from 'src/app/components/graph-table/graph-table.module';
import { ProductionPlanComponent } from './production-plan.component';
import { PlanGanttComponent } from './plan-gantt/plan-gantt.component';
import { LeftGanttWrapComponent } from './left-gantt-wrap/left-gantt-wrap.component';
import { GraphWrapComponent } from './graph-wrap/graph-wrap.component';
import { PlanGraphItemComponent } from './plan-graph-item/plan-graph-item.component';
import { ColorSettingComponent } from './color-setting/color-setting.component';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { ColorPickerModule } from '@iplab/ngx-color-picker';
import { DragDropModule } from '@angular/cdk/drag-drop';

const zorroComponents = [
  NzToolTipModule,
  NzButtonModule,
  NzIconModule,
  NzDividerModule,
  NzSelectModule,
  NzDatePickerModule,
  NzRadioModule,
  NzPopoverModule,
  NzFormModule,
  NzDrawerModule,
  NzGridModule,
  NzSpinModule,
  NzInputNumberModule,
  NzPopconfirmModule,
  NzMessageModule,
];

@NgModule({
  declarations: [
    ProductionPlanComponent,
    PlanGanttComponent,
    LeftGanttWrapComponent,
    GraphWrapComponent,
    PlanGraphItemComponent,
    ColorSettingComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    FlcComponentsModule,
    FlUiAngularModule,
    ...zorroComponents,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new MultiTranslateHttpLoader(http, [
            { prefix: './assets/i18n/elan-components/', suffix: '.json' },
            { prefix: './assets/i18n/plan/', suffix: '.json' },
          ]);
        },
        deps: [HttpClient],
      },
    }),
    GraphTableModule,
    ColorPickerModule,
    DragDropModule,
  ],
  providers: [FlCommonLibService],
})
export class ProductionPlanModule {
  constructor(public translateService: TranslateService, private iconService: NzIconService) {
    const cl = translateService.currentLang;
    translateService.currentLang = '';
    translateService.use(cl);

    this.iconService.fetchFromIconfont({
      scriptUrl: 'https://at.alicdn.com/t/c/font_2782026_cc7to7voms.js',
    });
  }
}
