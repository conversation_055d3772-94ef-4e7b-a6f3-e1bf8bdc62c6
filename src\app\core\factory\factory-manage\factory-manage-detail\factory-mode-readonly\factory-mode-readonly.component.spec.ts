import { ComponentFixture, TestBed } from '@angular/core/testing';

import { FactoryModeReadonlyComponent } from './factory-mode-readonly.component';

describe('FactoryModeReadonlyComponent', () => {
  let component: FactoryModeReadonlyComponent;
  let fixture: ComponentFixture<FactoryModeReadonlyComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [FactoryModeReadonlyComponent],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(FactoryModeReadonlyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
