{"env": {"browser": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 12, "sourceType": "module"}, "plugins": ["@typescript-eslint"], "ignorePatterns": "**/*.spec.ts", "rules": {"@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-explicit-any": "off", "quotes": ["error", "single"], "semi": [2, "always"], "no-console": 0, "space-before-blocks": 2, "switch-colon-spacing": 2, "keyword-spacing": 2, "space-infix-ops": 2, "key-spacing": ["error", {"beforeColon": false}], "arrow-spacing": ["error", {"before": true, "after": true}], "indent": [2, 2, {"VariableDeclarator": {"var": 2, "let": 2, "const": 3}, "SwitchCase": 1}], "no-trailing-spaces": "error", "no-irregular-whitespace": ["error", {"skipStrings": true, "skipComments": true, "skipRegExps": true, "skipTemplates": true}], "no-multi-spaces": ["error", {"ignoreEOLComments": false}], "comma-spacing": ["error", {"before": false, "after": true}], "object-curly-spacing": ["error", "always", {"objectsInObjects": true, "arraysInObjects": true}], "no-var": "error", "one-var": ["error", "never"]}}