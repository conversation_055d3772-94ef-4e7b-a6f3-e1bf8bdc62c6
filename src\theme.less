// Custom Theming for NG-ZORRO
// For more information: https://ng.ant.design/docs/customize-theme/en
@import '../node_modules/ng-zorro-antd/ng-zorro-antd.less';

// Override less variables to here
// View all variables: https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/components/style/themes/default.less

@import '../node_modules/ng-zorro-antd/resizable/style/entry.less';

// 引入fl-ui-angular组件库样式
@import '../node_modules/fl-ui-angular/style.less';

@import 'ag-grid-community/styles/ag-grid.css';
@import 'ag-grid-community/styles/ag-theme-alpine.css';

//系统字体黑色
@text-color: #222b3c;

//系统主颜色
@primary-color: #4d96ff;

//系统背景灰色
@background-color-base: #eff0f4;

//系统增强色
@fl-pretty-color: #00c790;
@fl-btn-pretty-color: @fl-pretty-color;

//系统danger颜色
@error-color: #fe5d56;

/*
********** override table style **********
*/
@table-border-color: #d4d7dc;
@table-border-radius-base: 4px;
@table-header-bg: #f9fafb;
@table-padding-vertical: 4px;
@table-padding-horizontal: 8px;
@table-row-hover-bg: #deefff; // 表格内容鼠标悬停背景色;

/* override nz-modal style */
@modal-header-padding: 12px 16px;
@modal-header-close-size: 46px;
@modal-body-padding: 16px;
@modal-footer-padding-vertical: 8px;

/* override nz-drawer style */
@drawer-header-padding: 12px 16px;
@drawer-body-padding: 16px;

/* 全局css变量（用于传递antd theme变量） */
:root {
  --nz-table-border-color: @table-border-color;
  --nz-table-row-hover-bg: @table-row-hover-bg;
}
