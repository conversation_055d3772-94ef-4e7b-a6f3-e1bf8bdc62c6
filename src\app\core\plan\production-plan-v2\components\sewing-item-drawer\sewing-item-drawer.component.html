<nz-spin [nzSpinning]="isSpinning">
  <div *ngIf="sewingData" class="sewing-line-drawer">
    <!-- 详情基础数据 -->
    <div class="sewing-base-info">
      <div class="sewing-base-info-header">
        <div style="display: flex; gap: 10px; align-items: center; flex: 69%">
          <div class="img-item" *ngIf="sewingData?.order_pictures?.length">
            <img [src]="sewingData.order_pictures?.[0]?.url" />
          </div>

          <div style="display: flex; flex-direction: column; width: 75%">
            <div style="display: flex; align-items: center">
              <flc-text-truncated style="font-size: 14px; color: #354060" [data]="sewingData.order_code"></flc-text-truncated>
              <div class="publish-status-tag tobe-publish-tag" *ngIf="sewingData.publish_status === publish_status.unpublished">待发布</div>
              <div class="publish-status-tag published-tag" *ngIf="sewingData.publish_status === publish_status.published">已发布</div>
            </div>
            <div style="margin-top: -6px">
              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: -12px">
                <div style="font-size: 12px; color: #2996ff">{{ sewingData.sewing_precent | flcNoValue }}%</div>
                <div style="font-size: 12px; color: #707070">
                  {{ (sewingData.sewing_qty | flcNoValue) + '/' + (sewingData.allocated_qty | flcNoValue) }}
                </div>
              </div>
              <nz-progress
                [nzPercent]="sewingData.sewing_precent"
                [nzShowInfo]="false"
                nzSize="small"
                [nzStrokeColor]="{ '0%': '#2996FF', '100%': '#53C7FF' }">
              </nz-progress>
            </div>
          </div>
        </div>

        <div colume-no-start style="color: #007aff; font-size: 12px; justify-content: center; padding-top: 6px">
          <div>
            <span>{{ sewingData.factory_name || '-' }}</span>
            <span *ngIf="sewingData.production_line_name"> / {{ sewingData.production_line_name }}</span>
          </div>
        </div>
      </div>
      <div class="sewing-other-container" colume-no-start>
        <div class="info-item">
          <div class="text-label">计划起止</div>
          <div class="text-content">
            <span>
              <ng-container *ngIf="sewingData.plan_start_time">
                {{ sewingData.plan_start_time | date: 'yyyy/MM/dd' }}
              </ng-container>
              <ng-container *ngIf="!sewingData.plan_start_time"> 未开始 </ng-container>
            </span>
            <i nz-icon nzType="swap-right" nzTheme="outline"></i>
            <span>
              <ng-container *ngIf="sewingData.plan_end_time">
                {{ sewingData.plan_end_time | date: 'yyyy/MM/dd' }}
              </ng-container>
              <ng-container *ngIf="!sewingData.plan_end_time"> 未完成 </ng-container>
            </span>
          </div>
        </div>
        <div class="info-item">
          <div class="text-label">实际起止</div>
          <div class="text-content">
            <span>
              <ng-container *ngIf="sewingData.actual_start_time">
                {{ sewingData.actual_start_time | date: 'yyyy/MM/dd' }}
              </ng-container>
              <ng-container *ngIf="!sewingData.actual_start_time"> 未开始 </ng-container>
            </span>
            <i nz-icon nzType="swap-right" nzTheme="outline"></i>
            <span>
              <ng-container *ngIf="sewingData.actual_end_time">
                {{ sewingData.actual_end_time | date: 'yyyy/MM/dd' }}
              </ng-container>
              <ng-container *ngIf="!sewingData.actual_end_time">未完成 </ng-container>
            </span>
          </div>
        </div>
        <div class="info-item">
          <div class="text-label">分配数</div>
          <div class="text-content">{{ sewingData.allocated_qty | number | flcNoValue }}</div>
        </div>
        <div class="info-item">
          <div class="text-label">实际车缝产出</div>
          <div class="text-content">{{ sewingData.sewing_qty | number | flcNoValue }}</div>
        </div>

        <div class="info-item">
          <div class="text-label">完成率</div>
          <div class="text-content">
            <ng-container *ngIf="sewingData?.allocated_qty === null; else progressTpl">
              {{ '-' }}
            </ng-container>
            <ng-template #progressTpl> {{ sewingData.sewing_precent | flcNoValue }}% </ng-template>
          </div>
        </div>

        <div class="info-item">
          <div class="text-label">订单总件数</div>
          <div class="text-content">{{ sewingData.order_qty | number | flcNoValue }}</div>
        </div>

        <div class="info-item">
          <div class="text-label">交期</div>
          <div class="text-content">{{ sewingData.due_times_str | flcNoValue }}</div>
        </div>

        <div class="info-item">
          <div class="text-label">物料齐套日期</div>
          <div class="text-content">{{ sewingData.pre_material_completed_time || null | date: 'yyyy/MM/dd' | flcNoValue }}</div>
        </div>

        <div class="info-item">
          <div class="text-label">款式分类</div>
          <flc-text-truncated class="text-content" [data]="sewingData.style_class"></flc-text-truncated>
        </div>

        <div class="info-item">
          <div class="text-label">客户名称</div>
          <flc-text-truncated class="text-content" [data]="sewingData.customer"></flc-text-truncated>
        </div>
        <div class="info-item">
          <div class="text-label">业务员</div>
          <div class="text-content">{{ sewingData.dept_name | flcNoValue }} / {{ sewingData.employee_name | flcNoValue }}</div>
        </div>
      </div>
    </div>

    <nz-divider></nz-divider>

    <div style="margin-bottom: 10px">
      <div class="info-item" *ngIf="sewingData.selected === orderCalculationTypeEnum.average">
        <div class="text-label">人均日台产</div>
        <div class="text-content">
          <ng-container *ngIf="!isEditInput">
            <ng-container *ngIf="!isEditInput">
              {{ sewingData.average_daily_production | flcNoValue }}件
              <i *ngIf="isEdit" class="edit-icon" nz-icon (click)="onEdit()" nzType="edit" nzTheme="outline"></i>
            </ng-container>
          </ng-container>
          <ng-container *ngIf="isEditInput">
            <nz-input-number
              [(ngModel)]="inputValue"
              [nzMin]="1"
              [nzStep]="1"
              [nzPrecision]="0"
              [nzMax]="99999999"
              [nzSize]="'small'"></nz-input-number>
            件
            <div class="sure-btn">
              <button nz-button nzType="text" (click)="onChangeInput(false)" [flcDisableOnClick]="1000">取消</button>
              <button nz-button nzType="link" (click)="onChangeInput(true)" [flcDisableOnClick]="1000">保存</button>
            </div>
          </ng-container>
        </div>
      </div>

      <ng-container *ngIf="sewingData.selected === orderCalculationTypeEnum.sam">
        <div class="info-item">
          <div class="text-label">SAM</div>
          <div class="text-content">{{ sewingData.sam | flcNoValue }}分钟</div>
        </div>
        <div class="info-item">
          <div class="text-label">目标效率</div>
          <div class="text-content">
            <ng-container *ngIf="!isEditInput">
              {{ sewingData.efficiency * 100 | flcNoValue }}%
              <i *ngIf="isEdit" class="edit-icon" nz-icon (click)="onEdit()" nzType="edit" nzTheme="outline"></i>
            </ng-container>
            <ng-container *ngIf="isEditInput">
              <nz-input-number
                [(ngModel)]="inputValue"
                [nzMin]="0.01"
                [nzStep]="0.01"
                [nzPrecision]="2"
                [nzMax]="1"
                [nzSize]="'small'"></nz-input-number>
              <div class="sure-btn">
                <button nz-button nzType="text" (click)="onChangeInput(false)" [flcDisableOnClick]="1000">取消</button>
                <button nz-button nzType="link" (click)="onChangeInput(true)" [flcDisableOnClick]="1000">保存</button>
              </div>
            </ng-container>
          </div>
        </div>
      </ng-container>
    </div>

    <div>
      <div class="production_line_title">
        <div class="production_line_name">
          {{ (sewingData.factory_name | flcNoValue) + '/' + (sewingData.production_line_name | flcNoValue) }}
        </div>
        <nz-divider nzType="vertical"></nz-divider>
        <div>人数</div>
        <div>{{ sewingData.user_count | flcNoValue }}人</div>
        <nz-divider nzType="vertical"></nz-divider>
        <div>每日工作时长</div>
        <div>{{ sewingData.daily_work_hours | flcNoValue }}小时</div>
      </div>
      <nz-table
        #poTable
        [nzData]="sewingData.production_list"
        [nzFrontPagination]="false"
        class="po-table"
        [nzScroll]="{ x: '100%', y: '300px' }">
        <thead>
          <tr>
            <th>日期</th>
            <th>每日目标产量</th>
            <th>每日实际产量</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of poTable.data">
            <td>
              <div class="rest-tag">
                {{ data.date | date: 'yyyy/MM/dd' }}
                <span *ngIf="data.is_break">休</span>
              </div>
            </td>
            <td>{{ data.plan_qty | number | flcNoValue }}</td>
            <td>{{ data.actual_qty | number | flcNoValue }}</td>
          </tr>
          <tr class="total-tr">
            <td>总计</td>
            <td>{{ totalObject.production.plan_qty | number }}</td>
            <td>{{ totalObject.production.actual_qty | number }}</td>
          </tr>
        </tbody>
      </nz-table>
    </div>

    <nz-divider></nz-divider>
    <!-- 分配详情 -->
    <div>
      <div class="subtitle-text">分配详情</div>
      <nz-table
        #allocateTable
        [nzData]="sewingData.allocate_list"
        [nzFrontPagination]="false"
        class="po-table"
        [nzScroll]="{ x: '100%', y: '300px' }">
        <thead>
          <tr>
            <th>交付单</th>
            <th>颜色</th>
            <th>订单数</th>
            <th>分配数</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of allocateTable.data">
            <td>
              <flc-text-truncated data="{{ data.po_code }}"></flc-text-truncated>
            </td>
            <td>
              <flc-text-truncated data="{{ data.color_name }}"></flc-text-truncated>
            </td>
            <td>{{ data.order_qty | number | flcNoValue }}</td>
            <td>{{ data.allocated_qty | number | flcNoValue }}</td>
          </tr>
          <tr class="total-tr">
            <td>总计</td>
            <td></td>
            <td>{{ totalObject.allocation.order_qty | number }}</td>
            <td>{{ totalObject.allocation.allocated_qty | number }}</td>
          </tr>
        </tbody>
      </nz-table>
    </div>
  </div>
</nz-spin>
