// 状态
.status {
  display: inline-block;
  height: 24px;
  line-height: 24px;
  padding: 0 8px;
  border-radius: 12px;
  margin-left: 6px;
  font-size: 14px;
}

.status1 {
  background: #e7f3fe;
  color: #138aff;
}
// 已取消
.status-1 {
  background-color: #e8eef4;
  color: #97999c;
}

.status7 {
  color: #515665;
  background: #e8eef4;
}

// 待审核 、 待接单 、 待收样
.status2,
.status4,
.status5,
.status6 {
  background-color: #feefe5;
  color: #fb6401;
}

// 待修改
.status3 {
  background-color: #ffeee9;
  color: #ff4a1d;
}

// 历史版本
.history-list {
  display: flex;
  height: 32px;
  align-items: center;
  background: #f1f7ff;
  border-radius: 16px;
  padding: 0 12px;
  span:first-child {
    font-size: 12px;
    color: #545f7c;
  }
  span:last-child {
    display: block;
    padding: 0 10px;
    margin-left: 6px;
    font-size: 12px;
    line-height: 20px;
    height: 22px;
    border-radius: 15px;
    border: 1px solid #138aff;
    color: #138aff;
    background: #fff;
    cursor: pointer;
  }
}

// 原因
.reason-box {
  display: flex;
  align-items: center;
  height: 40px;
  padding: 0 10px;
  border-radius: 6px 6px 0px 0px;
  background-color: #feecec;
  color: #f74949;

  .reason-icon {
    margin-right: 6px;
  }

  .status-reason {
    display: flex;
    flex: 1;
  }
}
