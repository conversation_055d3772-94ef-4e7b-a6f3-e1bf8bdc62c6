import { SampleOutsouringStatusEnum } from '../modal/sample-outsourcing.enum';

// 检索表单下拉数据来源
export const initialSearchOptionUrl = '/service/scm/sample_order/list_option';

// 搜索表单配置
export const searchOptions = [
  {
    label: '打样单号',
    column: 'sample_order',
    type: 'select',
    valueKey: 'sample_order_id',
  },
  {
    label: '款式编码',
    column: 'style_code',
    type: 'select',
    valueKey: 'style_code',
  },
  {
    label: '品名',
    type: 'input',
    valueKey: 'category',
  },
  {
    label: '款式分类',
    type: 'cascader',
    valueKey: 'style_id',
  },
  {
    label: '设计师',
    column: 'designer',
    type: 'select',
    valueKey: 'designer_id',
  },
  {
    label: '外发工厂',
    column: 'factory',
    type: 'select',
    valueKey: 'factory_id',
  },
  {
    label: '外发类型',
    column: 'process_type',
    type: 'select',
    valueKey: 'process_type',
  },
  {
    label: '样板类型',
    column: 'sample_type',
    type: 'select',
    valueKey: 'sample_type',
  },
  {
    label: '期望交付日期',
    type: 'date',
    valueKey: 'expected_delivery_date',
  },
  {
    label: '创建人',
    column: 'gen_user',
    type: 'select',
    valueKey: 'gen_user',
  },
  {
    label: '创建时间',
    type: 'date',
    valueKey: 'gen_time',
  },
] as const;

export function getStatusColorStyle(item: any) {
  const styleObj: any = {};
  switch (item.status) {
    case SampleOutsouringStatusEnum.wait_submit: //'待提交'
      styleObj.color = '#138AFF';
      break;
    case SampleOutsouringStatusEnum.wait_audit: //'待审核'
      styleObj.color = '#FB6401';
      break;
    case SampleOutsouringStatusEnum.wait_modify: //'待修改'
      styleObj.color = '#FF4A1D';
      break;
    case SampleOutsouringStatusEnum.wait_order: //'待接单'
      styleObj.color = '#FB6401';
      break;
    case SampleOutsouringStatusEnum.wait_outsourcing_send: //'待收样-未寄样',
      styleObj.color = '#FB6401';
      break;
    case SampleOutsouringStatusEnum.wait_outsourcing_sended: //'待收样-已寄样',
      styleObj.color = '#FB6401';
      break;
    case SampleOutsouringStatusEnum.processed: //'已收样'
      styleObj.color = '#515665';
      break;
    case SampleOutsouringStatusEnum.cancelled: // '已取消'
      styleObj.color = '#97999C';
      break;
  }
  return styleObj;
}

// 列表colume配置
export function initialTableHeader() {
  return [
    {
      key: 'sample_order_no',
      label: '打样单号',
      width: '168px',
      type: 'text',
      pinned: true,
      isHidePin: true,
      disable: true,
    },
    {
      key: 'style_code',
      label: '款式编码',
      width: '120px',
      type: 'text',
    },
    {
      key: 'category',
      label: '品名',
      width: '120px',
      type: 'text',
    },
    {
      key: 'first_style_name',
      label: '款式分类',
      width: '160px',
      type: 'template',
      templateName: 'first_style_name',
    },
    {
      key: 'style_pic_list',
      label: '图片',
      width: '64px',
      type: 'image',
    },
    {
      key: 'designer_name',
      label: '设计师',
      width: '104px',
      type: 'text',
    },
    {
      key: 'sample_num',
      label: '打样件数',
      width: '88px',
      type: 'quantity',
    },
    {
      key: 'factory_name',
      label: '外发工厂',
      width: '104px',
      type: 'text',
    },
    {
      key: 'process_type_value',
      label: '外发类型',
      width: '104px',
      type: 'text',
    },
    {
      key: 'sample_type_name',
      label: '样板类型',
      width: '124px',
      type: 'text',
    },
    {
      key: 'sample_sort_num',
      label: '轮次',
      width: '96px',
      type: 'template',
      templateName: 'sample_sort_num',
    },
    {
      key: 'expected_delivery_date',
      label: '期望交付日期',
      width: '112px',
      type: 'date',
      sort: true,
      sortOrderBy: null,
    },
    {
      key: 'status_value',
      label: '状态',
      width: '104px',
      type: 'text',
      style: getStatusColorStyle,
    },
    {
      key: 'gen_user_name',
      label: '创建人',
      width: '96px',
      type: 'text',
    },
    {
      key: 'gen_time',
      label: '创建时间',
      width: '112px',
      type: 'datetime',
      sort: true,
      sortOrderBy: null,
    },
  ].map((d) => ({ ...{ visible: true, pinned: false, disable: false }, ...d }));
}
