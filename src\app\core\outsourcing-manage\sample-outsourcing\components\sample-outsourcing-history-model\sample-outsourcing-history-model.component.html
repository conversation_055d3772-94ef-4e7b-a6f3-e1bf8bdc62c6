<div class="his-box">
  <div class="hismodel-left">
    <div class="his-left">
      <span class="his-title">{{ 'sampleOutsourcing.detailField.历史版本' | translate }}</span>
      <ul class="his-time" [ngStyle]="{ height: height - 95 + 'px', 'overflow-y': 'scroll' }">
        <li
          class="time"
          *ngFor="let item of versions; index as i; first as isFirst"
          [ngClass]="{ now: isFirst, actived: selectIndex === i }"
          (click)="onTab(item, i)">
          <span class="dot" *ngIf="isFirst"></span><span>{{ item.version | date: 'yyyy/MM/dd HH:mm:ss' }}</span>
        </li>
      </ul>
      <div class="wd-bottom">{{ currentTime | date: 'yyyy/MM/dd HH:mm:ss' }}（{{ 'sampleOutsourcing.detailField.当前' | translate }}）</div>
    </div>
  </div>
  <div class="his-right">
    <div class="content-title">
      <span>{{ currentVersion?.version | date: 'yyyy/MM/dd HH:mm:ss' }}</span>
      <div class="right-btn">
        <i nz-icon [nzIconfont]="'icon-cuohao'" (click)="closeDrawer()"></i>
      </div>
    </div>
    <div [ngStyle]="{ height: height - 50 + 'px', 'overflow-y': 'scroll' }">
      <app-sample-outsourcing-basic-info [detailData]="currentDetail" [simpleData]="currentDetail"></app-sample-outsourcing-basic-info>
    </div>
  </div>
</div>
