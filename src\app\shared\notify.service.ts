import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { NzNotificationService } from 'ng-zorro-antd/notification';

@Injectable({
  providedIn: 'root',
})
export class NotifyService {
  constructor(private _notification: NzNotificationService, public _translateService: TranslateService) {}

  msgUnknown!: string;

  notify(reason: string, code: number): void {
    const str = this._translateService.instant(`reason.${reason}`);
    if (str.indexOf('reason')) {
      this._notification.error('', str);
    } else {
      this._notification.error('', this.getMsgUnknown());
    }
  }

  getMsgUnknown() {
    if (!this.msgUnknown) {
      this.msgUnknown = this._translateService.instant('reason.unknown');
    }

    return this.msgUnknown;
  }
}
