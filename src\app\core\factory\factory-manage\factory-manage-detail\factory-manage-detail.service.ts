import { Injectable } from '@angular/core';
import { FactoryManageEnum } from '../interface/factory-manage.enum';
import { AsyncValidatorFn, FormGroup, ValidationErrors, AbstractControl } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { Observable, Observer, timer } from 'rxjs';
import { distinctUntilChanged, switchMap } from 'rxjs/operators';

@Injectable()
export class FactoryManageDetailService {
  factoryForm!: FormGroup | null;
  factoryStatus!: number;
  factoryStatusEnum = FactoryManageEnum;
  versionLists: { id: number; name: string; description?: string }[] = [];
  urlPattern = new RegExp('(https?)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]');

  constructor(private http: HttpClient) {}

  checkUrl(url: string) {
    if (url && url?.length > 0) {
      if (this.urlPattern.test(url)) {
        return true;
      }
    }
    return false;
  }

  /**
   * 获取ELAN租户
   */
  getElanFactorys(): Observable<any> {
    return this.http.get<any>('/factory/elan_pull');
  }

  /**
   * 获取系统编码
   */
  getFactoryCode(): Observable<any> {
    return this.http.get<any>('/factory/new-code');
  }

  /**
   * 检验编码唯一性
   */
  uniqueValidator = (old_value: null | string = null): AsyncValidatorFn => {
    return (control: AbstractControl) => {
      return timer(500).pipe(
        distinctUntilChanged(),
        switchMap(() => {
          if (control.value === null || control.value === '') {
            return Promise.resolve(null);
          } else {
            return new Observable((observer: Observer<ValidationErrors | null>) => {
              if (control.value !== old_value) {
                this.http.get<any>(`/factory/code/${control.value}/check`).subscribe((res) => {
                  if (res.data) {
                    observer.next(null);
                  } else {
                    observer.next({ duplicated: true, msg: res.message });
                  }
                  observer.complete();
                });
              } else {
                observer.next(null);
                observer.complete();
              }
            });
          }
        })
      );
    };
  };

  /**
   * 新增工厂
   * @param payload
   */
  addFactory(payload: any): Observable<any> {
    return this.http.post<any>('/factory', payload);
  }

  /**
   * 删除工厂
   */
  deleteFactory(id: number): Observable<any> {
    return this.http.delete<any>(`/factory/${id}`);
  }

  /**
   * 获取工厂详情
   */
  getFactoryDetail(id: number): Observable<any> {
    return this.http.get<any>(`/factory/${id}`);
  }

  /**
   * 更新保存
   */
  saveFactory(payload: any): Observable<any> {
    return this.http.put<any>(`/factory/${payload.id}`, payload);
  }

  /**
   * 更新保存部署结果
   */
  saveFactoryDomain(payload: any): Observable<any> {
    return this.http.put<any>(`/factory/${payload.id}/domain`, payload);
  }

  /**
   * 提交
   */
  commitFactory(payload: any): Observable<any> {
    return this.http.put<any>('/factory/apply', payload);
  }

  /**
   * 审批通过
   */
  approveFactory(id: number): Observable<any> {
    return this.http.put<any>(`/factory/${id}/approve`, id);
  }

  /**
   * 审批退回
   */
  disapproveFactory(payload: any): Observable<any> {
    return this.http.put<any>(`/factory/${payload.id}/disapprove`, payload);
  }

  /**
   * 部署成功
   */
  deployFactory(payload: any): Observable<any> {
    return this.http.put<any>(`/factory/${payload.id}/deploy`, payload);
  }

  /**
   * 撤销
   * @param id
   */
  withdrawFactory(id: number): Observable<any> {
    return this.http.put<any>(`/factory/${id}/withdraw`, id);
  }

  /**
   * 注销
   */
  cancelFactory(id: number): Observable<any> {
    return this.http.put<any>(`/factory/${id}/cancel`, id);
  }

  /**
   * 申请初始化链接
   */
  initialFactory(id: number): Observable<any> {
    return this.http.put<any>(`/factory/${id}/random-code`, id);
  }

  /**
   * 激活
   */
  reActiveFactory(id: number): Observable<any> {
    return this.http.put<any>(`/factory/${id}/reactivate`, id);
  }

  /**
   * 获取所有版本
   */
  getVersionLists(payload: any): Observable<any> {
    return this.http.post<any>('/product/list', payload);
  }

  /**
   * 获取部署区域下拉选项
   */
  getDeployRegion(): Observable<any> {
    return this.http.get<any>('/factory/region_option');
  }
}
