.basic-container {
  padding-bottom: 16px;

  .title-label {
    font-size: 16px;
    font-weight: 500;
    color: #54607c;
  }

  nz-divider {
    margin: 8px 0px 16px 0px;
  }

  .text-label-required:before {
    content: '*';
    color: red;
  }

  .text-label {
    font-size: 14px;
    font-weight: 500;
    color: #515661;

    &:after {
      content: '：';
    }
  }

  .info-line {
    display: flex;
    width: 33.33%;
    gap: 2px;
    align-items: baseline;

    & > div:nth-child(1) {
      min-width: 25%;
      text-align: right;
    }

    & > div:nth-child(2) {
      width: 70%;
      font-size: 14px;
      font-weight: 500;
      color: #222b3c;
    }

    .update-icon {
      color: #ff912a;
    }
  }
}
