import { Directive, ElementRef, EventEmitter, <PERSON><PERSON><PERSON>, OnDestroy, Output } from '@angular/core';

@Directive({
  selector: '[ganttDropContainer]',
})
export class DropContainerDirective implements OnDestroy {
  @Output('dropData') drop = new EventEmitter<DragEvent>();

  constructor(private _element: ElementRef<HTMLElement>, private zone: Ng<PERSON>one) {
    const element = _element.nativeElement;

    this.zone.runOutsideAngular(() => {
      element.addEventListener('drop', (event) => {
        const data = event.dataTransfer?.getData('text');
        this.zone.run(() => {
          this.drop.emit(event);
        });
      });
      element.addEventListener('dragover', (event) => {
        event.preventDefault();
      });

      element.addEventListener(
        'dragenter',
        (event) => {
          // 当可拖动的元素进入可放置的目标高亮目标节点
          if ((event.target as HTMLElement).className?.includes('day-cell')) {
            (event.target as HTMLElement).classList.add('target-position');
          }
        },
        false
      );

      element.addEventListener(
        'dragleave',
        (event) => {
          // 当拖动元素离开可放置目标节点，重置其背景
          // this.zone.runOutsideAngular(() => {
          if ((event.target as HTMLElement).className?.includes('day-cell')) {
            (event.target as HTMLElement).classList.remove('target-position');
          }
          // });
        },
        false
      );
    });
  }
  ngOnDestroy(): void {
    this._element.nativeElement.removeAllListeners?.();
  }
}
