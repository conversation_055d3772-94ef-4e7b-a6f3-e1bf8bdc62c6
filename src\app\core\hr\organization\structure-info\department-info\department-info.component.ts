import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { Component, OnInit, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { DeleteModalComponent } from '../../delete-modal/delete-modal.component';
import { OrganizationService } from '../../organization.service';
import { SubdepartModalComponent } from '../../subdepart-modal/subdepart-modal.component';
import { ModalService } from 'src/app/shared/modal.service';
import { DepartStatusOptions } from '../../interface/sturcture-config';
import { UtilService } from 'src/app/shared/util.service';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzMessageService } from 'ng-zorro-antd/message';
import { TreeOptions } from '../../interface/structure-data';
import { ShiftSettingComponent } from '../../shift-setting/shift-setting.component';
import { OSSCustomConfig } from 'fl-common-lib';

@Component({
  selector: 'app-department-info',
  templateUrl: './department-info.component.html',
  styleUrls: ['./department-info.component.scss'],
})
export class DepartmentInfoComponent implements OnInit {
  @ViewChild(DeleteModalComponent) deleteModal: any;
  @ViewChild(SubdepartModalComponent) subDepartModal: any;
  statusOptions = DepartStatusOptions;
  deleteSureText!: string;
  placeInput!: string;
  placeSelect!: string;
  ossCustomConfig: OSSCustomConfig = {
    namespace: 'contract-template-logo',
    authUrl: '/service/frontapi/v1/oss',
    filePath: '/service/frontapi/v1/oss/contract-template-logo',
    authParams: {
      biz_type: 'order',
    },
  };
  lang = localStorage.getItem('lang') || 'zh';

  constructor(
    private _translateService: TranslateService,
    public _service: OrganizationService,
    private _ownModal: ModalService,
    private _modal: NzModalService,
    private _util: UtilService,
    private _notice: NzNotificationService,
    private _msg: NzMessageService
  ) {}

  ngOnInit(): void {
    this.placeInput = this._translateService.instant('placeholder.input');
    this.placeSelect = this._translateService.instant('placeholder.select');
  }

  /**
   * 取消即返回只读状态，需判断页面数据是否有变更，有则提示用户
   */
  cancel() {
    this._service.addedEmployee = null;
    if (this._service.departForm?.dirty) {
      const ref: NzModalRef = this._ownModal.confirm('confirm-cancel');
      ref.afterClose.subscribe((val: boolean) => {
        if (val) {
          this._service.getDepartmentDetail({ id: this._service.departForm?.get('id')?.value }).subscribe((res) => {
            if (res.code === 200) {
              const { id, name, code, status, parent_id } = res.data;
              this._service.departForm.setValue({ id, name, code, status, parent_id });
              this._service.departStatus = res.data.status;
            }
          });
          this._service.editable = false;
        } else {
          return;
        }
      });
    } else {
      this._service.editable = false;
    }
  }

  /**
   * 删除部门
   * 无数据权限，则提示
   */
  delete() {
    if (this._service.checkDeptDataAuth()) {
      this._service.addedEmployee = null;
      this.deleteModal.is_employee = false;
      this.deleteModal.data = this._service.departForm.getRawValue();
      this.deleteModal.createDeleteModal();
    }
  }

  /**
   * 处理删除部门后的回调,自动定位至其上级部门
   * @param data：所删除的部门数据
   */
  handleDelete(data: any) {
    this._service.deleteDepartment(data).subscribe((res) => {
      if (res.code === 200) {
        const delete_msg = this._translateService.instant('success.delete');
        this._msg.success(delete_msg);
        const expandedNode = [...this._service.expandedTree] ?? [];
        const e_index = expandedNode.findIndex((item) => item === data?.id);
        if (e_index !== -1) {
          expandedNode.splice(e_index, 1);
        }
        this._service.getTree().then((datas: TreeOptions[]) => {
          this._service.selectNode(data.parent_id);
          this._service.getEmployeeList(data.parent_id, true);
          const line = datas.find((item) => item.id === data.parent_id && !item.is_employee);
          const expand_set = new Set([...expandedNode, ...(line?.expanded_list ?? [])]);
          this._service.expandedTree = [...expand_set];
        });
        this.deleteModal.closeDeleteModal();
      }
    });
  }

  /**
   * 班次设置
   */
  shiftSetting() {
    const data = {
      department_id: this._service.departForm?.get('id')?.value,
      department_code: this._service.departForm?.get('code')?.value,
      department_name: this._service.departForm?.get('code')?.value,
    };
    this._modal.create({
      nzTitle: this._translateService.instant('structure-btn.班次设置'),
      nzContent: ShiftSettingComponent,
      nzWidth: 600,
      nzWrapClassName: 'shift-setting-modal',
      nzComponentParams: {
        data,
      },
      nzFooter: [
        {
          label: this._translateService.instant('btn.cancel'),
          type: 'default',
          shape: 'round',
          onClick: () => this._modal.closeAll(),
        },
        {
          label: this._translateService.instant('btn.ok'),
          type: 'primary',
          shape: 'round',
          onClick: (res: any): boolean | void => {
            if (res.formIsInvalid()) {
              this._notice.error('', this._translateService.instant('flss.message.check-required'));
              return false;
            }
            const params = res.handleFormPayload();
            this.submitShiftSetting(params);
          },
        },
      ],
    });
  }

  submitShiftSetting(params: any) {
    this._service.submitShiftSetting(params).subscribe((res: any) => {
      if (res.code === 200) {
        this._msg.success(this._translateService.instant('success.submit'));
        this._modal.closeAll();
      }
    });
  }

  /**
   * 新建子部门，成功后自动跳转至新建的部门
   * 无数据权限则提示
   */
  addSubDepart() {
    if (this._service.checkDeptDataAuth()) {
      this._service.addedEmployee = null;
      this.subDepartModal.data = this._service.departForm.getRawValue();
      this.subDepartModal.createSubDepartModal();
    }
  }

  /**
   * 更新部门信息
   */
  save() {
    this._service.addedEmployee = null;
    this._util
      .checkIfFormPassesValidation(this._service.departForm as any)
      .then((valid) => {
        if (valid) {
          const data = this._service.departForm?.getRawValue();

          this._service.updateDepartment(data).subscribe((res) => {
            if (res.code === 200) {
              const update_msg = this._translateService.instant('success.update');
              this._msg.success(update_msg);
              this._service.setDepartForm(data, true);
              this._service.editable = false;
              this._service.departStatus = data.status;
              const treeNode = this._service.selectTree?.length ? this._service.selectTree[0] : null;
              const expandedNode = [...this._service.expandedTree] ?? [];
              this._service.getTree().then((datas: TreeOptions[]) => {
                this._service.selectTree = [treeNode ? treeNode : data.id];
                const line = datas?.find((item) => item.id === data.id && !item.is_employee);
                this._service.expandedTree = expandedNode?.length ? expandedNode : line?.expanded_list ?? [];
              });
            }
          });
        } else {
          const msg = this._translateService.instant('form-error.input-right');
          this._notice.error('', msg);
          return;
        }
      })
      .catch((error) => {
        console.log('error', error);
        return;
      })
      .finally(() => {
        return;
      });
  }

  edit() {
    if (this._service.checkDeptDataAuth()) {
      this._service.addedEmployee = null;
      this._service.editable = true;
    }
  }
}
