<div class="basic-container">
  <div class="title-label">{{ ('第几步' | translate: { value: '1' }) + '：' }}&nbsp;&nbsp;{{ '工厂基本信息' | translate }}</div>
  <nz-divider></nz-divider>
  <div>
    <form nz-form *ngIf="_service.factoryForm" [formGroup]="_service.factoryForm" style="display: flex; flex-wrap: wrap; row-gap: 16px">
      <nz-form-item class="info-line">
        <nz-form-label nzRequired>
          {{ '公司编码' | translate }}
        </nz-form-label>
        <nz-form-control [nzErrorTip]="codeErrorTpl">
          <nz-textarea-count [nzMaxCharacterCount]="20" class="inline-count">
            <textarea rows="1" nz-input formControlName="code" [placeholder]="placeInput" [maxLength]="20" nzAutosize inputTrim></textarea>
          </nz-textarea-count>
        </nz-form-control>
        <ng-template #codeErrorTpl let-control>
          <ng-container *ngIf="control?.hasError('required')">
            {{ 'placeholder.input' | translate }}{{ '公司编码' | translate }}
          </ng-container>
          <ng-container *ngIf="control?.hasError('duplicated')">
            {{ '公司编码' | translate }}{{ 'form-error.is-exit' | translate }}
          </ng-container>
          <ng-container *ngIf="control?.pending"> {{ '公司编码' | translate }}{{ 'form-error.check-pending' | translate }} </ng-container>
          <ng-container *ngIf="control?.hasError('pattern')">
            {{ 'form-error.special-chinese-characters' | translate }}
          </ng-container>
        </ng-template>
      </nz-form-item>

      <nz-form-item class="info-line">
        <nz-form-label nzRequired>
          {{ '公司名称' | translate }}
        </nz-form-label>
        <nz-form-control [nzErrorTip]="nameErrorTpl">
          <nz-textarea-count [nzMaxCharacterCount]="20" class="inline-count">
            <textarea rows="1" nz-input formControlName="name" [placeholder]="placeInput" [maxLength]="20" nzAutosize inputTrim></textarea>
          </nz-textarea-count>
        </nz-form-control>
        <ng-template #nameErrorTpl let-control>
          <ng-container *ngIf="control?.hasError('required')">
            {{ 'placeholder.input' | translate }}{{ '公司名称' | translate }}
          </ng-container>
        </ng-template>
      </nz-form-item>

      <nz-form-item class="info-line">
        <nz-form-label nzRequired>
          {{ '公司缩写' | translate }}
        </nz-form-label>
        <nz-form-control [nzErrorTip]="abbrErrorTpl">
          <nz-textarea-count [nzMaxCharacterCount]="20" class="inline-count">
            <textarea rows="1" nz-input formControlName="abbr" [placeholder]="placeInput" [maxLength]="20" nzAutosize inputTrim></textarea>
          </nz-textarea-count>
        </nz-form-control>
        <ng-template #abbrErrorTpl let-control>
          <ng-container *ngIf="control?.hasError('required')">
            {{ 'placeholder.input' | translate }}{{ '公司缩写' | translate }}
          </ng-container>
        </ng-template>
      </nz-form-item>

      <nz-form-item class="info-line">
        <nz-form-label [nzRequired]="_service.factoryForm.get('factory_type').value === 2"> ELAN租户 </nz-form-label>
        <nz-form-control [nzErrorTip]="elanCodeErrorTpl">
          <nz-select
            formControlName="elan_code"
            [nzOptions]="factoryOptions"
            [nzPlaceHolder]="placeSelect"
            [nzDropdownMatchSelectWidth]="false"
            nzShowSearch
            nzAllowClear>
          </nz-select>
        </nz-form-control>
        <ng-template #elanCodeErrorTpl let-control>
          <ng-container *ngIf="control?.hasError('required')"> 请选择ELAN租户 </ng-container>
        </ng-template>
      </nz-form-item>

      <nz-form-item class="info-line">
        <nz-form-label>
          {{ '合作模式' | translate }}
        </nz-form-label>
        <nz-form-control>
          <nz-select formControlName="coop" [nzPlaceHolder]="placeSelect" [nzDropdownMatchSelectWidth]="false" nzShowSearch nzAllowClear>
            <ng-container *ngFor="let item of coopOptions">
              <nz-option [nzValue]="item.value" [nzLabel]="item.label"> </nz-option>
            </ng-container>
          </nz-select>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item class="info-line">
        <nz-form-label>
          {{ '工厂规模' | translate }}
        </nz-form-label>
        <nz-form-control [nzErrorTip]="scaleErrorTpl">
          <nz-input-group nzCompact>
            <nz-input-number
              formControlName="scale"
              [nzPlaceHolder]="placeInput"
              [nzMax]="99999"
              [nzStep]="1"
              (ngModelChange)="numberChange($event, 'scale')"></nz-input-number>
            <div class="unit-box">
              {{ 'unit.people' | translate }}
            </div>
          </nz-input-group>
        </nz-form-control>
        <ng-template #scaleErrorTpl>
          <ng-container *ngIf="_service.factoryForm?.get('scale')?.dirty && _service.factoryForm?.get('scale')?.hasError('min')">
            {{ 'factory-commit-notice.input-num' | translate }}
          </ng-container>
        </ng-template>
      </nz-form-item>

      <nz-form-item class="info-line">
        <nz-form-label>
          {{ '车缝人数' | translate }}
        </nz-form-label>
        <nz-form-control [nzErrorTip]="workersErrorTpl">
          <nz-input-group nzCompact>
            <nz-input-number
              formControlName="sewing_workers"
              [nzPlaceHolder]="placeInput"
              [nzMax]="99999"
              [nzStep]="1"
              (ngModelChange)="numberChange($event, 'sewing_workers')"></nz-input-number>
            <div class="unit-box">
              {{ 'unit.people' | translate }}
            </div>
          </nz-input-group>
        </nz-form-control>
        <ng-template #workersErrorTpl>
          <ng-container
            *ngIf="_service.factoryForm?.get('sewing_workers')?.dirty && _service.factoryForm?.get('sewing_workers')?.hasError('min')">
            {{ 'factory-commit-notice.input-num' | translate }}
          </ng-container>
        </ng-template>
      </nz-form-item>

      <nz-form-item class="info-line">
        <nz-form-label>
          {{ '擅长品类' | translate }}
        </nz-form-label>
        <nz-form-control>
          <nz-textarea-count [nzMaxCharacterCount]="30" class="inline-count">
            <textarea
              rows="1"
              nz-input
              formControlName="category"
              [placeholder]="placeInput"
              [maxLength]="30"
              nzAutosize
              inputTrim></textarea>
          </nz-textarea-count>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item class="info-line">
        <nz-form-label>
          {{ '主要客户' | translate }}
        </nz-form-label>
        <nz-form-control>
          <nz-textarea-count [nzMaxCharacterCount]="30" class="inline-count">
            <textarea
              rows="1"
              nz-input
              formControlName="customer"
              [placeholder]="placeInput"
              [maxLength]="30"
              nzAutosize
              inputTrim></textarea>
          </nz-textarea-count>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item class="info-line">
        <nz-form-label>
          {{ '联系人' | translate }}
        </nz-form-label>
        <nz-form-control>
          <nz-textarea-count [nzMaxCharacterCount]="20" class="inline-count">
            <textarea rows="1" nz-input formControlName="contacts" [placeholder]="placeInput" [maxLength]="20" nzAutosize></textarea>
          </nz-textarea-count>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item class="info-line">
        <nz-form-label>
          {{ '联系方式' | translate }}
        </nz-form-label>
        <nz-form-control>
          <nz-textarea-count [nzMaxCharacterCount]="20" class="inline-count">
            <textarea rows="1" nz-input formControlName="tel" [placeholder]="placeInput" [maxLength]="20" nzAutosize inputTrim></textarea>
          </nz-textarea-count>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item class="info-line">
        <nz-form-label>
          {{ '邮箱' | translate }}
        </nz-form-label>
        <nz-form-control>
          <nz-textarea-count [nzMaxCharacterCount]="30" class="inline-count">
            <textarea rows="1" nz-input formControlName="email" [placeholder]="placeInput" [maxLength]="30" nzAutosize inputTrim></textarea>
          </nz-textarea-count>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item class="info-line">
        <nz-form-label>
          {{ '地区' | translate }}
        </nz-form-label>
        <nz-form-control>
          <nz-cascader
            [nzPlaceHolder]="placeSelect"
            formControlName="region_model"
            [nzOptions]="regionOptions"
            (ngModelChange)="regionChange($event)"></nz-cascader>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item class="info-line">
        <nz-form-label>
          {{ '详细地址' | translate }}
        </nz-form-label>
        <nz-form-control>
          <nz-textarea-count [nzMaxCharacterCount]="30" class="inline-count">
            <textarea
              rows="1"
              nz-input
              formControlName="address"
              [placeholder]="placeInput"
              [maxLength]="30"
              nzAutosize
              inputTrim></textarea>
          </nz-textarea-count>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item class="info-line">
        <nz-form-label nzRequired>
          {{ '部署区域' | translate }}
        </nz-form-label>
        <nz-form-control [nzErrorTip]="deployRegionErrorTpl">
          <nz-select
            formControlName="deploy_region"
            [nzOptions]="deployRegionOptions"
            [nzPlaceHolder]="placeSelect"
            [nzDropdownMatchSelectWidth]="false"
            nzShowSearch
            nzAllowClear
            (ngModelChange)="onDeployRegionChange($event)">
          </nz-select>
        </nz-form-control>
        <ng-template #deployRegionErrorTpl let-control>
          <ng-container *ngIf="control?.hasError('required')">
            {{ 'placeholder.select' | translate }}{{ '部署区域' | translate }}
          </ng-container>
        </ng-template>
      </nz-form-item>

      <nz-form-item class="info-line">
        <nz-form-label nzRequired>
          {{ '工厂类型' | translate }}
        </nz-form-label>
        <nz-form-control>
          <nz-radio-group formControlName="factory_type" (ngModelChange)="factoryTypeValueChanged()">
            <label nz-radio [nzValue]="1">深铺</label>
            <label nz-radio [nzValue]="2">Sewsmart</label>
            <label nz-radio [nzValue]="4">LMS</label>
          </nz-radio-group>
        </nz-form-control>
      </nz-form-item>
    </form>
  </div>
</div>
