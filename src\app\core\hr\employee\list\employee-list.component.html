<app-search-container #header toolPlace="topRight" [headerTitle]="translateName + '员工档案' | translate" (reset)="reset()">
  <div *ngFor="let item of searchList">
    <span class="search-name">{{ translateName + item.label | translate }}：</span>
    <ng-container *ngIf="item.code == 'status'; else others">
      <nz-select
        style="min-width: 96px"
        [nzPlaceHolder]="'placeholder.select' | translate"
        nzAllowClear
        [(ngModel)]="searchData.status"
        (ngModelChange)="onSearch()">
        <nz-option *ngFor="let op of item.options" [nzLabel]="translateName + op?.label | translate" [nzValue]="op?.value"></nz-option>
      </nz-select>
    </ng-container>
    <ng-template #others>
      <app-dynamic-search-select
        [cpnMode]="item.code == 'roles' ? 'multiple' : 'default'"
        [transData]="item.code == 'gen_user' ? { value: 'id', label: 'name' } : undefined"
        [dataUrl]="'/employee/search'"
        [(ngModel)]="searchData[item.code]"
        [column]="item.code"
        (ngModelChange)="onSearch()">
      </app-dynamic-search-select>
    </ng-template>
  </div>
  <div>
    <span class="search-name">{{ translateName + '创建日期' | translate }}：</span>
    <nz-range-picker [(ngModel)]="searchData.gen_time" (ngModelChange)="onChange()"></nz-range-picker>
  </div>
</app-search-container>
<div class="employee-table-box">
  <nz-table
    class="zebra-striped-table"
    [nzFrontPagination]="false"
    [nzScroll]="{ y: tableHeight + 'px' }"
    [(nzPageSize)]="pageSize"
    nzTableLayout="fixed"
    [nzLoading]="loading"
    [nzData]="tableData"
    [(nzPageIndex)]="pageIndex"
    [nzShowTotal]="totalTemplate"
    [nzTotal]="total"
    [nzPageSizeOptions]="[20, 30, 40, 50]"
    (nzPageIndexChange)="onChangePageIndex()"
    (nzPageSizeChange)="onSearch()"
    nzShowSizeChanger
    nzSize="middle"
    nzBordered="ture">
    <thead>
      <tr>
        <th nzLeft nzWidth="32px">#</th>
        <ng-container *ngFor="let item of renderHeaders">
          <th
            [nzLeft]="item.pinned"
            nz-resizable
            *ngIf="item.visible"
            nzPreview
            [nzWidth]="item.width"
            [nzMaxWidth]="360"
            [nzMinWidth]="56"
            (nzResizeEnd)="onResize($event, item.label)">
            {{ translateName + item.label | translate }}
            <app-sort-btn
              [(sortOrder)]="gen_time"
              *ngIf="item.key === 'gen_time'"
              (sortOrderChange)="sortOrderChange($event)"></app-sort-btn>
            <nz-resize-handle nzDirection="right">
              <div class="resize-trigger"></div>
            </nz-resize-handle>
          </th>
        </ng-container>
        <th nzWidth="90px" nzRight>
          <span>{{ translateName + '操作' | translate }}</span>
          <a class="setting-btn" [ngStyle]="{ color: btnHighLight ? '#4D96FF' : '' }" (click)="changeHeader($event)">
            <i nz-icon nzType="setting" nzTheme="fill"></i>
          </a>
        </th>
      </tr>
    </thead>
    <tbody>
      <tr [ngClass]="!item.status ? 'quit' : ''" *ngFor="let item of tableData; let i = index">
        <td>{{ i + 1 }}</td>
        <ng-container *ngFor="let col of renderHeaders">
          <td *ngIf="col.visible" [ngClass]="col.key === 'roles' ? 'roles-row' : ''">
            <ng-container *ngIf="col.key === 'roles'; else others">
              <ng-container *ngIf="item[col.key] && item[col.key].length > 0; else nullLength">
                <app-text-truncated [template]="titleTemplate"></app-text-truncated>
                <ng-template #titleTemplate>
                  <span class="roles" *ngFor="let con of item[col.key]">
                    {{ con }}
                  </span>
                </ng-template>
              </ng-container>
              <ng-template #nullLength>-</ng-template>
            </ng-container>

            <ng-template #others>
              <ng-container *ngIf="col.key === 'gen_time' || col.key === 'status'; else timeOther">
                <ng-container *ngIf="col.key === 'status'; else gen_time">
                  {{ translateName + (item[col.key] ? '在职' : '离职') | translate }}</ng-container
                >
                <ng-template #gen_time> {{ item[col.key] ? (item[col.key] | date: 'yyyy/MM/dd HH:mm:ss') : '--' }} </ng-template>
              </ng-container>
              <ng-template #timeOther>{{ item[col.key] | noValue }}</ng-template>
            </ng-template>
          </td>
        </ng-container>

        <td (click)="todetail(item)">
          <a nz-button nzType="link"> {{ translateName + '详情' | translate }} </a>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <ng-template #totalTemplate>
    <div
      style="margin-right: 16px; color: #000000; font-size: 14px"
      [innerHTML]="
        'common.共x条,第x页' | translate: { total: total, currentPage: pageIndex, totalPage: total / pageSize | mathCeil }
      "></div>
  </ng-template>
</div>
<nz-modal
  [(nzVisible)]="isVisibleDetail"
  [nzTitle]="translateName + '员工详情' | translate"
  nzWidth="470px"
  [nzFooter]="null"
  (nzOnCancel)="isVisibleDetail = false">
  <ng-container *nzModalContent>
    <div class="head-box">
      <div class="head-left">
        <img *ngIf="detail?.avatar" class="head-img" [src]="detail?.avatar" />
        <i *ngIf="!detail?.avatar" class="iconfont icon-yonghuming"></i>
        <div class="person-flag" [ngClass]="!detail.status ? 'quit' : ''">
          <span>{{ translateName + (detail.status ? '在职' : '离职') | translate }}</span>
        </div>
      </div>
      <div class="head-right">
        <div class="head-name">
          {{ detail.name }}
          <span>{{ detail.code }}</span>
        </div>
        <ng-container *ngIf="detail.roles && detail.roles.length > 0; else nullRole">
          <p class="roles-name">{{ translateName + '权限名称' | translate }}：</p>
          <ul class="roles-list">
            <ng-container *ngFor="let item of detail.roles; let i = index">
              <div class="item">
                <span class="index">{{ i + 1 }}</span>
                <li>
                  {{ item }}
                  <div class="san"></div>
                </li>
              </div>
            </ng-container>
          </ul>
        </ng-container>
        <ng-template #nullRole>
          <span class="null-role">{{ translateName + '暂未配置权限' | translate }}～</span>
        </ng-template>
      </div>
    </div>
  </ng-container>
</nz-modal>
