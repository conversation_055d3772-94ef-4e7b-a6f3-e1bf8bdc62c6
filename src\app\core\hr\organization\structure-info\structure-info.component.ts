import { Component, OnInit, ElementRef, ViewChild, OnDestroy, Input, OnChanges, SimpleChanges } from '@angular/core';
@Component({
  selector: 'app-structure-info',
  templateUrl: './structure-info.component.html',
  styleUrls: ['./structure-info.component.scss'],
})
export class StructureInfoComponent implements OnInit, OnDestroy, OnChanges {
  @ViewChild('departInfo') departInfo?: ElementRef;
  @Input() treeMaxHeight: any;
  tableMaxTable = 200;
  InfoHeader = 96;
  constructor() {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes && changes?.treeMaxHeight && changes.treeMaxHeight.currentValue) {
      console.log('sadasd', changes);
      this.InfoHeader = this.departInfo?.nativeElement?.innerHeight ?? 96;

      this.tableMaxTable = this.treeMaxHeight - this.InfoHeader - 24 - 24 - 8 - 34 - 24 - 32;

      //table最小高度
      if (this.tableMaxTable < 200) {
        this.tableMaxTable = 200;
      }
    }
  }

  ngOnInit(): void {}

  ngOnDestroy(): void {}
}
