import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { OrderSecProcessOutsourcingRoutingModule } from './sec-process-outsourcing-routing.module';
import { OrderSecProcessOutsourcingService } from './sec-process-outsourcing.service';
import { SecProcessOutsourcingListComponent } from './sec-process-outsourcing-list/sec-process-outsourcing-list.component';
import { SecProcessOutsourcingDetailComponent } from './sec-process-outsourcing-detail/sec-process-outsourcing-detail.component';
import { SecProcessBannerComponent } from './components/sec-process-banner/sec-process-banner.component';
import { OrderOutsourcingComponentsModule } from '../components/order-outsourcing-components.module';

import { FlButtonModule } from 'fl-ui-angular/button';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzCascaderModule } from 'ng-zorro-antd/cascader';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { FlcComponentsModule, FlcDirectivesModule } from 'fl-common-lib';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { OrderNotpassReasonComponent } from '../components/order-notpass-reason/order-notpass-reason.component';

const nzModules = [
  NzIconModule,
  NzButtonModule,
  NzTableModule,
  NzDatePickerModule,
  NzFormModule,
  NzInputModule,
  NzSelectModule,
  NzInputModule,
  NzInputNumberModule,
  NzCascaderModule,
  NzToolTipModule,
  NzTabsModule,
  NzDividerModule,
];

@NgModule({
  imports: [
    CommonModule,
    OrderSecProcessOutsourcingRoutingModule,
    ...nzModules,
    FlcComponentsModule,
    FlcDirectivesModule,
    FormsModule,
    ReactiveFormsModule,
    FlButtonModule,
    OrderOutsourcingComponentsModule,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new MultiTranslateHttpLoader(http, [
            { prefix: './assets/i18n/outsourcing-manage/garm-sec-outsourcing/', suffix: '.json' },
          ]);
        },
        deps: [HttpClient],
      },
    }),
  ],
  declarations: [
    SecProcessOutsourcingListComponent,
    SecProcessOutsourcingDetailComponent,
    SecProcessBannerComponent,
    OrderNotpassReasonComponent,
  ],
  providers: [OrderSecProcessOutsourcingService],
})
export class SecProcessOutsourcingModule {
  constructor(public translateService: TranslateService, private _service: OrderSecProcessOutsourcingService) {
    const cl = translateService.currentLang;
    translateService.currentLang = '';
    translateService.use(cl).subscribe(() => {
      this._service.translateEventEmitter.next();
    });
  }
}
