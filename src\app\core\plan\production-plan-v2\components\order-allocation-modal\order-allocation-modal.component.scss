.modal-body {
  overflow-y: scroll;
  margin-top: -16px;

  .modal-body-header {
    height: 42px;
    padding: 0 8px;
    line-height: 42px;
    font-weight: 500;
    background-color: #f7f8fa;
    border-radius: 0 0 4px 4px;
  }

  .modal-content {
    padding-top: 8px;

    .radio-box {
      margin-bottom: 8px;
      label {
        width: 110px;
        text-align: center;
      }
    }
  }
}

.modal-table {
  nz-select,
  nz-input-number {
    width: 100%;
  }

  .ant-form-item {
    margin-bottom: 0;
  }

  .data-picker-tpl {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2px;
    nz-data-picker {
      width: 116px;
    }
  }
}

.bottomBar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  column-gap: 8px;
  padding-top: 16px;
}

.line-operate-btn {
  a {
    cursor: pointer;
    width: auto;
    height: auto;
    margin: 0;
    background-color: transparent;
    &:first-child {
      color: #f96d6d;

      margin-right: 6px;
    }

    &:last-child {
      color: #4d96ff;
    }

    &[disabled] {
      color: #ddd !important;
    }
  }
}

:host ::ng-deep {
  .nz-disable-td > div {
    width: 100% !important;
  }
}
