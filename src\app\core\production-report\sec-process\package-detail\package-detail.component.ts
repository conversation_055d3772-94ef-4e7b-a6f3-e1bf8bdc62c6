import { ActivatedRoute, Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';
import _ from 'lodash';
import { SecProcessService } from '../sec-process.service';

@Component({
  selector: 'extra-package-detail',
  templateUrl: './package-detail.component.html',
  styleUrls: ['./package-detail.component.scss'],
})
export class PackageDetailComponent implements OnInit {
  dimension = 1;
  extraProcess = 0;
  loading = false;
  poOptions: any = [];
  colorOptions: any = [];
  positionOptions: any = [];
  extraOptions: any = [];
  extraIndex = 0;
  searchData: any = {
    io_code: null,
    po_code: null,
    color: null,
    position: null,
    extra_process_id: null,
  };

  isFold = false;

  data: any[] = [];

  constructor(private route: ActivatedRoute, private router: Router, private _service: SecProcessService) {}

  parseStatus(n: number, short = false) {
    switch (n) {
      case 1:
        return short ? '途' : '裁片在途';
      case 2:
        return short ? '收' : '裁片已收货';
      case 3:
        return short ? '生' : '生产中';
      default:
        return short ? '外' : '外发厂发货';
    }
  }

  reset() {
    this.getData(true);
  }

  goBack() {
    this.router.navigate(['/production-report/sec-process/list']);
  }

  setExtra(extra: any) {
    this.extraIndex = this.extraOptions.findIndex((i: any) => i.value === extra);
    this.searchData.extra_process_id = extra;
    this.getData();
  }

  getData(clear = false) {
    if (clear) {
      this.searchData.po_code = null;
      this.searchData.color = null;
      this.searchData.position = null;
      this.getOptions();
    }
    const params: any = {
      io_code: this.searchData.io_code,
      extra_process_id: this.searchData.extra_process_id,
    };
    if (this.searchData.po_code) {
      params.po_code = this.searchData.po_code;
    }
    if (this.searchData.color) {
      params.color_name = this.searchData.color;
    }
    if (this.searchData.position) {
      params.position_name = this.searchData.position;
    }
    this.loading = true;
    this._service.getOutsourcePackageProgress({ params }).subscribe({
      next: (res: any) => {
        if (res.code !== 200) return;
        this.data = res.data.list;
        this.data.forEach((item) => {
          item.specs.forEach((spec: any) => {
            spec.package_sections.forEach((pkg: any) => {
              pkg.progress = Math.floor(parseFloat(pkg.progress) * 100);
              pkg.status = _.union<number>(
                ...pkg.packages.map((i: any) => {
                  i.sum = i.positions.reduce((prev: any, curr: any) => prev + curr.qty, 0);
                  i.defectiveSum = i.positions.reduce((prev: any, curr: any) => prev + curr.defective_qty, 0);
                  i.status = _.union<number>(
                    i.positions.map((i: any) => {
                      if (i.status === 4) {
                        return 3;
                      }
                      if (i.status > 4) {
                        return 4;
                      }
                      return i.status;
                    })
                  );
                  return i.status;
                })
              ).sort((a, b) => b - a);
            });
          });
        });
      },
      complete: () => {
        this.loading = false;
      },
    });
  }

  getOptions() {
    this._service
      .getOutsourcePackageProgressOptions({
        params: {
          io_code: this.searchData.io_code,
        },
      })
      .subscribe((res: any) => {
        if (res.code !== 200) return;
        this.poOptions = res.data.po_options;
        this.colorOptions = res.data.color_options;
        this.positionOptions = res.data.position_options;
        this.extraOptions = res.data.extra_process;
        this.extraIndex = this.extraOptions.findIndex((i: any) => i.value === this.searchData.extra_process_id);
      });
  }

  ngOnInit(): void {
    this.searchData = { ...this.searchData, ...this.route.snapshot.queryParams };
    this.searchData.extra_process_id = +this.searchData.extra_process_id;
    this.getOptions();
    this.getData();
  }
}
