import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { FlcDrawerHelperService, FlcModalService, FlcValidatorService } from 'fl-common-lib';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { Subscription } from 'rxjs';
import { SampleOutsourcingBasicInfoComponent } from '../components/sample-outsourcing-basic-info/sample-outsourcing-basic-info.component';
import { SampleOutsourcingHistoryModelComponent } from '../components/sample-outsourcing-history-model/sample-outsourcing-history-model.component';
import { SampleOutsouringOperateBtnEnum, SampleOutsouringStatusEnum } from '../modal/sample-outsourcing.enum';
import { IDetailModel } from '../modal/sample-outsourcing.interface';
import { SampleOutsourcingService } from '../sample-outsourcing.service';
import { SampleOutsouringProgressComponent } from '../components/sample-outsouring-progress/sample-outsouring-progress.component';
import { SampleOursourcingTechArchiveComponent } from '../components/sample-oursourcing-tech-archive/sample-oursourcing-tech-archive.component';

@Component({
  selector: 'app-sample-outsouring-detail',
  templateUrl: './sample-outsouring-detail.component.html',
  styleUrls: ['./sample-outsouring-detail.component.scss'],
})
export class SampleOutsouringDetailComponent implements OnInit {
  @ViewChild(SampleOutsourcingBasicInfoComponent) baseInfoEl!: SampleOutsourcingBasicInfoComponent;
  @ViewChild(SampleOutsouringProgressComponent) progressComps?: SampleOutsouringProgressComponent;
  @ViewChild(SampleOursourcingTechArchiveComponent) techArchiveComps?: SampleOursourcingTechArchiveComponent;
  translateName = 'sampleOutsourcing.detailField.';
  id: string | number = this._route.snapshot.params.id;
  isEdit = this.id === 'new';
  detailData: IDetailModel | null = null; // 打样外发详情
  simpleData: Partial<IDetailModel> = {}; // 打样单详情

  statusEnum = SampleOutsouringStatusEnum; // 打样单状态枚举
  isFullScreenMode = true;
  // 退回修改表单
  returnRevisionForm: FormGroup = this._fb.group({
    reason: [null, Validators.required],
  });
  returnRevisionVisible = false;

  private _eventSubscription?: Subscription;
  constructor(
    private _fb: FormBuilder,
    private _route: ActivatedRoute,
    private _router: Router,
    private _service: SampleOutsourcingService,
    private _drawerHelp: FlcDrawerHelperService,
    private _validatorService: FlcValidatorService,
    private _notifyService: NzNotificationService,
    private _translate: TranslateService,
    private _flcModalService: FlcModalService,
    private _msg: NzMessageService
  ) {}

  ngOnInit() {
    this.id !== 'new' && this.getDetail(Number(this.id));
    this._eventSubscription = this._service.changeSampleOrderEvent.subscribe((res) => {
      this.onChangeSampleOrder(res);
    });
  }

  ngOnDestroy() {
    this._eventSubscription?.unsubscribe();
  }

  private onChangeSampleOrder(id: number | null) {
    if (id !== null) {
      this._service.getSampleDetail(id).subscribe((res) => {
        if (res.code === 200) {
          this.simpleData = res.data;
        }
      });
    } else {
      this.simpleData = {};
    }
  }

  private getDetail(id: number) {
    this._service.getSampleOutersouringDetail(id).subscribe({
      next: (res) => {
        if (res.code === 200) {
          this.detailData = res.data;
          this.simpleData = res.data;
        }
      },
      error: () => {
        this._service.refreshEvent.emit();
        this._router.navigate(['..'], { relativeTo: this._route });
      },
    });
  }

  onButtonAction(action: SampleOutsouringOperateBtnEnum) {
    switch (action) {
      case SampleOutsouringOperateBtnEnum.back:
        this.onBack();
        break;
      case SampleOutsouringOperateBtnEnum.edit:
        this.isEdit = true;
        this.getDetail(Number(this.id));
        break;
      case SampleOutsouringOperateBtnEnum.commit:
        this.onCommit();
        break;
      case SampleOutsouringOperateBtnEnum.save:
        this.onSave();
        break;
      case SampleOutsouringOperateBtnEnum.cancel:
        this.onCancel();
        break;
      case SampleOutsouringOperateBtnEnum.modify:
        this.returnRevisionVisible = true;
        break;
      case SampleOutsouringOperateBtnEnum.pass:
        this.onPass();
        break;
      case SampleOutsouringOperateBtnEnum.cancelSample:
        this.onCancelSample();
        break;
      case SampleOutsouringOperateBtnEnum.receive:
        this.onReceive();
        break;
    }
  }

  /**
   * 返回
   * @returns
   */
  private onBack() {
    this._router.navigate(['..'], { relativeTo: this._route });
  }

  /**
   * 取消
   * @returns
   */
  private onCancel() {
    if (this.baseInfoEl.basicFormCom.sampleBasicForm.dirty) {
      if (this._route.snapshot.params.id === 'new') {
        this.onBack();
      } else {
        this._flcModalService
          .confirmCancel({
            content: this.translateValue('确定取消当前操作？'),
          })
          .afterClose.subscribe((res: boolean) => {
            if (res) {
              this.isEdit = false;
              this.getDetail(Number(this.id));
            }
          });
      }
    } else {
      this._route.snapshot.params.id === 'new' ? this.onBack() : ((this.isEdit = false), this.getDetail(Number(this.id)));
    }
  }

  /**
   * 提交
   * @returns
   */
  private onCommit() {
    if (this.techArchiveComps?.isFullScreenMode) {
      this.techArchiveComps?.onToggleFullScreen();
    }
    if (this._validatorService.formIsInvalid(this.baseInfoEl.basicFormCom.sampleBasicForm)) {
      this._notifyService.error(this.translateValue('请检查必填项'), '');
      return;
    }
    const _value = this.baseInfoEl.basicFormCom.handleValue();
    this._service.submitSampleOutsouring(this.id, _value).subscribe((res) => {
      if (res.code === 200) {
        this._msg.success(this._translate.instant('success.submit'));
        this.baseInfoEl.basicFormCom.sampleBasicForm.markAsPristine();
        this.id = res.data;
        window.history.replaceState({}, '', `/outsourcing-manage/sample-outsourcing/list/${this.id}`);
        this.getDetail(res.data);
        this.isEdit = false;
        this.progressComps?.getProgressList(this.detailData?.sample_order_id as number);
        this._service.refreshEvent.emit();
      }
    });
  }

  /**
   * 保存
   * @returns
   */
  private onSave() {
    const _codeControl = this.baseInfoEl.basicFormCom.sampleBasicForm.get('sample_order_id');
    if (_codeControl?.invalid) {
      _codeControl.markAsDirty();
      _codeControl.updateValueAndValidity();
      this._notifyService.error(this.translateValue('请检查打样单号'), '');
      return;
    }
    const _value = this.baseInfoEl.basicFormCom.handleValue();
    this._service.saveSampleOutsouring(this.id, _value).subscribe((res) => {
      if (res.code === 200) {
        this._msg.success(this._translate.instant('success.save'));
        this.baseInfoEl.basicFormCom.sampleBasicForm.markAsPristine();
        this.id = res.data;
        window.history.replaceState({}, '', `/outsourcing-manage/sample-outsourcing/list/${this.id}`);
        this.getDetail(res.data);
        this._service.refreshEvent.emit();
      }
    });
  }

  // 退回修改
  onRevision() {
    if (this._validatorService.formIsInvalid(this.returnRevisionForm)) return;
    this._service
      .auditSample({
        sample_order_id: this.detailData?.id,
        status: SampleOutsouringStatusEnum.wait_modify,
        reason_for_return: this.returnRevisionForm.getRawValue().reason,
      })
      .subscribe((res) => {
        res.code === 200 && this.detailData?.id && this.getDetail(this.detailData.id);
        this.onCloseModal();
        this._service.refreshEvent.emit();
        this._msg.success(this._translate.instant('sampleOutsourcing.sampleOutsourcingMessage.退回成功'));
      });
  }

  // 审核通过
  onPass() {
    this._service.auditSample({ sample_order_id: this.detailData?.id, status: SampleOutsouringStatusEnum.wait_order }).subscribe((res) => {
      if (res.code === 200) {
        this.detailData?.id && this.getDetail(this.detailData.id);
        this._msg.success(this._translate.instant('success.pass'));
        this._service.refreshEvent.emit();
      }
    });
  }

  // 取消外发打样
  onCancelSample() {
    this._flcModalService
      .confirmCancel({
        content: this.translateValue('确定取消外发?'),
      })
      .afterClose.subscribe((res: boolean) => {
        if (res) {
          this._service.cancelSampleOutsourcing(this.id).subscribe((res) => {
            res.code === 200 && this.getDetail(this.id as number);
            this._service.refreshEvent.emit();
            this._msg.success(this._translate.instant('sampleOutsourcing.sampleOutsourcingMessage.取消成功'));
          });
        }
      });
  }

  onReceive() {
    this._flcModalService
      .confirmCancel({
        content: this.translateValue('确定样衣收货吗?'),
      })
      .afterClose.subscribe((res: boolean) => {
        if (res) {
          this._service.receiveSample(this.id).subscribe((res) => {
            if (res.code === 200) {
              this.detailData?.id && this.getDetail(this.detailData.id);
              this._msg.success(this._translate.instant('sampleOutsourcing.sampleOutsourcingMessage.收货成功'));
              this._service.refreshEvent.emit();
              this.progressComps?.getProgressList(this.detailData?.sample_order_id as number);
            }
          });
        }
      });
  }

  onCloseModal() {
    this.returnRevisionVisible = false;
    this.returnRevisionForm.reset();
  }

  onOpenHistoryDrawer() {
    this._drawerHelp
      .openDrawer({
        title: '',
        showCloseIcon: false,
        content: SampleOutsourcingHistoryModelComponent,
        contentParams: { id: this.id },
      })
      .subscribe(() => {});
  }

  translateValue(key: string) {
    return this._translate.instant('sampleOutsourcing.sampleOutsourcingMessage.' + key);
  }

  toggleFullScreen(e: boolean) {
    this.isFullScreenMode = !e;
    if (this.isFullScreenMode) {
      setTimeout(() => {
        document.getElementById('sampleTechArchive')?.scrollIntoView({ block: 'center' });
      }, 0);
    }
  }

  /** 编辑状态切换菜单 是否需要二次提示 */
  canLeave() {
    if (this.isEdit) {
      return !this.baseInfoEl.basicFormCom.sampleBasicForm.dirty;
    } else {
      return true;
    }
  }
}
