/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { Component, ElementRef, Inject, On<PERSON><PERSON>roy, OnInit, QueryList, TemplateRef, ViewChild, ViewChildren } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { ModalService } from 'src/app/shared/modal.service';
import { RoleLogModel, RoleNameChildModel, RoleNameItemModel, RoleNameListModel, RoleNameModel } from './role-manager.model';
import { RoleManagerService } from './role-manager.service';
import { format, startOfDay, endOfDay } from 'date-fns';
import { AppStorageService } from 'src/app/shared/app-storage.service';
import { finalize } from 'rxjs/operators';
import { firstValueFrom, Subscription } from 'rxjs';
import { RoleLineComponent } from './role-line/role-line.component';

@Component({
  selector: 'app-role-manager',
  templateUrl: './role-manager.component.html',
  styleUrls: ['./role-manager.component.scss'],
})
export class RoleManagerComponent implements OnInit, OnDestroy {
  @ViewChild('RoleNameFormTpl') RoleNameTpl!: TemplateRef<HTMLElement>;
  @ViewChild('deleteTemplate') deleteTpl!: TemplateRef<HTMLElement>;
  @ViewChild('deleteHeadTemplate') deleteHeadTpl!: TemplateRef<HTMLElement>;
  deleteTplHasUsers = false;
  deleteModal?: NzModalRef;
  @ViewChild('emptyBtnTemplate') emptyBtnTpl!: TemplateRef<HTMLElement>;
  @ViewChild('emptyBtnHeadTemplate') emptyBtnHeadTpl!: TemplateRef<HTMLElement>;
  emptyBtnModal?: NzModalRef;
  @ViewChild('RoleLog') RoleLogTpl!: TemplateRef<HTMLElement>;
  @ViewChild('listview', { static: false }) listviewTpl!: ElementRef<HTMLElement>;
  @ViewChild('RoleNameListWrap') RoleNameListWrapTpl!: ElementRef<HTMLElement>;
  @ViewChild('roleLineRef') roleLineRef!: RoleLineComponent;
  @ViewChildren('roleLineRefs') roleLineRefs!: QueryList<RoleLineComponent>;

  btnArr: string[] = [];
  RoleLogLoading = false;
  RoleLogList: RoleLogModel[] = [];
  RoleLogIndex = 1;
  RoleLogLimit = 10;
  RoleLogCount = 0;
  RoleLogGenTime: 'desc' | 'asc' | null = 'desc';
  RoleLogGenUserOptions: string[] = [];
  RoleLogRoleNameOptions: string[] = [];
  RoleLogOperationOptions: string[] = [];
  RoleLogSearchOptions: {
    gen_time?: Date[];
    gen_user?: string;
    role_name?: string;
    operation?: string;
    [key: string]: any;
  } = {};
  RoleNameList: RoleNameListModel[] = [];
  selectedRoleNameIndex?: number;
  selectedRoleNameItem?: RoleNameItemModel;
  firstMenuList: RoleNameChildModel[] = [];
  realSelectedIndex = 0;
  selectedFirstMenu: RoleNameChildModel | null = null;
  rangeList: RoleNameChildModel[] = [];
  rangeModelFirstMenuList: RoleNameChildModel[] = [];
  RoleNameForm?: FormGroup;
  isRangeMode = false;
  isEdit = false;
  showALLDetail = false;
  searchOptions: {
    name?: string;
    code?: string;
    gen_time_min?: Date;
    gen_time_max?: Date;
    status?: boolean;
    gen_user?: number[];
    order_by?: string;
  } = {};
  roleNameOrderBy: 'desc' | 'asc' | null = null;
  nameOptions: string[] = [];
  codeOptions: string[] = [];
  statusOptions: number[] = [0, 1];
  genUserOptions: string[] = [];
  genTimeSearchArray: Date[] = [];
  roleListIsLoading = true;
  roleDetailIsLoading = true;
  isSaveError = false;
  allDepartmentList: { id: number; name: string }[] = [];
  readySubcription?: Subscription;
  translateName = 'roleMgt.';
  constructor(
    private _service: RoleManagerService,
    private _modal: NzModalService,
    private _fb: FormBuilder,
    private _mssage: NzMessageService,
    private _ownModal: ModalService,
    private _notice: NzNotificationService,
    private _storage: AppStorageService
  ) {}
  ngOnDestroy(): void {
    this.readySubcription?.unsubscribe();
  }
  canLeave(): boolean {
    return !this.isEdit;
  }
  setLogOptions(column: 'gen_time' | 'gen_user' | 'role_name' | 'operation', event: any) {
    switch (column) {
      case 'gen_time':
        this.RoleLogSearchOptions.gen_time = event;
        break;
      case 'gen_user':
        this.RoleLogSearchOptions.gen_user = event;
        break;
      case 'role_name':
        this.RoleLogSearchOptions.role_name = event;
        break;
      case 'operation':
        this.RoleLogSearchOptions.operation = event;
        break;
      default:
        break;
    }
    this.getLogList(true);
  }
  getLogOptions(column: 'gen_user' | 'role_name' | 'operation', event: any) {
    switch (column) {
      case 'gen_user':
        if (this.RoleLogGenUserOptions.length < 1) {
          this._service
            .getLogSelectOption({
              column,
              limit: 0,
              page: 1,
            })
            .subscribe((res) => {
              this.RoleLogGenUserOptions = res.data.datalist;
            });
        }
        break;
      case 'role_name':
        if (this.RoleLogRoleNameOptions.length < 1) {
          this._service
            .getLogSelectOption({
              column,
              limit: 0,
              page: 1,
            })
            .subscribe((res) => {
              this.RoleLogRoleNameOptions = res.data.datalist;
            });
        }
        break;
      case 'operation':
        if (this.RoleLogOperationOptions.length < 1) {
          this._service
            .getLogSelectOption({
              column,
              limit: 0,
              page: 1,
            })
            .subscribe((res) => {
              this.RoleLogOperationOptions = res.data.datalist;
            });
        }
        break;

      default:
        break;
    }
  }
  toggleSortBy(event: 'desc' | 'asc' | null) {
    this.roleNameOrderBy = event;
    if (event != null) {
      this.searchOptions.order_by = `gen_time ${event}`;
    } else {
      this.searchOptions.order_by = undefined;
    }
    this.selectedRoleNameIndex = -1;
    this.getAllRoleName(true);
  }
  toggleShowALLDetail() {
    this.showALLDetail = !this.showALLDetail;
  }
  setOptions(column: 'name' | 'code' | 'status' | 'gen_user' | 'gen_time', event: any) {
    if (this.isEdit) {
      this._ownModal.cancelActionModal({
        clickYes: () => {
          this.isEdit = false;
          this.selectedRoleNameIndex = 0;
          const id = this.RoleNameList[0].id;
          this.getRoleNameDetail(id);
        },
      });
      return;
    }
    switch (column) {
      case 'name':
        this.searchOptions.name = event;
        break;
      case 'code':
        this.searchOptions.code = event;
        break;
      case 'status':
        this.searchOptions.status = event;
        break;
      case 'gen_user':
        this.searchOptions.gen_user = event;
        break;
      case 'gen_time':
        if (event.length > 0) {
          this.searchOptions.gen_time_min = startOfDay(event[0]);
          this.searchOptions.gen_time_max = endOfDay(event[1]);
        } else {
          this.searchOptions.gen_time_min = undefined;
          this.searchOptions.gen_time_max = undefined;
        }
        break;
      default:
        break;
    }
    this.roleDetailIsLoading = true;
    this.selectedRoleNameIndex = undefined;
    this.selectedRoleNameItem = undefined;
    this.getAllRoleName(true);
  }
  getOptions(column: 'name' | 'code' | 'status' | 'gen_user') {
    switch (column) {
      case 'name':
        if (this.nameOptions.length < 1) {
          this._service
            .getSelectOption({
              column,
              limit: 0,
              page: 1,
            })
            .subscribe((res) => {
              this.nameOptions = res.data.datalist;
            });
        }
        break;
      case 'code':
        if (this.codeOptions.length < 1) {
          this._service
            .getSelectOption({
              column,
              limit: 0,
              page: 1,
            })
            .subscribe((res) => {
              this.codeOptions = res.data.datalist;
            });
        }
        break;
      case 'status':
        if (this.statusOptions.length < 1) {
          this._service
            .getSelectOption({
              column,
              limit: 0,
              page: 1,
            })
            .subscribe((res) => {
              this.statusOptions = res.data.datalist;
            });
        }
        break;
      case 'gen_user':
        if (this.genUserOptions.length < 1) {
          this._service
            .getSelectOption({
              column,
              limit: 0,
              page: 1,
            })
            .subscribe((res) => {
              this.genUserOptions = res.data.datalist;
            });
        }
        break;
      default:
        break;
    }
  }
  cancel() {
    this.isEdit = false;
    const id = this.RoleNameList[this.selectedRoleNameIndex!].id;
    this.getRoleNameDetail(id);
  }
  handleRangleErrorStatus(list: RoleNameChildModel[]): boolean {
    let status = false;
    list.forEach((item) => {
      if (item.isLastMenu) {
        if (item.payload.data_type === 4) {
          if (item.payload.data_range.length === 0) status = true;
        }
      } else {
        if (!status) {
          status = this.handleRangleErrorStatus(item.children ?? []);
        }
      }
    });
    return status;
  }
  handleWriteBtnEmptyError(list: RoleNameChildModel[]): boolean {
    let status = false;
    list.forEach((item) => {
      if (item.isLastMenu) {
        if (item.isEmptyWrite) {
          status = true;
        }
      } else {
        if (!status) {
          status = this.handleWriteBtnEmptyError(item.children ?? []);
        }
      }
    });
    return status;
  }
  async beforeSaveCheck() {
    const rangEerrorStatus = this.handleRangleErrorStatus(this.selectedRoleNameItem?.children ?? []);
    if (rangEerrorStatus) {
      this.isSaveError = true;
      this._notice.error(this._service.translateValue(this.translateName + '管理范围的自定义部门字段不能为空'), '');
      this.switchToRangeMode(true);
      return;
    }
    const writeBtnEmptyError = this.handleWriteBtnEmptyError(this.selectedRoleNameItem?.children ?? []);
    if (writeBtnEmptyError) {
      // this._notice.error('功能权限内有尚未菜单定义为编辑者，但未为其选中任何按钮，是否继续？', '');
      // this.switchToRangeMode(false);
      this.emptyBtnModal = this._modal.create({
        nzBodyStyle: {
          padding: '0px 8px 8px 8px',
        },
        nzStyle: {
          'border-radius': '16px',
        },
        nzWidth: '320px',
        nzTitle: this.emptyBtnHeadTpl,
        nzContent: this.emptyBtnTpl,
        nzCentered: true,
        nzFooter: null,
        nzWrapClassName: 'modal-outer',
      });
      return;
    }
    this.save();
  }
  saving = false;
  save() {
    this.emptyBtnModal?.close();
    const id = this.selectedRoleNameItem!.id;
    this.saving = true;
    this.selectedRoleNameItem!.children?.forEach((item: any) => {
      item?.children?.forEach((child: any) => {
        // 字段权限后期待优化为通用设计，现设计的不通用，单针对模块去处理
        child['fields'] = child?.fieldList?.map((field: any) => {
          return {
            ...field,
            editPermission: ['order:bulk', 'settings:calendar'].includes(child?.code) ? (field?.write_value ? 2 : 1) : null,
            viewPermission: ['bulk:prod-status-report'].includes(child?.code) ? (field?.read_value ? 2 : 1) : null,
            fieldId: field?.id,
            id: field?.fieldRelId,
          };
        });
      });
      item?.children.forEach((item: any) => {
        item.moduleList.forEach((it: any) => {
          delete it.fields_list;
        });
      });
    });
    this._service
      .editRoleNameDetail(id, this.selectedRoleNameItem!.children)
      .pipe(
        finalize(() => {
          this.saving = false;
        })
      )
      .subscribe((res) => {
        if (res.code === 200) {
          this._mssage.success(this._service.translateValue('success.save'));
          this.selectedRoleNameItem = undefined;
          this.selectedRoleNameIndex = -1;
          this.getAllRoleName(false);
          this.selectedRoleNameIndex = this.RoleNameList.findIndex((item) => item.id === id);
          this.getRoleNameDetail(id);
          this.isEdit = false;
        }
      });
  }
  statusChanged() {
    this.rangeList = [];
    this.handleRangeList(this.selectedRoleNameItem?.children ?? []);
  }

  handleRangeList(list: RoleNameChildModel[]) {
    list.forEach((item) => {
      if (item.isLastMenu) {
        if (item.value) {
          this.rangeList.push(item);
          item.hasSelectedMenu = true;
        } else {
          item.hasSelectedMenu = false;
        }
      } else {
        this.handleRangeList(item.children ?? []);
        item.hasSelectedMenu = (item.children ?? []).some((item) => item.hasSelectedMenu);
        item.value = item.hasSelectedMenu;
        item.isHideForRangeMode = ([] as string[]).includes(item.code);
      }
    });
  }
  LastMenuCheck(itemlist: RoleNameChildModel[]) {
    itemlist.forEach((child) => {
      if ((child.children?.length ?? 0) > 0) {
        child.isLastMenu = child.children![0].type === 'btn';
      } else {
        child.isLastMenu = true;
      }
      if (!child.isLastMenu) {
        this.LastMenuCheck(child.children ?? []);
      } else {
        if ((child.children?.length ?? 0) && child.children?.every((item) => !item.visible)) {
          child.action_type = 'read';
        }
      }
    });
  }

  ngOnInit(): void {
    this.btnArr = this._storage.getUserActions('settings/role');
    this.getAllRoleName();
    this.getAllDepartment();
  }
  getAllDepartment() {
    this._service.getAllDepartmentList().subscribe((res) => {
      this.allDepartmentList = res.data;
    });
  }
  async getAllRoleName(reset = true): Promise<void> {
    if (!this.btnArr.includes('settings:role-list')) {
      return;
    }
    this.roleListIsLoading = true;
    const res: any = await this._service
      .getRoleNameList({
        ...this.searchOptions,
        limit: 0,
        page: 1,
      })
      .toPromise()
      .finally(() => {
        this.roleListIsLoading = false;
      });
    this.RoleNameList = res.data.datalist;
    if (this.RoleNameList.length > 0) {
      if (reset) {
        this.selectRoleName(0);
      }
    } else {
      this.roleDetailIsLoading = false;
    }
  }
  selectRoleName(index: number) {
    if (index === this.selectedRoleNameIndex) {
      return;
    }
    if (!this.btnArr.includes('settings:role-detail')) {
      return;
    }
    if (this.isEdit) {
      const modal = this._ownModal.cancelActionModal({
        clickYes: () => {
          this.isEdit = false;
          this.selectedRoleNameIndex = index;
          const id = this.RoleNameList[index].id;
          this.getRoleNameDetail(id);
        },
        clickNo: () => {
          modal.close();
        },
      });
      return;
    }
    this.isEdit = false;
    this.selectedRoleNameIndex = index;
    const id = this.RoleNameList[index].id;
    this.getRoleNameDetail(id);
  }
  async getRoleNameDetail(id: number) {
    this.roleDetailIsLoading = true;
    this.selectedRoleNameItem = undefined;
    const res: any = await this._service
      .getRoleNameDetail(id)
      .pipe(
        finalize(() => {
          this.roleDetailIsLoading = false;
        })
      )
      .toPromise();
    if (res.code === 200) {
      this.selectedRoleNameItem = res.data;
      this.firstMenuList = [...res.data.children];
      this.firstMenuList.unshift({
        name: this._service.translateValue(this.translateName + '全部'),
        code: 'ALLMenu',
        value: true,
        action_type: 'read',
        children: [],
        isLastMenu: true,
        hasSelectedMenu: true,
        payload: {
          data_type: 1,
          data_range: [],
        },
        type: 'menu',
        visible: true,
        isHideForRangeMode: false,
      });
      this.selectedFirstMenu = this.firstMenuList[0];
      this.isRangeMode = false;
      this.rangeList = [];
      this.LastMenuCheck(this.selectedRoleNameItem?.children ?? []);
      this.handleRangeList(this.selectedRoleNameItem?.children ?? []);
      this.updateAllChecked();
    }
  }
  buildForm(item?: RoleNameModel): void {
    this.RoleNameForm = this._fb.group({
      id: [item?.id ?? null],
      code: [{ value: item?.code ?? null, disabled: true }, Validators.required],
      name: [item?.name ?? null, [Validators.required, Validators.maxLength(12)]],
      status: [item?.status ?? true, Validators.required],
    });
  }
  async showLogModal() {
    this.RoleLogSearchOptions = {};
    await this.getLogList(true);
    this._modal.create({
      nzCentered: true,
      nzTitle: this._service.translateValue(this.translateName + '操作记录'),
      nzBodyStyle: {
        padding: '0 12px 0 12px',
      },
      nzContent: this.RoleLogTpl,
      nzWidth: '70%',
      nzFooter: null,
    });
  }
  async getLogList(reset = false) {
    if (this.RoleLogLoading) return;
    this.RoleLogLoading = true;
    if (reset) {
      this.RoleLogIndex = 1;
      this.RoleLogCount = 0;
    }
    const data: any = {
      where: {},
      limit: this.RoleLogLimit,
      page: this.RoleLogIndex,
      orderBy: [`gen_time ${this.RoleLogGenTime ?? 'desc'}`],
    };
    if (this.RoleLogSearchOptions.gen_time && this.RoleLogSearchOptions.gen_time.length > 0) {
      const start = startOfDay(this.RoleLogSearchOptions.gen_time[0]);
      const end = endOfDay(this.RoleLogSearchOptions.gen_time[1]);
      data.where.gen_time = {
        op: 'between',
        // eslint-disable-next-line quotes
        value: [format(start, `yyyy-MM-dd'T'HH:mm:ss.SSSxxx`), format(end, `yyyy-MM-dd'T'HH:mm:ss.SSSxxx`)],
      };
    }
    if (this.RoleLogSearchOptions.gen_user) {
      data.where.gen_user = {
        op: 'like',
        value: this.RoleLogSearchOptions.gen_user,
      };
    }
    if (this.RoleLogSearchOptions.operation) {
      data.where.operation = {
        op: 'like',
        value: this.RoleLogSearchOptions.operation,
      };
    }
    if (this.RoleLogSearchOptions.role_name) {
      data.where.role_name = {
        op: '=',
        value: this.RoleLogSearchOptions.role_name,
      };
    }
    const res = await this._service
      .getRoleLog(data)
      .pipe(
        finalize(() => {
          this.RoleLogLoading = false;
        })
      )
      .toPromise();
    if (res) {
      this.RoleLogList = res.data.datalist;
      this.RoleLogCount = res.data.count;
    }
  }
  addNewRoleNameBtn(): void {
    this.buildForm();
    this._service.getRoleCode().subscribe((res) => {
      if (res) {
        this.RoleNameForm?.get('code')?.setValue(res.data);
      }
    });
    const _modalComponent = this._modal.create({
      nzCentered: true,
      nzTitle: this._service.translateValue(this.translateName + '新建权限名称'),
      nzContent: this.RoleNameTpl,
      nzBodyStyle: {
        padding: '24px 38px',
      },
      nzFooter: [
        {
          label: this._service.translateValue('btn.cancel'),
          shape: 'round',
          type: 'text',
          size: 'large',
          onClick: () => {
            _modalComponent.close();
          },
        },
        {
          autoLoading: true,
          label: this._service.translateValue('btn.save'),
          type: 'primary',
          shape: 'round',
          size: 'large',
          // disabled: () => {
          //   this.RoleNameForm?.get('code')?.enable();
          //   const disabled = this.RoleNameForm?.invalid ?? true;
          //   this.RoleNameForm?.get('code')?.disable();
          //   return disabled;
          // },
          onClick: async () => {
            this.RoleNameForm?.get('code')?.enable();
            const disabled = this.RoleNameForm?.invalid ?? true;
            this.RoleNameForm?.get('code')?.disable();
            const data = this.RoleNameForm?.getRawValue();
            if (disabled) {
              if (data.code === null) {
                this._notice.create('error', this._service.translateValue(this.translateName + '服务器错误，无法保存'), '');
              }
              if (data.name === null || data.name === '') {
                this._notice.create('error', this._service.translateValue(this.translateName + '请输入权限名称'), '');
              }
              return;
            }
            const res: any = await this._service.addRoleName(data).toPromise();
            if (res.code === 200) {
              this.searchOptions = {};
              this._mssage.success(this._service.translateValue('success.save'));
              _modalComponent.close();
              this.selectedRoleNameIndex = -1;
              await this.getAllRoleName(false);
              const id = res.data;
              this.selectedRoleNameIndex = this.RoleNameList.findIndex((item) => item.id === id);
              this.getRoleNameDetail(id);
              this.isEdit = true;
            } else {
              this._notice.error(this._service.translateValue(this.translateName + '错误'), res.message);
            }
          },
        },
      ],
    });
  }
  editRoleNameBtn(): void {
    const item = this.selectedRoleNameItem;
    this.buildForm({
      id: item!.id,
      code: item!.code,
      name: item!.name,
      status: item!.active,
    });
    const _modalComponent = this._modal.create({
      nzCentered: true,
      nzTitle: this._service.translateValue(this.translateName + '修改权限名称'),
      nzContent: this.RoleNameTpl,
      nzBodyStyle: {
        padding: '24px 38px',
      },
      nzFooter: [
        {
          label: this._service.translateValue('btn.cancel'),
          shape: 'round',
          type: 'text',
          size: 'large',
          onClick: () => {
            _modalComponent.close();
          },
        },
        {
          autoLoading: true,
          label: this._service.translateValue('btn.save'),
          type: 'primary',
          shape: 'round',
          size: 'large',
          // disabled: () => {
          //   this.RoleNameForm?.get('code')?.enable();
          //   const disabled = this.RoleNameForm?.invalid ?? true;
          //   this.RoleNameForm?.get('code')?.disable();
          //   return disabled;
          // },
          onClick: async () => {
            this.RoleNameForm?.get('code')?.enable();
            const disabled = this.RoleNameForm?.invalid ?? true;
            this.RoleNameForm?.get('code')?.disable();
            const data = this.RoleNameForm?.getRawValue();
            if (disabled) {
              if (data.name === null || data.name === '') {
                this._notice.create('error', this._service.translateValue(this.translateName + '请输入权限名称'), '');
              }
              return;
            }
            const res: any = await this._service.editRoleName(data).toPromise();
            if (res.code === 200) {
              if (item!.name !== data.name) {
                this.searchOptions = {};
                this.nameOptions = [];
              }
              this._mssage.success(this._service.translateValue('success.save'));
              _modalComponent.close();
              await this.getAllRoleName(false);
              const id = item!.id;
              this.selectedRoleNameIndex = this.RoleNameList.findIndex((item) => item.id === id);
              await this.getRoleNameDetail(id);
              const parentElement = this.RoleNameListWrapTpl.nativeElement;
              const child = parentElement.querySelector('.selected') as HTMLElement;
              parentElement.scrollTo(0, child.offsetTop);
            } else {
              this._notice.error(this._service.translateValue(this.translateName + '错误'), res.message);
            }
          },
        },
      ],
    });
  }
  deleteRoleNameBtn() {
    this._service.getRoleUsers(this.selectedRoleNameItem!.id).subscribe((res) => {
      this.deleteTplHasUsers = res.data.length > 0;
      this.deleteModal = this._modal.create({
        nzCentered: true,
        nzBodyStyle: {
          padding: '0px 8px 8px 8px',
        },
        nzStyle: {
          'border-radius': '16px',
        },
        nzWidth: '320px',
        nzContent: this.deleteTpl,
        nzTitle: this.deleteHeadTpl,
        nzWrapClassName: 'modal-outer',
        nzMaskClosable: false,
        nzFooter: null,
      });
    });
  }
  deleteRoleName() {
    this._service.deleteRoleName(this.selectedRoleNameItem!.id).subscribe((res) => {
      if (res.code === 200) {
        this.isEdit = false;
        this._mssage.success(res.message);
        this.selectedRoleNameIndex = -1;
        this.selectedRoleNameItem = undefined;
        this.getAllRoleName(true);
        this.deleteModal!.close();
      } else {
        this._notice.error(this._service.translateValue(this.translateName + '错误'), res.message, { nzDuration: 0 });
      }
    });
  }
  activeSwitcher(active: boolean) {
    const item = this!.selectedRoleNameItem;
    this._service
      .editRoleName({
        id: item!.id,
        code: item!.code,
        name: item!.name,
        status: active,
      })
      .subscribe((res) => {
        if (res.code === 200) {
          if (active) {
            this._mssage.success(this._service.translateValue(this.translateName + '启用成功'));
          } else {
            this._mssage.success(this._service.translateValue(this.translateName + '停用成功'));
          }
          this._service.getRoleNameDetail(item!.id).subscribe((res) => {
            this.selectedRoleNameItem!.modified_time = res.data.modified_time;
          });
        } else {
          this._notice.error(this._service.translateValue(this.translateName + '错误'), res.message, { nzDuration: 0 });
        }
      });
  }
  FirstMenuChange(item: RoleNameChildModel): void {
    this.realSelectedIndex = this.firstMenuList.indexOf(item) - 1;
    this.updateAllChecked();
  }
  reset(): void {
    this.searchOptions = {};
    this.genTimeSearchArray = [];
    this.selectedRoleNameIndex = undefined;
    this.getAllRoleName(true);
  }
  resetLogSearch() {
    this.RoleLogSearchOptions = {};
    this.getLogList(true);
  }
  listviewOnScroll(event: any): void {}
  switchToRangeMode(isRangeMode: boolean): void {
    if (isRangeMode) {
      if (this.rangeList.length > 0) {
        this.isRangeMode = isRangeMode;
      }
    } else {
      this.isRangeMode = isRangeMode;
    }
    if (!this.selectedFirstMenu?.hasSelectedMenu || this.selectedFirstMenu.isHideForRangeMode) {
      this.selectedFirstMenu = this.firstMenuList[0];
    }
  }
  getIndex(index: number): number {
    if (this.roleNameOrderBy !== 'asc') {
      return index + 1;
    } else {
      return this.RoleNameList.length - index;
    }
  }

  allChecked = false;
  allCheckedIndeterminate = false;
  onAllCheckedChange(event: boolean) {
    this.allChecked = event;
    this.allCheckedIndeterminate = false;
    if (this.selectedFirstMenu!.code === 'ALLMenu') {
      this.roleLineRefs.toArray().forEach((lines) => {
        lines.menuLineRefs.toArray().forEach((item) => item.toggle(event));
      });
    } else {
      this.roleLineRef.menuLineRefs.toArray().forEach((item) => item.toggle(event));
    }
    this.statusChanged();
  }

  updateAllChecked() {
    setTimeout(() => {
      let states = [];
      if (this.selectedFirstMenu!.code === 'ALLMenu') {
        const multiStates = this.roleLineRefs.toArray().map((lines) => {
          return lines.menuLineRefs.toArray().map((item) => item.getAllChecked());
        });
        states = (multiStates as any).flat();
      } else {
        states = this.roleLineRef.menuLineRefs.toArray().map((item) => item.getAllChecked());
      }
      const allChecked = states.every(Boolean);
      const allNotChecked = states.every((checked: boolean) => !checked);
      this.allChecked = allChecked;
      this.allCheckedIndeterminate = !allChecked && !allNotChecked;
    });
  }

  onCopyAuth(event: Event, auth: RoleNameListModel) {
    event.stopPropagation();

    this.buildForm();
    this._service.getRoleCode().subscribe((res) => {
      if (res) {
        this.RoleNameForm?.get('code')?.setValue(res.data);
      }
    });

    const _modalComponent = this._modal.create({
      nzCentered: true,
      nzTitle: this._service.translateValue(this.translateName + '复制权限'),
      nzContent: this.RoleNameTpl,
      nzBodyStyle: {
        padding: '24px 26px',
      },
      nzFooter: [
        {
          label: this._service.translateValue('btn.cancel'),
          shape: 'round',
          type: 'text',
          size: 'large',
          onClick: () => {
            _modalComponent.close();
          },
        },
        {
          autoLoading: true,
          label: this._service.translateValue('btn.save'),
          type: 'primary',
          shape: 'round',
          size: 'large',
          onClick: async () => {
            this.RoleNameForm?.get('code')?.enable();
            const disabled = this.RoleNameForm?.invalid ?? true;
            this.RoleNameForm?.get('code')?.disable();
            const data = this.RoleNameForm?.getRawValue();
            delete data.id;
            data.role_id = auth.id;
            if (disabled) {
              if (data.code === null) {
                this._notice.create('error', this._service.translateValue(this.translateName + '服务器错误，无法保存'), '');
              }
              if (data.name === null || data.name === '') {
                this._notice.create('error', this._service.translateValue(this.translateName + '请输入权限名称'), '');
              }
              return;
            }
            const res: any = await firstValueFrom(this._service.copyRoleName(data));
            if (res.code === 200) {
              this.searchOptions = {};
              this._mssage.success(this._service.translateValue(this.translateName + '复制成功'));
              _modalComponent.close();
              this.selectedRoleNameIndex = -1;
              await this.getAllRoleName(false);
              const id = res.data.new_role_id;
              this.selectedRoleNameIndex = this.RoleNameList.findIndex((item) => item.id === id);
              this.getRoleNameDetail(id);
              this.isEdit = true;
            } else {
              this._notice.error(this._service.translateValue(this.translateName + '错误'), res.message);
            }
          },
        },
      ],
    });
  }
}
