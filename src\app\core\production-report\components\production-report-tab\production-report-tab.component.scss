.tab-wrapper {
  height: 46px;
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  border-bottom: 1px solid #d4d7dc;
  border-radius: 4px 4px 0 0;
  div {
    display: flex;
    gap: 8px;
    align-items: center;
    &:first-child {
      align-self: flex-end;
    }
  }
}
.tab-item {
  height: 36px;
  background-color: #f7f8fa;
  border: 1px solid #d0d5d9;
  padding: 0 8px;
  line-height: 36px;
  font-size: 16px;
  font-weight: 500;
  color: #515665;
  border-bottom: none;
  border-radius: 6px 6px 0 0;
  min-width: 80px;
  text-align: center;
  transition: 0.3s;
  cursor: pointer;
  &:hover {
    color: var(--fl-pretty-color);
  }
}
.tab-item.active {
  background-color: var(--fl-pretty-color-bg-light);
  border-color: var(--fl-pretty-color);
  color: var(--fl-pretty-color);
}
