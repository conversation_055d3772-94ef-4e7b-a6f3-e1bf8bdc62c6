import { RecommendKeyEnum } from './recommend.enum';

export type RecommendElementOption = {
  name: string;
  key: RecommendKeyEnum;
};

export type RecommendElementObjType = {
  [key in RecommendKeyEnum]: RecommendElementOption;
};

export const RecommendElementObj: RecommendElementObjType = {
  [RecommendKeyEnum.style_class]: { name: '款式分类', key: RecommendKeyEnum.style_class },
  [RecommendKeyEnum.product_type]: { name: '产品类型', key: RecommendKeyEnum.product_type },
  [RecommendKeyEnum.quality_level]: { name: '品质', key: RecommendKeyEnum.quality_level },
  [RecommendKeyEnum.process_type]: { name: '生产类型', key: RecommendKeyEnum.process_type },
  [RecommendKeyEnum.start_order_quantity]: { name: '起订量', key: RecommendKeyEnum.start_order_quantity },
  [RecommendKeyEnum.fast_reply]: { name: '快反', key: RecommendKeyEnum.fast_reply },
  [RecommendKeyEnum.accept_third_quality_check]: { name: '接受第三方质检', key: RecommendKeyEnum.accept_third_quality_check },
  [RecommendKeyEnum.efficiency]: { name: '效率', key: RecommendKeyEnum.efficiency },
  [RecommendKeyEnum.pass_rate]: { name: '合格率', key: RecommendKeyEnum.pass_rate },
  [RecommendKeyEnum.accurate_delivery_rate]: { name: '准交率', key: RecommendKeyEnum.accurate_delivery_rate },
  [RecommendKeyEnum.customer_brand]: { name: '客户品牌', key: RecommendKeyEnum.customer_brand },
  [RecommendKeyEnum.payment_condition]: { name: '付款条件', key: RecommendKeyEnum.payment_condition },
  [RecommendKeyEnum.has_customs_clearance_qualification]: {
    name: '报关资质',
    key: RecommendKeyEnum.has_customs_clearance_qualification,
  },
};
