export enum PlanOperateBtnEnum {
  edit = 'edit', // 编辑
  allocateOrder = 'allocateOrder', // 分配订单
  publish = 'publish', // 发布
  revoke = 'revoke', // 撤销
  exitEdit = 'exitEdit', // 退出编辑
}

// 订单计划规则方式
export enum OrderCalculationTypeEnum {
  sam = 1,
  average = 2,
}

// 订单分配方式
export enum OrderAllocationTypeEnum {
  order = 1,
  color = 2,
  po = 3,
}

// 工厂分配类型
export enum FactoryAllocationTypeEnum {
  factory = 1, // 工厂
  line = 2, // 产线
}

export enum ProductionPlanSubjectEventEnum {
  onZoomChange, // 缩放比例
  onEdit,
  revoke, // 撤销
  scrollLeft, // 滚动到左侧
  move, // 移动甘特图
}
