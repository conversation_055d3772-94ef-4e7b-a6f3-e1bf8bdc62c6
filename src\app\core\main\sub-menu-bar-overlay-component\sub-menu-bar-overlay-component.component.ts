import { AfterViewInit, Component, EventEmitter, Output } from '@angular/core';
import { MenuItem } from 'fl-common-lib';
import { Router } from '@angular/router';
import { animate, state, style, transition, trigger, AnimationEvent } from '@angular/animations';
import { AnimationCurves, AnimationDuration } from 'ng-zorro-antd/core/animation';
import { MainService } from '../main.service';
const ANIMATION_TRANSITION_IN = `${AnimationDuration.BASE} ${AnimationCurves.EASE_OUT_QUINT}`;
const ANIMATION_TRANSITION_OUT = `${AnimationDuration.BASE} ${AnimationCurves.EASE_IN_QUINT}`;

@Component({
  animations: [
    trigger('slide', [
      state(
        'hidden',
        style({
          opacity: 0,
          transform: 'scaleX(80%)',
          transformOrigin: 'left',
        })
      ),
      state(
        'visible',
        style({
          opacity: 1,
          transform: 'scaleX(100%)',
          transformOrigin: 'left',
        })
      ),
      transition('hidden => visible', [animate(ANIMATION_TRANSITION_IN)]),
      transition('visible => hidden', [animate(ANIMATION_TRANSITION_OUT)]),
    ]),
  ],
  selector: 'app-sub-menu-bar-overlay-component',
  templateUrl: './sub-menu-bar-overlay-component.component.html',
  styleUrls: ['./sub-menu-bar-overlay-component.component.scss'],
})
export class SubMenuBarOverlayComponentComponent implements AfterViewInit {
  isAnimation = false;
  @Output() onClose = new EventEmitter<null>();
  menuList: { groupName: string; children: MenuItem[] }[] = [];
  constructor(private router: Router, private _service: MainService) {}
  ngAfterViewInit(): void {
    setTimeout(() => {
      this.isAnimation = true;
    }, 0);
  }
  updateMenuList(menulist: MenuItem[]) {
    this.menuList = [];
    const groupedMenu = new Map<string, MenuItem[]>();
    menulist.forEach((menu) => {
      if (menu.value) {
        const groupName = menu.groupName ?? '';
        const group = groupedMenu.get(groupName);
        if (group) {
          group.push(menu);
        } else {
          groupedMenu.set(groupName, [menu]);
        }
      }
    });
    groupedMenu.forEach((children, groupName) => {
      if (children.length > 0) {
        this.menuList.push({ groupName, children });
      }
    });
  }
  navigator(target: MenuItem) {
    if (this._service.isDashboardLink(target)) {
      window.open(target.path, '_blank');
      return;
    }
    this.router.navigate([target.path]);
    this.close();
  }
  close() {
    this.isAnimation = false;
  }
  animationFinish(event: AnimationEvent) {
    if (event.fromState === 'visible' && event.toState === 'hidden') {
      this.onClose.emit();
    }
  }
}
