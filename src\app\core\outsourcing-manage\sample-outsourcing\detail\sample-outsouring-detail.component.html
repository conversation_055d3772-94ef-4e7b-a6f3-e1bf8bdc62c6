<!-- 头部按钮 -->
<flc-app-header [showTemplate]="true" [headerTopTitle]="headerTopTitleTpl" [headerBtn]="headerBtnTmpl"></flc-app-header>
<ng-template #headerTopTitleTpl>
  <div class="head-title">
    {{ translateName + (id == 'new' ? '新建打样外发' : isEdit ? '编辑打样外发' : '打样外发详情') | translate }}
    <span *ngIf="detailData?.status" class="status" [class]="'status' + detailData?.status">{{ detailData?.status_value }}</span>
  </div>
</ng-template>
<ng-template #headerBtnTmpl>
  <div class="history-list" *ngIf="detailData && detailData.has_history">
    <span
      >{{ translateName + '当前版本' | translate }}：{{
        detailData.modify_time ? (detailData.modify_time | date: 'yyyy/MM/dd HH:mm:ss') : ''
      }}</span
    >
    <span (click)="onOpenHistoryDrawer()">{{ translateName + '历史版本' | translate }}</span>
  </div>
  <div class="btn-box">
    <app-sample-outsourcing-operate-button (onButtonAction)="onButtonAction($event)" [status]="detailData?.status || 0" [isEdit]="isEdit">
    </app-sample-outsourcing-operate-button>
  </div>
</ng-template>

<div class="container">
  <!-- 退回修改原因 -->
  <div *ngIf="detailData?.status == statusEnum.wait_modify" class="reason-box">
    <i class="reason-icon" nz-icon [nzIconfont]="'icon-jinggao'"></i>
    <!-- 退回修改原因 -->
    <span style="margin-right: 5px">{{ detailData?.modify_time ? (detailData?.modify_time | date: 'yyyy/MM/dd HH:mm:ss') : '' }}</span>
    <flc-text-truncated
      *ngIf="detailData?.reason_for_return"
      class="status-reason"
      [data]="detailData?.reason_for_return"
      [tooltipOverlayClassName]="'sample-task-tool'"></flc-text-truncated>
  </div>
  <!-- 外发进度 -->
  <app-sample-outsouring-progress
    [id]="detailData.sample_order_id"
    *ngIf="
      detailData &&
      [statusEnum.wait_order, statusEnum.wait_outsourcing_send, statusEnum.wait_outsourcing_sended, statusEnum.processed].includes(
        detailData.status
      )
    "></app-sample-outsouring-progress>
  <!-- 基本信息 -->
  <app-sample-outsourcing-basic-info
    [detailData]="detailData"
    [simpleData]="simpleData"
    [isEdit]="isEdit"
    [hidden]="!isFullScreenMode"></app-sample-outsourcing-basic-info>
  <!-- 技术档案 -->
  <app-sample-oursourcing-tech-archive
    *ngIf="simpleData"
    [pkgData]="simpleData"
    (toggleFullScreen)="toggleFullScreen($event)"></app-sample-oursourcing-tech-archive>
</div>

<nz-modal
  [(nzVisible)]="returnRevisionVisible"
  [nzTitle]="'sampleOutsourcing.sampleOutsourcingMessage.退回修改' | translate"
  [nzContent]="modalContent"
  [nzMaskClosable]="false"
  [nzFooter]="modalFooter"
  nzWidth="520px"
  (nzOnCancel)="onCloseModal()"
  (nzAfterClose)="onCloseModal()">
  <ng-template #modalContent>
    <div class="body">
      <form nz-form [formGroup]="returnRevisionForm">
        <nz-form-item>
          <nz-form-control [flcErrorTip]="'sampleOutsourcing.sampleOutsourcingMessage.退回原因' | translate">
            <nz-textarea-count [nzMaxCharacterCount]="100" class="inline-count">
              <textarea
                formControlName="reason"
                nz-input
                [nzAutosize]="{ minRows: 4, maxRows: 4 }"
                [maxlength]="100"
                placeholder="请输入退回原因"
                flcInputTrim></textarea>
            </nz-textarea-count>
          </nz-form-control>
        </nz-form-item>
      </form>
    </div>
  </ng-template>

  <ng-template #modalFooter>
    <div class="footer">
      <button nz-button nzType="default" flButton="default-negative" [nzShape]="'round'" (click)="onCloseModal()">
        {{ 'btn.cancel' | translate }}
      </button>
      <button nz-button nzType="primary" flButton="default-positive" [nzShape]="'round'" (click)="onRevision()">
        {{ 'btn.ok' | translate }}
      </button>
    </div>
  </ng-template>
</nz-modal>
