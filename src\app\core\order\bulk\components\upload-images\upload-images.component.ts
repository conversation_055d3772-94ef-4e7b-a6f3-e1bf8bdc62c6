import { Component, OnInit, Input, ViewChildren, QueryList } from '@angular/core';
import { FormArray, FormBuilder, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { FlcDrawerHelperService, FlcFilesUploadComponent, FlcOssUploadService, OSSCustomConfig, resizable } from 'fl-common-lib';
import { groupBy } from 'lodash';
import { NzMessageService } from 'ng-zorro-antd/message';
@Component({
  selector: 'app-upload-images',
  templateUrl: './upload-images.component.html',
  styleUrls: ['./upload-images.component.scss'],
  providers: [FlcOssUploadService],
})
@resizable()
export class UploadImagesComponent implements OnInit {
  @ViewChildren('uploaderRef') uploaderRef!: QueryList<FlcFilesUploadComponent>;
  @Input() set list(list: any[]) {
    this.initTable(list);
  }
  translateName = 'bulk.';
  ossCustomConfig: OSSCustomConfig = {
    namespace: 'bulk-order',
    authUrl: '/service/frontapi/v1/oss',
    filePath: '/service/frontapi/v1/oss/bulk-order',
    authParams: {
      biz_type: 'order',
    },
  };
  tableHeight = '500px';
  formData = new FormGroup({ orders: new FormArray([]) });
  constructor(
    private _drawerHelp: FlcDrawerHelperService,
    private fb: FormBuilder,
    private _msg: NzMessageService,
    private _translate: TranslateService
  ) {}

  ngOnInit() {
    (this as any).addResizePageListener();
    this.resizePage();
  }
  ngOnDestroy() {
    (this as any).removeResizePageListener();
  }

  resizePage() {
    this.tableHeight = window.innerHeight - 194 + 'px';
  }
  dataWm = new WeakMap();
  initTable(list: any[]) {
    const groupedData = groupBy(list, 'style_code');
    const groupedList = Object.keys(groupedData).map((style_code) => groupedData[style_code]);
    groupedList.forEach((list: any[]) => {
      list[0].rowspan = list.length;
    });
    const flatList = (groupedList as any).flat();

    this.formData = new FormGroup({
      orders: this.fb.array(
        flatList.map((item: any) => {
          return this.fb.group({
            rowspan: [item.rowspan || 0],
            bulk_order_id: [item.id || item.bulk_order_id],
            style_code: [item.style_code],
            io_code: [item.io_code || item.bulk_order_code],
            pictures: [[]],
            files: [[]],
          });
        })
      ),
    });
  }

  onClose() {
    this._drawerHelp.closeDrawer();
  }
  onConfirm() {
    if (this.uploaderRef.toArray().some((ref: any) => ref.is_uploading())) {
      this._msg.warning(this._translate.instant(this.translateName + '请等待图片和附件上传完成'));
      return;
    }
    const orders = this.formData
      .getRawValue()
      .orders.map((item: any) => {
        return {
          bulk_order_id: item.bulk_order_id,
          pictures: item.pictures,
          files: item.files,
        };
      })
      .filter((item: any) => item.pictures.length || item.files.length);

    this._drawerHelp.closeDrawer({ orders });
  }
}
