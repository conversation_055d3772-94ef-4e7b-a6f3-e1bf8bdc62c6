// 基本信息配置
export const BasicInfoConfig: any = [
  {
    label: '企划名称',
    key: 'name',
    nzSpan: 6,
  },
  {
    label: '企划类型',
    key: 'type_name',
    nzSpan: 6,
  },
  {
    label: '企划编码',
    key: 'code',
    nzSpan: 6,
  },
  {
    label: '品牌',
    key: 'brand_name',
    nzSpan: 6,
  },
  {
    label: '年份',
    key: 'year',
    nzSpan: 6,
  },
  {
    label: '季节',
    key: 'season_id',
    nzSpan: 6,
  },
  {
    label: '月份',
    key: 'month',
    nzSpan: 6,
  },
  {
    label: '波段',
    key: 'band_name',
    nzSpan: 6,
  },
  {
    label: '上市日期',
    key: 'market_time',
    nzSpan: 6,
  },
];

// 品类结构规划配置
// 外层表格
export const MaterialOutuerHeaderConfig: any = [
  {
    label: '物料类型',
    key: 'type',
    type: 'template',
    width: '112px',
    formatter: (item: any) => {
      return item.type === 1 ? '面料' : item.type === 2 ? '辅料' : '';
    },
  }, // //物料类型：1：面料，2：辅料
  { label: '分类', key: 'category_name', type: 'text', width: '128px' },
  { label: '开发比例(%)', key: 'develop_percent', type: 'text', width: '112px' },
  { label: '开发倍率', key: 'develop_ratio', type: 'text', width: '112px' },
  {
    label: '成本',
    key: 'price',
    type: 'template',
    width: '128px',
    formatter: (item: any) => {
      return item?.start_price && item?.end_price ? item?.start_price + '-' + item?.end_price : '';
    },
  }, // 起始价格start_price，截止价格end_price
  { label: '系列名称', key: 'series_name', type: 'text', width: '128px' },
  {
    label: '是否主推',
    key: 'main_push',
    type: 'template',
    width: '96px',
    formatter: (item: any) => {
      return item.main_push === 1 ? '是' : '否';
    },
  },
  { label: '用途', key: 'purpose', type: 'text', width: '128px' },
  { label: '负责人', key: 'charge_user_name', type: 'text', width: '112px' },
];

// 内层表格
export const MaterialInnerHeaderConfig: any = [
  { label: '物料编码', key: 'raw_material_code', width: '128px' },
  { label: '物料名称', key: 'raw_material_name', width: '128px' },
  { label: '供应商', key: 'supplier_name', width: '128px' },
  { label: '供应商色号', key: 'supplier_color_name', width: '128px' },
  { label: '成分', key: 'composition', width: '100px' },
  { label: '规格', key: 'specifications', width: '100px' },
  { label: '计划投产数量', key: 'plan_production', width: '128px' },
  { label: '单位', key: 'unit_name', width: '104px' },
  { label: '备注', key: 'remark', width: '128px' },
];

// 开发时间节点配置
export const TimeNodeHeaderConfig: any = [
  { label: '日期', key: 'date_range', width: '200px' },
  { label: '工作内容', key: 'job_content', width: '240px' },
  { label: '负责人', key: 'charge_user_name', width: '144px' },
  { label: '备注', key: 'remark' },
];

// 季节
export const SeasonObj = {
  1: '春季',
  2: '夏季',
  3: '秋季',
  4: '冬季',
  5: '早春季',
  6: '早秋季',
};
