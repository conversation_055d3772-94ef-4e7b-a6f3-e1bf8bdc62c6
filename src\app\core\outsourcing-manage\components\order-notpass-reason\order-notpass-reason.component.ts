import { Component, OnInit } from '@angular/core';
import { NzModalRef } from 'ng-zorro-antd/modal';
import { FormBuilder, Validators } from '@angular/forms';
import { FlcValidatorService } from 'fl-common-lib';

@Component({
  selector: 'app-order-notpass-reason',
  templateUrl: './order-notpass-reason.component.html',
  styleUrls: ['./order-notpass-reason.component.scss'],
})
export class OrderNotpassReasonComponent implements OnInit {
  formGroup!: any;
  constructor(private modal: NzModalRef, private fb: FormBuilder, private validator: FlcValidatorService) {}

  ngOnInit() {
    this.formGroup = this.fb.group({
      reason: [null, Validators.required],
    });
  }

  handleCancel() {
    this.modal.close({ success: false });
  }
  handleOk() {
    if (this.validator.formIsInvalid(this.formGroup)) {
      this.validator.markFormDirty(this.formGroup);
    } else {
      this.modal.close({ success: true, reason: this.formGroup.get('reason').value });
    }
  }
}
