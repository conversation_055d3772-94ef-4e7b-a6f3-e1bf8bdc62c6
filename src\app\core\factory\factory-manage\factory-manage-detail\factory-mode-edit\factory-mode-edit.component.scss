.mode-setting-container {
  padding-bottom: 16px;

  .title-label {
    font-size: 16px;
    font-weight: 500;
    color: #54607c;
  }

  nz-divider {
    margin: 8px 0px 16px 0px;
  }

  .mode-content {
    display: flex;
    align-items: baseline;

    & > div:nth-child(1) {
      width: 8%;
      text-align: right;
    }
  }

  .label-required:before {
    content: '*';
    color: red;
  }

  nz-radio-group {
    display: flex;
    gap: 10px;
    align-items: center;
    position: relative;

    label {
      border-radius: 2px;
    }

    .ant-radio-button-wrapper:not(.ant-radio-button-wrapper-disabled):hover {
      border: 1px solid #4d96ff;
    }

    .ant-radio-button-wrapper:not(:first-child):not(.ant-radio-button-wrapper-disabled):hover::before {
      background-color: #4d96ff;
    }

    //三角形角标
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):after {
      position: absolute;
      background: #4d96ff;
      content: '✓';
      color: #ffffff;
      top: -1px;
      right: -1px;
      width: 22px;
      height: 22px;
      clip-path: polygon(0 0, 100% 0, 100% 100%);
      display: flex;
      align-items: flex-end;
      justify-content: flex-end;
      padding-right: 2px;
      font-size: 12px;
    }
  }
}
