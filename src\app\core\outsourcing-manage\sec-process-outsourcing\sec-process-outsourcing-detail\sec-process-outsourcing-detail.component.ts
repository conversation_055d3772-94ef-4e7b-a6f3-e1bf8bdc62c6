import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON>, <PERSON><PERSON>hildren, ChangeDetectorRef, ViewChild } from '@angular/core';
import { FormArray, FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { forkJoin, Subscription } from 'rxjs';
import { TranslatePipe } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { OrderStatus, EditModeEnum, PoChangeModeEnum } from '../model/sec-process-outsourcing.enum';
import { IoLine, Line, Po, Pos } from '../../components/models/order-outsourcing-interface';
import { OrderOutsourcingService } from '../../components/order-outsourcing.service';
import { OrderSecProcessOutsourcingService } from '../sec-process-outsourcing.service';
import { ExtraProcessAssignInfoInterface } from '../model/sec-process-outsourcing.interface';
import { setStyle } from '../sec-process-outsourcing-list/sec-process-outsourcing-list.config';
import { FlcColorSizeTableComponent, FlcValidatorService, FlcUtilService } from 'fl-common-lib';
import { OrderOutsourcingFactoryItemComponent } from '../../components/order-outsourcing-factory-item/order-outsourcing-factory-item.component';
import { SecProcessBannerComponent } from '../components/sec-process-banner/sec-process-banner.component';
import { topTitleText, defaultProgress } from './sec-process-outsourcing-detail.config';
import { AppStorageService } from 'src/app/shared/app-storage.service';
import { RecommendFactoryService } from 'fl-sewsmart-lib/recommend-factory';

@Component({
  selector: 'app-sec-process-outsourcing-detail',
  templateUrl: './sec-process-outsourcing-detail.component.html',
  styleUrls: ['./sec-process-outsourcing-detail.component.scss'],
  providers: [TranslatePipe, RecommendFactoryService],
})
export class SecProcessOutsourcingDetailComponent implements OnInit {
  @ViewChildren('colorSizeTable') colorSizeTableRefs!: QueryList<FlcColorSizeTableComponent>;
  @ViewChildren('colorSizeSurplusTable') colorSizeTableSurplusRefs!: QueryList<FlcColorSizeTableComponent>;
  @ViewChildren('factoryItem') factoryItem!: QueryList<OrderOutsourcingFactoryItemComponent>; // 加工厂每个盒子
  @ViewChild('secProcessRef') secProcessRef!: SecProcessBannerComponent;
  orderDetail: any; // 详情数据
  id!: string | number;
  io_id!: string | number;
  get topTitle() {
    return topTitleText[this.editMode];
  }
  editMode: 'add' | 'edit' | 'read' = 'read'; // 是否是编辑模式
  showMainPanel = false;
  orderStatus: OrderStatus = 1; // 当前订单状态
  OrderStatus = OrderStatus;
  editModeEnum = EditModeEnum;
  // 大货单号 form container
  searchOptionFetchUrl = this._service.bulkList;
  detailFormConfig: any = [];
  detailForm!: any;
  constructor(
    private _route: ActivatedRoute,
    private _router: Router,
    private fb: FormBuilder,
    private _validator: FlcValidatorService,
    public _service: OrderSecProcessOutsourcingService,
    private _notification: NzNotificationService,
    private _message: NzMessageService,
    private translatePipe: TranslatePipe,
    private flcUtilService: FlcUtilService,
    private outsourcingService: OrderOutsourcingService,
    private cd: ChangeDetectorRef,
    private _storage: AppStorageService
  ) {}
  io_basic: any; // io基本信息
  pos: Pos = []; // po交付单所有数据
  io_lines!: Array<IoLine>; // 合计的数据
  surplusPos: Pos = this.outsourcingService.surplusPos; // 剩余未分配
  surplusLines: Array<Line> = this.outsourcingService.surplusLines; //剩余未分配合计数据
  extra_process_info: any = []; // 二次工艺数据
  allFactoryInfo: any = {}; // 所有二次工艺的外发厂数据
  secProcessEventEmitter?: Subscription;
  changeValueEmitter!: Subscription;
  allSurplusPos: any = {}; // 所有二次工艺的未分配数据
  delete_distributions: Array<number> = []; // 删除的颜色尺码id
  delete_factory_infos: Array<number> = []; // 删除的外发厂id
  ioCodeDefaultValue = { value: '', label: '' };
  extraOptions: { label: any; value: any; hide: boolean; active?: boolean; disabled?: boolean }[] = [];
  factoryDataChange = false;
  isCancelOrder = true;
  undeletableLineIds: any = {}; // 按二次工艺分接单后不可删除的颜色尺码
  ngOnInit() {
    this._service.btnArr = [...this._storage.getUserActions('outsourcing-manage/sec-process-outsourcing')];

    this.id = this._route.snapshot.paramMap.get('id') || '';
    this.io_id = this._route.snapshot.queryParamMap.get('io_id') || '';
    this.editMode = this.id === EditModeEnum.add ? EditModeEnum.add : EditModeEnum.read;
    this.outsourcingService.factory_type = 2;
    this.initEditorInfoDef();
    this.initEditorFormValidation();
    if (this.editMode !== EditModeEnum.add) {
      this.getDetail();
    }
    this.changeValueEmitter = this.outsourcingService.changeValue.subscribe((res) => {
      const extra_process_info = this.outsourcingService.nowSelect_extra_process_info;
      if (res.type === 'sizeChange') {
        const surplusPos: any = [];

        const _originPos: Array<any> = [];
        for (let i = 0; i < this.detailForm.get('factorys').value.length; i++) {
          const _pos = this.factoryItem.get(i)!._pos;
          if (!_pos.length) continue;
          _pos.forEach((po: Po) => {
            const obj: Po = { po_basic: po.po_basic, po_lines: [] };
            po.po_lines.forEach((line: Line) => {
              obj.po_lines.push({ ...line });
            });
            if (obj.po_lines.length) {
              // 修复在已结单的加工厂A中 Po1添加数据 在删除数据。在加工厂B中添加po1，不显示x按钮图标
              const result = this.outsourcingService.deepclone(obj);
              this.outsourcingService.originPos.forEach((e) => {
                result.po_lines.forEach((line: any) => {
                  const tmp1: any = e.po_lines.find((l: any) => l.line_uuid === line.line_uuid);
                  if (tmp1) {
                    const tmp = _originPos.find((l: any) => l.line_uuid === tmp1.line_uuid) ?? tmp1;
                    line.qty = tmp.qty - line.qty;
                    line.over = line.qty < 0;
                    _originPos.push({ ...JSON.parse(JSON.stringify(line)) });
                  }
                });
              });
              result.po_basic.deletable = true;
              surplusPos.push(result);
            }
          });
        }
        this.outsourcingService.setSurplusPos([...surplusPos]);
        this.outsourcingService.surplusLines = this.outsourcingService.serialLines(
          this.outsourcingService.getLines(this.outsourcingService.surplusPos)
        );
      }
      if (res.type === 'surplusPos') {
        this.surplusPos = this.outsourcingService.serialPos(this.outsourcingService.serialNumberLines(this.outsourcingService.surplusPos));
        this.allFactoryInfo[extra_process_info?.extra_process_id] = this.outsourcingService.deepclone(this.factorys.value);
      }
      if (res.type === 'surplusLines') {
        this.surplusLines = this.outsourcingService.serialLines(this.outsourcingService.getLines(this.outsourcingService.surplusPos));
      }
      this.allSurplusPos[extra_process_info?.extra_process_id] = [
        this.surplusPos,
        this.outsourcingService.serialLines(this.outsourcingService.getLines(this.surplusPos)),
      ];
      this.cd.detectChanges();
    });
    this.listenSecProcessEvent();
  }
  ngAfterViewInit() {
    this.cd.detectChanges();
  }
  ngOnDestroy(): void {
    this.outsourcingService?.resetData();
    this.secProcessEventEmitter?.unsubscribe();
    this.changeValueEmitter?.unsubscribe();
  }
  /* 监听二次工艺tab切换和外发厂切换以及添加交付单时 */
  listenSecProcessEvent() {
    this.secProcessEventEmitter = this.outsourcingService.secProcessEventEmitter.subscribe((res) => {
      const extra_process_info: ExtraProcessAssignInfoInterface = this.outsourcingService.nowSelect_extra_process_info;
      if (res?.onchange === 'toggle') {
        this.factorys.controls[res?.factoryIndex]?.get('is_open')?.setValue(res?.toggle);
        this.allFactoryInfo[extra_process_info.extra_process_id] = this.outsourcingService.deepclone(this.factorys.value);
      }
      if (extra_process_info && res?.onchange === 'deleteFactory') {
        if (res?.factoryId) {
          this.delete_factory_infos.push(res?.factoryId);
        }
        this.allFactoryInfo[extra_process_info.extra_process_id] = this.outsourcingService.deepclone(this.factorys.value);

        this.refreshCellEditable(
          this.allFactoryInfo[extra_process_info.extra_process_id],
          this.allSurplusPos[extra_process_info.extra_process_id][0],
          extra_process_info.extra_process_id,
          false
        );
        this.factoryDataChange = true;
      }
      // 选择外发厂
      if (extra_process_info && res?.onchange === 'factoryChange') {
        const factoryList: Array<ExtraProcessAssignInfoInterface> = (this.allFactoryInfo[extra_process_info.extra_process_id] = [
          ...this.factorys.value,
        ]);
        // 验证同一个二次工艺是否选择了同一个外发厂
        const factoryCode = factoryList.map((item) => item.factory_code);
        for (const item of factoryList) {
          if (!item.factory_code) {
            continue;
          }
          const firstIndex = factoryCode.findIndex((val) => val === item.factory_code);
          const lastIndex = factoryCode.lastIndexOf(item.factory_code);
          if (firstIndex !== lastIndex) {
            setTimeout(() => {
              this.detailForm.get('factorys').controls[res.factoryIndex].reset({
                deletable: res?.preValue?.deletable,
                factory_id: res?.preValue?.factory_id,
                factory_short_name: res?.preValue?.factory_short_name,
                factory_code: res?.preValue?.factory_code,
                factory_name: res?.preValue?.factory_name,
                pos: res?.pos,
                lines: this.outsourcingService.getLines(res?.pos),
                extra_process_name: extra_process_info?.extra_process_name,
                extra_process_id: extra_process_info?.extra_process_id,
              });
              this.allFactoryInfo[extra_process_info.extra_process_id] = this.outsourcingService.deepclone(this.factorys.value);
            }, 0);
            this._message.error(
              `${this.translateKey('outsourcingMessage.已有加工厂')}${item.factory_short_name}，${this.translateKey(
                'outsourcingMessage.请重新选择'
              )}`
            );
            return;
          }
        }
        // 记录当前选择的外发厂信息，如果下次选择了重复的用这个数据恢复
        this.factoryItem.get(res.factoryIndex)?.setPreValue(this.factorys.value[res.factoryIndex]);
        // 不同二次工艺同一个加工厂需要同步数据
        const factory = this.factorys.controls[res?.factoryIndex].value || []; // 当前加工厂老数据
        const { pos, lines, haveSameFactoryPos } = this.getRepeatFactoryForDifProcess(factory);
        // 编辑时，切换外发厂需要删掉之前外发厂
        if (this.editMode === EditModeEnum.edit) {
          if (factory.factory_id) {
            this.delete_factory_infos.push(factory.factory_id);
            this.factorys.controls[res?.factoryIndex].get('factory_id')?.setValue(null);
          }
        }
        if (pos && lines && haveSameFactoryPos) {
          this.factorys.controls[res?.factoryIndex].get('pos')?.setValue(pos);
          this.factorys.controls[res?.factoryIndex].get('lines')?.setValue(lines);
          this.factoryItem.get(res?.factoryIndex)?.setPos(pos);
          this.factoryItem.get(res?.factoryIndex)?.setPoLines(this.outsourcingService.serialLines(lines));
          // 如果是复制的其他二次工艺的相同的外发厂数据，清除保存的未分配数据，然后在filterSurplusPos方法中重新计算
          this.allSurplusPos[extra_process_info.extra_process_id] = [];
          // 如果一个工厂获得了其他工艺下相同工厂的数据，则当前工艺下的其他工厂内相同的交付单的相同颜色尺码需要去掉
          const newFactoryValue = this.factorys.controls[res?.factoryIndex].value || []; // 当前加工厂新数据
          const newFactoryPosMap = new Map();
          const delete_distributions: Array<number> = [];
          newFactoryValue?.pos?.map((po: Po) => {
            newFactoryPosMap.set(po.po_basic.id, po.po_lines);
          });
          this.factorys.controls.forEach((ctr, index) => {
            const factory_short_name = ctr.get('factory_short_name')?.value;
            if (factory_short_name !== newFactoryValue?.factory_short_name) {
              const pos = ctr.get('pos')?.value;
              pos.forEach((po: Po) => {
                // 当前外发厂同步其他工艺下相同外发厂数据时，同一个二次工艺下不同外发厂相同po单的数据需要删掉
                const newFactoryLines: Array<Line> = newFactoryPosMap.get(po.po_basic.id);
                if (newFactoryLines) {
                  const newFactoryLineMap = new Map();
                  newFactoryLines.forEach((line) => {
                    newFactoryLineMap.set(line.id, line);
                  });
                  po.po_lines?.forEach((line: Line) => {
                    const newFactoryLine: Line = newFactoryLineMap.get(line.id);
                    if (newFactoryLine && newFactoryLine.qty !== null && line.deletable) {
                      line.qty = null;
                      if (line.order_id) {
                        delete_distributions.push(line.order_id);
                      }
                    }
                  });
                }
              });
              const _pos = this.outsourcingService.serialNumberLines(pos, false);
              const newLines = this.outsourcingService.getLines(_pos);
              ctr.get('pos')?.setValue(_pos);
              ctr.get('lines')?.setValue(newLines);
              this.factoryItem.get(index)?.setPos(this.outsourcingService.deepclone(_pos));
              this.factoryItem.get(index)?.setPoLines(this.outsourcingService.serialLines(newLines));
            }
          });
          this.allFactoryInfo[extra_process_info.extra_process_id] = this.outsourcingService.deepclone(this.factorys.value);
          // 计算当前未分配数据
          this.filterSurplusPos(this.allFactoryInfo[extra_process_info.extra_process_id], extra_process_info.extra_process_id);
          this.outsourcingService.surplusPos = this.allSurplusPos[extra_process_info.extra_process_id][0];
          this.outsourcingService.surplusLines = this.allSurplusPos[extra_process_info.extra_process_id][1];
          this.delete_distributions = Array.from(new Set([...this.delete_distributions, ...delete_distributions]));
        } else if (!haveSameFactoryPos) {
          if (res.tabIsEmpty) {
            if (this.outsourcingService.surplusLines.length === 0) {
              this.allFactoryInfo[extra_process_info.extra_process_id] = this.outsourcingService.deepclone(this.factorys.value);
              return;
            }
            const pos = this.outsourcingService.serialNumberLines(
              this.outsourcingService.getPoAllLine(this.outsourcingService.deepclone(this.outsourcingService.surplusPos)),
              false
            );

            const lines = this.outsourcingService.getLines(pos);
            this.factorys.controls[res?.factoryIndex].get('pos')?.setValue(pos);
            this.factorys.controls[res?.factoryIndex].get('lines')?.setValue(lines);
            this.factoryItem.get(res?.factoryIndex)?.setPos(pos);
            this.factoryItem.get(res?.factoryIndex)?.setPoLines(this.outsourcingService.serialLines(lines));
            this.outsourcingService.surplusPos = [];
            this.outsourcingService.surplusLines = [];
          }
        }
        this.cd.detectChanges();
        this.factoryDataChange = true;

        //根据未分配数据更新cellEditable状态
        this.refreshCellEditable(
          this.allFactoryInfo[extra_process_info.extra_process_id],
          this.allSurplusPos[extra_process_info.extra_process_id][0],
          extra_process_info.extra_process_id,
          false
        );
      }
      // 交付单数据修改
      if (extra_process_info && res?.onchange === 'poChange') {
        this.factoryDataChange = true;
        const delete_distributions: any = [];
        const factory = this.factorys.value[res?.factoryIndex];
        const removedPoIds: Array<number> = [];

        if (res?.mode === PoChangeModeEnum.removePo) {
          const result = res?.result;
          // 当前删除的po 中的order id
          result?.forEach((po: Po) => {
            removedPoIds.push(po.po_basic.id);
            po.po_lines.forEach((line: Line) => {
              if (factory.factory_id && factory.factory_id === line.factory_id && line.order_id) {
                delete_distributions.push(line.order_id);
              }
            });
          });
          this.factoryItem.get(res.factoryIndex)?.setPos(res.showResult);
        }
        if (
          res?.mode === PoChangeModeEnum.addSurplus ||
          res?.mode === PoChangeModeEnum.colorSizeChange ||
          res?.mode === PoChangeModeEnum.addPo
        ) {
          this.setDifSecDifFactoryButSamePoData(factory);
        }

        Object.keys(this.allFactoryInfo).forEach((key) => {
          this.allFactoryInfo[key]?.forEach((item: any) => {
            if (factory?.factory_short_name === item?.factory_short_name) {
              if (this.id !== EditModeEnum.add) {
                if (res?.mode === PoChangeModeEnum.colorSizeChange) {
                  // 编辑操作单元格时更新总数据中相同外发厂数据，并记录删除的颜色尺码
                  factory.pos.forEach((fpo: any) => {
                    fpo.po_lines.forEach((f_poline: any) => {
                      item.pos.forEach((po: any) => {
                        po.po_lines.forEach((po_line: Line) => {
                          if (
                            f_poline.po_id === po_line.po_id &&
                            f_poline.id === po_line.id &&
                            !this.undeletableLineIds[key]?.includes(po_line.id)
                          ) {
                            po_line.qty = f_poline.qty;
                            if (item.factory_id && item.factory_id === po_line.factory_id && po_line.qty === null && po_line.order_id) {
                              delete_distributions.push(po_line.order_id);
                            }
                          }
                        });
                      });
                    });
                  });
                  item.pos = this.outsourcingService.serialNumberLines(this.outsourcingService.getPoAllLine(item.pos), false);
                  item.lines = this.outsourcingService.getLines(item.pos);
                  this.updateDeleteDistributions(item);
                } else if (res?.mode === PoChangeModeEnum.removePo) {
                  // 其他二次工艺下相同外发厂的相同交付单颜色尺码id
                  item.pos.forEach((opo: Po) => {
                    if (removedPoIds.includes(opo.po_basic.id)) {
                      opo.po_lines.forEach((line: Line) => {
                        if (item.factory_id && item.factory_id === line.factory_id && line.order_id && line.deletable) {
                          delete_distributions.push(line.order_id);
                        }
                      });
                    }
                  });
                  const { pos, lines } = this.setAllSameFactoryData(factory.pos, item.pos, key);
                  item.pos = pos;
                  item.lines = lines;
                } else if (res?.mode === PoChangeModeEnum.addPo) {
                  const { pos, lines } = this.setAllSameFactoryData(factory.pos, item.pos, key);
                  item.pos = pos;
                  item.lines = lines;
                  this.updateDeleteDistributions(item);
                } else if (res?.mode === PoChangeModeEnum.addSurplus) {
                  const { pos, lines } = this.setAllSameFactoryData(factory.pos, item.pos, key);
                  item.pos = pos;
                  item.lines = lines;
                  this.updateDeleteDistributions(item);
                }
              } else {
                const pos = this.outsourcingService.deepclone(factory.pos);
                item.pos = this.outsourcingService.getPoAllLine(pos);
                item.lines = this.outsourcingService.getLines(item.pos);
              }
            }
          });
          // 更新每个二次工艺下的未分配数据
          this.filterSurplusPos(this.allFactoryInfo[key], key);

          //获取每个二次工艺的所有外发工厂，循环更新外发厂表格cellEditable状态
          this.refreshCellEditable(this.allFactoryInfo[key], this.allSurplusPos[key][0], key, false);
        });
        this.delete_distributions = Array.from(new Set([...this.delete_distributions, ...delete_distributions]));
      }
      // 切换二次工艺
      if (res?.onchange === 'secProcessChange') {
        this.factorys.clear();
        if (extra_process_info) {
          const setSurplusInfo = () => {
            this.surplusPos = this.outsourcingService.serialPos(
              this.outsourcingService.serialNumberLines(this.allSurplusPos[extra_process_info?.extra_process_id][0] || [])
            );
            this.surplusLines = this.outsourcingService.serialLines(this.allSurplusPos[extra_process_info?.extra_process_id][1] || []);
            this.outsourcingService._surplusPos = this.surplusPos;
            this.outsourcingService._surplusLines = this.surplusLines;
            this.cd.detectChanges();
          };
          if (this.allFactoryInfo[extra_process_info.extra_process_id]) {
            this.allFactoryInfo[extra_process_info.extra_process_id]?.forEach((item: any) => {
              this.factorys.push(
                this.fb.group({
                  deletable: [item?.deletable],
                  factory_id: [item?.factory_id],
                  factory_short_name: [item?.factory_short_name],
                  factory_code: [item?.factory_code],
                  factory_name: [item?.factory_name],
                  pos: [item?.pos],
                  lines: [item?.lines],
                  extra_process_name: [extra_process_info?.extra_process_name],
                  extra_process_id: [extra_process_info?.extra_process_id],
                  is_open: item?.is_open,
                })
              );
            });
            setTimeout(() => {
              this.factorys.value?.forEach((item: any, index: any) => {
                this.factoryItem.get(index)?.setPos(item.pos);
                this.factoryItem.get(index)?.setPoLines(this.outsourcingService.serialLines(this.outsourcingService.getLines(item.pos)));
                this.factoryItem.get(index)?.setSurplusPos(this.outsourcingService.surplusPos);
              });
            });
            setSurplusInfo();
          } else {
            this.factorys.push(
              this.fb.group({
                deletable: [true],
                factory_id: [null],
                factory_short_name: [null],
                factory_code: [null],
                factory_name: [null],
                pos: [[]],
                lines: [[]],
                extra_process_name: [extra_process_info?.extra_process_name],
                extra_process_id: [extra_process_info?.extra_process_id],
                is_open: true,
              })
            );
            setSurplusInfo();
          }
        }
      }
    });
  }

  /**
   * 更新cellEditable数据，表格会根据cellEditable判断格子是否为灰色并且不可修改
   * 没有可分配数据且当前颜色尺码为空或者已接单时，禁止修改，cellEditable为false
   * 并同步更新当前展示的外发厂po数据
   */
  refreshCellEditable(factory: any, surplusPos: any, key: any, isInit: boolean) {
    const surplusMap = new Map();
    // 记录当前工艺下未分配颜色尺码id
    surplusPos?.forEach((po: Po) => {
      po.po_lines?.forEach((line) => {
        if (line.qty) {
          surplusMap.set(line.id, line.id);
        }
      });
    });
    // 和surplusMap对比，不在未分配中的且当前qty为null时需将cellEditable设为false
    factory.forEach((item: any, index: number) => {
      const factoryPosMap = new Map();
      item.pos.forEach((po: Po) => {
        po.po_lines.forEach((line) => {
          const po_line = surplusMap.get(line.id);
          const extraParams = {
            cellEditable: line.deletable,
          };
          if (po_line === undefined && line.qty === null) {
            extraParams.cellEditable = false;
          } else if ((!isInit && line.deletable) || (po_line && !line.deletable)) {
            extraParams.cellEditable = true;
          }
          // 更新外发厂中line extraParams对象
          line.extraParams = extraParams;
          factoryPosMap.set(line.id, line);
        });
      });

      if (!isInit && this.outsourcingService.nowSelect_extra_process_info.extra_process_id === Number(key)) {
        this.factoryItem.get(index)?._pos.forEach((po: Po, poIndex: number) => {
          let haveChange = false;
          po.po_lines.forEach((line) => {
            const fpo: Line = factoryPosMap.get(line.id);
            if (fpo) {
              haveChange = true;
              line.extraParams = fpo.extraParams;
            }
          });
          if (haveChange) {
            this.outsourcingService.changePoLineEmitter.emit({ isPoChange: true, factoryIndex: index, poIndex, poLines: [...po.po_lines] });
          }
        });
        setTimeout(() => {
          const lines = this.outsourcingService.getLines(item.pos);
          this.factorys.controls[index]?.get('pos')?.setValue(this.outsourcingService.deepclone(item.pos));
          this.factorys.controls[index]?.get('lines')?.setValue(lines);
          this.allFactoryInfo[key] = factory;
        });
      }
    });
  }
  /**
   * 更新所有二次工艺外发厂数据：
   * 添加剩余未分配时，会同步更新其他二次工艺下相同外发厂数据，在更新相同外发厂数据时，需要更新不同外发厂相同po单的数据，
   * 和更新的相同外发厂数据比较，如果相同的颜色尺码已经分配到相同外发厂下了，那么需要将不同外发厂相同po单下的相同颜色尺码置为null
   * 如果被修改的颜色尺码为已接单状态，deletable为false，则不修改值
   */
  setDifSecDifFactoryButSamePoData(nowFactory: any) {
    const haveSameFactorySec: any = [];
    const delete_distributions: Array<number> = [];
    // 记录有相同外发厂的二次工艺
    for (const key of Object.keys(this.allFactoryInfo)) {
      for (const all_factory of this.allFactoryInfo[key]) {
        if (nowFactory.factory_short_name === all_factory.factory_short_name) {
          haveSameFactorySec.push(key);
          break;
        }
      }
    }
    // 更新有相同外发厂的二次工艺的其他外发厂相同po的数据
    haveSameFactorySec.forEach((key: any) => {
      this.allFactoryInfo[key]?.forEach((all_factory: any) => {
        if (nowFactory.factory_short_name !== all_factory.factory_short_name) {
          const nowFactoryLinesMap = new Map();
          nowFactory.pos?.forEach((nowFactoryPo: Po) => {
            nowFactoryLinesMap.set(nowFactoryPo.po_basic.id, nowFactoryPo.po_lines);
          });
          all_factory.pos?.forEach((all_factory_po: Po) => {
            const nowFactoryLines: Array<Line> = nowFactoryLinesMap.get(all_factory_po.po_basic.id);
            if (nowFactoryLines) {
              const nowFactoryLineMap = new Map();
              nowFactoryLines.forEach((line) => {
                nowFactoryLineMap.set(line.id, line);
              });
              all_factory_po.po_lines?.forEach((line: Line) => {
                const nowFactoryLine: Line = nowFactoryLineMap.get(line.id);
                if (nowFactoryLine && nowFactoryLine.qty !== null && line.deletable) {
                  line.qty = null;
                  if (line.order_id) {
                    delete_distributions.push(line.order_id);
                  }
                }
              });
            }
          });
          all_factory.lines = this.outsourcingService.getLines(all_factory.pos);
        }
      });
    });
    this.delete_distributions = Array.from(new Set([...this.delete_distributions, ...delete_distributions]));
  }
  /* 如果是详情编辑时，删除一个已有的po然后又将这个po加回来了，应该将相同外发厂的之前需要删除的order id去掉
   */
  updateDeleteDistributions(item: any) {
    item.pos.forEach((opo: Po) => {
      opo.po_lines.forEach((line: Line) => {
        const deleteIndex = this.delete_distributions.findIndex((val: any) => {
          return item.factory_id === line.factory_id && line.order_id === val;
        });
        if (deleteIndex !== -1) {
          this.delete_distributions.splice(deleteIndex, 1);
        }
      });
    });
  }
  /**
   * 详情编辑时，添加剩余未分配/添加交付单，相同外发厂的同一个po下的同一个颜色尺码只修改qty
   * 如果老数据中和新数据没有相同的po，则新数据的直接将这个po放到后面
   * 删除po时如果po deletable为false，则当前Po不删除
   */
  setAllSameFactoryData(nowFactoryPos: Pos, oldFactoryPos: Pos, extraProcessId: any) {
    let pos: Pos = [];
    let lines: Array<Line> = [];
    const samePoIds: Array<number> = [];
    // 相同po的，将老数据除qty之外复制给新数据，这里面包括order_id
    const _oldFactoryPos = this.outsourcingService.deepclone(oldFactoryPos);
    nowFactoryPos = this.outsourcingService.deepclone(nowFactoryPos);
    const nowFactoryMap = new Map();
    nowFactoryPos.forEach((npo: Po) => {
      nowFactoryMap.set(npo.po_basic.id, npo);
    });
    _oldFactoryPos.forEach((opo: Po) => {
      const npo: Po = nowFactoryMap.get(opo.po_basic.id);
      if (npo) {
        // 记录老数据和新数据相同的poid
        samePoIds.push(npo.po_basic.id);
        npo.po_lines.forEach((npo_line: Line) => {
          opo.po_lines.forEach((opo_line: Line) => {
            if (npo_line.id === opo_line.id && !this.undeletableLineIds[extraProcessId]?.includes(opo_line.id)) {
              opo_line.qty = npo_line.qty;
            }
          });
        });
        pos.push(opo);
      } else if (!opo.po_basic.deletable) {
        samePoIds.push(opo.po_basic.id);
        pos.push(opo);
      }
    });
    // 老数据中没有的po，直接整个放进去，放进去之前重置所有deletable
    nowFactoryPos.forEach((npo: Po) => {
      if (!samePoIds.includes(npo.po_basic.id)) {
        npo.po_basic.deletable = true;
        npo.po_lines?.forEach((line) => {
          line.deletable = true;
        });
        pos.push(npo);
      }
    });
    // 过滤已分配数据
    const newPos: Array<Po> = [];

    pos.forEach((po: Po) => {
      const obj: any = {
        po_basic: po.po_basic,
        po_lines: [],
      };
      po.po_lines.forEach((line) => {
        if (Number(extraProcessId) === this.outsourcingService.nowSelect_extra_process_info.extra_process_id) {
          obj.po_lines.push(line);
          return;
        }
        if (!this.undeletableLineIds[extraProcessId]?.includes(line.id)) {
          obj.po_lines.push(line);
        } else {
          if (line.deletable) {
            line.qty = null;
          }
          obj.po_lines.push(line);
        }
      });
      newPos.push(obj);
    });

    pos = newPos.filter((po) => po.po_lines.some((line) => line.qty !== null));
    pos = this.outsourcingService.serialNumberLines(this.outsourcingService.getPoAllLine(pos), false);
    lines = this.outsourcingService.getLines(pos);
    return { pos, lines };
  }
  /**
   * 每次合并同一个外发厂数据时，重新计算当前二次工艺下未分配数据
   * @param { factorys } 每个二次工艺下的所有外发厂数据
   * @param { key } 对应的二次工艺Id
   */
  filterSurplusPos(factorys = [], key: any) {
    const factoryPos: Pos = []; // 当前二次工艺下所有已分配po数据
    let poIds: Array<number> = [];
    factorys.forEach((factory: any) => {
      const ids = factory.pos?.map((po: any) => po?.po_basic?.id);
      poIds = [...poIds, ...ids];
    });
    // 多个外发厂中可能有重复的交付单Id需要去重
    poIds = Array.from(new Set(poIds));
    poIds.forEach((id: any) => {
      const obj: { po_lines: Array<any>; po_basic: any } = {
        po_basic: {},
        po_lines: [],
      };
      factorys.forEach((factory: any) => {
        factory.pos?.forEach((item: any) => {
          if (id === item?.po_basic?.id) {
            if (Object.keys(obj.po_basic).length === 0) {
              obj.po_basic = this.outsourcingService.deepclone(item?.po_basic);
              factoryPos.push(obj);
            }
            obj.po_lines = [...obj.po_lines, ...this.outsourcingService.deepclone(item?.po_lines)];
          }
        });
      });
      // 交付单line去重，保留qty不为null的那个
      const newLinesMap = new Map();
      obj.po_lines.forEach((line) => {
        const lineValue = newLinesMap.get(line.id);
        if (lineValue) {
          if (lineValue.qty === null && (line.qty !== null || line.qty !== 0)) {
            newLinesMap.set(line.id, line);
          }
        } else {
          newLinesMap.set(line.id, line);
        }
      });
      obj.po_lines = Array.from(newLinesMap.values());
    });

    // 过滤未分配数据
    let surplusPos: Pos = [];
    const fpoLines = new Map();
    factoryPos.forEach((fpo) => {
      fpoLines.set(fpo.po_basic.id, fpo.po_lines);
    });
    this.pos.forEach((po) => {
      const obj: Po = {
        po_basic: this.outsourcingService.deepclone(po.po_basic),
        po_lines: [],
      };
      if (fpoLines.get(po.po_basic?.id)) {
        obj.po_lines = this.filterSurplusLines(po.po_lines, fpoLines.get(po.po_basic?.id));
        if (obj.po_lines.length) {
          surplusPos.push(obj);
        }
      } else {
        obj.po_lines = this.outsourcingService.deepclone(po.po_lines);
        surplusPos.push(obj);
      }
    });
    surplusPos = this.outsourcingService.serialPos(this.outsourcingService.serialNumberLines(surplusPos));
    this.allSurplusPos[key] = [surplusPos, this.outsourcingService.serialLines(this.outsourcingService.getLines(surplusPos))];
  }
  /* 操作颜色尺码时根据所有的po数据和已分配的po数据，过滤未分配的数据 */
  filterSurplusLines(lines: Array<Line> = [], flines: Array<Line> = []) {
    const _lines = [];
    const _flines = new Map();
    for (const fline of flines) {
      _flines.set(fline.id, fline);
    }
    for (const line of lines) {
      const _fline: Line = _flines.get(line.id);
      if (_fline && _fline.qty === null && line.qty !== 0) {
        _fline.extraParams = { cellEditable: true };
        _fline.qty = line.qty;
        _lines.push(this.outsourcingService.deepclone(_fline));
      }
    }
    return _lines;
  }
  /**获取不同工艺下相同的外发厂的已分配po和合计数据
   * 返回值为pos、lines和haveSameFactoryPos
   * pos、lines是过滤了当前二次工艺下已接单的数据
   * haveSameFactoryPos表示是否有从其他二次工艺下获得过相同外发厂数据
   */
  getRepeatFactoryForDifProcess(factory: any) {
    const extraProcessKey = Object.keys(this.allFactoryInfo);
    let newFactory;
    let haveSameFactoryPos = false; //是否从相同外发厂中获取到了pos
    for (const key of extraProcessKey) {
      if (String(factory.extra_process_id) === key) {
        continue;
      }
      for (const _factory of this.allFactoryInfo[key]) {
        if (factory.factory_short_name === _factory.factory_short_name) {
          newFactory = this.outsourcingService.deepclone(_factory);
        }
      }
    }

    if (newFactory === undefined) {
      return { haveSameFactoryPos };
    } else {
      haveSameFactoryPos = true;
    }
    const { pos } = newFactory;
    // 初始化所有deletable
    pos.forEach((po: Po) => {
      po.po_basic.deletable = true;
      po.po_lines.forEach((line) => {
        line.deletable = true;
      });
    });

    if (this.undeletableLineIds[factory.extra_process_id] === undefined || this.undeletableLineIds[factory.extra_process_id].length === 0) {
      return { pos, lines: this.outsourcingService.getLines(pos), haveSameFactoryPos };
    }
    // 过滤当前二次工艺下已接单的数据
    let newPos: Array<Po> = [];
    pos.forEach((po: Po) => {
      const obj: any = {
        po_basic: po.po_basic,
        po_lines: [],
      };
      po.po_lines.forEach((line) => {
        if (!this.undeletableLineIds[factory.extra_process_id]?.includes(line.id)) {
          obj.po_lines.push(line);
        } else {
          line.qty = null;
          obj.po_lines.push(line);
        }
      });
      newPos.push(obj);
    });

    newPos = newPos.filter((po) => po.po_lines.some((line) => line.qty !== null));

    if (newPos.length) {
      const newLines = this.outsourcingService.getLines(newPos);
      return { pos: newPos, lines: newLines, haveSameFactoryPos };
    }
    return { haveSameFactoryPos };
  }
  // 新增一个 加工厂，将剩余未分配数据填充进去
  addFactoryItem() {
    const extra_process_info: ExtraProcessAssignInfoInterface = this.outsourcingService.nowSelect_extra_process_info;
    this.factorys.push(
      this.fb.group({
        deletable: [true],
        factory_id: [null],
        factory_short_name: [null],
        factory_code: [null],
        factory_name: [null],
        pos: [[]],
        lines: [[]],
        extra_process_name: [extra_process_info?.extra_process_name],
        extra_process_id: [extra_process_info?.extra_process_id],
        is_open: true,
      })
    );
    this.factorys.value.forEach((item: any, index: any, arr: any) => {
      if (index !== arr.length - 1) {
        item.is_open = false;
        this.factoryItem.get(index)?.setToggle(false);
        this.factorys.controls[index]?.get('is_open')?.setValue(false);
      }
    });
    this.allFactoryInfo[extra_process_info?.extra_process_id] = this.outsourcingService.deepclone(this.factorys.value);
  }
  // 获取详情回显数据
  async getDetail() {
    forkJoin([
      this._service.ioDetail({ id: Number(this.io_id), cache: false, production_type: 2 }),
      this._service.getDetailInfo({ id: Number(this.id), cache: true, production_type: 2 }),
      this._service.undistribution({ id: Number(this.id), cache: true, production_type: 2 }),
    ]).subscribe(async (res) => {
      if (res[0].code === 200 && res[1].code === 200 && res[2].code === 200) {
        // 给po_lines设置po_id
        const ioDetail = res[0]?.data;
        this.getOutData(ioDetail.io_basic.io_uuid);
        res[0]?.data?.pos.forEach((po: Po) => {
          po.po_basic.deletable = true;
          po.po_lines.forEach((line: Line) => {
            line.deletable = true;
            line['po_id'] = po?.po_basic?.id;
            if (line.qty === 0) {
              line.qty = null;
            }
          });
        });

        this.orderDetail = res[1]?.data;
        this.io_basic = ioDetail?.io_basic;
        this.io_lines = ioDetail?.io_lines;
        this.outsourcingService.io_lines = this.outsourcingService.deepclone(this.io_lines);
        this.pos = this.outsourcingService.deepclone(ioDetail?.pos);
        this.outsourcingService.pos = this.outsourcingService.deepclone(ioDetail?.pos);
        this.orderStatus = this.orderDetail?.status;
        this.detailForm.get('id')?.setValue(this.id);
        this.detailForm.get('io_id').setValue(`${this.io_basic?.id}`); // 回显用
        this.detailForm.get('io_id_local')?.setValue(`${this.io_basic?.id}`);
        this.detailForm.get('io_code')?.setValue(this.io_basic.io_code);
        this.detailForm.get('task_type')?.setValue(this.orderDetail.task_type);
        this.ioCodeDefaultValue = { label: this.io_basic.io_code, value: `${this.io_basic?.id}` };
        this.extraOptions = [
          { label: this.detailForm.get('io_code')?.value, value: this.detailForm.get('io_id_local')?.value, hide: false },
        ];

        this.io_basic?.extra_process_info.forEach((item: any) => {
          this.allFactoryInfo[item.extra_process_id] = [];
        });
        this.orderDetail?.extra_process_info.forEach((item: any) => {
          res[2].data.info.forEach((_item: any) => {
            if (_item.extra_process_name === item.extra_process_name) {
              _item.extra_process_id = item.extra_process_id;
            }
          });
        });
        res[2].data.info.forEach((item: any) => {
          const surplusPos: any = [];
          const surplusLines: any = [];
          item.po_info.forEach((po: any) => {
            po.po_line_ids.forEach((line: Line) => {
              line.deletable = true;
              line['po_line_id'] = line.id;
              line['po_id'] = po.po_id;
            });
            const result = this.outsourcingService.getEnabledLines(po.po_line_ids, this.pos);
            surplusPos.push(...this.outsourcingService.serialNumberLines(result.pos));
            surplusLines.push(...result.po_lines);
          });
          this.allSurplusPos[item.extra_process_id] = [[...surplusPos], [...surplusLines]];
        });
        const processMap = new Map();
        const extra_process_info: any = [];
        for (const process of this.orderDetail?.extra_process_info ?? []) {
          processMap.set(process.extra_process_id, process);
        }
        for (const process of this.io_basic?.extra_process_info ?? []) {
          if (processMap.get(process.extra_process_id)) {
            extra_process_info.push(processMap.get(process.extra_process_id));
          } else {
            this.allSurplusPos[process.extra_process_id] = [
              this.outsourcingService.deepclone(this.pos),
              this.outsourcingService.getSurplusLines(this.pos),
            ];
            extra_process_info.push(process);
          }
        }
        this.setAllFactoryInfo(this.orderDetail?.info);
        this.collectDeleteDistributions(this.orderDetail?.info);
        if (this.orderStatus === OrderStatus.toModifyAudit) {
          await this.getFactoryChangedPo();
        }
        this.extra_process_info = this.outsourcingService.deepclone(extra_process_info);
      }
    });
  }
  /**
   * 获取审核通过后有修改的po,根据po id标识have_new_line属性
   */
  getFactoryChangedPo() {
    return new Promise((resolve) => {
      this._service.getFactoryChangedPo({ id: Number(this.id) }).subscribe((res) => {
        if (res?.code === 200) {
          const extraProcessMap = new Map();
          res.data?.data?.forEach((item: any) => {
            extraProcessMap.set(item.extra_process_id, new Map());
          });
          res.data?.data?.forEach((item: any) => {
            const factoryMap = extraProcessMap.get(item.extra_process_id);
            if (factoryMap?.get(item.factory_code)) {
              factoryMap.set(item.factory_code, [...factoryMap.get(item.factory_code), ...item.changed_pos]);
            } else {
              factoryMap.set(item.factory_code, [...item.changed_pos]);
            }
          });

          Object.keys(this.allFactoryInfo).forEach((key) => {
            const extraProcess = extraProcessMap.get(Number(key));
            if (extraProcess) {
              this.allFactoryInfo[key].forEach((factory: any) => {
                const _factory = extraProcess.get(factory.factory_code);
                if (_factory) {
                  factory.pos.forEach((po: Po) => {
                    if (_factory.includes(po.po_basic.id)) {
                      po.po_basic.have_changed_line = true;
                    }
                  });
                }
              });
            }
          });
          resolve(true);
        }
      });
    });
  }
  // 选择了IO 获取大货单详情
  getIoDetail(id: string) {
    return new Promise((reslove, rejects) => {
      const payload = { id: Number(id), cache: false, production_type: 2 };
      this._service.ioDetail(payload).subscribe((res) => {
        if (res.code === 200) {
          this.resetSecProcessFactoryInfo();
          this.io_basic = res?.data?.io_basic;
          this.io_lines = res?.data?.io_lines;
          this.outsourcingService.io_lines = this.outsourcingService.deepclone(this.io_lines);
          res?.data?.pos.forEach((po: Po) => {
            po.po_basic.deletable = true;
            po.po_lines.forEach((line: Line) => {
              line.deletable = true;
              line['po_id'] = po?.po_basic?.id;
              if (line.qty === 0) {
                line.qty = null;
              }
            });
          });
          this.pos = this.outsourcingService.deepclone(res?.data?.pos);
          this.outsourcingService.pos = this.outsourcingService.deepclone(res?.data?.pos);
          this.detailForm.get('io_id').setValue(`${this.io_basic?.id}`);
          this.detailForm.get('io_code').setValue(this.io_basic?.io_code);
          this.detailForm.get('io_id_local')?.setValue(`${this.io_basic?.id}`);
          this.io_basic?.extra_process_info.forEach((item: any) => {
            const pos = this.outsourcingService.deepclone(res?.data?.pos);
            const poLines = this.outsourcingService.serialLines(this.outsourcingService.getLines(res?.data?.pos));
            this.allSurplusPos[item.extra_process_id] = [[...pos], [...poLines]];
          });
          this.showMainPanel = true;
          this.extra_process_info = this.io_basic?.extra_process_info;
          reslove(res?.data);
        } else {
          rejects(false);
        }
      });
    });
  }
  /* 重置外发厂数据 */
  resetSecProcessFactoryInfo() {
    this.allFactoryInfo = {};
    this.allSurplusPos = [];
  }
  /* 审核通过之后再次提交，这时如果大货订单里面将某个颜色尺码删掉了，
    那么进来的时候需要对比一下当前所有审核通过的数据和大货订单的数据，
    如果当前的数据里面的po_line_id和在大货订单的po_lines中没有，则将没有
    匹配到的这条数据删除
   */
  collectDeleteDistributions(info: Array<ExtraProcessAssignInfoInterface>) {
    const po_lines: Array<Line> = [];
    const info_lines: Array<{ id: number; po_id: number; po_line_id: number }> = [];
    this.pos.forEach((po: Po) => {
      po_lines.push(...po.po_lines);
    });
    info.forEach((info: ExtraProcessAssignInfoInterface) => {
      info_lines.push(...info.lines);
    });
    const poLineIds = po_lines.map((line) => {
      return line.id;
    });
    const delete_distributions: Array<number> = [];
    info_lines.forEach((item) => {
      if (!poLineIds.includes(item.po_line_id) && item.id !== 0) {
        delete_distributions.push(item.id);
      }
    });
    this.delete_distributions = delete_distributions;
  }

  setAllFactoryInfo(info: Array<ExtraProcessAssignInfoInterface>) {
    info.forEach((item: any) => {
      // 记录当前的订单是否可以取消
      if (item.deletable === false) {
        this.isCancelOrder = false;
      }
      const obj: any = {
        deletable: item?.deletable,
        factory_id: item?.id,
        factory_short_name: item?.factory_short_name,
        factory_code: item?.factory_code,
        factory_name: item?.factory_name,
        pos: [],
        lines: [],
        extra_process_name: item?.extra_process_name,
        extra_process_id: item?.extra_process_id,
        is_open: true,
      };
      item?.lines.forEach((line: Line) => {
        line.factory_id = item?.id;
      });
      const { pos, po_lines } = this.outsourcingService.getEnabledLines(item?.lines, this.pos);
      pos?.forEach((po: Po) => {
        po.po_lines.forEach((line) => {
          line.extraParams = { cellEditable: line.deletable };
        });
      });
      obj.pos = this.outsourcingService.serialPos(this.outsourcingService.serialNumberLines(pos, false));
      obj.lines = po_lines;
      // 保存后大货单可能会将这个二次工艺删掉，所以需要判断下这个二次工艺是否存在
      if (this.allFactoryInfo[item.extra_process_id]) {
        this.allFactoryInfo[item.extra_process_id].push(obj);
      }
    });
    Object.keys(this.allFactoryInfo).forEach((key) => {
      if (this.allFactoryInfo[key].length === 0) {
        this.allFactoryInfo[key].push({
          deletable: true,
          factory_id: null,
          factory_short_name: null,
          factory_code: null,
          factory_name: null,
          pos: [],
          lines: [],
          extra_process_name: null,
          extra_process_id: null,
          is_open: true,
        });
      } else {
        this.undeletableLineIds[key] = [];
        this.allFactoryInfo[key].forEach((factory: any) => {
          factory.pos.forEach((po: Po) => {
            po.po_lines.forEach((line) => {
              if (!line.deletable) {
                this.undeletableLineIds[key].push(line.id);
              }
            });
          });
        });
        this.refreshCellEditable(this.allFactoryInfo[key], this.allSurplusPos[key][0], key, true);
      }
    });
  }
  // 取消
  cancel() {
    if (this.editMode !== EditModeEnum.add && (this.detailForm.dirty || this.factoryDataChange)) {
      this.outsourcingService
        .confirmDialog(this.translateKey('outsourcingMessage.确定取消当前操作？'), ' ', false, true)
        .afterClose.subscribe((confirm) => {
          if (confirm) {
            this.factoryDataChange = false;
            if (this.editMode === EditModeEnum.add) {
              this.back();
            } else {
              this.editMode = EditModeEnum.read;
              this.getDetail();
            }
          }
        });
    } else {
      if (this.editMode === EditModeEnum.add) {
        this.back();
      } else {
        this.editMode = EditModeEnum.read;
        this.getDetail();
      }
    }
  }
  // 编辑
  modify() {
    this.outsourcingService.confirmDialogWithReason().afterClose.subscribe((res) => {
      if (res?.success) {
        this._service.modify({ id: Number(this.id), reason: res?.reason }).subscribe((res) => {
          if (res.code === 200) {
            this._message.success(this.translateKey('outsourcingMessage.退回修改成功'));
            this.getDetail();
            this._service.eventEmitter.emit('refresh');
          }
        });
      }
    });
  }
  // 审核通过
  pass() {
    if (!this.isValid()) return;
    this.outsourcingService
      .confirmDialog(
        this.translateKey('outsourcingMessage.确定审核通过？'),
        this.translateKey('outsourcingMessage.审核通过后订单状态变为待外发厂接单'),
        true,
        false
      )
      .afterClose.subscribe((res) => {
        if (res) {
          this._service.pass(Number(this.id)).subscribe((res) => {
            if (res.code === 200) {
              this._message.success(this.translateKey('success.pass'));
              this.getDetail();
              this._service.eventEmitter.emit('refresh');
            }
          });
        }
      });
  }
  outData: any;
  /**
   * 二次工艺数据
   */
  getOutData(io_uuid: string) {
    this._service.getOutData(io_uuid).subscribe((res: any) => {
      if (res?.code === 200) {
        if (res.data.list.length) {
          this.outData = res.data.list;
        } else {
          this.outData = defaultProgress;
        }
        this.outData.forEach((item: any) => {
          if (item.cut_count > 0) {
            this.outData['cutCount'] = true;
          }
          if (item.outsource_qty > 0) {
            this.outData['outsourceQty'] = true;
          }
          if (item.received_qty > 0) {
            this.outData['receivedQty'] = true;
          }
          if (item.finished_qty > 0) {
            this.outData['finishedQty'] = true;
          }
          if (item.qualified_qty > 0) {
            this.outData['qualifiedQty'] = true;
          }
          if (item.sent_qty > 0) {
            this.outData['sendQty'] = true;
          }
        });
      }
    });
  }
  // 保存
  save() {
    this.commit(false);
  }
  // 返回
  back() {
    this._router.navigate(['/outsourcing-manage/sec-process-outsourcing/list']);
  }
  // 取消订单
  cancelOrder() {
    if (!this.isCancelOrder) {
      this._message.error(this.translateKey('outsourcingMessage.工厂已接单，无法取消订单'));
      return;
    }
    this.outsourcingService
      .confirmDialog(
        `${this.translateKey('outsourcingMessage.确定')}<span class="red-mark">${this.translateKey(
          'outsourcingMessage.取消'
        )}</span>${this.translateKey('outsourcingMessage.订单？')}`,
        ' ',
        false,
        true
      )
      .afterClose.subscribe((confirm) => {
        if (confirm) {
          this._service.cancel({ id: Number(this.id), cache: false, production_type: 1 }).subscribe((res) => {
            if (res.code === 200) {
              this._message.success(this.translateKey('outsourcingMessage.取消成功'));
              this.getDetail();
              this._service.eventEmitter.emit('refresh');
            }
          });
        }
      });
  }
  // 编辑详情
  edit() {
    this.editMode = EditModeEnum.edit;
  }
  isValid() {
    let isValid = true;
    let isfactoryValid = false; // 外发厂是否为空
    const extra_process_info = this.detailForm.get('extra_process_info').value;
    const extraProcessInfoIds = extra_process_info.map((item: any) => {
      return item.extra_process_id;
    });
    // 设置收起展开
    const setToggleData = (factorys: any, index: number) => {
      factorys?.forEach((item: any, _index: number) => {
        if (index === _index) {
          item.is_open = true;
        } else {
          item.is_open = false;
        }
      });
    };
    // 验证是否有没有选择部位
    for (const [index, extra_process] of extra_process_info.entries()) {
      if (extra_process.position_list.length === 0) {
        if (extra_process.extra_process_id !== this.outsourcingService.nowSelect_extra_process_info.extra_process_id) {
          this.secProcessRef.handleBtn(index);
        }
        isValid = false;
        this._notification.error('', '请检查必填项');
        this._validator.markFormDirty(this.detailForm);
        return isValid;
      }
    }
    // 验证是否没有分配一个外发厂
    const factoryKeys = Object.keys(this.allFactoryInfo);
    for (const key of factoryKeys) {
      if (isfactoryValid) {
        break;
      }
      for (const item of this.allFactoryInfo[key]) {
        // 只要有一个不为空则为true
        if (!this.flcUtilService.isNilOrEmptyStr(item?.factory_short_name)) {
          isfactoryValid = true;
          break;
        }
      }
    }
    if (!isfactoryValid) {
      this._message.error(this.translateKey('outsourcingMessage.至少为一个二次工艺分配一个外发厂'));
      return isfactoryValid;
    }
    // 验证交付单是否为空或交付单表格是否为空
    for (const key of factoryKeys) {
      for (const [f_index, item] of this.allFactoryInfo[key].entries()) {
        // 如果某个加工厂下没有交付单，提示错误
        if (!this.flcUtilService.isNilOrEmptyStr(item.factory_short_name) && item.pos?.length === 0) {
          setToggleData(this.allFactoryInfo[key], f_index);
          this.secProcessRef.handleBtn(extraProcessInfoIds.indexOf(Number(key)));
          this._message.error(
            `【${item.extra_process_name}】${this.translateKey('outsourcingMessage.下外发厂')}【${
              item?.factory_short_name
            }】${this.translateKey('outsourcingMessage.交付单不能为空')}${
              [OrderStatus.toModifyAudit, OrderStatus.toAudit].includes(this.orderStatus)
                ? this.translateKey('outsourcingMessage.请退回修改')
                : ''
            }`
          );
          isValid = false;
          return isValid;
        } else {
          for (const [index, po] of item.pos.entries()) {
            const lines = po.po_lines?.filter((val: Line) => val.qty !== null);
            // 某个交付单下颜色尺码为空，提示错误
            if (lines.length === 0) {
              this._message.error(
                `【${item.extra_process_name}】${this.translateKey('outsourcingMessage.下外发厂')}【${
                  item?.factory_short_name
                }】${this.translateKey('outsourcingMessage.交付单')}【${po.po_basic.po_code}】${this.translateKey(
                  'outsourcingMessage.颜色/尺码不能为空'
                )}${
                  [OrderStatus.toModifyAudit, OrderStatus.toAudit].includes(this.orderStatus)
                    ? this.translateKey('outsourcingMessage.请退回修改')
                    : ''
                }`
              );
              setToggleData(this.allFactoryInfo[key], f_index);
              this.secProcessRef.handleBtn(extraProcessInfoIds.indexOf(Number(key)));
              setTimeout(() => {
                this.factoryItem.get(f_index)!.tabContainer.selectedIndex = index;
              });
              isValid = false;
              return isValid;
            }
          }
        }
      }
    }

    // 验证所有相同外发厂数据是否完全相同，不相同情况只有在同步已接单数据时会出现
    // 收集所有二次工艺下相同的外发厂，以外发厂的factory_short_name为key，value为一个数组，里面存的是二次工艺信息和字符串化的line的信息
    const allFactoryStrMap = new Map();
    for (const key of factoryKeys) {
      for (const [f_index, factory] of this.allFactoryInfo[key].entries()) {
        if (allFactoryStrMap.get(factory.factory_short_name) === undefined) {
          allFactoryStrMap.set(factory.factory_short_name, []);
        }
        // 收集每个颜色尺码的id和qty，之后会stringfy整个newPos，根据这个value去对比数据是否相同，所以需保证数据的顺序
        const newPos: any = [];
        factory.pos?.forEach((po: Po) => {
          po.po_lines.forEach((line) => {
            const obj = {
              id: line.id,
              qty: line.qty,
            };
            newPos.push(obj);
          });
        });
        allFactoryStrMap.get(factory.factory_short_name).push({
          extra_process_id: factory.extra_process_id,
          extra_process_name: factory.extra_process_name,
          value: JSON.stringify(newPos),
          k_index: extraProcessInfoIds.indexOf(Number(key)),
          f_index,
        });
      }
    }

    let extraProcessIndex; // 二次工艺下标
    let factoryIndex; // 外发厂下标
    let extraProcessId; // 二次工艺id
    for (const [key, factorys] of allFactoryStrMap.entries()) {
      if (!isValid) {
        break;
      }
      const extra_process_names = new Set();
      for (let index = 0; index < factorys.length; index++) {
        let _index = index + 1;
        while (_index < factorys.length) {
          if (factorys[index].value !== factorys[_index].value) {
            if (extraProcessIndex === undefined) {
              extraProcessIndex = factorys[_index].k_index;
              factoryIndex = factorys[_index].f_index;
              extraProcessId = factorys[_index].extra_process_id;
              extra_process_names.add(factorys[index].extra_process_name);
              extra_process_names.add(factorys[_index].extra_process_name);
            }
            // 收集所有相同的二次工艺名
            isValid = false;
          }
          _index++;
        }
      }

      // 如果有外发厂数据不一致，提示的同时定位到第一个二次工艺下的第一个外发厂，打开当前加工厂，关闭其他加工厂
      if (!isValid) {
        setToggleData(this.allFactoryInfo[extraProcessId], factoryIndex);
        this.secProcessRef.handleBtn(Number(extraProcessIndex));
        this._message.error(`二次工艺${Array.from(extra_process_names).join('、')}外发厂【${key}】颜色/尺码分配不一致`);
        return isValid;
      }
    }

    return isValid && isfactoryValid;
  }
  // 提交
  commit(isCommit = true) {
    if (!this.detailForm.get('io_id')?.valid) {
      this._notification.error('', this.translateKey('outsourcingMessage.请检查必填项'));
      this._validator.markFormDirty(this.detailForm);
      return;
    }
    if (isCommit) {
      if (!this.isValid()) {
        return;
      }
    }

    // 是否还有剩余未分配的颜色尺码，有且是提交 则二次弹框确认
    let haveSurplus = false;
    Object.keys(this.allSurplusPos).forEach((key) => {
      if (this.allSurplusPos[key][0] && this.allSurplusPos[key][0].length > 0) {
        haveSurplus = true;
      }
    });
    if (haveSurplus && isCommit) {
      this.outsourcingService
        .confirmDialog(this.translateKey('outsourcingMessage.有交付单未分配完毕，确定提交？'), ' ', false, true)
        .afterClose.subscribe((confirm) => {
          if (confirm) {
            this.confirmCommit(isCommit);
          }
        });
    } else {
      this.confirmCommit(isCommit);
    }
  }
  confirmCommit(isCommit = true) {
    const info: any = [];
    Object.keys(this.allFactoryInfo).forEach((key) => {
      this.allFactoryInfo[key].forEach((item: any) => {
        if (item.factory_short_name) {
          info.push({
            id: item?.factory_id || 0, // 分配单id，
            factory_code: item.factory_code, // 分配的工厂code
            factory_name: item.factory_name, // 分配的工厂名成
            factory_short_name: item.factory_short_name, // 分配工厂的简称，没有可不填
            lines: this.filterPayloadLines(item.lines, this.id === 'add', item?.factory_id || 0),
            deletable: item?.deletable, // 分配单是否可删除，
            extra_process_name: item.extra_process_name, // 二次工艺名称，成衣外发填写默认值即可
            extra_process_id: item.extra_process_id, // 二次工艺id，成衣外发填写默认值即可
          });
        }
      });
    });
    const payload = {
      commit: isCommit, // 是否提交
      production_type: 2, // 外发类型，1 成衣加工 2 二次工艺加工
      task_type: '',
      extra_process_info: this.detailForm.get('extra_process_info').value,
      info: info,
      io_id: Number(this.detailForm.get('io_id').value), // io id
      id: Number(this.id) || 0,
      status: 0, // 分配单状态
      status_value: '', // 分配单状态值
      reason: '', // 退回修改原因
    };
    if (this.id !== 'add') {
      const delete_info: any = {
        delete_factory_infos: this.delete_factory_infos, // 删除加工厂的id
        delete_distributions: this.delete_distributions, // 删除交付单 单个尺码的数据时
      };

      // 审核通过走edit编辑接口,其他状态更新走update接口
      const name = [OrderStatus.auditPass, OrderStatus.modifyAuditReturn, OrderStatus.toModifyAudit].includes(this.orderDetail.status)
        ? 'auditPassEdit'
        : 'update';
      this._service[name]({ distribution_order: payload, delete_info: delete_info }).subscribe((res) => {
        if (res.code === 200) {
          this.detailForm.reset();
          this.delete_factory_infos = [];
          this.delete_distributions = [];
          this.editMode = 'read';
          this.io_id = payload.io_id;
          this._router.navigate(['/outsourcing-manage/sec-process-outsourcing/list/', this.id], { queryParams: { io_id: payload.io_id } });
          this.getDetail();
          this._message.success(this.translateKey('success.update'));
          this._service.eventEmitter.emit('refresh');
        }
      });
    } else {
      // 创建数据
      this._service.createDistributionOrder(payload).subscribe((res) => {
        if (res?.code === 200) {
          this.id = res?.data.id;
          this.io_id = this.detailForm.get('io_id').value;
          this.getDetail();
          this.editMode = EditModeEnum.read;
          this._message.success(this.translateKey('success.create'));
          this._service.eventEmitter.emit('refresh');
          this._router.navigate(['/outsourcing-manage/sec-process-outsourcing/list/', this.id], {
            queryParams: { io_id: this.detailForm.get('io_id').value },
          });
        }
      });
    }
  }
  /* 获取已删除 */
  getDeleteLines(pos: Array<Po>, delete_distributions: Array<number | undefined>) {
    pos.forEach((po) => {
      po.po_lines.forEach((line: Line) => {
        if (line.qty === null) {
          delete_distributions.push(line.order_id);
        }
      });
    });
  }

  // 处理lines 返回成后端需要的数据格式体
  filterPayloadLines(
    lines: Array<Line>,
    isCreate: boolean,
    factory_id: number
  ): Array<{ id: number; po_id?: number; po_line_id?: number }> {
    lines = this.outsourcingService.deepclone(lines);
    const result: Array<{ id: number; po_id?: number; po_line_id?: number; deletable: boolean; qty: number }> = [];
    lines.forEach((line: Line) => {
      result.push({
        id: isCreate ? 0 : line.factory_id === factory_id ? line.order_id! : 0,
        po_id: line.po_id,
        po_line_id: line?.id,
        deletable: line.deletable,
        qty: line.qty,
      });
    });
    return [...result];
  }
  // 二次确认是否返回
  canLeave() {
    if (this.editMode === 'add') {
      return this.detailForm.dirty ? false : true;
    } else {
      return !(this.editMode === 'edit' && (this.detailForm.dirty || this.factoryDataChange));
    }
  }
  // 切换IO
  async handleChangeValueIo(event: any) {
    const ioId = this.detailForm.get('io_id_local').value;
    const ioCode = this.detailForm.get('io_code').value;
    if (!this.flcUtilService.isNilOrEmptyStr(ioId)) {
      const title = `${this.translateKey('outsourcingMessage.确定')}<span class="red-mark">${this.translateKey(
        'outsourcingMessage.切换'
      )}</span>${this.translateKey('outsourcingMessage.大货单号？')}`;
      const content = this.translateKey('outsourcingMessage.切换大货单后所有已分配的数据将会清空');
      this.outsourcingService.confirmDialog(title, content).afterClose.subscribe(async (val) => {
        if (val) {
          // 切换大货单时删除所有二次工艺的加工厂
          const delete_factory_infos: Array<number> = [];
          Object.keys(this.allFactoryInfo).forEach((key) => {
            this.allFactoryInfo[key].forEach((factory: any) => {
              if (factory.factory_id) {
                delete_factory_infos.push(factory.factory_id);
              }
            });
          });
          this.delete_factory_infos = delete_factory_infos;
          this.detailForm.get('io_id_local').setValue(event.value);
          if (!this.flcUtilService.isNilOrEmptyStr(event.value)) {
            await this.getIoDetail(event.value);
            this.showMainPanel = true;
          } else {
            this.showMainPanel = false;
          }
        } else {
          this.detailForm.get('io_id_local').setValue(ioId);
          this.detailForm.get('io_id').setValue(ioId);
          this.detailForm.get('io_code').setValue(ioCode);
          this.ioCodeDefaultValue = { label: ioCode, value: ioId };
        }
      });
    } else {
      this.detailForm.get('io_id_local').setValue(event.value);
      this.showMainPanel = true;
      await this.getIoDetail(event.value);
    }
  }
  // Tool
  /**
   * 初始化表单配置项
   */
  initEditorInfoDef() {
    const io_code = [
      {
        type: 'select',
        code: 'io_id_local',
        labelKey: 'io_code',
        valueKey: 'io_id',
        label: '大货单号',
        required: true,
      },
      {
        type: 'text',
        code: 'status',
        label: '状态',
        formater: setStyle,
      },
    ];
    this.detailFormConfig = [...io_code];
  }
  initEditorFormValidation() {
    this.detailForm = this.fb.group({
      id: [null, [Validators.required]],
      io_id_local: [null],
      io_id: [null, Validators.required],
      io_code: [null], // 回显大货单号使用
      position_list: [null, [Validators.required]],
      extra_process_info: [[]],
      factorys: new FormArray([]),
      delete_factory_infos: [[]], // 记录删除调的加工厂id
    });
  }
  get factorys() {
    return this.detailForm.get('factorys') as FormArray;
  }
  translateKey(key: string): string {
    return this.translatePipe.transform(key);
  }
}
