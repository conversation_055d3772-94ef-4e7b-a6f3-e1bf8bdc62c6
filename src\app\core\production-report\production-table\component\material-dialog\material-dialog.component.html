<ng-container>
  <div class="basic-header">
    <span *ngFor="let item of basicInfoConfig; last as last">
      {{ item.label }}：<span class="value"><flc-text-truncated [data]="orderInfo[item.key]"></flc-text-truncated></span>
      <nz-divider nzType="vertical" *ngIf="!last"></nz-divider>
    </span>
  </div>

  <ng-container *ngIf="datalist?.length; else notDataTpl">
    <div class="materical-content" *ngFor="let data of datalist">
      <div class="materical-header">
        <span *ngFor="let item of titleConfig; last as last">
          {{ item.label }}：<span class="value"><flc-text-truncated [data]="data.order_bom[item.key]"></flc-text-truncated></span>
          <nz-divider nzType="vertical" *ngIf="!last"></nz-divider>
        </span>
      </div>
      <nz-table
        #nzTable
        [nzData]="data?.bulk_rel_info || []"
        nzStriped="true"
        [nzFrontPagination]="false"
        [nzScroll]="{ x: '100%', y: 'calc(100vh - 160px)' }"
        nzBordered="true">
        <thead>
          <tr>
            <th nzWidth="150px">{{ currentLabels.title }}</th>
            <th nzWidth="100px">状态</th>
            <th nzWidth="100px" *ngIf="type !== 1">数量(基本)</th>
            <th nzWidth="100px" *ngIf="type !== 1">数量(采购)</th>
            <th nzWidth="180px">{{ currentLabels.date }}</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let item of nzTable.data; index as i">
            <tr>
              <td><flc-text-truncated [data]="item.code"></flc-text-truncated></td>
              <td><flc-text-truncated [data]="formateStatus(item.status)"></flc-text-truncated></td>
              <td *ngIf="type !== 1">
                <flc-text-truncated [data]="item.qty ? item.qty + (item.unit_name ?? '') : null"></flc-text-truncated>
              </td>
              <td *ngIf="type !== 1">
                <flc-text-truncated [data]="item.proc_qty ? item.proc_qty + (item.procurement_unit_name ?? '') : null"></flc-text-truncated>
              </td>
              <td><flc-text-truncated [data]="formateFinishTime(item.finish_time)"></flc-text-truncated></td>
            </tr>
          </ng-container>
        </tbody>
      </nz-table>
    </div>
  </ng-container>
  <ng-template #notDataTpl>
    <div style="margin: 16px 0">
      <flc-no-data></flc-no-data>
    </div>
  </ng-template>
</ng-container>
