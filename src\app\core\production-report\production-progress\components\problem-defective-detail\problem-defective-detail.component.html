<div class="problem-defective-detail">
  <nz-spin [nzSpinning]="loading">
    <div class="info">
      <div class="info-item" *ngFor="let item of infoList; let final = last">
        <ng-container *ngIf="item?.visible">
          <span class="info-item-title">{{ 'productionProgress.' + item?.label | translate }}：</span>
          <span class="info-item-value">
            <flc-text-ellipsis
              *ngIf="item.value === 'biz_date'"
              [qty]="16"
              [totalString]="(data[item.value] | date: 'MM/dd') ?? ''"></flc-text-ellipsis>
            <flc-text-ellipsis *ngIf="item.value !== 'biz_date'" [qty]="16" [totalString]="data[item.value]"></flc-text-ellipsis>
          </span>
          <nz-divider nzType="vertical" *ngIf="!final"></nz-divider>
        </ng-container>
      </div>
    </div>
    <defective-color-size-table *ngIf="data.po_lines?.length; else noPoLines" [dataList]="data.po_lines"></defective-color-size-table>
    <ng-template #noPoLines>
      <div class="no-data" *ngIf="!data.production_line_info?.length">
        <flc-no-data></flc-no-data>
      </div>
    </ng-template>
    <ng-container
      *ngIf="
        [progressDataTypeEnum.order_count, progressDataTypeEnum.sewing_qty, progressDataTypeEnum.daily_sewing_qty].includes(
          payload.data_type
        ) && data.production_line_info?.length
      ">
      <div class="line-item" *ngFor="let line of data.production_line_info">
        <div class="line-title">{{ line.line_name }}</div>
        <defective-color-size-table *ngIf="line.po_lines?.length; else noData" [dataList]="line.po_lines"></defective-color-size-table>
      </div>
    </ng-container>
  </nz-spin>
</div>
<ng-template #noData>
  <div class="no-data" *ngIf="!data.production_line_info?.length">
    <flc-no-data></flc-no-data>
  </div>
</ng-template>
