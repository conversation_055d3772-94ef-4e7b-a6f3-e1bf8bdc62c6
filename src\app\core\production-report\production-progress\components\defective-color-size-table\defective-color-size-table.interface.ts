/*====== FlcColorSizeTable Part ======*/

export interface FlcColorSizeTableCell {
  id: number;
  line_uuid?: string;
  color_info: FlcColorSizeTableColorInfo;
  size_info: FlcColorSizeTableSizeInfo;
  qty: number | null;
  indexing: number;
  rowIndex?: number;
  deletable: boolean;
  changeable?: boolean;
  over?: boolean; // 是否超出
  inspection_item_list?: Array<InspectionItem>;
  extraParams?: Partial<FlcColorSizeTableCellExtraParams>;
}

export interface InspectionItem {
  inspection_item_id: number;
  inspection_item_name: string;
}

export interface FlcColorSizeTableCellExtraParams {
  cellEditable: boolean;
  maxNum: number;
  minNum: number;
}

export interface FlcColorSizeTableColorInfo {
  color_id: number | string;
  color_name: string;
  color_code: string;
}

export interface FlcColorSizeTableSizeInfo {
  spec_id: number | string;
  spec_size: string;
  spec_code: string;
}

/*====== FlcStdTable Part ======*/

export interface FlcStdTableDef {
  mode: 'readOnly';
  tableCls: string;
  width: number;
  data: Array<FlcStdTableRowItem>;
  header: Array<FlcStdTableHeaderDef>;
  rowSum: Array<number>;
  colSum: Array<number>;
  totalSum: number;
  scroll: { x?: string; y?: string };
}

export interface FlcStdTableHeaderDef {
  columnId: string | number | null;
  columnName?: string;
  columnCode?: string;
  sortIndex: number;
  deletable: boolean;
  changeable: boolean;
  frozen: boolean;
  over: boolean;
}

export interface FlcStdTableRowItem {
  rowId: string | number | null;
  rowName?: string;
  rowCode?: string;
  columns: Array<FlcStdTableColumnItem>;
  deletable: boolean;
  changeable: boolean;
  frozen: boolean;
  over: boolean;
}

export interface FlcStdTableColumnItem extends Partial<FlcStdTableColumnItemExtraParams> {
  id: number;
  uuid: string;
  columnId: string | number | null;
  columnName?: string;
  columnCode?: string;
  qty: number | null;
  sortIndex: number;
  deletable: boolean;
  changeable: boolean;
  over: boolean;
}

export interface FlcStdTableColumnItemExtraParams {
  frozen: boolean;
  maxQty: number;
  minQty: number;
  over: boolean;
  inspectionItems: string;
}
