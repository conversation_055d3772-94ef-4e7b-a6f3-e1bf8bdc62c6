import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { FlcLeaveGuard } from 'fl-common-lib';
import { UserDetailComponent } from './detail/user-detail.component';
import { UserManagerListComponent } from './user-manager-list/user-manager-list.component'; // 用户列表
const routes: Routes = [
  {
    path: 'list',
    component: UserManagerListComponent,
    data: {
      keepAlive: true,
    },
  },
  {
    path: 'list/:id',
    component: UserDetailComponent,
    canDeactivate: [FlcLeaveGuard],
  },
  { path: '', redirectTo: 'list' },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class UserRoutingModule {}
