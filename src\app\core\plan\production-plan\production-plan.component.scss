$t3-color: #515665;
$l2-color: #d4d7dc;
$fl-bg-white: #ffffff;

$o2-color: #fc8334;
$o4-color: #ffe8d9;

::ng-deep .plan-search-box {
  .flc-search-container {
    margin-bottom: 8px;
  }

  .ant-select {
    min-width: 96px;
  }

  .plan-select-option {
    max-width: 240px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

::ng-deep .production-plan-back {
  display: flex;
  flex-direction: column;
  background: $fl-bg-white;
  border-radius: 4px;

  .app-title-bar {
    margin-bottom: 0px !important;
  }

  .left-title-bar {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 12px;
    font-weight: 500;
    color: #4a5160;
    margin: 8px 0;

    & > div {
      display: flex;
      align-items: center;
    }

    .title-divider {
      width: 1px;
      height: 10px;
      border: 1px solid #d4d7dc;
    }

    .title {
      font-size: 18px;
      font-weight: 500;
      color: $t3-color;
      margin-left: 12px;
    }

    .scheduled-icon {
      display: inline-block;
      width: 14px;
      height: 14px;
      background: $l2-color;
      border-radius: 5px;
      margin-right: 4px;
    }
  }

  .switch-io-btn,
  .switch-io-btn:focus,
  .switch-io-btn.fl-button-toggle-active,
  .switch-io-btn.fl-button-toggle-active:focus {
    background: #fff;
    border: 1px solid #138aff;
    color: #138aff;
  }

  .switch-io-btn.fl-button-toggle:hover,
  .switch-io-btn.fl-button-toggle-active:hover {
    background: #f2f9ff;
    border: 1px solid #138aff;
    color: #138aff;
  }

  .switch-mode-btn,
  .switch-mode-btn:focus,
  .switch-mode-btn.fl-button-toggle-active,
  .switch-mode-btn.fl-button-toggle-active:focus {
    background: #fff;
    border: 1px solid $o2-color;
    color: $o2-color;
  }

  .switch-mode-btn.fl-button-toggle:hover,
  .switch-mode-btn.fl-button-toggle-active:hover {
    background: $o4-color;
    border: 1px solid $o2-color;
    color: $o2-color;
  }
}
