#mainBoard {
}
.timeArea {
  margin-bottom: 12px;
}
.footer {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  .totalArea {
    position: absolute;
    left: 0;
    background-color: #ffebc7;
    padding: 4px 6px;
    border-radius: 6px;
    color: #223c4b;
    span {
      color: #ffa600;
    }
  }
}
:host ::ng-deep {
  .ant-divider-horizontal {
    margin: 12px 0;
  }
  .gallery-outer-container {
    scrollbar-width: none;
  }
}

.orderInfo {
  background: #f7f8fa;
  border-radius: 0px 0px 8px 8px;
  display: flex;
  padding: 4px 12px;
  align-items: center;
  margin-bottom: 6px;
  column-gap: 4px;
  flex-wrap: wrap;
  .orderItem {
    font-size: 14px;
    color: #262d48;
    font-weight: 500;
    span {
      color: #54607c;
    }
  }
}
