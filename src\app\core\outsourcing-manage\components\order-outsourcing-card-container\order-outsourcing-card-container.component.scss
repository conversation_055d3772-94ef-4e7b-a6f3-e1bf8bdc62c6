$titleColor: #222b3c;

.card-container {
  padding: 12px 8px;
  background-color: #ffffff;
  border-radius: 4px 4px 12px 12px;

  .card-container-title {
    color: $titleColor;
    font-weight: 500;
    font-size: 16px;
    position: relative;
  }

  .corner-title {
    font-size: 14px;
    display: flex;
    align-items: center;

    &::before {
      content: '';
      display: inline-block;
      width: 3px;
      height: 14px;
      margin-right: 4px;
      background: #54607c;
      border-radius: 2px;
    }
  }

  .title-line {
    &::after {
      content: '';
      display: block;
      height: 0.7px;
      background-color: #d4d7dc;
      margin: 8px 0 12px 0;
    }
  }
}
