<div class="modal-body">
  <div class="modal-body-header">
    <span>大货订单：</span>
    <span
      >{{ data.order_code }}
      <span class="pre-order-tag">预</span>
    </span>
    <nz-divider nzType="vertical"></nz-divider>
    <span>分配件数：</span>
    <span>{{ data.allocated_qty }}</span>
    <nz-divider nzType="vertical"></nz-divider>
    <span>计划起止：</span>
    <span>
      <flc-text-truncated [template]="timeTemplate">
        <ng-template #timeTemplate>
          <span>
            <ng-container *ngIf="data.start_time">
              {{ data.start_time | date: 'yyyy/MM/dd' }}
            </ng-container>
            <ng-container *ngIf="!data.start_time"> 未开始 </ng-container>
          </span>
          ~
          <span>
            <ng-container *ngIf="data.end_time">
              {{ data.end_time | date: 'yyyy/MM/dd' }}
            </ng-container>
            <ng-container *ngIf="!data.end_time"> 未完成 </ng-container>
          </span>
        </ng-template>
      </flc-text-truncated>
    </span>
  </div>
  <div class="modal-content">
    <form class="modal-table" nz-form [formGroup]="modalForm">
      <nz-table [nzData]="dataList.value" nzBordered nzShowPagination="false" [nzScroll]="{ y: '250px' }">
        <thead>
          <tr>
            <th>订单</th>
            <th>订单件数</th>
            <th [nzWidth]="'60px'" [nzRight]="true">操作</th>
          </tr>
        </thead>
        <tbody formArrayName="list">
          <tr *ngFor="let control of dataList.controls; index as i; first as isFirst; last as isLast" [formGroupName]="i">
            <td nzLeft>
              <nz-form-item>
                <nz-form-control [flcErrorTip]="'订单'">
                  <nz-select
                    [formControlName]="'order_uuid'"
                    nzAllowClear
                    nzShowSearch
                    nzPlaceHolder="请选择"
                    [nzDropdownMatchSelectWidth]="false"
                    (ngModelChange)="onChangeSelect($event, control)">
                    <nz-option *ngFor="let item of orderList" [nzValue]="item.order_uuid" [nzLabel]="item.order_code"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </td>
            <td>{{ control.get('qty')?.value ?? '-' }}</td>
            <td [nzRight]="true">
              <span class="line-operate-btn">
                <a nz-button nzType="text" [disabled]="dataList.controls.length === 1">
                  <i nz-icon [nzIconfont]="'icon-jianshao'" (click)="onRemoveRow(i)"></i>
                </a>
                <a nz-button nzType="text">
                  <i nz-icon [nzIconfont]="'icon-zengjia1'" (click)="onAddRow(i)"></i>
                </a>
              </span>
            </td>
          </tr>
        </tbody>
      </nz-table>
    </form>
  </div>
</div>
<div class="bottomBar">
  <button nz-button flButton="default-negative" [nzShape]="'round'" style="margin-right: 0" (click)="onCloseModal(false)">取消</button>
  <button style="margin-right: 0" nzType="primary" nz-button flButton="default-positive" [nzShape]="'round'" (click)="onCloseModal(true)">
    确认
  </button>
</div>
