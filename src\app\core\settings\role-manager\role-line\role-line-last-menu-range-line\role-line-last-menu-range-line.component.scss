.TitleBar {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  font-weight: 500;
  color: #222b3c;
  line-height: 20px;
}

.buttonContent {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  background-color: #fff;
  margin: 5px;
  border: 1px solid #e7e7e7;
  padding: 12px 20px;
  border-radius: 4px;
}

:host .radioText {
  ::ng-deep span:last-child {
    font-size: 14px;
    font-weight: 500;
    color: #222b3c;
    line-height: 20px;
  }
}

.errorText {
  color: #ff5c33;
}

:host .isDisabled {
  ::ng-deep {
    i {
      color: #d5d7dc !important;
    }

    span:last-child {
      color: #222b3c;
    }
  }
}
