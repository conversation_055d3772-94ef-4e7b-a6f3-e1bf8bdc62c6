<!-- 头部开始 -->
<flc-app-header [showTemplate]="true" [headerTopTitle]="headerTitle" [headerBtn]="headerBtnTmpl"></flc-app-header>
<ng-template #headerTitle>
  <div>
    排料单详情
    <span *ngIf="bomData?.approve_status_value?.length" class="approve_status" [ngClass]="'approve_status' + bomData?.approve_status">
      {{ bomData?.approve_status_value }}
    </span>
  </div>
</ng-template>
<ng-template #headerBtnTmpl>
  <div style="display: flex; align-items: center">
    <div>
      算料员:{{ baseInfo?.io_info?.dist_user_label }}
      <a nz-button style="width: 20px" nzType="link" *ngIf="btnArr.includes('order:layout-dist')" (click)="showDistributionModal()">
        <i nz-icon nzIconfont="icon-caozuolan_bianji1" style="font-size: 15px" class="icon iconfont"></i>
      </a>
    </div>
    <nz-divider style="margin: 0 8px 0 8px" nzType="vertical"></nz-divider>
    <button
      (click)="onBomAudit(true)"
      *ngIf="btnArr.includes('order:layout-approve') && bomData?.approve_status == 2"
      style="margin-right: 12px"
      [nzShape]="'round'"
      nz-button>
      审核通过
    </button>
    <button
      (click)="onBomAudit(false)"
      *ngIf="btnArr.includes('order:layout-approve') && bomData?.approve_status == 2"
      style="margin-right: 12px"
      [nzShape]="'round'"
      nz-button>
      退回修改
    </button>
    <button (click)="back()" nz-button [nzShape]="'round'" style="margin-right: 12px">返回</button>
    <!-- <nz-divider style="margin: 0 8px 0 8px" nzType="vertical"></nz-divider> -->
    <button
      nz-button
      flButton="minor"
      [nzShape]="'round'"
      [disabled]="exportDisabled"
      [nzLoading]="exportDisabled"
      *ngIf="btnArr.includes('order:layout-export')"
      (click)="export()">
      <i nz-icon nzIconfont="icon-daochu" style="font-size: 15px" class="icon iconfont"></i>导出
    </button>
  </div>
</ng-template>
<!-- 头部结束 -->
<!-- 小贴士开始 -->
<div *ngIf="bomData?.approve_reason?.length" class="approve_reason">退回原因： {{ bomData?.approve_reason }}</div>
<div class="discharge-tip">
  <div>
    <span class="margin-left-10 margin-right32">IO：{{ baseInfo?.io_info?.io_code | noValue }}</span>
    <span class="margin-right32">款式编码：{{ baseInfo?.io_info.style_code | noValue }}</span>
    <span class="margin-right32">品名：{{ baseInfo?.io_info.category | noValue }}</span>
    <span class="margin-right32">客户名称：{{ baseInfo?.io_info.customer | noValue }}</span>
    <span class="margin-right32">总数(件) ：{{ baseInfo?.io_info.qty | noValue }}</span>
  </div>
  <div style="margin-left: auto">
    <button
      style="height: 24px; display: flex; align-items: center; margin-right: 6px"
      nz-button
      [nzTooltipTitle]="_showTip ? '关闭小贴士' : '打开小贴士'"
      nz-tooltip
      nzType="default"
      nzShape="round"
      [nzSize]="'small'"
      (click)="toggleShowTip()"
      [ngClass]="{ 'ant-btn-default': !_showTip, textGray: !_showTip, tipActiveButton: _showTip, tipButton: !_showTip }">
      <i nz-icon nzType="sound" nzTheme="outline"></i>
      小贴士
    </button>
  </div>
</div>
<div *ngIf="_showTip" class="tipContainer">
  <div class="leftArea"><span>小 贴 士</span></div>
  <div class="rightArea">
    <div class="tip-content">
      <p>用料面积（平方厘米）= 毛门幅 * 段数 * 段长</p>
      <p>每打用料（公斤）= 用料面积（平方厘米）* 克重 * 损耗 / 10000000</p>
      <p>每打用料（米）= 段长 * 段数 * 损耗 / 100</p>
      <p>平均用料 = 每个尺码单耗 * 每个尺码数量（每个尺码出来的用料总数量相加的和）/ 总件数</p>
    </div>
  </div>
  <div style="position: absolute; right: 0; bottom: 0">
    <img style="width: 50px; height: 50px" src="../../../../../assets/image/speaker.png" alt="" />
  </div>
</div>
<div class="discharge-box">
  <div class="discharge-left">
    <div class="discharge-base-info">
      <div class="base-info-header" style="min-width: 600px">
        <span class="title">基础信息</span>
        <div style="display: flex; align-items: center">
          <span
            class="remark"
            *ngIf="moreFlag"
            [ngClass]="{ 'remark-active': showRemark }"
            nz-tooltip
            [nzTooltipTitle]="!showRemark ? '打开备注' : '删除备注'"
            (click)="remarkClick()">
            <i class="icon iconfont iconbianzu" style="font-size: 12px"></i>
            备注
          </span>
          <span class="expand" *ngIf="!moreFlag" (click)="toggleExpanded()" nzTooltipTitle="查看已排/未排" nz-tooltip>
            <i class="icon iconfont iconzhankai1" style="font-size: 12px"></i>
            展开
          </span>
          <span class="pack-up" *ngIf="moreFlag" (click)="toggleExpanded()" nzTooltipTitle="收起已排/未排" nz-tooltip>
            <i class="icon iconfont iconzhankai2" style="font-size: 12px"></i>
            收起
          </span>
        </div>
      </div>
      <div *ngIf="moreFlag" #baseWrap class="table-scroll clear-header-border" [ngStyle]="{ maxHeight: '400px' }">
        <nz-table
          #basicTable
          [nzData]="baseInfo?.lines_by_color_po"
          [nzBordered]="false"
          [nzOuterBordered]="false"
          [nzFrontPagination]="false"
          [nzShowPagination]="false"
          [nzScroll]="{ x: '600px', y: !moreFlag || remark ? '154px' : '354px' }">
          <thead>
            <tr>
              <th nzWidth="140px">颜色</th>
              <th nzWidth="140px">交付单</th>
              <th nzWidth="140px">客期</th>
              <th nzWidth="80px" *ngFor="let sizeItem of baseInfo?.size_list">{{ sizeItem.spec_name }}</th>
              <th nzWidth="100px">合计(件)</th>
            </tr>
          </thead>
          <tbody>
            <tr [ngStyle]="{ background: i % 2 !== 0 ? '#F7F8FA' : '#fff' }" *ngFor="let data of basicTable.data; index as i">
              <td>{{ data.color_name | noValue }}</td>
              <td>{{ data.po_code | noValue }}</td>
              <td>{{ data.due_time | date: 'yyyy/MM/dd' | noValue }}</td>
              <td *ngFor="let item of data.qty_list">
                {{ item | noValue }}
              </td>
            </tr>
            <tr *ngIf="basicTable.data.length > 0" style="background: #f1f7ff">
              <td style="color: #138aff">合计</td>
              <td></td>
              <td></td>
              <td style="color: #138aff" *ngFor="let item of colTotalArr">
                {{ item | noValue }}
              </td>
            </tr>
          </tbody>
        </nz-table>
        <div class="io-remark" *ngIf="showRemark">
          <nz-textarea-count class="inline-count" [nzMaxCharacterCount]="1000">
            <textarea
              [nzAutosize]="{ minRows: 4, maxRows: 10 }"
              [(ngModel)]="remark"
              nz-input
              placeholder="请输入备注"
              [maxLength]="1000"
              (blur)="saveRemark()">
            </textarea>
          </nz-textarea-count>
        </div>
        <ng-container *ngIf="moreFlag">
          <ng-container *ngFor="let item of baseInfo?.lines_by_color">
            <div class="detail-header" style="min-width: 600px">
              <span class="detail-header-left">{{ item.color_name | noValue }}</span>
              <div class="detail-header-right">
                <ng-container *ngIf="item.po_ucode_list.length === 1">
                  <span class="po-item" nz-popover [nzPopoverContent]="contentTemplate">
                    PO：{{ getPoInfo(item.po_ucode_list[0]).po_code }}({{
                      getPoInfo(item.po_ucode_list[0]).due_time | date: 'yyyy/MM/dd'
                    }}){{ getPoInfo(item.po_ucode_list[0]).country_name }}
                  </span>
                </ng-container>
                <ng-container *ngIf="item.po_ucode_list.length >= 2">
                  <div nz-popover [nzPopoverContent]="contentTemplate">
                    <span class="po-item" style="padding-right: 12px">
                      PO：{{ getPoInfo(item.po_ucode_list[0]).po_code }}({{
                        getPoInfo(item.po_ucode_list[0]).due_time | date: 'yyyy/MM/dd'
                      }}){{ getPoInfo(item.po_ucode_list[0]).country_name }}
                    </span>
                    <span class="po-item" style="padding-left: 12px">
                      {{ getPoInfo(item.po_ucode_list[1]).po_code }}({{ getPoInfo(item.po_ucode_list[1]).due_time | date: 'yyyy/MM/dd' }}){{
                        getPoInfo(item.po_ucode_list[1]).country_name
                      }}...
                    </span>
                  </div>
                </ng-container>
                <ng-template #contentTemplate>
                  <div style="max-height: 200px; overflow: auto">
                    <div
                      style="font-size: 12px; font-weight: 500; color: #354060; line-height: 17px"
                      *ngFor="let ioItem of item.po_ucode_list">
                      PO：{{ getPoInfo(ioItem).po_code }} ({{ getPoInfo(ioItem).due_time }}) {{ getPoInfo(ioItem).country_name }}
                    </div>
                  </div>
                </ng-template>
              </div>
            </div>
            <nz-table
              class="detail-body"
              [nzData]="item.order_qty"
              [nzBordered]="false"
              [nzOuterBordered]="false"
              [nzFrontPagination]="false"
              [nzShowPagination]="false"
              [nzScroll]="{ x: '900px' }">
              <thead>
                <tr>
                  <th nzWidth="100px"></th>
                  <th nzWidth="100px" *ngFor="let sizeItem of baseInfo?.size_list" nzWidth="100px">{{ sizeItem.spec_name }}</th>
                  <th nzWidth="100px">合计(件)</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>订单数</td>
                  <td *ngFor="let data of item.order_qty">{{ data }}</td>
                </tr>
                <tr style="background-color: #f7f8fa">
                  <td>已排</td>
                  <td *ngFor="let data of item.done_qty">{{ data }}</td>
                </tr>
                <tr>
                  <td>未排</td>
                  <td *ngFor="let data of item.unFinish_qty">
                    {{ data < 0 ? -data : data }}
                    <span class="over" *ngIf="data < 0">超</span>
                  </td>
                </tr>
              </tbody>
            </nz-table>
          </ng-container>
          <ng-container>
            <div class="detail-header" style="border-left: 2px solid #138aff; min-width: 600px">
              <span class="detail-header-left">合计</span>
              <div class="detail-header-right">
                <span class="po-item">IO：{{ baseInfo?.io_info.io_code | noValue }}</span>
              </div>
            </div>
            <nz-table
              style="margin-top: 4px"
              [nzData]="baseInfo?.lines_by_color"
              [nzBordered]="false"
              [nzOuterBordered]="false"
              [nzFrontPagination]="false"
              [nzShowPagination]="false"
              [nzScroll]="{ x: '900px' }">
              <thead>
                <tr>
                  <th nzWidth="100px"></th>
                  <th nzWidth="100px" *ngFor="let sizeItem of baseInfo?.size_list" nzWidth="100px">{{ sizeItem.spec_size }}</th>
                  <th nzWidth="100px">合计(件)</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>订单数</td>
                  <td *ngFor="let data of ioBaseInfo.order_qty">{{ data }}</td>
                </tr>
                <tr style="background-color: #f7f8fa">
                  <td>已排</td>
                  <td *ngFor="let data of ioBaseInfo.done_qty">{{ data }}</td>
                </tr>
                <tr>
                  <td>未排</td>
                  <td *ngFor="let data of ioBaseInfo.unfinish_qty">
                    {{ data < 0 ? -data : data }}<span class="over" *ngIf="data < 0">超</span>
                  </td>
                </tr>
              </tbody>
            </nz-table>
          </ng-container>
        </ng-container>
      </div>
    </div>
    <div class="discharge-bom-info">
      <div class="bom-info-header" style="min-width: 400px">
        <div class="bom-btn">
          <span class="left">BOM信息</span>
          <div class="right">
            <ng-container *ngIf="bomEditable; else bomEditBtnTemp">
              <button nz-button [nzShape]="'round'" [nzSize]="'small'" (click)="toggleBomEditState()" style="margin-right: 8px">
                取消
              </button>
              <button nz-button [nzShape]="'round'" [nzSize]="'small'" nzType="primary" [nzLoading]="saving" (click)="save()">提交</button>
            </ng-container>
            <ng-template #bomEditBtnTemp>
              <button
                (click)="toggleBomEditState()"
                *ngIf="btnArr.includes('order:layout-update')"
                [nzShape]="'round'"
                nz-button
                [nzType]="'primary'"
                [nzSize]="'small'">
                <i nz-icon nzType="edit" nzTheme="outline"></i>编辑
              </button>
            </ng-template>
            <i
              nz-icon
              [nzIconfont]="bomFullScreen ? 'icon-shouqi-weixuanzhong' : 'icon-zhankai1'"
              nz-tooltip
              [nzTooltipTitle]="bomFullScreen ? '收起全屏' : '展开全屏'"
              class="bom-zhankai-icon"
              (click)="onBomFullScreen()"></i>
          </div>
        </div>
        <div class="bom-tab">
          <div style="width: 900px">
            <nz-tabset nzType="card" class="linetabs" [nzSize]="'small'">
              <nz-tab
                *ngFor="let tab of colorList; index as i"
                [nzTitle]="tab.colorName"
                [nzDisabled]="bomEditable && tab.colorCode !== activeColor"
                (nzClick)="switchColor(tab)">
              </nz-tab>
            </nz-tabset>
          </div>
          <div style="display: flex; align-items: center">
            <ng-container *ngIf="!bomEditable; else bomEditTemp">
              <nz-form-label>排料状态</nz-form-label>
              <nz-form-control>
                <nz-select
                  style="width: 80px"
                  [(ngModel)]="selectedValue"
                  [nzSize]="'small'"
                  (ngModelChange)="dischargeModelChange($event)">
                  <nz-option *ngFor="let data of layoutStatusOption" [nzValue]="data.value" [nzLabel]="data.label"></nz-option>
                </nz-select>
              </nz-form-control>
            </ng-container>
            <ng-template #bomEditTemp>
              <a
                *ngIf="activeColor !== ''"
                [nzTooltipTitle]="'同步订单其它颜色维护好的数据'"
                nz-tooltip
                nz-button
                nzType="link"
                [nzSize]="'small'"
                flButton="link-minor"
                (click)="reuseOtherColor()"
                >同步其它颜色</a
              >
            </ng-template>
          </div>
        </div>
      </div>
      <form nz-form *ngIf="validateForm" [formGroup]="validateForm">
        <div class="bom-info-body" style="width: calc(100% - 6px); overflow-y: auto; overflow-x: hidden">
          <ng-container *ngFor="let item of bomData.lc_bom_list; index as i">
            <ng-container *ngIf="activeColor === '' || item.color_code === activeColor">
              <div style="margin-bottom: 16px">
                <div class="average-material" style="padding: 0 8px">
                  <span class="left" style="width: 150px">{{ item.color_name | noValue }}</span>
                  <div class="right">
                    <ng-container *ngIf="item.color_doz_cons_kg > 0">
                      <span class="unit-kg">公斤</span>
                      <span>平均用料(打){{ item.color_doz_cons_kg | noValue }}</span
                      ><span
                        style="margin-left: 5px"
                        class="icon iconfont icontishi1 exclamation-circle"
                        nz-popover
                        [nzPopoverContent]="item.dozConsListKg.length || item.dozConsListMi.length ? dozConsTemplate : null"></span>

                      <span style="margin-left: 20px">单耗{{ item.color_piece_cons_kg | noValue }}</span
                      ><span
                        style="margin-left: 5px"
                        class="icon iconfont icontishi1 exclamation-circle"
                        nz-popover
                        [nzPopoverContent]="item.pieceConsListMi.length || item.pieceConsListKg.length ? pieceConsTemplate : null"></span>
                      <ng-template #dozConsTemplate>
                        <div style="max-width: 300px">
                          <div>
                            <ng-container *ngFor="let oitem of item.dozConsListKg; index as i">
                              <span style="font-size: 14px; color: #596480; line-height: 20px">({{ oitem.material_name }})</span>
                              <span style="font-size: 14px; color: #262d48; line-height: 20px"
                                >{{ oitem.doz_cons }}{{ i === item.dozConsListKg.length - 1 ? '=' : '+' }}</span
                              >
                              <span style="font-size: 14px; color: #262d48; line-height: 20px" *ngIf="i === item.dozConsListKg.length - 1">
                                {{ item.dozConsKgSum.toFixed(2) }}公斤
                              </span>
                            </ng-container>
                          </div>
                        </div>
                      </ng-template>
                      <ng-template #pieceConsTemplate>
                        <div style="max-width: 300px">
                          <div>
                            <ng-container *ngFor="let oitem of item.pieceConsListKg; index as i">
                              <span style="font-size: 14px; color: #596480; line-height: 20px">({{ oitem.material_name }})</span>
                              <span style="font-size: 14px; color: #262d48; line-height: 20px"
                                >{{ oitem.piece_cons }}{{ i === item.pieceConsListKg.length - 1 ? '=' : '+' }}</span
                              >
                              <span
                                style="font-size: 14px; color: #262d48; line-height: 20px"
                                *ngIf="i === item.pieceConsListKg.length - 1">
                                {{ item.pieceConsKgSum.toFixed(3) }}公斤
                              </span>
                            </ng-container>
                          </div>
                        </div>
                      </ng-template>
                    </ng-container>
                    <span *ngIf="item.color_doz_cons_kg > 0 && item.color_doz_cons_m > 0" class="line">|</span>
                    <ng-container *ngIf="item.color_doz_cons_m > 0">
                      <span class="unit-m">米</span>
                      <span>平均用料(打) {{ item.color_doz_cons_m | noValue }}</span
                      ><span
                        class="icon iconfont icontishi1 exclamation-circle"
                        nz-popover
                        [nzPopoverContent]="item.dozConsListKg.length || item.dozConsListMi.length ? dozConsTemplate : null"></span>
                      <span style="margin-left: 20px">单耗 {{ item.color_piece_cons_m | noValue }}</span
                      ><span
                        class="icon iconfont icontishi1 exclamation-circle"
                        nz-popover
                        [nzPopoverContent]="item.pieceConsListMi.length || item.pieceConsListKg.length ? pieceConsTemplate : null"></span>
                      <ng-template #dozConsTemplate>
                        <div style="max-width: 300px">
                          <div>
                            <ng-container *ngFor="let oitem of item.dozConsListMi; index as i">
                              <span style="font-size: 14px; color: #596480; line-height: 20px">({{ oitem.material_name }})</span>
                              <span style="font-size: 14px; color: #262d48; line-height: 20px"
                                >{{ oitem.doz_cons }}{{ i === item.dozConsListMi.length - 1 ? '=' : '+' }}</span
                              >
                              <span *ngIf="i === item.dozConsListMi.length - 1" style="font-size: 14px; color: #262d48; line-height: 20px">
                                {{ item.dozConsMiSum.toFixed(2) }}米
                              </span>
                            </ng-container>
                          </div>
                        </div>
                      </ng-template>
                      <ng-template #pieceConsTemplate>
                        <div style="max-width: 300px">
                          <div>
                            <ng-container *ngFor="let oitem of item.pieceConsListMi; index as i">
                              <span style="font-size: 14px; color: #596480; line-height: 20px">({{ oitem.material_name }})</span>
                              <span style="font-size: 14px; color: #262d48; line-height: 20px">
                                {{ oitem.piece_cons }}{{ i === item.pieceConsListMi.length - 1 ? '=' : '+' }}</span
                              >
                              <span
                                style="font-size: 14px; color: #262d48; line-height: 20px"
                                *ngIf="i === item.pieceConsListMi.length - 1">
                                {{ item.pieceConsMiSum.toFixed(3) }}米
                              </span>
                            </ng-container>
                          </div>
                        </div>
                      </ng-template>
                    </ng-container>
                  </div>
                </div>
                <div style="min-width: 400px">
                  <nz-table
                    *ngIf="getBomFormArray(i)?.length > 0"
                    style="margin-top: 4px"
                    [nzData]="getBomFormArray(i)"
                    [nzBordered]="true"
                    [nzFrontPagination]="false"
                    [nzShowPagination]="false"
                    class="bom-border-right"
                    [nzScroll]="{ x: '600px', y: '360px' }">
                    <thead>
                      <tr>
                        <ng-container *ngFor="let item of renderHeaders">
                          <th *ngIf="item.visible && item.key === 'part'" nzLeft nzWidth="100px" style="text-align: left !important">
                            <a class="setting-btn" [ngStyle]="{ color: btnHighLight ? '#4D96FF' : '' }" (click)="changeHeader($event)">
                              <i nz-icon nzType="setting" nzTheme="fill"></i> </a
                            >部位
                          </th>
                          <th *ngIf="item.visible && item.key !== 'part'" [nzWidth]="item.width">
                            <ng-container *ngIf="item.required">
                              <span class="red-dot"></span>
                            </ng-container>
                            {{ item.label }}
                          </th>
                        </ng-container>

                        <th nzRight nzWidth="100px" nzRight *ngIf="bomEditable">操作</th>
                      </tr>
                    </thead>
                    <tbody [formArrayName]="'bom_accessories' + i">
                      <ng-container *ngFor="let data of getBomFormArray(i); index as ind" [formGroupName]="ind">
                        <tr [ngClass]="data?.get('add_remark').value ? 'clear-border' : ''" class="col-remark">
                          <td nzLeft class="position-remark" style="text-align: left !important; border-right: none">
                            <div
                              *ngIf="bomEditable"
                              [nzTooltipPlacement]="'topLeft'"
                              [nzTooltipTitle]="data?.get('add_remark').value ? '点击删除备注哦～' : '点击为本行数据添加备注'"
                              (click)="bomAddRemak(data)"
                              nz-tooltip>
                              <div *ngIf="!data?.get('add_remark').value" class="add-remark">
                                <i nz-icon nzIconfont="icon-caozuolan-tianjia" class="icon iconfont" style="color: #138aff"></i>
                              </div>
                              <div *ngIf="data?.get('add_remark').value" class="add-remark1">
                                <i nz-icon nzIconfont="icon-caozuolan_shanchu1" class="icon iconfont iconshanchu1 delete-hover"></i>
                              </div>
                            </div>

                            <div class="discharge-no-edit positions" style="padding: 8px 0">
                              <ng-container *ngFor="let positionItem of data?.get('position_list')?.value">
                                <span class="ioption" style="margin-right: 2px; white-space: nowrap">
                                  {{ positionItem }}
                                </span>
                              </ng-container>
                            </div>
                          </td>
                          <td *ngIf="columnIsDisplay('material_code')" style="border-right: none">
                            {{ data?.get('material_code').value | noValue }}
                          </td>

                          <td style="border-right: none">
                            <span>{{ data?.get('material_name').value | noValue }}</span>
                            <span
                              *ngIf="bomEditable && bomRowCanEdit(data)"
                              class="model-name-change"
                              (click)="onOpenMaterialSelector(data, i, 'change')"
                              >更改</span
                            >
                          </td>
                          <!-- 参与排料 -->
                          <td style="border-right: none">
                            <nz-form-item>
                              <nz-form-control *ngIf="bomEditable; else breakdownTmp">
                                <nz-switch
                                  [nzDisabled]="!bomEditable"
                                  formControlName="with_breakdown"
                                  nzCheckedChildren="是"
                                  nzUnCheckedChildren="否"></nz-switch>
                              </nz-form-control>
                              <ng-template #breakdownTmp>
                                <div class="option_style" [ngClass]="data?.get('with_breakdown')?.value ? 'option_style_true' : null">
                                  {{ data?.get('with_breakdown')?.value ? '是' : '否' }}
                                </div>
                              </ng-template>
                            </nz-form-item>
                          </td>
                          <!-- 按段长采购 -->
                          <td style="border-right: none">
                            <nz-form-item>
                              <nz-form-control *ngIf="bomEditable; else bySegTmp">
                                <nz-switch
                                  [nzDisabled]="!bomEditable"
                                  formControlName="by_seg_len"
                                  nzCheckedChildren="是"
                                  nzUnCheckedChildren="否"></nz-switch>
                              </nz-form-control>
                              <ng-template #bySegTmp>
                                <div class="option_style" [ngClass]="data?.get('by_seg_len')?.value ? 'option_style_true' : null">
                                  {{ data?.get('by_seg_len')?.value ? '是' : '否' }}
                                </div>
                              </ng-template>
                            </nz-form-item>
                          </td>
                          <!-- 适用尺码 -->
                          <td style="border-right: none">
                            <nz-form-item *ngIf="bomEditable && bomRowCanEdit(data); else match_specTpl">
                              <nz-form-control>
                                <!-- (ngModelChange)="matchSpecChange($event,getMatchSpecOptions(item.color_code),data)" -->
                                <nz-select formControlName="match_specs" [nzPlaceHolder]="'请选择'" nzMode="multiple">
                                  <nz-option
                                    *ngFor="let item of getMatchSpecOptions(item.color_code)"
                                    [nzLabel]="item.spec_name"
                                    [nzValue]="item.spec_name">
                                  </nz-option>
                                </nz-select>
                              </nz-form-control>
                            </nz-form-item>
                            <ng-template #match_specTpl>
                              <div class="discharge-no-edit positions" style="padding: 8px 0; justify-content: center">
                                <ng-container *ngFor="let spec of data?.get('match_specs')?.value">
                                  <span class="ioption" style="margin-right: 2px; white-space: nowrap">
                                    {{ transforCodeToLabelMatchSpec(spec) | noValue }}
                                  </span>
                                </ng-container>
                              </div>
                            </ng-template>
                          </td>
                          <!-- 毛门幅 -->
                          <td style="border-right: none">
                            <nz-form-item *ngIf="bomEditable && bomRowCanEdit(data); else widthTmp">
                              <nz-form-control>
                                <nz-input-number
                                  formControlName="raw_width"
                                  [nzPlaceHolder]="'请输入'"
                                  [nzMin]="0"
                                  [nzStep]="1"
                                  [nzPrecision]="0"></nz-input-number>
                              </nz-form-control>
                            </nz-form-item>
                            <ng-template #widthTmp>
                              {{ data?.get('raw_width')?.value | noValue }}
                            </ng-template>
                          </td>
                          <!-- 克重 -->
                          <td *ngIf="columnIsDisplay('weight')" style="border-right: none">{{ data?.get('weight')?.value | noValue }}</td>

                          <td *ngIf="columnIsDisplay('material_color_id')" style="border-right: none">
                            <nz-form-item *ngIf="bomEditable; else colorTpl">
                              <nz-form-control>
                                <nz-cascader
                                  formControlName="material_color_id"
                                  [nzOptions]="colorCascadeOption"
                                  nzPlaceHolder="请选择物料颜色"
                                  [nzShowSearch]="true"
                                  nzAllowClear
                                  (ngModelChange)="colorCascadeChange($event, data)">
                                </nz-cascader>
                              </nz-form-control>
                            </nz-form-item>
                            <ng-template #colorTpl>
                              {{ data?.get('material_color_name')?.value || '-' }}
                            </ng-template>
                          </td>
                          <!-- 成分 -->
                          <td *ngIf="columnIsDisplay('comp')" style="border-right: none">{{ data?.get('comp')?.value | noValue }}</td>
                          <!-- 规格 -->
                          <!-- <td *ngIf="columnIsDisplay('spec')" style="border-right: none">
                            <input
                              *ngIf="bomEditable && bomRowCanEdit(data); else specTpl"
                              type="text"
                              nz-input
                              [placeholder]="'flss.placeholder.input' | translate"
                              formControlName="spec_name"
                              (focus)="onSpecificationsFocus(data)"
                              (blur)="onSpecificationsBlur(data)"
                              (input)="onSpecificationsInput($event, data)"
                              [nzAutocomplete]="supplierAuto" />
                            <nz-autocomplete #supplierAuto>
                              <nz-auto-option
                                *ngFor="let item of filterSpecificationsOptions"
                                [nzHide]="item.nzHide"
                                [nzLabel]="item.specification_name"
                                [nzValue]="item.specification_name">
                                {{ item.specification_name }}
                              </nz-auto-option>
                            </nz-autocomplete>
                            <ng-template #specTpl>
                              <div class="discharge-no-edit" style="padding: 8px 0">
                                <span>{{ data?.get('spec_name')?.value | noValue }}</span>
                              </div>
                            </ng-template>
                          </td> -->

                          <td *ngIf="columnIsDisplay('doz_cons')" style="border-right: none">
                            <nz-form-item *ngIf="!data?.get('with_breakdown').value && bomEditable; else dozConsTmp">
                              <nz-form-control>
                                <nz-input-number formControlName="doz_cons" [nzMin]="0" [nzStep]="0.01" [nzPrecision]="2">
                                </nz-input-number>
                              </nz-form-control>
                            </nz-form-item>
                            <ng-template #dozConsTmp>
                              {{ data?.get('doz_cons')?.value | noValue }}
                            </ng-template>
                          </td>
                          <td *ngIf="columnIsDisplay('piece_cons')" style="border-right: none">
                            <nz-form-item *ngIf="!data?.get('with_breakdown')?.value && bomEditable; else pieceConsTmp">
                              <nz-form-control>
                                <nz-input-number formControlName="piece_cons" [nzMin]="0" [nzStep]="0.001" [nzPrecision]="3">
                                </nz-input-number>
                              </nz-form-control>
                            </nz-form-item>
                            <ng-template #pieceConsTmp> {{ data?.get('piece_cons')?.value | noValue }} </ng-template>
                          </td>
                          <td *ngIf="columnIsDisplay('done_qty')" style="border-right: none">
                            <nz-form-item *ngIf="!data?.get('with_breakdown')?.value && bomEditable; else doneQtyTmp">
                              <nz-form-control>
                                <nz-input-number
                                  formControlName="done_qty"
                                  [nzMin]="0"
                                  [nzPlaceHolder]="'已排件数'"
                                  [nzStep]="1"
                                  [nzPrecision]="0">
                                </nz-input-number>
                              </nz-form-control>
                            </nz-form-item>
                            <ng-template #doneQtyTmp>
                              {{ data?.get('done_qty')?.value | noValue }}/{{
                                (data?.get('total_qty').value - data?.get('done_qty').value < 0
                                  ? 0
                                  : data?.get('total_qty').value - data?.get('done_qty').value
                                ) | noValue
                              }}<span class="over" *ngIf="data?.get('total_qty').value - data?.get('done_qty').value < 0">超</span>
                            </ng-template>
                          </td>
                          <td
                            *ngIf="columnIsDisplay('layout_status')"
                            style="border-right: none"
                            [ngStyle]="{ color: data?.get('layout_status').value === 2 ? '#54607C' : '#222B3C' }">
                            {{
                              data?.get('with_breakdown').value ? (transforLayoutStatus(data?.get('layout_status').value) | noValue) : '-'
                            }}
                          </td>
                          <td *ngIf="columnIsDisplay('purchase_amount')" style="color: #138aff; border-right: none">
                            <nz-form-item *ngIf="!data?.get('with_breakdown')?.value && bomEditable; else purchaseAmountTmp">
                              <nz-form-control>
                                <nz-input-number
                                  class="purchase-amount"
                                  formControlName="purchase_amount"
                                  [nzPlaceHolder]="'请输入'"
                                  [nzMin]="0"
                                  [nzStep]="0.1"
                                  [nzPrecision]="1">
                                </nz-input-number>
                              </nz-form-control>
                            </nz-form-item>
                            <ng-template #purchaseAmountTmp>
                              <span (click)="purchaseClick(data)" class="purchase-num">
                                {{ data?.get('purchase_amount').value | noValue }}
                              </span>
                            </ng-template>
                          </td>
                          <td *ngIf="columnIsDisplay('unit_code')" style="border-right: none">
                            <nz-form-item *ngIf="bomEditable; else unitTpl">
                              <nz-form-control>
                                <nz-select
                                  formControlName="unit_code"
                                  [nzPlaceHolder]="'请选择单位'"
                                  nzAllowClear
                                  (ngModelChange)="unitChange($event, data)"
                                  nzShowSearch>
                                  <nz-option
                                    *ngFor="let unitItem of unitOption"
                                    [nzLabel]="unitItem.unit_name"
                                    [nzValue]="unitItem.unit_code">
                                  </nz-option>
                                </nz-select>
                              </nz-form-control>
                            </nz-form-item>
                            <ng-template #unitTpl>
                              {{ data?.get('unit_name').value | noValue }}
                            </ng-template>
                          </td>
                          <td
                            [rowSpan]="data?.get('remark').value || data?.get('add_remark').value ? 2 : 1"
                            nzRight
                            *ngIf="bomEditable"
                            style="border-bottom: 1px solid #d4d7dc">
                            <div class="op-td">
                              <ng-container>
                                <button
                                  nz-tooltip
                                  nzTooltipTitle="新增"
                                  nz-button
                                  nzType="link"
                                  (click)="onOpenMaterialSelector(data, i, '', ind)"
                                  flButton="link">
                                  <i nz-icon nzIconfont="icon-caozuolan-tianjia" style="font-size: 15px" class="icon iconfont"></i>
                                </button>
                                <button
                                  nz-tooltip
                                  nzTooltipTitle="复制本行数据"
                                  nz-button
                                  nzType="link"
                                  flButton="link"
                                  (click)="copyLine(data, i, ind, item.color_code)">
                                  <i nz-icon nzIconfont="icon-caozuolan_fuzhi" style="font-size: 15px" class="icon iconfont"></i>
                                </button>
                                <button
                                  nz-tooltip
                                  nzTooltipTitle="删除"
                                  nz-button
                                  nzType="link"
                                  flButton="link"
                                  class="error-color"
                                  nz-popconfirm
                                  [nzPopconfirmTitle]="'确定删除该条数据吗'"
                                  (nzOnConfirm)="deleteBom(i, ind)">
                                  <i
                                    nz-icon
                                    nzIconfont="icon-caozuolan_shanchu1"
                                    style="font-size: 15px"
                                    class="icon iconfont iconshanchu1 delete-hover"></i>
                                </button>
                              </ng-container>
                            </div>
                          </td>
                        </tr>
                        <tr *ngIf="data?.get('add_remark').value">
                          <td colSpan="12" style="padding-bottom: 8px; border-bottom: 1px solid #c6c6c6">
                            <nz-form-control *ngIf="data?.get('add_remark')?.value && bomEditable; else remarkTpl">
                              <nz-textarea-count class="bom-inline-count" [nzMaxCharacterCount]="60">
                                <textarea
                                  [nzSize]="'small'"
                                  rows="1"
                                  formControlName="remark"
                                  nz-input
                                  placeholder="请输入备注"
                                  nzAutosize
                                  [maxLength]="60"
                                  inputTrim></textarea>
                              </nz-textarea-count>
                            </nz-form-control>
                            <ng-template #remarkTpl>
                              <div
                                style="
                                  text-align: left;
                                  background: #f7f8fa;
                                  border-radius: 2px;
                                  height: 28px;
                                  color: #354060;
                                  font-size: 14px;
                                  padding-left: 6px;
                                  line-height: 28px;
                                ">
                                {{ data?.get('remark').value }}
                              </div>
                            </ng-template>
                          </td>
                        </tr>
                      </ng-container>
                    </tbody>
                  </nz-table>
                  <div *ngIf="getBomFormArray(i)?.length === 0" class="no-image">
                    <img src="../../../../../assets/image/no_chart_data.png" style="height: 100px" />
                    <div class="no-image-tip">暂无数据</div>
                    <div style="margin: 4px 16px 0 0" class="add-new" (click)="onOpenMaterialSelector(null, i, '', 0)" *ngIf="bomEditable">
                      <i nz-icon nzType="plus" nzTheme="outline"></i>新增
                    </div>
                  </div>
                </div>
              </div>
            </ng-container>
          </ng-container>
        </div>
      </form>
    </div>
  </div>
  <div
    class="discharge-right"
    #dischargeBox
    [style.width]="fullScreen ? '100%' : siderWidth + 'px'"
    [nzMinWidth]="0"
    nz-resizable
    (nzResize)="onSideResize($event)">
    <nz-resize-handle *ngIf="!fullScreen" nzDirection="left">
      <div class="line-wrap" nzTooltipTitle="长按这条线并左右拖拽 能调整可视范围哟～ 快来试试吧！👇" nz-tooltip>
        <div class="sider-resize-line"></div>
      </div>
    </nz-resize-handle>
    <div class="discharge-operation-header">
      <span class="left">排料操作区</span>
      <div class="right">
        <ng-container *ngIf="dischargeEditable; else dischargeEditTemp">
          <button
            nz-button
            [nzShape]="'round'"
            [nzSize]="'small'"
            nzType="primary"
            (click)="multiDisChargeLineCopy()"
            style="margin-right: 8px">
            复制
          </button>
          <button nz-button [nzShape]="'round'" [nzSize]="'small'" (click)="toggleDischargeEditState()" style="margin-right: 8px">
            取消
          </button>
          <button
            nz-button
            [nzShape]="'round'"
            [nzSize]="'small'"
            nzType="primary"
            (click)="disChargeSave()"
            [nzLoading]="saving"
            [disabled]="saveBtnDisable">
            提交
          </button>
        </ng-container>
        <ng-template #dischargeEditTemp>
          <button
            (click)="toggleDischargeEditState()"
            *ngIf="btnArr.includes('order:layout-update')"
            [nzShape]="'round'"
            nz-button
            [nzType]="'primary'"
            [nzSize]="'small'">
            <i nz-icon nzType="edit" nzTheme="outline"></i>编辑
          </button>
        </ng-template>
        <i
          nz-icon
          [nzIconfont]="fullScreen ? 'icon-shouqi-weixuanzhong' : 'icon-zhankai1'"
          nz-tooltip
          [nzTooltipTitle]="fullScreen ? '收起全屏' : '展开全屏'"
          class="zhankai-icon"
          (click)="onToggleFullScreen()"></i>
      </div>
    </div>
    <div class="discharge-operation-body" style="overflow: hidden">
      <form nz-form *ngIf="dischargeValidateForm" [formGroup]="dischargeValidateForm" style="height: 100%">
        <div class="table-scroll" style="height: 100%">
          <nz-table
            #dischargeTable
            *ngIf="formArrayCtrl.length > 0"
            style="margin-top: 4px"
            [nzData]="formArrayCtrl"
            [nzBordered]="false"
            [nzOuterBordered]="true"
            [nzFrontPagination]="false"
            [nzShowPagination]="false"
            [nzScroll]="{ x: '600px', y: this.dischargeBoxHeight + 'px' }">
            <thead>
              <tr>
                <th [nzAlign]="'center'" *ngIf="dischargeEditable" [nzWidth]="'12px'" nzLeft style="border-right: none"></th>
                <th [nzAlign]="'center'" *ngIf="dischargeEditable" [nzWidth]="'36px'" nzLeft style="border-right: none"></th>
                <th nzWidth="120px">
                  <span class="red-dot">关联颜色</span>
                </th>
                <th nzWidth="220px">
                  <span class="red-dot">面料</span>
                </th>
                <th
                  [nzWidth]="dischargeEditable ? '500px' : '400px'"
                  class="border-left"
                  [ngStyle]="{ padding: !dischargeEditable ? '0' : '' }">
                  <div style="display: flex; justify-content: space-between; align-items: center">
                    <span style="width: 88px">尺码</span>
                    <span style="width: 78px">配比</span>
                    <span [ngStyle]="{ width: dischargeEditable ? '160px' : '140px' }" style="text-align: left">部位</span>
                    <span style="width: 78px">件数</span>
                    <span style="width: 60px" *ngIf="dischargeEditable"></span>
                  </div>
                </th>
                <th nzWidth="78px" class="border-left">层数</th>
                <th nzWidth="78px">
                  毛门幅
                  <div style="font-size: 12px; font-weight: 500; color: #54607c; line-height: 17px">(厘米)</div>
                </th>
                <th nzWidth="78px">
                  <span>段长</span>
                  <div style="font-size: 12px; font-weight: 500; color: #54607c; line-height: 17px">(厘米)</div>
                </th>
                <th nzWidth="78px">
                  <span>段数</span>
                </th>
                <th nzWidth="120px">
                  <div>用料面积</div>
                  <div style="font-size: 12px; font-weight: 500; color: #54607c; line-height: 17px">(平方厘米)</div>
                </th>
                <th nzWidth="160px">
                  <div>每打用料</div>
                  <div style="font-size: 12px; color: #54607c; line-height: 17px; display: flex; justify-content: center">
                    （公斤<span style="color: #d4d7dc; margin: 0 4px">|</span>米）
                  </div>
                </th>
                <th nzWidth="78px">克重</th>
                <th nzWidth="78px">
                  <span>损耗</span>
                </th>
                <th nzWidth="100px" class="border-left" nzRight *ngIf="dischargeEditable">操作</th>
              </tr>
            </thead>
            <tbody formArrayName="discharge">
              <ng-container *ngFor="let data of formArrayCtrl; index as i" [formGroupName]="i">
                <tr
                  [ngClass]="{
                    'has-line': data?.get('with_divider').value,
                    'clear-border': data?.get('add_remark').value
                  }"
                  class="col-remark">
                  <td *ngIf="dischargeEditable" nzLeft class="border-bottom" style="border-right: none">
                    <ng-container *ngIf="i !== 0">
                      <i
                        *ngIf="!data?.get('with_divider').value"
                        (click)="isShowDivider(data)"
                        nz-icon
                        class="add-line"
                        nzType="plus-square"
                        nzTheme="fill"
                        [nzTooltipPlacement]="'topLeft'"
                        [nzTooltipTitle]="'点击添加分隔符标识'"
                        nz-tooltip
                        [ngStyle]="{ top: data?.get('with_divider').value ? '-7px' : '-6px' }"></i>
                      <i
                        *ngIf="data?.get('with_divider').value"
                        nz-icon
                        (click)="isShowDivider(data)"
                        class="del-line"
                        nzType="close-square"
                        nzTheme="fill"
                        [nzTooltipPlacement]="'topLeft'"
                        [nzTooltipTitle]="'点击删除分隔符标识'"
                        nz-tooltip
                        [ngStyle]="{ top: data?.get('with_divider').value ? '-7px' : '-6px' }"></i>
                    </ng-container>
                  </td>
                  <td *ngIf="dischargeEditable" nzLeft class="border-bottom">
                    <label nz-checkbox formControlName="checked"></label>
                  </td>
                  <td class="border-bottom position-remark">
                    <div
                      *ngIf="dischargeEditable"
                      [nzTooltipPlacement]="'topLeft'"
                      [nzTooltipTitle]="data?.get('add_remark').value ? '点击删除备注哦～' : '点击为本行数据添加备注'"
                      (click)="bomAddRemak(data)"
                      nz-tooltip>
                      <div *ngIf="!data?.get('add_remark').value" class="add-remark">
                        <i nz-icon nzIconfont="icon-caozuolan-tianjia" class="icon iconfont" style="color: #138aff"></i>
                      </div>
                      <div *ngIf="data?.get('add_remark').value" class="add-remark1">
                        <i nz-icon nzIconfont="icon-caozuolan_shanchu1" class="icon iconfont iconshanchu1 delete-hover"></i>
                      </div>
                    </div>
                    <nz-form-item *ngIf="dischargeEditable; else bulkColorTpl">
                      <nz-form-control>
                        <nz-select formControlName="color_code" [nzPlaceHolder]="'请选择'" (ngModelChange)="colorModelChange($event, data)">
                          <nz-option
                            *ngFor="let colorItem of baseInfo.color_list"
                            [nzLabel]="colorItem.color_name"
                            [nzValue]="colorItem.color_code">
                          </nz-option>
                        </nz-select>
                      </nz-form-control>
                    </nz-form-item>
                    <ng-template #bulkColorTpl>
                      <div class="discharge-no-edit">
                        {{ data?.get('color_name').value | noValue }}
                      </div>
                    </ng-template>
                  </td>
                  <td class="border-bottom">
                    <nz-form-item *ngIf="dischargeEditable; else matNameTpl">
                      <nz-form-control>
                        <nz-select
                          formControlName="combine_id"
                          [nzPlaceHolder]="'请选择'"
                          nzShowSearch
                          [nzDropdownMatchSelectWidth]="false"
                          [compareWith]="compareCombineOption"
                          (ngModelChange)="matModelChange($event, data)">
                          <nz-option
                            *ngFor="let matItem of combineOptions[data?.get('color_code').value]"
                            [nzLabel]="matItem.label"
                            [nzValue]="matItem.value">
                          </nz-option>
                        </nz-select>
                      </nz-form-control>
                    </nz-form-item>
                    <ng-template #matNameTpl>
                      <div class="discharge-no-edit">
                        {{
                          data?.get('material_name').value +
                            (data?.get('material_color_name').value?.length ? '-' + data?.get('material_color_name').value : '')
                        }}
                      </div>
                    </ng-template>
                  </td>
                  <ng-container formArrayName="specs">
                    <td class="border-bottom border-left" [ngStyle]="{ padding: !dischargeEditable ? '0' : '' }">
                      <div
                        *ngFor="let item of data?.get('specs').controls; index as j"
                        [formGroupName]="j"
                        style="display: flex; justify-content: space-between; align-items: center"
                        [ngClass]="{ 'border-bottom-d4d7dc': !dischargeEditable && j !== data?.get('specs').controls.length - 1 }">
                        <div style="width: 88px">
                          <nz-form-item *ngIf="dischargeEditable; else sizeNameTpl">
                            <nz-form-control>
                              <nz-select
                                formControlName="spec_code"
                                [nzPlaceHolder]="'请选择'"
                                (ngModelChange)="sizeModelChange($event, data, item)">
                                <nz-option
                                  *ngFor="let sizeItem of data?.get('spec_options').value"
                                  [nzLabel]="sizeItem.spec_name"
                                  [nzValue]="sizeItem.spec_name">
                                </nz-option>
                              </nz-select>
                            </nz-form-control>
                          </nz-form-item>
                          <ng-template #sizeNameTpl>
                            <div class="discharge-no-edit">
                              {{ item?.get('spec_name').value | noValue }}
                            </div>
                          </ng-template>
                        </div>
                        <div style="width: 78px">
                          <nz-form-item *ngIf="dischargeEditable; else sizeRatioTmp">
                            <nz-form-control>
                              <nz-input-number
                                formControlName="size_ratio"
                                [nzPlaceHolder]="'请输入'"
                                [nzMin]="0"
                                [nzStep]="1"
                                [nzPrecision]="0"
                                (nzBlur)="calulationLayersNumber(data, item)">
                              </nz-input-number>
                            </nz-form-control>
                          </nz-form-item>
                          <ng-template #sizeRatioTmp>
                            <div class="discharge-no-edit">
                              {{ item?.get('size_ratio').value | noValue }}
                            </div>
                          </ng-template>
                        </div>
                        <div [ngStyle]="{ width: dischargeEditable ? '160px' : '140px' }">
                          <nz-form-item *ngIf="dischargeEditable; else positionIdsTpl">
                            <nz-form-control>
                              <flc-dynamic-search-select
                                class="position-select1"
                                formControlName="position_ids"
                                [dataUrl]="'/service/archive/v1/part/basic_option'"
                                [defaultOptions]="item.get('position_ids_copy')?.value"
                                [optAlwaysReload]="true"
                                [cpnMode]="'tags'"
                                (ngModelChange)="positionChange($event, data, item)"
                                [column]="'position_name'">
                              </flc-dynamic-search-select>
                            </nz-form-control>
                            <!-- <nz-form-control>

                              <nz-select
                                formControlName="position_ids"
                                [nzPlaceHolder]="'请选择'"
                                nzMode="multiple"
                                class="position-select1"
                                (ngModelChange)="positionChange($event, data, item)">
                                <nz-option *ngFor="let item of positionOption" [nzLabel]="item.label" [nzValue]="item.value"> </nz-option>
                              </nz-select>
                            </nz-form-control> -->
                          </nz-form-item>
                          <ng-template #positionIdsTpl>
                            <div class="discharge-no-edit" style="text-align: left">
                              <ng-container *ngFor="let positionItem of item?.get('position_ids').value">
                                <span class="ioption" style="margin-right: 2px">
                                  {{ transforCodeToLabel(positionItem) | noValue }}
                                </span>
                              </ng-container>
                            </div>
                          </ng-template>
                        </div>
                        <div style="width: 78px">
                          <nz-form-item *ngIf="dischargeEditable; else qtyTmp">
                            <nz-form-control>
                              <nz-input-number
                                formControlName="done_qty"
                                [nzPlaceHolder]="'请输入'"
                                [nzMin]="0"
                                [nzStep]="1"
                                [nzPrecision]="0"
                                (nzBlur)="calulationLayersNumber(data, item)">
                              </nz-input-number>
                            </nz-form-control>
                          </nz-form-item>
                          <ng-template #qtyTmp>
                            <div class="discharge-no-edit">
                              {{ item?.get('done_qty').value | noValue }}
                            </div>
                          </ng-template>
                        </div>
                        <div *ngIf="dischargeEditable" style="width: 90px">
                          <i
                            nz-icon
                            nzType="minus-circle"
                            class="delete-icon"
                            nzTheme="fill"
                            nz-popconfirm
                            [nzPopconfirmTitle]="'确定删除该条数据吗'"
                            (nzOnConfirm)="delete(i, j)"></i>
                          <i nz-icon nzType="plus-circle" class="add-icon" nzTheme="fill" (click)="add(i)"></i>
                          <button
                            nz-tooltip
                            nzTooltipTitle="复制本行数据"
                            nz-button
                            nzType="link"
                            flButton="link"
                            (click)="copySizeLine(i, j)">
                            <i nz-icon nzIconfont="icon-caozuolan_fuzhi" style="font-size: 15px" class="icon iconfont"></i>
                          </button>
                        </div>
                      </div>
                    </td>
                  </ng-container>
                  <td class="border-bottom border-left">
                    <nz-form-item *ngIf="dischargeEditable; else layersNumberTmp">
                      <nz-form-control>
                        <nz-input-number
                          formControlName="layers_number"
                          [nzPlaceHolder]="'请输入'"
                          [nzMin]="0"
                          [nzStep]="1"
                          [nzPrecision]="0">
                        </nz-input-number>
                      </nz-form-control>
                    </nz-form-item>
                    <ng-template #layersNumberTmp>
                      <div class="discharge-no-edit">
                        {{ data?.get('layers_number').value | noValue }}
                      </div>
                    </ng-template>
                  </td>
                  <td class="border-bottom">
                    <nz-form-item *ngIf="dischargeEditable; else widthTmp">
                      <nz-form-control>
                        <nz-input-number
                          formControlName="width"
                          [nzPlaceHolder]="'请输入'"
                          (nzBlur)="caculationDozArea(data)"
                          [nzMin]="0"
                          [nzStep]="0.1"
                          [nzPrecision]="2">
                        </nz-input-number>
                      </nz-form-control>
                    </nz-form-item>
                    <ng-template #widthTmp>
                      <div class="discharge-no-edit">
                        {{ data?.get('width').value | noValue }}
                      </div>
                    </ng-template>
                  </td>
                  <td class="border-bottom">
                    <nz-form-item *ngIf="dischargeEditable; else segLenTmp">
                      <nz-form-control>
                        <nz-input-number
                          formControlName="seg_len"
                          [nzPlaceHolder]="'请输入'"
                          (nzBlur)="caculationDozArea(data)"
                          [nzMin]="0"
                          [nzStep]="0.1"
                          [nzPrecision]="3">
                        </nz-input-number>
                      </nz-form-control>
                    </nz-form-item>
                    <ng-template #segLenTmp>
                      <div class="discharge-no-edit">
                        {{ data?.get('seg_len').value | noValue }}
                      </div>
                    </ng-template>
                  </td>
                  <td class="border-bottom">
                    <nz-form-item *ngIf="dischargeEditable; else segQtyTmp">
                      <nz-form-control>
                        <nz-input-number
                          formControlName="seg_qty"
                          [nzPlaceHolder]="'请输入'"
                          (nzBlur)="caculationDozArea(data)"
                          [nzMin]="0"
                          [nzStep]="0.001"
                          [nzPrecision]="3">
                        </nz-input-number>
                      </nz-form-control>
                    </nz-form-item>
                    <ng-template #segQtyTmp>
                      <div class="discharge-no-edit">
                        {{ data?.get('seg_qty').value | noValue }}
                      </div>
                    </ng-template>
                  </td>
                  <td class="border-bottom">
                    <nz-form-item *ngIf="dischargeEditable; else dozAreaTpl">
                      <nz-form-control>
                        <nz-input-number
                          formControlName="doz_area"
                          [nzPlaceHolder]="'请输入'"
                          [nzMin]="0"
                          [nzStep]="0.001"
                          [nzPrecision]="3">
                        </nz-input-number>
                      </nz-form-control>
                    </nz-form-item>
                    <ng-template #dozAreaTpl>
                      <div class="discharge-no-edit">
                        {{ data?.get('doz_area').value | noValue }}
                      </div>
                    </ng-template>
                  </td>
                  <td class="border-bottom">
                    <nz-form-item *ngIf="dischargeEditable; else dozconsTpl">
                      <nz-form-control>
                        <nz-input-number-group [nzAddOnAfter]="getUnitNameDisplay(data)">
                          <nz-input-number
                            *ngIf="data?.get('unitCode').value === 'm'"
                            formControlName="doz_cons2"
                            [nzPlaceHolder]="'请输入'"
                            [nzMin]="0"
                            [nzStep]="0.01"
                            [nzPrecision]="2">
                          </nz-input-number>
                          <nz-input-number
                            *ngIf="data?.get('unitCode').value !== 'm'"
                            formControlName="doz_cons"
                            [nzPlaceHolder]="'请输入'"
                            [nzMin]="0"
                            [nzStep]="0.01"
                            [nzPrecision]="2">
                          </nz-input-number>
                        </nz-input-number-group>
                      </nz-form-control>
                    </nz-form-item>
                    <ng-template #dozconsTpl>
                      <div class="discharge-no-edit" *ngIf="data?.get('unitCode').value === 'm'">
                        {{ (data?.get('doz_cons2').value | noValue) + getUnitNameDisplay(data) }}
                      </div>
                      <div class="discharge-no-edit" *ngIf="data?.get('unitCode').value !== 'm'">
                        {{ (data?.get('doz_cons').value | noValue) + getUnitNameDisplay(data) }}
                      </div>
                    </ng-template>
                  </td>
                  <!-- <td class="border-bottom">
                    <div class="discharge-no-edit" style="display: flex; justify-content: center">
                      <span *ngIf="data?.get('unitCode').value !== 'm'">
                        {{ data?.get('doz_cons').value | noValue }}
                      </span>
                      <span style="margin: 0 4px; color: #d4d7dc; font-size: 12px">|</span>
                      <span *ngIf="data?.get('unitCode').value === 'm' || !unitCode"> {{ data?.get('doz_cons2').value | noValue }} </span>
                    </div>
                  </td> -->
                  <td class="border-bottom">
                    <nz-form-item *ngIf="dischargeEditable; else gsmTmp">
                      <nz-form-control>
                        <nz-input-number
                          formControlName="gsm"
                          (nzBlur)="caculationDozCons(data)"
                          [nzPlaceHolder]="'请输入'"
                          [nzMin]="0"
                          [nzStep]="1"
                          [nzPrecision]="0">
                        </nz-input-number>
                      </nz-form-control>
                    </nz-form-item>
                    <ng-template #gsmTmp>
                      <div class="discharge-no-edit">
                        {{ data?.get('gsm').value | noValue }}
                      </div>
                    </ng-template>
                  </td>
                  <td class="border-bottom">
                    <nz-form-item *ngIf="dischargeEditable; else lossMultiTmp">
                      <nz-form-control>
                        <nz-input-number
                          formControlName="loss_multi"
                          [nzPlaceHolder]="'请输入'"
                          (nzBlur)="caculationDozCons(data)"
                          [nzMin]="0.001"
                          [nzStep]="0.01"
                          [nzPrecision]="3">
                        </nz-input-number>
                      </nz-form-control>
                    </nz-form-item>
                    <ng-template #lossMultiTmp>
                      <div class="discharge-no-edit">
                        {{ data?.get('loss_multi').value | noValue }}
                      </div>
                    </ng-template>
                  </td>
                  <td
                    class="border-bottom border-left"
                    [rowSpan]="data?.get('remark').value || data?.get('add_remark').value ? 2 : 1"
                    *ngIf="dischargeEditable"
                    nzRight>
                    <div class="op-td">
                      <ng-container>
                        <button
                          nz-tooltip
                          nzTooltipTitle="新增"
                          nz-button
                          nz-button
                          nzType="link"
                          flButton="link"
                          (click)="addDischargeLine(i)">
                          <i nz-icon nzIconfont="icon-caozuolan-tianjia" style="font-size: 15px" class="icon iconfont"></i>
                        </button>
                        <button
                          nz-tooltip
                          nzTooltipTitle="复制本行数据"
                          nz-button
                          nzType="link"
                          flButton="link"
                          (click)="copyDischargeLine(data, i, $event)">
                          <i nz-icon nzIconfont="icon-caozuolan_fuzhi" style="font-size: 15px" class="icon iconfont"></i>
                        </button>
                        <button
                          nz-tooltip
                          nzTooltipTitle="删除"
                          nz-button
                          nzType="link"
                          flButton="link"
                          class="error-color"
                          nz-popconfirm
                          [nzPopconfirmTitle]="'确定删除该条数据吗'"
                          (nzOnConfirm)="deleteaddDischargeLine(i)">
                          <i
                            nz-icon
                            nzIconfont="icon-caozuolan_shanchu1"
                            style="font-size: 15px"
                            class="icon iconfont iconshanchu1 delete-hover"></i>
                        </button>
                      </ng-container>
                    </div>
                  </td>
                </tr>
                <tr *ngIf="data?.get('add_remark').value">
                  <td nzLeft *ngIf="dischargeEditable" style="border-bottom: 1px solid #c6c6c6"></td>
                  <td colSpan="11" style="padding-bottom: 8px; border-bottom: 1px solid #c6c6c6">
                    <nz-form-control *ngIf="data?.get('add_remark')?.value && dischargeEditable; else remarkTpl">
                      <nz-textarea-count class="bom-inline-count" [nzMaxCharacterCount]="200">
                        <textarea
                          [nzSize]="'small'"
                          rows="1"
                          formControlName="remark"
                          nz-input
                          placeholder="请输入备注"
                          nzAutosize
                          [maxLength]="200"
                          inputTrim></textarea>
                      </nz-textarea-count>
                    </nz-form-control>
                    <ng-template #remarkTpl>
                      <div
                        style="
                          text-align: left;
                          background: #f7f8fa;
                          border-radius: 2px;
                          height: 28px;
                          color: #354060;
                          font-size: 14px;
                          padding-left: 6px;
                          line-height: 28px;
                        ">
                        {{ data?.get('remark').value }}
                      </div>
                    </ng-template>
                  </td>
                </tr>
              </ng-container>
            </tbody>
          </nz-table>
          <div class="charge-no-image" [ngStyle]="{ height: this.dischargeBoxHeight + 'px' }" *ngIf="formArrayCtrl.length === 0">
            <img src="../../../../../assets/image/pl_no_data.png" style="height: 100px" />
            <div class="no-image-tip" style="margin-right: -8px">暂无数据</div>
            <div style="margin: 4px 0 0 10px" class="add-new" (click)="addDischargeLine()" *ngIf="dischargeEditable">
              <i nz-icon nzType="plus" nzTheme="outline"></i>新增
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

<!--  -->
<!-- 选择其他面料 -->
<!-- <app-checked-fabric
  *ngIf="showOtherMaterial"
  #otherMaterial
  [type]="1"
  [entry]="'bulkDataPackage'"
  [tableHeight]="400"
  (handleOtherMaterial)="handleOtherMaterial($event)"></app-checked-fabric> -->
<!-- <flss-material-package-bom-table #editTable [type]="1" [entry]="'bulkDataPackage'" [tableHeight]="400"> </flss-material-package-bom-table> -->

<!-- 复制其他颜色 -->
<nz-modal
  [(nzVisible)]="isVisibleColor"
  nzTitle="请选择其他颜色"
  (nzOnCancel)="handleColorCancel()"
  (nzOnOk)="handleColorOk()"
  [nzBodyStyle]="{ padding: '8px 16px' }"
  [nzWidth]="300">
  <ng-container *nzModalContent>
    <nz-radio-group [(ngModel)]="checkedColor" style="overflow: auto; max-height: 300px; width: 100%">
      <label nz-radio *ngFor="let color of colorTotalData" [nzValue]="color.colorCode" class="colorRadio">{{ color.colorName }}</label>
    </nz-radio-group>
  </ng-container>
</nz-modal>

<nz-modal
  [(nzVisible)]="isVisiblePurchaseNum"
  [nzStyle]="{ top: '100px' }"
  nzTitle="采购总量详情"
  (nzOnCancel)="handleCancel()"
  [nzBodyStyle]="{ padding: '8px 16px' }"
  [nzWidth]="1000"
  [nzContent]="modalContent"
  [nzFooter]="null">
  <ng-template #modalContent>
    <div style="padding: 13px 20px 11px; max-height: 500px; overflow-y: auto">
      <nz-table
        #purchaseNumTable
        [nzData]="baseData"
        [nzBordered]="true"
        [nzOuterBordered]="true"
        [nzFrontPagination]="false"
        [nzShowPagination]="false"
        [nzScroll]="{ x: '900px' }">
        <thead>
          <tr>
            <th nzWidth="100px">颜色</th>
            <th nzWidth="100px">部位</th>
            <th nzWidth="100px">物料名称</th>
            <th nzWidth="100px">物料颜色</th>
            <th nzWidth="80px">规格</th>
            <th nzWidth="120px">平均用料（打）</th>
            <th nzWidth="100px">单耗</th>
            <th nzWidth="100px">件数</th>
            <th nzWidth="100px">门幅</th>
            <th nzWidth="100px">数量</th>
            <th nzWidth="100px">段长</th>
            <th nzWidth="100px">数量</th>
            <th nzWidth="100px">采购总数量</th>
            <th nzWidth="100px">单位</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let item of purchaseNumTable.data">
            <ng-container *ngFor="let item1 of item.width_list; index as widthIndex">
              <ng-container *ngFor="let item2 of item1.seg_list; index as i">
                <tr>
                  <td *ngIf="widthIndex === 0 && i === 0" [rowSpan]="item.row">{{ item.material_color_name }}</td>
                  <td *ngIf="widthIndex === 0 && i === 0" [rowSpan]="item.row">
                    <ng-container *ngFor="let positionItem of item.position_list">
                      <span class="ioption" style="margin-right: 2px">
                        {{ positionItem | noValue }}
                      </span>
                    </ng-container>
                  </td>
                  <td *ngIf="widthIndex === 0 && i === 0" [rowSpan]="item.row">
                    {{ item.material_name }}
                  </td>
                  <td *ngIf="widthIndex === 0 && i === 0" [rowSpan]="item.row">
                    {{ item.material_color_name | noValue }}
                  </td>
                  <td *ngIf="widthIndex === 0 && i === 0" [rowSpan]="item.row">
                    {{ item.spec_name | noValue }}
                  </td>
                  <td *ngIf="widthIndex === 0 && i === 0" [rowSpan]="item.row">
                    {{ item.doz_cons }}
                  </td>
                  <td *ngIf="widthIndex === 0 && i === 0" [rowSpan]="item.row">
                    {{ item.piece_cons }}
                  </td>
                  <td *ngIf="widthIndex === 0 && i === 0" [rowSpan]="item.row">
                    {{ item.done_qty }}
                  </td>
                  <td *ngIf="i === 0" [rowSpan]="item1.seg_list.length">
                    {{ item1.width }}
                  </td>
                  <td *ngIf="i === 0" [rowSpan]="item1.seg_list.length">
                    {{ item1.purchase_amount }}
                  </td>
                  <td>{{ item2.seg }}</td>
                  <td>{{ item2.purchase_amount }}</td>
                  <td *ngIf="widthIndex === 0 && i === 0" [rowSpan]="item.row">{{ item.purchase_amount }}</td>
                  <td *ngIf="widthIndex === 0 && i === 0" [rowSpan]="item.row">{{ item.unit_name ? item.unit_name : '千克' }}</td>
                </tr>
              </ng-container>
            </ng-container>
          </ng-container>
        </tbody>
      </nz-table>
    </div>
  </ng-template>
</nz-modal>

<nz-modal
  [(nzVisible)]="isVisibleDistribution"
  nzTitle="分配算料员"
  (nzOnCancel)="isVisibleDistribution = false"
  (nzOnOk)="handleOkDistribution()">
  <ng-container *nzModalContent>
    <div>
      请分配算料员

      <flss-department-select
        [canSelectDepartment]="false"
        [treeOptions]="{ title: baseInfo?.io_info?.dist_user_label, id: baseInfo?.io_info?.dist_user_id }"
        [treeOptions]="deparmentOption"
        (onSelectChange)="_handleChangeValue($event)"
        [selectCurrentUser]="false">
      </flss-department-select>
    </div>
  </ng-container>
</nz-modal>
