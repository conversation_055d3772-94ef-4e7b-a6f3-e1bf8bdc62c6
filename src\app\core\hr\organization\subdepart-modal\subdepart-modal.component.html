<ng-template #titleTpl>
  <div class="subdepart-title">
    {{ 'subdepart-modal.新建子部门' | translate }}
  </div>
</ng-template>

<ng-template #contentTpl>
  <div class="subdepart-content">
    <div class="subdepart-notice" [innerHTML]="createNotice"></div>
    <div class="subdepart-msg">
      <form nz-form *ngIf="subDepartForm" [formGroup]="subDepartForm" class="content-form" style="width: 100%">
        <nz-form-item>
          <nz-form-label nzRequired>
            {{ 'subdepart-modal.部门名称' | translate }}
          </nz-form-label>
          <nz-form-control [nzErrorTip]="nameTpl">
            <nz-textarea-count [nzMaxCharacterCount]="16" class="inline-count">
              <textarea
                rows="1"
                nz-input
                formControlName="name"
                [placeholder]="placeInput"
                [maxLength]="16"
                nzAutosize
                inputTrim></textarea>
            </nz-textarea-count>
          </nz-form-control>
          <ng-template #nameTpl>
            <ng-container *ngIf="subDepartForm?.get('name')?.dirty && subDepartForm?.get('name')?.hasError('required')">
              {{ 'placeholder.input' | translate }} {{ 'subdepart-modal.部门名称' | translate }}
            </ng-container>
          </ng-template>
        </nz-form-item>
        <nz-form-item>
          <nz-form-label nzRequired>
            {{ 'subdepart-modal.部门编码' | translate }}
          </nz-form-label>
          <nz-form-control [nzErrorTip]="codeTpl">
            <nz-textarea-count [nzMaxCharacterCount]="20" class="inline-count">
              <textarea
                rows="1"
                nz-input
                formControlName="code"
                [placeholder]="placeInput"
                [maxLength]="20"
                nzAutosize
                inputTrim></textarea>
            </nz-textarea-count>
          </nz-form-control>
          <ng-template #codeTpl>
            <ng-container *ngIf="subDepartForm?.get('code')?.dirty && subDepartForm?.get('code')?.hasError('required')">
              {{ 'placeholder.input' | translate }} {{ 'subdepart-modal.部门编码' | translate }}
            </ng-container>
            <ng-container *ngIf="subDepartForm?.get('code')?.dirty && subDepartForm?.get('code')?.hasError('duplicated')">
              {{ 'subdepart-modal.部门编码' | translate }}{{ 'form-error.is-exit' | translate }}
            </ng-container>
            <ng-container *ngIf="subDepartForm?.get('code')?.pending">
              {{ 'subdepart-modal.部门编码' | translate }}{{ 'form-error.check-pending' | translate }}
            </ng-container>
            <ng-container *ngIf="subDepartForm.get('code')?.dirty && subDepartForm.get('code')?.hasError('pattern')">
              {{ 'form-error.special-chinese-characters' | translate }}
            </ng-container>
          </ng-template>
        </nz-form-item>
        <nz-form-item>
          <nz-form-label nzRequired>
            {{ 'subdepart-modal.状态' | translate }}
          </nz-form-label>
          <nz-form-control>
            <nz-switch formControlName="status"></nz-switch>
          </nz-form-control>
        </nz-form-item>
      </form>
    </div>
  </div>
</ng-template>

<ng-template #footerTpl>
  <div class="subdepart-footer">
    <button nz-button flButton="default" nzShape="round" (click)="handleCancel()">
      {{ 'btn.cancel' | translate }}
    </button>
    <button nz-button nzType="primary" nzShape="round" (click)="handleOk()">
      {{ 'btn.ok' | translate }}
    </button>
  </div>
</ng-template>
