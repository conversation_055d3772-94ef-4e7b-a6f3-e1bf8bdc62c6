<button
  *ngIf="isEdit"
  nz-button
  [nzShape]="'round'"
  flButton="default"
  [flcDisableOnClick]="1000"
  (click)="handleButtonAction(sampleOperateBtnEnum.cancel)">
  {{ 'btn.cancel' | translate }}
</button>

<button
  *ngIf="!isEdit"
  nz-button
  [nzShape]="'round'"
  flButton="default"
  [flcDisableOnClick]="1000"
  (click)="handleButtonAction(sampleOperateBtnEnum.back)">
  {{ 'btn.back' | translate }}
</button>

<button
  *ngIf="
    isEdit &&
    [sampleStatusEnum.wait_submit, sampleStatusEnum.new].includes(status) &&
    (permission.includes(permissionPrefix + 'create') || permission.includes(permissionPrefix + 'update'))
  "
  nz-button
  flButton="pretty-minor"
  [nzShape]="'round'"
  [flcDisableOnClick]="1000"
  flcErrorScroll
  (click)="handleButtonAction(sampleOperateBtnEnum.save)">
  {{ translateName + '暂存' | translate }}
</button>

<button
  *ngIf="
    isEdit &&
    [
      sampleStatusEnum.wait_submit,
      sampleStatusEnum.new,
      sampleStatusEnum.wait_modify,
      sampleStatusEnum.wait_order,
      sampleStatusEnum.wait_outsourcing_send
    ].includes(status) &&
    (permission.includes(permissionPrefix + 'create') || permission.includes(permissionPrefix + 'update'))
  "
  nz-button
  flButton="pretty-primary"
  [nzShape]="'round'"
  [flcDisableOnClick]="1000"
  flcErrorScroll
  (click)="handleButtonAction(sampleOperateBtnEnum.commit)">
  <i nz-icon [nzIconfont]="'icon-tijiao'"></i>
  {{ 'btn.commit' | translate }}
</button>

<ng-container *ngIf="[sampleStatusEnum.wait_audit].includes(status) && permission.includes(permissionPrefix + 'examine')">
  <button
    nz-button
    [nzShape]="'round'"
    flButton="fault-minor"
    [flcDisableOnClick]="1000"
    (click)="handleButtonAction(sampleOperateBtnEnum.modify)">
    {{ 'btn.modify' | translate }}
  </button>

  <button
    nz-button
    [nzShape]="'round'"
    flButton="pretty-primary"
    [flcDisableOnClick]="1000"
    (click)="handleButtonAction(sampleOperateBtnEnum.pass)">
    {{ 'btn.pass' | translate }}
  </button>
</ng-container>
<button
  *ngIf="
    !isEdit &&
    [sampleStatusEnum.wait_modify, sampleStatusEnum.wait_order].includes(status) &&
    permission.includes(permissionPrefix + 'cancel')
  "
  nz-button
  [nzShape]="'round'"
  flButton="default-negative-danger"
  nzDanger
  [flcDisableOnClick]="1000"
  (click)="handleButtonAction(sampleOperateBtnEnum.cancelSample)">
  {{ translateName + '取消外发' | translate }}
</button>

<button
  *ngIf="
    !isEdit &&
    [
      sampleStatusEnum.wait_submit,
      sampleStatusEnum.new,
      sampleStatusEnum.wait_modify,
      sampleStatusEnum.wait_order,
      sampleStatusEnum.wait_outsourcing_send
    ].includes(status) &&
    permission.includes(permissionPrefix + 'update')
  "
  nz-button
  flButton="pretty-primary"
  [nzShape]="'round'"
  [flcDisableOnClick]="1000"
  (click)="handleButtonAction(sampleOperateBtnEnum.edit)">
  <i nz-icon [nzIconfont]="'icon-caozuolan_bianji1'"></i>{{ 'btn.edit' | translate }}
</button>

<button
  *ngIf="!isEdit && [sampleStatusEnum.wait_outsourcing_sended].includes(status) && permission.includes(permissionPrefix + 'receive')"
  nz-button
  [nzShape]="'round'"
  flButton="pretty-primary"
  [flcDisableOnClick]="1000"
  (click)="handleButtonAction(sampleOperateBtnEnum.receive)">
  {{ translateName + '样衣收货' | translate }}
</button>
