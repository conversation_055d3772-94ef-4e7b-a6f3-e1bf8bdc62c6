import { Component, OnInit, Input } from '@angular/core';
import { ProductionTableService } from '../../production-table.service';
import { MaterialEnum, STATUSMAP } from '../../production-table.config';
import { format } from 'date-fns';
@Component({
  selector: 'app-material-dialog',
  templateUrl: './material-dialog.component.html',
  styleUrls: ['./material-dialog.component.scss'],
})
export class MaterialDialogComponent implements OnInit {
  @Input() bulkOrderId = null;
  @Input() type = 1;
  @Input() orderInfo: any;
  tableHeader = {
    1: {
      title: '采购单',
      date: '时间',
    },
    2: {
      title: '入库单',
      date: '入库时间',
    },
    3: {
      title: '出库单',
      date: '出库时间',
    },
  };

  get currentLabels() {
    return this.tableHeader[this.type];
  }
  titleConfig = [
    {
      key: 'material_name',
      label: '物料名称',
    },
    {
      key: 'supplier_name',
      label: '供应商',
    },
    {
      key: 'supplier_color_code',
      label: '物料颜色/色号',
    },
    {
      key: 'specification',
      label: '规格',
    },
  ];

  basicInfoConfig: any = [
    { label: '订单需求号', key: 'bulk_order_code' },
    { label: '款式编码', key: 'style_code' },
    { label: '加工厂', key: 'factory_name' },
  ];

  constructor(private _service: ProductionTableService) {}
  datalist = [];

  ngOnInit(): void {
    this.getMaterial();
  }
  getMaterial() {
    const data = {
      bulk_order_id: this.bulkOrderId,
      type: this.type,
    };
    this._service.getNodeDetail(data).subscribe((res: any) => {
      if (res.code === 200) {
        this.datalist = res.data.node_detail;
      }
    });
  }
  formateFinishTime(time: any) {
    return time > 0 ? format(new Date(time), 'yyyy-MM-dd') : null;
  }
  formateStatus(status: any) {
    return STATUSMAP[MaterialEnum[this.type]]?.[status] || '';
  }
}
