import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { PackageDetailComponent } from './package-detail/package-detail.component';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import { FlcComponentsModule, FlcPipesModule } from 'fl-common-lib';
import { FlButtonModule } from 'fl-ui-angular/button';

import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzSelectModule } from 'ng-zorro-antd/select';

import { SecProcessRoutingModule } from './sec-process-routing.module';
import { SecProcessListComponent } from './list/sec-process-list.component';
import { SecProcessTableComponent } from './components/sec-process-table/sec-process-table.component';
import { ProductionReportComponentsModule } from '../components/components.module';
import { SecProcessService } from './sec-process.service';

const NzModules = [
  NzButtonModule,
  NzToolTipModule,
  NzRadioModule,
  NzTabsModule,
  NzIconModule,
  NzSpinModule,
  NzEmptyModule,
  NzDatePickerModule,
  NzTableModule,
  NzDividerModule,
  NzModalModule,
  NzSelectModule,
];

@NgModule({
  declarations: [SecProcessListComponent, SecProcessTableComponent, PackageDetailComponent],
  imports: [
    CommonModule,
    SecProcessRoutingModule,
    FormsModule,
    ...NzModules,
    FlcComponentsModule,
    FlButtonModule,
    ProductionReportComponentsModule,
    FlcPipesModule,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new MultiTranslateHttpLoader(http, [{ prefix: './assets/i18n/production-report/sec-process/', suffix: '.json' }]);
        },
        deps: [HttpClient],
      },
    }),
  ],
  providers: [SecProcessService],
})
export class SecProcessModule {
  constructor(public translateService: TranslateService, private _service: SecProcessService) {
    const cl = translateService.currentLang;
    translateService.currentLang = '';
    translateService.use(cl).subscribe(() => {
      this._service.translateEventEmitter.next();
    });
  }
}
