import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ProductionProgressListComponent } from './list/production-progress-list.component';
const routes: Routes = [
  {
    path: 'list',
    component: ProductionProgressListComponent,
  },
  { path: '', redirectTo: 'list' },
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ProductionProgressRoutingModule {}
