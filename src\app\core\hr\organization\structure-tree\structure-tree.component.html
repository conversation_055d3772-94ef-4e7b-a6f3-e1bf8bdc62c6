<div class="structure-tree">
  <!-- 只读状态下才可搜索 -->
  <nz-select
    #selectRef
    [(ngModel)]="searchValue"
    [nzPlaceHolder]="searchHolder"
    [nzShowArrow]="false"
    [nzBorderless]="true"
    [nzDropdownMatchSelectWidth]="false"
    [nzDisabled]="_service.editable"
    [nzOptionOverflowSize]="8"
    [nzCustomTemplate]="defaultTemplate"
    (ngModelChange)="searchTree($event)"
    nzShowSearch>
    <ng-container *ngFor="let item of _service.selectOptions">
      <nz-option [nzValue]="item" [nzLabel]="item.name" [nzHide]="!item.parent_id && !item.is_employee" nzCustomContent>
        <div style="max-width: 240px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis">{{ item.label }}</div>
      </nz-option>
    </ng-container>
  </nz-select>
  <ng-template #defaultTemplate let-selected>
    {{ searchValue?.label }}
  </ng-template>
  <ng-template #searchHolder> <i nz-icon nzType="search"></i>{{ 'tree-search-locate-holder' | translate }} </ng-template>

  <div #treeWrapTpl style="overflow-y: auto; overflow-x: auto" [ngStyle]="{ 'max-height': treeMaxHeight - 32 - 12 - 16 + 'px' }">
    <nz-tree
      [nzData]="_service.nodes"
      [nzTreeTemplate]="nzTreeTemplate"
      [nzSelectedKeys]="_service.selectTree"
      [nzExpandedKeys]="_service.expandedTree"
      (nzExpandChange)="expandedChange($event)"
      (nzClick)="nzEvent($event)">
    </nz-tree>
    <ng-template #nzTreeTemplate let-node let-origin="origin">
      <span>
        <span *ngIf="node.level === 0" style="display: flex; align-items: center; gap: 5px">
          <span class="title-name">
            <app-text-ellipsis [qty]="12" [totalString]="node.title"></app-text-ellipsis>
            <ng-container *ngIf="!origin.is_employee">({{ origin.emp_count || 0 }}{{ 'unit.people' | translate }})</ng-container>
          </span>
          <i *ngIf="!origin.status" nz-icon nzType="stop" nzTheme="fill" class="stop-icon" nz-tooltip [nzTooltipTitle]="tooltipTpl"></i>
        </span>
        <span *ngIf="node.level !== 0" style="display: flex; align-items: center; gap: 5px">
          <span class="subtitle-name">
            <i *ngIf="origin.is_employee" nz-icon [nzIconfont]="'icon-yonghuming'" style="color: #e3e5eb"></i>
            <app-text-ellipsis [qty]="12 - node.level < 6 ? 6 : 12 - node.level" [totalString]="node.title"></app-text-ellipsis>
            <ng-container *ngIf="!origin.is_employee">({{ origin.emp_count || 0 }}{{ 'unit.people' | translate }})</ng-container>
          </span>
          <i
            *ngIf="!origin.status && !origin.is_employee"
            nz-icon
            nzType="stop"
            nzTheme="fill"
            class="stop-icon"
            nz-tooltip
            [nzTooltipTitle]="tooltipTpl"></i>
        </span>
        <ng-template #tooltipTpl>
          {{ 'depart-status.已停用' | translate }}
        </ng-template>
      </span>
    </ng-template>
    <div *ngIf="_service.selectOptions.length < 2" class="no-depart-container">
      <button
        *ngIf="!_service.editable && _service.btnArr.includes('settings:org-depart_create')"
        nz-button
        nzType="link"
        class="add-btn"
        (click)="toAddSubDepart()">
        <i nz-icon [nzIconfont]="'icon-zengjia1'"></i>{{ '去新建' | translate }}
      </button>
      <div class="no-depart-text">{{ 'no-subDepart' | translate }}</div>
    </div>
  </div>
</div>
<app-subdepart-modal></app-subdepart-modal>
