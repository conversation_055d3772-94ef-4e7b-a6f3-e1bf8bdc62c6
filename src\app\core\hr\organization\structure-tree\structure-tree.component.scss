.structure-tree {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;

  nz-select {
    flex: 1;
    border: none;
    background: #f7f8fa;
    box-shadow: 0px 1px 3px 0px rgba(235, 234, 247, 0.5);
    border-radius: 16px;
    margin: 0px 16px;

    .ant-select-selection-placeholder {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #84879a;
    }
  }

  nz-tree {
    flex: auto;
  }

  .title-name {
    font-size: 16px;
    font-weight: 500;
    color: #515661;
    white-space: nowrap;

    &:hover {
      color: #00af7f;
      background: transparent;
    }
  }

  .subtitle-name {
    font-size: 14px;
    font-weight: 500;
    color: #222b3c;
    white-space: nowrap;

    &:hover {
      color: #00af7f;
      background: transparent;
    }
  }

  .stop-icon {
    color: #e3e5eb;
  }

  ::ng-deep .ant-tree-switcher_open {
    color: #17b68a;
  }

  ::ng-deep .ant-tree .ant-tree-node-content-wrapper {
    position: relative;
    z-index: auto;
    min-height: 24px;
    margin: 0;
    padding: 0px;
    color: inherit;
    line-height: 24px;
    background: transparent;
    border-radius: 2px;
    cursor: pointer;
    transition: all 0.3s, border 0s, line-height 0s, box-shadow 0s;
  }

  ::ng-deep .ant-tree .ant-tree-node-content-wrapper:hover {
    background: transparent;
  }

  ::ng-deep .ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
    background-color: transparent;

    .title-name {
      color: #00af7f;
    }

    .subtitle-name {
      color: #00af7f;
    }
  }

  ::ng-deep .ant-tree-treenode-selected:not(:first-child) {
    background: rgba(242, 252, 249, 0.69);
    border-radius: 16px;
  }

  .no-depart-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 60vh;

    .add-btn {
      font-size: 14px;
      font-weight: 500;
      color: #00af7f;
    }

    .no-depart-text {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #84879a;
    }
  }

  ::ng-deep {
    .ant-tree-switcher-noop {
      width: 8px;
    }
  }
}
