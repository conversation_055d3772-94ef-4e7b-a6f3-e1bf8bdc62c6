<div class="TitleBar">
  <div class="title">
    <!-- <label (ngModelChange)="statusChange.emit($event)" [nzDisabled]="!isEdit" nz-checkbox [(ngModel)]="child.value">{{ child.name }}</label> -->
    <label>{{ child.name }}</label>
  </div>
  <div class="roleType">
    <ng-container *ngIf="child.action_type === 'read'">{{ translateName + '只读权限' | translate }}</ng-container>
    <ng-container *ngIf="child.action_type === 'write'">{{ translateName + '编辑权限' | translate }}</ng-container>
  </div>
</div>
<div class="buttonContent">
  <ng-container *ngIf="canConfigRange">
    <nz-radio-group [nzDisabled]="!isEdit" [(ngModel)]="child.payload.data_type" (ngModelChange)="typeChanged($event)">
      <label class="radioText" nz-radio [nzValue]="1">{{ translateName + '全部数据' | translate }}</label>
      <label class="radioText" nz-radio [nzValue]="2">{{ translateName + '仅本人' | translate }}</label>
      <label class="radioText" nz-radio [nzValue]="3">{{ translateName + '仅本部门及下级部门' | translate }}</label>
      <label class="radioText" nz-radio [nzValue]="4">
        <div>
          <span [ngClass]="{ errorText: isSaveError && child.payload.data_type === 4 && child.payload.data_range.length === 0 }">
            {{ translateName + '自定义部门' | translate }}
          </span>
          <nz-select
            nz-tooltip
            [nz-tooltip]="child.payload.data_range.length > 0 ? departmentList : null"
            *ngIf="!isEdit"
            [nzDisabled]="true"
            style="width: 250px"
            [(ngModel)]="child.payload.data_range"
            nzMode="multiple"
            [nzPlaceHolder]="'placeholder.select' | translate"
            [nzMaxTagCount]="3"
            nzAllowClear>
            <nz-option *ngFor="let item of allDepartmentList" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
          </nz-select>
          <nz-select
            *ngIf="isEdit"
            [nzDisabled]="false"
            style="width: 250px"
            [(ngModel)]="child.payload.data_range"
            nzMode="multiple"
            [nzPlaceHolder]="'placeholder.select' | translate"
            [nzMaxTagCount]="3"
            nzAllowClear>
            <nz-option *ngFor="let item of allDepartmentList" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
          </nz-select>
        </div>
      </label>
    </nz-radio-group>
  </ng-container>
  <ng-container *ngIf="!canConfigRange">
    <div [ngClass]="{ isDisabled: !isEdit }">
      <i style="color: #f49231" nz-icon nzType="info-circle" nzTheme="fill"></i> {{ translateName + '无数据范围可配置' | translate }}
    </div>
  </ng-container>
</div>

<ng-template #departmentList>
  <!-- <ng-container *ngFor="let department of allDepartmentList">
    <span style="margin: 4px 8px" *ngIf="item.payload.data_range.includes(department.id)">{{ department.name }}</span>
  </ng-container> -->
  <ng-container *ngFor="let deptId of child.payload.data_range">
    <span> {{ getDepartmentName(deptId) }} </span>
    <span *ngIf="deptId !== child.payload.data_range[child.payload.data_range.length - 1]" style="margin: 0 8px">|</span>
  </ng-container>
</ng-template>
