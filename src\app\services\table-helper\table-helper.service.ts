import { Injectable, Injector } from '@angular/core';
import { ConnectionPositionPair, Overlay } from '@angular/cdk/overlay';
import { ComponentPortal } from '@angular/cdk/portal';
import { TABLE_HEADER_DATA, TableHeaderMidifyDialogComponent } from './table-header-midify-dialog/table-header-midify-dialog.component';
import { Observable, Subject } from 'rxjs';
export interface TableHeaderConfigBase {
  label: string; // 列名
  visible: boolean; // 是否显示
  disable: boolean; // 是否不可被编辑(popup里)
  type: TableCompoentType; // 组件类型
  resizeble: boolean; // 是否可拖动
  width: string; // 宽度
  pinned: boolean; // 是否在左边固定
  template?: string; // 模板
  sort?: boolean; // 是否排序
}
export interface TableHeaderConfig<T> extends TableHeaderConfigBase {
  key: keyof T; // 对应的key
}
export type TableCompoentType = 'text' | 'price' | 'quantity' | 'image' | 'date' | 'datetime' | 'template';
@Injectable({
  providedIn: 'root',
})
export class TableHelperService {
  constructor(private overlayService: Overlay, private injector: Injector) {}
  /**
   *
   * @param headers 表头的list
   * @param defaultHeaders 默认的表头list
   * @param version 当前表格的版本(用以防止后期更新表格字段之后导致的无法获取到数据的问题)
   * @param tableName 如果一个路径的页面有多个表时用此来区分不同表格
   */
  public getTableHeaderConfig<T>(defaultHeaders: T[], defaultVersion: string, tableName?: string): T[] {
    const localHeaders = [];
    const path = tableName == null ? `${window.location.pathname}.table_config` : `${window.location.pathname}.${tableName}.table_config`;
    const localHeaderString = localStorage.getItem(path);
    if (localHeaderString) {
      const { headers, version } = JSON.parse(localHeaderString);
      if (version === defaultVersion) {
        localHeaders.push(...headers);
      } else {
        localHeaders.push(...defaultHeaders);
      }
    } else {
      localHeaders.push(...defaultHeaders);
    }
    return localHeaders;
  }
  public saveTableHeaderConfig(headers: TableHeaderConfigBase[], version: string, tableName?: string): void {
    if (tableName) {
      localStorage.setItem(`${window.location.pathname}.${tableName}.table_config`, JSON.stringify({ headers: headers, version: version }));
    } else {
      localStorage.setItem(`${window.location.pathname}.table_config`, JSON.stringify({ headers: headers, version: version }));
    }
  }
  /**
   * 获取表头宽度修改并保存至locolStorage
   * @param width NzResizeEvent 组件的拖动事件中的宽度
   * @param col 表头的字段名字
   * @param headers 表头的list
   * @param version 当前表格的版本(用以防止后期更新表格字段之后导致的无法获取到数据的问题)
   * @param tableName 如果一个路径的页面有多个表时用此来区分不同表格
   */
  public tableResize<T extends TableHeaderConfigBase>(
    width: number | undefined,
    col: string,
    headers: T[],
    version: string,
    tableName?: string
  ): T[] {
    headers = headers.map((e) => (e.label === col ? { ...e, width: `${width ?? 10}px` } : e));
    this.saveTableHeaderConfig(headers, version, tableName);
    return headers;
  }
  /**
   * 打开公用的修改表头显示/拖动/固定弹窗
   * @param tableHeaderList 表头的所有数据
   * @param targetElement 点击的元素
   * @returns 返回一个Observable 可以监听到返回修改后的表头 如果返回是error则表示是点击了空白区域
   */
  public openTableHeaderMidifyDialog<T extends TableHeaderConfigBase>(tableHeaderList: T[], targetElement: HTMLElement): Observable<T[]> {
    const subject = new Subject<T[]>();
    const injector = Injector.create({ providers: [{ provide: TABLE_HEADER_DATA, useValue: tableHeaderList }], parent: this.injector });
    const overlayRef = this.overlayService.create({
      hasBackdrop: true,
      backdropClass: 'cdk-overlay-transparent-backdrop',
      maxWidth: '216px',
      minWidth: '100px',
      positionStrategy: this.overlayService
        .position()
        .flexibleConnectedTo(targetElement)
        .withPositions([new ConnectionPositionPair({ originX: 'start', originY: 'center' }, { overlayX: 'end', overlayY: 'center' })]),
      disposeOnNavigation: true,
    });
    const componetRef = overlayRef.attach(new ComponentPortal(TableHeaderMidifyDialogComponent, null, injector));
    componetRef.instance.onClose.subscribe((data: T[]) => {
      subject.next(data);
      subject.complete();
      overlayRef.dispose();
    });
    overlayRef.backdropClick().subscribe(() => {
      subject.error('error: click backdrop');
      overlayRef.dispose();
    });
    return subject.asObservable();
  }
  public getRenderHeader<T extends TableHeaderConfigBase>(headers: T[]): T[] {
    const shadowList: T[] = JSON.parse(JSON.stringify(headers));
    const pinnedList: T[] = [];
    const normalList: T[] = [];
    shadowList.forEach((item) => {
      if (item.disable) {
        pinnedList.push(item);
      } else if (item.pinned) {
        pinnedList.push(item);
      } else {
        normalList.push(item);
      }
    });
    return [...pinnedList, ...normalList];
  }
}
