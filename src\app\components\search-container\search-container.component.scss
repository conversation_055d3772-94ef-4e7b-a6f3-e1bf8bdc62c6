.search-container {
  display: flex;
  justify-content: space-between;
  align-items: baseline;

  .search-content {
    display: flex;
    flex-wrap: wrap;
    gap: 10px 24px;

    .search-title {
      font-size: 16px;
      font-weight: 500;
      color: #515661;
    }
  }

  .btn-container {
    display: flex;
    align-items: center;
    gap: 8px;

    .reset-btn {
      background: #ffffff;
      box-shadow: 0px 0px 4px 0px rgba(227, 236, 250, 0.5);
      border-radius: 8px;
      color: #5d6883;
      border: none;

      &:hover {
        background: #ebecf0;
        box-shadow: 0px 0px 4px 0px rgba(227, 236, 250, 0.5);
        border-radius: 8px;
        border: none;
      }
    }

    .filter-btn {
      background: #ffffff;
      box-shadow: 0px 0px 4px 0px rgba(227, 236, 250, 0.5);
      border-radius: 8px;
      color: #5d6883;
      border: none;
    }

    .filter-btn-active {
      background: #ffffff;
      box-shadow: 0px 0px 4px 0px rgba(227, 236, 250, 0.5);
      border-radius: 8px;
      border: 1px solid #00c790;
      color: #00c790;
    }

    .filter-btn:hover,
    .filter-btn-active:hover {
      background: rgba(0, 199, 144, 0.08);
      box-shadow: 0px 0px 4px 0px rgba(227, 236, 250, 0.5);
      border-radius: 8px;
      border: 1px solid #2fd2a5;
      color: #2fd2a5;
    }
  }
}
