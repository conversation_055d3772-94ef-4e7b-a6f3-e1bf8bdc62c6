<div class="wrap">
  <div class="roleWrap" *ngIf="!isFullMode">
    <div class="roleNameArea">
      <div class="roleNameTitleBar">
        <span>{{ translateName + '权限名称' | translate }}</span>
        <div class="roleNameSearchBar">
          <nz-select
            nzShowSearch
            [nzPlaceHolder]="translateName + '输入权限名称搜索' | translate"
            nzAllowClear
            [(ngModel)]="roleSearchInput"
            (ngModelChange)="roleNameSearchChange($event)">
            <nz-option *ngFor="let item of searchRoleNameList!" [nzLabel]="item.name" [nzValue]="item.name"></nz-option>
          </nz-select>
          <!-- <nz-input-group [nzPrefix]="prefixTemplateUser" [nzSuffix]="inputClearTpl">
            <input
              type="text"
              nz-input
              [(ngModel)]="roleSearchInput"
              (ngModelChange)="roleNameSearchChange($event)"
              (keyup.enter)="searchRole()"
              id="name"
              name="name"
              placeholder="输入权限名称搜索" />
          </nz-input-group>
          <ng-template #inputClearTpl>
            <i nz-icon class="ant-input-clear-icon" nzTheme="fill" nzType="close-circle" *ngIf="roleSearchInput" (click)="searchRole(true)">
            </i>
          </ng-template> -->
        </div>
      </div>
      <div
        [sortablejsOptions]="{ disabled: !canDrag || !isEdit, group: { name: 'role', pull: 'clone', put: false }, sort: false }"
        [sortablejs]="roleNameList"
        class="roleNameListWrap"
        [ngClass]="{ spinning: roleNameList === undefined, empty: roleNameList?.length === 0 }">
        <ng-container *ngIf="roleNameList === undefined">
          <nz-spin nzSimple [nzTip]="'加载中...'" [nzSpinning]="true"></nz-spin>
        </ng-container>
        <ng-container *ngIf="roleNameList?.length === 0"> {{ translateName + '暂无数据,请添加权限' | translate }} </ng-container>
        <div
          class="roleNameItem"
          appThrottleClick
          [debounceTime]="300"
          (debounceClick)="selectedRoleName(item.id)"
          *ngFor="let item of roleNameList"
          [ngClass]="{ selected: item.id === selectedRoleNameDetail?.id, edit: isEdit }">
          <span>
            <app-text-truncated [template]="roleNameTpl"></app-text-truncated>
            <ng-template #roleNameTpl>{{ item.name }}</ng-template>
          </span>
        </div>
      </div>
    </div>
    <div class="roleNameDetail">
      <app-assign-role-detail
        [allDepartmentList]="flatDepartmentList"
        [selectedItem]="selectedRoleNameDetail"
        *ngIf="selectedRoleNameDetail"></app-assign-role-detail>
      <ng-container *ngIf="selectedRoleNameDetail === undefined">
        <div class="roleNameDetailFlexWrap">
          <div style="visibility: hidden" class="roleNameTitleBar">
            <span>{{ translateName + '权限名称' | translate }}</span>
            <div class="roleNameSearchBar">
              <nz-select
                nzShowSearch
                [nzPlaceHolder]="translateName + '输入权限名称搜索' | translate"
                nzAllowClear
                [(ngModel)]="roleSearchInput"
                (ngModelChange)="roleNameSearchChange($event)">
                <nz-option *ngFor="let item of searchRoleNameList!" [nzLabel]="item.name" [nzValue]="item.name"></nz-option>
              </nz-select>
            </div>
          </div>
          <div style="flex-grow: 1" [ngClass]="{ spinning: selectedRoleNameDetail === undefined }">
            <nz-spin nzSimple [nzTip]="translateName + '加载中...' | translate" [nzSpinning]="true"></nz-spin>
          </div>
        </div>
      </ng-container>
      <ng-container *ngIf="selectedRoleNameDetail === null">
        <div class="roleNameDetailFlexWrap">
          <div style="visibility: hidden" class="roleNameTitleBar">
            <span>{{ translateName + '权限名称' | translate }}</span>
            <div class="roleNameSearchBar">
              <nz-select
                nzShowSearch
                [nzPlaceHolder]="translateName + '输入权限名称搜索' | translate"
                nzAllowClear
                [(ngModel)]="roleSearchInput"
                (ngModelChange)="roleNameSearchChange($event)">
                <nz-option *ngFor="let item of searchRoleNameList!" [nzLabel]="item.name" [nzValue]="item.name"></nz-option>
              </nz-select>
            </div>
          </div>
          <div style="flex-grow: 1" [ngClass]="{ empty: selectedRoleNameDetail === null }">
            <span>{{ translateName + '暂无数据,请添加权限' | translate }}</span>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
  <div class="companyContent" [ngClass]="{ spinning: !companyInfo }">
    <ng-container *ngIf="!companyInfo">
      <nz-spin nzSimple [nzTip]="translateName + '加载中...' | translate" [nzSpinning]="true"></nz-spin>
    </ng-container>
    <ng-container *ngIf="companyInfo">
      <div class="companyInfo">
        <div class="searchBar">
          <span> {{ companyInfo?.name }}</span>
          <nz-input-group [nzPrefix]="prefixTemplateUser" [nzSuffix]="companyInputClearTpl">
            <input
              #departmentSearchInput
              nz-input
              [(ngModel)]="searchValue"
              [placeholder]="translateName + '输入部门或员工搜索' | translate"
              (input)="searchInput($event)"
              [nzAutocomplete]="autoComplete" />
          </nz-input-group>
          <nz-autocomplete #autoComplete (selectionChange)="locateIt($event.nzValue)">
            <nz-auto-option
              *ngFor="let item of searchKeywordOptions"
              [nzValue]="item"
              [nzLabel]="getRealName(item)"
              nz-tooltip
              [nzTooltipTitle]="getSearchName(item)">
              <div>
                <ng-container *ngTemplateOutlet="searchTpl; context: { $implicit: item }"></ng-container>
                <ng-template #searchTpl let-item
                  >{{ item.name
                  }}<ng-container *ngIf="!item.is_leaf"
                    >/<ng-container *ngTemplateOutlet="searchTpl; context: { $implicit: item.child }"></ng-container>
                  </ng-container>
                </ng-template>
              </div>
            </nz-auto-option>
          </nz-autocomplete>
          <ng-template #companyInputClearTpl>
            <i
              nz-icon
              class="ant-input-clear-icon"
              nzTheme="fill"
              nzType="close-circle"
              *ngIf="searchValue"
              (click)="searchValue = undefined"></i>
          </ng-template>
        </div>
        <div class="btnBar">
          <ng-container *ngIf="isEdit">
            <button nz-button nzShape="round" (click)="toggleEdit(false)">{{ 'btn.cancel' | translate }}</button>
            <button nz-button nzType="primary" (click)="save()" nzShape="round">
              <i nz-icon [nzIconfont]="'icon-baocun'"></i> {{ 'btn.save' | translate }}
            </button>
          </ng-container>
          <ng-container *ngIf="!isEdit && canDrag">
            <button nz-button nzType="primary" nzShape="round" (click)="toggleEdit(true)">{{ 'btn.edit' | translate }}</button>
          </ng-container>
          <a
            nz-button
            nzShape="circle"
            (click)="toggleFullMode()"
            nzType="text"
            style="display: flex; align-items: center; justify-content: center">
            <!-- <i nz-icon nzType="fullscreen" nzTheme="outline"></i> -->
            <i *ngIf="!isFullMode" nz-icon [nzIconfont]="'icon-zhankai1'"></i>
            <i *ngIf="isFullMode" nz-icon [nzIconfont]="'icon-shouqi-weixuanzhong'"></i>
          </a>
        </div>
      </div>
      <div style="flex-grow: 1; overflow: hidden; display: flex" [ngClass]="{ spinning: departmentTreeList === undefined }">
        <ng-container *ngIf="departmentTreeList === undefined">
          <nz-spin nzSimple [nzTip]="translateName + '加载中...' | translate" [nzSpinning]="true"></nz-spin>
        </ng-container>
        <ng-container *ngIf="departmentTreeList">
          <!-- <div class="departmentBoard">
            <div class="directlyEmployeeWrap" [ngClass]="{ edit: isEdit }" *ngIf="topEmployees && topEmployees!.employees.length > 0">
              <div class="directlyEmployee" [ngClass]="{ edit: isEdit, selected: showTopEmployee }" (click)="selectTopEmployee()">
                <div class="titleLine">直属员工({{ topEmployees!.employees.length }}人)</div>
                <div
                  [sortablejs]="topEmployees!.roles"
                  [sortablejsOptions]="{
                    disabled: !canDrag || !isEdit,
                    handle: '.dragHandle',
                    sort: false,
                    group: 'role',
                    onAdd: departmentRoleOnAdd(topEmployees)
                  }"
                  class="departmentRoleBoard"
                  *ngIf="showTopEmployee">
                  <div class="RoleLine" *ngFor="let role of topEmployees?.roles">
                    <span>{{ role.role_name }}</span>
                    <i *ngIf="isEdit" nz-icon nzType="close-circle" nzTheme="fill" (click)="showDepartmentRoleRemoveModal(topEmployees, role)"></i>
                  </div>
                </div>
              </div>
            </div>
            <div #departmentListWrap>
              <div
                class="departmentItem"
                [ngClass]="{ edit: isEdit, selected: item.isSelected }"
                *ngFor="let item of departmentTreeList!"
                (click)="selectDepartment(item, true)">
                <div class="titleLine">
                  <div class="title">
                    <span>
                      <app-text-truncated [template]="departmentTitle"></app-text-truncated>
                      <ng-template #departmentTitle>{{ item.name }}</ng-template>
                    </span>
                    <div>({{ item.count }}人)</div>
                  </div>
                  <i nz-icon nzType="right" nzTheme="outline"></i>
                </div>
                <div
                  [sortablejs]="item.roles"
                  [sortablejsOptions]="{
                    disabled: !canDrag || !isEdit,
                    handle: '.dragHandle',
                    sort: false,
                    group: 'role',
                    onAdd: departmentRoleOnAdd(item)
                  }"
                  class="departmentRoleBoard"
                  *ngIf="item.isSelected && item.isLeaf">
                  <div class="RoleLine" *ngFor="let role of item.roles">
                    <span>{{ role.role_name }}</span>
                    <i *ngIf="isEdit" nz-icon nzType="close-circle" nzTheme="fill" (click)="showDepartmentRoleRemoveModal(item, role)"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <ng-container *ngIf="showTopEmployee">
            <div #employeeBoard class="employeeBoard">
              <div class="employeeItem" *ngFor="let item of topEmployees!.employees">
                <div class="employeeName">
                  <span>
                    {{ item.name }}
                  </span>
                  <ng-container *ngIf="!item.user_id">
                    <i
                      nz-tooltip="无操作帐号, 不可分配权限"
                      nz-icon
                      nzType="exclamation-circle"
                      nzTheme="fill"
                      style="margin-left: 8px; color: #f49231"></i>
                  </ng-container>
                </div>
                <div
                  class="employeeRole"
                  [sortablejs]="item.roles"
                  [sortablejsOptions]="{
                    handle: '.dragHandle',
                    group: 'role',
                    disabled: !canDrag || (!isEdit && !item.user_id),
                    onAdd: employeeRoleOnAdd(item),
                    onUpdate: employeeRoleDrop(item)
                  }">
                  <div *ngFor="let role of item?.roles; let index = index" class="employeeRoleItem">
                    <div class="rolePriority" *ngIf="item.showIndex">{{ role.priority }}</div>
                    <div class="roleName" [ngClass]="{ alone: !item.showIndex }">
                      <span>{{ role.role_name }}</span>
                      <i *ngIf="isEdit" nz-icon nzType="close-circle" nzTheme="fill" (click)="employeeRoleRemove(item, index)"></i>
                    </div>
                    <div class="dragHandle" *ngIf="item.showIndex && isEdit">
                      <i nz-icon nzType="drag" nzTheme="outline"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ng-container>
          <ng-container *ngIf="!showTopEmployee && selectedDepartment">
            <ng-container *ngTemplateOutlet="subDepartmentBoard; context: { $implicit: selectedDepartment }"></ng-container>
          </ng-container> -->

          <ng-container *ngIf="departmentTreeList!.length === 0">
            <div class="departmentListEmpty">
              <span><img style="width: 200px" src="../../../../assets/image/role_empty.png" alt="RoleDetailEmpty" /></span>
              <span>{{ translateName + '暂无部门与人员哟～' | translate }}</span>
            </div>
          </ng-container>
          <ng-container *ngIf="departmentTreeList!.length > 0">
            <div #departmentWrap class="departmentWrap">
              <ng-container *ngTemplateOutlet="subDepartmentBoard; context: { $implicit: topEmployees }"></ng-container>
            </div>
            <ng-container *ngIf="selectedShowEmployeesDepartment">
              <ng-container *ngIf="(selectedShowEmployeesDepartment?.employees || []).length === 0">
                <div class="employeeListEmpty">
                  <span><img style="width: 200px" src="../../../../assets/image/role_empty.png" alt="RoleDetailEmpty" /></span>
                  <span>{{ translateName + '暂无员工哟～' | translate }}</span>
                </div>
              </ng-container>
              <ng-container *ngIf="(selectedShowEmployeesDepartment?.employees || []).length > 0">
                <ng-container
                  *ngTemplateOutlet="departmentEmployeeBoard; context: { $implicit: selectedShowEmployeesDepartment?.employees }">
                </ng-container>
              </ng-container>
            </ng-container>
          </ng-container>
        </ng-container>
      </div>
    </ng-container>
  </div>
</div>

<ng-template #prefixTemplateUser> <i nz-icon [nzIconfont]="'icon-sousuoxiao'" style="color: #595959"></i></ng-template>
<ng-template #subDepartmentBoard let-selectedDepartment>
  <ng-container *ngIf="selectedDepartment && !selectedDepartment.isLeaf">
    <div class="departmentBoard">
      <div *ngIf="selectedDepartment && selectedDepartment.employees.length > 0" class="directlyEmployeeWrap" [ngClass]="{ edit: isEdit }">
        <div
          class="directlyEmployee"
          [ngClass]="{ edit: isEdit, selected: selectedDepartment?.showEmployee }"
          (click)="selectDepartmentEmployee(selectedDepartment)">
          <div class="titleLine">
            {{ translateName + '直属员工' | translate }}({{ selectedDepartment!.employees.length }}{{ translateName + '人' | translate }})
          </div>
          <div
            [sortablejs]="selectedDepartment!.roles"
            [sortablejsOptions]="{
              disabled: !canDrag || !isEdit,
              handle: '.dragHandle',
              sort: false,
              group: 'role',
              onAdd: departmentRoleOnAdd(selectedDepartment)
            }"
            class="departmentRoleBoard"
            *ngIf="selectedDepartment.showEmployee">
            <div class="RoleLine" *ngFor="let role of selectedDepartment!.roles">
              <span><app-text-truncated [template]="roleNameTpl"></app-text-truncated></span>
              <ng-template #roleNameTpl>{{ role.role_name }}</ng-template>
              <i
                *ngIf="isEdit"
                nz-icon
                nzType="close-circle"
                nzTheme="fill"
                (click)="showDepartmentRoleRemoveModal(selectedDepartment, role)"></i>
            </div>
          </div>
        </div>
      </div>
      <div #departmentListWrap *ngIf="selectedDepartment">
        <div
          class="departmentItem"
          [ngClass]="{ edit: isEdit, selected: item.isSelected }"
          *ngFor="let item of selectedDepartment.children">
          <div class="titleLine" (click)="selectDepartment(item)">
            <div class="title">
              <span>
                <app-text-truncated [template]="departmentTitle"></app-text-truncated>
                <ng-template #departmentTitle>{{ item.name }}</ng-template>
              </span>
              <div>({{ item.count }}{{ translateName + '人' | translate }})</div>
            </div>
            <i nz-icon nzType="right" nzTheme="outline"></i>
          </div>
          <div
            [sortablejs]="item.roles"
            [sortablejsOptions]="{
              disabled: !canDrag || !isEdit,
              handle: '.dragHandle',
              sort: false,
              group: 'role',
              onAdd: departmentRoleOnAdd(item)
            }"
            class="departmentRoleBoard"
            *ngIf="item.isSelected && item.isLeaf">
            <div class="RoleLine" *ngFor="let role of item?.roles">
              <span><app-text-truncated [template]="roleNameTpl"></app-text-truncated></span>
              <ng-template #roleNameTpl>{{ role.role_name }}</ng-template>
              <i *ngIf="isEdit" nz-icon nzType="close-circle" nzTheme="fill" (click)="showDepartmentRoleRemoveModal(item, role)"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-container>
  <ng-container *ngIf="selectedDepartment">
    <ng-container *ngIf="selectedDepartment.showEmployee">
      <!-- <ng-container *ngTemplateOutlet="departmentEmployeeBoard; context: { $implicit: selectedDepartment.employees }"></ng-container> -->
    </ng-container>
    <ng-container *ngIf="!selectedDepartment.showEmployee">
      <ng-container *ngIf="!selectedDepartment.isLeaf">
        <ng-container *ngTemplateOutlet="subDepartmentBoard; context: { $implicit: selectedDepartment.seletedChild }"></ng-container>
      </ng-container>
      <ng-container *ngIf="selectedDepartment.isLeaf">
        <!-- <ng-container *ngTemplateOutlet="departmentEmployeeBoard; context: { $implicit: selectedDepartment.employees }"></ng-container> -->
      </ng-container>
    </ng-container>
  </ng-container>
</ng-template>
<ng-template #departmentEmployeeBoard let-employees>
  <div #employeeBoard class="employeeBoard">
    <div class="employeeItem" *ngFor="let item of employees">
      <div class="employeeName">
        <span>
          {{ item.name }}
        </span>
        <ng-container *ngIf="item.user_id === null || item.user_id === undefined">
          <i
            nz-tooltip
            [nzTooltipTitle]="toolTipTemplate"
            nz-icon
            nzType="exclamation-circle"
            nzTheme="fill"
            style="margin-left: 8px; color: #f49231"></i>

          <ng-template #toolTipTemplate>
            <span class="tooltip-text"
              >{{ translateName + '无操作帐号, 不可分配权限' | translate }},
              <a class="link" (click)="onConfigAccount(item)">{{ translateName + '去配置账号' | translate }}</a></span
            >
          </ng-template>
        </ng-container>
      </div>
      <div
        class="employeeRole"
        [sortablejs]="item.roles"
        [sortablejsOptions]="{
          handle: '.dragHandle',
          group: 'role',
          disabled: !canDrag || (!isEdit && !item.user_id),
          onAdd: employeeRoleOnAdd(item),
          onUpdate: employeeRoleDrop(item)
        }">
        <div *ngFor="let role of item?.roles; let index = index" class="employeeRoleItem">
          <div class="rolePriority" *ngIf="item.showIndex">{{ role.priority }}</div>
          <div class="roleName" [ngClass]="{ alone: !item.showIndex }">
            <span><app-text-truncated [template]="roleNameTpl"></app-text-truncated></span>
            <ng-template #roleNameTpl>{{ role.role_name }}</ng-template>
            <!-- <span>{{ role.role_name }}</span> -->
            <i
              *ngIf="isEdit"
              nz-icon
              nzType="close-circle"
              nzTheme="fill"
              nz-popconfirm
              [nzPopconfirmTitle]="translateName + '确定删除该权限吗？' | translate"
              nzPopconfirmPlacement="top"
              [nzOkType]="'danger'"
              (nzOnConfirm)="employeeRoleRemove(item, index)"></i>
          </div>
          <div class="dragHandle" *ngIf="item.showIndex && isEdit">
            <i nz-icon nzType="drag" nzTheme="outline"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #deleteHeadTemplate>
  <div
    style="
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 18px;
      font-weight: 500;
      color: #54607c;
      line-height: 24px;
    ">
    {{ 'btn.delete' | translate }}
  </div>
</ng-template>
<ng-template #deleteTemplate>
  <div>
    <div>
      <div style="border-radius: 0 0 8px 8px; background-color: #f4f7f9; display: flex; justify-content: center; align-items: center">
        {{ translateName + '该权限名称有部门和员工被分配，删后不可用' | translate }}
      </div>
      <div>
        <div style="height: 72px; display: flex; justify-content: center; align-items: center">
          <i nz-icon [nzIconfont]="'icon-jinggao'" style="color: #ff5c33"></i>
          <div class="modalContent">
            {{ translateName + '确定移除' | translate }}
            <span style="color: #138aff">{{ deleteRoleName!.role_name }}</span>
            ？
          </div>
          <!-- <div class="modalContent">{{ '确定删除权限名称？' | translate }}</div> -->
        </div>
        <div style="display: flex; justify-content: center; align-items: center; column-gap: 8px">
          <button nz-button nzShape="round" nzType="default" (click)="deleteModal!.close()">{{ 'btn.cancel' | translate }}</button>
          <button nz-button nzShape="round" nzType="default" (click)="departmentRoleRemove(deleteDepartment!, deleteRoleName!)">
            {{ 'btn.ok' | translate }}
          </button>
        </div>
      </div>
    </div>
  </div>
</ng-template>
