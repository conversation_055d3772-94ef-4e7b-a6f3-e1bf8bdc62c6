import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { Component, EventEmitter, Inject, InjectionToken, OnInit, Output } from '@angular/core';
import { TableHeaderConfigBase } from '../table-helper.service';
export const TABLE_HEADER_DATA = new InjectionToken<TableHeaderConfigBase[]>('CONTAINER_DATA');
@Component({
  selector: 'app-table-header-midify-dialog',
  templateUrl: './table-header-midify-dialog.component.html',
  styleUrls: ['./table-header-midify-dialog.component.scss'],
})
export class TableHeaderMidifyDialogComponent implements OnInit {
  @Output() onClose = new EventEmitter<TableHeaderConfigBase[]>();
  _shadowList: TableHeaderConfigBase[] = [];
  constructor(@Inject(TABLE_HEADER_DATA) public dataList: TableHeaderConfigBase[]) {}

  ngOnInit(): void {
    this.reset();
  }
  reset() {
    this._shadowList = [];
    this.dataList.forEach((item) => {
      this._shadowList.push({ ...item });
    });
  }
  submit() {
    this.onClose.emit(this._shadowList);
  }
  drop(event: CdkDragDrop<TableHeaderConfigBase[]>) {
    moveItemInArray(this._shadowList, event.previousIndex, event.currentIndex);
  }
  pin(item: TableHeaderConfigBase) {
    item.pinned = !item.pinned;
  }
}
