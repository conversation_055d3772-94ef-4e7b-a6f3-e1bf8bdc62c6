$highlightColor: #138aff;
$highlightIconColor: #0f86f8;
$highlightHoverColor: #4d96ff;

// 加工厂
.plant-container {
  &::ng-deep .card-container {
    padding: 12px;
  }

  .plant-header {
    position: relative;

    .plant-add {
      color: #00c790;
      position: absolute;
      top: 0;
      right: 0;
      margin: auto 0;
      bottom: 0;
      line-height: 32px;

      &::ng-deep .ant-btn > .anticon + span {
        margin-left: 0;
      }

      .anticon {
        font-size: 12px;
        margin-right: 4px;
      }

      &:hover {
        cursor: pointer;
        color: #33d2a6;
      }
    }

    .plant-select-width {
      &::ng-deep nz-select {
        width: 210px;

        & nz-select-top-control {
          border-radius: 20px;
        }
      }
    }
  }

  .plant-body {
    .plant-item {
      background: #f2f9ff;
      border-radius: 4px;
      padding: 8px;

      .plant-item-header-line {
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          width: calc(100% + 16px);
          display: block;
          height: 0.7px;
          background-color: #d4d7dc;
          margin: 0 -8px 0 -8px;
        }
      }

      .plant-item-header {
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        translate: all 1s;

        .plant-item-header-left {
          display: flex;
          align-items: center;

          .plant-item-header-serial {
            width: 19px;
            height: 19px;
            background: #7fbcff;
            border-radius: 11px;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 4px;
          }
        }

        .xinjian-box {
          &:hover {
            cursor: pointer;

            span {
              text-decoration: underline;
              color: $highlightHoverColor;
            }

            i {
              color: $highlightHoverColor;
            }
          }
        }

        .plant-item-header-right {
          display: flex;
          align-items: center;

          .icon-delete {
            &::after {
              content: '';
              margin: 0 12px;
              display: inline-block;
              height: 12px;
              width: 1px;
              background-color: #d4d7dc;
            }

            &:hover {
              color: #f74949;
              cursor: pointer;
            }
          }

          span {
            margin-right: 4px;
            color: $highlightColor;
          }

          .icon-toggle {
            color: $highlightIconColor;
            font-size: 12px;
          }

          .icon-xinjian1 {
            margin-right: 4px;
            font-size: 12px;
            color: $highlightIconColor;
          }

          .surplus {
            display: flex;
            align-items: center;
            margin-right: 0;

            &::after {
              content: '';
              margin: 0 12px;
              display: inline-block;
              height: 12px;
              width: 1px;
              background-color: #d4d7dc;
            }

            &:hover {
              cursor: pointer;
              color: $highlightHoverColor;
              text-decoration: underline;
            }
          }
        }
      }

      .toggle-zhankai {
        max-height: 666px;
        transition: all 0.4s ease-in-out;
        overflow: hidden;
      }

      .toggle-shouqi {
        max-height: 0;
        transition: all 0.4s;
      }
    }
  }
}
