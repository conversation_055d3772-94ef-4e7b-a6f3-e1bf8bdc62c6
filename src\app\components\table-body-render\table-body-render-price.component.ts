import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'component-table-body-render-price',
  template: '{{ (data | currency: "￥":"symbol":"1.2-2")|noValue }}',
  styles: [],
})
export class TableBodyRenderPriceComponent implements OnInit {
  @Input('data') inputData?: any;
  data?: number;
  constructor() {}

  ngOnInit(): void {
    if (this.inputData && typeof this.inputData === 'number') {
      this.data = this.inputData;
    }
  }
}
