import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { FlcDrawerHelperService, FlcOrderStatusCheckItem } from 'fl-common-lib';
import { MainService } from '../main.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-sample-notice',
  templateUrl: './sample-notice.component.html',
  styleUrls: ['./sample-notice.component.scss'],
})
export class SampleNoticeComponent implements OnInit {
  statusOptions: FlcOrderStatusCheckItem[] = [
    {
      label: '全部',
      value: 1,
      isTotalItem: true,
      checked: true,
    },
    { label: '已超时', value: 2, amount: 1 },
    { label: '可能超时', value: 3 },
    { label: '', value: -1 },
  ];

  tableConfig = {
    dataList: [], //表格数据
    count: 0, //数据总数量
    pageIndex: 1, //当前页码
    pageSize: 20, //当前每页数量
    loading: false,
  };
  status = 1;

  isMySelf = true;

  constructor(private _service: MainService, private _router: Router, private _flcDrawer: FlcDrawerHelperService) {}

  ngOnInit() {
    const sampleNoticeObj = localStorage.getItem('sampleNotice') ?? '{}';
    this.isMySelf = JSON.parse(sampleNoticeObj)?.isMySelf;
    this.getOrderList(true);
  }

  onCheckChange($event: any) {
    this.status = $event[0] ?? 1;
    this.tableConfig.pageIndex = 1;
    this.getOrderList();
  }

  onRadioChange() {
    this.isMySelf = !this.isMySelf;

    localStorage.setItem('sampleNotice', JSON.stringify({ isMySelf: this.isMySelf }));

    this.tableConfig.pageIndex = 1;
    this.getOrderList();
  }

  /**
   * 页码变化
   * @param e
   */
  onIndexChanges(e: number) {
    this.tableConfig.pageIndex = e;
    this.getOrderList();
  }

  /**
   * 每页数量变化
   * @param e
   */
  onSizeChanges(e: number) {
    this.tableConfig.pageSize = e;
    this.tableConfig.pageIndex = 1;
    this.getOrderList();
  }

  goToSample(id: any) {
    this._flcDrawer.closeDrawer();
    this._router.navigate(['sample-manage/sample-task/list', id]);
  }

  getOrderList(init = false) {
    const payload = {
      page: this.tableConfig.pageIndex,
      size: this.tableConfig.pageSize,
      viewAll: !this.isMySelf,
      status: this.status,
    };
    this._service.getSampleNotice(payload).subscribe((res) => {
      if (res.code === 200) {
        this.tableConfig.dataList = res.data.datalist;
        this.tableConfig.count = res.data.count;
        if (init) {
          this.statusOptions[0].amount = res.data.count;
          this.statusOptions[1].amount = res.data.ady_tmo_cnt;
          this.statusOptions[2].amount = res.data.may_tmo_cnt;
        }
      }
    });
  }
}
