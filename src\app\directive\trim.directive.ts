import { Directive, HostListener, Input } from '@angular/core';
import { NgControl } from '@angular/forms';

@Directive({
  selector: '[inputTrim]',
})
export class TrimDirective {
  constructor(private _control: NgControl) {}

  /**
   * @Description: 【<input.*nz-input.*formControlName】失焦需要自动去除首尾字符串
   */
  @HostListener('blur', ['$event'])
  onBlur(event: MouseEvent) {
    // const data = (event.target as HTMLInputElement | HTMLTextAreaElement).value;
    // const new_data = data ? data.trim() : data;
    // (event.target as HTMLInputElement | HTMLTextAreaElement).value = new_data;
    const data = this._control?.control?.value;
    const new_data = data ? data.trim() : data;
    this._control?.control?.setValue(new_data);
  }
}
