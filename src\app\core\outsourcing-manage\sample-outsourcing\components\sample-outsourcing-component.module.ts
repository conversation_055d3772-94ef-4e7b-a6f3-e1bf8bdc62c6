import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { FlButtonModule } from 'fl-ui-angular';
import { FlcComponentsModule, FlcDirectivesModule, FlcDrawerHelperService, FlcOssUploadService } from 'fl-common-lib';

import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzTimelineModule } from 'ng-zorro-antd/timeline';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { TranslateModule } from '@ngx-translate/core';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';

import { MaterialPackageModule } from 'fl-sewsmart-lib/material-package';
import { NzTreeSelectModule } from 'ng-zorro-antd/tree-select';
import { NzCascaderModule } from 'ng-zorro-antd/cascader';
import { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzTableModule } from 'ng-zorro-antd/table';

import { SampleOutsourcingOperateButtonComponent } from './sample-outsourcing-operate-button/sample-outsourcing-operate-button.component';
import { SampleOutsourcingBasicInfoComponent } from './sample-outsourcing-basic-info/sample-outsourcing-basic-info.component';
import { SampleOursourcingTechArchiveComponent } from './sample-oursourcing-tech-archive/sample-oursourcing-tech-archive.component';
import { SampleOutsourcingHistoryModelComponent } from './sample-outsourcing-history-model/sample-outsourcing-history-model.component';
import { SampleOursourcingBasicFormComponent } from './sample-oursourcing-basic-form/sample-oursourcing-basic-form.component';
import { SampleOutsouringProgressComponent } from './sample-outsouring-progress/sample-outsouring-progress.component';
import { MaterialPlanIntegrationFormComponent } from './material-plan-integration/material-plan-integration-form/material-plan-integration-form.component';
import { MaterialPlanDetailComponent } from './material-plan-integration/material-plan-detail/material-plan-detail.component';

const components = [
  SampleOutsourcingOperateButtonComponent,
  SampleOutsourcingBasicInfoComponent,
  SampleOursourcingTechArchiveComponent,
  SampleOutsourcingHistoryModelComponent,
  SampleOursourcingBasicFormComponent,
  SampleOutsouringProgressComponent,
  MaterialPlanIntegrationFormComponent,
  MaterialPlanDetailComponent,
];

const nzModules = [
  NzIconModule,
  NzButtonModule,
  NzToolTipModule,
  NzSpinModule,
  NzDrawerModule,
  NzTimelineModule,
  NzSelectModule,
  NzFormModule,
  NzDatePickerModule,
  NzInputModule,
  NzPopconfirmModule,
  NzTreeSelectModule,
  NzCascaderModule,
  NzAutocompleteModule,
  NzDividerModule,
  NzTableModule,
];

@NgModule({
  declarations: [...components],
  imports: [
    CommonModule,
    MaterialPackageModule,
    FlcComponentsModule,
    FlButtonModule,
    DragDropModule,
    FlcDirectivesModule,
    TranslateModule,
    ReactiveFormsModule,
    FormsModule,
    ...nzModules,
  ],
  exports: [...components],
  providers: [FlcDrawerHelperService, FlcOssUploadService],
})
export class SampleOutsourcingComponentsModule {}
