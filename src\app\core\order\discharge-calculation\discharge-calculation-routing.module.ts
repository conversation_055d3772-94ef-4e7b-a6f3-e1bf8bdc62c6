import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DischargeCalculationDetailComponent } from 'src/app/core/order/discharge-calculation/discharge-calculation-detail/discharge-calculation-detail.component';
import { DischargeCalculationListComponent } from 'src/app/core/order/discharge-calculation/discharge-calculation-list/discharge-calculation.component';
const routes: Routes = [
  {
    path: 'list',
    component: DischargeCalculationListComponent,
    data: {
      keepAlive: true,
    },
  },
  {
    path: 'list/:id',
    component: DischargeCalculationDetailComponent,
  },
  { path: '', redirectTo: 'list' },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class DischargeCalculationRoutingModule {}
