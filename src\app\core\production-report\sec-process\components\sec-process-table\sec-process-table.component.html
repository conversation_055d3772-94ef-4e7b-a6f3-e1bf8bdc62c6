<nz-spin [nzSpinning]="loading">
  <div class="basic-info">
    <div class="basic-info-item" *ngFor="let info of baseInfoConfig; last as isLast">
      <ng-container *ngIf="info?.visible">
        <span class="basic-info-title">{{ 'secProcess.' + info.label | translate }}：</span>
        <flc-text-ellipsis
          *ngIf="info.key === 'biz_date'"
          [qty]="16"
          [totalString]="(detailInfo?.[info.key] | date: 'MM/dd') ?? ''"></flc-text-ellipsis>
        <flc-text-ellipsis *ngIf="info.key !== 'biz_date'" [qty]="16" [totalString]="detailInfo?.[info.key]"></flc-text-ellipsis>
        <nz-divider *ngIf="!isLast" nzType="vertical"></nz-divider>
      </ng-container>
    </div>
  </div>
  <div class="table-wrapper">
    <ng-container *ngFor="let data of detailList">
      <p style="margin-bottom: 8px">{{ 'secProcess.裁片来源' | translate }}：{{ data?.origin_factory_name | flcNoValue }}</p>
      <flc-table
        *ngIf="mode !== detailModalMode.ColorSizeMode"
        [id]="0"
        [tableHeader]="renderHeader"
        [tableConfig]="data?.tableConfig"
        [template]="renderTpl">
      </flc-table>
      <!-- 颜色尺码弹框使用flc-color-size-table -->
      <ng-container *ngIf="mode === detailModalMode.ColorSizeMode">
        <flc-color-size-table *ngIf="data?.color_size_info?.length" [dataList]="data?.color_size_info"></flc-color-size-table>
        <div class="no-data" *ngIf="!data?.color_size_info?.length">
          <flc-no-data></flc-no-data>
        </div>
      </ng-container>
    </ng-container>
  </div>
</nz-spin>

<ng-template #renderTpl let-data="data">
  <ng-container *ngIf="data.isTd">
    <ng-container *ngFor="let pos of data.item.positions">
      <div
        *ngIf="['expected_back_time', 'position_name', 'actual_back_time', 'status'].includes(data.key)"
        class="template-cell"
        [ngStyle]="{ height: pos.color_size_info?.length * 42 + 'px', 'line-height': pos.color_size_info?.length * 42 + 'px' }">
        <flc-text-truncated *ngIf="data.key !== 'status'" [data]="pos[data.key]"></flc-text-truncated>
        <span *ngIf="data.key === 'status'" [ngStyle]="{ color: pos.status === 2 ? '#fe5d56' : '' }">{{ statusValue[pos.status] }}</span>
      </div>

      <div
        *ngIf="data.key === 'qualified_qty'"
        class="template-cell"
        [ngStyle]="{ height: pos.color_size_info?.length * 42 + 'px', 'line-height': pos.color_size_info?.length * 42 + 'px' }">
        <span class="flex-center">
          <flc-text-truncated
            *ngIf="pos?.qualified_qty; else noValueTpl"
            [data]="pos?.qualified_qty | number: '1.0-0'"></flc-text-truncated>
          <span>/</span>
          <flc-text-truncated
            [ngStyle]="{ color: pos?.defective_qty ? '#f96d6d' : '' }"
            *ngIf="pos?.defective_qty; else noValueTpl"
            [data]="pos?.defective_qty | number: '1.0-0'"></flc-text-truncated>
        </span>
      </div>
      <ng-container *ngFor="let line of pos.color_size_info; last as isLast">
        <div *ngIf="data.key === 'color'" class="template-cell" [ngStyle]="{ 'background-color': isLast ? '#F3FAFF' : '' }">
          <flc-text-truncated [data]="isLast ? '总计' : line.color_name"></flc-text-truncated>
        </div>
        <div *ngIf="sizeList.includes(data.key)" class="template-cell" [ngStyle]="{ 'background-color': isLast ? '#F3FAFF' : '' }">
          <flc-text-truncated *ngIf="line[data.key]; else noValueTpl" [data]="line[data.key] | number: '1.0-0'"></flc-text-truncated>
        </div>

        <div *ngIf="data.key === 'total'" class="template-cell" [ngStyle]="{ 'background-color': isLast ? '#F3FAFF' : '' }">
          <flc-text-truncated *ngIf="line.total; else noValueTpl" [data]="line.total | number: '1.0-0'"></flc-text-truncated>
        </div>
      </ng-container>
    </ng-container>
  </ng-container>
</ng-template>

<ng-template #noValueTpl>
  <span>0</span>
</ng-template>
