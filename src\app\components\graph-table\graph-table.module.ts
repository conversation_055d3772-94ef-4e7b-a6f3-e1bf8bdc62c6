import { DragDropModule } from '@angular/cdk/drag-drop';
import { FormsModule } from '@angular/forms';
import { FlcComponentsModule, FlcDirectivesModule } from 'fl-common-lib';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GraphTableComponent } from './graph-table.component';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { HttpClient } from '@angular/common/http';
import { TranslateModule, TranslateLoader, TranslateService } from '@ngx-translate/core';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import { ScrollingModule } from '@angular/cdk/scrolling';

const zorroComponents = [NzToolTipModule, NzCheckboxModule, NzButtonModule, NzIconModule];

@NgModule({
  declarations: [GraphTableComponent],
  imports: [
    CommonModule,
    FormsModule,
    FlcComponentsModule,
    FlcDirectivesModule,
    DragDropModule,
    ScrollingModule,
    ...zorroComponents,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new MultiTranslateHttpLoader(http, [{ prefix: './assets/i18n/elan-components/', suffix: '.json' }]);
        },
        deps: [HttpClient],
      },
    }),
  ],
  exports: [GraphTableComponent],
})
export class GraphTableModule {
  constructor(public translateService: TranslateService) {
    const cl = translateService.currentLang;
    translateService.currentLang = '';
    translateService.use(cl);
  }
}
