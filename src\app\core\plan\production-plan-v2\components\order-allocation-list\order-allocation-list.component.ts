import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ProductionPlanV2Service } from '../../production-plan-v2.service';
import { endOfDay, format, startOfDay } from 'date-fns';
import { allocationListHeader, allocationListSearchConfig } from './order-allocation-list.config';
import { AllocationListItem, AllocationListPayload, CommonOptionItem } from '../../model/production-plan.interface';
import { FormBuilder } from '@angular/forms';
import { NzModalService } from 'ng-zorro-antd/modal';
import { OrderAllocationModalComponent } from '../order-allocation-modal/order-allocation-modal.component';

@Component({
  selector: 'app-order-allocation-list',
  templateUrl: './order-allocation-list.component.html',
  styleUrls: ['./order-allocation-list.component.scss'],
})
export class OrderAllocationListComponent implements OnInit {
  @ViewChild('searchBarWrap') searchBarWrap!: ElementRef<HTMLElement>;
  @Input() isEdit = false;
  @Input() factory_items: { factory_code: string; production_line_no: string }[] = [];
  searchList = allocationListSearchConfig;
  searchData: any = {};
  tableHeader = allocationListHeader;
  tableConfig = {
    detailBtn: false,
    dataList: <AllocationListItem[]>[],
    count: 0,
    height: 800,
    loading: false,
    tableName: 'inquiryAcceptList',
    pageSize: 20,
    pageIndex: 1,
    actionWidth: '90px',
  };

  samEditForm = this._fb.group({
    sam: [null],
    average_daily_production: [null],
    selected: [null],
  });

  optionList: { [key: string]: CommonOptionItem[] } = {};

  hasEditSam = false;

  constructor(
    private _fb: FormBuilder,
    private _msg: NzMessageService,
    private _modal: NzModalService,
    private _service: ProductionPlanV2Service
  ) {}

  ngOnInit(): void {
    this.getList();
    this.getSearchOptions();
    this.hasEditSam = this._service.getUserActionsMap()?.hasEditSam;
  }

  calcTableHeight() {
    const bodyHeight = document.querySelector('.ant-drawer-body')?.clientHeight || 0;
    const searchBarHeight = this.searchBarWrap.nativeElement.clientHeight;
    const renderY = bodyHeight - searchBarHeight;
    const renderScrollY = renderY - 130;
    this.tableConfig.height = renderScrollY;
    this.tableConfig = { ...this.tableConfig };
  }
  /**
   * 页码改变
   * @param  {number} e
   */
  indexChanges(e: number) {
    this.tableConfig.pageIndex = e;
    this.getList();
  }
  /**
   * 页数改变
   * @param  {number} e
   */
  sizeChanges(e: number) {
    this.tableConfig.pageIndex = 1;
    this.tableConfig.pageSize = e;
    this.getList();
  }
  handleWhere() {
    const where: AllocationListPayload = {
      ...this.searchData,
    };
    if (this.searchData.due_time?.length) {
      const startTime = format(startOfDay(this.searchData.due_time[0]), 'T');
      const endTime = format(endOfDay(this.searchData.due_time[1]), 'T');
      where.due_time_start = startTime;
      where.due_time_end = endTime;
    }

    if (this.searchData.pre_material_completed_time?.length) {
      const startTime = format(startOfDay(this.searchData.pre_material_completed_time[0]), 'T');
      const endTime = format(endOfDay(this.searchData.pre_material_completed_time[1]), 'T');
      where.pre_material_completed_time_start = startTime;
      where.pre_material_completed_time_end = endTime;
    }

    return where;
  }
  getList(reset = false) {
    if (this.tableConfig.loading) {
      this.tableConfig.pageIndex = 1;
    }
    this.tableConfig.pageIndex = reset ? 1 : this.tableConfig.pageIndex;
    this.tableConfig.loading = true;
    const data = {
      ...this.handleWhere(),
      size: this.tableConfig.pageSize,
      page: this.tableConfig.pageIndex,
    };
    this.tableConfig = { ...this.tableConfig };
    this._service.getAllocateOrderList(data).subscribe((res) => {
      if (res.code === 200) {
        this.tableConfig.dataList = res.data.list;
        this.tableConfig.count = res.data.total;
        this.tableConfig.loading = false;
        this.tableConfig = { ...this.tableConfig };
      }
    });
  }

  getSearchOptions() {
    this._service.getAllocateOrderListOptions().subscribe((res) => {
      if (res.code === 200) {
        this.optionList = res.data;
      }
    });
  }

  onReset() {
    this.searchData = {};
    this.getList(true);
    this.getSearchOptions();
  }

  onChangeDatePicker() {
    this.getList();
  }

  onAllocate(item: AllocationListItem) {
    if (!item.selected) {
      this._msg.error('请先设置SAM值或人均日台产');
      return;
    }
    const _modal = this._modal.create({
      nzTitle: '订单分配',
      nzContent: OrderAllocationModalComponent,
      nzFooter: null,
      nzWidth: '1250px',
      nzWrapClassName: 'flc-confirm-modal',
      nzMaskClosable: false,
      nzComponentParams: {
        data: item,
        factory_items: this.factory_items,
      },
      nzAutofocus: null,
      nzOnOk: (com) => {
        this._service.alloacationOrder(com.getPayload()).subscribe((res) => {
          if (res.code === 200) {
            _modal.close();
            this.getList();
            this.getSearchOptions();
          }
        });
        return false;
      },
    });
  }

  onComrimSam(item: AllocationListItem) {
    const _value = this.samEditForm.getRawValue();
    if (!_value.selected) {
      this._msg.error('请选择SAM值或人均日台产');
      return;
    }
    this._service
      .setOrderAllocation({
        style_lib_uuid: item.style_lib_uuid,
        ..._value,
      })
      .subscribe((res) => {
        if (res.code === 200) {
          this._msg.success('设置成功');
          this.getList();
        }
      });
  }

  onChangeVisible(e: boolean, item: AllocationListItem) {
    if (e) {
      this.samEditForm.patchValue({
        sam: item.sam,
        average_daily_production: item.average_daily_production,
        selected: item.selected,
      });
    } else {
      this.samEditForm.reset();
    }
  }
}
