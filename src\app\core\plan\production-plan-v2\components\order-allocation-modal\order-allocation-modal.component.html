<div class="modal-body">
  <div class="modal-body-header">
    <span>大货订单：</span>
    <span>{{ detailData?.order_code }}</span>
    <nz-divider nzType="vertical"></nz-divider>
    <span>交期：</span>
    <span>{{ detailData?.due_times || '-' }}</span>
    <nz-divider nzType="vertical"></nz-divider>
    <span>预计物料齐套日期：</span>
    <span>{{ detailData?.pre_material_completed_time ? (data.pre_material_completed_time | date: 'yyyy/MM/dd') : '-' }}</span>
    <nz-divider nzType="vertical"></nz-divider>
    <span>待分配件数：</span>
    <span>{{ detailData?.to_be_allocated }}</span>
  </div>
  <div class="modal-content">
    <div class="radio-box">
      <nz-radio-group [(ngModel)]="orderAllocationType" nzButtonStyle="solid" (ngModelChange)="onCheckTab()">
        <label nz-radio-button [nzValue]="orderAllocationTypeEnum.order">按订单件数</label>
        <label nz-radio-button [nzValue]="orderAllocationTypeEnum.color">按颜色</label>
        <label nz-radio-button [nzValue]="orderAllocationTypeEnum.po">按交付单</label>
      </nz-radio-group>
    </div>
    <form class="modal-table" nz-form [formGroup]="modalForm">
      <nz-table [nzData]="dataList.value" nzBordered nzShowPagination="false" [nzScroll]="{ y: '250px' }">
        <thead>
          <tr>
            <th [nzWidth]="'40px'" nzLeft>#</th>
            <th *ngFor="let header of tableHeaders" [nzWidth]="header.width">
              {{ header.name }}
            </th>
            <th [nzWidth]="'60px'" [nzRight]="true">操作</th>
          </tr>
        </thead>
        <tbody formArrayName="list">
          <tr *ngFor="let control of dataList.controls; index as i; first as isFirst; last as isLast" [formGroupName]="i">
            <td nzLeft>{{ i + 1 }}</td>
            <td *ngFor="let header of tableHeaders">
              <nz-form-item [ngSwitch]="header.type">
                <nz-form-control [flcErrorTip]="header.name">
                  <ng-container *ngSwitchCase="'select'">
                    <nz-select
                      [formControlName]="header.key"
                      nzAllowClear
                      nzShowSearch
                      nzPlaceHolder="请选择"
                      [nzDropdownMatchSelectWidth]="false"
                      (ngModelChange)="onChangeSelect($event, header, control)"
                      (nzOpenChange)="onOpenChange($event, header, control)">
                      <nz-option
                        *ngIf="header.key === 'production_line_no'"
                        nzHide
                        [nzValue]="control.get(header.key)?.value"
                        [nzLabel]="control.get(header.labelKey!)?.value">
                      </nz-option>
                      <nz-option
                        *ngFor="let item of opitonsMap[header.key] || []"
                        [nzValue]="item[header.optionKey!.value]"
                        [nzLabel]="item[header.optionKey!.label]"></nz-option>
                    </nz-select>
                  </ng-container>

                  <ng-container *ngSwitchCase="'input-number'">
                    <nz-input-number
                      [formControlName]="header.key"
                      [nzMin]="header.min || 0"
                      [nzMax]="header.max || 0"
                      [nzPlaceHolder]="'请输入'"
                      [nzStep]="1"
                      (ngModelChange)="onChangeValues($event, header, control)"></nz-input-number>
                  </ng-container>

                  <ng-container *ngSwitchCase="'text'">
                    <flc-text-truncated
                      data="{{ control.get(header.key)?.value === 0 ? '0' : control.get(header.key)?.value }}"></flc-text-truncated>
                  </ng-container>

                  <div class="data-picker-tpl" *ngSwitchCase="'dateTpl'">
                    <nz-date-picker
                      [formControlName]="header.key"
                      [nzAllowClear]="false"
                      (ngModelChange)="onChangeValues($event, header, control)"></nz-date-picker>
                    <span>~</span>
                    <span>
                      {{ control.get(header.labelKey!)?.value ? (control.get(header.labelKey!)?.value | date: 'yyyy/MM/dd') : '-' }}
                    </span>
                  </div>

                  <ng-container *ngSwitchCase="'global-text'">
                    <flc-text-truncated [data]="control.get('surplus_qty')?.value"></flc-text-truncated>
                  </ng-container>
                </nz-form-control>
              </nz-form-item>
            </td>

            <td [nzRight]="true">
              <span class="line-operate-btn">
                <a nz-button nzType="text" [disabled]="dataList.controls.length === 1">
                  <i nz-icon [nzIconfont]="'icon-jianshao'" (click)="onRemoveRow(i)"></i>
                </a>
                <a nz-button nzType="text">
                  <i nz-icon [nzIconfont]="'icon-zengjia1'" (click)="onAddRow(i)"></i>
                </a>
              </span>
            </td>
          </tr>
        </tbody>
      </nz-table>
    </form>
  </div>
</div>
<div class="bottomBar">
  <button nz-button flButton="default-negative" [nzShape]="'round'" style="margin-right: 0" (click)="onCloseModal(false)">取消</button>
  <button style="margin-right: 0" nzType="primary" nz-button flButton="default-positive" [nzShape]="'round'" (click)="onCloseModal(true)">
    确认
  </button>
</div>
