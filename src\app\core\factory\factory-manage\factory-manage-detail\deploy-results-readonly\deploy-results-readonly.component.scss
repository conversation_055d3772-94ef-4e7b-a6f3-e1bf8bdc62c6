.deploy-container {
  padding-bottom: 16px;

  .title-label {
    font-size: 16px;
    font-weight: 500;
    color: #54607c;
  }

  nz-divider {
    margin: 8px 0px 16px 0px;
  }

  .deploy-content {
    display: flex;
    align-items: baseline;

    & > div:nth-child(1) {
      min-width: 8%;
      text-align: right;
    }

    .label-required:before {
      content: '*';
      color: red;
    }

    .link-container {
      display: flex;
      align-items: center;
      gap: 20px;

      p {
        margin-bottom: 0px;
        color: #13aa81;
      }
    }

    .notice-container {
      background: rgba(19, 138, 255, 0.06);
      border-radius: 4px;
      padding: 5px 9px;
      font-size: 14px;
      font-family: PingFangHK-Medium, PingFangHK;
      font-weight: 500;
      color: #222b3c;

      i {
        color: #138aff;
      }
    }
  }
}
