::ng-deep .plan-zoom {
  width: 146px;
  .ant-slider-handle {
    box-shadow: 0px 1px 3px 0px rgba(158, 171, 193, 0.5);
    border-radius: 5px;
    border: 2px solid #fff;
    background: #bedfff;
    &:focus {
      border-radius: 5px;
      background: #bedfff;
      box-shadow: 0px 1px 3px 0px rgba(141, 182, 255, 0.5);
      border: 2 solid #ffffff;
    }
  }

  .ant-slider:hover {
    .ant-slider-rail {
      background-color: #e7e9ef;
    }

    .ant-slider-track {
      background-color: #e7e9ef;
    }

    .ant-slider-handle:not(.ant-tooltip-open) {
      border-color: #ffffff;
      box-shadow: 0px 1px 3px 0px rgba(141, 182, 255, 0.5), 0px 0px 4px -1px #2996ff;
      background: #2996ff;
    }
  }

  .ant-slider-rail {
    height: 6px;
    background: #e7e9ef;
    box-shadow: inset 0px 1px 2px 1px rgba(195, 216, 238, 0.5);
    border-radius: 1px;
  }

  .ant-slider-track {
    height: 6px;
    background: #e7e9ef;
    border-radius: 1px;
  }
}
