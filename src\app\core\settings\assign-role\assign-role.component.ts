/* eslint-disable @typescript-eslint/no-non-null-assertion */
// import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { Component, ElementRef, OnD<PERSON>roy, OnInit, QueryList, TemplateRef, ViewChild, ViewChildren } from '@angular/core';
import { Subject, Subscription } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { RoleNameItemModel, RoleNameListModel } from '../role-manager/role-manager.model';
import { RoleManagerService } from '../role-manager/role-manager.service';
import {
  assignRoleDepartmentItem,
  assignRoleDepartmentModel,
  assignRoleEmployeeItem,
  assignRoleEmployeeModel,
  assignRoleModel,
  searchItemModel,
} from './assign-role.model';
import { AssignRoleService } from './assign-role.service';
import { SortableEvent, SortableOptions } from 'sortablejs';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzMessageService } from 'ng-zorro-antd/message';
import { AppStorageService } from 'src/app/shared/app-storage.service';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { Router } from '@angular/router';
import { FlcSpUtilService } from 'fl-common-lib';

@Component({
  selector: 'app-assign-role',
  templateUrl: './assign-role.component.html',
  styleUrls: ['./assign-role.component.scss'],
})
export class AssignRoleComponent implements OnInit, OnDestroy {
  @ViewChild('employeeBoard') employeeBoard!: ElementRef<HTMLElement>;
  @ViewChild('departmentWrap') departmentWrap!: ElementRef<HTMLElement>;
  @ViewChildren('departmentListWrap') departmentListWrap!: QueryList<ElementRef<HTMLElement>>;

  @ViewChild('deleteHeadTemplate') deleteHeadTemplate!: TemplateRef<HTMLElement>;
  @ViewChild('deleteTemplate') deleteTemplate!: TemplateRef<HTMLElement>;
  @ViewChild('departmentSearchInput') departmentSearchInput!: ElementRef<HTMLElement>;
  deleteRoleName?: assignRoleModel;
  deleteDepartment?: assignRoleDepartmentItem;
  deleteModal?: NzModalRef;

  roleNameList?: RoleNameListModel[];
  searchRoleNameList?: RoleNameListModel[];
  selectedRoleNameDetail?: RoleNameItemModel | null;
  flatDepartmentList: { id: number; name: string }[] = [];
  departmentTreeList?: assignRoleDepartmentItem[];
  topEmployees?: assignRoleDepartmentItem;
  selectedShowEmployeesDepartment?: assignRoleDepartmentItem;
  showTopEmployee = false;
  companyInfo?: { name: string };
  isEdit = false;
  isFullMode = false;

  searchValue?: string;
  searchSubject = new Subject<string>();
  searchSubjection?: Subscription;
  roleSearchInput?: string;
  // roleNmaeSearchSubject = new Subject<string>();
  // roleNmaeSearchSubjection?: Subscription;
  searchKeywordOptions: searchItemModel[] = [];
  someOptions: SortableOptions = {
    group: {
      name: 'shared',
    },
    sort: false,
    draggable: '.draggable',
  };
  selectedDepartment?: assignRoleDepartmentItem;
  selectedDepartmentObject?: { ids: number[]; isEmployess: boolean };
  canDrag = false;
  btnArr: string[] = [];
  readySubcription?: Subscription;
  translateName = 'assign.role.';
  constructor(
    private _roleService: RoleManagerService,
    private _service: AssignRoleService,
    private _notice: NzNotificationService,
    private _storage: AppStorageService,
    private _modal: NzModalService,
    private _massage: NzMessageService,
    private _router: Router,
    private _spUtil: FlcSpUtilService
  ) {}
  canLeave(): boolean {
    return !this.isEdit;
  }
  ngOnDestroy(): void {
    this.searchSubject.complete();
    this.searchSubjection?.unsubscribe();
    // this.roleNmaeSearchSubject.complete();
    // this.roleNmaeSearchSubjection?.unsubscribe();
    this.readySubcription?.unsubscribe();
    document.body.removeEventListener('drop', this.onStopAction);
  }

  ngOnInit(): void {
    this.btnArr = this._storage.getUserActions('settings/assignRole');
    this.canDrag = this.btnArr.includes('settings:assignRole-allocate');
    this.getCompanyInfo();
    this.getRoleNameList(true);
    this.getFlatDepartment();
    this.getDepartmentTree();
    this.searchSubjection = this.searchSubject.pipe(debounceTime(300)).subscribe((value) => this.searchDeptNEmp(value));
    // this.roleNmaeSearchSubjection = this.roleNmaeSearchSubject.pipe(debounceTime(300)).subscribe((value) => this.getRoleNameList());
  }

  onStopAction(event: Event) {
    event.preventDefault();
    event.stopPropagation();
  }

  ngAfterViewInit() {
    // 防止sortablejs在firefox浏览器拖拽打开新窗口
    document.body.addEventListener('drop', this.onStopAction);
  }

  getCompanyInfo() {
    this._service.getGlobalInfo().subscribe((res) => {
      if (res.code === 200) {
        this.companyInfo = {
          name: res.data.company,
        };
      }
    });
  }
  getDepartmentTree() {
    this.showTopEmployee = false;
    this.selectedDepartment = undefined;
    this._service.getDepartmentRoot().subscribe((res) => {
      if (res.code === 200) {
        const shadowList = res.data;
        const deptList: assignRoleDepartmentModel[] = [];
        const empList: assignRoleEmployeeModel[] = [];
        shadowList.forEach((item) => {
          if (item.type === 'dept') {
            deptList.push(item);
          }
          if (item.type === 'emp') {
            empList.push(item);
          }
        });
        this.departmentTreeList = [];
        this.topEmployees = new assignRoleDepartmentItem(
          {
            id: -10,
            name: '直属',
            code: '',
            status: true,
            type: 'dept',
            count: 0,
            isLeaf: false,
            children: deptList,
            employees: empList,
          },
          null
        );
        this.departmentTreeList = this.topEmployees.children;
        this.departmentTreeList.forEach((item) => (item.isInit = false));
        this.topEmployees.handleEmployeeRolesChanged();
        if (this.selectedDepartmentObject) {
          const item = JSON.parse(JSON.stringify(this.selectedDepartmentObject));
          this.locateToSelectedDepartment(item);
        }
      }
    });
  }
  async locateToSelectedDepartment(
    item: { ids: number[]; isEmployess: boolean },
    target?: assignRoleDepartmentItem,
    times = 0,
    isTop = false
  ) {
    const shadowTarget = target || this.topEmployees!;
    const id = item.ids.shift();
    if (id === -10) {
      if (item.ids.length > 0) {
        this.locateToSelectedDepartment(item, shadowTarget, times, true);
      } else {
        shadowTarget.showEmployee = true;
        this.showTopEmployee = true;
        this.selectedShowEmployeesDepartment = shadowTarget;
      }
    } else {
      const index = shadowTarget?.children.findIndex((department) => id === department.id);
      const child = shadowTarget!.children[index!];
      await this.selectDepartment(child, isTop, true);
      setTimeout(() => {
        const departmentWrap = this.departmentListWrap.get(times)?.nativeElement;
        const departmentBoard = departmentWrap?.parentElement as HTMLElement;
        const selectedElement = departmentWrap?.querySelector('.selected') as HTMLElement;
        departmentBoard?.scrollTo(0, selectedElement?.offsetTop - departmentBoard.offsetTop);
      }, 0);
      if (item.ids.length > 0) {
        this.locateToSelectedDepartment(item, child, times + 1);
      } else {
        if (item.isEmployess) {
          child.showEmployee = true;
        }
      }
    }
  }
  handleSelectedDepartmentObject(item: assignRoleDepartmentItem, isEmployess: boolean) {
    if (this.selectedDepartmentObject === undefined) {
      this.selectedDepartmentObject = {
        ids: isEmployess ? [item.id] : [item.partent!.id],
        isEmployess,
      };
    } else {
      this.selectedDepartmentObject.isEmployess = isEmployess;
      this.selectedDepartmentObject.ids = this.handleDepartmentIdscalc(item);
    }
  }
  handleDepartmentIdscalc(item: assignRoleDepartmentItem): number[] {
    const ids: number[] = [];
    let parent: assignRoleDepartmentItem | null = item;
    while (parent) {
      ids.unshift(parent.id);
      parent = parent.partent;
    }
    return ids;
  }
  async getDepartmentDetail(department: assignRoleDepartmentItem) {
    const res: any = await this._service.getDepartmentDetail(department.id).toPromise();
    if (res.code === 200) {
      const shadowList = res.data.children;
      shadowList.forEach((item: assignRoleDepartmentModel) => {
        department.children?.push(new assignRoleDepartmentItem(item, department));
      });
      res.data.employees.forEach((item: assignRoleEmployeeModel) => {
        department.employees?.push(new assignRoleEmployeeItem(item, department));
      });
      department.status = res.data.status;
      department.isLeaf = res.data.isLeaf;
      department.handleEmployeeRolesChanged();
      department.isInit = true;
    }
  }
  getFlatDepartment() {
    this._roleService.getAllDepartmentList().subscribe((res) => {
      if (res.code === 200) {
        this.flatDepartmentList = res.data;
      }
    });
  }
  roleNameSearchChange(event: RoleNameListModel) {
    this.getRoleNameList();
    // this.roleNmaeSearchSubject.next(event);
  }
  searchRole(reset = false) {
    if (reset || this.roleSearchInput?.length === 0) {
      this.roleSearchInput = undefined;
    }
    this.getRoleNameList();
  }
  // 只获取启用的权限
  getRoleNameList(isInit = false) {
    this._roleService.getRoleNameList({ name: this.roleSearchInput, limit: 0, page: 1, status: 1 }).subscribe((res) => {
      if (res.code === 200) {
        this.roleNameList = res.data.datalist;
        if (isInit) {
          this.searchRoleNameList = res.data.datalist;
        }
        if (this.roleNameList.length > 0) {
          this.selectedRoleName(this.roleNameList[0].id);
        } else {
          this.selectedRoleNameDetail = null;
        }
      }
    });
  }
  selectedRoleName(id: number) {
    this.selectedRoleNameDetail = undefined;
    this._roleService.getRoleNameDetail(id).subscribe((res) => {
      if (res.code === 200) {
        this.selectedRoleNameDetail = res.data;
      }
    });
  }
  toggleFullMode() {
    this.isFullMode = !this.isFullMode;
  }
  toggleEdit(status: boolean) {
    this.isEdit = status;
    if (!status) {
      this.getDepartmentTree();
    }
  }
  departmentWrapScrollToRight() {
    setTimeout(() => {
      const element = this.departmentWrap.nativeElement;
      element.scrollTo(element.scrollWidth, 0);
    }, 0);
  }
  save() {
    const data = this.topEmployees?.getAllChangedUser();
    if (data) {
      this._service.saveAllChange(data).subscribe(
        (res) => {
          if (res.code === 200) {
            this._notice.success(this._service.translateValue('success.save'), '');
            this.toggleEdit(false);
          }
        },
        undefined,
        () => {
          this.toggleEdit(false);
        }
      );
    }
  }
  searchInput(event: Event) {
    const value = (event.target as HTMLInputElement).value;
    this.searchSubject.next(value);
  }
  searchDeptNEmp(keyword: string) {
    if (keyword.length > 0) {
      this._service.searchDeptNEmp(keyword).subscribe((res) => {
        if (res.code === 200) {
          this.searchKeywordOptions = res.data;
        }
      });
    } else {
      this.searchKeywordOptions = [];
    }
  }
  getRealName(item: searchItemModel): string {
    if (item.is_leaf) {
      return item.name;
    } else {
      return this.getRealName(item.child);
    }
  }
  getSearchName(item: searchItemModel): string {
    if (item.is_leaf) {
      return item.name;
    } else {
      return `${item.name} - ${this.getSearchName(item.child)}`;
    }
  }
  async locateIt(item: searchItemModel, target?: assignRoleDepartmentItem | null, times = 0) {
    const shadowTarget = target ?? this.topEmployees;
    const isTop = target === undefined;
    if (item.type === 'emp') {
      const index = shadowTarget!.employees.findIndex((employee) => item.id === employee.id);
      if (index !== -1) {
        this.selectDepartmentEmployee(shadowTarget!);
        setTimeout(() => {
          const parentElement = this.employeeBoard.nativeElement;
          const child = parentElement.querySelectorAll('.employeeItem')[index] as HTMLElement;
          parentElement.scrollTo(0, child.offsetTop - parentElement.offsetTop);
        }, 0);
      }
    } else {
      const index = shadowTarget?.children.findIndex((department) => item.id === department.id);
      const child = shadowTarget!.children[index!];
      await this.selectDepartment(child, isTop, true);
      setTimeout(() => {
        const departmentWrap = this.departmentListWrap.get(times)?.nativeElement;
        const departmentBoard = departmentWrap?.parentElement as HTMLElement;
        const selectedElement = departmentWrap?.querySelector('.selected') as HTMLElement;
        departmentBoard?.scrollTo(0, selectedElement?.offsetTop - departmentBoard.offsetTop);
      }, 0);
      if (item.is_leaf) {
        // do nothing
      } else {
        this.locateIt(item.child, child, times + 1);
      }
    }
    if (isTop) {
      this.departmentSearchInput.nativeElement.blur();
      this.departmentWrapScrollToRight();
      this.searchValue = undefined;
      this.searchKeywordOptions = [];
    }
  }

  async selectDepartment(item: assignRoleDepartmentItem, isTop = false, isLocate = false) {
    if (item.partent) {
      item.partent.children?.forEach((child) => (child.isSelected = false));
      item.partent.seletedChild = item;
      item.partent.showEmployee = false;
    }
    if (isTop) {
      this.selectedDepartment = item;
      this.selectedDepartment.showEmployee = true;
      this.showTopEmployee = false;
      this.departmentTreeList?.forEach((department) => (department.isSelected = false));
    }
    if (item.isInit === false) {
      await this.getDepartmentDetail(item);
    }
    this.selectedShowEmployeesDepartment = item;
    item.seletedChild = null;
    item.showEmployee = true;
    item.children.forEach((child) => (child.isSelected = false));
    item.isSelected = true;
    this.departmentWrapScrollToRight();
    if (!isLocate) {
      this.handleSelectedDepartmentObject(item, false);
    }
  }
  selectTopEmployee(isLocate = false) {
    this.showTopEmployee = true;
    this.departmentTreeList?.forEach((department) => (department.isSelected = false));
    if (!isLocate) {
      this.handleSelectedDepartmentObject(this.topEmployees!, true);
    }
  }
  selectDepartmentEmployee(item: assignRoleDepartmentItem) {
    item.showEmployee = true;
    item.children.forEach((child) => (child.isSelected = false));
    this.selectedShowEmployeesDepartment = item;
    this.handleSelectedDepartmentObject(item, true);
  }
  employeeRoleOnAdd(item: assignRoleEmployeeItem) {
    return (event: SortableEvent) => {
      const NewIndex = item.roles.findIndex((role) => (role as any).id);
      if (NewIndex !== -1) {
        const role: RoleNameListModel = JSON.parse(JSON.stringify(item.roles[NewIndex]));
        if (item.roles.some((listRole) => listRole.role_id === role.id)) {
          item.roles.splice(NewIndex, 1);
          this._notice.create('error', this._service.translateValue(this.translateName + '该权限已存在'), '');
        } else {
          if (item.user_id === null) {
            item.roles.splice(NewIndex, 1);
            this._notice.create('error', this._service.translateValue(this.translateName + '该员工没有操作帐号，不可分配权限'), '');
          } else {
            item.roles[NewIndex] = {
              role_id: role.id,
              role_name: role.name,
              priority: 0,
            };
          }
        }
        item.handleRoleChanged();
        item.partent?.handleEmployeeRolesChanged();
      }
    };
  }
  employeeRoleDrop(item: assignRoleEmployeeItem) {
    return (event: SortableEvent) => {
      item.handleRoleChanged();
    };
  }

  employeeRoleRemove(item: assignRoleEmployeeItem, index: number) {
    item.roles.splice(index, 1);
    item.handleRoleChanged();
    item.partent.handleEmployeeRolesChanged();
  }
  departmentRoleOnAdd(item: assignRoleDepartmentItem) {
    return (event: SortableEvent) => {
      const NewIndex = item.roles.findIndex((role) => (role as any).id);
      if (NewIndex !== -1) {
        const role: RoleNameListModel = JSON.parse(JSON.stringify(item.roles[NewIndex]));
        const shadowRole = {
          role_id: role.id,
          role_name: role.name,
          priority: 0,
        };
        if (item.employees.length === 0) {
          item.roles.splice(NewIndex, 1);
          this._notice.error(this._service.translateValue(this.translateName + '该部门下没有员工, 无法添加权限'), '');
          return;
        }
        if (item.employees.length === item.invalidEmployeeCount) {
          item.roles.splice(NewIndex, 1);
          this._notice.error(this._service.translateValue(this.translateName + '该部门下所有员工均未配置账户, 无法添加权限'), '');
          return;
        }
        if (item.roles.some((listRole) => listRole.role_id === role.id)) {
          item.roles.splice(NewIndex, 1);
          this._notice.error(this._service.translateValue(this.translateName + '该权限已存在'), '');
        } else {
          item.roles[NewIndex] = shadowRole;
          const RawRole = JSON.stringify(shadowRole);
          item.employees.forEach((employee) => {
            if (employee.user_id !== null) {
              if (employee.roles.some((role) => role.role_id === shadowRole.role_id)) {
                // do nothing
              } else {
                employee.roles.push(JSON.parse(RawRole));
                employee.handleRoleChanged();
              }
            }
          });
        }
      }
    };
  }
  showDepartmentRoleRemoveModal(item: assignRoleDepartmentItem, role: assignRoleModel) {
    this.deleteRoleName = role;
    this.deleteDepartment = item;
    this.deleteModal = this._modal.create({
      nzCentered: true,
      nzBodyStyle: {
        padding: '0px 8px 8px 8px',
      },
      nzStyle: {
        'border-radius': '16px',
      },
      nzWidth: '320px',
      nzWrapClassName: 'modal-outer',
      nzMaskClosable: false,
      nzTitle: this.deleteHeadTemplate,
      nzContent: this.deleteTemplate,
      nzFooter: null,
    });
  }
  departmentRoleRemove(item: assignRoleDepartmentItem, role: assignRoleModel) {
    item.roles.splice(
      item.roles.findIndex((roleItem) => roleItem.role_id === role.role_id),
      1
    );
    item.employees.forEach((employee) => {
      employee.roles.splice(
        employee.roles.findIndex((roleItem) => roleItem.role_id === role.role_id),
        1
      );
      employee.handleRoleChanged();
    });
    this.deleteModal?.close();
  }
  // employeeRoleDrop(event: CdkDragDrop<assignRoleModel[]>) {
  //   console.log(event);
  //   if (event.previousContainer === event.container) {
  //     moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
  //     // moveItemInArray(roleList, event.previousIndex, event.currentIndex);
  //   }
  // }

  // 配置账号
  onConfigAccount(item: any) {
    this._router.navigate(['hr/org']);
    this._spUtil.putObject('orgKey', { code: item.code, name: item.name, id: item.id });
  }
}
