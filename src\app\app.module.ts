import { HttpClient, HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { LOCALE_ID, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { NotFoundPageComponent } from './portal/not-found-page/not-found-page.component';
import { NZ_DATE_LOCALE, NZ_I18N } from 'ng-zorro-antd/i18n';
import { zh_CN } from 'ng-zorro-antd/i18n';
import { registerLocaleData } from '@angular/common';
import zh from '@angular/common/locales/zh';
import { FormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { HttpInterceptorService } from './shared/http-interceptor.service';
import { RouteReuseStrategy } from '@angular/router';
import { TableHelperModule } from './services/table-helper/table-helper.module';
import { NzNotificationModule } from 'ng-zorro-antd/notification';

import { zhCN } from 'date-fns/locale';
import { OwnModalModule } from './shared/modal.service';
import { SortablejsModule } from 'ngx-sortablejs';
import { AppCommonModule } from './core/common/common.module';
import { environment } from '../environments/environment';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import { AppResolverService } from './app.resolver';
import { TINYMCE_SCRIPT_SRC } from '@tinymce/tinymce-angular';
import { FlcRouteReuseStrategy } from 'fl-common-lib';

registerLocaleData(zh, 'zh-cn');
@NgModule({
  declarations: [AppComponent, NotFoundPageComponent],
  imports: [
    BrowserModule,
    AppRoutingModule,
    HttpClientModule,
    TranslateModule.forRoot({
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new MultiTranslateHttpLoader(http, [
            { prefix: './assets/i18n/', suffix: '.json' },
            { prefix: './assets/i18n/common/', suffix: '.json' },
            { prefix: './assets/i18n/components/', suffix: '.json' },
            { prefix: './assets/i18n/flss-component/', suffix: '.json' },
          ]);
        },
        deps: [HttpClient],
      },
    }),
    FormsModule,
    BrowserAnimationsModule,
    TableHelperModule,
    NzNotificationModule,
    OwnModalModule,
    AppCommonModule,
    SortablejsModule.forRoot({ animation: 150 }),
  ],
  providers: [
    // { provide: NZ_I18N, useValue: zh_CN },
    // { provide: LOCALE_ID, useValue: 'zh-cn' },
    // { provide: NZ_DATE_LOCALE, useValue: zhCN },
    { provide: HTTP_INTERCEPTORS, useClass: HttpInterceptorService, multi: true },
    { provide: RouteReuseStrategy, useClass: FlcRouteReuseStrategy },
    { provide: 'environment', useValue: environment },
    AppResolverService,
    { provide: TINYMCE_SCRIPT_SRC, useValue: '/assets/js/tinymce/tinymce.min.js' },
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
