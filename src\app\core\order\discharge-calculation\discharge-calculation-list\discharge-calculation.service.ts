import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';

@Injectable()
export class DischargeCalculationService {
  constructor(private http: HttpClient) {}

  /**
   * 大货单号、客户货号、客户名称下拉
   * @returns Observable
   */
  getHeadSelect(): Observable<any> {
    return this.http.get('/order_subordinate?table_name=bulk_order');
  }

  /**
   * 款式分类下拉
   * @returns Observable
   */
  getMaterialCascade(): Observable<any> {
    return this.http.get('/material_cascade?table_name=bulk_order');
  }

  /**
   * 状态
   * @returns Observable
   */
  getStatusSelect(): Observable<any> {
    return this.http.post<any>('/code_table', { types: ['LAYOUT_CONS_STATUS'] });
  }

  /**
   * 订单排料列表
   * @param  {} payload
   * @returns Observable
   */
  getList(payload: any): Observable<any> {
    return this.http.post<any>('/service/procurement-inventory/layoutcons/v1/list', payload);
  }

  // 分配算料员
  batchSubmitMaterialsEmployeeOption(payload: any) {
    return this.http.post<any>('/service/procurement-inventory/layoutcons/v1/dist', payload);
  }

  getDepartmentOption() {
    return this.http.get<any>('/service/archive/v1/api/organization/basic_option');
  }
}
