import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FlcColorSizeTableCellV2, FlcColorSizeTableChangeEvent } from 'fl-common-lib';
import { startOfDay } from 'date-fns';
import { ProductionTableService } from '../production-table.service';
import { groupBy, uniqBy } from 'lodash';
import { ProductionTableOrderLineInfo } from '../production-table.config';

@Component({
  selector: 'app-production-table-color-spec-dialog',
  templateUrl: './production-table-color-spec-dialog.component.html',
  styleUrls: ['./production-table-color-spec-dialog.component.scss'],
})
export class ProductionTableColorSpecDialogComponent implements OnInit {
  @Input() bulkOrderId!: number;
  @Input() factory_id!: number;
  @Input() rawNodeList!: rawNodeItem[];
  @Input() orderInfo!: ProductionTableOrderLineInfo;
  @Output() onSave = new EventEmitter<rawNodeItem[] | null>();
  displayNodeList: DisplayNodeItem[] = [];
  orderColorSpecDetail: RawColorSpecItem[] = [];
  get TotalQty(): number {
    let qty = 0;
    this.displayNodeList.forEach((item) => {
      qty += item.totalQty;
    });
    return qty;
  }
  translateName = 'ProductionTableTemplateFiled.';
  basicInfoConfig: any = [
    { label: '订单需求号', key: 'bulk_order_code' },
    { label: '款式编码', key: 'style_code' },
    { label: '加工厂', key: 'factory_name' },
  ];

  constructor(private _service: ProductionTableService) {}

  ngOnInit(): void {
    this.rawNodeList.forEach((item) => {
      this.displayNodeList.push(new DisplayNodeItem(item));
    });
    this.getColorSpecDetail();
  }
  getColorSpecDetail() {
    this._service.getFactoryColorSizeQty({ factory_id: this.factory_id, bulk_order_id: this.bulkOrderId }).subscribe((result) => {
      if (result.code === 200) {
        this.orderColorSpecDetail = result.data.color_size_list;
        const specList = uniqBy(
          this.orderColorSpecDetail.map((item) => ({ spec_id: item.spec_id, spec_size: item.spec_size })),
          'spec_id'
        );
        const colorList = groupBy(this.orderColorSpecDetail, 'color_id');
        Object.values(colorList).forEach((color) => {
          if (color.length !== specList.length) {
            const targetColor = color[0];
            const hasSpec = color.map((item) => item.spec_id);
            const without = specList.filter((item) => !hasSpec.includes(item.spec_id));
            without.forEach((item) => {
              this.orderColorSpecDetail.push({
                color_id: targetColor.color_id,
                color_name: targetColor.color_name,
                ...item,
                qty: 0,
                isEmpty: true,
              });
            });
          }
        });
        if (this.displayNodeList.length === 0) {
          const newOne = new DisplayNodeItem();
          newOne.raw_line_list = newOne.line_list = this.orderColorSpecDetail.map((item) => ({
            color_id: item.color_id,
            color_name: item.color_name,
            size_id: item.spec_id,
            size_name: item.spec_size,
            color_size_id: -1,
            num: item.qty,
            extraParams: { cellEditable: !item.isEmpty },
          }));
          this.displayNodeList.push(newOne);
        } else {
          this.displayNodeList.forEach((item) => {
            if (item.isSingleLine) {
              item.raw_line_list = item.line_list = this.orderColorSpecDetail.map((item) => ({
                color_id: item.color_id,
                color_name: item.color_name,
                size_id: item.spec_id,
                size_name: item.spec_size,
                color_size_id: -1,
                num: item.qty,
              }));
            }
          });
        }
      }
    });
  }
  cancel() {
    this.onSave.emit();
  }
  save() {
    this.onSave.emit(this.displayNodeList.map((item) => item.toJson()));
  }
  tableChange(currentItem: DisplayNodeItem, event: FlcColorSizeTableChangeEvent) {
    currentItem.line_list = event.tableData as FlcColorSizeTableCellV2[];
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  shadowTotalQtyChange(_currentItem: DisplayNodeItem, event: any) {
    // nothing
  }
  addTime(index: number) {
    const newOne = new DisplayNodeItem();
    newOne.raw_line_list = newOne.line_list = this.orderColorSpecDetail.map((item) => ({
      color_id: item.color_id,
      color_name: item.color_name,
      size_id: item.spec_id,
      size_name: item.spec_size,
      color_size_id: -1,
      num: item.qty,
      extraParams: { cellEditable: !item.isEmpty },
    }));
    this.displayNodeList.splice(index + 1, 0, newOne);
  }
  removeTime(index: number) {
    this.displayNodeList.splice(index, 1);
  }
  onUploadImage(curreentItem: DisplayNodeItem, event: ImageItem[]) {
    curreentItem.pictures = event;
  }
  onDeleteImage(curreentItem: DisplayNodeItem, event: ImageItem[]) {
    curreentItem.pictures = event;
  }
}
interface rawNodeItem {
  finished_time: number;
  pictures: { name: string; url: string; version: string }[];
  line_list: NodeInfoNodeDetail[];
}
interface NodeInfoNodeDetail {
  color_id: number;
  color_name: string;
  spec_id: number;
  spec_size: string;
  qty: number;
}
class DisplayNodeItem {
  constructor(input?: rawNodeItem) {
    if (input) {
      if (input.line_list.length > 0) {
        const first = input.line_list[0];
        if (input.line_list.length === 1 && first.color_id === 0 && first.spec_id === 0) {
          this.shadowTotalQty = first.qty;
        } else {
          this.raw_line_list = this.line_list = input.line_list.map((item) => ({
            ...item,
            color_size_id: -1,
            size_id: item.spec_id,
            size_name: item.spec_size,
            num: item.qty,
          }));
        }
      }
      this.finished_time = new Date(input.finished_time);
      this.pictures = input.pictures;
    } else {
      this.finished_time = new Date();
    }
  }
  get isSingleLine(): boolean {
    return this.shadowTotalQty !== null && this.shadowTotalQty > 0;
  }
  get totalQty(): number {
    if (this.isSingleLine) {
      return this.shadowTotalQty ?? 0;
    }
    let qty = 0;
    this.line_list.forEach((line) => (qty += line.num ?? 0));
    return qty;
  }
  shadowTotalQty: number | null = null;
  raw_line_list: FlcColorSizeTableCellV2[] = [];
  line_list: FlcColorSizeTableCellV2[] = [];
  finished_time: Date;
  pictures: ImageItem[] = [];
  toJson(): rawNodeItem {
    return {
      finished_time: startOfDay(this.finished_time).getTime(),
      pictures: this.pictures,
      line_list: this.isSingleLine
        ? [
            {
              color_id: 0,
              color_name: '',
              spec_id: 0,
              spec_size: '',
              qty: this.shadowTotalQty ?? 0,
            },
          ]
        : this.line_list.map((item) => ({
            color_id: item.color_id,
            color_name: item.color_name,
            spec_id: item.size_id,
            spec_size: item.size_name,
            qty: item.num ?? 0,
          })),
    };
  }
}
interface ImageItem {
  name: string;
  url: string;
  version: string;
}
interface RawColorSpecItem {
  color_id: number;
  color_name: string;
  qty: number;
  spec_id: number;
  spec_size: string;
  isEmpty?: boolean;
}
