<app-search-container
  #header
  [headerTitle]="translateName + '操作用户' | translate"
  [btnTpl]="btnTpl"
  (reset)="reset()"
  (handleFold)="resize()">
  <div *ngFor="let item of searchList">
    <span class="search-name">{{ translateName + item.label | translate }}：</span>
    <ng-container *ngIf="item.code == 'status'; else others">
      <nz-select
        style="min-width: 96px"
        [nzPlaceHolder]="'placeholder.select' | translate"
        nzAllowClear
        [(ngModel)]="searchData.status"
        (ngModelChange)="onSearch()">
        <nz-option *ngFor="let op of item.options" [nzValue]="op?.value" [nzLabel]="translateName + op?.label | translate"></nz-option>
      </nz-select>
    </ng-container>
    <ng-template #others>
      <app-dynamic-search-select
        [cpnMode]="item.code == 'roles' ? 'multiple' : 'default'"
        [dataUrl]="'/user/search'"
        [transData]="item.code == 'gen_user' ? { value: 'id', label: 'name' } : undefined"
        [(ngModel)]="searchData[item.code]"
        [column]="item.code"
        (ngModelChange)="onSearch()">
      </app-dynamic-search-select>
    </ng-template>
  </div>
  <div>
    <span class="search-name">{{ translateName + '创建日期' | translate }}：</span>
    <nz-range-picker [(ngModel)]="searchData.gen_time" (ngModelChange)="onChange()"></nz-range-picker>
  </div>
</app-search-container>
<ng-template #btnTpl>
  <button *ngIf="btnArr.includes('settings:user-create')" (click)="detail('new')" nz-button flButton="pretty-primary" nzShape="round">
    <i nz-icon [nzIconfont]="'icon-xinjian1'"></i>
    {{ 'btn.add' | translate }}
  </button>
</ng-template>
<div style="height: 14px"></div>
<div>
  <nz-table
    class="zebra-striped-table"
    [nzFrontPagination]="false"
    [nzScroll]="{ y: tableHeight + 'px' }"
    [(nzPageSize)]="pageSize"
    nzTableLayout="fixed"
    [nzLoading]="loading"
    [(nzPageIndex)]="pageIndex"
    [nzShowTotal]="totalTemplate"
    [nzTotal]="total"
    (nzPageIndexChange)="onChangePageIndex()"
    (nzPageSizeChange)="onSearch()"
    [nzPageSizeOptions]="[20, 30, 40, 50]"
    [nzData]="tableData"
    nzShowSizeChanger
    nzSize="middle"
    nzBordered="ture">
    <thead>
      <tr>
        <th nzLeft nzWidth="32px">#</th>
        <ng-container *ngFor="let item of renderHeaders">
          <th
            [nzLeft]="item.disable || item.pinned"
            nz-resizable
            *ngIf="item.visible"
            nzPreview
            [nzWidth]="item.width"
            [nzMaxWidth]="360"
            [nzMinWidth]="56"
            (nzResizeEnd)="onResize($event, item.label)">
            {{ translateName + item.label | translate }}
            <app-sort-btn
              [(sortOrder)]="gen_time"
              *ngIf="item.key === 'gen_time'"
              (sortOrderChange)="sortOrderChange($event, item.key)"></app-sort-btn>
            <app-sort-btn
              [(sortOrder)]="modified_time"
              *ngIf="item.key === 'modified_time'"
              (sortOrderChange)="sortOrderChange($event, item.key)"></app-sort-btn>
            <app-sort-btn
              [(sortOrder)]="last_login_time"
              *ngIf="item.key === 'last_login_time'"
              (sortOrderChange)="sortOrderChange($event, item.key)"></app-sort-btn>
            <nz-resize-handle nzDirection="right">
              <div class="resize-trigger"></div>
            </nz-resize-handle>
          </th>
        </ng-container>
        <th nzRight nzWidth="90px">
          {{ translateName + '操作' | translate }}
          <a class="setting-btn" [ngStyle]="{ color: btnHighLight ? '#4D96FF' : '' }" (click)="changeHeader($event)">
            <i nz-icon nzType="setting" nzTheme="fill"></i>
          </a>
        </th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let item of tableData; let i = index" [ngStyle]="{ background: item.id === id ? '#EEF6FF' : '' }">
        <td [ngStyle]="{ background: item.id === id ? '#EEF6FF' : '' }" nzLeft>{{ i + 1 }}</td>
        <ng-container *ngFor="let col of renderHeaders">
          <td
            [ngStyle]="{ background: item.id === id ? '#EEF6FF' : '' }"
            [nzLeft]="col.disable || col.pinned"
            [ngClass]="col.key === 'status' && !item[col.key] ? 'stop-use' : ''"
            *ngIf="col.visible">
            <component-table-body-render
              [width]="col.width"
              [data]="col.key === 'status' ? (translateName + (item[col.key] ? '启用' : '停用') | translate) : item[col.key]"
              [type]="col.type"
              [template]="getTemplate(col.template, i)">
            </component-table-body-render>
          </td>
        </ng-container>
        <td *ngIf="btnArr.includes('settings:user-detail')" [ngStyle]="{ background: item.id === id ? '#EEF6FF' : '' }" nzRight>
          <a nz-button nzType="link" (click)="detail(item.id)"> {{ translateName + '详情' | translate }} </a>
        </td>
        <ng-template #roles>
          <ng-container *ngIf="item.roles && item.roles.length > 0; else nullLength">
            <app-text-truncated [template]="titleTemplate"></app-text-truncated>
            <ng-template #titleTemplate>
              <span class="roles" *ngFor="let con of item.roles">
                {{ con.role_name }}
              </span>
            </ng-template>
          </ng-container>
          <ng-template #nullLength>-</ng-template>
        </ng-template>
      </tr>
    </tbody>
  </nz-table>
  <ng-template #totalTemplate>
    <div
      style="margin-right: 16px; color: #000000; font-size: 14px"
      [innerHTML]="
        translateName + '共x条,第x页'
          | translate
            : {
                total: total,
                currentPage: pageIndex,
                totalPage: total / pageSize | mathCeil
              }
      "></div>
  </ng-template>
</div>
