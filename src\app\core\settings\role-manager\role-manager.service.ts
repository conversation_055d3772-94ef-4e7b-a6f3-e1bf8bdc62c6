import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ResponseData, ResponseDataList } from '../../common/interface/http';
import {
  RoleLogModel,
  RoleNameChildModel,
  RoleNameItemModel,
  RoleNameListModel,
  RoleNameListSearchOptionModel,
  RoleNameModel,
} from './role-manager.model';
import { TranslateService } from '@ngx-translate/core';
@Injectable({
  providedIn: 'root',
})
export class RoleManagerService {
  constructor(private _http: HttpClient, private _translateService: TranslateService) {}
  translateValue(key: string, param?: any) {
    return this._translateService.instant(key, param);
  }
  getRoleCode() {
    return this._http.get<ResponseData<string>>('/role/new-code');
  }
  addRoleName(data: RoleNameModel) {
    return this._http.post<ResponseData<number>>('/role', data);
  }
  copyRoleName(data: RoleNameModel) {
    return this._http.post<ResponseData<number>>('/role/copy', data);
  }
  editRoleName(data: RoleNameModel) {
    return this._http.put<ResponseData<number>>('/role', data);
  }
  editRoleNameDetail(id: number, data: RoleNameChildModel[]) {
    return this._http.put<ResponseData<boolean>>(`/role/setting/${id}`, data);
  }
  getRoleUsers(id: number) {
    return this._http.get<ResponseData<{ id: number; name: string }[]>>(`/role/users/${id}`);
  }
  deleteRoleName(id: number) {
    return this._http.delete<ResponseData<number>>(`/role/${id}`);
  }
  getSelectOption(data: RoleNameListSearchOptionModel<'name' | 'code' | 'gen_time_min' | 'gen_time_max' | 'gen_user' | 'status'>) {
    return this._http.post<
      ResponseData<{
        datalist: any[];
        count: number;
      }>
    >('/role/list/search', data);
  }
  getLogSelectOption(data: RoleNameListSearchOptionModel<'gen_user' | 'role_name' | 'operation'>) {
    return this._http.post<
      ResponseData<{
        datalist: any[];
        count: number;
      }>
    >('/role/log/search', data);
  }
  getRoleNameList(data: {
    page: number;
    limit: number;
    name?: string;
    code?: string;
    gen_time_min?: Date;
    gen_time_max?: Date;
    status?: boolean | number;
    order_by?: string;
    gen_user_id?: number[];
  }) {
    return this._http.post<ResponseDataList<RoleNameListModel>>('/role/list/all', data);
  }
  getRoleNameDetail(id: number) {
    return this._http.get<ResponseData<RoleNameItemModel>>(`/role/${id}`);
  }
  getRoleLog(data: {
    page: number;
    limit: number;
    where: { gen_time: string[]; gen_user: string; operation: string; role_name: string };
    orderBy: string[];
  }) {
    return this._http.post<ResponseDataList<RoleLogModel>>('/role/log/list', data);
  }
  getDefaultRole() {
    return this._http.get('/role/default');
  }
  getAllDepartmentList() {
    return this._http.get<
      ResponseData<
        {
          id: number;
          name: string;
        }[]
      >
    >('/role/depts/all');
  }
  // updateRoleNameStatus(data: RoleNameModel) {
  //   return this._http.put<ResponseData<number>>('/role/status', data);
  // }
}
