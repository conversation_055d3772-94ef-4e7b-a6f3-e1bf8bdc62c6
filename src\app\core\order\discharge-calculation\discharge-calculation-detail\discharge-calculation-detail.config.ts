export const bomInfoTabelHeaderTitles = [
  {
    label: '部位',
    key: 'part',
    type: 'template',
    template: '',
    required: false,
    visible: true,
    pinned: true,
    disable: true,
    isHidePin: true,
    width: '100px',
  },
  {
    label: '物料编码',
    key: 'material_code',
    type: 'template',
    template: '',
    required: false,
    visible: true,
    pinned: true,
    disable: true,
    isHidePin: true,
    width: '100px',
  },
  {
    label: '物料名称',
    key: 'material_name',
    type: 'template',
    template: '',
    required: true,
    visible: true,
    pinned: false,
    disable: true,
    isHidePin: true,
    width: '150px',
  },
  {
    label: '参与排料',
    key: 'with_breakdown',
    type: 'template',
    template: '',
    required: false,
    visible: true,
    pinned: false,
    disable: true,
    isHidePin: true,
    width: '100px',
  },
  {
    label: '按段长采购',
    key: 'by_seg_len',
    type: 'template',
    template: '',
    required: false,
    visible: true,
    pinned: false,
    disable: true,
    isHidePin: true,
    width: '100px',
  },
  {
    label: '适用尺码',
    key: 'match_specs',
    type: 'template',
    template: '',
    required: true,
    visible: true,
    pinned: false,
    disable: true,
    isHidePin: true,
    width: '120px',
  },
  {
    label: '毛门幅(厘米)',
    key: 'raw_width',
    type: 'template',
    template: '',
    required: false,
    visible: true,
    pinned: false,
    disable: true,
    isHidePin: true,
    width: '100px',
  },
  {
    label: '克重',
    key: 'weight',
    type: 'text',
    template: '',
    required: false,
    visible: true,
    pinned: false,
    disable: true,
    isHidePin: true,
    width: '120px',
  },
  {
    label: '物料颜色',
    key: 'material_color_id',
    type: 'template',
    template: '',
    required: true,
    visible: true,
    pinned: false,
    disable: false,
    isHidePin: true,
    width: '160px',
  },
  {
    label: '成分',
    key: 'comp',
    type: 'template',
    template: '',
    required: false,
    visible: true,
    pinned: false,
    disable: false,
    isHidePin: true,
    width: '80px',
  },
  // {
  //   label: '规格',
  //   key: 'spec',
  //   type: 'template',
  //   template: '',
  //   required: false,
  //   visible: true,
  //   pinned: false,
  //   disable: false,
  //   isHidePin: true,
  //   width: '160px',
  // },

  {
    label: '平均用料(打)',
    key: 'doz_cons',
    type: 'template',
    template: '',
    required: false,
    visible: true,
    pinned: false,
    disable: false,
    isHidePin: true,
    width: '120px',
  },
  {
    label: '单耗',
    key: 'piece_cons',
    type: 'template',
    template: '',
    required: false,
    visible: true,
    pinned: false,
    disable: false,
    isHidePin: true,
    width: '120px',
  },
  {
    label: '已排/未排(件)',
    key: 'done_qty',
    type: 'template',
    template: '',
    required: false,
    visible: true,
    pinned: false,
    disable: false,
    isHidePin: true,
    width: '120px',
  },
  {
    label: '排料状态',
    key: 'layout_status',
    type: 'template',
    template: '',
    required: false,
    visible: true,
    pinned: false,
    disable: false,
    isHidePin: true,
    width: '120px',
  },
  {
    label: '采购总数',
    key: 'purchase_amount',
    type: 'template',
    template: '',
    required: false,
    visible: true,
    pinned: false,
    disable: false,
    isHidePin: true,
    width: '120px',
  },
  {
    label: '单位',
    key: 'unit_code',
    type: 'template',
    template: '',
    required: false,
    visible: true,
    pinned: false,
    disable: false,
    isHidePin: true,
    width: '120px',
  },
];
