import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DischargeCalculationRoutingModule } from 'src/app/core/order/discharge-calculation/discharge-calculation-routing.module';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { DischargeCalculationListComponent } from 'src/app/core/order/discharge-calculation/discharge-calculation-list/discharge-calculation.component';
import { NzCascaderModule } from 'ng-zorro-antd/cascader';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzResizableModule } from 'ng-zorro-antd/resizable';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzFormModule } from 'ng-zorro-antd/form';
import { DischargeCalculationDetailComponent } from 'src/app/core/order/discharge-calculation/discharge-calculation-detail/discharge-calculation-detail.component';
import { PipesModule } from '../../../pipes/pipes.module';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { FlcComponentsModule, FlcDirectivesModule, FlcDrawerHelperService, FlcOssUploadService } from 'fl-common-lib';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import { FlButtonModule } from 'fl-ui-angular/button';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { MaterialPackageModule, MaterialPackageService } from 'fl-sewsmart-lib/material-package';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { DepartmentSelectComponent } from 'src/app/core/order/discharge-calculation/department-select/department-select.component';
import { NzTreeSelectModule } from 'ng-zorro-antd/tree-select';
import { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NotpassReasonComponent } from './notpass-reason/notpass-reason.component';
import { DischargeCommonService } from './discharge-calculation-common.service';
const nzModules = [
  FlcComponentsModule,
  NzTableModule,
  NzSelectModule,
  NzCascaderModule,
  NzDatePickerModule,
  NzDrawerModule,
  NzToolTipModule,
  NzButtonModule,
  NzTabsModule,
  NzResizableModule,
  NzModalModule,
  NzFormModule,
  NzIconModule,
  NzInputModule,
  NzSwitchModule,
  NzInputNumberModule,
  FlButtonModule,
  NzPopconfirmModule,
  NzDividerModule,
  NzRadioModule,
  NzTreeSelectModule,
  NzAutocompleteModule,
  FlcDirectivesModule,
  NzCheckboxModule,
];
@NgModule({
  declarations: [DischargeCalculationListComponent, DischargeCalculationDetailComponent, DepartmentSelectComponent, NotpassReasonComponent],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    DischargeCalculationRoutingModule,
    ...nzModules,
    PipesModule,
    MaterialPackageModule,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new MultiTranslateHttpLoader(http, [
            { prefix: './assets/i18n/basic-archive/material/', suffix: '.json' },
            { prefix: './assets/i18n/material-selector/', suffix: '.json' },
            { prefix: './assets/i18n/flss-component/', suffix: '.json' },
            { prefix: './assets/i18n/data-pkg/', suffix: '.json' },
          ]);
        },
        deps: [HttpClient],
      },
    }),
  ],
  providers: [FlcDrawerHelperService, MaterialPackageService, FlcOssUploadService, DischargeCommonService],
})
export class DischargeCalculationModule {
  constructor(public translateService: TranslateService) {
    const cl = translateService.currentLang;
    translateService.currentLang = '';
    translateService.use(cl);
  }
}
