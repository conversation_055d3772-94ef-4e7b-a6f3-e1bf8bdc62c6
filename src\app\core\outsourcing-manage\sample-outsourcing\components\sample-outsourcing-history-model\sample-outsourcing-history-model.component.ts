import { Component, ElementRef, Input, OnInit } from '@angular/core';
import { FlcDrawerHelperService } from 'fl-common-lib';
import { SampleOutsourcingService } from '../../sample-outsourcing.service';
import { IDetailModel } from '../../modal/sample-outsourcing.interface';

@Component({
  selector: 'app-sample-outsourcing-history-model',
  templateUrl: './sample-outsourcing-history-model.component.html',
  styleUrls: ['./sample-outsourcing-history-model.component.scss'],
})
export class SampleOutsourcingHistoryModelComponent implements OnInit {
  @Input() id!: number; //打样外发单id
  versions: { current_version: boolean; id: number; version: string }[] = [];
  height: number = window.innerHeight * 0.8;
  currentVersion?: { current_version: boolean; id: number; version: string };
  currentTime = '';
  currentDetail: Partial<IDetailModel> = {};
  selectIndex = 0;

  constructor(private _element: ElementRef, private _service: SampleOutsourcingService, private _drawerHelp: FlcDrawerHelperService) {}

  ngOnInit(): void {
    this.getVersionList();
  }
  ngAfterViewInit() {
    this.scroll();
  }

  /**
   * 滚动到当前位置
   */
  scroll() {
    const parent = this._element.nativeElement.querySelector('.his-time');
    const el = this._element.nativeElement.querySelector('.now');
    if (el) {
      // 滚动到当前元素
      parent.scrollTop = el.offsetTop;
    }
  }

  /**
   * 切换tab
   * @param index
   */
  onTab(item: any, index: number) {
    this.selectIndex = index;
    this.currentVersion = item;
    this.getDetailInfoByVersion(item.id);
  }

  /**
   * 版本list
   */
  private getVersionList() {
    this._service.getVersions(this.id).subscribe((res: any) => {
      if (res.code === 200 && res.data?.length) {
        this.versions = res.data;
        this.currentVersion = this.versions[0];
        this.currentTime = this.currentVersion?.version;
        this.selectIndex = 0;
        this.currentVersion && this.getDetailInfoByVersion(this.currentVersion.id);
      }
    });
  }

  private getDetailInfoByVersion(id: number) {
    this._service.getHistoryDetail(id).subscribe((res) => {
      if (res.code === 200) this.currentDetail = res.data;
    });
  }

  // 关闭弹窗
  closeDrawer() {
    this._drawerHelp.closeAllDrawer();
  }
}
