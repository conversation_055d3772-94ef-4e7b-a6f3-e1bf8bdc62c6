<div class="wrap">
  <ng-container *ngIf="!isRangeMode">
    <div style="display: flex; justify-content: center; background-color: #f0f2f5; padding-top: 10px">
      <div class="titleBar">
        {{ roleItem.name }}
      </div>
    </div>
    <div class="board">
      <ng-container *ngFor="let child of roleItem.children">
        <div class="secondMenu" *ngIf="!child.isLastMenu">
          <div class="Title">
            <label nz-checkbox [(ngModel)]="child.value" [name]="child.name">{{ child.name }}</label>
          </div>
          <div class="buttonContent">
            <ng-container *ngFor="let btn of child.children">
              <div *ngIf="btn.visible">
                {{ btn.name }}
              </div>
            </ng-container>
          </div>
        </div>
        <div class="lastMenu" *ngIf="child.isLastMenu">
          <app-role-line-last-menu-line
            #menuLineRefs
            [showALLDetail]="showALLDetail"
            (statusChange)="statusChange.emit($event)"
            (checkedChange)="checkedChange.emit($event)"
            [isEdit]="isEdit"
            [child]="child">
          </app-role-line-last-menu-line>
        </div>
      </ng-container>
    </div>
  </ng-container>

  <ng-container *ngIf="isRangeMode && !roleItem.isHideForRangeMode && roleItem.hasSelectedMenu">
    <div style="display: flex; justify-content: center; background-color: #f0f2f5; padding-top: 10px">
      <div class="titleBar">
        {{ roleItem.name }}
      </div>
    </div>
    <div class="board">
      <ng-container *ngFor="let child of roleItem.children">
        <div class="secondMenu" *ngIf="!child.isLastMenu">
          <!-- <div class="Title">
            <label nz-checkbox [(ngModel)]="child.value" [name]="child.name">{{ child.name }}</label>
          </div>
          <div class="buttonContent">
            <ng-container *ngFor="let btn of child.children">
              <div *ngIf="btn.visible">
                {{ btn.name }}
              </div>
            </ng-container>
          </div> -->
        </div>
        <div class="lastMenu rangeMenu" *ngIf="child.isLastMenu && child.value">
          <app-role-line-last-menu-range-line
            [isSaveError]="isSaveError"
            [showALLDetail]="showALLDetail"
            (statusChange)="statusChange.emit($event)"
            (checkedChange)="checkedChange.emit($event)"
            [isEdit]="isEdit"
            [allDepartmentList]="allDepartmentList"
            [child]="child">
          </app-role-line-last-menu-range-line>
        </div>
      </ng-container>
    </div>
  </ng-container>
</div>
