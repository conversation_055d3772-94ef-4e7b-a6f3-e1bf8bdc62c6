<nz-spin [nzSpinning]="loading">
  <div class="material-plan-detail-wrap">
    <div class="title">{{ translateLabel + '基本信息' | translate }}</div>
    <div nz-row>
      <div *ngFor="let item of basicInfoConfig" nz-col [nzSpan]="item.nzSpan">
        <nz-form-item style="margin-bottom: 16px">
          <nz-form-label class="label-wrapper">
            <span class="label-text">{{ item.label }}</span>
          </nz-form-label>
          <nz-form-control class="controls-text">
            <ng-container *ngIf="item.key === 'season_id'">
              <flc-text-truncated [data]="detailData?.[item.key] ? seasonObj[detailData?.[item.key]] : ''"></flc-text-truncated>
            </ng-container>

            <ng-container *ngIf="item.key === 'market_time'">
              <flc-text-truncated [data]="detailData?.[item.key] | date: 'yyyy/MM/dd'"></flc-text-truncated>
            </ng-container>

            <ng-container *ngIf="item.key === 'month'">
              <flc-text-truncated [data]="detailData?.[item.key] ? detailData?.[item.key] + '月' : ''"></flc-text-truncated>
            </ng-container>

            <ng-container *ngIf="!['season_id', 'market_time', 'month'].includes(item.key)">
              <flc-text-truncated [data]="detailData?.[item.key]"></flc-text-truncated>
            </ng-container>
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>

    <div class="title">{{ translateLabel + '物料品类结构规划' | translate }}</div>
    <nz-table
      #outterTable
      nzBordered
      class="outter-table zebra-striped-table"
      [nzData]="detailData?.category_plan_list || []"
      [nzShowPagination]="false"
      [nzScroll]="{ x: 'auto', y: '300px' }"
      [nzNoResult]="notDataTpl">
      <thead>
        <tr>
          <th nzLeft nzWidth="40px"></th>
          <th nzLeft nzWidth="40px">#</th>
          <ng-container *ngFor="let th of outterHeader">
            <th [nzWidth]="th.width">{{ translateLabel + th.label | translate }}</th>
          </ng-container>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let item of outterTable.data; index as i">
          <tr>
            <td nzLeft [(nzExpand)]="item.expand">
              <i
                *ngIf="item.sub?.length"
                nz-tooltip
                [nzTooltipTitle]="(item.expand ? '收起子表' : '展开子表') | translate"
                nz-icon
                nzType="caret-right"
                class="transition-icon"
                nzTheme="outline"
                [ngClass]="item.expand ? 'transform90' : ''"
                (click)="item.expand = !item.expand"></i>
            </td>
            <td nzLeft>{{ i + 1 }}</td>
            <ng-container *ngFor="let td of outterHeader">
              <td>
                <flc-text-truncated *ngIf="td.type === 'template'" [data]="td.formatter(item)"></flc-text-truncated>
                <flc-text-truncated *ngIf="td.type !== 'template'" [data]="item?.[td.key]"></flc-text-truncated>
              </td>
            </ng-container>
          </tr>

          <!-- 子表 -->
          <tr [nzExpand]="item.expand || false">
            <nz-table
              #innerTable
              class="inner-table zebra-striped-table"
              [nzData]="item.sub"
              [nzShowPagination]="false"
              [nzScroll]="{ x: 'auto' }"
              [nzNoResult]="notDataTpl">
              <thead>
                <tr>
                  <th *ngFor="let th of innerHeader" [nzWidth]="th.width">{{ translateLabel + th.label | translate }}</th>
                </tr>
              </thead>
              <tbody>
                <ng-container *ngFor="let plan of innerTable.data; index as j">
                  <tr>
                    <td *ngFor="let td of innerHeader">
                      <flc-text-truncated [data]="plan?.[td.key]"></flc-text-truncated>
                    </td>
                  </tr>
                </ng-container>
              </tbody>
            </nz-table>
          </tr>
        </ng-container>
      </tbody>
    </nz-table>

    <div class="title">{{ translateLabel + '开发时间节点规划' | translate }}</div>
    <nz-table
      #timePlanNodeTable
      class="zebra-striped-table"
      [nzData]="detailData?.time_plan_list || []"
      [nzShowPagination]="false"
      [nzScroll]="{ x: 'auto' }"
      [nzNoResult]="notDataTpl"
      nzBordered>
      <thead>
        <tr>
          <th nzLeft nzWidth="40px">#</th>
          <th *ngFor="let th of timeNodeHeader" [nzWidth]="th.width">{{ translateLabel + th.label | translate }}</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let node of timePlanNodeTable.data; index as nodeIndex">
          <td>{{ nodeIndex + 1 }}</td>
          <td *ngFor="let td of timeNodeHeader">
            <ng-container *ngIf="td.key === 'date_range'">
              <flc-text-truncated
                [data]="
                  node?.start_time ? (node?.start_time | date: 'yyyy/MM/dd') + '-' + (node?.end_time | date: 'yyyy/MM/dd') : ''
                "></flc-text-truncated>
            </ng-container>
            <ng-container *ngIf="td.key !== 'date_range'">
              <flc-text-truncated [data]="node?.[td.key]"></flc-text-truncated>
            </ng-container>
          </td>
        </tr>
      </tbody>
    </nz-table>

    <div class="title">{{ translateLabel + '灵感来源' | translate }}</div>
    <div class="inspiration-source">
      <ng-container *ngIf="imageLeftList?.length || imageRightList?.length; else notDataTpl">
        <div class="image-title-pannel">
          <div class="image-title" *ngIf="imageLeftList?.length">{{ translateLabel + '灵感图稿' | translate }}:</div>
          <div class="image-title" *ngIf="imageRightList?.length">{{ translateLabel + '面料小样参考图' | translate }}:</div>
        </div>

        <div class="drop-content">
          <div class="drop-container" #LeftContainer [ngStyle]="{ height: 'auto' }" *ngIf="imageLeftList.length">
            <ng-container *ngIf="imageLeftList.length > 0; else emptyTemplate">
              <div *ngFor="let item of imageLeftList; let i = index">
                <div class="drop-box">
                  <ng-container>
                    <img [src]="item.url" alt="" (click)="onPreview(imageLeftList, i)" />
                    <div class="action-line">
                      <span style="flex: 1" (click)="onDownLoadImage(item)">{{ translateAction + '下载' | translate }}</span
                      >|
                      <span style="flex: 1" (click)="onPreview(imageLeftList, i)">{{ translateAction + '预览' | translate }}</span>
                    </div>
                  </ng-container>
                </div>
              </div>
            </ng-container>
          </div>

          <div class="drop-container" #rightContainer [ngStyle]="{ height: 'auto' }" *ngIf="imageRightList.length">
            <ng-container *ngIf="imageRightList.length; else emptyTemplate">
              <div *ngFor="let item of imageRightList; let i = index">
                <div class="drop-box">
                  <ng-container>
                    <img [src]="item.url" alt="" (click)="onPreview(imageRightList, i)" />
                    <div class="action-line">
                      <span style="flex: 1" (click)="onDownLoadImage(item)">{{ translateAction + '下载' | translate }}</span
                      >|
                      <span style="flex: 1" (click)="onPreview(imageRightList, i)">{{ translateAction + '预览' | translate }}</span>
                    </div>
                  </ng-container>
                </div>
              </div>
            </ng-container>
          </div>
        </div>
      </ng-container>

      <ng-template #emptyTemplate>
        <div class="no-data">
          <flc-no-data> </flc-no-data>
        </div>
      </ng-template>

      <div class="image-describe">
        <div>
          {{ translateLabel + '备注' | translate }}: <span style="padding-left: 5px">{{ detailData?.image_remark || '-' }}</span>
        </div>
      </div>
    </div>
  </div>
</nz-spin>

<ng-template #notDataTpl>
  <div style="margin: 16px 0">
    <flc-no-data></flc-no-data>
  </div>
</ng-template>
