import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { FlcLeaveGuard } from 'fl-common-lib';
import { SecProcessOutsourcingListComponent } from './sec-process-outsourcing-list/sec-process-outsourcing-list.component';
import { SecProcessOutsourcingDetailComponent } from './sec-process-outsourcing-detail/sec-process-outsourcing-detail.component';
const routes: Routes = [
  {
    path: 'list',
    component: SecProcessOutsourcingListComponent,
    data: {
      keepAlive: true,
    },
  },
  {
    path: 'list/:id',
    component: SecProcessOutsourcingDetailComponent,
    canDeactivate: [FlcLeaveGuard],
  },
  { path: '', redirectTo: 'list' },
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class OrderSecProcessOutsourcingRoutingModule {}
