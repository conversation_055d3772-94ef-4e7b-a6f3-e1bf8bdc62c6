<div class="proofing-task-list">
  <div #header [hidden]="isFold" style="margin-bottom: 4px">
    <flc-search-container [showBtnContainer]="false">
      <div *ngFor="let item of searchOptions">
        <span class="search-name">{{ translateSuffix + 'sampleOutsourcingTable.' + item.label | translate }}：</span>

        <ng-container *ngIf="item.type === 'select'">
          <flc-dynamic-search-select
            [dataUrl]="searchOptionUrl"
            [column]="item.column || ''"
            [placeSelect]="'placeholder.select' | translate"
            [optAlwaysReload]="true"
            [(ngModel)]="searchData[item.valueKey]"
            (ngModelChange)="onSearch()">
          </flc-dynamic-search-select>
        </ng-container>

        <nz-input-group *ngIf="item.type === 'input'" [nzSuffix]="inputClearTpl">
          <input
            flcInputTrim
            nz-input
            [(ngModel)]="searchData[item.valueKey]"
            [placeholder]="'flss.placeholder.input' | translate"
            [flcDebounceEvent]="onSearch.bind(this)"
            [controlTime]="500" />
          <ng-template #inputClearTpl>
            <i
              *ngIf="searchData[item.valueKey]"
              nz-icon
              class="ant-input-clear-icon"
              nzTheme="fill"
              nzType="close-circle"
              (click)="searchData[item.valueKey] = null; onSearch()"></i>
          </ng-template>
        </nz-input-group>

        <ng-container *ngIf="item.type === 'cascader'">
          <nz-cascader
            nzAllowClear
            [nzShowSearch]="true"
            nzLabelProperty="title"
            nzValueProperty="key"
            [nzOptions]="styleList"
            [nzPlaceHolder]="'placeholder.select' | translate"
            [(ngModel)]="searchData[item.valueKey]"
            (nzVisibleChange)="openCascader($event)"
            (nzSelectionChange)="onSearch()">
          </nz-cascader>
        </ng-container>

        <ng-container *ngIf="item.type === 'date'">
          <nz-range-picker
            [nzPlaceHolder]="[
              translateSuffix + 'sampleOutsourcingPage.开始' | translate,
              translateSuffix + 'sampleOutsourcingPage.结束' | translate
            ]"
            [(ngModel)]="searchData[item.valueKey]"
            (ngModelChange)="onSearch()">
          </nz-range-picker>
        </ng-container>
      </div>
    </flc-search-container>
  </div>

  <div class="table-box">
    <div class="check-box-search" #quickStatus>
      <flc-order-status-checkbox shape="round" [checkData]="statusOptions" (onCheckChange)="onStatusCheck($event)">
      </flc-order-status-checkbox>
      <div class="btn-box">
        <button
          nz-button
          flButton="border-reset"
          nz-tooltip
          [nzTooltipTitle]="'btn.reset' | translate"
          (click)="onRest()"
          style="margin-right: 8px">
          <i nz-icon [nzIconfont]="'icon-zhongzhi1'"></i>
        </button>
        <button
          nz-button
          flToggleButton
          (toggleActiveChange)="onFold($event)"
          [toggleActive]="!isFold"
          nz-tooltip
          [nzTooltipPlacement]="'top'"
          [nzTooltipTitle]="foldTooltip | translate"
          style="margin: 0">
          <i nz-icon [nzIconfont]="'icon-shaixuan-xuanzhong'"></i>
        </button>

        <ng-container *ngIf="permission.includes('dev-manage:sample-outsourcing-save')">
          <nz-divider nzType="vertical"></nz-divider>
          <button nz-button [nzShape]="'round'" flButton="pretty-primary" (click)="onCreate()">
            <i nz-icon [nzIconfont]="'icon-xinjian1'"></i>{{ 'btn.add' | translate }}
          </button>
        </ng-container>
      </div>
    </div>

    <flc-table
      [tableHeader]="tableHeader"
      [tableConfig]="tableConfig"
      [template]="TdTemplate"
      (sizeChanges)="sizeChanges($event)"
      (indexChanges)="indexChanges($event)"
      (sortDataLists)="sortOrderChange($event)"
      (getDetails)="jumpDetail($event)">
      <ng-template #TdTemplate let-data="data">
        <ng-container *ngIf="data.isTd">
          <!-- 款式分类 -->
          <flc-text-truncated *ngIf="data.key === 'first_style_name'" [data]="categoryFormatter(data.item)"></flc-text-truncated>
          <!-- 打样轮次 -->
          <flc-text-truncated *ngIf="data.key === 'sample_sort_num'" [data]="sampleSortNumFormatter(data.item)"></flc-text-truncated>
        </ng-container>
      </ng-template>
    </flc-table>
  </div>
</div>
