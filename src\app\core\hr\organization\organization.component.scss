.organization-outer {
  display: flex;
  align-items: stretch;
  justify-content: stretch;
  gap: 12px;
  height: 100%;

  .tree-container {
    display: flex;
    align-items: stretch;
    justify-content: stretch;
    background: #ffffff;
    border-radius: 4px;
    padding: 8px 0px;
    width: 25%;

    app-structure-tree {
      width: 100%;
    }
  }

  .content-container {
    width: 75%;
    background: transparent;
  }
}
