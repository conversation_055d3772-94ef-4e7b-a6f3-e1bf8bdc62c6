import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { OrderTrackingListComponent } from './order-tracking-list/order-tracking-list.component';

const routes: Routes = [
  {
    path: 'list',
    component: OrderTrackingListComponent,
    data: {
      keepAlive: true,
    },
  },
  { path: '', redirectTo: 'list' },
];

@NgModule({ imports: [RouterModule.forChild(routes)], exports: [RouterModule] })
export class OrderTrackingRoutingModule {}
