import { Component, Input, OnInit, TemplateRef } from '@angular/core';
import { TableCompoentType } from 'src/app/services/table-helper/table-helper.service';

@Component({
  selector: 'component-table-body-render',
  template: `
    <ng-container *ngIf="type === 'text'">{{ data | noValue }}</ng-container>
    <ng-container *ngIf="type === 'price'">
      <component-table-body-render-price [data]="data"></component-table-body-render-price>
    </ng-container>
    <ng-container *ngIf="type === 'quantity'">
      <component-table-body-render-quantity [data]="data"></component-table-body-render-quantity>
    </ng-container>
    <ng-container *ngIf="type === 'image'">
      <component-table-body-render-image [data]="data"></component-table-body-render-image>
    </ng-container>
    <ng-container *ngIf="type === 'date'">
      <component-table-body-render-date [type]="type" [data]="data"></component-table-body-render-date>
    </ng-container>
    <ng-container *ngIf="type === 'datetime'">
      <component-table-body-render-date [type]="type" [data]="data"></component-table-body-render-date>
    </ng-container>
    <ng-container *ngIf="type === 'template'">
      <ng-container *ngTemplateOutlet="template"></ng-container>
    </ng-container>
  `,
  styles: [''],
})
export class TableBodyRenderComponent implements OnInit {
  @Input() data?: any;
  @Input() type?: TableCompoentType;
  @Input() width = '100px';
  @Input() template: TemplateRef<HTMLElement> | null = null;
  imgWidth = 100;
  constructor() {}

  ngOnInit(): void {
    // if (this.type === 'text') {
    // }
    // if (this.type === 'image') {
    //   // nz-image的width文档里写的是string，但实际使用是number才能生效
    //   const width = Number.parseInt(this.width.split('px')[0]);
    //   this.imgWidth = width - 18;
    // }
  }
}
