.TitleBar {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  font-weight: 500;
  color: #222b3c;
  line-height: 20px;
}

.buttonContent {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  background-color: #fff;
  margin: 8px 0;
  border: 1px solid #e7e7e7;
  padding: 12px 20px;
  border-radius: 4px;
  &.permission-item {
    padding: 12px 4px 12px 12px;
    .header {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .left,
      .right {
        display: flex;
        align-items: center;
      }
      .title {
        display: flex;
        align-items: center;
        &::before {
          content: '';
          display: block;
          width: 4px;
          height: 12px;
          background: #138aff;
          border-radius: 4px;
          margin-right: 4px;
        }
      }
      .link {
        font-weight: 500;
        font-size: 14px;
        color: #138aff;
        text-decoration-line: underline;
        text-underline-offset: 2px;
        cursor: pointer;
      }

      .tips {
        font-weight: 500;
        font-size: 12px;
        color: #97999c;
        margin-right: 8px;
      }
    }
    .content {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      margin-top: 8px;
      row-gap: 8px;
      &.flex-3 {
        width: 100%;
        > div {
          display: flex;
          flex-wrap: nowrap;
          white-space: nowrap;
          padding-right: 4px;
          width: 33%;
          ::ng-deep {
            .ant-checkbox-wrapper {
              overflow: hidden;
            }
            .ant-checkbox + span {
              display: inline-block;
              overflow: hidden;
              text-overflow: ellipsis;
              padding-right: 6px;
            }
          }

          .toggle-tag {
            white-space: nowrap;
            padding: 2px 5px;
            background: #e7f3fe;
            border-radius: 4px;
            font-weight: 500;
            font-size: 12px;
            color: #138aff;
            cursor: pointer;
            user-select: none;
            &.read {
              background: #d9f7ef;
              color: #00c790;
            }
          }
        }
      }
    }
  }
}

:host .isDisabled {
  ::ng-deep {
    i {
      color: #d5d7dc !important;
    }

    span:last-child {
      color: #222b3c;
    }
  }
}

.field-permission-box {
  margin-top: 10px;
  .field-radio-box {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }
}
