<div class="wrap" [ngClass]="{ isSelected: data.isSelected }" style="height: '52px'">
  <div class="checkbox">
    <label nz-checkbox [(ngModel)]="data.isSelected" (ngModelChange)="onSelectLine.emit(data)"></label>
  </div>

  <div class="factory-name">
    <flc-text-truncated [data]="data.factory_name"></flc-text-truncated>
    <div
      *ngIf="data.rawData.lock_user_id && !isEdit"
      class="editing-tag"
      nz-tooltip
      [nzTooltipTitle]="data.rawData.lock_user_name + '正在编辑中，同一时间只能由一个人编辑~'">
      编辑中
    </div>
  </div>
  <div class="line-name">
    <flc-text-truncated [data]="data.production_line_name"></flc-text-truncated>
    <div *ngIf="data.unPublish" class="un-publish">待发布</div>
  </div>
</div>
