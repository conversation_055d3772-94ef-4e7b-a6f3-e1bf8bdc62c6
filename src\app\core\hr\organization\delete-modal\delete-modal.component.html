<ng-template #titleTpl>
  <div class="delete-title">
    <ng-container *ngIf="is_employee">
      {{ 'delete-modal.delete-employee' | translate }}
    </ng-container>
    <ng-container *ngIf="!is_employee">
      {{ 'delete-modal.delete-depart' | translate }}
    </ng-container>
  </div>
</ng-template>

<ng-template #contentTpl>
  <div class="delete-content">
    <div *ngIf="!is_employee" class="delete-notice">{{ 'delete-modal.delete-notice' | translate }}</div>
    <div class="delete-msg" [ngStyle]="{ 'margin-top': is_employee ? '0px' : '45px' }">
      <i nz-icon [nzIconfont]="'icon-jinggao'"></i>
      <div style="white-space: normal; word-break: break-all">{{ 'delete-modal.sure-delete' | translate: { name: data?.name } }}</div>
    </div>
  </div>
  <div class="delete-footer">
    <button nz-button flButton="default" nzShape="round" [disableOnClick]="1000" (click)="handleCancel()">
      {{ 'btn.cancel' | translate }}
    </button>
    <button nz-button flButton="default" nzShape="round" [disableOnClick]="3000" (click)="handleOk()">
      {{ 'btn.ok' | translate }}
    </button>
  </div>
</ng-template>
