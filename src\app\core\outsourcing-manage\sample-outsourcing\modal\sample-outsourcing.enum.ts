// 打样外发单状态
export enum SampleOutsouringStatusEnum {
  new = 0, // 新建
  wait_submit = 1, // 待提交
  wait_audit = 2, // 待审核
  wait_modify = 3, // 待修改
  wait_order = 4, //待接单
  wait_outsourcing_send = 5, //待收样
  wait_outsourcing_sended = 6, //待收样-已寄样
  processed = 7, // 已收样
  cancelled = -1, // 已取消
}

// 打样单操作按钮
export enum SampleOutsouringOperateBtnEnum {
  cancel = 'cancel', // 取消
  back = 'back', // 返回
  save = 'save', // 暂存
  edit = 'edit', // 编辑
  commit = 'commit', // 提交
  modify = 'modify', // 退回修改
  pass = 'pass', // 审核通过
  cancelSample = 'cancelSample', // 取消外发
  receive = 'receive', //样衣收货
  delete = 'delete', // 删除
}

// 打样外发进度
export enum SampleOutsouringStepsEnum {
  accept_order = 9, //"接单
  assign_printer = 2, //"分配版师"
  print_complete = 4, //"打版完成"
  assign_dresser = 5, //"分配样衣工"
  cloth_complete = 7, //"样衣制成
  post_sample = 10, //"样衣寄出
  accept_sample = 11, //样衣收货
}
