<div class="wrap">
  <div class="roleNameDetailTitleBar">
    <span class="title">{{ selectedItem.name }}</span>
    <span class="bar">
      <span [ngClass]="{ actived: !isRangeMode }" (click)="toggleRangMode(false)">{{ translateName + '功能权限' | translate }}</span>
      <nz-divider style="background-color: #d8d8d8" nzType="vertical"></nz-divider>
      <span [ngClass]="{ actived: isRangeMode }" (click)="toggleRangMode(true)">{{ translateName + '管理范围' | translate }}</span>
    </span>
  </div>
  <div class="roleNameDetailFirstMenu">
    <nz-radio-group [(ngModel)]="selectedFirstMenu">
      <ng-container *ngFor="let item of firstMenuList">
        <ng-container *ngIf="!(isRangeMode && item.isHideForRangeMode)">
          <label nz-radio-button [nzValue]="item" [nzDisabled]="isRangeMode && !item.hasSelectedMenu">
            {{ item.name }}
          </label>
        </ng-container>
      </ng-container>
    </nz-radio-group>
  </div>
  <div class="roleNameDetailMenuContent">
    <ng-container *ngIf="!(isRangeMode && selectedFirstMenu?.isHideForRangeMode)">
      <div *ngFor="let item of selectedFirstMenu?.children">
        <!-- <ng-container *ngIf="item.value"> -->
        <ng-container *ngIf="!item.isLastMenu">
          <ng-container *ngTemplateOutlet="notLastMenu; context: { $implicit: item }"></ng-container>
        </ng-container>
        <ng-container *ngIf="item.isLastMenu">
          <app-assign-role-detail-line
            [allDepartmentList]="allDepartmentList"
            [item]="item"
            [isRangeMode]="isRangeMode"></app-assign-role-detail-line>
        </ng-container>
        <!-- </ng-container> -->
      </div>
    </ng-container>
  </div>
</div>
<ng-template #notLastMenu let-item>
  <!-- <ng-container *ngIf="item.value"> -->
  <ng-container *ngFor="let child of item.children">
    <ng-container *ngIf="!child.isLastMenu">
      <ng-container *ngTemplateOutlet="notLastMenu; context: { $implicit: child }"></ng-container>
    </ng-container>
    <ng-container *ngIf="child.isLastMenu">
      <app-assign-role-detail-line
        [allDepartmentList]="allDepartmentList"
        [item]="child"
        [isRangeMode]="isRangeMode"></app-assign-role-detail-line>
    </ng-container>
  </ng-container>
  <!-- </ng-container> -->
</ng-template>
