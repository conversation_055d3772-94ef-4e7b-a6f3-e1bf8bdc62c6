<ng-template #titleTpl>
  <div class="drawer-title">{{ (isAdd ? 'employee-drawer.新建员工' : 'employee-drawer.编辑员工') | translate }}</div>
</ng-template>

<ng-template #contentTpl>
  <div>
    <form nz-form *ngIf="employeeForm" [formGroup]="employeeForm" [ngClass]="lang === 'en' ? 'content-form-en' : 'content-form'">
      <nz-form-item>
        <nz-form-label nzRequired>
          {{ 'employee-drawer.员工姓名' | translate }}
        </nz-form-label>
        <nz-form-control [nzErrorTip]="nameTpl">
          <nz-textarea-count [nzMaxCharacterCount]="factoryCodes.includes(userInfo?.factory_code) ? 100 : 16" class="inline-count">
            <textarea
              rows="1"
              nz-input
              formControlName="name"
              [placeholder]="placeInput"
              [maxLength]="factoryCodes.includes(userInfo?.factory_code) ? 100 : 16"
              nzAutosize
              inputTrim></textarea>
          </nz-textarea-count>
        </nz-form-control>
        <ng-template #nameTpl>
          <ng-container *ngIf="employeeForm?.get('name')?.dirty && employeeForm?.get('name')?.hasError('required')">
            {{ 'placeholder.input' | translate }}{{ 'employee-drawer.员工姓名' | translate }}
          </ng-container>
        </ng-template>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label nzRequired>
          {{ 'employee-drawer.工号' | translate }}
        </nz-form-label>
        <nz-form-control [nzErrorTip]="codeTpl">
          <nz-textarea-count [nzMaxCharacterCount]="16" class="inline-count">
            <textarea rows="1" nz-input formControlName="code" [placeholder]="placeInput" [maxLength]="16" nzAutosize inputTrim></textarea>
          </nz-textarea-count>
        </nz-form-control>
        <ng-template #codeTpl>
          <ng-container *ngIf="employeeForm?.get('code')?.dirty && employeeForm?.get('code')?.hasError('required')">
            {{ 'placeholder.input' | translate }}{{ 'employee-drawer.工号' | translate }}
          </ng-container>
          <ng-container *ngIf="employeeForm?.get('code')?.dirty && employeeForm?.get('code')?.hasError('duplicated')">
            {{ 'employee-drawer.工号' | translate }}{{ 'form-error.is-exit' | translate }}
          </ng-container>
          <ng-container *ngIf="employeeForm?.get('code')?.pending">
            {{ 'employee-drawer.工号' | translate }}{{ 'form-error.check-pending' | translate }}
          </ng-container>
          <ng-container *ngIf="employeeForm.get('code')?.dirty && employeeForm.get('code')?.hasError('pattern')">
            {{ 'form-error.chinese-characters' | translate }}
          </ng-container>
        </ng-template>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label nzRequired>
          {{ 'employee-drawer.状态' | translate }}
        </nz-form-label>
        <nz-form-control [nzErrorTip]="statusTpl">
          <nz-select formControlName="status" [nzPlaceHolder]="placeSelect">
            <ng-container *ngFor="let item of statusOptions">
              <nz-option [nzValue]="item.value" [nzLabel]="'employee-drawer.' + item.label | translate"> </nz-option>
            </ng-container>
          </nz-select>
        </nz-form-control>
        <ng-template #statusTpl>
          <ng-container *ngIf="employeeForm?.get('status')?.dirty && employeeForm?.get('status')?.hasError('required')">
            {{ 'placeholder.select' | translate }}{{ 'employee-drawer.状态' | translate }}
          </ng-container>
        </ng-template>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label>
          {{ 'employee-drawer.操作账号' | translate }}
        </nz-form-label>
        <nz-form-control>
          <nz-select formControlName="user_id" [nzPlaceHolder]="placeSelect" nzShowSearch nzAllowClear [nzDropdownMatchSelectWidth]="false">
            <ng-container *ngFor="let item of accountOptions">
              <nz-option [nzValue]="item.value" [nzLabel]="item.label" nzCustomContent>
                {{ item.label + ' / ' + item.name }}
              </nz-option>
            </ng-container>
          </nz-select>
        </nz-form-control>
        <button *ngIf="_service.btnArr.includes('settings:user-create')" nz-button nzType="link" (click)="createAccount()">
          {{ 'employee-drawer.新建账号' | translate }}
        </button>
      </nz-form-item>
    </form>
    <div class="footer-btn">
      <button nz-button flButton="default" nzShape="round" [disableOnClick]="1000" (click)="handleCancel()">
        {{ 'btn.cancel' | translate }}
      </button>
      <button nz-button nzType="primary" nzShape="round" [disableOnClick]="3000" (click)="handleOk()">
        {{ 'btn.ok' | translate }}
      </button>
    </div>
  </div>
</ng-template>
<app-account-drawer (handleReturn)="handleReturn()" (handleAccountOk)="handleAccountOk($event)"></app-account-drawer>
