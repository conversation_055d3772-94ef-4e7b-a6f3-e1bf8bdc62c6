import { Component, EventEmitter, Input, OnInit, Output, TemplateRef, ElementRef, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-search-container',
  templateUrl: './search-container.component.html',
  styleUrls: ['./search-container.component.scss'],
})
export class SearchContainerComponent implements OnInit {
  @ViewChild('headerBox') headerBox?: ElementRef;
  @Input() btnTpl!: TemplateRef<void>;
  @Output() reset = new EventEmitter<any>();
  @Input() isFold = false;
  @Input() toolPlace = 'top';
  @Input() showFoldBtn = true;
  @Output() handleFold = new EventEmitter<any>();
  isHeaderTitleString!: boolean;
  _headerTitle!: string;
  _headerTplTitle!: TemplateRef<void>;
  resetTooltip!: string;
  foldTooltip!: string;

  @Input()
  get headerTitle(): string {
    return this._headerTitle;
  }

  set headerTitle(val: string | TemplateRef<void>) {
    this.isHeaderTitleString = !(val instanceof TemplateRef);
    if (this.isHeaderTitleString) {
      this._headerTitle = <string>val;
    } else {
      this._headerTplTitle = <TemplateRef<void>>val;
    }
  }

  get headerTplTitle(): TemplateRef<void> {
    return this._headerTplTitle;
  }

  constructor(public _translateService: TranslateService) {}

  ngOnInit(): void {
    this.resetTooltip = this._translateService.instant('btn.reset');
    this.foldTooltip = this._translateService.instant('btn.fold');
  }

  fold() {
    this.isFold = !this.isFold;
    this.foldTooltip = this.isFold ? this._translateService.instant('btn.unfold') : this._translateService.instant('btn.fold');
    setTimeout(() => {
      this.handleFold.emit();
    });
  }
}
