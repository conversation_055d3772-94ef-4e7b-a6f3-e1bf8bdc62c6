.outer-container {
  display: flex;
  flex-direction: column;

  .search-item-container {
    display: flex;
    align-items: center;

    .search-name::after {
      content: '：';
    }
  }
}

.status {
  padding: 2px 7px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-text1 {
  color: #40a2ff;
  font-weight: 500;
  background-color: #e1f0ff;
}

.status-text2 {
  color: #ff971f;
  font-weight: 500;
  background-color: #fff1e1;
}

.status-text3 {
  color: #515661;
  font-weight: 500;
  background-color: #f1f3f6;
}

.operate-btn {
  color: #138aff;
  font-weight: 500;
}

.blue-link-text {
  text-decoration: underline;
  text-underline-offset: 3px;
  color: #138aff;
  cursor: pointer;
  &:hover {
    opacity: 0.8;
  }
}

.red-link-text {
  text-decoration: underline;
  text-underline-offset: 3px;
  color: #f96d6d;
  cursor: pointer;
  &:hover {
    opacity: 0.8;
  }
}

.red {
  color: #f96d6d;
}

:host ::ng-deep {
  .total-row.ant-table-row td {
    background-color: #f3faff !important;
  }

  .total-row.ant-table-row {
    position: sticky;
    bottom: 0;
    z-index: 2;
    td {
      background-color: #f3faff;
      border-top: 1px solid #d4d7dc;
    }
  }

  .due_times {
    display: flex;
    flex-direction: column;
    span {
      height: 20px;
      line-height: 20px;
    }
  }
}
