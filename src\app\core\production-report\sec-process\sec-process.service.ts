import { HttpClient } from '@angular/common/http';
import { EventEmitter, Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable()
export class SecProcessService {
  constructor(private http: HttpClient) {}
  public translateEventEmitter = new EventEmitter<void>();
  public serviceUrl = '/service/order/v1/bi-elan';

  getSecProcessList(payload: any): Observable<any> {
    return this.http.post(`${this.serviceUrl}/outsource-list`, payload);
  }

  getSecProcessDetail(payload: any): Observable<any> {
    return this.http.post(`${this.serviceUrl}/outsource-detail`, payload);
  }

  getOutsourcePackageProgressOptions(payload: any): Observable<any> {
    return this.http.get(`${this.serviceUrl}/outsource-package-progress-options`, payload);
  }

  getOutsourcePackageProgress(payload: any): Observable<any> {
    return this.http.get(`${this.serviceUrl}/outsource-package-progress`, payload);
  }

  getExtraProcess(): Observable<any> {
    return this.http.get('/service/order/v1/extra_process');
  }
}
