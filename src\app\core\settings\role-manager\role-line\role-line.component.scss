.wrap {
  background-color: #fff;

  .titleBar {
    position: absolute;
    background-color: #f0f2f5;
    text-align: center;
    width: 146px;
    font-size: 14px;
    font-weight: 500;
    color: #84879a;
    line-height: 20px;
    padding: 3px;
    transform: translateY(-10px);
    border-bottom-left-radius: 100px;
    border-bottom-right-radius: 100px;
  }
}

.board {
  padding-left: 12px;
  padding-right: 12px;
  padding-top: 20px;
  padding-bottom: 16px;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  column-gap: 10px;
  row-gap: 10px;

  .lastMenu {
    display: flex;
    flex-direction: column;
    background-color: #f7f7f7;
    border-radius: 4px;
    width: 480px;
    padding: 8px 8px 0;
  }

  .rangeMenu {
    width: 440px;
  }

  .secondMenu {
    display: flex;
    column-gap: 8px;
    flex-direction: column;
    width: 100%;

    .Title {
      font-size: 16px;
      font-weight: 500;
      color: #222b3c;
      line-height: 22px;
    }

    .buttonContent {
      display: flex;
      flex-wrap: wrap;
    }
  }
}
