import { DatePipe } from '@angular/common';
import { Pipe, PipeTransform } from '@angular/core';
import { isNotNil } from 'ng-zorro-antd/core/util';

@Pipe({ name: 'dateEmpty' })
export class DateEmptyPipe implements PipeTransform {
  constructor(private _datePipe: DatePipe) {}
  transform(value: any, format?: string): string {
    if (isNotNil(value) && value !== '') {
      if (value === 0) {
        return '-';
      } else {
        return this._datePipe.transform(value, format) ?? '-';
      }
    } else {
      return '-';
    }
  }
}
