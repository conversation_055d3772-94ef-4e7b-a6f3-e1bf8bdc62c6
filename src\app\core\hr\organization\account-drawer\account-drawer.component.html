<ng-template #titleTpl>
  <div class="drawer-title">{{ 'account-drawer.新建操作账号' | translate }}</div>
</ng-template>

<ng-template #contentTpl>
  <div>
    <form nz-form *ngIf="accountForm" [formGroup]="accountForm" [ngClass]="lang === 'en' ? 'content-form-en' : 'content-form'">
      <nz-form-item>
        <nz-form-label nzRequired>
          {{ 'account-drawer.用户名称' | translate }}
        </nz-form-label>
        <nz-form-control [nzErrorTip]="nameTpl">
          <nz-textarea-count [nzMaxCharacterCount]="factoryCodes.includes(userInfo?.factory_code) ? 100 : 16" class="inline-count">
            <textarea
              rows="1"
              nz-input
              formControlName="name"
              [placeholder]="placeInput"
              [maxLength]="factoryCodes.includes(userInfo?.factory_code) ? 100 : 16"
              nzAutosize
              inputTrim></textarea>
          </nz-textarea-count>
        </nz-form-control>
        <ng-template #nameTpl>
          <ng-container *ngIf="accountForm?.get('name')?.dirty && accountForm?.get('name')?.hasError('required')">
            {{ 'placeholder.input' | translate }}{{ 'account-drawer.用户名称' | translate }}
          </ng-container>
          <ng-container *ngIf="accountForm.get('name')?.dirty && accountForm.get('name')?.hasError('pattern')">
            {{ 'form-error.special-characters' | translate }}
          </ng-container>
        </ng-template>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label nzRequired>
          {{ 'account-drawer.用户编码' | translate }}
        </nz-form-label>
        <nz-form-control [nzErrorTip]="codeTpl">
          <nz-textarea-count [nzMaxCharacterCount]="16" class="inline-count">
            <textarea
              rows="1"
              nz-input
              formControlName="login_name"
              [placeholder]="placeInput"
              [maxLength]="16"
              nzAutosize
              inputTrim></textarea>
          </nz-textarea-count>
        </nz-form-control>
        <ng-template #codeTpl>
          <ng-container *ngIf="accountForm?.get('login_name')?.dirty && accountForm?.get('login_name')?.hasError('required')">
            {{ 'placeholder.input' | translate }}{{ 'account-drawer.用户编码' | translate }}
          </ng-container>
          <ng-container *ngIf="accountForm?.get('login_name')?.dirty && accountForm?.get('login_name')?.hasError('duplicated')">
            {{ 'account-drawer.用户编码' | translate }}{{ 'form-error.is-exit' | translate }}
          </ng-container>
          <ng-container *ngIf="accountForm?.get('login_name')?.pending">
            {{ 'account-drawer.用户编码' | translate }}{{ 'form-error.check-pending' | translate }}
          </ng-container>
          <ng-container *ngIf="accountForm.get('login_name')?.dirty && accountForm.get('login_name')?.hasError('pattern')">
            {{ 'form-error.special-chinese-characters' | translate }}
          </ng-container>
        </ng-template>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label nzRequired>
          {{ 'account-drawer.初始密码' | translate }}
        </nz-form-label>
        <nz-form-control [nzErrorTip]="statusTpl">
          <nz-input-group [nzSuffix]="suffixTemplate">
            <input
              [type]="passwordVisible ? 'text' : 'password'"
              nz-input
              [placeholder]="placeInput"
              formControlName="password"
              [maxLength]="16"
              inputTrim />
          </nz-input-group>
          <ng-template #suffixTemplate>
            <i nz-icon [nzType]="passwordVisible ? 'eye' : 'eye-invisible'" (click)="passwordVisible = !passwordVisible"></i>
          </ng-template>
        </nz-form-control>
        <ng-template #statusTpl>
          <ng-container *ngIf="accountForm?.get('password')?.dirty && accountForm?.get('password')?.hasError('required')">
            {{ 'placeholder.select' | translate }}{{ 'account-drawer.初始密码' | translate }}
          </ng-container>
        </ng-template>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label nzRequired>
          {{ 'account-drawer.用户状态' | translate }}
        </nz-form-label>
        <nz-form-control>
          <nz-switch formControlName="status"></nz-switch>
        </nz-form-control>
      </nz-form-item>
    </form>
    <nz-divider nzDashed style="margin-top: 0px"></nz-divider>
    <div class="priority-content">
      <div class="priority-header">
        <div class="priority-head">{{ 'account-drawer.优先级' | translate }}</div>
        <div class="priority-subcontent-head">{{ 'account-drawer.权限分配' | translate }}</div>
        <div class="drag-notice">{{ 'account-drawer.drag-notice' | translate }}</div>
      </div>
      <div #roleTable style="max-height: 225px; overflow: auto">
        <div cdkDropList class="priority-body" (cdkDropListDropped)="drop($event)">
          <div class="priority-box" *ngFor="let role of priorityLists; index as i" cdkDrag>
            <div class="priority-num">
              {{ i + 1 }}
            </div>
            <div class="priority-subcontent">
              <app-dynamic-search-select
                style="width: 100%; max-width: 175px"
                [(ngModel)]="role.role_id"
                [column]="'roles'"
                [dataUrl]="'/user/role/list'"
                [transData]="{ value: 'role_id', label: 'role_name' }">
              </app-dynamic-search-select>
              <button
                nz-button
                nzType="link"
                (click)="deletePriority(i)"
                [ngStyle]="{ color: priorityLists?.length === 1 ? '#C4C9D5' : '#fe5d56' }"
                [disabled]="priorityLists?.length === 1">
                <i nz-icon [nzIconfont]="'icon-jianshao1'"></i>
              </button>
              <button nz-button nzType="link" style="color: #4d96ff" (click)="addPriority(i)">
                <i nz-icon [nzIconfont]="'icon-zengjia1'"></i>
              </button>
            </div>
            <div class="priority-handle" cdkDragHandle>
              <i nz-icon nzType="drag" nzTheme="outline"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="footer-btn">
      <button nz-button flButton="default" nzShape="round" [disableOnClick]="1000" (click)="handleCancel()">
        {{ 'btn.return-step' | translate }}
      </button>
      <button nz-button nzType="primary" nzShape="round" [disableOnClick]="3000" (click)="handleOk()">
        {{ 'btn.ok' | translate }}
      </button>
    </div>
  </div>
</ng-template>
