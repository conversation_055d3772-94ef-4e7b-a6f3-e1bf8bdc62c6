import { ConnectionPositionPair, Overlay, OverlayRef } from '@angular/cdk/overlay';
import { ComponentPortal } from '@angular/cdk/portal';
import {
  ChangeDetectorRef,
  Component,
  ComponentRef,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { SubMenuBarOverlayComponentComponent } from '../sub-menu-bar-overlay-component/sub-menu-bar-overlay-component.component';
import { MenuItem } from 'fl-common-lib';
import { Router } from '@angular/router';

@Component({
  selector: 'app-side-bar',
  templateUrl: './side-bar.component.html',
  styleUrls: ['./side-bar.component.scss'],
})
export class SideBarComponent implements OnInit {
  @ViewChild('menuDom') menuDom!: ElementRef;
  @Output() refreshCollapsedStatus = new EventEmitter();
  @Input() menu!: MenuItem[];
  @Input() isCollapsed = false;
  overlayRef?: OverlayRef;
  subMenuComponentRef?: ComponentRef<SubMenuBarOverlayComponentComponent>;
  private debounceTimer?: ReturnType<typeof setTimeout>;

  private readonly debounceTime = 150;

  lang = localStorage.getItem('lang') || 'zh';

  constructor(private overlayService: Overlay, private renderer: Renderer2, private _router: Router) {}

  ngOnInit(): void {}

  getSubMenuCount(sub: MenuItem) {
    return sub.children?.filter((v) => {
      return v.value;
    }).length;
  }

  resetMenu() {
    const selectedItems: HTMLElement[] = this.menuDom.nativeElement.querySelectorAll('.ant-menu-item');
    selectedItems.forEach((d: HTMLElement) => {
      d.blur();
    });
  }
  clearCurrent() {
    this.menu.forEach((first) => {
      first.isCurrent = false;
      first.children?.forEach((sub) => (sub.isCurrent = false));
    });
  }
  resetCurrent() {
    const urls = this._router.url.split('/');
    this.menu.filter((sub) => {
      sub.isCurrent = false;
      sub.children?.forEach((leaf) => {
        leaf.isCurrent = false;
        const leafPaths = leaf.path.split('/');
        const isLeafPathMatch = leafPaths.slice(1, leafPaths.length).join('/') === urls.slice(1, leafPaths.length > 3 ? 4 : 3).join('/');
        if (isLeafPathMatch) {
          sub.isCurrent = true;
          leaf.isCurrent = true;
        }
      });
    });
  }
  onOpenChange($event: boolean, subMenu: any) {
    subMenu.isOpen = $event;
    this.refreshCollapsedStatus.emit();
  }
  enterMenu(currentMenu: MenuItem, $event: MouseEvent) {
    if (this.debounceTimer) {
      this.clearTimer();
    }
    this.clearCurrent();
    currentMenu.isCurrent = true;
    if (this.subMenuComponentRef) {
      this.subMenuComponentRef.instance.updateMenuList([...(currentMenu.children ?? [])]);
    } else {
      this.showSubmenu(currentMenu.children ?? [], $event.target as HTMLElement);
    }
  }
  leaveMenu() {
    this.setTimer();
    const element = this.overlayRef?.overlayElement;
    if (element) {
      this.renderer.listen(element, 'mouseenter', () => {
        this.clearTimer();
      });
      this.renderer.listen(element, 'mouseleave', () => {
        this.setTimer();
      });
    }
  }
  setTimer() {
    this.clearTimer();
    this.debounceTimer = setTimeout(() => {
      this.subMenuComponentRef?.instance.close();
    }, this.debounceTime);
  }
  clearTimer() {
    clearTimeout(this.debounceTimer);
    this.debounceTimer = undefined;
  }
  clearSubmenuOverlay() {
    this.overlayRef?.dispose();
    this.subMenuComponentRef?.destroy();
    this.subMenuComponentRef = undefined;
    this.resetCurrent();
  }
  showSubmenu(currentMenu: MenuItem[], target: HTMLElement) {
    this.overlayRef = this.overlayService.create({
      width: '400px',
      height: '100%',
      disposeOnNavigation: true,
      positionStrategy: this.overlayService
        .position()
        .flexibleConnectedTo(target)
        .withPositions([
          new ConnectionPositionPair({ originX: 'end', originY: 'center' }, { overlayX: 'start', overlayY: 'center' }, 0, 48),
        ]),
    });
    this.subMenuComponentRef = this.overlayRef.attach(new ComponentPortal(SubMenuBarOverlayComponentComponent));
    this.subMenuComponentRef?.instance.updateMenuList([...currentMenu]);
    this.subMenuComponentRef?.instance.onClose.subscribe(() => {
      this.clearTimer();
      this.subMenuComponentRef?.destroy();
    });
    this.subMenuComponentRef.onDestroy(() => {
      this.clearSubmenuOverlay();
    });
  }
}
