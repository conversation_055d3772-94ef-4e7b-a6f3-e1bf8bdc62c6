import { Component, ElementRef, EventEmitter, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { NzDrawerRef, NzDrawerService } from 'ng-zorro-antd/drawer';
import { moveItemInArray, CdkDragDrop } from '@angular/cdk/drag-drop';
import { OrganizationService } from '../organization.service';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { UtilService } from 'src/app/shared/util.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { factoryCodes } from 'src/app/shared/common-config';

@Component({
  selector: 'app-account-drawer',
  templateUrl: './account-drawer.component.html',
  styleUrls: ['./account-drawer.component.scss'],
})
export class AccountDrawerComponent implements OnInit {
  @ViewChild('titleTpl') titleTpl: TemplateRef<any> | undefined;
  @ViewChild('contentTpl') contentTpl: TemplateRef<any> | undefined;
  @ViewChild('roleTable') roleTable!: ElementRef;
  @Output() handleReturn = new EventEmitter<any>();
  @Output() handleAccountOk = new EventEmitter<any>();
  passwordVisible = false;
  account!: NzDrawerRef;
  placeInput!: string;
  placeSelect!: string;
  accountForm!: FormGroup;
  priorityLists = [
    {
      role_id: null,
      priority: 1,
    },
  ];
  lang = localStorage.getItem('lang') || 'zh';
  userInfo: any = null;
  factoryCodes = factoryCodes || [];

  constructor(
    private _drawer: NzDrawerService,
    private _translateService: TranslateService,
    private _service: OrganizationService,
    private _fb: FormBuilder,
    private _notice: NzNotificationService,
    private _util: UtilService,
    private _msg: NzMessageService
  ) {
    const userInfo: string | null = localStorage.getItem('userInfo');
    this.userInfo = userInfo ? JSON.parse(userInfo) : {};
  }

  ngOnInit(): void {}

  /**
   * 创建账号抽屉
   */
  createAccountDrawer() {
    this.passwordVisible = false;
    this.placeInput = this._translateService.instant('placeholder.input');
    this.placeSelect = this._translateService.instant('placeholder.select');
    this.account = this._drawer.create({
      nzTitle: this.titleTpl,
      nzContent: this.contentTpl,
      nzWidth: '400px',
      nzWrapClassName: 'drawer-outer',
    });
    this.account.afterClose.subscribe(() => this.handleCancel());
  }

  initAccountForm(line: any) {
    const namePattern = '[\u4e00-\u9fa5A-Za-z0-9]*';
    const codePattern = '[A-Za-z0-9]*';
    this.accountForm = this._fb.group({
      name: [
        line.name ?? null,
        [
          Validators.required,
          Validators.maxLength(this.factoryCodes.includes(this.userInfo?.factory_code) ? 100 : 16),
          Validators.pattern(namePattern),
        ],
      ],
      login_name: [
        line.login_name ?? null,
        [Validators.required, Validators.maxLength(16), Validators.pattern(codePattern)],
        [this._service.uniqueRolesValidator()],
      ],
      password: [null, [Validators.required, Validators.maxLength(16)]],
      status: [1, [Validators.required]],
      roles: [],
    });
  }

  closeAccountDrawer() {
    this.account.close();
  }

  async handleOk() {
    const lines = this.priorityLists.filter((item) => item.role_id) ?? [];
    const res = new Map();
    const new_lines = lines.filter((item) => !res.has(item['role_id']) && res.set(item['role_id'], 1));
    if (new_lines.length < lines.length) {
      this._notice.create('error', '权限名称有重复，请调整～', '');
      return;
    }
    this._util
      .checkIfFormPassesValidation(this.accountForm)
      .then(async (valid) => {
        if (valid) {
          lines.forEach((item, index) => {
            item.priority = index + 1;
          });
          const payload = {
            ...this.accountForm.getRawValue(),
            roles: lines,
          };
          const data = await this._service.addUser(payload).toPromise();
          if (data.code === 200) {
            const create_msg = this._translateService.instant('success.create');
            this._msg.success(create_msg);
            this.handleAccountOk.emit({
              ...payload,
              id: data.data,
            });
          }
        } else {
          const msg = this._translateService.instant('form-error.input-right');
          this._notice.error('', msg);
          return;
        }
      })
      .catch((error) => {
        console.log('error', error);
        return;
      })
      .finally(() => {
        return;
      });
  }

  handleCancel() {
    this.handleReturn.emit();
  }

  /**
   * 权限行拖拽改变顺序
   * @param event
   */
  drop(event: CdkDragDrop<string[]>) {
    moveItemInArray(this.priorityLists, event.previousIndex, event.currentIndex);
  }

  /**
   * 删除权限行
   */
  deletePriority(index: number) {
    this.priorityLists.splice(index, 1);
  }

  /**
   * 新增权限行
   */
  addPriority(i: number) {
    this.priorityLists.splice(i + 1, 0, {
      role_id: null,
      priority: 1,
    });
    const scrollele = this.roleTable.nativeElement;
    scrollele.scrollTo({ top: scrollele.scrollTop + 50 });
  }
}
