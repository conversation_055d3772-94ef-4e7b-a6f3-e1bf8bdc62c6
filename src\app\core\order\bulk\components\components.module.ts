import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { FlButtonModule } from 'fl-ui-angular/button';

import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzRadioModule } from 'ng-zorro-antd/radio';

import { printLabelComponent } from './print-label.component';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzCascaderModule } from 'ng-zorro-antd/cascader';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';
import { FlcComponentsModule, FlcDirectivesModule } from 'fl-common-lib';
import { NgxQRCodeModule } from '@techiediaries/ngx-qrcode';
import { AcceptListComponent } from './accept-list/accept-list.component';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { AcceptInfoComponent } from './accept-info/accept-info.component';
import { DepartmentSelectComponent } from './department-select/department-select.component';
import { NzTreeSelectModule } from 'ng-zorro-antd/tree-select';

const nzModules = [
  NzIconModule,
  NzButtonModule,
  NzDatePickerModule,
  NzFormModule,
  NzInputModule,
  NzRadioModule,
  NzCheckboxModule,
  NzPopoverModule,
  NzSelectModule,
  NzInputNumberModule,
  NzCascaderModule,
  NzToolTipModule,
  NzTabsModule,
  NgxQRCodeModule,
  NzDividerModule,
  NzSpinModule,
  NzModalModule,
  NzAutocompleteModule,
  NzPopconfirmModule,
  NzTreeSelectModule,
];

@NgModule({
  declarations: [printLabelComponent, AcceptListComponent, AcceptInfoComponent, DepartmentSelectComponent],
  imports: [CommonModule, FormsModule, FlcComponentsModule, FlcDirectivesModule, FlButtonModule, ...nzModules, TranslateModule],
  exports: [printLabelComponent, AcceptListComponent, AcceptInfoComponent, DepartmentSelectComponent],
})
export class printLabelComponentsModule {}
