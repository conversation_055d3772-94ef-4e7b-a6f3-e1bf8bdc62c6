import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { RouteReuseStrategy } from '@angular/router';
import { FlcRouteReuseStrategy } from 'fl-common-lib';

@Component({
  selector: 'app-not-found-page',
  templateUrl: './not-found-page.component.html',
  styleUrls: ['./not-found-page.component.scss'],
})
export class NotFoundPageComponent implements OnInit, OnDestroy {
  constructor(private _routeReuseStrategy: RouteReuseStrategy) {}
  ngOnDestroy(): void {
    window.location.reload();
  }

  ngOnInit(): void {
    (this._routeReuseStrategy as FlcRouteReuseStrategy).clearAllCache();
  }
}
