<div id="mainBoard" [@slide]="isAnimation ? 'visible' : 'hidden'" (@slide.done)="animationFinish($event)">
  <div class="secondMenu" *ngFor="let second of menuList">
    <div class="secondMenuTitle">{{ second.groupName }}</div>
    <div class="thirdMenuWrapper">
      <ng-container *ngFor="let third of second.children">
        <div *ngIf="third.value" class="thirdMenuLine" [ngClass]="{ selected: third.isCurrent }" (click)="navigator(third)">
          <i class="menuIcon" nz-icon [nzIconfont]="third.icon"></i>
          {{ third.name }}
        </div>
      </ng-container>
    </div>
  </div>
</div>
