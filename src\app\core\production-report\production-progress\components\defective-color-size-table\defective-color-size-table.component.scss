$table-hover-bg-color: #deefff;
$table-static-cell-bg-color: #fafbfd;

:host ::ng-deep {
  .ant-btn,
  .ant-btn-link {
    margin-right: 0px;
  }

  .table-outer-container {
    background-color: #ffffff;
    padding: 8px;
  }

  .ant-table-body {
    scroll-behavior: smooth;
  }

  .ant-table-cell {
    height: 32px;
    padding: 0;
  }

  .ant-table-thead {
    th:first-child {
      padding: 0;
    }

    .desc-cell {
      width: 100%;
      height: 100%;
      color: #5e6577;
      background: #d0d5d9 !important;
      font-size: 14px;
      font-weight: 500;
      &::before {
        content: attr(before-content);
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        height: 100%;
        display: flex;
        align-items: flex-end;
        padding-left: 15%;
        -webkit-clip-path: polygon(0% 1%, 0% 100%, 99% 100%);
        clip-path: polygon(0% 1%, 0% 100%, 99% 100%);
        background: #f7f8fa;
        transform: translateX(0);
      }
      &::after {
        content: attr(after-content);
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        width: 100%;
        padding-right: 15%;
        -webkit-clip-path: polygon(1% 0%, 100% 0, 100% 99%);
        clip-path: polygon(1% 0%, 100% 0, 100% 99%);
        background: #f7f8fa;
        transform: translateX(0);
        text-align: right;
      }
    }
  }

  .ant-table-tbody {
    tr.ant-table-row:hover > td {
      div.frozen-cell {
        background-color: $table-hover-bg-color;
      }
    }

    tr.ant-table-row.sum-row:hover > td {
      border-top: 1px solid #d4d7dc;
      border-right: 1px solid #d4d7dc;
      border-bottom: 1px solid #d4d7dc;
      border-left: none;
      transform: translate(0, 0);
      background-color: $table-static-cell-bg-color;
    }

    .ant-table-row {
      &:nth-last-child(2) {
        td {
          border-bottom: none;
        }
      }

      &:last-child {
        position: sticky;
        bottom: 0;
        z-index: 2;

        td {
          border-top: 1px solid #d4d7dc;
        }
      }
    }

    td.sum-col,
    .sum-row > td {
      background-color: $table-static-cell-bg-color;
    }

    .sum-row > td:not(:first-child),
    .sum-col {
      color: #138aff;
    }
    .sum-row > td:last-child {
      color: #fb6401;
    }
    .sum-hint {
      color: #ff0000;
    }
  }

  .ant-table-wrapper {
    &:not(.has-horizontal-scroll) {
      .ant-table-ping-right .ant-table-cell-fix-right-first::after {
        box-shadow: none;
      }
    }
  }

  .sort-only-mode,
  .toggle-cell-mode,
  .edit-table-mode {
    &:not(.has-horizontal-scroll) {
      .ant-table-body::before {
        background: none;
      }
    }

    .ant-table-header {
      &::before {
        content: '';
        position: absolute;
        height: 24px;
        width: 1px;
        background-color: #ffffff;
        z-index: 2;
        margin-left: -1px;
      }

      table {
        border-top: 0 !important;
      }

      .ant-table-thead {
        .ant-table-row > th {
          height: 56px;
          border-right: 0 !important;
          border-radius: 8px 8px 0 0;

          & > div {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;

            &:first-child {
              height: 24px;
              background-color: #ffffff;

              &.col-action-banner {
                background-color: #f2f9ff;
                border-right: 1px solid #ffffff;
                border-radius: 8px 8px 0 0;
                gap: 6px;
                padding: 6px;

                &:hover {
                  background-color: $table-hover-bg-color;

                  .action-btn.remove,
                  .action-btn.batch-remove {
                    color: #f96d6d;
                  }
                  .action-btn.add,
                  .action-btn.move,
                  .action-btn.drag,
                  .action-btn.batch-add {
                    color: #4d96ff;
                  }

                  .action-btn.drag {
                    cursor: grab;
                  }

                  .action-btn.frozen {
                    color: #abb7c2;
                    cursor: not-allowed;
                  }

                  .action-btn.not-deletable {
                    cursor: not-allowed;
                  }
                }

                .action-btn {
                  width: 14px;
                }

                .action-btn.add {
                  color: #a2ceff;
                }

                .action-btn.remove {
                  color: #f9bec0;
                  margin-left: 6px;
                }

                .action-btn.move,
                .action-btn.drag,
                .action-btn.batch-add,
                .action-btn.batch-remove {
                  color: #abb7c2;
                }
              }

              .batch-add-column-btn {
                display: flex;
                align-items: center;
                height: 22px;
                font-size: 12px;
                padding: 2px 12px;

                .anticon + span {
                  margin-left: 2px;
                }
              }
            }

            &:last-child {
              height: 32px;
              border-top: 1px solid #d4d7dc;
              border-right: 1px solid #d4d7dc;
            }
          }
        }

        .ant-table-cell-fix-right-first::after {
          border-right: none !important;
        }
      }
    }

    .ant-table-tbody {
      tr.ant-table-row:hover > td {
        background: #ffffff;
      }

      td.sum-col,
      .sum-row > td {
        background-color: $table-static-cell-bg-color !important;
      }
    }

    .ant-table-ping-right .ant-table-cell-fix-right-first::after,
    .ant-table-ping-right .ant-table-thead .ant-table-cell-fix-right-first::after {
      border-right: 1px solid #d4d7dc !important;
    }

    &.has-horizontal-scroll {
      .ant-table-ping-left .ant-table-header .ant-table-thead > tr > th:first-child > div:first-child {
        border-right: 1px solid #d4d7dc !important;
      }
    }
  }

  .toggle-cell-mode,
  .edit-table-mode {
    .ant-table-thead {
      .ant-table-row > th:last-child {
        border-bottom: none;

        div:last-child {
          border-top: none;
          border-right: none;
          background-color: #ffffff;
        }
      }
    }

    .ant-table-tbody {
      .ant-table-row {
        .ant-table-cell-fix-right:last-child {
          border-top: none;
          border-right: none;
          border-bottom: none;
          background-color: #ffffff !important;

          .row-action-banner {
            display: flex;
            justify-content: flex-end;
            background-color: #f2f9ff;
            border-radius: 0 8px 8px 0;
            margin: 0.5px 0;
            padding: 0 8px;
            gap: 5px;

            &:hover {
              background-color: $table-hover-bg-color;

              .action-btn.remove,
              .action-btn.batch-remove {
                color: #f96d6d;
              }
              .action-btn.add,
              .action-btn.batch-add {
                color: #4d96ff;
              }

              .action-btn.copy {
                color: #4d96ff;
              }

              .action-btn.frozen {
                color: #abb7c2;
                cursor: not-allowed;
              }

              .action-btn.not-deletable {
                cursor: not-allowed;
              }
            }

            .action-btn {
              width: 14px;
              color: #a2ceff;

              &.batch-add,
              &.batch-remove {
                color: #abb7c2;
              }

              &.remove {
                color: #f9bec0;
              }
            }
          }
        }
      }

      .sum-row > td:nth-last-child(2) {
        color: #fb6401;
      }
    }
  }

  .edit-table-mode {
    .ant-input-number {
      position: absolute;
      top: 1px;
      display: flex;
      align-items: center;
      width: 100%;
      height: calc(100% - 2px);
      border: none;

      &:hover {
        border: 1px solid #138aff;
        transition: none;
      }

      input.ant-input-number-input {
        text-align: center;
      }
    }

    .ant-cascader {
      position: absolute;
      top: 1px;
      display: flex;
      width: 100%;
      height: calc(100% - 2px);

      &:hover,
      &.ant-select-focused {
        border: 1px solid #138aff;
      }

      & > div:first-child {
        flex: 1;
        display: flex;
        align-items: center;

        .ant-select-selector {
          overflow: hidden;
          height: 100%;
          display: flex;
          align-items: center;
          border: none;
          box-shadow: none;

          .ant-select-selection-search-input {
            height: 100%;
            text-align: center;
          }

          .custom-label-wrapper {
            float: left;
            width: 108px;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }

    .ant-select {
      position: absolute;
      width: 100%;

      &:hover,
      &.ant-select-focused {
        border: 1px solid #138aff;
      }

      .ant-select-selector {
        height: 30px;
        border: none;
        box-shadow: none;
        background: none;
        padding-right: 0px;

        .ant-select-selection-placeholder {
          padding-right: 8px;
        }
      }
    }
  }

  .edit-cell-mode {
    .ant-input-number {
      position: absolute;
      top: 1px;
      display: flex;
      align-items: center;
      width: 100%;
      height: calc(100% - 2px);
      border: none;

      &:hover {
        border: 1px solid #138aff;
        transition: none;
      }

      input.ant-input-number-input {
        text-align: center;
      }
    }

    .ant-table-tbody {
      tr.ant-table-row:hover > td {
        background: #ffffff;
      }

      td.sum-col,
      .sum-row > td {
        background-color: $table-static-cell-bg-color !important;
      }
    }
  }

  .read-only-mode,
  .edit-cell-mode {
    .null-cell {
      color: #b5b8bf;
    }
  }

  .read-only-mode,
  .toggle-cell-mode {
    .frozen-cell {
      display: flex;
      height: 100%;
      align-items: center;
      justify-content: center;
      background-color: #e8eef4;
    }
    .ant-input-number {
      position: absolute;
      top: 1px;
      display: flex;
      align-items: center;
      width: 100%;
      height: calc(100% - 2px);
      border: none;

      &:hover {
        border: 1px solid #138aff;
        transition: none;
      }

      input.ant-input-number-input {
        text-align: center;
      }
    }
  }

  .toggle-cell-mode {
    .toggle-cell {
      position: relative;
      display: flex;
      height: 100%;
      align-items: center;
      justify-content: center;

      &.focus {
        border: 1px solid #138aff;
      }
    }

    .action-container {
      position: absolute;
      right: 8px;

      .action-btn {
        width: 14px;
        &.remove {
          color: #b5b8bf;
        }
      }
    }

    .no-cell-value {
      color: #b5b8bf;

      &.placeholder-text {
        font-size: 12px;
      }
    }
  }
}

.header-drag-preview {
  display: flex;
  flex-direction: column;
  background-color: #e7f3fe;
  border: 1px solid #007aff;
  box-shadow: 0 2px 6px #d4d7dc;
  border-radius: 8px 8px 0 0;

  div {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    &:first-child {
      height: 24px;
      background-color: #ffffff;

      &.col-action-banner {
        background-color: #f2f9ff;
        border-right: 1px solid #ffffff;
        border-radius: 8px 8px 0 0;
        justify-content: flex-end;
        gap: 6px;
        padding: 6px;

        &:hover {
          background-color: $table-hover-bg-color;

          .action-btn.remove {
            color: #f96d6d;
          }
          .action-btn.add,
          .action-btn.move,
          .action-btn.drag {
            color: #4d96ff;
          }
        }

        .action-btn {
          width: 14px;
          color: #a2ceff;

          &.remove {
            color: #f9bec0;
            margin-left: 6px;
          }
        }
      }
    }

    &:last-child {
      height: 32px;
      border-top: 1px solid #d4d7dc;
      border-right: 1px solid #d4d7dc;
      position: relative;

      & ::ng-deep {
        .ant-select {
          width: 100%;
          .ant-select-selector {
            border: none;
            background: none;
          }
        }
      }
    }
  }
}

.header-drag-placeholder {
  height: 56px;
  background-color: #f2f9ff;
  border: 2px dotted #138aff;
  border-radius: 8px 8px 0 0;
}

::ng-deep .color-size-table-action-btn-tooltip {
  padding-bottom: 0px;
}

::ng-deep .color-size-table-move-popover {
  padding-bottom: 0px;
  .ant-popover-arrow {
    bottom: -16px;
  }

  .select-section {
    margin: 4px 0;

    .col-select {
      width: 150px;

      &.ant-select-status-error:not(.ant-select-disabled) {
        .ant-select-selector > .ant-select-selection-placeholder {
          color: #fe5d56;
          transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
        }
      }
    }

    .order-select {
      width: 84px;

      .ant-select-selector {
        background-color: #f7f8fa;
      }
    }
  }

  .action-btn-section {
    display: flex;
    justify-content: flex-end;

    .ant-btn {
      padding-right: 0;

      &.cancel-btn {
        color: #54607c;

        &:hover {
          color: #5d6883;
        }
      }
    }
  }
}

::ng-deep .color-size-table-remove-popover {
  padding-bottom: 0px;
  .ant-popover-arrow {
    bottom: -16px;
  }

  .error-icon {
    color: #fe5d56;
    margin-right: 8px;
  }

  .action-btn-section {
    display: flex;
    justify-content: flex-end;

    .ant-btn {
      padding-right: 0;

      &.cancel-btn {
        color: #54607c;

        &:hover {
          color: #5d6883;
        }
      }
    }
  }
}

::ng-deep .color-size-table-batch-add-tooltip {
  padding-bottom: 10px;
}

::ng-deep .color-size-table-batch-add-popover {
  padding-bottom: 10px;
  .ant-popover-arrow {
    bottom: -6px;
  }

  .ant-popover-inner-content {
    padding: 0;
  }

  .option-item-wrapper {
    width: 100%;
    max-height: 160px;
    overflow: auto;

    .option-item {
      display: flex;
      align-items: center;
      padding: 0 10px;
      height: 32px;

      &:nth-of-type(even) {
        background-color: #fafbfd;
      }

      &:hover {
        background-color: #eef7ff;
      }

      .ant-checkbox-wrapper {
        line-height: 32px;
        width: 100%;

        &.used-option {
          color: #fb6401;
        }

        &:hover,
        &.ant-checkbox-wrapper-checked {
          color: #138aff;
        }
      }

      .sequence-indicator {
        line-height: 14px;
        font-size: 12px;
        color: #b5b8bf;
        background-color: #f0f3fa;
        padding: 0 8px;
        border-radius: 8px;
      }
    }
  }

  .select-section {
    display: flex;
    align-items: center;
    padding: 8px;
    box-shadow: 0px -1px 2px 0px rgba(202, 208, 216, 0.5);

    .select-input-grp {
      width: calc(100% - 57px);

      .col-select {
        width: 100%;
        min-width: 132px;
        padding-right: 0;

        &.ant-select-status-error:not(.ant-select-disabled) {
          .ant-select-selector > .ant-select-selection-placeholder {
            color: #fe5d56;
            transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
          }
        }
      }
    }
  }

  .action-btn-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
    padding-left: 2px;
    padding-bottom: 8px;

    .select-all-checkbox {
      height: 24px;
      background-color: #f6f8fb;
      border-radius: 10px;
      padding-left: 8px;
      padding-top: 1px;

      &:hover {
        background-color: #e7f3fe;
      }

      .ant-checkbox-wrapper {
        &:hover,
        &.ant-checkbox-wrapper-checked {
          color: #138aff;
        }
      }
    }

    .btn-grp {
      .ant-btn {
        padding: 4px 8px;

        &.cancel-btn {
          color: #54607c;

          &:hover {
            color: #5d6883;
          }
        }
      }
    }
  }
}

::ng-deep .color-size-table-color-cascader {
  .ant-cascader-menu-item:not(.ant-cascader-menu-item-disabled) {
    .used-option {
      color: #fb6401;
    }
    .add-option {
      color: #4d96ff;
    }
  }
}

::ng-deep .color-size-table-size-seletor {
  .ant-select-item-option:not(.ant-select-item-option-disabled) {
    .used-option {
      color: #fb6401;
    }
  }
}
