import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: 'sys-parameters',
    loadChildren: () => import('./sys-parameters/sys-parameters.module').then((m) => m.SysParametersModule),
  },
  {
    path: 'calendar',
    loadChildren: () => import('./other-settings/other-settings.module').then((m) => m.OtherSettingsModule),
  },
  {
    path: 'printing-management',
    loadChildren: () => import('fl-sewsmart-lib/custom-template').then((m) => m.CustomShadowTemplateModule),
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class SysSettingsRoutingModule {}
