<div class="searchBarWrap" #searchBarWrap>
  <flc-screen-container (reset)="getList(true)" (handleFold)="calcTableHeight()">
    <div class="select-box">
      <div *ngFor="let item of searchList" class="search-box">
        <span class="search-label">{{ translateName + item.label | translate }}：</span>
        <flc-dynamic-search-select
          *ngIf="item.type === 'select'"
          [(ngModel)]="searchData[item.valueKey]"
          [dataUrl]="'/service/order/v1/accept/list_option'"
          [payLoad]="item?.payLoad || {}"
          [transData]="{ value: 'value', label: 'label' }"
          [column]="item.valueKey"
          (handleSearch)="getList()">
        </flc-dynamic-search-select>
        <ng-container *ngIf="item.type === 'cascader'">
          <nz-cascader
            [nzPlaceHolder]="'flss.placeholder.select' | translate"
            nzAllowClear
            [nzShowSearch]="true"
            [nzValueProperty]="'label'"
            [nzOptions]="styleList"
            [(ngModel)]="searchData[item.valueKey]"
            (nzVisibleChange)="openStyle($event)"
            (nzSelectionChange)="getList()">
          </nz-cascader>
        </ng-container>
        <nz-range-picker
          *ngIf="item.type === 'date'"
          [nzPlaceHolder]="['flss.placeholder.start' | translate, 'flss.placeholder.end' | translate]"
          [(ngModel)]="searchData[item.valueKey]"
          (ngModelChange)="getList()"></nz-range-picker>
      </div>
    </div>
  </flc-screen-container>
</div>
<div style="margin-top: 10px">
  <flc-table
    [tableHeader]="tableHeader"
    [tableConfig]="tableConfig"
    [template]="btnTpl"
    (sortDataLists)="sortDataLists($event)"
    (indexChanges)="indexChanges($event)"
    (sizeChanges)="sizeChanges($event)">
  </flc-table>
  <ng-template let-data="data" #btnTpl>
    <ng-container *ngIf="data.isTd">
      <ng-container *ngIf="data.key === 'po_due_times'">
        <flc-text-truncated [template]="poDueTime"></flc-text-truncated>
        <ng-template #poDueTime>
          <ng-container *ngIf="data.item?.po_due_times.length > 0; else noTime">
            <span *ngFor="let value of data.item?.po_due_times; index as i">
              {{ value | date: 'yyyy/MM/dd' }}<ng-container *ngIf="i < data.item?.po_due_times.length - 1">、</ng-container>
            </span>
          </ng-container>
          <ng-template #noTime>
            <span style="color: #b5b8bf">-</span>
          </ng-template>
        </ng-template>
      </ng-container>
    </ng-container>
    <ng-container *ngIf="data.isAction">
      <div class="order-btn-box">
        <button
          class="accept-btn"
          nz-popconfirm
          [nzPopconfirmOverlayStyle]="{ width: '200px' }"
          [nzPopconfirmTitle]="translateName + '确定接单?' | translate"
          (nzOnConfirm)="accept(data.item?.id)"
          nz-button
          nzType="link">
          {{ translateName + '接单' | translate }}
        </button>
        <!-- <button nz-button nzType="link" flButton="link" (click)="getDetail(data.item?.id)">详情</button> -->
      </div>
    </ng-container>
  </ng-template>
</div>
