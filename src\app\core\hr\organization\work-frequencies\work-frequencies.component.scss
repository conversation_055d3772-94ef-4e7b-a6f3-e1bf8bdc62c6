:host ::ng-deep .work-frequency-container {
  margin-left: 120px;
  background: #f7f8fa;
  border-radius: 2px;
  .work-frequency-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    font-weight: 500;
    color: #4a5160;
    padding: 8px;
  }
  nz-divider {
    margin: 0;
  }

  .work-frequency-body {
    padding: 8px;

    .ant-checkbox-group {
      display: flex;
      gap: 4px;
      align-items: center;
      flex-wrap: wrap;
    }

    .ant-checkbox {
      display: none;
    }
    .ant-checkbox + span {
      padding: 0;
    }
    .ant-checkbox-wrapper + .ant-checkbox-wrapper {
      margin-left: 0;
    }

    .checkbox-btn {
      background: #ffffff;
      border-radius: 50px;
      min-width: 40px;
      text-align: center;
      padding: 2px 5px;
      font-size: 14px;
      font-weight: 500;
      color: #262d48;
    }

    .ant-checkbox-wrapper-checked {
      .checkbox-btn {
        background: #40a2ff;
        color: #ffffff;
      }
    }
  }
}
