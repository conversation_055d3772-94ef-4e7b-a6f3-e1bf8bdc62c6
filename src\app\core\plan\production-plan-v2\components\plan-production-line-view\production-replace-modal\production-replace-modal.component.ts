import { Component, Input, OnInit } from '@angular/core';
import { LineOrderInterface } from '../../../interface';
import { NzModalRef } from 'ng-zorro-antd/modal';
import { AbstractControl, FormArray, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ProductionPlanV2Service } from '../../../production-plan-v2.service';
import { FlcModalService, FlcValidatorService } from 'fl-common-lib';

@Component({
  selector: 'app-production-replace-modal',
  templateUrl: './production-replace-modal.component.html',
  styleUrls: ['./production-replace-modal.component.scss'],
})
export class ProductionReplaceModalComponent implements OnInit {
  @Input() data!: LineOrderInterface;

  orders: any[] = [];
  modalForm = this._fb.group({
    list: this._fb.array([]),
  });

  get dataList() {
    return this.modalForm.get('list') as FormArray;
  }

  orderList: any[] = [];

  constructor(
    private modelRef: NzModalRef,
    private _fb: FormBuilder,
    private _msg: NzMessageService,
    private _service: ProductionPlanV2Service,
    private _flcValidatorService: FlcValidatorService,
    private _flcModalService: FlcModalService
  ) {}

  ngOnInit() {
    this.onAddRow(0);
    this.getReplaceOrderList();
  }

  onChangeSelect(e: string, control: AbstractControl) {
    const _option = this.orderList.find((item) => item.order_uuid === e);
    control.get('qty')?.setValue(_option?.qty ?? null);
    control.get('sam_configured')?.setValue(_option?.sam_configured ?? null);
    control.get('order_code')?.setValue(_option?.order_code ?? null);
  }

  // 添加行
  onAddRow(index: number, data?: any) {
    const _group: FormGroup = this._fb.group({
      order_uuid: [data?.order_uuid ?? null, [Validators.required, this.validatorFactory()]],
      order_code: [data?.order_code ?? null],
      qty: [data?.qty ?? null],
      sam_configured: [data?.sam_configured ?? null],
    });
    this.dataList.insert(index + 1, _group);
  }

  onRemoveRow(index: number) {
    this.dataList.removeAt(index);
  }

  onCloseModal(isClosed: boolean) {
    if (!isClosed) {
      this.modelRef.close();
      return;
    }
    if (this._flcValidatorService.formIsInvalid(this.modalForm)) return;

    const _value = this.dataList.getRawValue();
    const _filter = _value.filter((item) => item.sam_configured === false).map((item) => item.order_code);
    if (_filter.length) {
      this._flcModalService
        .confirmCancel({ content: `订单${_filter.join('、')}未设置sam值或人均台产, 是否使用当前预排单的设置` })
        .afterClose.subscribe((res) => {
          if (res) this.modelRef.triggerOk();
        });

      return;
    }
    this.modelRef.triggerOk();
  }

  getPayload() {
    const _value = this.dataList.getRawValue();
    return _value.map((item) => ({ order_uuid: item.order_uuid }));
  }

  getReplaceOrderList() {
    this._service.getReplaceOrderList(this.data.group_id).subscribe((res) => {
      if (res.code === 200) {
        this.orderList = res.data.order_list;
      }
    });
  }

  validatorFactory(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value && control.value !== 0) {
        return null;
      } else {
        const _repeatArr = [];
        const _values = this.dataList.getRawValue();
        _values.forEach((item) => {
          item.order_uuid === control.value && _repeatArr.push(item.order_uuid);
        });
        return _repeatArr.length >= 2 ? { duplicated: { message: '订单不可重复' } } : null;
      }
    };
  }
}
