.operate-bts {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
}

.eidt-btn {
  position: relative;
}
.tip-content {
  position: absolute;
  bottom: 100%;
  width: 180px;
  left: -50%;
  transform: translateY(-14px);
  .div {
    background-color: #1890ff;
    padding: 6px 12px;
    border-radius: 8px 0px 8px 0;
  }

  i {
    color: #fff;
  }

  .ant-tooltip-arrow {
    left: 50%;
    transform: translateY(100%) translateX(-50%);
    bottom: 0;
    .ant-tooltip-arrow-content {
      box-shadow: 3px 3px 7px rgba(0, 0, 0, 0.07);
      transform: translateY(-11px) rotate(45deg);
      &::before {
        position: absolute;
        top: -11.3137085px;
        left: -11.3137085px;
        width: 33.9411255px;
        height: 33.9411255px;
        background: #1890ff;
        background-repeat: no-repeat;
        background-position: -10px -10px;
        content: '';
        clip-path: path(
          'M 9.849242404917499 24.091883092036785 A 5 5 0 0 1 13.384776310850237 22.627416997969522 L 20.627416997969522 22.627416997969522 A 2 2 0 0 0 22.627416997969522 20.627416997969522 L 22.627416997969522 13.384776310850237 A 5 5 0 0 1 24.091883092036785 9.849242404917499 L 23.091883092036785 9.849242404917499 L 9.849242404917499 23.091883092036785 Z'
        );
      }
    }
  }
}
