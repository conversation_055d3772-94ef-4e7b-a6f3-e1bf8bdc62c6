import { PlanAlertStatusEnum, PlanStatus } from './plan.enum';

export interface ProductionLineResponse {
  list: ProductionLineListItem[];
  total: number;
  temp_session: string;
  order_info_map: { [key: string]: PlanOrderInfo };
  cmd_list?: string[];
}

/**
 * v1PlanListRespItem
 */
export interface ProductionLineListItem {
  factory_code: string;
  factory_name: string;
  production_line_name: string;
  production_line_no: string;
  recent_idle_date: number; // 最近空闲日
  view_list: PlanOrderViewItem[]; //具体甘特图信息
  rest_days?: number[];
  lock_user_id: number;
  lock_user_name: string;
}

/**
 * view_list_item
 */
export interface PlanOrderViewItem {
  actual_end_time: number;
  /**
   * 已分配
   */
  allocated_qty: number;
  /**
   * 计划结束
   */
  end_time: number;
  /**
   * 甘特图条id
   */
  group_id: number;
  order_uuid: string;
  publish_status: PlanStatus;
  /**
   * 已产出
   */
  sewing_qty: number;
  /**
   * 计划开始
   */
  start_time: number;
  /**
   * 1-已逾期，2-进度落后，3-计划超客期
   */
  tags: PlanAlertStatusEnum[];

  last_publish_user: string; //上一次编辑人
  last_publish_time: number; //上一次编辑人
}

export interface PlanOrderInfo {
  dept_id: number;
  dept_name: string;
  due_times: number[];
  employee_id: string;
  employee_name: string;
  allocated_qty: number;
  order_qty: number;
  order_uuid: string;
  order_code: string;
  plan_start_time: number;
  plan_end_time: number;
  /**
   * 物料齐套日期
   */
  pre_material_completed_time?: number | null;
  style_class: string;
  style_code: string;
  can_edit: boolean;
  is_pre_order: boolean;
  is_outsourced: boolean; // 是否外发
}

export interface PlanListPayload {
  order_uuids?: string[];
  style_codes?: string[];
  factory_codes?: FactoryRequestPayload[];
  page: number;
  size: number;
  temp_session: string | null;
  start_time: number;
  end_time: number;
}

export interface FactoryRequestPayload {
  factory_code: string;
  production_line_nos: string[];
  is_all_lines: boolean;
}

export interface ForwardConnectRequestPaylod {
  /**
   * 操作的工厂行
   */
  factory_code: string;
  /**
   * 向前衔接的group_id，整体衔接时可选
   */
  group_id?: number;
  /**
   * 是否整体向前衔接
   */
  is_all: boolean;
  /**
   * 操作的产线行
   */
  production_line_no?: string;
  temp_session?: string;
}

export interface LockFactoryItem {
  factory_code: string;
  production_line_no: string;
  is_current_user_lock: boolean;
}
