import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { FlButtonModule } from 'fl-ui-angular/button';

import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzRadioModule } from 'ng-zorro-antd/radio';

import { ProductionReportTabComponent } from './production-report-tab/production-report-tab.component';

const NzModules = [NzButtonModule, NzIconModule, NzDividerModule, NzToolTipModule, NzRadioModule];

@NgModule({
  declarations: [ProductionReportTabComponent],
  imports: [CommonModule, FormsModule, FlButtonModule, ...NzModules, TranslateModule],
  exports: [ProductionReportTabComponent],
})
export class ProductionReportComponentsModule {}
