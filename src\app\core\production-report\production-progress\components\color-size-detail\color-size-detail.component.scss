:host ::ng-deep {
  .color-size-detail {
    .info {
      display: flex;
      width: 100%;
      padding: 10px 12px;
      background: #f7f8fa;
      border-radius: 0px 0px 8px 8px;
      flex-wrap: wrap;
      flex-direction: row;
      &-item {
        display: flex;
        align-items: center;

        &-title {
          font-weight: 500;
          color: #54607c;
        }
        &-value {
          font-weight: 500;
          color: #262d48;
        }
      }

      .ant-divider-vertical {
        margin: 0 16px;
        background: #d1d8e1;
        margin-top: 4px;
      }
    }
    nz-table {
      width: 100% !important;
    }
    .table-outer-container {
      padding: 8px 0;
    }
  }

  .no-data {
    display: flex;
    justify-content: center;
    padding: 20px 0;
  }
}

.line-item {
  padding-top: 12px;
  &:last-child {
    padding-top: 0;
  }
  .line-title {
    padding-left: 12px;
    line-height: 30px;
    font-weight: 500;
    color: #138aff;
    background-color: #e7f3fe;
    border-left: 2px solid #138aff;
  }
}
