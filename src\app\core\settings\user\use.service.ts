/*
 * File: use.service.ts
 * Project: elan-web
 * File Created: Wednesday, 24th November 2021 5:48:18 pm
 * Author: liucp
 * Description:
 * -----
 * Last Modified: Tuesday, 14th December 2021 3:27:30 pm
 * Modified By: liucp
 */

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, Observer, timer } from 'rxjs';
import { AbstractControl, AsyncValidatorFn, ValidationErrors } from '@angular/forms';
import { distinctUntilChanged, switchMap } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  detailUpdate?: any;
  constructor(private http: HttpClient, private _translate: TranslateService) {}
  /**
   * 检验编码唯一性
   */
  uniqueValidator = (old_value: null | string = null): AsyncValidatorFn => {
    return (control: AbstractControl) => {
      return timer(500).pipe(
        distinctUntilChanged(),
        switchMap(() => {
          if (control.value === null || control.value === '') {
            return Promise.resolve(null);
          } else {
            return new Observable((observer: Observer<ValidationErrors | null>) => {
              if (control.value !== old_value) {
                this.http.get<any>(`/user/login-name/${control.value}/check`).subscribe((res) => {
                  if (res.data) {
                    observer.next(null);
                  } else {
                    observer.next({ duplicated: true, msg: res.message });
                  }
                  observer.complete();
                });
              } else {
                observer.next(null);
                observer.complete();
              }
            });
          }
        })
      );
    };
  };
  /**
   * 请求操作用户列表
   * @param payload
   */
  getUserList(payload: any): Observable<any> {
    return this.http.post<any>('/user/list', payload);
  }
  /**
   * 获取权限列表
   * @param payload
   */
  getRolesList(payload: any): Observable<any> {
    return this.http.post<any>('/user/role/list', payload);
  }
  /**
   * 重置用户密码
   * @param  {number} id 用户id
   * @param  {any} payload // 新密码
   */
  resetPassword(id: number, payload: any): Observable<any> {
    return this.http.put<any>(`/user/${id}/password`, payload);
  }
  /**
   * 新建操作用户
   * @param payload
   */
  newUser(payload: any): Observable<any> {
    return this.http.post<any>('/user', payload);
  }
  /**
   * 获取操作用户详情
   */
  getUserDetail(id: number): Observable<any> {
    return this.http.get<any>(`/user/${id}`);
  }
  /**
   * 更新操作用户详情
   */
  editUserDetail(id: number, payload: any): Observable<any> {
    return this.http.put<any>(`/user/${id}`, payload);
  }
  /**
   * 删除操作用户
   */
  deleteUser(id: number): Observable<any> {
    return this.http.delete<any>(`/user/${id}`);
  }

  /**
   * 获取搜索下拉数据
   */
  getOptionLists(payload: any): Observable<any> {
    return this.http.post<any>('/user/search', payload);
  }

  translateValue(key: string) {
    return this._translate.instant(key);
  }
}
