import { Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';
import { HttpClient, HttpParams } from '@angular/common/http';
import { isNil } from 'lodash';

@Injectable()
export class DischargeCalculationDetailService {
  constructor(private http: HttpClient) {}

  /**
   * 获取部位下拉
   * @param  {} payload
   * @returns Observable
   */

  createHttpParams(payload: any): HttpParams {
    let params = new HttpParams();
    Object.keys(payload).forEach((key: string) => {
      if (!isNil(payload[key]) && String(payload[key]).trim() !== '') {
        params = params.append(key, payload[key]);
      }
    });
    return params;
  }
  getPositionSelect(payload: any): Observable<any> {
    // const params = this.createHttpParams({
    //   relation_task_id: payload,
    // });
    return this.http.post('/service/archive/v1/api/part/basic_option', {
      limit: 300,
      page: 1,
      column: 'name',
      value: '',
    });
  }

  /**
   * 颜色级联
   * @returns Observable
   */
  getColorCascade(): Observable<any> {
    return this.http.get('/service/archive/v1/color/basic_cascade');
  }

  /**
   * 排料单详情
   * @returns Observable
   */
  getDischargeDetail(io_uuid: any): Observable<any> {
    return this.http.post<any>('/service/procurement-inventory/layoutcons/v1/basic-info', {
      io_uuid,
    });
  }

  /**
   * bom维护面料列表
   * @returns Observable
   */
  getLcBom(io_uuid: any): Observable<any> {
    return this.http.post<any>('/service/procurement-inventory/layoutcons/v1/lc-bom-info', {
      io_uuid,
    });
  }

  /**
   * 面料档案下拉获取
   * @param  {} paramObj
   * @returns Observable
   */
  // getTableOptions(paramObj: any): Observable<any> {
  //   const params: any = new HttpParams({ fromObject: paramObj });
  //   return this.http.get<any>('/component/multi', { params }).pipe(map((res: any) => res.data.datalist));
  // }

  /**
   * 保存维护的bom信息
   * @param  {} paramObj
   * @returns Observable
   */
  saveBomInfo(paramObj: any): Observable<any> {
    return this.http.post<any>('/service/procurement-inventory/layoutcons/v1/lc-bom-save', paramObj);
  }

  getBom(id: any): Observable<any> {
    return this.http.get<any>(`/bulk_task_order/${id}`);
  }

  submitBom(id: any, payload: any): Observable<any> {
    return this.http.put<any>(`/bulk_task_order/${id}/batch/submit`, payload);
  }

  addSpec(material_id: any, spec: any) {
    return this.http.get<any>(`/service/archive/v1/raw_material/fabric/${material_id}`);
  }

  /**
   * 排料操作区列表
   * @returns Observable
   */
  getDischargeList(io_uuid: any): Observable<any> {
    return this.http.post<any>('/service/procurement-inventory/layoutcons/v1/lc-sheet-info', {
      io_uuid,
    });
  }

  batchGetSpecByMaterial(materialid: any) {
    return this.http.get<any>(`/service/archive/v1/api/material/detail/${materialid}`);
  }

  /**
   * 保存bom操作区数据
   * @param  {} paramObj
   * @returns Observable
   */
  saveDidChargeInfo(paramObj: any): Observable<any> {
    return this.http.post<any>('/service/procurement-inventory/layoutcons/v1/lc-sheet-save', paramObj);
  }

  // /**
  //  * 是否同步bom单
  //  * @param  {} paramObj
  //  * @returns Observable
  //  */
  // updateBom(paramObj: any): Observable<any> {
  //   return this.http.post<any>('/layoutcons/sync-bulk-bom', paramObj);
  // }

  /**
   * 保存io备注
   * @param  {} paramObj
   * @returns Observable
   */
  saveRemark(paramObj: any): Observable<any> {
    return this.http.post<any>('/service/procurement-inventory/layoutcons/v1/lc-remark-save', paramObj);
  }

  /**
   * 导出
   * @returns Observable
   */
  exportExcel(data: any): Observable<any> {
    return this.http.post<any>('/service/procurement-inventory/layoutcons/v1/export', data);
  }

  getList = (payload: object): Observable<any> => {
    return this.http.post<any>('/service/archive/v1/raw_material/list', payload);
  };

  getDetail(id: any, isFabric: any): Observable<any> {
    return this.http.get<any>(`/service/archive/v1/raw_material/ ${isFabric ? 'fabric' : 'accessory'}/${id}}`);
  }

  // getOptions(type: any): Observable<any> {
  //   return this.http.get(`/gc/raw_material/option?type=${type}`);
  //

  // 分配算料员
  submitMaterialsEmployeeOption(payload: any) {
    return this.http.post<any>('/service/procurement-inventory/layoutcons/v1/dist', payload);
  }

  /* 获取员工 */
  getPriceEvaluatorOptions(): Observable<any> {
    return this.http.get<any>('/organization/basic_option');
  }

  getDepartmentOption() {
    return this.http.get<any>('/service/archive/v1/api/organization/basic_option');
  }
}
