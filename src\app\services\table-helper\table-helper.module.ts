import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { OverlayModule } from '@angular/cdk/overlay';
import { TableHeaderMidifyDialogComponent } from './table-header-midify-dialog/table-header-midify-dialog.component';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { TranslateModule } from '@ngx-translate/core';
const nzModule = [NzButtonModule, NzCheckboxModule, NzIconModule, NzToolTipModule];
@NgModule({
  exports: [TableHeaderMidifyDialogComponent],
  declarations: [TableHeaderMidifyDialogComponent],
  imports: [CommonModule, FormsModule, DragDropModule, OverlayModule, ...nzModule, TranslateModule],
})
export class TableHelperModule {}
