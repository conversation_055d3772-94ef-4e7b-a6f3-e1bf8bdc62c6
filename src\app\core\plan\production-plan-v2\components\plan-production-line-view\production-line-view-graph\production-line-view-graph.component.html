<div class="wrap">
  <div class="graph-container">
    <app-graph-table
      [isEdit]="isEdit"
      [date]="dates"
      [graphOptions]="graphOptions"
      [data]="renderData"
      [checkboxConfig]="checkboxConfig"
      (selectedAll)="selectAll($event)"
      (daysChange)="daysChange($event)"
      (weeksChange)="weeksChange($event)"
      (optionsChange)="optionsChange($event)"
      (forwardDate)="forward()"
      (backwardsDate)="backwards()">
      <div ngProjectAs="app-io-wrap">
        <ng-container *ngFor="let d of renderData; index as i">
          <app-production-line-wrap [isEdit]="isEdit" (onSelectLine)="onSelectLine($event)" [data]="d"> </app-production-line-wrap>
        </ng-container>
      </div>
      <div ngProjectAs="app-graph-wrap">
        <ng-container *ngFor="let d of renderData; index as i">
          <app-production-line-graph-wrap
            [isEdit]="isEdit"
            [data]="d"
            [options]="options"
            [days]="days"
            [weeks]="weeks"
            [graphOptions]="graphOptions"
            (tapGraphItem)="tapGraphItem($event, i)"
            (hoverGraphItem)="hoverGraphItem($event)"
            (leaveGraphItem)="leaveGraphItem()"
            (rightClickGraph)="rightClick($event, i)">
          </app-production-line-graph-wrap>
        </ng-container>
      </div>
    </app-graph-table>
  </div>
  <div class="detailBar">
    <app-production-line-view-detail [detailBarItem]="detailBarItem"></app-production-line-view-detail>
  </div>
</div>

<ng-template #blankRightClicktpl let-productionLineId>
  <div class="rightMenuWrap"></div>
</ng-template>
<ng-template #graphItemRightClicktpl let-item>
  <div class="rightMenuWrap">
    <span>IO: {{ item?.order_code }}</span>
    <div class="divider"></div>
    <ng-container *ngIf="isEdit">
      <ng-container *ngIf="!item.is_accepted">
        <button (click)="onActionType(planGraphActionTypeEnum.split, item)">快速拆分</button>
        <button (click)="onActionType(planGraphActionTypeEnum.forwardMerge, item)">向前合并</button>
        <button (click)="onActionType(planGraphActionTypeEnum.forwardConnect, item)">向前衔接</button>
        <button (click)="onActionType(planGraphActionTypeEnum.AllConnect, item)">整体向前衔接</button>
        <button (click)="onActionType(planGraphActionTypeEnum.cancel, item)">取消分配</button>
      </ng-container>

      <ng-container *ngIf="item.is_pre_order">
        <button (click)="onActionType(planGraphActionTypeEnum.cancel, item)" *ngIf="item.is_accepted">取消分配</button>
        <button (click)="onActionType(planGraphActionTypeEnum.replace, item)">替换正式订单</button>
      </ng-container>
    </ng-container>
    <button (click)="onActionType(planGraphActionTypeEnum.detail, item)">查看详情</button>
  </div>
</ng-template>
