:host {
  .tree-container-header {
    text-align: left;
    width: 200px;
  }

  .calendar-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    .today:hover {
      background: #eef7ff;
      border: 1px solid #2996ff;
      color: #138aff;
      cursor: pointer;
    }
    .today {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48px;
      height: 30px;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #dfe2e5;
      margin-left: 14px;
      font-size: 14px;
      color: #515661;
    }

    .switch {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-left: 12px;
      width: 52px;
      height: 30px;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #dfe2e5;
      font-size: 14px;
      color: #262d48;
      cursor: pointer;
      .line {
        height: 10px;
        width: 1px;
        background: #d4d7dc;
      }

      .switch-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 25px;
        height: 28px;
      }

      .switch-button:hover {
        background: #eef7ff;
        color: #138aff;
      }
    }
    .month {
      margin-left: 10px;
      font-size: 20px;
      color: #262d48;
      line-height: 24px;
      font-family: 500;
    }
  }

  .dateCell {
    height: 100%;
    width: 100%;
    aspect-ratio: 1;
    background-color: #efffff;
  }
  .dateCell.disable {
    background-color: rgba(0, 0, 0, 0.05);
    color: grey;
  }
  .dateCell.current {
    background-color: #dcefff;
    color: #0e87f8;
  }
  .dateCellDay {
    position: absolute;
    top: 0;
    left: 0;
    width: 25%;
    height: 25%;
    align-content: center;
  }

  .dateCellSelect {
    position: absolute;
    bottom: 10px;
    width: 100%;
  }

  .treeItem {
    margin: 5px 0px;
    padding: 5px 10px;
    width: 100%;
    background-color: white;
  }
  .treeItem.selected {
    background-color: #d9f7ef;
    color: #25af7f;
  }
}
:host ::ng-deep {
  .ant-fullcalendar-header {
    display: none !important;
  }

  .ant-picker-cell .ant-picker-cell-inner {
    width: 100%;
    min-width: 100px;
    min-height: 100px;
  }

  .ant-picker-cell-disabled::before {
    background: unset;
  }

  .ant-picker-calendar-mini .ant-picker-content th {
    height: 30px;
  }

  .ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner::before {
    content: unset;
  }

  .ant-btn {
    text-align: left;
  }
}
