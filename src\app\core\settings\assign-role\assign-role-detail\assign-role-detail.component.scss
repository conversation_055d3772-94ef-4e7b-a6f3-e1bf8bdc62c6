.wrap {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.roleNameDetailTitleBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
}

.title {
  font-size: 14px;
  font-weight: 500;
  color: #138aff;
  line-height: 20px;
}

.bar {
  font-size: 14px;
  font-weight: 500;
  color: #54607c;
  line-height: 20px;
  cursor: pointer;

  span {
    transition: all 0.3s;
  }

  .actived {
    color: #222b3c;
    // text-decoration-line: underline;
    // text-decoration-color: #138AFF;
    // text-underline-offset: 2px;
    border-bottom: #138aff 1.5px solid;
    padding-bottom: 2px;
  }
}

.roleNameDetailFirstMenu {
  padding: 0 12px;
}

.roleNameDetailMenuContent {
  margin: 6px 12px 12px 12px;
  padding: 8px;
  background: rgba(237, 244, 251, 0.6);
  border-radius: 4px;
  overflow-y: auto;
  flex-grow: 1;
}
