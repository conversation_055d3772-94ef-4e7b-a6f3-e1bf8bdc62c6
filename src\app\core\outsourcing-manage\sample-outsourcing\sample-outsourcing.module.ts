import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SampleOutsouringRoutingModule } from './sample-outsourcing-routing.module';
import { FlcComponentsModule, FlcDirectivesModule, FlcOssUploadService } from 'fl-common-lib';
import { FlButtonModule } from 'fl-ui-angular/button';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { SampleOutsourcingService } from './sample-outsourcing.service';

import { SampleOutsourcingListComponent } from './list/sample-outsourcing-list.component';
import { SampleOutsouringDetailComponent } from './detail/sample-outsouring-detail.component';
import { SampleOutsourcingComponentsModule } from './components/sample-outsourcing-component.module';
import { RecommendFactoryModule } from 'fl-sewsmart-lib/recommend-factory';

import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzCascaderModule } from 'ng-zorro-antd/cascader';
import { NzFormModule } from 'ng-zorro-antd/form';
import { HttpClient } from '@angular/common/http';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzDividerModule } from 'ng-zorro-antd/divider';

const nzModules = [
  NzIconModule,
  NzButtonModule,
  NzToolTipModule,
  NzCascaderModule,
  NzDatePickerModule,
  NzFormModule,
  NzInputModule,
  NzSelectModule,
  NzModalModule,
  NzDividerModule,
];

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    SampleOutsouringRoutingModule,
    FlcComponentsModule,
    FlcDirectivesModule,
    FlButtonModule,
    ...nzModules,
    SampleOutsourcingComponentsModule,
    RecommendFactoryModule,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new MultiTranslateHttpLoader(http, [
            { prefix: './assets/i18n/outsourcing-manage/sample-outsourcing/', suffix: '.json' },
            { prefix: './assets/i18n/data-pkg/', suffix: '.json' },
            { prefix: './assets/i18n/components/material-detail/', suffix: '.json' },
            { prefix: './assets/i18n/components/material-selector/', suffix: '.json' },
            { prefix: './assets/i18n/material-selector/', suffix: '.json' },
            { prefix: './assets/i18n/material-plan-integration/', suffix: '.json' },
          ]);
        },
        deps: [HttpClient],
      },
    }),
  ],
  declarations: [SampleOutsourcingListComponent, SampleOutsouringDetailComponent],
  providers: [SampleOutsourcingService, FlcOssUploadService, NzMessageService],
})
export class SampleOutsourcingModule {
  constructor(
    public translateService: TranslateService,
    public ossUploadService: FlcOssUploadService,
    private _service: SampleOutsourcingService
  ) {
    const cl = translateService.currentLang;
    translateService.currentLang = '';
    translateService.use(cl).subscribe(() => {
      this._service.translateEventEmitter.next();
    });

    // oss upload service模块级别配置信息
    ossUploadService.setOssNamespace('sample-task');
    ossUploadService.setOssAuthUrl('/service/frontapi/v1/oss');
    ossUploadService.setOssAuthParams({
      biz_type: 'order',
    });

    const userInfo: string | null = localStorage.getItem('userInfo');
    if (userInfo) {
      const user = JSON.parse(userInfo);
      ossUploadService.setFilePath(`scm/sample-task/${user?.factory_code}`);
    }
  }
}
