import { HttpClient } from '@angular/common/http';
import { EventEmitter, Injectable } from '@angular/core';

@Injectable()
export class OrderSecProcessOutsourcingService {
  btnArr: Array<string> = [];
  constructor(private http: HttpClient) {}
  public translateEventEmitter = new EventEmitter<void>();
  eventEmitter = new EventEmitter();
  public get serviceUrl(): string {
    return '/service/order/v1';
  }
  public get archiveServiceUrl(): string {
    return '/service/archive/v1';
  }
  /**
   * 列表页面下拉
   */
  get optionsUrl(): string {
    return `${this.serviceUrl}/extra_process/list_option`;
  }
  /**
   * 大货单下拉列表
   */
  get bulkList(): string {
    return `${this.serviceUrl}/order/get_audi_pass_code`;
  }

  /**
   * 加工厂下拉列表
   */
  get plantOptions(): string {
    return `${this.archiveServiceUrl}/factory/basic_option`;
  }
  /**
   * 部位下拉列表
   */
  get partOptions(): string {
    return `${this.archiveServiceUrl}/part/basic_option`;
  }
  /**
   *二次工艺外发列表
   */
  getOutsourcingList(payload: any) {
    return this.http.post<any>(`${this.serviceUrl}/extra_process/list`, payload);
  }
  /**
   * 获取二次工艺详情
   * @param id
   */
  getDetailInfo(payload: any) {
    return this.http.post<any>(`${this.serviceUrl}/extra_process`, payload);
  }
  /**
   *审核通过
   */
  pass(id: number) {
    return this.http.post<any>(`${this.serviceUrl}/audit_pass/extra_process/${id}`, {});
  }
  /**
   *取消订单
   * @param id // 大货单id
   * @param cache    true, // 取消订单时，这个字段没有意义，随便填写
   * @param production_type    0 // 生产类型，1 成衣加工 2 二次工艺外发, 3 只分发自己不做生产
   */
  cancel(payload: { id: number; cache: boolean; production_type: number }) {
    return this.http.post<any>(`${this.serviceUrl}/extra_process/cancel`, payload);
  }
  /**
   * 退回修改
   * @param id
   * @param reason 退回原因
   */
  modify(payload: { id: number; reason: string }) {
    return this.http.post<any>(`${this.serviceUrl}/audit_return/extra_process`, payload);
  }
  /**
   * 更新分配单
   * @param payload
   */
  update(payload: any) {
    return this.http.put<any>(`${this.serviceUrl}/distribution/extra_process`, payload);
  }
  /**
   * 审核通过后的编辑提交 更新内容信息
   * @param payload
   */
  auditPassEdit(payload: any) {
    return this.http.put<any>(`${this.serviceUrl}/distribution/extra_process/edit`, payload);
  }
  /**
   * 新建二次工艺外发 查询大货单信息
   * @param payload
   * @param payload.id
   * @param payload.cache
   * @param payload.production_type
   */
  ioDetail(payload: { id: number; cache: boolean; production_type: number }) {
    return this.http.post<any>(`${this.serviceUrl}/order/${payload.id}`, payload);
  }
  /**
   * 新建二次工艺外发订单
   * @param
   */
  createDistributionOrder(param: any) {
    return this.http.post<any>(`${this.serviceUrl}/distribution/extra_process`, param);
  }
  /**
   * 获取订单剩余未分配数据
   * @param id
   * @param payload.id 大货单id
   * @param payload.cache = false
   * @param payload.production_type = 1 // 外发类型，1 成衣加工 2 二次工艺加工
   */
  undistribution(payload: { id: number; cache: boolean; production_type: number }) {
    return this.http.post<any>(`${this.serviceUrl}/undistribution`, payload);
  }

  /**
   * 订单详情二次工艺进度
   */
  getOutData(io_uuid: string) {
    return this.http.get<any>(`${this.serviceUrl}/bi-elan/io-outsource-data/${io_uuid}`);
  }

  /**
   * 获取二次工艺加工厂有数据修改的po
   * @param id 分配单id
   */
  getFactoryChangedPo(payload: any) {
    return this.http.post<any>(`${this.serviceUrl}/distribution/changed_pos`, payload);
  }
}
