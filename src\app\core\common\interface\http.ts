/**
 * 用来包装res.data.data类型的结果
 */
export interface ResponseDataAny {
  code: number | string;
  message: string;
  data: any;
}

/**
 * 用来包装res.data.data类型的结果
 */
export interface ResponseData<T> {
  code: number | string;
  message: string;
  data: T;
}

/**
 * 用来包装res.data.datalist类型的结果
 */
export interface ResponseDataListAny {
  code: string | number;
  msg: string;
  data: {
    datalist: [];
    count: number;
  };
}

export interface ResponseDataList<T> {
  code: string | number;
  msg: string;
  data: {
    datalist: T[];
    count: number;
  };
}

/**
 * 用来包装报错结果
 */
export interface ResponseError {
  code: number; // 返回code，表示失败状态
  reason: string; // 业务定义错误码
  message: string; // 失败信息，展示给用户
  metadata: object; // 错误相关
}
