import { Component, EventEmitter, Injectable, Input, NgModule, Output } from '@angular/core';
import { NzModalModule, NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { FlUiAngularModule } from 'fl-ui-angular';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { CommonModule } from '@angular/common';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { TranslateModule } from '@ngx-translate/core';
import { CancelActionModalComponent } from '../core/common/popup-modal/cancel-action-modal.component';

@Injectable({
  providedIn: 'root',
})
export class ModalService {
  constructor(public nzModalService: NzModalService) {}

  cancelActionModal({
    content = 'sure-notice.cancel',
    clickYes,
    clickNo,
  }: {
    content?: string;
    clickYes?: () => Promise<void> | void;
    clickNo?: () => Promise<void> | void;
  }): NzModalRef<_cancelActionModalComponent, boolean> {
    const modal = this.nzModalService.create<_cancelActionModalComponent, boolean>({
      nzWidth: '300px',
      nzWrapClassName: 'modal-outer',
      nzMaskClosable: false,
      nzContent: _cancelActionModalComponent,
      nzComponentParams: { content },
      nzFooter: null,
      nzOnCancel: () => {
        modal.close(false);
        clickNo?.call(this);
      },
    });
    modal.componentInstance?.clickYes.subscribe(() => {
      modal.close(true);
      clickYes?.call(this);
    });
    modal.componentInstance?.clickNo.subscribe(() => {
      modal.close(false);
      clickNo?.call(this);
    });
    return modal;
  }

  confirm(type: string) {
    const modal = this.nzModalService.create({
      nzWidth: '300px',
      nzWrapClassName: 'modal-outer',
      nzMaskClosable: false,
      nzClosable: false,
      nzContent: CancelActionModalComponent,
      nzFooter: null,
      nzComponentParams: {
        type: type,
      },
    });

    return modal;
  }
}

@Component({
  providers: [],
  template: `
    <div class="wrap">
      <i nz-icon [nzIconfont]="'icon-jinggao'" style="color: #ff5c33"></i>
      <div class="content">{{ content | translate }}</div>
    </div>
    <div class="bottomBar">
      <button nz-button nzType="text" nzShape="round" (click)="clickNo.emit()">{{ 'btn.cancel' | translate }}</button>
      <button nz-button flButton="default-positive" nzShape="round" (click)="clickYes.emit()">{{ 'btn.ok' | translate }}</button>
    </div>
  `,
  styles: [
    `
      .wrap {
        display: flex;
        align-items: center;
        margin-bottom: 30px;
      }
      .content {
        padding-left: 10px;
        font-size: 16px;
        font-weight: 500;
        color: #222b3c;
        line-height: 22px;
      }
      .bottomBar {
        display: flex;
        justify-content: flex-end;
        column-gap: 8px;
      }
    `,
  ],
})
export class _cancelActionModalComponent {
  @Input() content!: string;
  @Output() clickYes = new EventEmitter();
  @Output() clickNo = new EventEmitter();
}

const nzModules = [NzIconModule, NzModalModule, NzButtonModule];
@NgModule({
  declarations: [_cancelActionModalComponent],
  imports: [CommonModule, ...nzModules, TranslateModule, FlUiAngularModule],
})
export class OwnModalModule {}
