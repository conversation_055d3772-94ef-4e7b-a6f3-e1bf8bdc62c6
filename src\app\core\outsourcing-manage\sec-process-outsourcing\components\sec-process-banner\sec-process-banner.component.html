<div class="sec-process-tab-container">
  <div class="sec-process-tab-btn-area">
    <button
      *ngFor="let item of _extra_process_info ?? []; index as index"
      nz-button
      nzShape="round"
      flButton="pretty-default"
      class="btn-margin-right"
      [ngClass]="{ 'sec-process-tab-btn-active': activeIndex === index }"
      (click)="handleBtn(index)">
      {{ item.extra_process_name }}
    </button>
  </div>
  <div class="vertical-divider">
    <nz-divider nzType="vertical"></nz-divider>
  </div>
  <div class="sec-process-select-area" [formGroup]="detailForm">
    <nz-form-item>
      <nz-form-label [nzRequired]="true">{{ 'outsourcingComponents.部位' | translate }}</nz-form-label>
      <nz-form-control [flcErrorTip]="'outsourcingComponents.部位' | translate">
        <flc-dynamic-search-select
          [hidden]="editMode === editModeEnum.read"
          [cpnMode]="'multiple'"
          [maxTagCount]="2"
          [optAlwaysReload]="true"
          [dataUrl]="searchOptionFetchUrl"
          [transData]="{ value: 'value', label: 'label' }"
          [column]="'position_name'"
          [defaultOptions]="partSelectDefaultOptions"
          [formControlName]="'position_list'"
          (handleSearch)="handleChangeValueIo($event)">
        </flc-dynamic-search-select>
        <ng-template #selectClearIcon>
          <i nz-icon [nzIconfont]="'icon-cuowu'"></i>
        </ng-template>
        <div [hidden]="editMode !== editModeEnum.read">
          <flc-text-truncated [data]="partInfo"></flc-text-truncated>
        </div>
      </nz-form-control>
    </nz-form-item>
  </div>
</div>
