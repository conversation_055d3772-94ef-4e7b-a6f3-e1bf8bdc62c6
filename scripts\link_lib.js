'use strict';

// const dependencyModules = ['assets', 'material-package'];

// const bizModules = [
//   ...dependencyModules,
//   'autocheck-price-config', //报价配置
//   'basic-archive', //基础档案
//   'check-price-manage', //报价
//   'data-pkg', //基础资料包
//   'dev-manage', // 研发管理
//   'inventory', //库存
//   'material',
//   'packing', //装箱
//   'procurement', //采购
//   'product-plan-manage', //商品企划
//   'sample-manage', //打样管理
//   'style-center', //款式中心
//   'tech-archive', //技术档案
// ];

const os = require('os');

if (os.type() == 'Windows_NT') {
  //windows
  initLibWindows();
} else if (os.type() == 'Darwin') {
  //mac
  initLibLinux();
} else if (os.type() == 'Linux') {
  //Linux
  initLibLinux();
} else {
  console.log('=== os system is not supported ===');
}

function initLibLinux() {
  let exec = require('child_process').execSync;
  let libs = require('./lib.json'); // 本地自行添加lib.json文件，见下面示例

  // 添加 fl-sewsmart-lib 各模块的软连接
  exec(`ln -s ${libs.flSewsmartLib}/projects/fl-sewsmart-lib src/fl-sewsmart-lib`, {
    stdio: 'inherit',
  });

  // 添加 fl-common-lib 的软连接
  exec(`ln -s ${libs.flCommonLib}/projects/fl-common-lib/lib src/fl-common-lib`, {
    stdio: 'inherit',
  });
}

function initLibWindows() {
  let exec = require('child_process').execSync;
  let libs = require('./lib.json'); // 本地自行添加lib.json文件，见下面示例

  // 添加 fl-sewsmart-lib 各模块的软连接
  exec(`mklink /j  src\\fl-sewsmart-lib ${libs.flSewsmartLib}\\projects\\fl-sewsmart-lib `, {
    stdio: 'inherit',
  });

  // 添加 fl-common-lib 的软连接
  exec(`mklink /j src\\fl-common-lib ${libs.flCommonLib}\\projects\\fl-common-lib\\lib `, {
    stdio: 'inherit',
  });
}

/**
  sample: lib.json in mac/linux
  {
    "flSewsmartLib": "/Users/<USER>/work_ss/fl-sewsmart-lib",
    "flCommonLib": "/Users/<USER>/work_ss/fl-common-lib"
  }

  sample: lib.json in windows
  {
    "flSewsmartLib": "D:\\code\\fl-sewsmart-lib",
    "flCommonLib": "D:\\code\\fl-common-lib"
  }
**/
