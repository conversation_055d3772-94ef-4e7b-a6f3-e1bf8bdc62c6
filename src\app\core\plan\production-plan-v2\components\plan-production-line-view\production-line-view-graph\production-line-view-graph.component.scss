.wrap {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.graph-container {
  flex-grow: 1;
  overflow: hidden;
  box-shadow: 0px 2px 4px 1px rgb(203 214 226 / 50%);
  z-index: 1;
}
.detailBar {
  flex-shrink: 0;
  height: 56px;
  width: 100%;
  background: #ddebfc;
  box-shadow: 0px 2px 4px 1px rgba(162, 169, 186, 0.5);
  border-radius: 0px 0px 10px 10px;
}

.rightMenuWrap {
  display: flex;
  flex-direction: column;
  background: #fcfdff;
  box-shadow: 0px 1px 4px 1px rgba(188, 196, 223, 0.5);
  overflow: hidden;
  color: #54607c;
  span {
    font-size: 12px;
    line-height: 17px;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 4px 8px;
  }
  .divider {
    border-top: 1px solid #dbe0e6;
  }
  button {
    cursor: pointer;
    margin: 4px 8px;
    padding: 0;
    font-size: 14px;
    line-height: 20px;
    font-weight: 500;
    text-align: start;
    border: none;
    background-color: transparent;
    &:hover {
      color: #218dfe;
    }
  }
}

.overlayWrap {
  background: #ffffff;
  box-shadow: 0px 2px 4px 1px #d2d5e1;
  padding: 12px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .overlayTitle {
    font-size: 14px;
    font-weight: 500;
    color: #262d48;
    line-height: 20px;
    margin-bottom: 8px;
  }
  .actionBar {
    display: flex;
    justify-content: space-between;
  }
  .selectedLength {
    font-size: 12px;
    font-weight: 500;
    color: #797e89;
    line-height: 17px;
    > span {
      color: #007dff;
    }
  }
}

::ng-deep .colorTooltipWrap.ant-tooltip {
  max-width: 800px;
}

.po-temp {
  background: #f2f5f8;
  border-radius: 2px;
  padding: 0px 4px;
  font-size: 14px;
  font-weight: 500;
  color: #272e45;
  line-height: 20px;
  margin: 0 4px 0 0;
  display: inline-block;
}
