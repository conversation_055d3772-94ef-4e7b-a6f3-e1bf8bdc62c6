.subdepart-title {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  font-size: 18px;
  font-weight: 500;
  color: #54607c;
}

.subdepart-content {
  .subdepart-notice {
    background: #f4f7f9;
    border-radius: 0px 0px 8px 8px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #222b3c;
    text-align: center;
    padding: 8px;
    margin: -16px;
  }

  .subdepart-msg {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 500;
    color: #222b3c;
    margin-top: 45px;

    .content-form {
      display: flex;
      flex-direction: column;

      nz-form-label {
        width: 30%;
        text-align: right;
        font-size: 14px;
        font-weight: 500;
        color: #515661;
      }

      nz-form-control {
        max-width: 70%;
      }
    }
  }
}

.subdepart-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  //gap:8px;
}
::ng-deep .depart-name {
  color: #4d96ff;
}
