<div
  #graphItem
  class="graph-item"
  [ngStyle]="{
    left: data.left + 'px',
    top: data.top + 'px',
    width: data.width + 'px'
  }"
  [ngClass]="{ 'can-operate-item': data.plan_type !== 3, 'is-heightlight': data.isHighLight }"
  (mouseup)="data.plan_type === 3 ? null : tapGraph($event, graphItem)"
  (mouseleave)="leaveGraph($event)"
  (mouseenter)="onMouseEnter($event)">
  <div class="operator-wrap">
    <div
      class="operator-title"
      [ngStyle]="{
        background: data.is_selected ? '#2996ff' : backGroundColor
      }">
      <ng-container>
        <div class="alter-status-tag">
          <i nz-icon class="sample-circle" *ngIf="data?.overdue">
            <svg>
              <path
                d="M32 32m360 0l240 0q360 0 360 360l0 240q0 360-360 360l-240 0q-360 0-360-360l0-240q0-360 360-360Z"
                fill="#FFFFFF"
                p-id="8186"></path>
              <path
                d="M92 92m300 0l240 0q300 0 300 300l0 240q0 300-300 300l-240 0q-300 0-300-300l0-240q0-300 300-300Z"
                fill="#FF4141"
                p-id="8187"></path>
              <path
                d="M563.6 262.28l-266.16 276.6c-7.44 7.98-9.3 15.6-5.58 22.74 5.58 10.74 17.58 12.72 24.66 12.72h125.58l-68.94 174.72c-2.76 9.24-1.14 16.32 4.92 21.24 9 7.32 25.02 13.86 38.16 2.46 8.76-7.56 114.84-106.02 318.18-295.38 6.48-10.26 7.32-18.72 2.4-25.26-4.8-6.6-14.4-9.48-28.56-8.76L576.92 442.4l36-156c0.84-15.6-3.9-25.62-14.4-30.12-10.44-4.44-22.08-2.4-34.86 6z"
                fill="#FFFFFF"
                p-id="8188"></path>
            </svg>
          </i>
        </div>
        <div class="graph-item-text" [ngStyle]="{ color: getContrastColor(backGroundColor) }">
          <ng-container *ngIf="data?.plan_type == 1">
            <flc-text-truncated [data]="data.order_code"></flc-text-truncated>
          </ng-container>
          <ng-container *ngIf="data?.plan_type == 2">
            <flc-text-truncated [data]="data.po_code"></flc-text-truncated>
          </ng-container>
        </div>
      </ng-container>
    </div>
  </div>
</div>
