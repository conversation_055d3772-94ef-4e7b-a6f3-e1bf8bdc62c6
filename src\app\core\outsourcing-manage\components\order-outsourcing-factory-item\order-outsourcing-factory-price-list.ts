import { Component, Input, OnInit, AfterViewInit } from '@angular/core';
import { OrderGarmentOutsourcingService } from '../../garment-outsourcing/order-garment-outsourcing.service';
import { resizable } from 'fl-common-lib';
@Component({
  selector: 'app-order-outsourcing-factory-price-list',
  template: `
    <div class="inference-price-list">
      <div nz-row class="inference-price-list-header">
        <div nz-col nzSpan="11" class="list-header-cell">
          <nz-form-label>{{ 'outsourcingTableHeaderAndLabel.款式编码' | translate }}</nz-form-label>
          <flc-text-truncated [data]="priceInfo.style_code"></flc-text-truncated>
        </div>
        <div nz-col>
          <nz-divider nzType="vertical"></nz-divider>
        </div>
        <div nz-col nzSpan="11" class="list-header-cell">
          <nz-form-label>{{ 'outsourcingTableHeaderAndLabel.外发厂' | translate }}</nz-form-label>
          <flc-text-truncated [data]="priceInfo.factory_short_name"></flc-text-truncated>
        </div>
      </div>
      <nz-table
        #editRowTable
        nzBordered
        [nzData]="priceList"
        [nzFrontPagination]="false"
        [nzScroll]="{ x: priceList.length ? '100%' : null, y: priceList.length ? tableHeight + 'px' : null }"
        [nzLoading]="tableLoading">
        <thead>
          <tr>
            <th nzWidth="20%">{{ 'outsourcingTableHeaderAndLabel.价格来源' | translate }}</th>
            <th nzWidth="20%">{{ 'outsourcingTableHeaderAndLabel.单据编号' | translate }}</th>
            <th nzWidth="30%">{{ 'outsourcingTableHeaderAndLabel.时间' | translate }}</th>
            <th nzWidth="10%">{{ 'outsourcingTableHeaderAndLabel.数量' | translate }}</th>
            <th nzWidth="20%">{{ 'outsourcingTableHeaderAndLabel.含税单价(元)' | translate }}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of editRowTable.data" class="editable-row">
            <td>
              <flc-text-truncated [data]="priceSource[data.price_source]"></flc-text-truncated>
            </td>
            <td>
              <flc-text-truncated [data]="data.document_code"></flc-text-truncated>
            </td>
            <td>
              <flc-text-truncated [data]="data.time | date: 'yyyy/MM/dd HH:mm:ss'"></flc-text-truncated>
            </td>
            <td>
              <flc-text-truncated [data]="data.qty"></flc-text-truncated>
            </td>
            <td>
              <!-- 最大最小值一样的话，展示一个价格就行 -->
              <ng-container *ngIf="data.min_price === data.max_price; else priceTpl">
                <flc-text-truncated [data]="data.min_price"></flc-text-truncated>
              </ng-container>
              <ng-template #priceTpl>
                <flc-text-truncated [data]="data.min_price + '-' + data.max_price"></flc-text-truncated>
              </ng-template>
            </td>
          </tr>
        </tbody>
      </nz-table>
    </div>
  `,
  styles: [
    `
      .inference-price-list {
        margin: -16px;
        padding: 0 8px;
      }
      .inference-price-list-header {
        background-color: #f7f8fa;
        height: 36px;
        align-items: center;
        border-radius: 0 0 4px 4px;
        margin-bottom: 8px;
      }
      .list-header-cell {
        display: flex;
        justify-content: center;
        padding: 0 8px;
      }
      flc-text-truncated {
        align-self: center;
      }
      nz-table {
        margin-bottom: 12px;
      }
    `,
  ],
})
@resizable()
export class OrderOutsourcingFactoryPriceListComponent implements OnInit, AfterViewInit {
  @Input() priceInfo: any = {};
  priceList: any = [];
  priceSource = {
    OutCy: '成衣加工外发',
    Quotation: '报价单',
    Settle: '结算单',
  };
  tableLoading = false;
  tableHeight = 252;
  constructor(private outsourcingService: OrderGarmentOutsourcingService) {}

  ngOnInit() {
    (this as any).addResizePageListener();
    this.getDataList();
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.resizePage();
    });
  }

  ngOnDestroy(): void {
    (this as any).removeResizePageListener();
  }

  resizePage() {
    this.tableHeight = (window.innerHeight - 100) * 0.8 - 104;
  }

  getDataList() {
    this.tableLoading = true;
    const params = {
      style_code_uuid: this.priceInfo.style_code_uuid,
      ss_factory_code: this.priceInfo.factory_code,
    };
    this.outsourcingService.getReferencePrice(params).subscribe((res) => {
      if (res.code === 200) {
        this.tableLoading = false;
        this.priceList = res.data.datalist;
      }
    });
  }
}
