.main {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 64px);

  .capacity-panel {
    background-color: #ffffff;
    border-radius: 5px;
    margin-top: 8px;
  }

  .order-panel {
    flex: 3;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 8px 12px;
  }

  .capacity-search-box {
    nz-select {
      width: 100px;
    }
  }

  .plan-icon {
    display: inline-block;
    width: 14px;
    height: 14px;
    background: #d4d7dc;
    border-radius: 5px;
    vertical-align: text-bottom;
  }

  .po-button {
    margin-right: 8px;
    color: rgb(252, 131, 52);
    border-color: rgb(252, 131, 52);
  }

  .io-button {
    margin-right: 8px;
    color: #138aff;
    border-color: #138aff;
  }

  .number-span {
    background: #f0f2f5;
    border-radius: 4px;
    min-width: 50px;
    height: 20px;
    padding: 0 3px;
    display: inline-block;
    vertical-align: middle;
  }

  .expand-icon {
    color: #54607c;
    cursor: pointer;
  }

  .expand-icon:hover {
    color: #138aff;
  }
}

::ng-deep {
  .capacity-panel {
    // 表格自适应显示滚动条
    nz-spin,
    nz-table,
    .ant-spin-container,
    nz-table-inner-scroll > .ant-table-content,
    nz-table-inner-scroll {
      height: 100%;

      .ant-table.ant-table-fixed-header {
        // 36px => 页码高度+上边距
        max-height: calc(100% - 36px);
      }

      .ant-table-body {
        max-height: calc(100% - 33px) !important;
      }
    }
    // 表格自适应显示滚动条
    .ant-spin-container {
      display: flex;
      flex-direction: column;
    }
  }
}
