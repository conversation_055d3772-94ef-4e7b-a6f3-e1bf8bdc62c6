import { HttpClient } from '@angular/common/http';
import { EventEmitter, Injectable } from '@angular/core';

@Injectable()
export class OrderGarmentOutsourcingService {
  constructor(private http: HttpClient) {}
  public translateEventEmitter = new EventEmitter<void>();
  eventEmitter = new EventEmitter();
  btnArr: Array<string> = [];
  public get serviceUrl(): string {
    return '/service/order/v1';
  }

  /**
   * 列表页面下拉
   */
  get optionsUrl(): string {
    return `${this.serviceUrl}/garments/list_option`;
  }
  /**
   * 大货单下拉列表
   */
  get bulkList(): string {
    return `${this.serviceUrl}/order/get_audi_pass_code`;
  }

  /**
   *成衣外发列表
   */
  list(payload: any) {
    return this.http.post<any>(`${this.serviceUrl}/garments/list`, payload);
  }
  /**
   * 获取所有员工
   */
  getDepartmentTree() {
    return this.http.get<any>('/service/archive/v1/api/organization/basic_option');
  }
  /**
   *分配员工
   */
  assigneeEmployee(payload: any) {
    return this.http.post<any>(`${this.serviceUrl}/assignee_employee/garments`, payload);
  }
  /**
   *指派跟单员/QC
   */
  assigneeMerchandiser_QC(payload: any) {
    return this.http.post<any>(`${this.serviceUrl}/assignee_merchandiser_qc/garments`, payload);
  }
  /**
   *审核通过
   */
  process(id: number) {
    return this.http.post<any>(`${this.serviceUrl}/audit_pass/garments/${id}`, {});
  }

  complete(ids: string[]) {
    return this.http.post<any>('/service/procurement-inventory/v1/bulk-order/batch-garments-finish', { ids });
  }
  /**
   *取消订单
   * @param id // 订单id
   * @param cache    true, // 取消订单时，这个字段没有意义，随便填写
   * @param production_type    0 // 生产类型，1 成衣加工 2 二次工艺外发, 3 只分发自己不做生产
   */
  cancel(payload: { id: string; cache: boolean; production_type: number }) {
    return this.http.post<any>(`${this.serviceUrl}/garments/cancel`, payload);
  }
  /**
   * 删除 订单id
   * @param id // 订单id
   */
  delete(id: string) {
    return this.http.delete<any>(`${this.serviceUrl}/order/${id}`);
  }
  /**
   * 审核通过
   * @param id // 订单id
   */
  pass(id: string) {
    return this.http.post<any>(`${this.serviceUrl}/audit_pass/garments/${id}`, {});
  }
  /**
   * 批量审核通过
   * @param id // 订单id
   */
  passList(payload: { ids: string[] }) {
    return this.http.post<any>(`${this.serviceUrl}/batch_audit_pass/garments`, payload);
  }
  /**
   * 批量外发
   * @param payload
   */
  createList(payload: any) {
    return this.http.post<any>(`${this.serviceUrl}/batch_distribution/garments`, payload);
  }

  /**
   * 创建生产进度报表
   * @param payload
   */
  createProductionTable(payload: any) {
    return this.http.post<any>('/service/procurement-inventory/prod_status/v1/prod_status/create', payload);
  }
  // 批量创建生产进度报表
  batchCreateProductionTable(payload: any) {
    return this.http.post<any>('/service/procurement-inventory/prod_status/v1/prod_status/create/batch', payload);
  }
  createMaterialShipmentPlan(uuids: any[]) {
    return this.http.post<any>('/service/procurement-inventory/delivery/v1/delivery-plan/auto-create', { io_uuids: uuids });
  }
  /**
   * 退回修改
   * @param id
   * @param reason 退回原因
   */
  modify(payload: { id: string; reason: string }) {
    return this.http.post<any>(`${this.serviceUrl}/audit_return/garments`, payload);
  }
  /**
   * 批量退回修改
   * @param id
   * @param reason 退回原因
   */
  modifyList(payload: { ids: string[]; reason: string }) {
    return this.http.post<any>(`${this.serviceUrl}/batch_audit_return/garments`, payload);
  }
  /**
   * 获取订单剩余未分配数据
   * @param id
   * @param payload.id 大货单id
   * @param payload.cache = true
   * @param payload.production_type = 1 // 外发类型，1 成衣加工 2 二次工艺加工
   */
  undistribution(payload: { id: string; cache: boolean; production_type: number }) {
    return this.http.post<any>(`${this.serviceUrl}/undistribution`, payload);
  }
  /**
   * 查询成衣外发订单
   * @param id
   * @param payload.id 大货单id
   * @param payload.cache = true
   * @param payload.production_type = 1 // 外发类型，1 成衣加工 2 二次工艺加工
   */
  detail(payload: { id: string; cache: boolean; production_type: number; from: string }) {
    return this.http.post<any>(`${this.serviceUrl}/garments`, payload);
  }
  /**
   * 新建成衣外发 根基id 查询大货单信息
   * @param payload
   * @param payload.id
   * @param payload.cache
   * @param payload.production_type // 外发类型，1 成衣加工 2 二次工艺加工
   */
  ioDetail(payload: { id: string; cache: boolean; production_type: number }) {
    return this.http.post<any>(`${this.serviceUrl}/order/${payload.id}`, payload);
  }
  /**
   * 创建分配单
   * @param payload
   */
  create(payload: any) {
    return this.http.post<any>(`${this.serviceUrl}/distribution/garments`, payload);
  }

  /**
   * 更新分配单
   * @param payload
   */
  update(payload: any) {
    return this.http.put<any>(`${this.serviceUrl}/distribution/garments`, payload);
  }
  /**
   * 审核通过后的编辑提交 更新内容信息
   * @param payload
   */
  auditPassEdit(payload: any) {
    return this.http.put<any>(`${this.serviceUrl}/distribution/garments/edit`, payload);
  }
  /**
   * 审核通过后的编辑提交 更新内容信息
   * @param payload
   */
  checkFactory(id: number) {
    return this.http.get<any>('/factory/check/ss_url', { params: { id } });
  }
  /**
   * 订单详情二次工艺进度
   */
  getProdProgress(io_uuid: string) {
    return this.http.get<any>(`${this.serviceUrl}/bi-elan/io-production-progress/${io_uuid}`);
  }
  /**
   * 获取二次工艺加工厂有数据修改的po
   * @param id 分配单id
   */
  getFactoryChangedPo(payload: any) {
    return this.http.post<any>(`${this.serviceUrl}/distribution/changed_pos`, payload);
  }
  /* 获取参考价 */
  getReferencePrice(payload: any) {
    return this.http.post<any>(`${this.serviceUrl}/ProcessSettlement/reference-price`, payload);
  }
  /* 订单完成 */
  orderFinish(id: number) {
    return this.http.post<any>(`${this.serviceUrl}/garments/finish`, { id });
  }

  /**
   * 工厂下拉
   */
  getFactorysOptions() {
    return this.http.post<any>('/service/procurement-inventory/outsourcing_management/v1/outsourcing_management/factory-option', {
      column: 'factory_name',
      factory_type: 1,
      limit: 3000,
      page: 1,
      value: '',
    });
  }

  /* 成衣外发订单列表工段维度 */
  getList2(payload: any) {
    return this.http.post<any>(`${this.serviceUrl}/garments/factory/list`, payload);
  }
  /* 成衣外发订单列表工段维度导出 */
  exportList2(payload: any) {
    return this.http.post<any>(`${this.serviceUrl}/garments/factory/list/export`, payload);
  }

  /* 大货订单-一键入库 */
  handleOneClickInbound(payload: any) {
    return this.http.post<any>('/service/procurement-inventory/outsourcing_management/v1/outsourcing_management/batch-inbound', payload);
  }

  // 生成预付款
  generateAdvancePayment(payload: any) {
    return this.http.post<any>('/service/procurement-inventory/settlement/v1/process-payment/bulk-order-one-click-create', payload);
  }
}
