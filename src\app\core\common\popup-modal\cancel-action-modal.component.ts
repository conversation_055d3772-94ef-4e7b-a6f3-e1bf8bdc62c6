import { Component, Input, OnInit } from '@angular/core';
import { NzModalRef } from 'ng-zorro-antd/modal';

@Component({
  selector: 'app-cancel-action-modal',
  template: `
    <div class="wrap">
      <i nz-icon [nzIconfont]="'icon-jinggao'" style="color: #ff5c33"></i>
      <div class="content" *ngIf="type == 'confirm-cancel'">{{ 'sure-notice.cancel' | translate }}</div>
      <div class="content" *ngIf="type == 'confirm-delete'">{{ 'sure-notice.确认当前删除操作' | translate }}</div>
      <div class="content" *ngIf="type == 'confirm-leave'">{{ 'sure-notice.当前页面未保存，确认离开' | translate }}</div>
    </div>
    <div class="bottomBar">
      <button nz-button nzType="text" nzShape="round" (click)="close(false)">{{ 'btn.cancel' | translate }}</button>
      <button nz-button flButton="default-positive" nzShape="round" (click)="close(true)">{{ 'btn.ok' | translate }}</button>
    </div>
  `,
  styles: [
    `
      .wrap {
        display: flex;
        align-items: center;
        margin-bottom: 30px;
      }
      .content {
        padding-left: 10px;
        font-size: 16px;
        font-weight: 500;
        color: #222b3c;
        line-height: 22px;
      }
      .bottomBar {
        display: flex;
        justify-content: flex-end;
        column-gap: 8px;
      }
    `,
  ],
})
export class CancelActionModalComponent implements OnInit {
  @Input() type?: string;

  constructor(private modelRef: NzModalRef) {}

  ngOnInit() {}

  close(val: boolean) {
    this.modelRef.close(val);
  }
}
