<div class="search-head">
  <div class="selects" *ngIf="!isFold">
    <div>
      <span>{{ 'extraProcess.IO' | translate }}:</span>
      <flc-dynamic-search-select
        [(ngModel)]="searchData.io_code"
        [canClear]="false"
        [column]="'io_code'"
        [payLoad]="{ cache: false }"
        [transData]="{ value: 'value', label: 'label' }"
        [dataUrl]="'/service/order/v1/list_option'"
        (ngModelChange)="getData(true)">
      </flc-dynamic-search-select>
    </div>
    <div>
      <span>{{ 'extraProcess.交付单' | translate }}:</span>
      <nz-select nzPlaceHolder="请选择" nzAllowClear [(ngModel)]="searchData.po_code" (ngModelChange)="getData()">
        <nz-option *ngFor="let p of poOptions" [nzValue]="p" [nzLabel]="p"></nz-option>
      </nz-select>
    </div>
    <div>
      <span>{{ 'extraProcess.颜色' | translate }}:</span>
      <nz-select nzPlaceHolder="请选择" nzAllowClear [(ngModel)]="searchData.color" (ngModelChange)="getData()">
        <nz-option *ngFor="let p of colorOptions" [nzValue]="p" [nzLabel]="p"></nz-option>
      </nz-select>
    </div>
    <div>
      <span>{{ 'extraProcess.部位' | translate }}:</span>
      <nz-select nzPlaceHolder="请选择" nzAllowClear [(ngModel)]="searchData.position" (ngModelChange)="getData()">
        <nz-option *ngFor="let p of positionOptions" [nzValue]="p" [nzLabel]="p"></nz-option>
      </nz-select>
    </div>
  </div>
  <h2 *ngIf="isFold">{{ 'extraProcess.二次工艺跟踪' | translate }}</h2>
  <div class="btns">
    <button nz-button flButton="grey-reset" nz-tooltip nzTooltipTitle="重置" (click)="reset()">
      <i nz-icon>
        <svg>
          <path
            d="M958.72 225.856l-82.688 211.136-13.76 35.2c-3.776 9.728-14.784 14.528-24.512 10.688l-35.2-13.76L591.36 386.368C581.632 382.528 576.832 371.584 580.672 361.856l13.76-35.2c3.776-9.728 14.784-14.528 24.512-10.688l174.912 68.544c-50.56-124.416-171.584-212.672-314.112-212.672-187.84 0-340.16 152.32-340.16 340.16s152.256 340.16 340.16 340.16c148.032 0 273.6-94.72 320.384-226.752l79.296 0c-49.408 174.4-209.408 302.336-399.68 302.336C250.112 927.744 64 741.632 64 512s186.112-415.744 415.744-415.744c157.056 0 293.376 87.296 363.968 215.872l44.608-113.856c3.776-9.728 14.784-14.528 24.512-10.688l35.2 13.76C957.696 205.184 962.496 216.128 958.72 225.856z"></path>
        </svg>
      </i>
    </button>

    <button
      nz-button
      flToggleButton
      (toggleActiveChange)="isFold = !isFold"
      [toggleActive]="!isFold"
      nz-tooltip
      [nzTooltipTitle]="isFold ? '展开筛选' : '折叠筛选'">
      <i nz-icon>
        <svg>
          <path
            d="M839.93 285.718c5.258-5.246 11.167-11.165 15.006-17.923 28.762-35.707 32.198-89.755 8.064-128.98-24.312-39.51-60.35-59.544-107.103-59.544H177.253c-11.213 0-22.8 0-32.89 3.182-24.706 5.976-47.309 20.578-63.69 41.156C63.987 144.56 54.8 170.46 54.8 196.534c0 33.835 12.893 63.821 36.317 84.475 74.405 71.852 148.69 146.108 220.57 217.993v237.254l0.124 1.997c3.907 31.238 27.638 48.302 56.362 35.966l0.5-0.235c15.541-7.767 24.45-23.409 24.45-42.917V487.155c0-16.464-5.074-29.89-15.086-39.902-35.904-35.909-71.28-73.224-105.485-109.316-35.266-37.21-71.736-75.686-109.853-113.803l-1.867-1.886-2.25-1.11c-0.53-0.364-1.7-1.89-2.588-3.056-0.975-1.263-1.925-2.458-2.923-3.548-6.582-8.433-7.97-15.6-4.897-25.271 5.343-12.092 16.057-25.969 29.074-25.969h583.834c7.109 0 13.963 3.84 19.295 10.819 6.917 9.058 9.49 21.034 6.644 31.489-1.45 5.784-3.864 8.404-11.156 15.71-80.394 77.794-157.185 154.579-238.482 235.872l-0.432 0.437c-8.237 8.227-17.688 20.203-17.688 39.907V907.52c0 5.04 0 19.243 10.382 30.61 0 0 3.153 7.118 16.747 12.287 16.8 6.385 33.211-0.888 33.211-0.888l4.286-3.206c16.81-12.614 16.81-32.26 16.81-44.001v-387.75c40.2-40.103 81.855-79.113 122.165-116.86 41.05-38.428 79.814-74.736 117.067-111.994z m89.82 525.284H709.184c-23.043 0-36.815 18.724-36.815 36.83v10.388c0 23.05 18.715 36.82 36.815 36.82H929.75c23.054 0 36.825-18.72 36.825-36.821V847.83c0-22.026-14.798-36.83-36.825-36.83z m0-132.327H709.184c-23.043 0-36.815 18.725-36.815 36.83v7.772c0 23.059 18.715 36.834 36.815 36.834H929.75c23.054 0 36.825-18.724 36.825-36.834v-7.772c0-18.105-13.77-36.83-36.825-36.83z m0-127.147H709.184c-23.043 0-36.815 18.724-36.815 36.83v5.19c0 23.043 18.715 36.82 36.815 36.82H929.75c23.054 0 36.825-18.72 36.825-36.82v-5.19c0-22.03-14.798-36.83-36.825-36.83z"></path>
        </svg>
      </i>
    </button>
    <nz-divider nzType="vertical"></nz-divider>
    <button class="back" nz-button (click)="goBack()">{{ 'extraProcess.返回' | translate }}</button>
  </div>
</div>
<div style="height: 8px"></div>
<div class="content">
  <div class="filt">
    <nz-radio-group [(ngModel)]="dimension">
      <label nz-radio-button [nzValue]="1">{{ 'extraProcess.区间维度' | translate }}</label>
      <label nz-radio-button [nzValue]="2">{{ 'extraProcess.包裹维度' | translate }}</label>
    </nz-radio-group>
    <nz-divider nzType="vertical"></nz-divider>
    <nz-tabset nzType="card" [nzSelectedIndex]="extraIndex">
      <nz-tab *ngFor="let extra of extraOptions" [nzTitle]="extra.label" (nzClick)="setExtra(extra.value)"></nz-tab>
    </nz-tabset>
  </div>
  <nz-spin [nzSpinning]="loading">
    <nz-empty *ngIf="data.length === 0"></nz-empty>
    <div class="content-detail">
      <div *ngFor="let item of data">
        <div class="head">
          <label>
            {{ 'extraProcess.IO' | translate }}:
            <span>{{ item.io_code }}</span>
          </label>
          <label>
            {{ 'extraProcess.PO' | translate }}:
            <span>{{ item.po_code }}</span>
          </label>
          <label>
            {{ 'extraProcess.走货国' | translate }}:
            <span>{{ item.country }}</span>
          </label>
          <label>
            {{ 'extraProcess.颜色' | translate }}:
            <span>{{ item.color_name }}</span>
          </label>
          <label>
            {{ 'extraProcess.实裁数' | translate }}:
            <span>{{ item.qty }}</span>
          </label>
          <label>
            {{ 'extraProcess.部位' | translate }}:
            <span>{{ item.position_name }}</span>
          </label>
        </div>
        <div class="body">
          <div class="col" *ngFor="let col of item.specs">
            <div class="col-info">
              <b>{{ col.spec_name }}</b>
              <span>{{ 'extraProcess.实裁' | translate }} {{ col.qty }}</span>
            </div>
            <div *ngFor="let row of col.package_sections" class="col-row">
              <div class="card" [class]="'card-status-' + row.status[0]">
                <div class="pkg-head">
                  <flc-text-truncated [data]="row.package_code"></flc-text-truncated>
                  <span *ngIf="row.status.length === 1" [class]="'status-' + row.status[0]">{{ parseStatus(row.status[0]) }}</span>
                  <ng-container *ngIf="row.status.length > 1">
                    <span *ngFor="let s of row.status" [class]="'status-' + s" nz-tooltip [nzTooltipTitle]="parseStatus(s)">{{
                      parseStatus(s, true)
                    }}</span>
                  </ng-container>
                </div>
                <div class="pkg-detail">
                  <span class="qty"
                    >({{ row.package_qty }}{{ 'extraProcess.包' | translate }}/{{ row.qty }}{{ 'extraProcess.件' | translate }})</span
                  >
                  <div>
                    <span *ngIf="row.status.includes(3) || row.status.includes(4)"
                      >{{ 'extraProcess.进度' | translate }}{{ row.progress }}%</span
                    >
                    <span *ngIf="row.defective_qty" class="defective"
                      >{{ 'extraProcess.不良' | translate }}({{ row.defective_package_qty }}{{ 'extraProcess.包' | translate }}/<span
                        class="danger"
                        >{{ row.defective_qty }}</span
                      >{{ 'extraProcess.件' | translate }})</span
                    >
                  </div>
                </div>
              </div>
              <!-- <ng-container *ngIf="row.fold"> -->
              <ng-container *ngIf="dimension === 2">
                <div class="pkg-row" *ngFor="let card of row.packages; let index = index">
                  <span
                    ><b>{{ card.package_code }}</b
                    >/{{ card.sum }}{{ 'extraProcess.件' | translate
                    }}<ng-container *ngIf="card.defectiveSum"
                      >/<span class="danger">{{ card.defectiveSum }}</span></ng-container
                    ></span
                  >
                  <span *ngIf="card.status.length === 1" [class]="'status-' + card.status[0]">{{ parseStatus(card.status[0]) }}</span>
                  <span
                    *ngIf="card.status.length > 1"
                    nz-tooltip
                    [nzTooltipTitle]="positionDetail"
                    [nzTooltipOverlayStyle]="{ 'max-width': 'unset' }">
                    <span *ngFor="let s of card.status; let idx = index" [class]="'status-' + s"
                      >{{ parseStatus(s, true) }}<nz-divider nzType="vertical" *ngIf="idx < card.status.length - 1"></nz-divider
                    ></span>
                  </span>
                  <ng-template #positionDetail>
                    <div class="position-tooltip">
                      <table>
                        <tbody>
                          <tr *ngFor="let p of card.positions">
                            <td>
                              {{ p.position_name }}
                              ({{ p.qty }})
                            </td>
                            <td>&nbsp;|&nbsp;</td>
                            <td>
                              {{ parseStatus(p.status) }}
                              &nbsp;
                              <ng-container *ngIf="p.defective_qty"
                                >{{ 'extraProcess.不良' | translate }}({{ p.defective_qty }})</ng-container
                              >
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </ng-template>
                </div>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
    </div>
  </nz-spin>
</div>
