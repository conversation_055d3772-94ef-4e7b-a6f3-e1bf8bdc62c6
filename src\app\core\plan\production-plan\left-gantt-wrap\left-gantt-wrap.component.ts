import { Component, ElementRef, Input, OnChanges, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { ProductionPlanService } from '../production-plan.service';

@Component({
  selector: 'app-left-gantt-wrap',
  templateUrl: './left-gantt-wrap.component.html',
  styleUrls: ['./left-gantt-wrap.component.scss'],
})
export class LeftGanttWrapComponent implements OnInit, OnChanges {
  @Input() data: any;
  @Input() graphOptions: { item_dimension: 'io' | 'po' } = {
    item_dimension: 'io',
  };
  @ViewChild('bodyLeftWrap') bodyLeftWrap!: ElementRef;
  get isHeightLight(): boolean {
    return this.data.isHighLight;
  }
  constructor(private _service: ProductionPlanService) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.graphOptions && changes?.graphOptions?.currentValue && !changes.graphOptions.firstChange) {
      this.calcHeight();
    }
  }

  calcHeight() {
    const plan_items = this.graphOptions?.item_dimension === 'io' ? [...this.data.order_plan_items] : [...this.data.po_plan_items];
    const lineList = [...this.data.schedule_plan_items, ...plan_items];
    const sortedList = this._service?.sort(lineList);
    const tempList: any[] = [];
    sortedList.forEach((e, i: number) => {
      e.forEach((d: any) => {
        d.top = i * 30 + 10;
        tempList.push(d);
      });
    });
    this.data.height = sortedList.length * 30 + 10;
  }
}
