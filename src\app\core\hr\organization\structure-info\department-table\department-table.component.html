<div class="depart-table-outer">
  <div class="depart-table-header">
    <div>{{ '直属人员' | translate }}</div>
    <div>
      <ng-container *ngIf="_service.departStatus && _service.departForm">
        <ng-container *ngIf="_service.hasDeptDataAuth; else noDeptDataAuthTpl">
          <nz-upload
            [(nzFileList)]="fileList"
            [nzLimit]="1"
            [nzShowUploadList]="false"
            [nzBeforeUpload]="beforeUpload"
            [nzCustomRequest]="handleUpload"
            *ngIf="_service.btnArr.includes('settings:org-user_import')">
            <a nz-button nzType="link" flButton="link-minor">{{ 'btn.batch_import' | translate }}</a>
          </nz-upload>
        </ng-container>
        <ng-template #noDeptDataAuthTpl>
          <a
            *ngIf="_service.btnArr.includes('settings:org-user_import')"
            nz-button
            nzType="link"
            flButton="link-minor"
            (click)="checkDeptDataAuth()"
            >{{ 'btn.batch_import' | translate }}</a
          >
        </ng-template>

        <nz-divider nzType="vertical"></nz-divider>
        <a
          nz-button
          nzType="link"
          flButton="link-minor"
          (click)="_service.addedEmployee = null"
          [href]="'../../../../../../assets/resource/' + (lang === 'en' ? '员工导入模板-en' : '员工导入模板') + '.xlsx'"
          [download]="('员工导入模板' | translate) + '.xlsx'"
          *ngIf="_service.btnArr.includes('settings:org-download_template')">
          {{ 'btn.download_tpl' | translate }}
        </a>
        <button nz-button flButton="minor" (click)="addEmployee()" *ngIf="_service.btnArr.includes('settings:org-user_create')">
          <i nz-icon [nzIconfont]="'icon-xinjian1'"></i> {{ 'structure-btn.新建员工' | translate }}
        </button>
      </ng-container>
    </div>
  </div>

  <div style="margin-top: 8px">
    <nz-table
      class="zebra-striped-table"
      #nzTable
      [nzFrontPagination]="false"
      [nzScroll]="{ x: '800px', y: tableMaxTable + 'px' }"
      [(nzPageSize)]="_service.pageSize"
      [(nzPageIndex)]="_service.pageIndex"
      [nzLoading]="_service.loading"
      [nzData]="_service.tableData"
      [nzShowTotal]="totalTemplate"
      [nzTotal]="_service.total"
      [nzPageSizeOptions]="[20, 30, 40, 50]"
      (nzPageIndexChange)="_service.getEmployeeList()"
      (nzPageSizeChange)="_service.getEmployeeList(null, true)"
      [nzShowSizeChanger]="_service.total > 1"
      nzSize="middle"
      nzBordered="ture">
      <thead>
        <tr>
          <th nzLeft nzWidth="32px">#</th>
          <ng-container *ngFor="let item of renderHeaders">
            <th
              [nzLeft]="item.pinned"
              nz-resizable
              *ngIf="item.visible"
              nzPreview
              [nzWidth]="item.width"
              [nzMinWidth]="66"
              [nzMaxWidth]="360"
              [nzMinWidth]="56"
              (nzResizeEnd)="onResize($event, item.label)">
              {{ 'employee-drawer.' + item.label | translate }}
              <nz-resize-handle nzDirection="right">
                <div class="resize-trigger"></div>
              </nz-resize-handle>
            </th>
          </ng-container>
          <th nzWidth="90px" nzRight>
            {{ 'employee-drawer.操作' | translate }}
            <a class="setting-btn" [ngStyle]="{ color: btnHighLight ? '#4D96FF' : '' }" (click)="changeHeader($event)">
              <i nz-icon nzType="setting" nzTheme="fill"></i>
            </a>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr [ngClass]="!item.status ? 'quit' : ''" *ngFor="let item of _service.tableData; let i = index">
          <td [ngStyle]="{ background: item.id === _service.addedEmployee ? '#EEF6FF' : '' }" nzLeft>{{ i + 1 }}</td>
          <ng-container *ngFor="let col of renderHeaders">
            <td
              *ngIf="col.visible"
              [ngClass]="col.key === 'roles' ? 'roles-row' : ''"
              [ngStyle]="{ background: item.id === _service.addedEmployee ? '#EEF6FF' : '' }">
              <ng-container *ngIf="col.key === 'roles'; else others">
                <ng-container *ngIf="item[col.key] && item[col.key].length > 0; else nullLength">
                  <app-text-truncated [template]="titleTemplate"></app-text-truncated>
                  <ng-template #titleTemplate>
                    <span class="roles" *ngFor="let con of item[col.key]">
                      {{ con }}
                    </span>
                  </ng-template>
                </ng-container>
                <ng-template #nullLength>-</ng-template>
              </ng-container>

              <ng-template #others>
                <ng-container *ngIf="col.key === 'status'; else timeOther">
                  <ng-container> {{ (item[col.key] ? 'employee-drawer.在职' : 'employee-drawer.离职') | translate }}</ng-container>
                </ng-container>
                <ng-template #timeOther>{{ item[col.key] | noValue }}</ng-template>
              </ng-template>
            </td>
          </ng-container>

          <td class="op-td" nzRight [ngStyle]="{ background: item.id === _service.addedEmployee ? '#EEF6FF' : '' }">
            <ng-container *ngIf="_service.departStatus">
              <button
                nz-button
                nzType="link"
                flButton="link"
                (click)="editEmployee(item)"
                *ngIf="_service.btnArr.includes('settings:org-user_update')">
                <i nz-icon [nzIconfont]="'icon-caozuolan_bianji1'"></i>
              </button>
              <button
                nz-button
                nzType="link"
                flButton="link"
                class="delete-btn"
                (click)="deleteEmployee(item)"
                *ngIf="_service.btnArr.includes('settings:org-user_delete')">
                <i nz-icon [nzIconfont]="'icon-caozuolan_shanchu1'"></i>
              </button>
            </ng-container>
          </td>
        </tr>
      </tbody>
    </nz-table>
    <ng-template #totalTemplate>
      <div
        style="margin-right: 16px; color: #000000; font-size: 14px"
        [innerHTML]="
          'common.共x条,第x页'
            | translate
              : { total: _service.total, currentPage: _service.pageIndex, totalPage: _service.total / _service.pageSize | mathCeil }
        "></div>
    </ng-template>
  </div>
</div>
<app-employee-drawer></app-employee-drawer>
<app-delete-modal (handleDelete)="handleDelete($event)"></app-delete-modal>
