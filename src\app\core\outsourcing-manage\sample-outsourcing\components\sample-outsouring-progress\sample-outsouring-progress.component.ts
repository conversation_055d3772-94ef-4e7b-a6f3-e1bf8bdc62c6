import { Component, Input, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { format } from 'date-fns';
import { ProgressStep } from 'fl-common-lib';
import { SampleOutsourcingService } from '../../sample-outsourcing.service';
import { SampleOutsouringStepsEnum } from '../../modal/sample-outsourcing.enum';

@Component({
  selector: 'app-sample-outsouring-progress',
  templateUrl: './sample-outsouring-progress.component.html',
  styleUrls: ['./sample-outsouring-progress.component.scss'],
})
export class SampleOutsouringProgressComponent implements OnInit {
  @ViewChild('logisticsRef') logisticsRef!: TemplateRef<any>;
  constructor(private _translate: TranslateService, private _service: SampleOutsourcingService) {}

  translateName = 'sampleOutsourcing.sampleOutsourcingProgress.';

  @Input() set id(val: string | number) {
    if (val && val !== 'new') {
      this.stepItems = [];
      this.getProgressList(val);
    }
  }

  logisticsInfo = '';

  initialSteps = [
    { title: '接单', key: SampleOutsouringStepsEnum.accept_order },
    { title: '分配版师', key: SampleOutsouringStepsEnum.assign_printer },
    { title: '打版完成', key: SampleOutsouringStepsEnum.print_complete },
    { title: '分配样衣工', key: SampleOutsouringStepsEnum.assign_dresser },
    { title: '样衣制成', key: SampleOutsouringStepsEnum.cloth_complete },
    { title: '样衣寄出', key: SampleOutsouringStepsEnum.post_sample },
    { title: '样衣收货', key: SampleOutsouringStepsEnum.accept_sample },
  ];

  ngOnInit() {}

  stepItems: ProgressStep[] = [];
  getProgressList(id: string | number) {
    this._service.getSampleProgress(id).subscribe((res: any) => {
      this.stepItems = this.initialSteps.map((d) => {
        const item = res.data.find((v: any) => v.progress_status === d.key);
        const result = {
          title: this._translate.instant(this.translateName + d.title),
          inProcess: !item,
          description: item?.gen_time ? format(item?.gen_time, 'yyyy/MM/dd') : undefined,
        };

        // 样衣寄出节点
        if (item?.progress_status === SampleOutsouringStepsEnum.post_sample) {
          this.logisticsInfo = item?.post_no;
          result['subtitleTpl'] = this.logisticsInfo ? this.logisticsRef : undefined;
        }

        return result;
      });
    });
  }
}
