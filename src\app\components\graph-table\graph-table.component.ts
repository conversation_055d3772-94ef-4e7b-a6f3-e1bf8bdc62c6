import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import {
  differenceInDays,
  format,
  startOfDay,
  endOfDay,
  getDate,
  isWeekend,
  getMonth,
  getWeek,
  getYear,
  isToday,
  addDays,
  isEqual,
} from 'date-fns';
import { DayRange, DimensionType, GanttCellWidth, WeekRange } from './graph-table';

@Component({
  selector: 'app-graph-table',
  templateUrl: './graph-table.component.html',
  styleUrls: ['./graph-table.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GraphTableComponent implements OnInit, OnChanges {
  @ViewChild('planBack', { static: true }) planBack!: ElementRef<HTMLElement>;
  @ViewChild('overlay', { static: true }) overlay!: ElementRef<HTMLElement>;
  @ViewChild('wrapLeft', { static: true }) wrapLeft!: ElementRef<HTMLElement>;
  @ViewChild('wrapBody', { static: true }) wrapBody!: ElementRef<HTMLElement>;
  @ViewChild('wrapTop', { static: true }) wrapTop!: ElementRef<HTMLElement>;
  @ViewChild('wrapTopScroll', { static: true }) wrapTopScroll!: ElementRef<HTMLElement>;
  @Input() flexBasis = 120;
  @Input() isEdit = false;
  @Input() data: any[] = [];
  @Input() rest_list: string[] = []; // 休息日
  @Input() date = {
    // 绘制时间表头的起止日期
    start_date: new Date(),
    end_date: new Date(),
  };
  @Input() searchDate = {
    start_date: new Date(),
    end_date: new Date(),
  };
  @Output() forwardDate = new EventEmitter(); // 向前10天
  @Output() backwardsDate = new EventEmitter(); // 向后10天

  maskVisible = false;
  options = {
    signalWidth: GanttCellWidth.day,
    dayWidth: GanttCellWidth.day, // 日维度一天48，小时维度一天24*12
    wrapBodyWidth: 0,
    dimension: 'day',
    per1Px: 1,
  };
  needPadding = false;

  days: DayRange[] = []; // 用于显示有多少个天（表头）
  weeks: WeekRange[] = []; // 用于显示多少个周
  hours = [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24];

  @Output() daysChange = new EventEmitter<DayRange[]>();
  @Output() weeksChange = new EventEmitter<WeekRange[]>();
  @Output() optionsChange = new EventEmitter<any>();

  scrollTop = 0;
  scrollLeft = 0;

  @Output() selectedItem = new EventEmitter<number>();

  rate = 1;
  get tableRate(): number {
    return this.rate;
  }

  set tableRate(rate: number) {
    this.rate = rate;
  }

  constructor(private _cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.calcData();
    setTimeout(() => {
      this.toScrollDate(this.searchDate?.start_date ?? new Date());
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.data && changes.data.currentValue && !changes.data.firstChange) {
      this.profileTable();
    }
    if (changes.date && changes.date.currentValue && !changes.date.firstChange) {
      this.calcData();
    }
    if (changes?.graphData && changes?.graphData?.currentValue?.toStartDate && !changes?.graphData?.firstChange) {
      setTimeout(() => {
        this.toScrollDate(this.searchDate?.start_date ?? new Date());
      }, 10);
    }
  }

  /**
   * 根据搜索日期获取日期列表与星期列表
   */
  calcData() {
    const { start_date: startDate, end_date: endDate } = this.date;
    this.options.wrapBodyWidth = this.wrapBody.nativeElement.offsetWidth;
    this.days = this.getRangeDates(startDate, endDate);
    this.daysChange.emit(this.days);
    this.weeks = this.getRangeWeeks(this.days);
    this.weeksChange.emit(this.weeks);
    setTimeout(() => {
      this.calCellWidth();
      if (this.wrapBody.nativeElement.scrollWidth > this.options.wrapBodyWidth) {
        this.needPadding = true;
      }
    });
  }

  /**
   * 计算不同维度下单元格的宽度与一天的宽度
   * 总体最小宽度之和要能占满整屏
   */
  calCellWidth() {
    const rawWidth = this.wrapTop.nativeElement.offsetWidth;
    const calWidth = Math.ceil(rawWidth / this.days?.length) ?? 0;
    const calHourWidth = Math.ceil(rawWidth / (this.days?.length * 12)) ?? 0;
    // 计算宽度大于设置宽度，不允许缩小
    // this.allowNarrow = !(calWidth > GanttCellWidth.day || calHourWidth > GanttCellWidth.hour);
    switch (this.options.dimension) {
      case 'day':
        this.options.signalWidth = Math.max(calWidth, GanttCellWidth.day) * this.rate;
        this.options.dayWidth = this.options.signalWidth;
        this.options.per1Px = (24 * 60) / this.options.dayWidth;
        break;
      case 'hour':
        this.options.signalWidth = Math.max(calHourWidth, GanttCellWidth.hour) * this.rate;
        this.options.dayWidth = this.options.signalWidth * 12;
        this.options.per1Px = (24 * 60) / this.options.dayWidth;
        break;
      default:
        this.options.signalWidth = Math.max(calWidth, GanttCellWidth.day) * this.rate;
        this.options.dayWidth = this.options.signalWidth;
        this.options.per1Px = (24 * 60) / this.options.dayWidth;
        break;
    }
    this.optionsChange.emit(this.options);
  }

  profileTable() {
    this.calcData();
    this.data = this.data.map((d) => ({ ...d })); // 为了触发子组件的ngOnChanges
  }

  /**
   * 根据日期范围获取日期数据
   * @param start 开始日期
   * @param end 结束日期
   */
  getRangeDates(start: Date, end: Date) {
    const ranges = [];
    const dayFormat = 'yyyy-MM-dd HH:mm:ss';
    let flagDate = start;
    let range: DayRange;
    const day = differenceInDays(end, start) + 1; // 起始日期的天数
    for (let i = 0; i < day; i++) {
      const startTime: string = format(startOfDay(flagDate), dayFormat);
      const endTime: string = format(endOfDay(flagDate), dayFormat);
      range = {
        start: startTime,
        end: endTime,
        date: flagDate,
        day: getDate(flagDate),
        isWeekday: isWeekend(flagDate),
        isRestDay: false,
        month: getMonth(flagDate) + 1,
        week: getWeek(flagDate),
        year: getYear(flagDate),
        isToday: isToday(flagDate),
      };
      if (this.rest_list?.indexOf(range.start?.split(' ')[0]) >= 0) {
        range.isRestDay = true;
      }
      ranges.push(range);
      flagDate = addDays(flagDate, 1);
    }
    return ranges;
  }

  /**
   * 将日期列表按周分组
   * @param days
   * @returns
   */
  getRangeWeeks(days: DayRange[]) {
    const weeks: any[] = [];
    days.forEach((item) => {
      const index = weeks.length - 1;
      if (weeks[index]?.week === item.week) {
        weeks[index].children.push(item);
      } else {
        weeks.push({
          week: item.week,
          year: item.year,
          children: [item],
        });
      }
    });
    return weeks;
  }

  /**
   * 拼接日维度下的周显示内容
   * @param week
   * @returns
   */
  getWeekData(week: WeekRange) {
    const startWeek = format(week?.children[0]?.date, 'MM/dd');
    const endWeek = format(week?.children[week?.children?.length - 1]?.date, 'MM/dd');
    const weekText = `第${week.week}周`;
    return ' (' + startWeek + ' - ' + endWeek + ') ' + weekText;
  }

  /**
   * 切换维度
   * @param dimension
   */
  transformDimension(dimension: DimensionType, day: any) {
    this.options.dimension = dimension;
    this.calCellWidth();
    setTimeout(() => {
      const dayIndex = this.days.findIndex((item) => isEqual(new Date(item.date), new Date(day.date)));
      this.wrapTopScroll.nativeElement.scrollLeft = this.options.dayWidth * dayIndex;
      this.wrapBody.nativeElement.scrollLeft = this.options.dayWidth * dayIndex;
    });
  }

  /**
   * 判断“返回今天”是否可点击
   * 当时间返回不包括今天，不可点击
   * @returns
   */
  showTodayBtn() {
    return !this.days.find((item) => item.isToday);
  }

  /**
   * 页面滚动至今天
   */
  toToday() {
    const todayIndex = this.days.findIndex((item) => item.isToday);
    this.scrollLeft = this.options.dayWidth * todayIndex;
    this.wrapTopScroll.nativeElement.scrollLeft = this.scrollLeft;
    this.wrapBody.nativeElement.scrollLeft = this.scrollLeft;
  }

  /**
   * 滚动到指定日期到位置
   * @param date
   */
  toScrollDate(date: string | Date) {
    const formatDate = format(new Date(date), 'yyyy-MM-dd');
    const index = this.days.findIndex((item) => format(new Date(item.date), 'yyyy-MM-dd') === formatDate);
    this.wrapTopScroll.nativeElement.scrollLeft = this.options.dayWidth * index;
    this.wrapBody.nativeElement.scrollLeft = this.options.dayWidth * index;
    this.scrollLeft = this.options.dayWidth * index;
  }

  /**
   * 日期向前
   */
  forward() {
    this.forwardDate.emit();
  }

  /**
   * 日期向后
   */
  backwards() {
    this.backwardsDate.emit();
  }

  /*
   * 高亮后显示蒙层，仅高亮一行数据
   */
  showMask(e: { event: MouseEvent; element: HTMLElement }, index: number) {
    this.data.forEach((v, i) => {
      v.selected = i === index;
    });
    this.selectedItem.emit(index);
    this.maskVisible = true;
  }

  closeMask() {
    // this.data.forEach((v) => {
    //   v.selected = false;
    // });
    // this.unSelectedAll.emit(false);
    this.maskVisible = false;
  }

  onTopScroll() {
    this.scrollLeft = this.wrapTopScroll.nativeElement.scrollLeft;
    this._cdr.detectChanges();
  }

  onLeftScroll() {
    this.scrollTop = this.wrapLeft.nativeElement.scrollTop;
    this._cdr.detectChanges();
  }

  onBodyScroll() {
    this.scrollTop = this.wrapBody.nativeElement.scrollTop;
    this.scrollLeft = this.wrapBody.nativeElement.scrollLeft;
    this._cdr.detectChanges();
  }
}
