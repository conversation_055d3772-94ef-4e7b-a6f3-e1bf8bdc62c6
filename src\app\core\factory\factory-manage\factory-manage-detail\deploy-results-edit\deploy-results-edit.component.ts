import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { FactoryManageDetailService } from '../factory-manage-detail.service';

@Component({
  selector: 'app-deploy-results-edit',
  templateUrl: './deploy-results-edit.component.html',
  styleUrls: ['./deploy-results-edit.component.scss'],
})
export class DeployResultsEditComponent implements OnInit {
  placeInput!: string;

  constructor(private translateService: TranslateService, public _service: FactoryManageDetailService) {}

  ngOnInit(): void {
    this.placeInput = this.translateService.instant('placeholder.input');
  }

  checkUrl() {
    const data = this._service.factoryForm?.get('domain')?.value;
    if (data && this._service.checkUrl(data)) {
      return;
    } else {
      this._service.factoryForm?.get('domain')?.setErrors({
        pattern: {
          actualValue: data,
          requiredPattern: '^(https?)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]$',
        },
      });
    }
  }
}
