.materical-header {
  height: 36px;
  line-height: 36px;
  border-radius: 0px 0px 8px 8px;
  background: #f7f8fa;
  display: flex;
  padding: 0 12px;
  justify-content: flex-start;
  margin: 8px 0;
  span {
    color: #54607c;
    display: flex;
    .value {
      color: #262d48;
    }
  }
}
.materical-content {
  margin-top: -16px;
}
:host ::ng-deep .ant-divider-vertical {
  margin: 12px 16px;
}

.basic-header {
  height: 36px;
  line-height: 36px;
  border-radius: 0px 0px 8px 8px;
  background: #f1f8ff;
  display: flex;
  padding: 0 12px;
  justify-content: flex-start;
  align-items: center;
  margin-top: -16px;
  margin-bottom: 25px;
  span {
    color: #54607c;
    display: flex;
    .value {
      color: #262d48;
    }
  }
}
