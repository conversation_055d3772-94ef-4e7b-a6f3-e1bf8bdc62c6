import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FieldPermissionModel, RoleNameChildModel } from '../../role-manager.model';
import { FlcDrawerHelperService } from 'fl-common-lib';
import { PermissionSettingComponent } from '../permission-setting/permission-setting.component';
import { cloneDeep } from 'lodash';
import { NzDrawerService } from 'ng-zorro-antd/drawer';
import { NzModalService } from 'ng-zorro-antd/modal';

@Component({
  selector: 'app-role-line-last-menu-line',
  templateUrl: './role-line-last-menu-line.component.html',
  styleUrls: ['./role-line-last-menu-line.component.scss'],
})
export class RoleLineLastMenuLineComponent implements OnInit {
  @Input() child!: RoleNameChildModel;
  @Input() isEdit!: boolean;
  @Input() showALLDetail!: boolean;
  @Output() statusChange = new EventEmitter<boolean>();
  @Output() checkedChange = new EventEmitter<void>();
  renderBtn: RoleNameChildModel[] = [];
  renderField: FieldPermissionModel[] = [];
  invisibleBtn: RoleNameChildModel[] = [];
  isNotEmpty = false;
  fieldIsNotEmpty = false;
  isSelectedAll = false;
  fieldIsSelectAll = true;
  indeterminate = false;
  filedIndeterminate = false;
  showEditRoleRadio = false;
  priceAllChecked = false;
  priceIndeterminate = true;
  showPrice = false;
  priceConfig = [
    { label: '基础信息', value: '1', disabled: false, checked: true },
    { label: '价格信息', value: '2', disabled: false, checked: false },
  ];
  canPriceRange = false;
  nonPriceConfigKey: string[] = [
    'material:in-stock', // 物料入库单
    'material:out-stock', // 物料出库单
    'material:stock-on-hand', // 物料库存
    'material:in-out-detail-stock', // 出入库明细
  ];
  showReadFieldPermission = ['bulk:prod-status-report']; // 查看者下展示字段权限的模块
  translateName = 'roleMgt.';
  needTranslate = ['order:bulk', 'bulk:prod-status-report']; // 需要翻译的模块

  constructor(private _drawer: NzDrawerService, private modal: NzModalService) {}
  ngOnInit(): void {
    (this.child?.children ?? []).forEach((item) => {
      if (item.visible) {
        this.showEditRoleRadio = true;
        this.renderBtn.push(item);
      } else {
        this.invisibleBtn.push(item);
      }
    });

    // 初始筛选具有读权限的操作，并将值默认设为true
    this.invisibleBtn.forEach((item) => {
      if (item.action_type === 'read') {
        item.value = true;
      }
    });
    // 判断是否展示价格范围
    for (const key of this.nonPriceConfigKey) {
      if (this.child.code.endsWith(key)) {
        this.canPriceRange = true;
        break;
      }
    }
    // 回显价格字段
    if (this.child.payload?.visual_type === 3) {
      this.showPrice = true;
    } else {
      this.showPrice = false;
    }
    this.isNotEmpty = this.renderBtn.length > 0;
    this.singleChecked();
    this.writeEmptyCheck();

    // 字段权限
    (this.child?.fieldList ?? []).forEach((item) => {
      if (item?.editPermission === 2) {
        item['write_value'] = true;
      }
      if (item?.viewPermission === 2) {
        item['read_value'] = true;
      }
      this.renderField.push(item);
    });
    this.fieldSingleChecked();
    this.fieldIsNotEmpty = this.renderField?.length > 0;

    // 模块权限
    (this.child?.moduleList ?? []).forEach((item) => {
      item.value = item.permission !== 3;
      this.moduleList.push(item);
    });

    this.singleModuleChecked();
  }
  writeEmptyCheck() {
    if (this.isSelectedAll === true) {
      this.child.isEmptyWrite = false;
    } else {
      if (this.child.action_type === 'write' && this.renderBtn.length > 0) {
        const isEmpty = this.renderBtn.every((item) => item.value === false);
        this.child.isEmptyWrite = isEmpty;
      }
    }
  }
  statusChanged() {
    this.child.action_type = this.showEditRoleRadio ? 'write' : 'read';
    this.radioChanged(this.child.action_type);
  }
  updateAllChecked() {
    this.indeterminate = false;
    this.renderBtn.forEach((item) => {
      item.value = this.isSelectedAll;
    });
    this.writeEmptyCheck();
  }

  updateFieldAllChecked() {
    this.filedIndeterminate = false;
    const key = this.child.action_type === 'write' ? 'write_value' : 'read_value';
    this.renderField.forEach((field) => {
      field[key] = this.fieldIsSelectAll;
    });
    this.writeEmptyCheck();
    this.checkedChange.emit();
  }
  radioChanged(event: 'write' | 'read') {
    const status = event === 'write';
    this.invisibleBtn.forEach((item) => {
      if (item.action_type === 'write') {
        item.value = status;
      } else {
        item.value = true;
      }
    });
    if (status === false) {
      this.renderBtn.forEach((item) => {
        if (item.action_type === 'write') {
          this.indeterminate = false;
          item.value = status;
        } else {
          item.value = true;
        }
      });
    } else {
      this.isSelectedAll = true;
      this.isSelectedAllModule = true;
      this.updateAllChecked();
      this.updateAllModuleChecked();
    }
    this.singleChecked();
    this.fieldSingleChecked();
    this.singleModuleChecked();
  }
  checkboxChanged(event: boolean, btn: RoleNameChildModel) {
    btn.value = event;
    if (event) {
      if (btn.code.includes('create')) {
        const update = btn.code.replace('create', 'update');
        this.renderBtn.forEach((item) => {
          if (item.code === update) {
            item.value = event;
          }
        });
      }
      if (btn.code.includes('settings:role-updateStatus')) {
        // 写死 权限名称的启用/禁用需要和编辑联动
        const update = 'settings:role-update';
        this.renderBtn.forEach((item) => {
          if (item.code === update) {
            item.value = event;
          }
        });
      }
    } else {
      if (btn.code.includes('update')) {
        const create = btn.code.replace('update', 'create');
        this.renderBtn.forEach((item) => {
          if (item.code === create) {
            item.value = event;
          }
        });
      }
      if (btn.code.includes('settings:role-update')) {
        // 写死 权限名称的启用/禁用需要和编辑联动
        const updateStatus = 'settings:role-updateStatus';
        this.renderBtn.forEach((item) => {
          if (item.code === updateStatus) {
            item.value = event;
          }
        });
      }
    }
    this.singleChecked();
    this.writeEmptyCheck();
  }

  fieldCheckboxChanged(event: boolean, field: FieldPermissionModel) {
    const key = this.child.action_type === 'write' ? 'write_value' : 'read_value';
    field[key] = event;
    this.fieldSingleChecked();
    this.writeEmptyCheck();
    this.checkedChange.emit();
  }

  singleChecked(): void {
    if (this.renderBtn.every((item) => !item.value)) {
      this.isSelectedAll = false;
      this.indeterminate = false;
    } else if (this.renderBtn.every((item) => item.value)) {
      this.isSelectedAll = true;
      this.indeterminate = false;
    } else {
      this.isSelectedAll = false;
      this.indeterminate = true;
    }
  }

  fieldSingleChecked(): void {
    const key = this.child.action_type === 'write' ? 'write_value' : 'read_value';
    if (this.renderField.every((item) => !item?.[key])) {
      this.fieldIsSelectAll = false;
      this.filedIndeterminate = false;
    } else if (this.renderField.every((item) => item?.[key])) {
      this.fieldIsSelectAll = true;
      this.filedIndeterminate = false;
    } else {
      this.fieldIsSelectAll = false;
      this.filedIndeterminate = true;
    }
  }

  getAllChecked() {
    if (this.showEditRoleRadio) {
      const showPriceValid = !this.canPriceRange || this.showPrice;
      const moduleValid = !this.moduleList.length || this.isSelectedAllModule;
      if (this.renderField?.length) {
        return this.child.value && this.isSelectedAll && this.fieldIsSelectAll && showPriceValid && moduleValid;
      }
      return this.child.value && this.child.action_type === 'write' && this.isSelectedAll && showPriceValid && moduleValid;
    } else {
      return this.child.value && (!this.renderField.length || this.fieldIsSelectAll);
    }
  }

  onStatusChanged(event: boolean) {
    this.statusChange.emit(event);
    this.checkedChange.emit();
    this.statusChanged();
  }
  onUpdateAllChecked() {
    this.checkedChange.emit();
    this.updateAllChecked();
  }
  onRadioChanged(event: 'write' | 'read') {
    this.checkedChange.emit();
    this.radioChanged(event);
  }
  onCheckboxChanged(event: boolean, btn: RoleNameChildModel) {
    this.checkedChange.emit();
    this.checkboxChanged(event, btn);
  }
  /**
   * TIPS
   * 新增权限类型, 需要实现:
   * 1. Toggle方法 (toggle方法)
   * 2. checkedChange事件 (checkedChange.emit())
   * 3. checked check (getAllChecked)
   */
  toggle(value: boolean) {
    this.child.value = value;
    this.child.action_type = this.showEditRoleRadio ? 'write' : 'read';
    this.radioChanged(this.child.action_type);
    // 一键全选Toggle
    this.showPrice = value;
    this.child.payload.visual_type = value ? 3 : 1;
  }
  priceConfigChanged(val: boolean) {
    this.checkedChange.emit();
    if (val) {
      this.child.payload.visual_type = 3;
    } else {
      this.child.payload.visual_type = 1;
    }
  }

  isSelectedAllModule = false;
  indeterminateModule = false;

  moduleList: any[] = [];
  onUpdateAllModuleChecked() {
    this.checkedChange.emit();
    this.updateAllModuleChecked();
  }
  updateAllModuleChecked() {
    this.indeterminateModule = false;
    this.moduleList.forEach((item) => {
      item.value = this.isSelectedAllModule;
      item.permission = this.isSelectedAllModule ? 1 : 3;
    });
  }
  onModuleCheckboxChanged(event: boolean, btn: any) {
    this.checkedChange.emit();
    btn.permission = event ? 1 : 3;
    btn.value = btn.permission !== 3;
    this.singleModuleChecked();
  }

  singleModuleChecked(): void {
    if (this.moduleList.every((item) => !item.value)) {
      this.isSelectedAllModule = false;
      this.indeterminateModule = false;
    } else if (this.moduleList.every((item) => item.value)) {
      this.isSelectedAllModule = true;
      this.indeterminateModule = false;
    } else {
      this.isSelectedAllModule = false;
      this.indeterminateModule = true;
    }
  }

  onToggleModule(btn: any) {
    if (!this.isEdit) return false;
    btn.permission = btn.permission === 1 ? 2 : 1;
    this.updateChildren(btn);
    return true;
  }

  updateChildren(btn: any) {
    if (btn.permission === 1) {
      // 已选中字段, 都切换为编辑权限
      btn.module_field_list.forEach((item: any) => {
        if (item.permission !== 3) {
          item.permission = 1;
        }
      });

      // 操作权限都切换为选中
      btn.module_action_list.forEach((item: any) => {
        item.permission = 1;
        item.value = true;
      });
    } else {
      // 已选中字段, 都切换为只读权限
      btn.module_field_list.forEach((item: any) => {
        if (item.permission !== 3) {
          item.permission = 2;
        }
      });

      // 操作权限都切换为不选中
      btn.module_action_list.forEach((item: any) => {
        item.permission = 3;
        item.value = false;
      });
    }
  }

  _packageName() {
    const maps = {
      打样需求: '打样',
      标准资料包: '标准',
      大货资料包: '大货',
      OTHER: '资料包',
    };
    return maps[this.child.name as keyof typeof maps] || maps.OTHER;
  }

  openPermissionSetting() {
    const ref = this._drawer.create({
      nzTitle: `${this._packageName()}资料包-权限配置`,
      nzHeight: 'calc(100% - 48px)',
      nzPlacement: 'bottom',
      nzContent: PermissionSettingComponent,
      nzContentParams: {
        moduleList: cloneDeep(this.moduleList),
      },
      nzMaskClosable: false,
      nzOnCancel: () => {
        return new Promise((resolve) => {
          const hasChanged = ref.getContentComponent()?.hasChanged();
          if (!hasChanged) {
            resolve(true);
          } else {
            this.modal.confirm({
              nzTitle: '当前编辑未保存,确定离开吗？',
              nzOnOk: () => resolve(true),
            });
          }
        });
      },
    });

    ref.afterClose.subscribe((params: any[] | undefined) => {
      if (params) {
        const moduleIdMaps = params.reduce((maps, item) => {
          maps[item.id] = item;
          return maps;
        }, {});
        this.child.moduleList = this.child.moduleList?.map((item) => {
          return moduleIdMaps[item.id];
        });
        this.moduleList = this.child.moduleList as any[];
      }
    });
  }
}
