export interface SecProcessOutsourcingListInterface {
  id: number; // io id
  io_code: string; // io code
  category: string; // 品名
  first_material_name: string; // 一级分类名称
  first_material_code: string; // 一级分类code
  first_material_id: number; // 一级分类 id
  second_material_name: string; // 二级分类名称
  second_material_code: string; // 二级分类code
  second_material_id: number; // 二级分类id
  third_material_id: number; //
  third_material_name: string; // 三级分类名称
  third_material_code: string; // 三级分类code
  material_name: string;
  order_pictures: Array<{
    // 订单图片
    name: string;
    url: string;
  }>;
  contract_number: string; // 销售单号
  order_date: string; // 下单日期，13位时间戳
  order_status: number; // 订单状态
  order_status_value: string; // 订单状态值
  gen_time: string; // 创建时间
  gen_user: string; // 创建人
  qty: number; // 订单货物总数
  distribution_factory_name: Array<string>; // 加工厂名称
  extra_process_name: Array<string>; // 二次工艺名称
  po_due_times: Array<string>; // po 交期，13位时间戳
  customer_style: string; // 客户款号
  customer_io_code: string; // 客户生产单
  customer: string; // 客户名称
  extra_process_info: Array<{
    extra_process_id: number; // 二次工艺id
    extra_process_name: string; // 二次工艺name
    position_list: Array<{
      position_id: number; // 部位id
      position_name: string; // 部位名称
    }>;
    deletable: boolean; // 是否可删除
  }>;
}
/* 详情信息 */
export interface SecProcessDetailInfoInterface {
  commit: true; // 是否提交
  production_type: number; // 外发类型，1 成衣加工 2 二次工艺加工
  task_type: string; // 任务类型  来料加工 || 包工包料
  extra_process_info: Array<ExtraProcessInfoInterface>;
  info: Array<ExtraProcessAssignInfoInterface>;
  io_id: number; // io id
  status: number; // 分配单状态
  status_value: string; // 分配单状态值
  reason: string; // 退回修改原因
  id?: number;
}
/* 二次工艺信息 */
export interface ExtraProcessInfoInterface {
  extra_process_id: number; // 二次工艺id
  extra_process_name: string; // 二次工艺名称
  position_list: Array<{
    position_id: number; // 部位id
    position_name: string; // 部位名称
  }>;
  deletable: boolean; // 是否可删除
}
/* po分配信息 */
export interface ExtraProcessAssignInfoInterface {
  id: number; // 分配单id，
  factory_id: number;
  factory_code: string; // 分配的工厂code
  factory_name: string; // 分配的工厂名成
  factory_short_name: string; // 分配工厂的简称，没有可不填
  lines: Array<{
    id: number; // 分配单id，创建时为0
    po_id: number; // 分配的颜色尺码po id
    po_line_id: number; // 分配的颜色尺码 po line id
  }>;
  pos: Array<any>;
  deletable: boolean; // 分配单是否可删除，
  extra_process_name: string; // 二次工艺名称，成衣外发填写默认值即可
  extra_process_id: number; // 二次工艺id，成衣外发填写默认值即可
}
