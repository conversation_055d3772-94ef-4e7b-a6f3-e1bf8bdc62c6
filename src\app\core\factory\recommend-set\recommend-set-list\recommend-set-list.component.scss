.recommend-set-list {
  background-color: #fff;
  border-radius: 4px;
  padding: 0 12px 12px 12px;
  height: calc(100vh - 65px);
}

.header {
  height: 44px;

  display: flex;
  align-items: center;
  justify-content: space-between;

  .title {
    position: relative;
    padding-left: 12px;

    font-weight: 500;
    font-size: 16px;
    color: #222b3c;
    line-height: 22px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 16px;
      background: #138aff;
      border-radius: 2px;
    }
  }

  .ant-btn {
    margin-right: 8px;

    & > span {
      margin: 0 1px 0 1px;
    }
  }
}

::ng-deep {
  .recommend_element_popover {
    .ant-popover-inner-content {
      padding: 0;
      min-width: 200px;
      max-width: 300px;
    }
  }
}
