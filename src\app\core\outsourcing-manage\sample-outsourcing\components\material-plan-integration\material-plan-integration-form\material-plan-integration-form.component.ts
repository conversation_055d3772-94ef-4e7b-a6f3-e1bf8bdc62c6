import { Component, Input, OnInit } from '@angular/core';
import { FlcDrawerHelperService } from 'fl-common-lib';

import { MaterialPlanDetailComponent } from '../material-plan-detail/material-plan-detail.component';
import { FlssMaterialDetailComponent } from 'fl-sewsmart-lib/material-selector';
import { IMaterialLine, IMaterialRel } from '../../../modal/sample-outsourcing.interface';
import { SampleOutsourcingService } from '../../../sample-outsourcing.service';

enum FabricTypeEnum {
  fabric = 1,
  accessory = 2,
}

@Component({
  selector: 'flss-material-plan-integration-form',
  templateUrl: './material-plan-integration-form.component.html',
  styleUrls: ['./material-plan-integration-form.component.scss'],
})
export class MaterialPlanIntegrationFormComponent implements OnInit {
  @Input() data: IMaterialRel[] = []; // 材料企划数据
  @Input() canLinkDetail = true; // 是否可以点击看详情

  translateLabel = 'materialPlanIntegration.label.';
  translateAction = 'materialPlanIntegration.action.';

  constructor(private _service: SampleOutsourcingService, private _flcDrawer: FlcDrawerHelperService) {}

  ngOnInit(): void {}

  ngOnDestroy() {}

  onChangeToggleRead(plan: IMaterialRel) {
    plan.is_open = !plan.is_open;
  }

  // 查看关联材料企划明细
  openMaterialPlanDetail(id: string | number) {
    this._flcDrawer.openDrawer({
      title: this._service.translateValue(this.translateLabel + '材料企划明细'),
      content: MaterialPlanDetailComponent,
      contentParams: {
        id,
      },
    });
  }

  // 查看关键物料明细
  openMaterialDetail(material: IMaterialLine) {
    this._flcDrawer.openDrawer({
      title: this._service.translateValue(
        this.translateLabel + (material?.raw_material_type === FabricTypeEnum.fabric ? '面料详情' : '辅料详情')
      ),
      content: FlssMaterialDetailComponent,
      contentParams: { id: material?.raw_material_id, type: material?.raw_material_type },
    });
  }
}
