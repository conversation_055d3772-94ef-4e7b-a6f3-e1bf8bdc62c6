.disabled {
  background-color: #d3dde6 !important;
}

.target-item {
  border: 2px solid #0eba77 !important;
}

.graph-item {
  position: absolute;
  height: 20px;
  border-radius: 2px;
  border: 1px solid #a2abbe;
  // min-width: 16px;
  // min-width: 7px;
  overflow: hidden;

  // 实际进度条
  &.isActual {
    height: 16px;
    border-radius: 2px;

    &:hover {
      // background: #ffffff !important;
      // box-shadow: inset 1px 1px 3px 0px #a5adb4;
      border-radius: 2px;
      border: 1px solid #ff8014;
      font-size: 10px;
      font-weight: 500;
      color: #272e45;
    }
  }

  // 悬停计划进度条时对应高亮实际进度条
  &.isActualHover {
    background: #ffffff !important;
    box-shadow: inset 1px 1px 3px 0px #a5adb4;
    border-radius: 2px;
    border: 1px solid #ff8014;
    font-size: 10px;
    font-weight: 500;
    color: #272e45;
  }

  // 悬停实际进度条时对应高亮计划进度条
  &.isSewingHover {
    border: 1px solid #ff8014 !important;
  }

  &.isSelected {
    border-width: 1px;
    background-color: #2996ff;
    border-color: #2996ff;
    .operator-title {
      color: #fff;
    }
    .poMode {
      .poBar {
        border-width: 1px;
        border-top-color: #2996ff;
        border-bottom-color: #2996ff;
        border-left-color: #fff;
        border-right-color: #fff;
        &:not(:last-child) {
          border-left-color: #2996ff;
        }
        &:not(:first-child) {
          border-right-color: #2996ff;
        }
      }
    }
  }

  &.isHeightLight {
    border-color: #007dff;
    border-width: 2px;
    .poMode {
      .poBar {
        border-width: 2px;
        border-color: #007dff;
      }
    }
  }

  &.isDraging {
    z-index: 600 !important ;
  }

  &.poBorderNone {
    border-color: transparent;
    border-width: 0;
    &:hover {
      border-color: transparent;
    }
  }

  &.hoverClass:hover {
    border-color: #f77615;
  }

  .operator-wrap {
    // position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;

    // & > div {
    //   top: 0px;
    //   position: absolute;
    //   display: none;
    //   width: 7px;
    //   height: 20px;
    //   border-radius: 2px 0px 0px 2px;
    //   cursor: ew-resize;
    // }
  }

  .operator-title {
    flex-grow: 1;
    padding: 0 6px;
    font-size: 10px;
    font-weight: 500;
    color: #272e45;
    line-height: 20px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    overflow: hidden;
    white-space: nowrap;

    &.isActual {
      height: 16px;
      background: #cdd8e3;
      box-shadow: inset 1px 1px 3px 0px #a5adb4;
      border-radius: 2px;
      font-size: 10px;
      font-weight: 500;
      color: #5d6681;

      &:hover {
        background: #ffffff !important;
        // box-shadow: inset 1px 1px 3px 0px #a5adb4;
        border-radius: 2px;
        // border: 1px solid #ff8014;
        font-size: 10px;
        font-weight: 500;
        color: #272e45;
      }
    }

    // 悬停计划进度条时对应高亮实际进度条
    &.isActualHover {
      background: #ffffff !important;
      box-shadow: inset 1px 1px 3px 0px #a5adb4;
      border-radius: 2px;
      // border: 1px solid #ff8014;
      font-size: 10px;
      font-weight: 500;
      color: #272e45;
    }
  }

  .graph-item-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
    overflow: hidden;
    flex: 100%;

    .union-line-wrap {
      background: rgba(255, 255, 255, 0.6);
      border-radius: 2px;
      font-size: 10px;
      font-weight: 500;
      color: #54607c;
      overflow: hidden;
      margin: 3px;
      padding: 1px 3px;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .time-tip-left,
  .time-tip-right {
    display: none;
  }

  .drag-icon {
    font-size: 18px;
    width: 7px;
    height: 18px;
    position: absolute;
    top: 0px;
    z-index: 100;
  }

  .drag-icon-left {
    left: -1px;
  }

  .drag-icon-right {
    right: -1px;
  }

  .drag-icon-left:hover > .time-tip-left,
  .drag-icon-right:hover > .time-tip-right {
    display: block;
  }

  &:hover {
    z-index: 5;
    min-width: 16px;

    .operator-wrap > .drag-handler {
      // display: block;
      display: flex;
      align-items: center;
    }
  }
}
.drag-handler {
  display: none;
  cursor: ew-resize;
}

.alter-status-tag {
  display: flex;
  align-items: center;
  .sample-circle {
    z-index: 100;
  }
  .underlying-icon {
    position: relative;
    z-index: 50;
    height: 10px;
    margin-right: 5px;
    svg {
      position: absolute;
      left: -5px;
    }
    &:hover {
      z-index: 200 !important;
    }
  }
}

.time-tip {
  position: absolute;
  height: 20px;
  line-height: 20px;
  background: rgba(0, 0, 0, 0.63);
  border-radius: 8px;
  color: white;
  padding: 0 4px;
  word-break: keep-all;
  width: 100px;
  text-align: center;
}

.time-tip-left {
  left: calc(-92px + min(50%, 46px));
  //transform: translateX(-40%);
  top: -20px;
}

.time-tip-right {
  right: calc(-92px + min(50%, 46px));
  //transform: translateX(40%);
  top: -20px;
}

.poMode {
  padding: 0 !important;
  .poBar {
    white-space: nowrap;
    padding-left: 4px;
    display: flex;
    align-items: center;
    overflow: hidden;
    text-overflow: ellipsis;
    border-top: 1px #a2abbe solid;
    border-bottom: 1px #a2abbe solid;
    border-left: 0.5px #797e89 dashed;
    border-right: 0.5px #797e89 dashed;
    &:first-child {
      border-top-left-radius: 2px;
      border-bottom-left-radius: 2px;
      border-left-width: 1px;
      border-left-style: solid;
    }
    &:last-child {
      border-top-right-radius: 2px;
      border-bottom-right-radius: 2px;
      border-right-width: 1px;
      border-right-style: solid;
    }
    &:hover {
      border-color: #f77615 !important;
    }

    // 悬停实际进度条时对应高亮计划进度条
    &.isSewingHover {
      border-color: #ff8014 !important;
    }
  }
}

::ng-deep .graph-item-tooltip.ant-tooltip {
  max-width: 800px;
}

.tooltip-item {
  word-break: break-all;
  > div {
    display: inline;
    margin-right: 10px;
  }
  .cancel-tag {
    font-size: 10px;
    background: #eaebed;
    color: #616161;
    border-radius: 4px;
    padding: 0 4px;
    margin: 2px;
    word-break: break-all;
    white-space: nowrap;
  }
}
