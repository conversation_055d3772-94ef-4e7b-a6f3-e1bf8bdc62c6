import { FlcTableHeaderConfig } from 'fl-common-lib';
import { SecProcessOutsourcingListInterface } from '../model/sec-process-outsourcing.interface';
import { OrderStatus } from '../model/sec-process-outsourcing.enum';
type lineItem = FlcTableHeaderConfig<SecProcessOutsourcingListInterface> & { sortOrderBy?: any };

export function setStyle(item: any) {
  const styleObj: any = {};
  switch (item.order_status) {
    case OrderStatus.toSubmit: // 待提交
      styleObj.color = '#138AFF';
      break;
    case OrderStatus.toAudit: // 待审核
      styleObj.color = '#FB6401';
      break;
    case OrderStatus.toModifyAudit: // 修改待审核
      styleObj.color = '#FB6401';
      break;
    case OrderStatus.modifyAuditReturn: // 修改审核未通过
      styleObj.color = '#FB6401';
      break;
    case OrderStatus.toModify: // 待修改
      styleObj.color = '#FF4A1D';
      break;
    case OrderStatus.cancelled: // 已取消
      styleObj.color = '#97999C';
      break;
    default:
      styleObj.color = '#515661';
      break;
  }
  return styleObj;
}

// 款式分类
function categoryFormatter(item: SecProcessOutsourcingListInterface) {
  let text = '';
  if (item.first_material_name) {
    text += item.first_material_name;
  }
  if (item.second_material_name) {
    text += '-' + item.second_material_name;
  }
  if (item.third_material_name) {
    text += '-' + item.third_material_name;
  }
  return text;
}

export function initialSecProcessOutSourcingListHeader() {
  const commonTableHeaderConfig = {
    visible: true,
    pinned: false,
    disable: false,
    resizeble: true,
    sort: false,
  };
  const defaultHeaders: lineItem[] = [
    {
      label: '大货单号',
      key: 'io_code',
      width: '120px',
      type: 'text',
      ...commonTableHeaderConfig,
      pinned: true,
      isHidePin: true,
    },
    {
      label: '客户款号',
      key: 'customer_style',
      width: '136px',
      type: 'text',
      ...commonTableHeaderConfig,
    },
    {
      label: '款式分类',
      key: 'material_name',
      formatter: categoryFormatter,
      width: '136px',
      type: 'text',
      ...commonTableHeaderConfig,
    },
    {
      label: '款式图片',
      key: 'order_pictures',
      width: '80px',
      type: 'image',
      ...commonTableHeaderConfig,
    },
    {
      label: '客户名称',
      key: 'customer',
      width: '96px',
      type: 'text',
      ...commonTableHeaderConfig,
    },
    {
      label: '总件数',
      key: 'qty',
      width: '112px',
      type: 'quantity',
      ...commonTableHeaderConfig,
    },
    {
      label: '交付日期',
      key: 'po_due_times',
      width: '144px',
      type: 'template',
      templateName: 'po_due_times',
      ...commonTableHeaderConfig,
      sort: true,
    },
    {
      label: '二次工艺',
      key: 'extra_process_info',
      width: '144px',
      type: 'template',
      templateName: 'extra_process_info',
      ...commonTableHeaderConfig,
    },
    {
      label: '外发厂',
      key: 'distribution_factory_name',
      width: '160px',
      type: 'template',
      ...commonTableHeaderConfig,
      templateName: 'distribution_factory_name',
    },
    {
      label: '状态',
      key: 'order_status_value',
      style: setStyle,
      width: '96px',
      minWidth: 88,
      type: 'text',
      ...commonTableHeaderConfig,
    },
    {
      label: '创建时间',
      key: 'gen_time',
      width: '104px',
      type: 'datetime',
      minWidth: 88,
      ...commonTableHeaderConfig,
      sort: true,
      sortOrderBy: null,
    },
    {
      label: '创建人',
      key: 'gen_user',
      width: '96px',
      type: 'text',
      ...commonTableHeaderConfig,
    },
  ];
  return defaultHeaders;
}
/*
初始化搜索
*/
export function initSearchList() {
  const searchList = [
    {
      label: '大货单号',
      labelKey: 'io_code',
      valueKey: 'io_code',
      type: 'select',
    },
    {
      label: '客户款号',
      labelKey: 'customer_style',
      valueKey: 'customer_style',
      type: 'select',
    },
    {
      label: '外发厂',
      labelKey: 'factory_code',
      valueKey: 'factory_code',
      payload: { production_type: 2 },
      type: 'select',
    },
    {
      label: '状态',
      labelKey: 'status',
      valueKey: 'status',
      type: 'select',
      canSearch: false,
      alwaysReload: true,
    },
    {
      label: '客户名称',
      labelKey: 'customer',
      valueKey: 'customer',
      type: 'input',
    },
    {
      label: '创建人',
      labelKey: 'gen_user',
      valueKey: 'gen_user',
      type: 'select',
    },
    {
      label: '创建时间',
      labelKey: 'gen_time',
      valueKey: 'gen_time',
      type: 'date',
    },
  ];
  return searchList;
}
