.wrap {
  width: 100%;
  height: 100%;
  margin: 0;
}

::ng-deep .more-text {
  font-size: 14px;
  font-weight: 500;
  color: #4a5160;

  .color-setting {
    cursor: pointer;

    &:hover {
      color: #2996ff !important;
    }
  }

  .ant-checkbox-wrapper {
    color: #4a5160;
  }
}

.ganttTable {
  height: calc(100% - 42px);
  flex-grow: 1;
  overflow: hidden;
  position: relative;
}

::ng-deep .auto-connection {
  .ant-modal-confirm-body .ant-modal-confirm-content {
    margin-top: 0;
  }
}

.example-content {
  .example-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 40px;
    font-size: 12px;
    font-weight: 500;
    color: #4a5160;

    .example-switch {
      margin: 0;
      padding: 0;
      transform: scale(0.8);
    }
  }
}

:host::ng-deep {
  [row-no-start] {
    display: flex;
    flex-flow: row nowrap;
    justify-content: flex-start;
  }
  [row-no-center] {
    display: flex;
    flex-flow: row nowrap;
    justify-content: center;
  }
}
