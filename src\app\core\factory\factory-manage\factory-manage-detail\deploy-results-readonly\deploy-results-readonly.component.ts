import { Component, Input, OnInit } from '@angular/core';
import { FactoryManageDetailService } from '../factory-manage-detail.service';

@Component({
  selector: 'app-deploy-results-readonly',
  templateUrl: './deploy-results-readonly.component.html',
  styleUrls: ['./deploy-results-readonly.component.scss'],
})
export class DeployResultsReadonlyComponent implements OnInit {
  @Input() id!: number;

  constructor(public _service: FactoryManageDetailService) {}

  ngOnInit(): void {}

  // 生成初始化链接
  getInitLink() {
    this._service.initialFactory(+this.id).subscribe((res) => {
      if (res.code === 200) {
        const data = res.data;
        this._service.factoryForm?.get('initial_link')?.setValue(data);
      }
    });
  }
}
