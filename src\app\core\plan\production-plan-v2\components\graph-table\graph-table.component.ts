import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
  OnChanges,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
} from '@angular/core';
import {
  differenceInDays,
  format,
  startOfDay,
  endOfDay,
  isToday,
  addDays,
  isWeekend,
  getMonth,
  getWeek,
  getYear,
  getDate,
  isEqual,
} from 'date-fns';
import { NavigationEnd, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { ProductionPlanShareService } from '../../production-plan-share.service';
import { DayRange, DimensionType, GanttCellWidth, WeekRange } from '../../interface';
import { ProductionPlanSubjectEventEnum } from '../../model/production-plan.enum';

@Component({
  selector: 'app-graph-table',
  templateUrl: './graph-table.component.html',
  styleUrls: ['./graph-table.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GraphTableComponent implements OnInit, OnChanges {
  @Input() isEdit = false;
  @Input() filterStage: any[] = [];
  @Input() graphOptions: { view: 'order' | 'productionLine'; item_dimension: 'io' | 'po' } = {
    view: 'order',
    item_dimension: 'io',
  };
  @Input() data: any[] = [];
  @Input() rest_list: any = []; // 休息日
  @Input() date: any = {
    start_date: null,
    end_date: null,
  };
  @Output() actionSelectId = new EventEmitter();
  @Output() planException = new EventEmitter();
  @Output() selectedItem = new EventEmitter<number>();
  @Output() selectedAll = new EventEmitter<boolean>();
  @Output() unSelectedAll = new EventEmitter<boolean>();
  @Output() hasMoveItem = new EventEmitter<any>();
  @Output() actionRefresh = new EventEmitter(); // 重新获取list（非缓存）
  @Output() actionRefreshTemp = new EventEmitter(); // 重新获取list（缓存）
  @Output() forwardDate = new EventEmitter(); // 向前90天
  @Output() backwardsDate = new EventEmitter(); // 向后90天
  @ViewChild('planBack', { static: true }) planBack!: ElementRef<HTMLElement>;
  @ViewChild('overlay', { static: true }) overlay!: ElementRef<HTMLElement>;
  @ViewChild('wrapLeft', { static: true }) wrapLeft!: ElementRef<HTMLElement>;
  @ViewChild('wrapBody', { static: true }) wrapBody!: ElementRef<HTMLElement>;
  @ViewChild('wrapTop', { static: true }) wrapTop!: ElementRef<HTMLElement>;
  @ViewChild('wrapTopScroll', { static: true }) wrapTopScroll!: ElementRef<HTMLElement>;

  @Output() daysChange = new EventEmitter<any>();
  @Output() weeksChange = new EventEmitter<any>();
  @Output() optionsChange = new EventEmitter<any>();

  days: DayRange[] = []; // 用于显示有多少个天（表头）
  weeks: WeekRange[] = []; // 用于显示多少个周
  hours = [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24];
  @Output() allCheckedChange = new EventEmitter<boolean>();
  @Output() indeterminateChange = new EventEmitter<boolean>();
  @Input() checkboxConfig = {
    indeterminate: false,
    allChecked: false,
  };
  @Input() disableCheckbox = false;

  maskVisible = false;

  options = {
    signalWidth: GanttCellWidth.day,
    dayWidth: GanttCellWidth.day, // 日维度一天48，小时维度一天24*12
    wrapBodyWidth: 0,
    dimension: 'day',
    per1Px: 1,
  };

  scrollTop = 0;
  scrollLeft = 0;

  get paintOptions() {
    return this.options;
  }

  private _routerSubscription?: Subscription;

  constructor(public _productionPlanShareService: ProductionPlanShareService, private _router: Router, private _cdr: ChangeDetectorRef) {}

  /**
   * 根据搜索日期获取日期列表与星期列表
   */
  calcData() {
    const { start_date: startDate, end_date: endDate } = this.date;
    this.options.wrapBodyWidth = this.wrapBody.nativeElement.offsetWidth;
    this.days = this.getRangeDates(startDate!, endDate!);
    this.daysChange.emit(this.days);
    this.weeks = this.getRangeWeeks(this.days);
    this.weeksChange.emit(this.weeks);
    setTimeout(() => {
      this.calCellWidth();
    });
  }

  /**
   * 计算不同维度下单元格的宽度与一天的宽度
   * 总体最小宽度之和要能占满整屏
   */
  calCellWidth() {
    const rawWidth = this.wrapTop.nativeElement.offsetWidth;
    const calWidth = Math.ceil(rawWidth / this.days?.length);
    const calHourWidth = Math.ceil(rawWidth / (this.days?.length * 12));
    // 计算宽度大于设置宽度，不允许缩小
    this._productionPlanShareService.allowNarrow = !(calWidth > GanttCellWidth.day || calHourWidth > GanttCellWidth.hour);
    switch (this.options.dimension) {
      case 'day':
        // eslint-disable-next-line no-case-declarations
        const width = Math.max(calWidth, GanttCellWidth.day);
        this.options.signalWidth = width * this._productionPlanShareService.rate;
        this.options.dayWidth = this.options.signalWidth;
        this.options.per1Px = (24 * 60) / this.options.dayWidth;
        break;
      case 'hour':
        // eslint-disable-next-line no-case-declarations
        const hourWidth = Math.max(calHourWidth, GanttCellWidth.hour);
        this.options.signalWidth = hourWidth * this._productionPlanShareService.rate;
        this.options.dayWidth = this.options.signalWidth * 12;
        this.options.per1Px = (24 * 60) / this.options.dayWidth;
        break;
      default:
        // eslint-disable-next-line no-case-declarations
        const dwidth = Math.max(calWidth, GanttCellWidth.day);
        this.options.signalWidth = dwidth * this._productionPlanShareService.rate;
        this.options.dayWidth = this.options.signalWidth;
        this.options.per1Px = (24 * 60) / this.options.dayWidth;
        break;
    }
    this.optionsChange.emit(this.options);
  }

  ngOnInit(): void {
    this.calcData();
    setTimeout(() => {
      const showStart = new Date();
      this.toScrollDate(showStart);
    });
    this._productionPlanShareService.addSubjectListener('graph-table', [ProductionPlanSubjectEventEnum.scrollLeft], (res) => {
      if (res.type === ProductionPlanSubjectEventEnum.scrollLeft) {
        this.toScrollDate(res.data);
      }
    });
    this._routerSubscription = this._router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        if (event.url !== '/intellect-plan/production-plan') return;
        setTimeout(() => {
          this.wrapTopScroll.nativeElement.scrollLeft = this.scrollLeft;
          this.wrapBody.nativeElement.scrollLeft = this.scrollLeft;
        });
      }
    });
  }

  ngOnDestroy(): void {
    this._productionPlanShareService.removeSubjectListener('graph-table');
    this._routerSubscription?.unsubscribe();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.data && changes.data.currentValue && !changes.data.firstChange) {
      this.profileTable();
    }
    if (changes.date && changes.date.currentValue && !changes.date.firstChange) {
      this.calcData();
    }
  }

  profileTable() {
    this.calcData();
    this.data = this.data.map((d) => ({ ...d })); // 为了触发子组件的ngOnChanges
    // this.toggleCheckedItem();
  }

  onTopScroll(e: any) {
    this.scrollLeft = this.wrapTopScroll.nativeElement.scrollLeft;
  }

  onLeftScroll(e: any) {
    this.scrollTop = this.wrapLeft.nativeElement.scrollTop;
  }

  onBodyScroll(e: any) {
    this.scrollTop = this.wrapBody.nativeElement.scrollTop;
    this.scrollLeft = this.wrapBody.nativeElement.scrollLeft;
  }

  /**
   * 根据日期范围获取日期数据
   * @param start 开始日期
   * @param end 结束日期
   */
  getRangeDates(start: Date, end: Date) {
    const ranges: any[] = [];
    const dayFormat = 'yyyy-MM-dd HH:mm:ss';
    let flagDate = start;
    let range: DayRange;
    const day = differenceInDays(end, start) + 1; // 起始日期的天数
    for (let i = 0; i < day; i++) {
      const startTime = format(startOfDay(flagDate), dayFormat);
      const endTime = format(endOfDay(flagDate), dayFormat);
      range = {
        start: startTime,
        end: endTime,
        date: flagDate,
        day: getDate(flagDate),
        isWeekday: isWeekend(flagDate),
        isRestDay: false,
        month: getMonth(flagDate) + 1,
        week: getWeek(flagDate),
        year: getYear(flagDate),
        isToday: isToday(flagDate),
      };
      if (this.rest_list.indexOf(range.start.split(' ')[0]) >= 0) {
        range.isRestDay = true;
      }
      ranges.push(range);
      flagDate = addDays(flagDate, 1);
    }
    return ranges;
  }

  /**
   * 将日期列表按周分组
   * @param days
   * @returns
   */
  getRangeWeeks(days: DayRange[]) {
    const weeks: any[] = [];
    days.forEach((item) => {
      const index = weeks.length - 1;
      if (weeks[index]?.week === item.week) {
        weeks[index].children.push(item);
      } else {
        weeks.push({
          week: item.week,
          year: item.year,
          children: [item],
        });
      }
    });
    return weeks;
  }

  /**
   * 拼接日维度下的周显示内容
   * @param week
   * @returns
   */
  getWeekData(week: WeekRange) {
    const startWeek = format(week?.children[0]?.date, 'MM/dd');
    const endWeek = format(week?.children[week?.children?.length - 1]?.date, 'MM/dd');
    const weekText = `第${week.week}周`;
    return ' (' + startWeek + ' - ' + endWeek + ') ' + weekText;
  }

  /**
   * 切换维度
   * @param dimension
   */
  transformDimension(dimension: DimensionType, day: any) {
    this.options.dimension = dimension;
    this.calCellWidth();
    setTimeout(() => {
      const dayIndex = this.days.findIndex((item) => isEqual(new Date(item.date), new Date(day.date)));
      this.wrapTopScroll.nativeElement.scrollLeft = this.options.dayWidth * dayIndex;
      this.wrapBody.nativeElement.scrollLeft = this.options.dayWidth * dayIndex;
    });
  }

  /**
   * 判断“返回今天”是否可点击
   * 当时间返回不包括今天，不可点击
   * @returns
   */
  showTodayBtn() {
    return !this.days.find((item) => item.isToday);
  }

  /**
   * 页面滚动至今天
   */
  toToday() {
    const todayIndex = this.days.findIndex((item) => item.isToday);
    this.scrollLeft = this.options.dayWidth * todayIndex;
  }

  /**
   * 滚动到指定日期到位置
   * @param date
   */
  toScrollDate(date: string | Date) {
    const formatDate = format(new Date(date), 'yyyy-MM-dd');
    const index = this.days.findIndex((item) => format(new Date(item.date), 'yyyy-MM-dd') === formatDate);
    this.wrapTopScroll.nativeElement.scrollLeft = this.options.dayWidth * index;
    this.wrapBody.nativeElement.scrollLeft = this.options.dayWidth * index;
    this.scrollLeft = this.options.dayWidth * index;
  }

  /*
   * 高亮后显示蒙层，仅高亮一行数据
   */
  showMask(e: { event: MouseEvent; element: HTMLElement }, index: number) {
    this.data.forEach((v, i) => {
      v.selected = i === index;
    });
    this.selectedItem.emit(index);
    this.maskVisible = true;
  }

  closeMask(e: any) {
    this.data.forEach((v) => {
      v.selected = false;
    });
    this.unSelectedAll.emit(false);
    this.maskVisible = false;
  }

  updateAllChecked(e: boolean) {
    this.selectedAll.emit(e);
    this.indeterminateChange.emit(false);
  }

  /**
   * 更新已经勾选的io的信息
   * @param e
   */
  // toggleCheckedItem() {
  //   if (this.data?.length) {
  //     this.allChecked = this.data.every((v) => v?.checked);
  //     this.indeterminate = !(this.data.every((v) => v?.checked) || this.data.every((v) => !v?.checked));
  //     this.actionSelectId.emit(this.data.filter((item) => item?.checked));
  //   } else {
  //     this.allChecked = false;
  //     this.indeterminate = false;
  //     this.actionSelectId.emit([]);
  //   }
  // }

  forward() {
    this.forwardDate.emit();
  }

  backwards() {
    this.backwardsDate.emit();
  }
}
