<!-- 头部区域 -->

<div #header>
  <flc-search-combination
    [btnTpl]="btnTpl"
    [isFold]="false"
    [checkedCount]="selectedData?.count || 0"
    [isMultiCheck]="false"
    [showAddBtn]="false"
    [checkData]="data_statics"
    (reset)="reset()"
    (refresh)="getList()"
    (handleFold)="resizePage()"
    (onInputSearchValue)="onInputSearchValue($event)"
    (onCheckChange)="onOrderStatusChanged($event)">
    <div *ngFor="let item of searchList">
      <span class="search-name">{{ item.label }}：</span>
      <flc-dynamic-search-select
        *ngIf="item.type === 'select'"
        [payLoad]="{ cache: false }"
        [dataUrl]="'/service/order/v1/list_option'"
        [(ngModel)]="searchData[item.valueKey]"
        [column]="item.labelKey"
        (handleSearch)="onSearch()"
        [canSearch]="item.canSearch || true"
        [optAlwaysReload]="item.alwaysReload || false">
      </flc-dynamic-search-select>
      <flc-dynamic-search-select
        *ngIf="item.type === 'dynamic-select' && item.valueKey === 'dist_user_id'"
        [payLoad]="{ cache: false }"
        [dataUrl]="'/service/procurement-inventory/layoutcons/v1/list-option'"
        [(ngModel)]="searchData[item.valueKey]"
        [column]="item.labelKey"
        (handleSearch)="onSearch()"
        [canSearch]="item.canSearch || true"
        [optAlwaysReload]="item.alwaysReload || false">
      </flc-dynamic-search-select>
      <flc-dynamic-search-select
        *ngIf="item.type === 'dynamic-select' && item.valueKey === 'biz_emp_id'"
        [payLoad]="{ cache: false }"
        [dataUrl]="'/service/procurement-inventory/layoutcons/v1/list-option'"
        [(ngModel)]="searchData[item.valueKey]"
        [column]="item.labelKey"
        (handleSearch)="onSearch()"
        [canSearch]="item.canSearch || true"
        [optAlwaysReload]="item.alwaysReload || false">
      </flc-dynamic-search-select>
      <ng-container *ngIf="item.type === 'cascader'">
        <nz-cascader
          nzPlaceHolder="请选择"
          nzAllowClear
          [nzValueProperty]="'label'"
          [nzShowSearch]="true"
          [nzOptions]="styleList"
          [(ngModel)]="searchData[item.valueKey]"
          (nzVisibleChange)="openStyle($event)"
          (nzSelectionChange)="onSearch()">
        </nz-cascader>
      </ng-container>
      <nz-range-picker
        *ngIf="item.type === 'date'"
        [(ngModel)]="searchData[item.valueKey]"
        (ngModelChange)="getTableList()"></nz-range-picker>
    </div>
  </flc-search-combination>
</div>

<div class="bulk-table-box">
  <flc-table
    [tableHeader]="tableHeader"
    [template]="outTpl"
    [tableConfig]="tableConfig"
    (getDetails)="goDetail($event)"
    (indexChanges)="indexChanges($event)"
    (sizeChanges)="sizeChanges($event)"
    (sortDataLists)="sortDataLists($event)"
    (getCount)="geteSelectedData($event)">
  </flc-table>
  <ng-template #outTpl let-data="data">
    <ng-container *ngIf="data.key === 'material_name'">
      <flc-text-truncated
        [data]="
          data.item?.first_material_name + '-' + data.item?.second_material_name + '-' + data.item?.third_material_name
        "></flc-text-truncated>
    </ng-container>
    <ng-container *ngIf="data.key === 'due_time'">
      <div *ngFor="let dateItem of data.item.po_due_times">
        {{ dateItem | date: 'yyyy/MM/dd' }}
      </div>
    </ng-container>
    <ng-container *ngIf="data.key === 'io_code'">
      <flc-text-truncated [data]="data.item.io_code"></flc-text-truncated>
      <div class="update-tip" *ngIf="data.item.layout_bom_update_times && !data.item.disabled">
        BOM更新+{{ data.item.layout_bom_update_times }}
      </div>
      <div class="cancel-tip" *ngIf="data.item.disabled">已取消</div>
    </ng-container>
    <ng-container *ngIf="data.isAction">
      <a nz-button nzType="link" [disabled]="data.item.disabled" (flcClickStop)="goDetail(data.item)">
        {{ 'flc.common.goDetail' | translate }}
      </a>
    </ng-container>
  </ng-template>
</div>

<ng-template #btnTpl>
  <button nz-button [disabled]="!selectedData?.count" flButton="pretty-default" [nzShape]="'round'" (click)="audit(true)">审核通过</button>
  <button nz-button [disabled]="!selectedData?.count" flButton="pretty-default" [nzShape]="'round'" (click)="audit(false)">退回修改</button>
  <button nz-button [disabled]="!selectedData?.count" flButton="pretty-primary" [nzShape]="'round'" (click)="batchAssignEmployee()">
    分配算料员
  </button>
</ng-template>

<nz-modal
  [(nzVisible)]="isVisibleDistribution"
  nzTitle="分配算料员"
  (nzOnCancel)="isVisibleDistribution = false"
  (nzOnOk)="handleOkDistribution()">
  <ng-container *nzModalContent>
    <div>
      算料员

      <flss-department-select
        [canSelectDepartment]="false"
        [treeOptions]="{}"
        [treeOptions]="deparmentOption"
        (onSelectChange)="_handleChangeValue($event)"
        [selectCurrentUser]="false">
      </flss-department-select>
    </div>
  </ng-container>
</nz-modal>
