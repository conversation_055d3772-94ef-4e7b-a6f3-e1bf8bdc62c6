import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SearchContainerComponent } from './search-container/search-container.component';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule, NzIconService } from 'ng-zorro-antd/icon';
import { FlUiAngularModule } from 'fl-ui-angular';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TableBodyRenderDateComponent } from './table-body-render/table-body-render-date.component';
import { TableBodyRenderComponent } from './table-body-render/table-body-render.component';
import { TableBodyRenderQuantityComponent } from './table-body-render/table-body-render-quantity.component';
import { TableBodyRenderPriceComponent } from './table-body-render/table-body-render-price.component';
import { TableBodyRenderImageComponent } from './table-body-render/table-body-render-image.component';
import { NzImageModule } from 'ng-zorro-antd/image';
import { NzTableModule } from 'ng-zorro-antd/table';
import { TextTruncatedComponent } from './text-truncated.component';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { PipesModule } from '../pipes/pipes.module';
import { TranslateModule } from '@ngx-translate/core';
import { SortBtnComponent } from './sort-btn/sort-btn.component';
import { DynamicSearchSelectComponent } from './dynamic-search-select/dynamic-search-select.component';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { TextEllipsisComponent } from './text-ellipsis/text-ellipsis.component';
import { AffixTopComponent } from './affix-top/affix-top.component';
import { GraphTableModule } from './graph-table/graph-table.module';
import { SearchOptionsCustomContainerComponent } from './search-options-custom-container/search-options-custom-container.component';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { DragDropModule } from '@angular/cdk/drag-drop';

const nzComponents = [
  SortBtnComponent,
  SearchContainerComponent,
  TableBodyRenderComponent,
  TextTruncatedComponent,
  DynamicSearchSelectComponent,
  TextEllipsisComponent,
  AffixTopComponent,
  SearchOptionsCustomContainerComponent,
];
@NgModule({
  declarations: [
    TableBodyRenderDateComponent,
    TableBodyRenderImageComponent,
    TableBodyRenderPriceComponent,
    TableBodyRenderQuantityComponent,
    ...nzComponents,
  ],
  imports: [
    CommonModule,
    FormsModule,
    NzButtonModule,
    NzIconModule,
    FlUiAngularModule,
    NzImageModule,
    NzTableModule,
    ReactiveFormsModule,
    NzToolTipModule,
    PipesModule,
    TranslateModule,
    NzSpinModule,
    NzSelectModule,
    NzToolTipModule,
    GraphTableModule,
    NzSwitchModule,
    DragDropModule,
  ],
  exports: [...nzComponents, GraphTableModule],
})
export class ComponentsModule {
  constructor(private iconService: NzIconService) {
    this.iconService.fetchFromIconfont({
      scriptUrl: 'https://at.alicdn.com/t/font_2782026_gkrcufahyqc.js',
    });
  }
}
