import { Component, Input, OnInit } from '@angular/core';
import { NzImage, NzImageService } from 'ng-zorro-antd/image';
import { finalize } from 'rxjs';

import { FlcOssUploadService } from 'fl-common-lib';

import {
  BasicInfoConfig,
  MaterialInnerHeaderConfig,
  MaterialOutuerHeaderConfig,
  TimeNodeHeaderConfig,
  SeasonObj,
} from './material-plan-detail.config';
import { SampleOutsourcingService } from '../../../sample-outsourcing.service';

interface IImageGalleryItem {
  name: string;
  url: string;
  version: string;
  rawUrl: string;
  imageId?: number;
}
@Component({
  selector: 'flss-material-plan-detail',
  templateUrl: './material-plan-detail.component.html',
  styleUrls: ['./material-plan-detail.component.scss'],
})
export class MaterialPlanDetailComponent implements OnInit {
  @Input() id!: number;
  detailData: any = null;

  translateLabel = 'materialPlanIntegration.label.';
  translateAction = 'materialPlanIntegration.action.';

  basicInfoConfig = BasicInfoConfig;
  timeNodeHeader = TimeNodeHeaderConfig;
  outterHeader = MaterialOutuerHeaderConfig;
  innerHeader = MaterialInnerHeaderConfig;
  seasonObj = SeasonObj;

  imageLeftList: IImageGalleryItem[] = [];
  imageRightList: IImageGalleryItem[] = [];

  loading = false;

  constructor(
    private _service: SampleOutsourcingService,
    private nzImageService: NzImageService,
    private ossUploadService: FlcOssUploadService
  ) {}

  ngOnInit(): void {
    this.loading = true;
    this._service
      .getMaterialPlanDetail(this.id)
      .pipe(
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe((res: any) => {
        if (res.code === 200) {
          this.detailData = res.data;
          this.initImageList();
        }
      });
  }

  /**
   * 初始化image
   */
  async initImageList() {
    this.imageLeftList = [];
    this.imageRightList = [];
    this.detailData?.inspiration_image_url?.length &&
      this.detailData?.inspiration_image_url.forEach(async (item: any) => {
        this.imageLeftList.push({
          ...item,
          rawUrl: item.url,
          url: await this.ossUploadService.getSignatureUrl(item.url, item.version),
        });
      });
    this.detailData?.reference_image_url?.length &&
      this.detailData?.reference_image_url.forEach(async (item: any) => {
        this.imageRightList.push({
          ...item,
          rawUrl: item.url,
          url: await this.ossUploadService.getSignatureUrl(item.url, item.version),
        });
      });
  }

  /**
   * down load image
   * @param item
   */
  async onDownLoadImage(item: IImageGalleryItem) {
    const _downloadUrl = await this.ossUploadService.getSignatureUrl(item.rawUrl || item.url, item.version!, 'download');
    const a = document.createElement('a');
    a.href = _downloadUrl;
    a.target = '_blank';
    a.click();
  }

  /**
   * preview image
   * @param imageList
   * @param index
   */
  async onPreview(imageList: IImageGalleryItem[], index: number) {
    const _imageList: NzImage[] = [];
    imageList.forEach(async (item) => {
      const _previewUrl = item.url;
      _imageList.push({
        src: _previewUrl,
      });
    });
    this.nzImageService.preview(_imageList).switchTo(index);
  }
}
