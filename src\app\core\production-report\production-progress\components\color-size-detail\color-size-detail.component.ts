import { finalize } from 'rxjs';
import { Component, Input, OnInit } from '@angular/core';
import { IDetailData, IDetailParams } from '../../models/production-progress-interface';
import { ProductionProgressService } from '../../production-progress.service';
import { ReportRange, DimensionRange } from './../../../production-report.enum';
import { ProgressDataType } from '../../models/production-progress.enum';

@Component({
  selector: 'app-color-size-detail',
  templateUrl: './color-size-detail.component.html',
  styleUrls: ['./color-size-detail.component.scss'],
})
export class ColorSizeDetailComponent implements OnInit {
  @Input() tab!: number;
  @Input() payload!: IDetailParams;
  @Input() dimension!: number;
  @Input() typeName?: string;
  infoList: Array<{
    label: string;
    value: 'biz_date' | 'io_code' | 'po_code' | 'factory_name' | 'type_name';
    visible: boolean;
  }> = [];
  data: IDetailData = {
    io_code: '',
    po_code: '',
    factory_name: '',
    biz_date: '',
    po_lines: [],
    type_name: '',
    production_line_info: [],
  };
  loading = false;

  progressDataTypeEnum = ProgressDataType;

  constructor(private _service: ProductionProgressService) {}

  ngOnInit(): void {
    this.infoList = [
      {
        label: '日期',
        value: 'biz_date',
        visible: this.tab === ReportRange.daily,
      },
      {
        label: '大货单号',
        value: 'io_code',
        visible: true,
      },
      {
        label: '交付单',
        value: 'po_code',
        visible: this.dimension === DimensionRange.po,
      },
      {
        label: '加工厂',
        value: 'factory_name',
        visible: true,
      },
      {
        label: '类型',
        value: 'type_name',
        visible: (this.typeName ?? '').length > 0,
      },
    ];

    this._getTableList();
  }

  _getTableList() {
    this.loading = true;
    this._service
      .getProgressDetail(this.payload)
      .pipe(
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe((res: any) => {
        if (res.code == 200) {
          this.data = {
            ...res.data,
            type_name: this.typeName,
          };
          this._refreshData();
          this.loading = false;
        }
      });
  }

  _refreshData() {
    let colorItems: number[] = [];
    let specItems: number[] = [];
    let colorSpecItems: string[] = [];
    let poLines: any[] = [];
    this.data.po_lines?.forEach((poLine: any) => {
      const line = poLine;
      if (!colorItems.includes(line.color_info.color_id)) {
        colorItems.push(line.color_info.color_id);
      }
      if (!specItems.includes(line.size_info.spec_id)) {
        specItems.push(line.size_info.spec_id);
      }
      line.indexing = specItems.indexOf(line.size_info.spec_id) + 1;
      colorSpecItems.push(line.color_info.color_id + '-' + line.size_info.spec_id);
      poLines.push(line);
    });
    colorItems?.forEach((colorId: any) => {
      specItems?.forEach((specId: any, index: number) => {
        if (!colorSpecItems.includes(colorId + '-' + specId)) {
          const color: any = poLines.find((poLine: any) => poLine.color_info.color_id === colorId).color_info;
          const spec: any = poLines.find((poLine: any) => poLine.size_info.spec_id === specId).size_info;
          poLines.push({
            id: 0,
            indexing: index + 1,
            deletable: false,
            inspection_item_list: [],
            line_uuid: '',
            qty: null,
            color_info: {
              color_code: color.color_code,
              color_id: colorId,
              color_name: color.color_name,
            },
            size_info: {
              spec_code: spec.spec_code,
              spec_id: specId,
              spec_name: spec.spec_name,
            },
          });
        }
      });
    });
    this.data.po_lines = poLines;
  }
}
