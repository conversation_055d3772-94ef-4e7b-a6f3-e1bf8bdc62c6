/**
 * v1PlanViewDetailResp
 */
export interface SewingDetailResponse {
  actual_end_time: number;
  actual_start_time: number;
  /**
   * 分配详情
   */
  allocate_list: PlanViewDetailRespAllocateItem[];
  /**
   * 分配数
   */
  allocated_qty: number;
  /**
   * 人均日台产
   */
  average_daily_production: number;
  /**
   * 客户
   */
  customer: string;
  /**
   * 每日工作时长
   */
  daily_work_hours: number;
  /**
   * 部门
   */
  dept_name: string;
  /**
   * 交期
   */
  due_times: number[];
  /**
   * 效率
   */
  efficiency: number;
  /**
   * 员工
   */
  employee_name: string;
  factory_code: string;
  /**
   * 工厂
   */
  factory_name: string;
  /**
   * 订单号
   */
  order_code: string;
  /**
   * 订单数
   */
  order_qty: number;
  order_uuid: string;
  plan_end_time: number;
  plan_start_time: number;
  /**
   * 物料齐套日期
   */
  pre_material_completed_time?: number;
  /**
   * 产线
   */
  production_line_name: string;
  production_line_no: string;
  production_list: PlanViewDetailRespProductionItem[];
  /**
   * sam
   */
  sam?: number;
  /**
   * 1-sam 2-每人每日每台产
   */
  selected: number;
  /**
   * 实际车缝产出
   */
  sewing_qty: number;
  /**
   * 款式分配
   */
  style_class: string;
  /**
   * 款号
   */
  style_code: string;
  /**
   * 人数
   */
  user_count: number;

  order_pictures?: any[];
  publish_status: number;
  tags: number[];

  due_times_str: string;

  sewing_precent: number;
}

/**
 * PlanViewDetailRespAllocateItem
 */
export interface PlanViewDetailRespAllocateItem {
  allocated_qty: number;
  color_code: string;
  color_name: string;
  order_qty: number;
  po_code: string;
  po_unique_code: string;
}

/**
 * PlanViewDetailRespProductionItem
 */
export interface PlanViewDetailRespProductionItem {
  /**
   * 目标实际产量
   */
  actual_qty: number;
  /**
   * 日期
   */
  date: number;
  /**
   * 是否休息日
   */
  is_break: boolean;
  /**
   * 目标计划产量
   */
  plan_qty: number;
}
