import { AssignTypeEnum } from '../models/order-garment-outsourcing.enum';

export const commonColumn = [
  'io_code',
  'bulk_codes',
  'order_category_label',
  'order_production_type_label',
  'brand_name',
  'style_code',
  'material_name',
  'order_pictures',
  'customer',
  'po_due_times',
  'distribution_factory_name',
  'order_status_value',
];

const emp = {
  batchConfig: {
    label: '负责人',
    key: 'employee_id',
    labelKey: 'employee_name',
    valueKey: 'employee_user_id',
    type: 'department-select',
  },
  headerConfig: {
    key: 'employee_id',
    labelKey: 'employee_name',
    valueKey: 'employee_user_id',
    label: '负责人',
    width: '150px',
    type: 'template',
    templateName: 'employee_id',
    thType: 'template',
    thTemplateName: 'employee_id',
    pinRight: true,
    disable: false,
    pinned: false,
    visible: true,
    required: true,
  },
};
const mer = {
  batchConfig: {
    label: '跟单员',
    key: 'merchandiser_id',
    labelKey: 'merchandiser_name',
    valueKey: 'merchandiser_user_id',
    type: 'department-select',
  },
  headerConfig: {
    key: 'merchandiser_id',
    labelKey: 'merchandiser_name',
    valueKey: 'merchandiser_user_id',
    label: '跟单员',
    width: '150px',
    type: 'template',
    templateName: 'merchandiser_id',
    pinRight: true,
    disable: false,
    pinned: false,
    visible: true,
    required: true,
  },
};
const qc = {
  batchConfig: {
    label: 'QC',
    key: 'qc_id',
    valueKey: 'qc_user_id',
    labelKey: 'qc',
    type: 'department-select',
  },
  headerConfig: {
    key: 'qc_id',
    labelKey: 'qc',
    valueKey: 'qc_user_id',
    label: 'QC',
    width: '150px',
    type: 'template',
    templateName: 'qc_id',
    pinRight: true,
    disable: false,
    pinned: false,
    visible: true,
    required: true,
  },
};

/**
 * 指派抽屉的配置
 * batchConfig： 抽屉上方批量赋值的操作配置
 * editColumn：可编辑列
 */
export const AssignComponentConfig = {
  [AssignTypeEnum.AssignEmployee]: {
    title: '负责人',
    batchConfig: [emp?.batchConfig],
    editColumn: [emp.headerConfig],
  },
  [AssignTypeEnum.AssignMerchandiser]: {
    title: '跟单员',
    batchConfig: [mer?.batchConfig],
    editColumn: [mer.headerConfig],
  },
  [AssignTypeEnum.AssignQC]: {
    title: 'QC',
    batchConfig: [qc?.batchConfig],
    editColumn: [qc.headerConfig],
  },
  [AssignTypeEnum.AssignMerOrQc]: {
    title: '跟单员/QC',
    batchConfig: [mer?.batchConfig, qc?.batchConfig],
    editColumn: [mer.headerConfig, qc.headerConfig],
  },
};
