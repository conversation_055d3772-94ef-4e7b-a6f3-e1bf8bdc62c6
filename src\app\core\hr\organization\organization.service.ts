import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { AbstractControl, AsyncValidatorFn, FormGroup, ValidationErrors } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { Observable, Observer, timer } from 'rxjs';
import { distinctUntilChanged, finalize, switchMap } from 'rxjs/operators';
import { DepartmentData, Employee, EmployeeListData, TreeOptions } from './interface/structure-data';

@Injectable()
export class OrganizationService {
  btnArr: any[] = [];
  editable = false; // 页面编辑状态
  departStatus = true; // 部门、公司启停用状态记录
  departForm!: FormGroup;
  nodes: any[] = [];
  selectTree: any[] = [];
  expandedTree: any[] = [];
  tableData: EmployeeListData[] = [];
  loading = false;
  total = 0;
  pageIndex = 1;
  pageSize = 20;
  selectOptions: TreeOptions[] = [];
  addedEmployee?: any;
  treeComponent!: any;
  hasDeptDataAuth = true; // 是否有数据权限
  deptDataAuthError: any = null; // 数据权限错误信息
  showDept = false; // 是否展示部门信息

  isEnterprise = true; // 是否是企业

  public procurementInventoryUrl = '/service/procurement-inventory/department/v1';

  constructor(private _http: HttpClient, private _notification: NzNotificationService, private _translate: TranslateService) {}

  selectNode(e: number | string, data: any = null, clearValidator = false) {
    this.selectTree = [e];
    if (data) {
      this.setDepartForm(data, clearValidator);
    } else {
      this.getDepartmentDetail({ id: e }).subscribe((res) => {
        if (res.code === 200) {
          const { id, name, code, status, parent_id } = res.data;
          this.departForm.setValue({ id, name, code, status, parent_id });
          this.departForm.get('code')?.setAsyncValidators(this.uniqueDepartValidator(code ?? null));
          this.updateCodeStatus(res.data);
        }
      });
    }
  }

  setDepartForm(data: any = null, clearValidator = false) {
    this.departStatus = data.status;
    this.departForm.setValue({ ...data });
    if (clearValidator) {
      this.departForm.get('code')?.clearAsyncValidators;
      this.departForm.get('code')?.setAsyncValidators(this.uniqueDepartValidator(data?.code ?? null));
    }
    this.updateCodeStatus(data);
  }

  // 更新部门编码和公司编码的是否可编辑
  updateCodeStatus(data: any) {
    if (!data?.parent_id) {
      this.departForm.get('code')?.disable();
    } else {
      this.departForm.get('code')?.enable();
    }
  }

  /**
   * 获取架构树的数据
   */
  async getTree(): Promise<TreeOptions[]> {
    const data = await this.getDepartmentList().toPromise();
    if (data && data?.code === 200) {
      const line = data?.data;
      this.nodes = this.handleTreeChildren(line);
      this.selectOptions = [];
      this.handleTreeOptions(line);
    }
    return this.selectOptions || [];
  }

  /**
   * 处理架构树的部门数据
   * @param data：原数据
   * @returns
   */
  handleTreeChildren(data: DepartmentData[], expanded_list: any[] = []): any {
    const line = data?.map((item: any) => {
      return {
        ...item,
        key: item.id,
        title: item.name,
        isLeaf: item.employee.length ? false : item.is_leaf,
        expanded_list: expanded_list,
        is_employee: false,
        children: [
          ...this.handleTreeChildren(item.child, [...expanded_list, item.id]),
          ...this.handleTreeEmplyee(item.employee, item.id, [...expanded_list, item.id]),
        ],
      };
    });
    return line;
  }

  /**
   * 处理架构树中的人员数据
   * @param data：人员列表数据
   * @param depart_id：人员对应的部门id
   * @returns
   */
  handleTreeEmplyee(data: Employee[], depart_id: number | null = null, expanded_list: any[] = []): any {
    const line = data?.map((item: any) => {
      return {
        ...item,
        key: 'employee_' + item.id,
        title: item.name,
        isLeaf: true,
        is_employee: true,
        expanded_list: expanded_list,
        depart_id,
      };
    });
    return line;
  }

  /**
   * 将架构树数据结构转化为下拉数据
   * @param data：原数据
   * @param parent_label：上级部门的label
   * @param expanded_list：展开的上级部门id列表
   */
  handleTreeOptions(data: DepartmentData[], parent_label = '', expanded_list: any[] = []): void {
    data?.forEach((item) => {
      let new_label = '';
      if (item.parent_id) {
        new_label = parent_label.length ? parent_label + '/' + item.name : item.name;
      }
      this.selectOptions.push({
        label: new_label,
        value: item.id,
        id: item.id,
        name: item.name,
        expanded_list: expanded_list,
        is_employee: false,
        parent_id: item.parent_id,
      });
      item.employee.forEach((emp) => {
        this.selectOptions.push({
          label: new_label + (item.parent_id ? '/' : '') + emp.name,
          value: 'employee_' + emp.id,
          id: emp.id,
          name: emp.name,
          expanded_list: [...expanded_list, item.id],
          is_employee: true,
          depart_id: item.id,
          parent_id: item.parent_id,
        });
      });
      if (item.child.length) {
        this.handleTreeOptions(item.child, new_label, [...expanded_list, item.id]);
      }
    });
  }

  /**
   * 根据条件获取员工列表
   * @param depart_id：部门id
   * @param reset：是否重置
   * @param id：员工id
   */
  getEmployeeList(depart_id: number | null = null, reset = false, id = null) {
    this.loading = true;
    this.pageIndex = reset ? 1 : this.pageIndex;
    const where = id ? { id: { op: '=', value: id } } : {};
    const depart = depart_id ? depart_id : this.departForm.get('id')?.value;
    const payload = {
      limit: this.pageSize,
      page: this.pageIndex,
      where: {
        dept_id: {
          op: '=',
          value: depart,
        },
        ...where,
      },
      orderBy: [],
    };
    this.getAllemployee(payload)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((res) => {
        if (res.code === 200) {
          this.tableData = res.data.data ?? [];
          this.total = res.data.len ?? 0;
        }
      });
  }

  /**
   * 判断是否有数据权限
   * 无数据权限进行提示
   */
  checkDeptDataAuth(): boolean {
    if (!this.hasDeptDataAuth) {
      this._notification.error(this._translate.instant('bizErrorNotification.prompt'), this.deptDataAuthError.message);
      return false;
    }
    return true;
  }

  /**
   * 获取所有员工
   * @param payload
   */
  getAllemployee(payload: any): Observable<any> {
    return this._http.post<any>('/department/employee/list', payload);
  }

  /**
   * 新增员工
   * @param payload
   * @returns
   */
  addEmployee(payload: any): Observable<any> {
    return this._http.post<any>('/employee', payload);
  }

  /**
   * 更新员工
   * @param payload
   * @returns
   */
  updateEployee(payload: any): Observable<any> {
    return this._http.put<any>(`/employee/${payload.id}`, payload);
  }

  /**
   * 删除员工
   * @param payload
   * @returns
   */
  deleteEmployee(payload: any): Observable<any> {
    return this._http.delete<any>(`/employee/${payload.id}`);
  }

  /**
   * 员工下拉
   * @param payload
   * @returns
   */
  getOptionLists(payload: any): Observable<any> {
    return this._http.post<any>('/employee/search', payload);
  }

  employeeImport(uploadUrl: any, formData: FormData): Observable<any> {
    return this._http.post<any>(uploadUrl, formData, { reportProgress: true });
  }

  /**
   * 检验员工编码唯一性
   */
  uniqueEmployeeValidator = (old_value: null | string = null): AsyncValidatorFn => {
    return (control: AbstractControl) => {
      return timer(500).pipe(
        distinctUntilChanged(),
        switchMap(() => {
          if (control.value === null || control.value === '') {
            return Promise.resolve(null);
          } else {
            // 处理特殊字符
            const newVal = encodeURIComponent(control.value);
            return new Observable((observer: Observer<ValidationErrors | null>) => {
              if (control.value !== old_value) {
                this._http.get<any>(`/employee/code/check?code=${newVal}`).subscribe((res) => {
                  if (res.data) {
                    observer.next(null);
                  } else {
                    observer.next({ duplicated: true, msg: res.message });
                  }
                  observer.complete();
                });
              } else {
                observer.next(null);
                observer.complete();
              }
            });
          }
        })
      );
    };
  };

  /**
   * 新增部门
   * @param payload
   * @returns
   */
  addDepartment(payload: any): Observable<any> {
    return this._http.post<any>('/department', payload);
  }

  /**
   * 更新部门
   * @param payload
   * @returns
   */
  updateDepartment(payload: any): Observable<any> {
    return this._http.put<any>(`/department/${payload.id}`, payload);
  }

  /**
   * 删除部门
   * @param payload
   * @returns
   */
  deleteDepartment(payload: any): Observable<any> {
    return this._http.delete<any>(`/department/${payload.id}`);
  }

  /**
   * 获取部门列表
   * @param payload
   * @returns
   */
  getDepartmentList(): Observable<any> {
    return this._http.get<any>('/department/tree');
  }

  /**
   * 获取部门详情
   * @param payload
   * @returns
   */
  getDepartmentDetail(payload: any): Observable<any> {
    return this._http.get<any>(`/department/${payload.id}`);
  }

  /**
   * 检验部门编码唯一性
   */
  uniqueDepartValidator = (old_value: null | string = null): AsyncValidatorFn => {
    return (control: AbstractControl) => {
      return timer(500).pipe(
        distinctUntilChanged(),
        switchMap(() => {
          if (control.value === null || control.value === '') {
            return Promise.resolve(null);
          } else {
            return new Observable((observer: Observer<ValidationErrors | null>) => {
              if (control.value !== old_value) {
                this._http.get<any>(`/department/code/${control.value}/check`).subscribe((res) => {
                  if (res.data) {
                    observer.next(null);
                  } else {
                    observer.next({ duplicated: true, msg: res.message });
                  }
                  observer.complete();
                });
              } else {
                observer.next(null);
                observer.complete();
              }
            });
          }
        })
      );
    };
  };

  /**
   * 新增操作用户
   * @param payload
   * @returns
   */
  addUser(payload: any): Observable<any> {
    return this._http.post<any>('/user', payload);
  }

  /**
   * 获取操作用户列表
   * @param payload
   * @returns
   */
  geUserList(payload: any): Observable<any> {
    return this._http.post<any>('/department/user/list', payload);
  }

  /**
   * 获取权限列表
   * @param payload
   * @returns
   */
  getRolesList(payload: any): Observable<any> {
    return this._http.post<any>('/user/role/list', payload);
  }

  /**
   * 班次设置详情
   */
  getShiftSettingDetail(params: any): Observable<any> {
    return this._http.post(`${this.procurementInventoryUrl}/shift/detail`, params);
  }

  /**
   * 提交班次设置
   */
  submitShiftSetting(params: any): Observable<any> {
    return this._http.post(`${this.procurementInventoryUrl}/shift/submit`, params);
  }

  /**
   * 检验操作用户编码唯一性
   */
  uniqueRolesValidator = (old_value: null | string = null): AsyncValidatorFn => {
    return (control: AbstractControl) => {
      return timer(500).pipe(
        distinctUntilChanged(),
        switchMap(() => {
          if (control.value === null || control.value === '') {
            return Promise.resolve(null);
          } else {
            return new Observable((observer: Observer<ValidationErrors | null>) => {
              if (control.value !== old_value) {
                this._http.get<any>(`/user/login-name/${control.value}/check`).subscribe((res) => {
                  if (res.data) {
                    observer.next(null);
                  } else {
                    observer.next({ duplicated: true, msg: res.message });
                  }
                  observer.complete();
                });
              } else {
                observer.next(null);
                observer.complete();
              }
            });
          }
        })
      );
    };
  };
}
