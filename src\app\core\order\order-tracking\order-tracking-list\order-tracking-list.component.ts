import { Component, OnInit } from '@angular/core';
import { NzModalService } from 'ng-zorro-antd/modal';
import { OrderTrackingEditDialogComponent } from '../order-tracking-edit-dialog/order-tracking-edit-dialog.component';
import { OrderTrackingLineModel } from '../order-tracking-model';
import { differenceInCalendarDays, endOfDay, isBefore, startOfDay } from 'date-fns';
import { OrderTrackingService } from '../order-tracking.service';

@Component({
  selector: 'app-order-tracking-list',
  templateUrl: './order-tracking-list.component.html',
  styleUrls: ['./order-tracking-list.component.scss'],
})
export class OrderTrackingListComponent implements OnInit {
  dataList: OrderTrackingLineModel[] = [];
  currentIndex = 1;
  currentSize = 20;
  totalCount = 100;
  searchData: { code?: string; customer?: string; style_code?: string; date_range?: any[]; overdue?: any } = {};
  dateFormatter = 'yyyy-MM-dd';
  today = startOfDay(new Date());
  constructor(private _modal: NzModalService, public _service: OrderTrackingService) {}
  isRed(a?: number | null, b?: number | null): boolean {
    if (a && b) {
      return isBefore(a, b);
    }
    return false;
  }
  differenceByToday(targetDate?: number | null): number {
    if (targetDate) {
      const day = differenceInCalendarDays(startOfDay(targetDate), this.today);
      if (day > 0) {
        return day;
      }
    }
    return 0;
  }
  ngOnInit(): void {
    this.getTableList(true);
  }
  onSearch() {
    this.getTableList(true);
  }
  resetAllSelect() {
    this.searchData = {};
    this.getTableList(true);
  }
  getTableList(isReset = false) {
    if (isReset) {
      this.currentIndex = 1;
    }
    const queryData = { ...this.searchData, page: this.currentIndex, size: this.currentSize };
    if (Array.isArray(this.searchData.date_range) && this.searchData.date_range.length) {
      queryData['order_date_start'] = startOfDay(this.searchData.date_range[0]).getTime().toString();
      queryData['order_date_end'] = endOfDay(this.searchData.date_range[1]).getTime().toString();
    }
    this._service.getOrderTrackingList(queryData).subscribe((result) => {
      if (result.code === 200) {
        this.totalCount = result.data.count;
        this.dataList = result.data.data_list;
      }
    });
  }
  editDetailModal(currentLine: OrderTrackingLineModel, tabIndex = 0) {
    const modal = this._modal.create<OrderTrackingEditDialogComponent, boolean>({
      nzContent: OrderTrackingEditDialogComponent,
      nzComponentParams: {
        line: currentLine,
        tabIndex: tabIndex,
      },
      nzClosable: false,
      nzFooter: [
        {
          label: '取消',
          onClick: () => {
            modal.close();
          },
        },
        {
          label: '确定',
          type: 'primary',
          onClick: async (contentComponentInstance) => {
            await contentComponentInstance?.submit();
          },
        },
      ],
      nzMaskClosable: false,
      nzWidth: 1200,
      nzBodyStyle: {
        padding: '0 16px 16px 16px',
      },
    });
    modal.afterClose.subscribe((result) => {
      if (result) {
        this.getTableList();
      }
    });
  }
}
