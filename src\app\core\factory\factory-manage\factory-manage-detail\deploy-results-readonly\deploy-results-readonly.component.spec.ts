import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DeployResultsReadonlyComponent } from './deploy-results-readonly.component';

describe('DeployResultsReadonlyComponent', () => {
  let component: DeployResultsReadonlyComponent;
  let fixture: ComponentFixture<DeployResultsReadonlyComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DeployResultsReadonlyComponent],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DeployResultsReadonlyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
