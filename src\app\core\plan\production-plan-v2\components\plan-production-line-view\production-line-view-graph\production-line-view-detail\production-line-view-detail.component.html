<div class="detail-content" *ngIf="detailBarItem !== null">
  <div class="detail-item">
    <span>IO: </span>
    <div>{{ detailBarItem.order_code || '-' }}</div>
    <div class="is_pre_order" *ngIf="detailBarItem.is_pre_order">预</div>
    <div class="IoBar">
      <div [ngClass]="'publish_status' + detailBarItem.publish_status">
        {{ statusMap[detailBarItem.publish_status] }}
      </div>
    </div>
  </div>
  <div class="detail-item">
    <span>款式编码: </span>
    <div class="itemValue">
      {{ detailBarItem.style_code || '-' }}
    </div>
  </div>
  <div class="detail-item">
    <span>款式分类: </span>
    <div class="itemValue">
      {{ detailBarItem.style_class || '-' }}
    </div>
  </div>

  <div class="detail-item">
    <span>{{ '产出/分配' }}: </span>
    <div class="itemValue">{{ (detailBarItem.sewing_qty | number) || '-' }}/{{ (detailBarItem.allocated_qty | number) || '-' }}</div>
  </div>

  <div class="detail-item">
    <span>交付日期: </span>
    <div class="itemValue">
      <span *ngFor="let due_time of detailBarItem.due_times; last as isLast">
        {{ due_time | date: 'yyyy/MM/dd' }}
        <ng-container *ngIf="!isLast">,</ng-container>
      </span>
    </div>
  </div>

  <div class="detail-item">
    <span>物料齐套时间：</span>
    <div class="itemValue">
      <flc-no-value-text
        data=" {{ detailBarItem.pre_material_completed_time || null | date: 'yyyy/MM/dd' | flcNoValue }}"></flc-no-value-text>
    </div>
  </div>

  <div class="detail-item">
    <span>计划起止: </span>
    <div class="itemValue">
      {{ detailBarItem.start_time | date: 'yyyy/MM/dd' | flcNoValue }}~{{ detailBarItem.end_time | date: 'yyyy/MM/dd' }}
    </div>
  </div>

  <div class="detail-item">
    <span>业务员: </span>
    <div class="itemValue">{{ detailBarItem.dept_name | flcNoValue }} / {{ detailBarItem.employee_name | flcNoValue }}</div>
  </div>
</div>
