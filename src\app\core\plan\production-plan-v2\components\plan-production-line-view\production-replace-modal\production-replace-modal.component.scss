.modal-body {
  overflow-y: scroll;
  margin-top: -16px;

  .modal-body-header {
    min-height: 42px;
    padding: 0 8px;
    font-weight: 500;
    background-color: #f7f8fa;
    border-radius: 0 0 4px 4px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .pre-order-tag {
      width: 16px;
      height: 16px;
      font-size: 10px;
      color: #ef6c69;
      border: 1px solid #f99616;
      border-radius: 50%;
      text-align: center;
    }
  }

  .modal-content {
    padding-top: 8px;
  }
}

.modal-table {
  nz-select,
  nz-input-number {
    width: 100%;
  }

  .ant-form-item {
    margin-bottom: 0;
  }
}

.bottomBar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  column-gap: 8px;
  padding-top: 16px;
}

.line-operate-btn {
  a {
    cursor: pointer;
    width: auto;
    height: auto;
    margin: 0;
    background-color: transparent;
    &:first-child {
      color: #f96d6d;

      margin-right: 6px;
    }

    &:last-child {
      color: #4d96ff;
    }

    &[disabled] {
      color: #ddd !important;
    }
  }
}
