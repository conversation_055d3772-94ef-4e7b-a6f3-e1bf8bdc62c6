<div style="display: flex; padding: 12px; gap: 12px; background-color: #fff">
  <nz-spin [nzSpinning]="treeLoading">
    <div class="tree-container-header">
      <!-- title -->
      <span style="color: #25af7f; font-size: 18px">{{ translateName + '国家列表' | translate }}</span>
      <ng-container *ngFor="let item of countryList">
        <button
          nz-button
          nzType="text"
          class="treeItem"
          [ngClass]="{ selected: selectedCountryCode === item.country_code }"
          (click)="clickCountry(item)">
          {{ item.country_name }}
        </button>
      </ng-container>
    </div>
  </nz-spin>
  <div style="width: 100%; overflow: auto">
    <div class="calendar-header">
      <ng-container *ngIf="!editable">
        <div class="today" nzTooltipTitle="{{ translateName + '回到今天' | translate }}" nz-tooltip (click)="goToday()">
          {{ translateName + '今天' | translate }}
        </div>
        <span class="switch">
          <span class="switch-button" (click)="previousMonth()"><i nz-icon nzType="left" nzTheme="outline"></i></span>
          <span class="line"></span>
          <span class="switch-button" (click)="nextMonth()"><i nz-icon nzType="right" nzTheme="outline"></i></span>
        </span>
      </ng-container>
      <span class="month">
        {{ getCurrentMonth() }}
      </span>
      <div style="margin-left: auto" *ngIf="selectedCountryCode && can_edit_field(selectedCountryName)">
        <ng-container *ngIf="!editable; else otherTemp">
          <button (click)="editClick()" nz-button [flButton]="'pretty-primary'" [nzShape]="'round'">
            {{ translateName + '编辑' | translate }}
          </button>
        </ng-container>
        <ng-template #otherTemp>
          <button style="margin-right: 10px" (click)="cancel()" nz-button [flButton]="'default'" [nzShape]="'round'">
            {{ 'flss.btn.cancel' | translate }}
          </button>
          <button (click)="submit()" nz-button [flButton]="'pretty-primary'" [nzShape]="'round'" [nzLoading]="submitLoading">
            {{ translateName + '提交' | translate }}
          </button>
        </ng-template>
      </div>
    </div>
    <nz-calendar
      #calendar
      [nzMode]="'month'"
      [nzFullscreen]="false"
      [nzDateFullCell]="dateCellTpl"
      [(ngModel)]="currentDate"
      [nzDisabledDate]="disabledDate"
      (nzSelectChange)="onSelectedDateChanged($event)">
      <ng-template #dateCellTpl let-date>
        <ng-container>
          <div class="dateCell" [ngClass]="{ disable: !validDate(date), current: isCurrentDate(date) }">
            <div class="dateCellDay">{{ date.getDate() }}</div>
            <div class="dateCellSelect">
              <nz-select
                style="width: 90%"
                *ngIf="editable && validDate(date)"
                [ngModel]="getDateType(date)"
                [nzPlaceHolder]="'flss.placeholder.select' | translate"
                (ngModelChange)="onDateTypeChanged($event, date)">
                <nz-option [nzValue]="1" [nzLabel]="translateName + '工作日' | translate"></nz-option>
                <nz-option [nzValue]="2" [nzLabel]="translateName + '休息日' | translate"></nz-option>
              </nz-select>
              <span class="dateCellSpan" *ngIf="!editable || !validDate(date)">
                {{ getDateTypeString(date) }}
              </span>
            </div>
          </div>
        </ng-container>
      </ng-template>
    </nz-calendar>
  </div>
</div>
