.depart-info {
  display: flex;
  flex-direction: column;
  background: #ffffff;
  border-radius: 4px 4px 12px 12px;
  padding: 8px 16px;
  gap: 16px;

  .depart-header {
    display: flex;
    justify-content: space-between;
    align-items: baseline;

    & > div:nth-child(1) {
      font-size: 16px;
      font-weight: 500;
      color: #515661;
    }

    & > div:nth-child(2) {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .depart-content {
    display: flex;

    form {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      nz-form-item {
        margin-bottom: 12px;
        display: flex;
        align-items: flex-start;
      }
    }
  }
}

::ng-deep .shift-setting-modal .ant-modal {
  .ant-modal-title {
    text-align: center;
    font-weight: 600;
    font-size: 16px;
    color: #262d48;
  }

  .ant-modal-body {
    padding: 24px 24px 4px !important;
  }

  .ant-modal-footer {
    text-align: center !important;
  }
}

.contract-logo {
  display: flex;

  .contract-logo-tip {
    padding-top: 4px;
    padding-left: 8px;

    span {
      color: #54607c;
    }

    div {
      font-size: 12px;
      color: #97999c;
    }
  }
}
