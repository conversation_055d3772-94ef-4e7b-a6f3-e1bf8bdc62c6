import { Component, Input, OnInit } from '@angular/core';
import { format } from 'date-fns';

@Component({
  selector: 'app-order-outsourcing-basic-info',
  templateUrl: './order-outsourcing-basic-info.component.html',
  styleUrls: ['./order-outsourcing-basic-info.component.scss'],
})
export class OrderOutsourcingBasicInfoComponent implements OnInit {
  @Input() set basicInfo(val: any) {
    this._basicInfo = val;
    this.initData();
  }

  basicInfoConfig = [
    {
      label: '客户名称',
      key: 'customer',
      itemSpan: 8,
      type: 'text',
    },
    // {
    //   label: '客户款号',
    //   key: 'customer_style',
    //   itemSpan: 8,
    //   lableSpan: 6,
    //   controlSpan: 18,
    //   type: 'text',
    // },
    {
      label: '款式编码',
      key: 'style_code',
      itemSpan: 8,
      type: 'text',
    },
    {
      label: '品名',
      key: 'category',
      itemSpan: 8,
      type: 'text',
    },
    {
      label: '款式分类',
      key: 'material_name',
      itemSpan: 8,
      type: 'text',
    },
    {
      label: '下单日期',
      key: 'order_date',
      itemSpan: 8,
      type: 'text',
    },
    {
      label: '二次工艺',
      key: 'extra_process_info',
      itemSpan: 8,
      type: 'text',
    },
    {
      label: '销售单号',
      key: 'contract_number',
      itemSpan: 8,
      type: 'text',
    },
    { label: '品质', key: 'quality_level', type: 'select', itemSpan: 8 },
    { label: '产品类型', key: 'production_category_name', type: 'text', itemSpan: 8 },
    { label: '付款条件', key: 'payment_condition', type: 'select', itemSpan: 8 },
    { label: '是否快反', key: 'fast_reply', type: 'select', itemSpan: 8 },
    { label: '是否需要第三方质检', key: 'third_quality_check', type: 'select', itemSpan: 8 },
    { label: '是否需要报关', key: 'customs_clearance', type: 'select', itemSpan: 8 },
    {
      label: '备注',
      key: 'remark',
      itemSpan: 16,
      type: 'text',
    },
    {
      label: '附件',
      key: 'appendix_requirements',
      itemSpan: 24,
      type: 'file',
    },
  ];
  _basicInfo: any = {};
  constructor() {}

  ngOnInit() {}
  initData() {
    if (Object.keys(this._basicInfo).length !== 0) {
      this.basicInfoConfig.forEach((item) => {
        if (item.key === 'order_date') {
          this._basicInfo[item.key] = format(Number(this._basicInfo[item.key]), 'yyyy/MM/dd');
        }
        if (item.key === 'extra_process_info') {
          let extra_process_info = '';
          this._basicInfo[item.key]?.forEach((item: any) => {
            extra_process_info += `${item.extra_process_name}、`;
          });
          this._basicInfo[item.key] = extra_process_info.replace(/[、]$/, '');
        }
        if (item.key === 'material_name') {
          this._basicInfo[
            item.key
          ] = `${this._basicInfo.first_material_name}-${this._basicInfo.second_material_name}-${this._basicInfo.third_material_name}`;
        }
      });
    }
  }
}
