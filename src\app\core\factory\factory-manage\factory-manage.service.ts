import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ResData } from './interface/common-res';
import { Factories, SearchParam } from './interface';

@Injectable()
export class FactoryManageService {
  btnArr: any[] = [];
  statusLists = [
    {
      label: '待激活',
      value: 1,
    },
    {
      label: '待审核',
      value: 2,
    },
    {
      label: '待部署',
      value: 3,
    },
    {
      label: '待修改',
      value: 4,
    },
    {
      label: '待初始化',
      value: 5,
    },
    {
      label: '初始化完成',
      value: 6,
    },
    {
      label: '已撤销',
      value: 7,
    },
    {
      label: '已注销',
      value: 8,
    },
  ];

  constructor(private http: HttpClient) {}

  /**
   * 获取工厂列表数据
   * @param payload
   */
  getFactoryList(payload: SearchParam): Observable<ResData<Factories>> {
    return this.http.post<ResData<Factories>>('/factory/list', payload);
  }
}
