.wrap {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: #222b3c;
  line-height: 20px;
  margin: 3px 0;
  white-space: nowrap;
}

.title {
  display: inline-flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
}

.type {
  display: inline-flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  // width: 114px;
}

.btnBoard {
  background-color: #fff;
  display: flex;
  row-gap: 4px;
  column-gap: 4px;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: flex-start;
  margin-left: 8px;
  padding: 4px;
  flex-grow: 1;
}

.rangeBoard {
  background-color: #fff;
  display: flex;
  row-gap: 4px;
  column-gap: 4px;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: flex-start;
  margin-left: 8px;
  padding: 4px;
  flex-grow: 1;
}

:host .radioText {
  ::ng-deep span:last-child {
    font-size: 14px;
    font-weight: 500;
    color: #222b3c;
    line-height: 20px;
  }
}
