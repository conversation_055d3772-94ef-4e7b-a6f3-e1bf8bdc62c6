export const FactoryDetail = [
  { label: '公司编码', key: 'code', required: true },
  { label: '公司名称', key: 'name', required: true },
  { label: '公司缩写', key: 'abbr', required: true },
  { label: 'ELAN租户', key: 'elan_code', required: false },
  { label: '合作模式', key: 'coop', required: false },
  { label: '工厂规模', key: 'scale', required: false, unit: 'unit.people' },
  { label: '车缝人数', key: 'sewing_workers', required: false, unit: 'unit.people' },
  { label: '擅长品类', key: 'category', required: false },
  { label: '主要客户', key: 'customer', required: false },
  { label: '联系人', key: 'contacts', required: false },
  { label: '联系方式', key: 'tel', required: false },
  { label: '邮箱', key: 'email', required: false },
  { label: '地区', key: 'region', required: false, template: 'region' },
  { label: '详细地址', key: 'address', required: false },
  { label: '部署区域', key: 'deploy_region', labelKey: 'deploy_region_name', required: true },
  { label: '工厂类型', key: 'factory_type', required: true, template: 'factoryType' },
];

export const CoopOptions = [
  {
    label: '来料加工',
    value: '来料加工',
  },
  {
    label: '包工包料',
    value: '包工包料',
  },
];

export const InitProcessSteps = [
  {
    name: '待激活',
    gen_time: '',
    status: 'process',
  },
  {
    name: '待审批',
    gen_time: '',
    status: 'wait',
  },
  {
    name: '待部署',
    gen_time: '',
    status: 'wait',
  },
  {
    name: '待初始化',
    gen_time: '',
    status: 'wait',
  },
  {
    name: '初始化完成',
    gen_time: '',
    status: 'wait',
  },
];
