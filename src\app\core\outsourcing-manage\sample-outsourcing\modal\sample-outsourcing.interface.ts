import { searchOptions } from '../list/smaple-outsourcing.config';
import { SampleOutsouringStatusEnum } from './sample-outsourcing.enum';

// 将联合类型转换成对象类型
export type MappedType<T extends PropertyKey, ValueType = string | number | null> = {
  [P in T]?: ValueType;
};

/**
 * @description
 * 检索searchOptions valueKey联合类型
 */
export type SearchModal = typeof searchOptions[number]['valueKey'];

/**
 * @description
 * api数据返回基本格式
 */
export type ResultData<T> = {
  code: number;
  message: string;
  data: T;
};

/**
 * @description
 * 打样外发额外字段
 */
export interface IBaseDetailModel {
  sample_order_id: number;
  sample_order_no: string;
  factory_id: number; // 工厂id
  factory_name: string; // 工厂
  process_type: number; //外发类型
  process_type_value: string;
  recipient: string; //收件人
  contact_information: string;
  country_id: number;
  country: string; //国家
  province_id: number; //省
  city_id: number; //市
  district_id: number; //区
  province: string;
  city: string;
  district: string;
  detailed_address: string;
  has_history: boolean;
}

// 颜色尺码
interface IColorSizeModel {
  id: number; // color_size_id
  color_info: {
    color_id: number;
    color_name: string;
    color_code: string;
  };
  size_info: {
    spec_id: number;
    spec_size: string;
    spec_code: string;
  };
  qty: number; // num
  indexing: number;
  deletable: boolean;
}

/**
 * @description
 * 打样需求单详情数据类型
 */
export interface IDetailModel extends IBaseDetailModel {
  id: number;
  style_code: string; // 款式编码
  sample_order_id: number; //打样单号id
  sample_order_no: string; // 打样单号
  expected_delivery_date: number | null; //期望交付时间
  sample_type_name: string;
  style_requirements: string; // 款式要求
  material_requirements: string; //物料要求:1000字,
  plate_requirements: string; //制版要求:1000字,
  craft_requirements: string; //工艺要求:1000字,
  remark: string; //备注:1000字,
  design_code: string; //图稿编码,
  first_style_name: string; // 一级名称,
  second_style_name: string; // 二级名称,
  third_style_name: string; //三级款式分类名称,
  brand_name: string; //品牌名称,
  year: number; // 年份
  month: number; // 月份
  season_name: string; // 季节名称
  band_name: string; // 波段名称 ,
  from_type_value: string; //来源,
  designer_name: string; //设计师,
  style_pic_list: { name: string; url: string; version: string }[]; //[图片地址],
  color_size: Array<IColorSizeModel>;
  sample_sort_num?: number | null; // 打样轮次
  status: SampleOutsouringStatusEnum; // 打样单状态
  status_value: string; // 打样单状态
  reason_for_return?: string; //退回修改原因,当status为3时有效
  modify_time?: number | null; //退回时间,当status为3时有效
  extra_process_name: Array<string>; //二次工艺id
  outfit: number; //是否套装
  series_name: string; //系列档案名称
  category: string; //品名
  style_uuid: string;
  uuid: string;
  // 1.4
  material_rel?: Array<IMaterialRel> | null; // 关联材料企划
  production_category_name?: string;
  quality_level?: number;
}

export interface IMaterialRel {
  indexing: number | null; //关键材料企划页面显示顺序，必填
  material_plan_id: number | null; // 关键材料企划主键
  material_plan_name: string | null; // 关键材料企划名称
  material_lines: Array<IMaterialLine> | null; // 关键物料列表
  is_open?: boolean;
  is_empty?: boolean;
  fabric_ids?: Array<number> | null; // 面料ids
  accessories_ids?: Array<number> | null; // 辅料ids
}

export interface IMaterialLine {
  raw_material_id: number | null; // 物料主键。
  raw_material_type: number | null; // 物料类型，1-面料，2-辅料
  raw_material_name: string | null; // 物料名称
  reference_image_url: Array<{ name: string; url: string; version: string }> | null; // 图片地址
  description: string | null; // 描述
}

/**
 * @description
 * tranlate详情特殊展示数据
 */
export interface IFormartDetailModel extends IDetailModel {
  styleName: string;
  monthName: string;
  outfitName: string;
  expected_delivery_dateline: string;
  area: string;
}

// 款式分类下拉
