import { NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { finalize } from 'rxjs';
import { Component, Input, OnInit } from '@angular/core';
import { PlanStatus } from '../../interface';
import { SewingItemDrawerService } from './sewing-item-drawer.service';
import { NzDrawerRef } from 'ng-zorro-antd/drawer';
import { format } from 'date-fns';
import { ProductionPlanShareService } from '../../production-plan-share.service';
import { SewingDetailResponse } from '.';
import { FlcUtilService } from 'fl-common-lib';
import { OrderCalculationTypeEnum } from '../../model/production-plan.enum';

@Component({
  selector: 'app-sewing-item-drawer',
  templateUrl: './sewing-item-drawer.component.html',
  styleUrls: ['./sewing-item-drawer.component.scss'],
  providers: [SewingItemDrawerService],
})
export class SewingItemDrawerComponent implements OnInit {
  @Input() group_id!: number;
  @Input() isEdit = true;
  sewingData!: SewingDetailResponse;
  isSpinning = true;
  is_refresh_list = false;

  isEditInput = false;
  inputValue?: number;

  orderCalculationTypeEnum = OrderCalculationTypeEnum;
  publish_status = PlanStatus;

  totalObject = {
    production: {
      plan_qty: 0,
      actual_qty: 0,
    },
    allocation: {
      order_qty: 0,
      allocated_qty: 0,
    },
  };

  constructor(
    private _service: SewingItemDrawerService,
    private drawerRef: NzDrawerRef<{ dataParam: any; sewingData: any; isSpinning: boolean }>,
    public _shareService: ProductionPlanShareService,
    private _msg: NzMessageService,
    private _modal: NzModalService,
    private _flcUtilService: FlcUtilService
  ) {}

  ngOnInit(): void {
    this.getSewingLineDetail();
  }

  close() {
    return this.drawerRef.close({
      is_refresh_list: this.is_refresh_list,
    });
  }

  private getSewingLineDetail() {
    this.isSpinning = true;
    this._service
      .getLineDetail({ group_id: this.group_id })
      .pipe(finalize(() => (this.isSpinning = false)))
      ?.subscribe((res) => {
        if (res.code === 200) {
          this.isEditInput = false;
          this.sewingData = res.data;
          this.inputValue =
            this.sewingData.selected === OrderCalculationTypeEnum.average
              ? this.sewingData.average_daily_production
              : this.sewingData.efficiency;
          this.sewingData.due_times_str = this.sewingData.due_times?.map((item) => format(item, 'yyyy/MM/dd')).join(',');
          this.sewingData.sewing_precent =
            this._flcUtilService.toFixed(
              this._flcUtilService.accDivPlus(this.sewingData.sewing_qty, this.sewingData.allocated_qty || 1) * 100,
              2
            ) || 0;
          this.calTotoalObject();
        }
      });
  }

  private calTotoalObject() {
    this.totalObject.production.plan_qty = this.sewingData.production_list.reduce((acc, cur) => acc + cur.plan_qty || 0, 0);
    this.totalObject.production.actual_qty = this.sewingData.production_list.reduce((acc, cur) => acc + cur.actual_qty || 0, 0);
    this.totalObject.allocation.order_qty = this.sewingData.allocate_list.reduce((acc, cur) => acc + cur.order_qty || 0, 0);
    this.totalObject.allocation.allocated_qty = this.sewingData.allocate_list.reduce((acc, cur) => acc + cur.allocated_qty || 0, 0);
  }

  onEdit() {
    this.isEditInput = true;
  }

  onChangeInput(isEdit: boolean) {
    if (!isEdit) {
      this.isEditInput = false;
      this.getSewingLineDetail();
      return;
    }
    if (!this.inputValue) {
      this._msg.error(this.sewingData.selected === OrderCalculationTypeEnum.average ? '请输入平均日产量' : '请输入目标效率');
      return;
    }
    const payload: any = {
      group_id: this.group_id,
      temp_session: this._shareService.temp_session,
    };
    if (this.sewingData.selected === OrderCalculationTypeEnum.average) {
      payload.average_daily_production = this.inputValue;
    } else {
      payload.efficiency = this.inputValue;
    }
    this._service.modifySamOrEffi(payload).subscribe((res) => {
      if (res.code === 200) {
        this.is_refresh_list = true;
        this._msg.success('修改成功');
        this.getSewingLineDetail();
      }
    });
  }
}
