import { Component, ElementRef, EventEmitter, Input, OnInit, Output, Renderer2, SimpleChanges, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { endOfDay, format, startOfDay } from 'date-fns';
import { isNotNil } from 'ng-zorro-antd/core/util';
import { resizable, FlcRouterEventBusService } from 'fl-common-lib';
import { BulkService } from '../bulk.service';
import { AppStorageService } from 'src/app/shared/app-storage.service';
import { Subject, Subscription } from 'rxjs';

@Component({
  selector: 'print-label-component',
  templateUrl: './print-label.component.html',
  styleUrls: ['./print-label.component.scss'],
})
@resizable()
export class printLabelComponent implements OnInit {
  @Input() isVisible = false;
  @Input() printTitle = '标签全打';
  @Input() printType = 1;
  @Input() selectedIds = [];
  @Input() bulk_order_card = [];
  @Output() visibleCahnge = new EventEmitter(); // 向前10天
  printValue = 1;
  printTotalQty: any = null;
  printAty = 0;
  printVisible = false;
  bulk_order_card_list = [];
  pageSize: any = null;
  translateName = 'printLabel.';

  constructor(public _storage: AppStorageService, public _service: BulkService, private _renderer: Renderer2) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes && changes.bulk_order_card && changes.bulk_order_card.currentValue) {
      this.bulk_order_card_list = JSON.parse(JSON.stringify(changes.bulk_order_card.currentValue));
    }
  }

  onCancel() {
    this.visibleCahnge.emit(false);
  }

  // 打印方法
  private doPrint(finish?: Subject<void>) {
    const style = document.createElement('style');
    style.innerHTML = `@media print{@page {
      size: ${this.pageSize?.replace(',', ' ')};
      margin: 0cm;
    }
    body {
      margin: 0cm;
    }}`;
    document.head.appendChild(style);
    const printEle = document.getElementById('printPage')!.cloneNode(true);
    this._renderer.setStyle(document.body, 'display', 'none');
    this._renderer.setStyle(printEle, 'display', 'block');
    this._renderer.setStyle(printEle, 'zoom', 1);
    this._renderer.appendChild(document.body.parentElement, printEle);
    window.print();
    setTimeout(() => {
      style.remove();
      this._renderer.removeChild(document.body.parentElement, printEle);
      this._renderer.setStyle(document.body, 'display', 'block');
      setTimeout(() => {
        finish?.next();
      });
    }, 160);
  }

  handleOk() {
    this.doPrint();
  }
  getPrintCard() {
    let arr: any = [];
    this.bulk_order_card_list.forEach((item: any) => {
      if (this.printType === 2) {
        item.line_code_list.forEach((f: any) => {
          if (!f.printMinQty) {
            f.printMinQty = null;
          }
          if (!f.printMaxQty) {
            f.printMaxQty = null;
          }

          // 如果选中，则需要根据范围筛选打印数量
          if (item.printOptionsOne.includes(f.line_card_list)) {
            f.printQtyList = [];
            f.printQtyList = f.line_card_list.slice(f.printMinQty > 0 ? f.printMinQty - 1 : 0, f.printMaxQty ?? f.line_card_list.length);
            arr = arr.concat(...f.printQtyList);
          }
          if (!item.printOptionsOne.length) {
            f.printQtyList = [];
          }
        });
      } else {
        arr = arr.concat(...item.printOptionsOne);
      }
    });
    this.printTotalQty = arr.length;
    return arr;
  }

  printSizeChange(e: Event, item: any, size: any) {
    item.printOptionsOne = e;
    console.log(2222, e);
    this.pageSize = size;
    this.getPrintCard();
  }
}
