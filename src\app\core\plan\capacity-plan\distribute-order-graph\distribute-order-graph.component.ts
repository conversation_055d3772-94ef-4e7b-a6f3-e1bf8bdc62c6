import { finalize } from 'rxjs';
import { Component, Input, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { GanttCellWidth } from 'src/app/components/graph-table/graph-table';
import { addDays, differenceInMinutes, format, startOfMinute, subDays } from 'date-fns';
import { CapacityPlanService } from '../capacity-plan.service';
import { CapacityGraph, CapacityGraphParam, FactorySchedule, FactoryType } from '../capacity-plan.interface';
import { GraphTableComponent } from 'src/app/components/graph-table/graph-table.component';

@Component({
  selector: 'app-distribute-order-graph',
  templateUrl: './distribute-order-graph.component.html',
  styleUrls: ['./distribute-order-graph.component.scss'],
})
export class DistributeOrderGraphComponent implements OnInit {
  @ViewChild(GraphTableComponent) graphTable!: GraphTableComponent;
  @Input() dates!: {
    start_date: Date;
    end_date: Date;
  };
  @Input() factoryId: number | null = null;
  options = {
    signalWidth: GanttCellWidth.day,
    dayWidth: GanttCellWidth.day, // 日维度一天48，小时维度一天24*12
    wrapBodyWidth: 0,
    dimension: 'day',
    per1Px: 1,
  };
  allFactoryList: CapacityGraph[] = [];
  loading = false;

  get cellWidth(): number {
    // left、top计算单位为分钟， 天除以60 /24，小时只需要除以60, 再除以2是因为是每2小时一格
    return this.options.dimension === 'day' ? GanttCellWidth.day / 60 / 24 : GanttCellWidth.hour / 60 / 2;
  }

  /**
   * 当前时间线位置
   */
  get currentLineLeft(): number {
    const startTime = startOfMinute(new Date(format(new Date(), 'yyyy-MM-dd') + ' 12:00:00'));
    return differenceInMinutes(startTime, this.dates?.start_date ?? new Date());
  }

  /**
   * 根据选择工厂筛选
   */
  get factoryList() {
    return this.factoryId ? this.allFactoryList.filter((item) => item.factory_id === this.factoryId) : this.allFactoryList;
  }

  constructor(private _service: CapacityPlanService) {}

  ngOnInit() {}

  ngOnChanges(changes: SimpleChanges): void {
    const isUpdateDate = changes?.dates?.currentValue !== changes?.dates?.previousValue;

    if (isUpdateDate) {
      this.getCapacityDetail();
    }
  }

  /**
   * 向前10天
   */
  forwardDate() {
    this.dates = {
      start_date: subDays(new Date(this.dates.start_date), 10),
      end_date: this.dates.end_date,
    };
    this.getCapacityDetail();
  }

  /**
   * 向后10天
   */
  backwardsDate() {
    this.dates = {
      start_date: this.dates.start_date,
      end_date: addDays(new Date(this.dates.end_date), 10),
    };
    this.getCapacityDetail();

    setTimeout(() => {
      const showEnd = this.dates.end_date ?? new Date();
      this.graphTable?.toScrollDate(showEnd);
    }, 10);
  }

  optionsChange(e: any) {
    this.options = { ...e };
  }

  getCapacityDetail() {
    const payload: CapacityGraphParam = {
      start_date: this.dates.start_date.getTime(),
      end_date: this.dates.end_date.getTime(),
    };
    this.loading = true;
    this._service
      .capacityList(payload)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((res) => {
        if (res.code === 200) {
          this.allFactoryList = res.data.factories;

          this._service.factories = this.allFactoryList.map((item) => {
            return { factory_id: item.factory_id, factory_name: item.factory_name };
          });

          this.allFactoryList.forEach((line) => {
            if (line.factory_type === FactoryType.profession) {
              line.factory_schedules.forEach((item) => {
                this.calcGraphPosition(item);
              });
            } else {
              line.factory_schedules = [];
            }
          });
        }
      });
  }

  /** 计算每个graph位置 */
  calcGraphPosition(item: FactorySchedule): void {
    const startTime = startOfMinute(new Date(item.start_time));
    const endTime = startOfMinute(new Date(item.end_time));
    const offset = differenceInMinutes(startTime, this.dates.start_date);
    const width = differenceInMinutes(endTime, startTime);
    item.left = offset;
    item.width = width;
  }
}
