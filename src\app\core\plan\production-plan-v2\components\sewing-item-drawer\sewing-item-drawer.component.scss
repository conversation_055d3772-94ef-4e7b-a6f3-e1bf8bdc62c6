::ng-deep .sewing-line-drawer {
  .sewing-base-info {
    .sewing-base-info-header {
      display: flex;
      justify-content: space-between;
      background: rgba(0, 130, 255, 0.07);
      border-radius: 8px;
      padding: 2px 10px;
      margin-bottom: 10px;

      .img-item {
        img {
          width: 34px;
          height: 34px;
          border-radius: 8px;
        }
      }

      .ant-progress-inner {
        position: relative;
        display: inline-block;
        width: 100%;
        overflow: hidden;
        vertical-align: middle;
        border-radius: 100px;
        background: rgba(41, 150, 255, 0.23);
        // box-shadow: inset 0px 1px 3px 0px rgba(12, 35, 78, 0.5);
      }

      .union-line-show {
        border-radius: 4px;
        border: 1px solid #2996ff;
        font-weight: 500;
        color: #2996ff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 1px 5px;
      }
    }
  }

  .subtitle-text {
    font-weight: 500;
    margin-bottom: 10px;
  }

  .daily-production-table {
    margin-top: 14px;

    .week-text {
      color: #bfbfbf;
      font-size: 20px;
      -webkit-transform: scale(0.5);
      transform: scale(0.5);
      -webkit-transform-origin-x: center;
      margin-top: -6px;
      //   font-size: 10px;
    }
  }

  .ant-table-thead > tr > th {
    background: #f7f8fc;
    border-bottom: none;
    font-size: 14px;
    font-weight: 500;
    color: #24294e;
  }

  .ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan]):before {
    display: none;
  }

  .ant-table-tbody > tr > td {
    border-bottom: none;
    font-size: 14px;
    font-weight: 500;
    color: #565454;
  }

  .ant-table-tbody > .ant-table-row:nth-child(even) {
    background: #f9faff;
  }

  nz-table-inner-scroll .ant-table-tbody > .ant-table-row {
    &:nth-child(odd) {
      background: #f9faff;
      .ant-table-cell-fix-left,
      .ant-table-cell-fix-right {
        background: #f9faff;
      }
    }
    &:nth-child(even) {
      background: #ffffff;
    }
  }

  .nz-table-hide-scrollbar::-webkit-scrollbar-thumb {
    background-color: transparent;
  }

  .ant-divider-horizontal {
    margin: 14px 0;
  }

  .ant-drawer-content {
    box-shadow: 0px 2px 4px 4px rgba(141, 141, 141, 0.5);
    border-radius: 12px 0px 0px 12px;
  }

  .ant-drawer-body {
    padding: 18px 12px;
  }

  .ant-drawer-header {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18px 12px 0px 12px;
    color: rgba(0, 0, 0, 0.85);
    background: #fff;
    border-bottom: none;
    border-radius: 4px 4px 0 0;
  }

  .ant-drawer-title {
    flex: 1;
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: #262d48;
  }

  .ant-drawer-close {
    width: 36px;
    height: 36px;
    background: #f3f4f8;
    border-radius: 18px;
    font-size: 16px;
    color: #797e89;
  }

  .sure-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    margin-left: 16px;
    button {
      padding: 0px 4px;
      margin: 0px;
    }
  }

  .publish-status-tag {
    padding: 0px 4px;
    font-size: 20px;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin-x: left;
    // font-size: 10px;
    font-weight: 500;
    border-radius: 0px 0px 0px 4px;
    margin-left: 10px;
    white-space: nowrap;
  }

  .tobe-publish-tag {
    color: #f33215;
    background: #fee8e8;
  }

  .published-tag {
    color: #007aff;
    background: #e1f0ff;
  }

  .save-published-tag {
    color: #ff5318;
    background: #ffeee8;
  }
}

.info-item {
  display: flex;
  align-items: center;
  gap: 10px;
  .text-label {
    width: 90px;
    font-size: 14px;
    font-weight: 500;
    color: #707070;
    text-align: right;
  }
  .text-content {
    font-size: 14px;
    font-weight: 500;
    color: #24294e;
  }

  .edit-icon {
    color: #2996ff;
    font-size: 16px;
    cursor: pointer;
    margin-left: 10px;
  }
}

.production_line_title {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 10px;

  .production_line_name {
    padding: 0 4px;
    border-radius: px;
    color: #007aff;
    border: 1px solid #007aff;
  }
}

.rest-tag {
  display: flex;
  align-items: center;
  span {
    display: block;
    color: #138aff;
    background-color: #edf8ff;
    padding: 0 4px;
    border-radius: 4px;
    margin-left: 5px;
  }
}

.total-tr {
  position: sticky;
  bottom: 0;
  z-index: 100;
  td {
    color: #138aff;
    background-color: #deefff;
  }
}
