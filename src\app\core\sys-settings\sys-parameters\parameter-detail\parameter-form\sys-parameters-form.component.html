<form nz-form [formGroup]="paramsForm">
  <ng-container *ngFor="let config of paramsFormConfig">
    <div class="params-section">
      <div class="section-title">{{ translateSuffix + 'sectionTitle.' + config.titleKey | translate }}</div>
      <nz-divider></nz-divider>
      <div class="section-container">
        <ng-container *ngFor="let subConfig of config.children">
          <div
            class="section-sub-container"
            [ngClass]="{ 'text-sub-container': subConfig.type === 'text' }"
            *ngIf="
              subConfig.display &&
              ((subConfig.fieldKey === 'delivery_qty_is_required' ? paramsForm.value.delivery_dimension != 1 : true) ||
                (subConfig.fieldKey === 'material-procurement-type-num-is-required'
                  ? paramsForm.value['material-procurement-type-delivery-dimension'] != 1
                  : true))
            ">
            <div *ngIf="subConfig.titleKey?.length" class="title">
              {{ translateSuffix + 'sectionSubTitle.' + subConfig.titleKey | translate }}
            </div>
            <div class="form-container" *ngIf="subConfig.type !== 'text'">
              <nz-form-item>
                <ng-container *ngIf="subConfig.type === 'input' || subConfig.type === 'input-number' || subConfig.type === 'input-only'">
                  <nz-form-label>
                    <span class="label">{{ translateSuffix + 'input.' + subConfig.inputLabelKey | translate }}</span>
                  </nz-form-label>
                </ng-container>
                <nz-form-control>
                  <nz-container [ngSwitch]="subConfig.type">
                    <nz-container *ngSwitchCase="'input'">
                      <nz-input-group [nzAddOnAfter]="translateSuffix + 'input.' + subConfig.inputSuffixKey | translate">
                        <input nz-input flcInputTrim [formControlName]="subConfig.fieldKey" />
                      </nz-input-group>
                    </nz-container>

                    <nz-container *ngSwitchCase="'input-number'">
                      <nz-input-number-group [nzAddOnAfter]="translateSuffix + 'input.' + subConfig.inputSuffixKey | translate">
                        <nz-input-number
                          nz-input
                          flcInputTrim
                          [formControlName]="subConfig.fieldKey"
                          [nzMax]="subConfig.max"
                          [nzMin]="subConfig.min"
                          [nzPrecision]="subConfig.precision"></nz-input-number>
                      </nz-input-number-group>
                    </nz-container>

                    <ng-container *ngSwitchCase="'input-only'">
                      <input nz-input flcInputTrim [formControlName]="subConfig.fieldKey" [maxLength]="subConfig.maxLength" />
                    </ng-container>

                    <nz-container *ngSwitchCase="'simple-radio-group'">
                      <nz-radio-group [formControlName]="subConfig.fieldKey">
                        <label nz-radio [nzValue]="'1'">{{ translateSuffix + 'radio.' + subConfig.oneItem | translate }}</label>
                        <label nz-radio [nzValue]="'0'">{{ translateSuffix + 'radio.' + subConfig.twoItem | translate }}</label>
                      </nz-radio-group>
                    </nz-container>
                    <nz-container *ngSwitchCase="'valueLabel-radio-group'">
                      <nz-radio-group [formControlName]="subConfig.fieldKey">
                        <label nz-radio [nzValue]="item.value" *ngFor="let item of subConfig.options">{{
                          translateSuffix + 'options.' + item.label | translate
                        }}</label>
                      </nz-radio-group>
                    </nz-container>
                    <nz-container *ngSwitchCase="'local-multiple-select'">
                      <nz-select [formControlName]="subConfig.fieldKey" nzMode="multiple">
                        <nz-option
                          *ngFor="let option of subConfig.options"
                          [nzLabel]="translateSuffix + 'options.' + option.label | translate"
                          [nzValue]="option.value"></nz-option>
                      </nz-select>
                    </nz-container>
                    <nz-container *ngSwitchCase="'template'">
                      <ng-container *ngTemplateOutlet="{checkPriceTemplate}[subConfig.template]"></ng-container>
                      <ng-template #placeholder> </ng-template>
                      <ng-template #checkPriceTemplate>
                        <nz-radio-group [formControlName]="subConfig.fieldKey" (ngModelChange)="priceEvaluationChanged($event)">
                          <label nz-radio [nzValue]="'1'">{{ translateSuffix + 'options.' + '一个款式只自动生成一次' | translate }}</label>
                          <label nz-radio [nzValue]="'2'">{{ translateSuffix + 'options.' + '每个打样单均自动生成' | translate }}</label>
                          <label nz-radio [nzValue]="'3'">
                            {{ translateSuffix + 'options.' + '根据样板类型生成' | translate }}
                            <nz-select
                              style="width: 160px"
                              nzSize="small"
                              [compareWith]="compareFn"
                              [formControlName]="'sample_type_auto_gen_rule'">
                              <nz-option [nzValue]="item.value" [nzLabel]="item.label" *ngFor="let item of chekcedPriceTemplateOptions">
                              </nz-option>
                            </nz-select>
                          </label>
                        </nz-radio-group>
                      </ng-template>
                    </nz-container>
                  </nz-container>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
        </ng-container>
      </div>
    </div>
  </ng-container>
</form>
