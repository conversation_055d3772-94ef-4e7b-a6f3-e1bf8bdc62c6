$border-radius: 4px; // 外层边框的圆角
$border-color: var(--nz-table-border-color); // 外层边框的颜色
$header-bg-color: #f5f7fa; // 表头背景色
$body-text-color: #222b3c; // 表格内容文字颜色
$body-text-inactive-color: #97999c; // 表格内容失效文字颜色
$body-line-even-bgcolor: #fafbfd; // 表格内容偶数行背景色
$body-line-hover-bgcolor: var(--nz-table-row-hover-bg); // 表格内容鼠标悬停背景色

.ant-table-thead > tr > th {
  text-align: center;
  height: 32px;
  min-height: 32px;
  font-size: 14px;
  font-weight: 500;
  color: #54607c;
  line-height: 20px;
}

.ant-table-tbody > tr > td {
  text-align: center;
  height: 42px;
  max-height: 42px;
  font-size: 14px;
  font-weight: 500;
  color: $body-text-color;
}

.ant-table-body::before {
  left: 0;
  content: '';
  background: $border-color;
  height: 1px;
  width: calc(100% - 9px);
  bottom: 0;
  position: absolute;
  z-index: 10;
}

// 表格下方分页器样式
.ant-table-pagination.ant-pagination {
  margin: 8px 0 0 0;
}

// 表格内选中数据的底色
.selected-row td {
  background-color: $body-line-hover-bgcolor !important;
}

// 表格行内容inactive状态字体颜色
.inactive-row td {
  color: $body-text-inactive-color !important;
}

// 行变色 start
// 滚动的表格因为最开始多了一行所以使用奇数列
.zebra-striped-table nz-table-inner-scroll tbody.ant-table-tbody > tr.ant-table-row:nth-child(odd) {
  background-color: $body-line-even-bgcolor;

  .ant-table-cell-fix-left,
  .ant-table-cell-fix-right {
    position: -webkit-sticky !important;
    position: sticky !important;
    z-index: 2;
    background: $body-line-even-bgcolor;
  }
  &:hover {
    .ant-table-cell-fix-left,
    .ant-table-cell-fix-right {
      background: $body-line-hover-bgcolor;
    }
  }
}

.zebra-striped-table nz-table-inner-default tbody.ant-table-tbody > tr.ant-table-row:nth-child(even) {
  background-color: $body-line-even-bgcolor;

  .ant-table-cell-fix-left,
  .ant-table-cell-fix-right {
    position: -webkit-sticky !important;
    position: sticky !important;
    z-index: 2;
    background: $body-line-even-bgcolor;
  }
}

// 行变色 end

.setting-btn {
  color: #54607c;

  &:hover {
    color: #4d96ff;
  }
}

/* ====== 表格内滚动条样式 ===== */
// 固定表头的滚动
.ant-table-body::-webkit-scrollbar-track:horizontal {
  border-top: 1px solid $border-color;
}
.ant-table-bordered .ant-table-body::-webkit-scrollbar-track:horizontal {
  border-right: 1px solid $border-color;
}

// 不固定表头的滚动
.ant-table-bordered .ant-table-content::-webkit-scrollbar-track:horizontal {
  border-bottom: 1px solid $border-color;
  border-right: 1px solid $border-color;
}
/* ========================== */

.nz-table-hide-scrollbar,
.ant-table.ant-table-small .nz-table-hide-scrollbar {
  scrollbar-color: unset;
}
