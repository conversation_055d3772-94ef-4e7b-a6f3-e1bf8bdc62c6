import { NzDividerModule } from 'ng-zorro-antd/divider';
import { ComponentsModule } from 'src/app/components/components.module';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzTreeModule } from 'ng-zorro-antd/tree';
import { NzInputModule } from 'ng-zorro-antd/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzResizableModule } from 'ng-zorro-antd/resizable';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { NzIconModule, NzIconService } from 'ng-zorro-antd/icon';
import { HttpClient } from '@angular/common/http';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { FlUiAngularModule } from 'fl-ui-angular';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzDrawerModule, NzDrawerService } from 'ng-zorro-antd/drawer';
import { PipesModule } from 'src/app/pipes/pipes.module';
import { NzPipesModule } from 'ng-zorro-antd/pipes';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { UserManagerListComponent } from './user-manager-list/user-manager-list.component'; // 操作用户列表
import { UserRoutingModule } from './user-routing.module';
import { UserDetailComponent } from './detail/user-detail.component'; // 用户详情
import { DirectivesModule } from 'src/app/directive/directives.module';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import { FlcComponentsModule } from 'fl-common-lib';

@NgModule({
  declarations: [UserManagerListComponent, UserDetailComponent],
  imports: [
    UserRoutingModule,
    CommonModule,
    NzTreeModule,
    NzInputModule,
    FlcComponentsModule,
    FormsModule,
    NzResizableModule,
    DirectivesModule,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new TranslateHttpLoader(http, './assets/i18n/settings/user/', '.json');
        },
        deps: [HttpClient],
      },
    }),
    NzIconModule,
    NzButtonModule,
    FlUiAngularModule,
    NzPopconfirmModule,
    NzFormModule,
    NzSelectModule,
    NzTableModule,
    NzDrawerModule,
    ReactiveFormsModule,
    NzPipesModule,
    PipesModule,
    NzDatePickerModule,
    ComponentsModule,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new MultiTranslateHttpLoader(http, [
            { prefix: './assets/i18n/user/', suffix: '.json' },
            { prefix: './assets/i18n/', suffix: '.json' },
          ]);
        },
        deps: [HttpClient],
      },
    }),
    NzSwitchModule,
    NzDividerModule,
    DragDropModule,
    NzSpinModule,
    NzToolTipModule,
    NzModalModule,
  ],
  providers: [NzDrawerService, NzMessageService, NzNotificationService, NzModalService],
})
export class UserModule {
  constructor(public translateService: TranslateService, private iconService: NzIconService) {
    // 切换lang必须刷新页面，模块级别i18n文件才能成功加载
    const dl = translateService.defaultLang;
    translateService.defaultLang = '';
    translateService.setDefaultLang(dl);

    this.iconService.fetchFromIconfont({
      scriptUrl: '//at.alicdn.com/t/font_2782026_973nd2nll2.js',
    });
  }
}
