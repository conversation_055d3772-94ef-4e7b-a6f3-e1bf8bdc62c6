import { Component, OnInit, Input } from '@angular/core';
import { OrderSecProcessOutsourcingService } from '../../sec-process-outsourcing.service';
import { SecProcessDetailInfoInterface, ExtraProcessInfoInterface } from '../../model/sec-process-outsourcing.interface';
import { EditModeEnum } from '../../model/sec-process-outsourcing.enum';
import { OrderOutsourcingService } from '../../../components/order-outsourcing.service';

@Component({
  selector: 'sec-process-banner',
  templateUrl: './sec-process-banner.component.html',
  styleUrls: ['./sec-process-banner.component.scss'],
})
export class SecProcessBannerComponent implements OnInit {
  @Input() detailForm: any;
  @Input() set extra_process_info(val: Array<ExtraProcessInfoInterface>) {
    if (val) {
      this._extra_process_info = val;
      this.activeIndex = 0;
      this.setPartInfo();
    }
  } // 二次工艺信息
  @Input() editMode?: string;
  @Input() paramInfo: any;
  activeIndex = 0;

  editModeEnum = EditModeEnum;

  searchOptionFetchUrl = this._service.partOptions;

  partInfo = ''; // 部位信息

  partSelectDefaultOptions = []; // 部位下拉默认展示

  _extra_process_info: Array<ExtraProcessInfoInterface> = [];

  _detailInfo?: SecProcessDetailInfoInterface;

  constructor(private _service: OrderSecProcessOutsourcingService, private orderOutsourcingService: OrderOutsourcingService) {}

  ngOnInit() {}

  setPartInfo() {
    const readPartList: any = [];
    const partList: any = [];
    const partSelectDefaultOptions: any = [];
    this._extra_process_info[this.activeIndex]?.position_list.forEach((item) => {
      readPartList.push(item.position_name);
    });
    this.partInfo = readPartList.join('、');
    this._extra_process_info[this.activeIndex]?.position_list.forEach((item) => {
      partSelectDefaultOptions.push({ value: item.position_id, label: item.position_name, hide: false });
      partList.push(item.position_id);
    });
    this.detailForm.get('position_list').reset();
    this.detailForm.get('position_list').setValue(partList);
    this.orderOutsourcingService.nowSelect_extra_process_info = this._extra_process_info[this.activeIndex];
    this.orderOutsourcingService._surplusPos = [];
    this.orderOutsourcingService._surplusLines = [];
    this.orderOutsourcingService.secProcessEventEmitter.emit({
      onchange: 'secProcessChange',
    });
    this.partSelectDefaultOptions = partSelectDefaultOptions;
    this.detailForm.get('extra_process_info').setValue(this._extra_process_info);
  }

  handleBtn(index: number) {
    this.activeIndex = index;
    this.setPartInfo();
  }

  handleChangeValueIo(e: any) {
    const position_list: Array<{ position_id: number; position_name: string }> = [];
    e.selectLines.forEach((item: any) => {
      position_list.push({ position_id: item.value, position_name: item.label });
    });
    this._extra_process_info[this.activeIndex].position_list = position_list;
    this.detailForm.get('extra_process_info').setValue(this._extra_process_info);
  }
}
