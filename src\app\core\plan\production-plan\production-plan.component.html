<flc-search-container #searchContainer class="plan-search-box" [showBtnContainer]="false" *ngIf="!collapse">
  <div>
    {{ 'plan.productionPlan.加工厂' | translate }}：
    <nz-select
      [(ngModel)]="_service.searchData.factory_id"
      (ngModelChange)="onSearch()"
      [nzPlaceHolder]="'placeholder.select' | translate"
      [nzDropdownMatchSelectWidth]="false"
      nzDropdownClassName="plan-select-option"
      nzAllowClear
      nzShowSearch
      [nzBackdrop]="true">
      <ng-container *ngFor="let item of searchOption.factoryOptions">
        <nz-option [nzValue]="item.factory_id" [nzLabel]="item.factory_name"></nz-option>
      </ng-container>
    </nz-select>
  </div>
  <div>
    {{ 'plan.productionPlan.大货单号' | translate }}：
    <nz-select
      [(ngModel)]="_service.searchData.order_code"
      (ngModelChange)="associatedChange()"
      [nzPlaceHolder]="'placeholder.select' | translate"
      [nzDropdownMatchSelectWidth]="false"
      nzDropdownClassName="plan-select-option"
      nzAllowClear
      nzShowSearch
      [nzBackdrop]="true">
      <ng-container *ngFor="let item of searchOption.codeOptions">
        <nz-option [nzValue]="item" [nzLabel]="item"></nz-option>
      </ng-container>
    </nz-select>
  </div>
  <div *ngIf="graphOptions?.item_dimension === 'po'">
    {{ 'plan.productionPlan.交付单号' | translate }}：
    <nz-select
      [(ngModel)]="_service.searchData.po_code"
      (ngModelChange)="associatedChange()"
      [nzPlaceHolder]="'placeholder.select' | translate"
      [nzDropdownMatchSelectWidth]="false"
      nzDropdownClassName="plan-select-option"
      nzAllowClear
      nzShowSearch
      [nzBackdrop]="true">
      <ng-container *ngFor="let item of searchOption.poOptions">
        <nz-option [nzValue]="item" [nzLabel]="item"></nz-option>
      </ng-container>
    </nz-select>
  </div>
  <div>
    {{ 'plan.productionPlan.款式编码' | translate }}：
    <nz-select
      [(ngModel)]="_service.searchData.style_code"
      (ngModelChange)="associatedChange()"
      [nzPlaceHolder]="'placeholder.select' | translate"
      [nzDropdownMatchSelectWidth]="false"
      nzDropdownClassName="plan-select-option"
      nzAllowClear
      nzShowSearch
      [nzBackdrop]="true">
      <ng-container *ngFor="let item of searchOption.materialCodeOptions">
        <nz-option [nzValue]="item" [nzLabel]="item"></nz-option>
      </ng-container>
    </nz-select>
  </div>
  <div>
    {{ 'plan.productionPlan.时间' | translate }}：
    <nz-range-picker [nzRanges]="ranges" [(ngModel)]="_service.searchData.date" (ngModelChange)="timeChange()" [nzAllowClear]="false">
    </nz-range-picker>
  </div>
</flc-search-container>
<div class="production-plan-back" [ngStyle]="{ height: bodyHeight + 'px' }">
  <div style="height: 100%">
    <flc-title-bar
      #tableTitleBtn
      style="flex-shrink: 0"
      [title]="title"
      [action]="action"
      (reset)="reset()"
      (expandChange)="expandChange()">
      <ng-template #title>
        <div class="left-title-bar">
          <div class="title">{{ 'plan.productionPlan.生产计划' | translate }}</div>
          <div class="title-divider"></div>
          <div>
            {{ 'plan.productionPlan.示例' | translate }}：
            <i nz-icon style="font-size: 16px; margin-right: 4px">
              <svg>
                <path
                  d="M32 32m360 0l240 0q360 0 360 360l0 240q0 360-360 360l-240 0q-360 0-360-360l0-240q0-360 360-360Z"
                  fill="#FFFFFF"
                  p-id="8186"></path>
                <path
                  d="M92 92m300 0l240 0q300 0 300 300l0 240q0 300-300 300l-240 0q-300 0-300-300l0-240q0-300 300-300Z"
                  fill="#FF4141"
                  p-id="8187"></path>
                <path
                  d="M563.6 262.28l-266.16 276.6c-7.44 7.98-9.3 15.6-5.58 22.74 5.58 10.74 17.58 12.72 24.66 12.72h125.58l-68.94 174.72c-2.76 9.24-1.14 16.32 4.92 21.24 9 7.32 25.02 13.86 38.16 2.46 8.76-7.56 114.84-106.02 318.18-295.38 6.48-10.26 7.32-18.72 2.4-25.26-4.8-6.6-14.4-9.48-28.56-8.76L576.92 442.4l36-156c0.84-15.6-3.9-25.62-14.4-30.12-10.44-4.44-22.08-2.4-34.86 6z"
                  fill="#FFFFFF"
                  p-id="8188"></path>
              </svg>
            </i>
            {{ 'plan.productionPlan.已逾期' | translate }}
          </div>
          <div>
            <div class="scheduled-icon"></div>
            {{ 'plan.productionPlan.已排程' | translate }}
          </div>
        </div>
      </ng-template>

      <ng-template #action>
        <button
          nz-button
          flToggleButton
          nzShape="round"
          [ngClass]="{
            'switch-io-btn': _service.getProOrderDimension() === 'po',
            'switch-mode-btn': _service.getProOrderDimension() === 'io'
          }"
          style="margin-right: 8px"
          (toggleActiveChange)="switchPoMode()"
          [toggleActive]="getToggleActive()"
          nz-tooltip
          [nzTooltipTitle]="'plan.productionPlan.' + getToggleTooltipTitle() | translate">
          <i nz-icon nzType="swap" nzTheme="outline"></i>
          {{ (_service.getProOrderDimension() === 'po' ? 'plan.productionPlan.订单维度' : 'plan.productionPlan.交付单维度') | translate }}
        </button>
        <button
          nz-button
          *ngIf="_service.btnArr.includes('bulk:production-plan-setting')"
          style="margin-right: 12px"
          nzType="default"
          nzShape="round"
          nz-tooltip
          [nzTooltipTitle]="'plan.productionPlan.colorSettingTooltip' | translate"
          (click)="settingColor()">
          <i nz-icon>
            <svg>
              <path
                d="M473.96176455 935.58235273C306.85294092 935.58235273 108.3235291 808.55 69.59705908 573.17352911c-28.53529453-173.38235273 30.75882364-315.15882364 171.50294092-410.02941182C316.96470547 111.9764709 398.81176455 86.06176455 484.39117637 86.06176455c261.21176455 0 475.49117636 241.54411729 477 399.1764709 0.60882364 64.32352911-19.90588271 120.78529453-57.70588184 158.98235273-33.14117636 33.45882363-76.71176455 51.08823545-126 51.08823545a219.46764727 219.46764727 0 0 1-50.50588271-6.06176455c-37.21764727-8.73529453-64.77352911-4.65882363-78.88235274 12.15-11.32941182 13.5-12.6264709 32.37352911-11.32941181 39.28235274 10.45588271 61.06764727 1.69411729 109.66764727-26.1 144.29117636-32.05588271 40.07647089-78.53823545 46.16470547-88.38529454 47.03823545a327.97058818 327.97058818 0 0 1-48.52058818 3.5735291z m6.3264709-796.71176455c-74.03823545 0-145.24411729 22.65882363-211.57941182 67.44705909C145.93823545 289.06470547 96.41176455 408.10294092 121.4 560.1235291c33.67058818 204.59117636 204.67058818 315 348.51176455 315.00000001 13.92352911 0 27.74117637-0.97941182 41.13529453-2.99117638l2.75294092-0.26470546c0.05294092-0.05294092 30.25588271-2.38235273 49.26176455-26.62941182 16.96764727-21.52058818 21.65294092-54.74117637 14.00294092-98.78823545-3.97058818-23.58529453 1.8-59.02941182 23.8764709-85.31470547 14.10882364-16.75588271 40.73823545-36.71470547 87.22058818-36.71470635 14.74411729 0 30.81176455 2.01176455 47.83235274 6.06176455 48.70588271 11.38235273 93.17647089 0.60882364 123.3-29.88529365 27.13235273-27.39705908 41.8764709-69.51176455 41.4-118.64117637-1.24411729-132.3264709-194.13529453-343.08529453-420.40588184-343.08529453z"></path>
              <path
                d="M277.28529453 408.42058818m-52.94117724 0a52.94117637 52.94117637 0 1 0 105.8823536 0 52.94117637 52.94117637 0 1 0-105.8823536 0Z"></path>
              <path
                d="M402.30588271 305.74117637m-52.94117724 0a52.94117637 52.94117637 0 1 0 105.88235361 0 52.94117637 52.94117637 0 1 0-105.88235361 0Z"></path>
              <path
                d="M562.29411729 305.74117637m-52.94117637 0a52.94117637 52.94117637 0 1 0 105.88235361 0 52.94117637 52.94117637 0 1 0-105.88235361 0Z"></path>
              <path
                d="M708.2 368.26470547m-52.94117637 0a52.94117637 52.94117637 0 1 0 105.88235274 0 52.94117637 52.94117637 0 1 0-105.88235274 0Z"></path>
              <path
                d="M277.28529453 573.30588271m-52.94117724 0a52.94117637 52.94117637 0 1 0 105.8823536 0 52.94117637 52.94117637 0 1 0-105.8823536 0Z"></path>
            </svg>
          </i>
          {{ 'plan.productionPlan.颜色设置' | translate }}
        </button>
      </ng-template>
    </flc-title-bar>
    <app-plan-gantt style="flex-grow: 1" [dates]="dates" [graphOptions]="graphOptions"></app-plan-gantt>
  </div>
</div>

<app-color-setting (closeClick)="getNewColor($event)"></app-color-setting>
