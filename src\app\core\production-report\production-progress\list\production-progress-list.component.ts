import { Component, <PERSON>ementR<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { NzModalService } from 'ng-zorro-antd/modal';
import { isNotNil } from 'ng-zorro-antd/core/util';
import { endOfDay, format, startOfDay } from 'date-fns';
import { FlcTableHelperService, resizable } from 'fl-common-lib';
import { Subject, Subscription } from 'rxjs';
import { debounceTime, finalize, startWith } from 'rxjs/operators';

import { ProductStatusOptions, TransportStatusOptions, renderModalTitle } from '../models/production-progress.config';
import { ProgressDataType } from '../models/production-progress.enum';
import { ProductionProgressService } from '../production-progress.service';
import { IDetailParams, IListParams, ProductionHeaderType } from '../models/production-progress-interface';
import { ColorSizeDetailComponent } from '../components/color-size-detail/color-size-detail.component';
import { DimensionRange, ReportRange } from '../../production-report.enum';
import { getTableHeaders } from './production-progress-list.config';
import { TranslateService } from '@ngx-translate/core';
import { ProblemDefectiveDetailComponent } from '../components/problem-defective-detail/problem-defective-detail.component';

@Component({
  selector: 'app-production-progress-list',
  templateUrl: './production-progress-list.component.html',
  styleUrls: ['./production-progress-list.component.scss'],
})
@resizable()
export class ProductionProgressListComponent implements OnInit, OnDestroy {
  @ViewChild('searchBar') searchBarRef!: ElementRef;
  searchOptionFetchUrl = this._service.searchOptionUrl;
  dimensionRange = DimensionRange;
  currentDimension = DimensionRange.io;
  selectedTab = ReportRange.daily;
  searchData: any = {
    io_code: null,
    factory_code: null,
    customer_style: null,
    po_code: null,
    due_time: null,
    biz_date: null,
    status: null,
  };
  tableConfig: any = {
    dataList: [],
    count: 0,
    height: 200,
    loading: false,
    pageSize: 20,
    pageIndex: 1,
    orderBy: [],
  };
  searchList: Array<{ label: string; key: any; type: string; visible: boolean; options?: any }> = [
    { label: '日期', key: 'biz_date', type: 'dateRange', visible: true },
    { label: '大货单号', key: 'io_code', type: 'select', visible: true },
    { label: '款式编码', key: 'customer_style', type: 'select', visible: true },
    { label: '交付单', key: 'po_code', type: 'select', visible: false },
    { label: '加工厂', key: 'factory_name', type: 'select', visible: true },
    { label: '生产状态', key: 'status', type: 'local-select', visible: true, options: ProductStatusOptions },
    { label: '交期', key: 'due_time', type: 'dateRange', visible: true },
  ];
  _searchDataInitState: any = {};
  sumData: any = {}; // 合计
  isShowSearchContainer = true;
  listSubject$ = new Subject<boolean>();
  listSubscription!: Subscription;
  translateSubscription!: Subscription;
  immediate = false; // 是否立即执行
  dueSort: any = null;

  tableName = '';
  tableVersion = '1.1.0';
  renderHeaders: ProductionHeaderType[] = [];

  constructor(
    private _modal: NzModalService,
    private _service: ProductionProgressService,
    private _translate: TranslateService,
    private _tableHelper: FlcTableHelperService
  ) {}

  ngOnInit(): void {
    this._searchDataInitState = JSON.parse(JSON.stringify(this.searchData));
    this._onRenderSearchList();
    this.listSubscription = this.listSubject$
      .pipe(
        startWith(true),
        debounceTime(500),
        finalize(() => {
          this.tableConfig.loading = false;
        })
      )
      .subscribe((res: boolean) => {
        this._getList(res);
      });
    (this as any).addResizePageListener();
    this.translateSubscription = this._service.translateEventEmitter.subscribe(() => {
      this.resizePage();
    });
    this.getRenderTableHeader();
  }

  ngAfterViewInit(): void {
    this.resizePage();
  }

  ngOnDestroy() {
    (this as any).removeResizePageListener();
    this.listSubscription?.unsubscribe();
    this.translateSubscription?.unsubscribe();
  }

  resizePage() {
    setTimeout(() => {
      const searchBarHeight = this.searchBarRef?.nativeElement?.offsetHeight ?? 0;
      let _height = window.innerHeight - searchBarHeight - 250;
      if (_height < 200) {
        _height = 200;
      }

      this.tableConfig = { ...this.tableConfig, height: _height };
    }, 0);
  }

  // 重置， tab和维度不变
  onReset() {
    this.searchData = JSON.parse(JSON.stringify(this._searchDataInitState));
    this.tableConfig = {
      ...this.tableConfig,
      pageIndx: 1,
      pageSize: 20,
      orderBy: [],
    };
    this.dueSort = null;
    this._getList(true);
  }

  // 下拉选择搜索
  onSearch(isReset: boolean) {
    this.listSubject$.next(isReset);
  }

  /**
   * tab切换
   * 1、重置筛选项、页码、排序等
   * 2、维度不变，保持上次用户切换行为
   */
  onTabChange() {
    this.immediate = true;
    this.onReset();
    this._onRenderSearchList();
    this.resizePage();
    this.getRenderTableHeader();
  }

  onToggle() {
    this.isShowSearchContainer = !this.isShowSearchContainer;
    this.resizePage();
  }

  // 切换维度， 只重置页码
  onDimensionChange() {
    this.searchData = { ...this.searchData, po_code: null, status: null }; // 清除交付单, 由于状态区分, 清空状态值
    this._onRenderSearchList();
    this._getList(true);
    this.resizePage();
    this.getRenderTableHeader();
  }

  onIndexChanges(e: number) {
    this.tableConfig = { ...this.tableConfig, pageIndex: e };
    this._getList(false);
  }

  onSortChange(e: any, key: string) {
    const orderBy = e ? [key + ' ' + e] : [];
    this.tableConfig = { ...this.tableConfig, orderBy };
    this._getList(false);
  }

  onSizeChanges(e: number) {
    this.tableConfig = { ...this.tableConfig, pageSize: e };
    this._getList(true);
  }

  // 查看明细
  onOpenModal(key: string, item: any, po: any, sub: any) {
    const title = renderModalTitle(this.selectedTab, key);
    type KeyType = keyof typeof ProgressDataType;
    const payload: IDetailParams = {
      io_code: item?.io_code,
      po_code: this.currentDimension == DimensionRange.po ? po.po_code : '',
      factory_code: sub?.factory_code,
      factory_name: sub?.factory_name,
      biz_date: this.selectedTab == ReportRange.daily ? item?.biz_date : '',
      data_type: ProgressDataType[key as KeyType],
      dimension: this.currentDimension,
    };
    this._modal.create({
      nzTitle: title,
      nzContent: ColorSizeDetailComponent,
      nzComponentParams: {
        tab: this.selectedTab,
        dimension: this.currentDimension,
        typeName: (key ?? '').includes('final_')
          ? '终检'
          : (key ?? '').includes('qualified_qty') || (key ?? '').includes('defective_qty')
          ? '质检'
          : '',
        payload,
      },
      nzBodyStyle: { padding: '0px 12px 14px' },
      nzFooter: null,
      nzWidth: '800px',
    });
  }

  // 查看不良率明细
  onOpenDefectiveModal(key: string, item: any, po: any, sub: any) {
    const title = renderModalTitle(this.selectedTab, key);
    type KeyType = keyof typeof ProgressDataType;
    const payload: IDetailParams = {
      io_code: item?.io_code,
      po_code: this.currentDimension == DimensionRange.po ? po.po_code : '',
      factory_code: sub?.factory_code,
      factory_name: sub?.factory_name,
      biz_date: this.selectedTab == ReportRange.daily ? item?.biz_date : '',
      data_type: ProgressDataType[key as KeyType],
      dimension: this.currentDimension,
    };
    this._modal.create({
      nzTitle: title,
      nzContent: ProblemDefectiveDetailComponent,
      nzComponentParams: {
        tab: this.selectedTab,
        dimension: this.currentDimension,
        typeName: (key ?? '').includes('final_')
          ? '终检'
          : (key ?? '').includes('qualified_qty') || (key ?? '').includes('defective_qty')
          ? '质检'
          : '',
        payload,
      },
      nzBodyStyle: { padding: '0px 12px 14px' },
      nzFooter: null,
      nzWidth: '800px',
    });
  }

  onSetStatus(item: number) {
    const statusOptions = this.currentDimension === DimensionRange.io ? ProductStatusOptions : TransportStatusOptions;
    return statusOptions.filter((status) => status?.value == item)[0]?.label;
  }

  // 筛选项
  private _onRenderSearchList() {
    const opt = this.searchList.find((item) => item.key === 'biz_date');
    if (opt) opt.visible = this.selectedTab === ReportRange.daily;
    const poOpt = this.searchList.find((item) => item.key === 'po_code');
    if (poOpt) poOpt.visible = this.currentDimension === DimensionRange.po;
    const statusSearchItem = this.searchList.find((item) => item.key === 'status');
    if (statusSearchItem) {
      statusSearchItem.label = this.currentDimension === DimensionRange.io ? '生产状态' : '走货状态';
      statusSearchItem.options = this.currentDimension === DimensionRange.io ? ProductStatusOptions : TransportStatusOptions;
    }
  }

  private _getList(reset = false) {
    let success = false;
    this.immediate = false;
    this.tableConfig = {
      ...this.tableConfig,
      loading: true,
      pageIndex: reset ? 1 : this.tableConfig.pageIndex,
    };
    const payload: IListParams = {
      page: this.tableConfig?.pageIndex,
      limit: this.tableConfig?.pageSize,
      order_by: this.tableConfig?.orderBy,
      where: this._handleWhere(),
      dimension: this.currentDimension,
      daily: this.selectedTab,
    };
    this._service
      .getProgressList(payload)
      .pipe(
        finalize(() => {
          this.tableConfig = { ...this.tableConfig, loading: false };
          if (!success) {
            this.tableConfig = { ...this.tableConfig, dataList: [], count: 0 };
            this.sumData = {};
          }
        })
      )
      .subscribe((res: any) => {
        if (res.code === 200) {
          success = true;
          const dataList = res.data?.list;

          dataList.forEach((item: any) => {
            item.len = item.po_list.reduce((prev: any, curr: any) => {
              return prev + curr.sub_list?.length;
            }, 0);
            item.po_list.forEach((po: any) => {
              po.len = po.sub_list?.length;
              po.customer = item.customer;
              // 分隔交期
              po.due_time_list = po.due_time ? po.due_time.split('、') : [];
              const due_time_len = po.due_time_list?.length;
              po.due_time_arr = po.due_time_list.slice(0, po.len * 2);
              if (due_time_len > po.due_time_arr?.length) {
                po.due_time_overflow = true;
              }
              po.sub_list.forEach((sub_item: any) => {
                const status_name = this.onSetStatus(sub_item.status);
                sub_item.status_name = !status_name ? null : this._translate.instant('productionProgressOption.' + status_name);
              });
            });
          });
          this.tableConfig = { ...this.tableConfig, dataList, count: res.data.count, loading: false };
          if (res.data?.aggregation) {
            const { po_list } = res.data.aggregation;
            this.sumData = { ...po_list[0], ...po_list[0]?.sub_list[0] };
          }
        }
      });
  }

  private _handleWhere() {
    const where: any = [];
    Object.entries(this.searchData).forEach((item: any) => {
      if (isNotNil(item[1])) {
        if (['due_time', 'biz_date'].includes(item[0])) {
          if (Array.isArray(item[1]) && !!item[1].length) {
            const startTime = format(startOfDay(item[1][0]), 'T');
            const endTime = format(endOfDay(item[1][1]), 'T');
            where.push({ column: item[0], op: 'between', value: `${startTime},${endTime}` });
          }
        } else {
          where.push({ column: item[0], op: '=', value: item[1]?.toString() });
        }
      }
    });
    return where;
  }

  getRenderTableHeader() {
    this.tableName = `productionProgressTab${this.selectedTab}${this.currentDimension}`;
    this.renderHeaders = this._tableHelper.getFlcTableHeaderConfig(
      getTableHeaders(this.selectedTab, this.currentDimension),
      this.tableVersion,
      this.tableName
    );
  }

  onChangeHeader(event: MouseEvent): void {
    const shadow = JSON.parse(JSON.stringify(this.renderHeaders));
    for (const item of shadow) {
      this._translate
        .get('productionProgress.' + item.label)
        .subscribe((res: string) => {
          item.label = res;
        })
        .unsubscribe();
    }
    console.log(shadow);
    this._tableHelper.openTableHeaderMidifyDialog<any>(shadow, event.target as HTMLElement).subscribe((res) => {
      if (res) {
        console.log(res);
        for (const item of res) {
          const _defaultHeaders = getTableHeaders(this.selectedTab, this.currentDimension);
          item.label = _defaultHeaders.find((i) => i.key === item.key)?.label ?? '';
        }
        this.renderHeaders = res;
        this._tableHelper.saveFlcTableHeaderConfig(this.renderHeaders, this.tableVersion, this.tableName);
      }
    });
  }
}
