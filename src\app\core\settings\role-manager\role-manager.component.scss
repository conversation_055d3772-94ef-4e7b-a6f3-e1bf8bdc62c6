:host ::ng-deep .ant-divider-vertical {
  border-left: 1px solid #d4d7dc;
}

.wrap {
  height: calc(100vh - 48px - 12px - 12px);
  row-gap: 8px;
  display: flex;
  flex-direction: column;
  overflow-y: clip;
}

.board {
  flex-grow: 1;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  border-radius: 4px 4px 0 0;
  overflow: hidden;

  > .titleBar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 7px 12px;
    border-bottom: #d4d7dc 1px solid;

    > .title {
      font-size: 18px;
      font-weight: 500;
      color: #000000;
      line-height: 25px;
    }
  }

  > .content {
    flex-grow: 1;
    column-gap: 4px;
    display: grid;
    grid-template-columns: auto 1fr;
    overflow: hidden;
  }
}

.left {
  position: relative;
  box-shadow: 4px 0px 5px 0px rgb(0 0 0 / 10%);
  width: 180px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  overflow-y: auto;
  border-right: rgba(0, 0, 0, 0.1) 1px solid;

  .topLeftButton {
    position: absolute;
    top: 0;
    width: 24px;
    height: 24px;
    padding: 4px 8px;
    border-radius: 0 0 76px 0;
    background: #ffffff;
    box-shadow: 0px 0px 5px 0px rgba(99, 99, 99, 0.1);
    app-sort-btn {
      position: absolute;
      top: 40%;
      left: 15%;
    }
  }

  .roleListEmpty {
    height: 600px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 400;
    color: #84879a;
    line-height: 20px;
  }

  .roleLine {
    font-size: 16px;
    color: #222b3c;
    line-height: 22px;
    padding: 11px 0 11px 22px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &:hover {
      color: #138aff;
      cursor: pointer;
    }

    &.selected {
      color: #138aff;
      background-color: #edf4fb;
    }
    .menu-item {
      display: flex;
      width: 100px;
      flex: 1;
      flex-shrink: 0;
      overflow: hidden;
    }
    .copy {
      flex-shrink: 0;
      margin-left: 2px;
    }
  }
}

.right {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .detailEmpty {
    height: 600px;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 400;
    color: #84879a;
    line-height: 20px;
  }
}

#listview {
  position: relative;
  overflow-y: auto;
  flex-grow: 1;
}

.tabbar {
  padding: 12px;

  > .controlBar {
    display: grid;
    grid-template-columns: 1fr auto;

    > .flagBar {
      display: grid;
      grid-template-columns: 1fr 1fr;
      font-size: 12px;
      font-weight: 400;
      line-height: 20px;
      color: #84879a;

      .leftArrow {
        display: grid;
        grid-template-columns: 1fr auto;
        cursor: pointer;

        div {
          padding: 6px 10px;
          background-color: #f7f7f7;
        }

        &::after {
          content: '';
          display: block;
          width: 0;
          height: 0;
          border-top: 16.5px solid transparent;
          border-bottom: 16.5px solid transparent;
          border-left: 16.5px solid #f7f7f7;
        }
      }

      .rightArrow {
        display: grid;
        grid-template-columns: auto 1fr;
        cursor: pointer;

        div {
          padding: 6px 10px;
          background-color: #f7f7f7;
        }

        &::before {
          content: '';
          display: block;
          width: 0;
          height: 0;
          border-top: 16.5px solid #f7f7f7;
          border-bottom: 16.5px solid #f7f7f7;
          border-left: 16.5px solid transparent;
        }
      }

      .active {
        color: #138aff;

        div {
          background-color: #edf4fb;
        }

        &::after {
          border-left: 16.5px solid #edf4fb;
        }

        &::before {
          border-top: 16.5px solid #edf4fb;
          border-bottom: 16.5px solid #edf4fb;
        }
      }

      span {
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        padding-right: 2px;
      }
    }

    > .buttonGroup {
      button {
        width: 80px;
      }

      width: 200px;
      display: flex;
      column-gap: 8px;
      justify-content: flex-end;
      align-items: center;
    }
  }

  > .menuList {
    padding-top: 12px;

    display: flex;
    justify-content: space-between;
    align-items: center;

    .menu-right {
      display: flex;
      align-items: center;
      button {
        padding: 0;
        width: 78px;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        margin-left: 20px;
      }

      button:not(:hover) {
        color: #138aff;
      }
    }
  }
}

.contentTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.1);

  > .detailInfo {
    display: grid;
    grid-template-rows: 1fr auto;
    row-gap: 2px;

    > .roleTitle {
      font-size: 18px;
      font-weight: 500;
      color: #000000;
      line-height: 25px;
    }

    > .roleSubtitle {
      font-size: 12px;
      font-weight: 400;
      color: #616161;
      line-height: 18px;
    }
  }
}

.searchLine {
  font-size: 14px;
  color: #54607c;

  nz-select {
    min-width: 150px;
  }
}

.addOperation {
  color: #138aff;
}

.deleteOperation {
  color: #ff7070;
}

.editOperation {
  color: #222b3c;
}

.formIcon:hover {
  color: #40a2ff;
}

.deleteIcon:hover {
  color: #fe5d56;
}

.modalContent {
  font-size: 16px;
  font-weight: 500;
  color: #222b3c;
  line-height: 24px;
}
