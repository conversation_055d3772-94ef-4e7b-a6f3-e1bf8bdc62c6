import { Component, Input, OnInit, TemplateRef, EventEmitter, Output } from '@angular/core';
import { TranslatePipe } from '@ngx-translate/core';
@Component({
  selector: 'app-order-outsourcing-factory-container',
  templateUrl: './order-outsourcing-factory-container.component.html',
  styleUrls: ['./order-outsourcing-factory-container.component.scss'],
  providers: [TranslatePipe],
})
export class OrderPlantContainerComponent implements OnInit {
  @Output() addFactoryItem = new EventEmitter();
  @Input() parentGroup!: any;
  @Input() editMode!: 'add' | 'edit' | 'read'; // 当前页面编辑状态
  @Input() leftContainerTpl?: TemplateRef<any>;
  constructor(private translatePipe: TranslatePipe) {}
  ngOnInit() {}
  // 添加一个加工厂
  handleAddPlant() {
    this.addFactoryItem.emit(this.parentGroup.get('factorys')?.length);
  }
}
