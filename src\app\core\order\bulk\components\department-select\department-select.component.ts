import { Component, EventEmitter, forwardRef, Input, OnInit, Output, ViewChild } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { NzTreeSelectComponent } from 'ng-zorro-antd/tree-select';
import { HttpClient } from '@angular/common/http';
import { Observable, Subject, Subscription } from 'rxjs';
interface DepartmentNode {
  key: string;
  title: string;
  isLeaf: boolean;
  type: number; // 1部门 2员工
  selectable: boolean; // 是否可选
  children?: DepartmentNode[];
  disabled: boolean;
}

export interface DepartmentData {
  key: string;
  title: string;
  isLeaf: boolean;
  type: number; // 1部门 2员工
  children?: DepartmentData[];
  user_id: number; // 登录的账号id
  is_relation: boolean; // 是否有关联账号
}

@Component({
  selector: 'flss-department-select',
  templateUrl: './department-select.component.html',
  styleUrls: ['./department-select.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DepartmentSelectComponent),
      multi: true,
    },
  ],
})
export class DepartmentSelectComponent implements OnInit, ControlValueAccessor {
  @ViewChild('treeSelect') treeSelect!: NzTreeSelectComponent;

  @Output() onSelectChange = new EventEmitter();
  @Output() onCheckUpdateValue = new EventEmitter(); //检查更新value值

  @Input() requestInside = false; // 是否内部请求数据
  @Input() canSelectDepartment = true; // 是否可以选择部门
  @Input() canSelectEmployee = true; // 是否可以选择员工
  @Input() selectCurrentUser = false; // 是否选择当前登录账号的员工

  _subject$ = new Subject<string>();
  _subscription?: Subscription;

  /** 实际选项 */
  @Input() set treeOptions(value: DepartmentData[]) {
    const _value = JSON.parse(JSON.stringify(value));
    this.departmentOptions = this.handleTreeOptions(_value) || [];
  }

  /** 默认选项，在处于打开下拉面板才请求数据的场景下使用，目前没有使用 */
  @Input() set defaultOption(value: DepartmentNode[]) {
    this.departmentOptions = value;
  }
  constructor(public _http: HttpClient) {}

  onChange: any = () => {};

  writeValue(value: string): void {
    this.binding = value;
    if (this.departmentOptions?.length && value) this._subject$.next(value);
  }
  registerOnChange(fn: any): void {
    this.onChange = fn;
  }
  registerOnTouched(fn: any): void {}

  binding!: string;
  departmentOptions: DepartmentNode[] = [];

  ngOnInit(): void {
    if (this.requestInside) {
      this.getTreeOptions();
    }
    //检查更新value值（历史value可能修改和删除了）
    this._subscription = this._subject$.subscribe((value) => {
      setTimeout(() => {
        const originNodeValue = this.treeSelect.getTreeNodeByKey(value)?.origin;
        if (originNodeValue && originNodeValue.disabled) {
          this.onCheckUpdateValue.emit(null);
          return;
        }
        this.onCheckUpdateValue.emit(originNodeValue);
      });
      this._subscription?.unsubscribe();
    });
  }

  ngOnDestroy() {
    this._subscription?.unsubscribe;
  }

  private _getOptions(): Observable<any> {
    return this._http.get('/organization/basic_option');
  }

  getTreeOptions() {
    this._getOptions().subscribe((res) => {
      if (res?.code === 200) {
        this.departmentOptions = this.handleTreeOptions(res?.data?.children || []);
        if (this.binding) {
          this._subject$.next(this.binding);
        }
      }
    });
  }

  handleTreeOptions(value: DepartmentData[]): DepartmentNode[] {
    const ret: DepartmentNode[] = [];
    // 找到和当前用户id匹配的key
    const userInfo = localStorage.getItem('userInfo') ?? null;
    const userId = userInfo ? JSON.parse(userInfo).user_id : null;
    if (value.length) {
      value.forEach((node: DepartmentData) => {
        // 部门和员工的key存在重复，需要加上前缀区分
        const key_value = node.type === 1 ? `department-${node.key}` : `employ-${node.key}`;
        if (this.selectCurrentUser && !this.binding && userId === node.user_id) {
          setTimeout(() => {
            this.binding = key_value;
            this.onTreeSelectChange(key_value);
          });
        }
        // 员工只能选择有关联账号的员工
        let selectable = true;
        if (node.type === 2) {
          selectable = this.canSelectEmployee && node.is_relation;
        }
        if (node.type === 1) {
          selectable = this.canSelectDepartment;
        }

        ret.push({
          key: key_value,
          title: node.title,
          isLeaf: node.isLeaf,
          type: node.type,
          selectable: selectable,
          disabled: !selectable && node.isLeaf,
          children: this.handleTreeOptions(node?.children ?? []),
        });
      });
    }
    return ret || [];
  }

  /** 打开下拉面板的时候请求数据，目前没有使用 */
  // onOpenChange(status: boolean) {
  //   if (status) {
  //     this.getTreeOptions();
  //   }
  // }

  onTreeSelectChange(value: string) {
    const originNodeValue = this.treeSelect.getTreeNodeByKey(value)?.origin;
    this.onChange(value);
    this.onSelectChange.emit(originNodeValue);
  }

  onTreeNodeSelect(e: any) {
    if (e.node && !e.node.isLeaf) {
      e.node.isExpanded = !e.node.isExpanded;
    }
  }
}
