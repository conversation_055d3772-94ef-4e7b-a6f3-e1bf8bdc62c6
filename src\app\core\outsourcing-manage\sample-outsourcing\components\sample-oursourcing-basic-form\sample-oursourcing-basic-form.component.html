<form nz-form [formGroup]="sampleBasicForm">
  <div class="sample-basic-container" nz-row nzType="flex">
    <ng-container *ngFor="let item of basicFormConfig">
      <nz-form-item nz-col [nzSpan]="8" class="form-item">
        <nz-form-label class="info-label" [nzRequired]="item.required">{{ translateName + item.label | translate }}</nz-form-label>
        <nz-form-control class="info-control" [flcErrorTip]="item.label" *ngIf="this.isEdit && !item.readOnly">
          <ng-container *ngIf="item.type == 'select'">
            <div style="display: flex; align-items: center; width: 100%">
              <flc-dynamic-search-select
                [dataUrl]="item.url || ''"
                [transData]="{ value: 'value', label: 'label' }"
                [column]="item?.column ?? ''"
                [optAlwaysReload]="false"
                [formControlName]="item.key"
                [defaultValue]="{ label: sampleBasicForm.get(item.labelKey)?.value, value: sampleBasicForm.get(item.key)?.value }"
                (handleSearch)="onSelectSearch($event, item)">
              </flc-dynamic-search-select>
              <span *ngIf="item.key == 'sample_order_id' && sample_sort_num" class="tag">
                {{ 'sampleOutsourcing.detailField.第' | translate }}{{ sample_sort_num
                }}{{ 'sampleOutsourcing.detailField.轮' | translate }}
              </span>
              <button
                *ngIf="item.key === 'factory_id'"
                nz-button
                class="recommend-btn"
                flButton="pretty-primary"
                [nzLoading]="isRecommendLoading"
                (click)="onSelectFactroy()"
                style="padding: 4px 8px">
                {{ isRecommendLoading ? '推荐中…' : '智能推荐' }}
              </button>
            </div>
          </ng-container>

          <ng-container *ngIf="item.type === 'input'">
            <nz-textarea-count [nzMaxCharacterCount]="item.maxLength!" class="inline-count">
              <textarea
                [nzAutosize]="{ minRows: 0, maxRows: 3 }"
                [formControlName]="item.key"
                flcInputTrim
                nz-input
                [maxlength]="item.maxLength!"
                [placeholder]="'placeholder.input' | translate"></textarea>
            </nz-textarea-count>
          </ng-container>

          <ng-container *ngIf="item.type === 'localSelect'">
            <nz-select
              [nzShowSearch]="true"
              [nzAllowClear]="true"
              [formControlName]="item.key"
              [nzOptions]="item.options || []"
              [nzPlaceHolder]="'flss.placeholder.select' | translate"
              (ngModelChange)="onSelectSearch($event, item)"></nz-select>
          </ng-container>

          <ng-container *ngIf="item.type === 'address'">
            <nz-cascader
              [nzPlaceHolder]="'placeholder.select' | translate"
              [nzShowSearch]="true"
              [nzExpandTrigger]="'hover'"
              [nzOptions]="addressOption"
              [formControlName]="item.key"
              (ngModelChange)="onSelectSearch($event, item)"></nz-cascader>
            <ng-container *ngIf="item.subkey">
              <div style="margin-top: 6px">
                <nz-textarea-count [nzMaxCharacterCount]="255" class="inline-count">
                  <textarea
                    [nzAutosize]="{ minRows: 0, maxRows: 4 }"
                    [formControlName]="item.subkey"
                    flcInputTrim
                    nz-input
                    [maxlength]="item.maxLength || null"
                    [placeholder]="translateName + '请输入详细地址' | translate"></textarea>
                </nz-textarea-count>
              </div>
            </ng-container>
          </ng-container>
        </nz-form-control>
        <nz-form-control *ngIf="item.readOnly">
          <div class="item-value">
            <flc-text-truncated [data]="formInfo[item.labelKey]"></flc-text-truncated>
            <span *ngIf="item.key === 'sample_order_id' && formInfo.sample_sort_num" class="tag">
              {{ 'sampleOutsourcing.detailField.第' | translate }}{{ formInfo.sample_sort_num
              }}{{ 'sampleOutsourcing.detailField.轮' | translate }}</span
            >
          </div>
        </nz-form-control>
      </nz-form-item>
    </ng-container>
  </div>
</form>
