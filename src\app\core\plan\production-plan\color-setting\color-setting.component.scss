::ng-deep .drawer {
  .ant-drawer-header {
    padding: 12px 0 12px 16px;
    border-bottom: 1px solid #e6eaf0;
    border-radius: 16px 16px 0 0;
  }

  .ant-drawer-title {
    width: calc(100% - 50px);
    flex: none;
  }

  .ant-drawer-content {
    border-radius: 16px 16px 0 0 !important;
  }
  .ant-radio-wrapper {
    font-size: 14px;
  }
  .ant-form-item {
    display: flex;
    align-items: center;
  }
  .ant-form-item {
    margin-bottom: 0px;
  }
  .ant-input-number-handler-wrap {
    display: none;
  }
  .ant-input-number-input {
    padding: 0 5px;
  }

  .ant-radio-wrapper {
    margin-right: 0;
  }

  .ant-spin-container {
    display: flex;
    overflow: hidden;
    height: 100%;
  }
}

::ng-deep indicator-component svg {
  vertical-align: 20% !important;
}

::ng-deep .graph-color .ant-popover-inner-content {
  padding: 0;
}

[row-center-between] {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

[row-center] {
  display: flex;
  align-items: center;
}

.header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .left {
    font-size: 18px;
    font-weight: 500;
    color: #262d48;
    line-height: 25px;
  }
  .right {
    display: flex;
    align-items: center;
    gap: 8px;
    .line {
      display: inline-block;
      width: 1px;
      height: 16px;
      background: #d8d8d8;
      border-radius: 2px;
    }
  }
}

.error {
  border-radius: 4px !important;
  border: 1px solid #f70707 !important;
  .iconshouzhi {
    color: #f70707 !important;
  }
}

.serachWrap {
  padding: 8px 10px;
  margin: 0 8px;
  background: #f7f8fa;
  border-radius: 0px 0px 8px 8px;
}

.card-text {
  font-size: 13px;
  font-weight: 500;
  color: #4a5671;
  line-height: 32px;
}

.serachContent {
  flex: 1;
  overflow: auto;
  background-color: #f3f5f9;
  border-top: 8px solid #fff;
  padding: 8px 2px 8px 12px;
  .card {
    margin-bottom: 8px;
    width: calc(20% - 8px);
    margin-right: 8px;
    float: left;
    .title {
      height: 40px;
      padding: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #f6f9fe;
      border-radius: 6px 6px 0px 0px;
      border: 1px solid #dfe3ec;
      border-bottom: none;
      .left {
        height: 22px;
        font-size: 16px;
        font-weight: 500;
        color: #262d48;
        line-height: 22px;
      }
      .right {
        font-size: 12px;
        font-weight: 500;
        color: #303852;
        line-height: 16px;
      }
    }
    .content {
      background: #ffffff;
      box-shadow: inset 1px 1px 3px 0px rgba(215, 221, 228, 0.5);
      border-radius: 0px 0px 6px 6px;
      border: 1px solid #dfe3ec;
      .wrap {
        padding: 0 8px;
      }
      .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 54px;
        border-bottom: 1px dotted #dfe3ec;
        &:last-child {
          border-bottom: none;
        }
        .left {
          display: flex;
          align-items: center;
          .color-box {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            background: #f1f2f5;
            border-radius: 4px;
            border: 2px solid #ffffff;
          }
          .background-hover {
            &:hover {
              background-color: #edf6ff !important;
            }
          }
          .edit-color-box {
            position: relative;
            cursor: pointer;
            &:hover {
              border-radius: 4px !important;
              border-color: #2996ff !important;
              // background-color: ;
              .icon-cuowu {
                display: block;
              }
              .iconshouzhi {
                color: #2996ff !important;
              }
            }
            .icon-cuowu {
              display: none;
              color: #ff0000;
              opacity: 0.5;
              position: absolute;
              top: -12px;
              right: -10px;
              &:hover {
                color: #ff0000;
                opacity: 1;
              }
            }
          }
        }
        .edit-input {
          display: flex;
          align-items: center;
          .no-input-value {
            overflow: hidden;
            padding: 0 5px;
            display: flex;
            align-items: center;
            // justify-content: center;
            height: 24px;
            width: 50px;
            border-radius: 4px;
            font-size: 14px;
            border: 1px solid #c4c9d5;
            color: rgba(0, 0, 0, 0.85);
            background-color: #f5f5f5;
          }
        }
        .right {
          font-size: 14px;
          font-weight: 500;
          color: #262d48;
          line-height: 20px;
        }
        .right-no-data {
          font-size: 14px;
          color: #8c8f9b;
          line-height: 20px;
        }
      }
    }
    .foot {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #fff;
      margin-top: 10px;
      height: 24px;
      .left {
        display: inline-block;
        padding: 0 8px;
        font-size: 12px;
        font-weight: 500;
        color: #788093;
        line-height: 24px;
        background: #f7f7fa;
        border-radius: 0 12px 0 0;
        &:hover {
          color: #007aff;
          background-color: #edf6ff;
        }
        cursor: pointer;
        .iconqingkong1 {
          margin-right: 4px;
          font-size: 12px;
        }
      }
      .line {
        width: 1px;
        height: 10px;
        background: #aeb3c2;
        border-radius: 1px;
        margin: 4px 6px 0;
        display: inline-block;
      }
      .right {
        display: inline-block;
        padding: 0 8px;
        font-size: 12px;
        font-weight: 500;
        color: #788093;
        line-height: 24px;
        background: #f7f7fa;
        border-radius: 12px 0 0 0;
        .clear-color:hover {
          color: #007aff;
          cursor: pointer;
        }
        .clear-sam:hover {
          color: #007aff;
          cursor: pointer;
        }
      }
    }
  }
}

.drag-color-box {
  width: 288px;
  z-index: 99;
  position: relative;
  position: absolute;
  bottom: 10px;
  left: 16px;
  cursor: move;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px 0 12px;
    height: 26px;
    background: #2996ff;
    box-shadow: 0px 2px 4px 0px rgba(181, 188, 201, 0.5);
    color: #ffffff;
    font-size: 12px;
    border-radius: 4px 4px 0 0;
    .iconshouzhi {
      color: #ffffff;
      margin-right: 2px;
      font-size: 12px;
    }
    .icon-cuohao {
      color: #dcdedf;
      cursor: pointer;
      &:hover {
        color: #fff;
      }
    }
  }
  .body {
    background: #ffffff;
    border: 1px solid #dfe3ec;
    padding: 10px 8px 0;
    border-radius: 0 0 4px 4px;
    .top-area {
      padding-left: 6px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 25px;
      .left {
        .title {
          font-size: 14px;
          font-weight: 500;
          color: #262d48;
          line-height: 20px;
        }
        .icon-yiwen {
          cursor: pointer;
          color: #c3c6cf;
          margin-left: 5px;
          font-size: 12px;
          &:hover {
            color: #2996ff;
          }
        }
      }
      .right {
        margin-right: -8px;
      }
    }
    .middle-area {
      margin-top: 8px;
      .default-color-box {
        background: #f9fafc;
        opacity: 0.89;
        height: 124px;
        padding: 6px;
        display: flex;
        border-radius: 4px;
        justify-content: space-between;
        cursor: pointer;
        .color-grounp {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          border: 1px solid #ffffff;
          .color-item {
            width: 22px;
            height: 22px;
            background: #eae7ff;
            border: 2px solid #fff;
            border-radius: 2px;
          }
        }
      }
      .custom-color {
        margin-top: 12px;
        padding-left: 6px;
        .custom-color-title {
          height: 20px;
          font-size: 14px;
          font-weight: 500;
          color: #4a5671;
          line-height: 20px;
        }
        .custom-color-grounp {
          display: flex;
          flex-wrap: wrap;
          .color-item-box {
            cursor: pointer;
            border: 1px solid #fff;
            margin-top: 8px;
            margin-right: 9px;
            .color-item {
              width: 22px;
              height: 22px;
              background: #eae7ff;
              border: 2px solid #fff;
              border-radius: 2px;
            }
          }
        }
      }
      .color-picker {
        margin: 16px 0 28px;
        padding-left: 6px;
        height: 32px;
        display: flex;
        align-items: center;
        .color-picker-title {
          font-size: 12px;
          font-weight: 500;
          color: #4a5671;
          line-height: 16px;
        }
        .box {
          height: 100%;
          margin-left: 8px;
          height: 32px;
          border: 1px solid #d4d7dc;
          border-radius: 4px;
          display: flex;
          align-items: center;
          cursor: pointer;
          .color-picker-area {
            padding: 0 6px;
            width: 54px;
            height: 100%;
            background: #ffffff;
            font-size: 14px;
            font-weight: 500;
            color: #262d48;
            line-height: 20px;
          }
          .iconyanse {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 26px;
            color: #ced3de;
            background: #f7f7f7;
            border-left: 1px solid #d4d7dc;
            &:hover {
              color: #69b6ff;
              background-color: #edf6ff;
            }
          }
        }
        .redo {
          margin-left: 8px;
          color: #c8ced9;
          font-size: 12px;
          cursor: pointer;
        }
        .reset {
          margin-left: 4px;
          font-size: 14px;
          font-weight: 500;
          color: #788093;
          line-height: 20px;
          cursor: pointer;
        }
      }
    }
  }
  .drag-area {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    right: 1px;
    bottom: 1px;
    width: 32px;
    height: 32px;
    background: #f4f4f7;
    border-radius: 12px 0 0 0;
    &:hover {
      i {
        color: #76bbff;
      }
      background-color: #edf6ff;
    }
    i {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 14px;
      height: 14px;
      color: #b1b5c1;
      font-size: 14px;
      cursor: move;
    }
  }
}

.active {
  border-radius: 4px !important;
  border-color: #2996ff !important;
  .iconshouzhi {
    color: #2996ff !important;
  }
}

.source-active {
  &:active {
    border-radius: 4px !important;
    border-color: #2996ff !important;
    .iconshouzhi {
      color: #2996ff !important;
    }
  }
  &:hover {
    border-radius: 4px !important;
    border-color: #2996ff !important;
    .iconshouzhi {
      color: #2996ff !important;
    }
  }
}

.color-grounp-active {
  &:active {
    border-radius: 4px !important;
    border-color: #2996ff !important;
    .iconshouzhi {
      color: #2996ff !important;
    }
  }
  &:hover {
    border-radius: 4px !important;
    border-color: #2996ff !important;
    .iconshouzhi {
      color: #2996ff !important;
    }
  }
}

.no-image {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

::ng-deep .color-tooltip {
  p {
    display: flex;
  }
  .color-word {
    width: 42px;
    text-align: justify;
    text-align-last: justify;
  }
}
