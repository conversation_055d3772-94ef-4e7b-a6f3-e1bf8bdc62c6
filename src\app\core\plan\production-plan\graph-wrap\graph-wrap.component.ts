import { Component, OnInit, Input, EventEmitter, Output, OnChanges, SimpleChanges, ChangeDetectorRef } from '@angular/core';
import { differenceInMilliseconds, startOfDay } from 'date-fns';
import { PlanItem } from '../production-plan';
import { ProductionPlanService } from '../production-plan.service';
import { DayRange, WeekRange } from 'src/app/components/graph-table/graph-table';

@Component({
  selector: 'app-graph-wrap',
  templateUrl: './graph-wrap.component.html',
  styleUrls: ['./graph-wrap.component.scss'],
})
export class GraphWrapComponent implements OnInit, OnChanges {
  @Input() data!: any;
  @Input() options!: {
    signalWidth: number;
    dayWidth: number; // 日维度一天48，小时维度一天24*12
    wrapBodyWidth: number;
    dimension: string;
    per1Px: number;
  };
  @Input() graphOptions: { item_dimension: 'io' | 'po' } = {
    item_dimension: 'io',
  };
  @Input() days!: DayRange[];
  @Input() weeks!: WeekRange[];
  @Input() exitClickable!: boolean;
  @Output() tapGraphItem = new EventEmitter<PlanItem>();
  @Output() hoverGraphItem = new EventEmitter<PlanItem>();
  @Output() leaveGraphItem = new EventEmitter();
  lineList: any[] = [];
  hours = [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24];

  constructor(private _service: ProductionPlanService, private _cdr: ChangeDetectorRef) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.data && changes?.data?.currentValue) {
      this.arrange();
      if (!changes?.data?.firstChange) {
        this.data = { ...this.data };
      }
    }
    if (changes?.graphOptions && changes?.graphOptions?.currentValue && !changes.graphOptions.firstChange) {
      this.arrange();
    }
    if (changes?.options && changes?.options?.currentValue && !changes.options.firstChange) {
      this.arrange();
    }
  }

  /**
   * 设置好每个甘特图的位置信息
   */
  arrange() {
    const start_date = this._service.searchData.start_date as Date;
    const plan_items = this.graphOptions?.item_dimension === 'io' ? [...this.data.order_plan_items] : [...this.data.po_plan_items];
    const lineList = [...this.data.schedule_plan_items, ...plan_items];
    // 重新计算甘特图的时长
    lineList.forEach((e) => {
      this._service.calcGraphPosition(e, this.options.signalWidth, startOfDay(start_date), this.options.dimension === 'hour');
    });
    const sortedList = this._service?.sort(lineList);
    const tempList: any[] = [];
    sortedList.forEach((e, i: number) => {
      e.forEach((d: any) => {
        d.top = i * 30 + 10;
        tempList.push(d);
      });
    });
    this.data.height = sortedList.length * 30 + 10;
    this.lineList = [...tempList];
  }

  tapGraph(e: any, i: number) {
    if (e?.is_selected) {
      this.data.exitSelected = true;
      this.lineList?.forEach((item, index) => {
        if (index !== i) {
          item.setSelected(false);
        } else {
          item.setSelected(true);
        }
      });
    } else {
      this.data.exitSelected = false;
      this.lineList?.forEach((item) => {
        item.setSelected(false);
      });
    }
    this.tapGraphItem.emit(e);
    this._cdr.detectChanges();
  }

  leaveGraph() {
    this._cdr.detectChanges();
    this.leaveGraphItem.emit();
  }

  hoverGraph(e: any, i: number) {
    this._cdr.detectChanges();
    this.hoverGraphItem.emit(e);
  }
}
