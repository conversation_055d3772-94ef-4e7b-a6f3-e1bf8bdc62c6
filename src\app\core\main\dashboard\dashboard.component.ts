import { ChangeDetectionStrategy, Component, NgZone, OnDestroy, OnInit } from '@angular/core';
import { FlcRouterEventBusService, MenuItem, MenuService } from 'fl-common-lib';
import { Subscription } from 'rxjs';
import { DashboardService } from './dashboard.service';
import { Router } from '@angular/router';
import { MainService } from '../main.service';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
  providers: [DashboardService],
})
export class DashboardComponent implements OnInit, OnDestroy {
  menuList: MenuItem[] = [];
  currentSubMenu: { groupName: string; children: MenuItem[] }[] = [];
  routeNavigationEndListerner?: Subscription;
  readonly limit = 50;
  unfinishPage = 1;
  finishedPage = 1;
  totalUnfinishMessageCount = 0;
  totalFinishedMessageCount = 0;
  unfinishMessageList: MessageItem[] = [];
  finishedMessageList: MessageItem[] = [];
  searchKeyword?: string;
  messageeIndex = 0;
  currentMenuIndex = 0;
  messageSearchString?: string | null;
  translateName = 'main.dashboard.';
  constructor(
    private _menu: MenuService,
    private _routerEventBus: FlcRouterEventBusService,
    private _router: Router,
    private _zone: NgZone,
    private _service: DashboardService,
    private _mainService: MainService
  ) {}

  clickLink(selectMenu: MenuItem) {
    if (this._mainService.isDashboardLink(selectMenu)) {
      window.open(selectMenu.path, '_blank');
      return;
    }
    this.navigateTo(selectMenu.path);
  }
  messageSearchStringChange(event: string) {
    this.searchKeyword = event;
    this.messageIndexChange(this.messageeIndex);
  }

  ngOnDestroy(): void {
    this.routeNavigationEndListerner?.unsubscribe();
  }
  routeChangeListener() {
    this.routeNavigationEndListerner = this._routerEventBus.onRouterActivationEnd('/dashboard', () => {
      this.messageIndexChange(this.messageeIndex);
    });
  }
  getMessageList(isFinished: boolean, isReload = true) {
    if (!isReload) {
      if (isFinished) this.finishedPage++;
      else this.unfinishPage++;
    } else {
      if (isFinished) {
        this.finishedPage = 0;
        this.finishedMessageList = [];
      } else {
        this.unfinishPage = 0;
        this.unfinishMessageList = [];
      }
    }
    const data = {
      page: isFinished ? this.finishedPage : this.unfinishPage,
      limit: this.limit,
      keyword: this.searchKeyword,
      handle_status: (isFinished ? 2 : 1) as 2 | 1,
    };
    this._service.getMessageList(data).subscribe((res) => {
      if (res.code === 200) {
        const count = res.data.count;
        const data = res.data.notify_info;
        if (isFinished) {
          this.totalFinishedMessageCount = count;
          this.finishedMessageList = [...this.finishedMessageList, ...data];
        } else {
          this.totalUnfinishMessageCount = count;
          this.unfinishMessageList = [...this.unfinishMessageList, ...data];
        }
      }
    });
  }
  readMessage(id: number) {
    this._service.markMessageRead([id]).subscribe((res) => {});
    this.navigateTo('/material-procurement/bulkorder/list');
  }
  navigateTo(url: string) {
    this._zone.run(() => {
      this._router.navigateByUrl(url);
    });
  }
  messageIndexChange(event: number) {
    this._zone.run(() => {
      this.messageeIndex = event;
    });
    if (event === 0) {
      this.getMessageList(false);
    } else {
      this.getMessageList(true);
    }
  }

  ngOnInit(): void {
    this.getMessageList(false);
    this.getMessageList(true);
    this.menuList = this._menu.getMenuList();
    this.menuList.forEach((first) => {
      first.children = first.children?.filter((sub) => sub.value);
    });
    this.menuList = this.menuList.filter((first) => (first.children?.length ?? 0) > 0);
    if (this.menuList.length > 0) {
      this.changeSubMenu(this.currentMenuIndex);
    }
    this.routeChangeListener();
  }
  changeSubMenu(currentIndex: number) {
    this._zone.run(() => {
      this.currentMenuIndex = currentIndex;
    });
    this.currentSubMenu = [];
    const groupedMenu = new Map<string, MenuItem[]>();
    const selectedSubMenu = this.menuList[currentIndex];
    selectedSubMenu.children?.forEach((menu) => {
      const groupName = menu.groupName ?? '';
      const group = groupedMenu.get(groupName);
      if (group) {
        group.push(menu);
      } else {
        groupedMenu.set(groupName, [menu]);
      }
    });
    groupedMenu.forEach((children, groupName) => {
      this.currentSubMenu.push({ groupName, children });
    });
  }
}
interface MessageItem {
  id: number;
  type: number;
  type_name: string;
  content: string;
  related_code: string;
  related_id: number;
  created_at: number;
  status: 1 | 2; //  1-未读 2-已读
}
