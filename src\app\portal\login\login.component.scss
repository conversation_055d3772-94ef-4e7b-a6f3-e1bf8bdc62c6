@import 'variables';

.logo-corner {
  width: 180px;
  position: absolute;
  top: 34px;
  left: 36px;
}

.full-screen {
  height: 100%;
  min-width: 1000px;
  background-image: url(/assets/image/login_bg_new.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

.main {
  float: right;
  height: 100%;
  padding-top: 117px;
  width: 534px;
  background-color: #fff;
  text-align: center;
}

.title {
  font-size: 30px;
  font-weight: 500;
}

:host ::ng-deep .highlight {
  color: $fl-pretty-color-dark;
  font-size: 36px;
}

.sub-title {
  padding-top: 80px;
  font-size: 20px;
  font-weight: 500;
}

.title-image-wrap {
  display: flex;
  justify-content: center;
  gap: 16px;

  .image {
    width: 155px;
    background-image: url(/assets/image/logo_elan.png);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    background-origin: content-box;
  }
}

.login-form,
.captcha-form {
  margin: 20px 107px;
}

.ant-form-item {
  margin-bottom: 28px;
}

.resetPwd .login-form .ant-form-item {
  margin-bottom: 0;
}

:host ::ng-deep .resetPwd .ant-form-item-label > label {
  font-size: 12px;
  color: $fl-grey-dark;
}

:host ::ng-deep .resetPwd .ant-form-item-has-error .ant-form-item-label > label {
  color: #ff5c33;
}
.error {
  color: #ff5c33;
}

.ant-form-item-has-error .ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled) {
  border-color: #ff5c33;
  background-color: #fff6f3;
}

.ant-form-item-has-error .ant-input:not(.ant-input-disabled) {
  background-color: #fff6f3;
}

.ant-input-affix-wrapper {
  padding: 8px 11px;
}

i {
  color: $fl-grey;
}

.login-form-button {
  width: 100%;
  margin-top: 14px;
}

.msg {
  margin: 50px 0;
  color: $fl-grey-dark;

  i {
    color: $fl-pretty-color;
  }
}

.ant-input-affix-wrapper {
  &:hover,
  &:focus,
  &-focused {
    border-color: $fl-pretty-color;
  }
}

.pwd-hint {
  user-select: none;
}

.mobile {
  touch-action: pan-y;

  &.full-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: unset;
    width: 100%;
  }

  .logo-corner {
    width: 8em;
    position: absolute;
    top: 2em;
    left: 2em;
  }

  .main {
    display: flex;
    justify-content: center;
    flex-direction: column;
    float: unset;
    height: 60%;
    width: 100%;
    margin: 0.5em;
    padding-top: 1em;
    background: radial-gradient(
      rgb(255, 255, 255),
      rgb(255, 255, 255),
      rgb(255, 255, 255, 0.8),
      rgb(255, 255, 255, 0.6),
      rgb(255, 255, 255, 0.2)
    );
  }

  .title-image-wrap {
    gap: 1em;

    .title {
      font-size: 2em;
      font-weight: 500;
    }

    .image {
      width: 9.5em;
    }
  }

  .sub-title {
    padding-top: 5%;
    font-size: 1.25em;
  }

  .login-form,
  .captcha-form {
    margin: 2%;
  }

  .ant-input {
    font-size: 16px;
  }
}
