import { Component, EventEmitter, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { NzModalService } from 'ng-zorro-antd/modal';

@Component({
  selector: 'app-undo-modal',
  templateUrl: './undo-modal.component.html',
  styleUrls: ['./undo-modal.component.scss'],
})
export class UndoModalComponent implements OnInit {
  @ViewChild('titleTpl') titleTpl: TemplateRef<any> | undefined;
  @ViewChild('contentTpl') contentTpl: TemplateRef<any> | undefined;
  @ViewChild('footerTpl') footerTpl: TemplateRef<any> | undefined;
  @Output() handleUndoOk = new EventEmitter<any>();

  constructor(private _modal: NzModalService) {}

  ngOnInit(): void {}

  createUndoModel() {
    this._modal.create({
      nzWidth: '350px',
      nzTitle: this.titleTpl,
      nzContent: this.contentTpl,
      nzFooter: null,
    });
  }

  closeUndoModel() {
    this._modal.closeAll();
  }

  handleCancel() {
    this.closeUndoModel();
  }

  handleOk() {
    this.handleUndoOk.emit();
  }
}
