$highlightColor: #138aff;
$highlightHoverColor: #4d96ff;

:host ::ng-deep {
  .wrapper {
    border-radius: 0px 0px 2px 2px;
    background: #fafbfd;
    padding: 0 12px;
    overflow-y: auto;

    .content-wrapper {
      display: flex;
      flex-direction: row;
      margin-bottom: 8px;
      align-items: baseline;
      .sort {
        width: 20px;
        height: 20px;
        background: #f2f9ff;
        border-radius: 4px;
        text-align: center;
        line-height: 20px;
        margin-right: 8px;
      }

      .content {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow-x: hidden;
        color: #515661;
        width: 100%;
      }
    }

    &:first-child .content-wrapper {
      margin-top: 8px;
    }
  }
}

:host ::ng-deep {
  .inner-content {
    width: 100%;
    padding: 8px 12px;
    background: #f5f7fa;
    border-radius: 2px;

    .file-wrapper {
      margin-left: 90px;
    }
  }

  .label-wrapper {
    width: 90px;
  }

  .ant-form-item {
    margin-bottom: 8px;
  }

  .link-text {
    color: $highlightColor;
    text-decoration: underline;
    cursor: pointer;
  }

  // 展开收起
  .toggle {
    display: flex;
    align-items: center;
    color: $highlightColor;
  }

  .toggle {
    i,
    span {
      color: #54607c;
    }
    &:hover {
      cursor: pointer;
      span {
        color: $highlightHoverColor;
      }
      i {
        color: $highlightHoverColor;
      }
    }
  }

  .toggle-zhankai {
    margin-top: 8px;
    overflow: hidden;
    padding: 8px 12px !important;
  }

  .toggle-shouqi {
    margin-top: 0px;
    max-height: 0;
    padding: 0 !important;
  }

  .align-items-center {
    display: flex;
    align-items: center;
  }
}
