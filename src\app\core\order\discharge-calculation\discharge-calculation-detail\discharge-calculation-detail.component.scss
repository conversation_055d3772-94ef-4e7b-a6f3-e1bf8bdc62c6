:host {
  height: calc(100vh - 64px);
  display: flex;
  flex-direction: column;
}
// 表格相关样式重置
:host
  ::ng-deep
  .table-scroll
  .ant-table-thead
  > tr
  > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
  width: 0px;
}

:host ::ng-deep .ant-btn-primary {
  border-color: #40a2ff;
  background: #40a2ff;
}

:host ::ng-deep .table-scroll .ant-table-body::before {
  height: 0px;
}

:host ::ng-deep .discharge-operation-body .ant-table-body tr:nth-child(2) td {
  border-top: none !important;
}

:host ::ng-deep .table-scroll .ant-table-tbody > tr > td {
  border-bottom-width: 0px;
}

:host ::ng-deep [nz-button] {
  margin-right: 0;
}

:host ::ng-deep .fl-button-link {
  color: #54607c;
  &:hover {
    color: #2996ff;
  }
}

:host ::ng-deep .fl-button-link-minor {
  color: #54607c;
  &:hover {
    color: #2996ff;
  }
}

:host ::ng-deep .ant-input-number {
  width: 65px;
}

:host ::ng-deep .purchase-amount.ant-input-number {
  width: 80px !important;
}

// tab样式重置
:host ::ng-deep .header_title_top {
  top: 0 !important;
}

:host ::ng-deep .ant-tabs {
  color: #54607c;
  font-size: 14px;
}

:host ::ng-deep .ant-tabs-tab {
  padding: 2px 10px !important;
  height: 24px !important;
}

:host ::ng-deep .ant-tabs-nav-operations {
  height: 24px !important;
}

:host ::ng-deep .linetabs > .ant-tabs-nav {
  border-radius: 4px 4px 12px 12px;
  padding: 8px 0;
}

:host ::ng-deep .linetabs > .ant-tabs-nav::before {
  border-bottom: none;
}

:host ::ng-deep .linetabs > .ant-tabs-nav .ant-tabs-nav-wrap {
  padding: 0px;
}

:host ::ng-deep .linetabs > .ant-tabs-nav .ant-tabs-tab {
  border-radius: 24px;
  background: #fff;
}

:host ::ng-deep .linetabs > .ant-tabs-nav .ant-tabs-tab-active {
  background: #eef7ff;
  border: 1px solid #40aaff;
}

:host ::ng-deep .ant-select-selector {
  max-height: 116px;
  overflow-y: auto;
  overflow-x: hidden;
}

:host ::ng-deep .bom-border-right .ant-form-item {
  margin-bottom: 0;
}

:host ::ng-deep .ant-table-cell .ant-form-item {
  margin-bottom: 10px;
  margin-top: 10px;
  justify-content: center;
}

:host ::ng-deep .position-select .ant-select-selection-search {
  width: 40px !important;
}

:host ::ng-deep .ant-input-number-handler-wrap {
  display: none;
}

// 开关相关样式重置
:host ::ng-deep .ant-switch-checked {
  background-color: #138aff !important;
}

:host ::ng-deep .ant-switch-disabled {
  opacity: 1;
}

:host ::ng-deep .ant-switch {
  background-color: #ecedf2;
}

:host ::ng-deep .ant-switch-checked .ant-switch-inner {
  color: white;
}

:host ::ng-deep .ant-switch-inner {
  color: #54607c;
}

.purchase-num:hover {
  text-decoration: underline;
  cursor: pointer;
}

// 带数字的文本域样式重置
.inline-count {
  position: relative;
  &::after {
    position: absolute;
    right: 8px;
    bottom: 0px;
    font-size: 12px;
  }
}

// 小贴士相关样式
.discharge-tip {
  display: flex;
  background: #f1f7ff;
  border-radius: 4px 4px 0px 0px;
  font-size: 14px;
  font-weight: 500;
  color: #354060;
  line-height: 32px;
  align-items: center;
  box-shadow: 0 0 4px 0 #d4d9e9;
}

.tipButton {
  &:focus {
    border-color: rgb(217, 217, 217);
  }
}

.tipActiveButton {
  background-color: #fff;
  color: #138aff;
  border-color: #138aff;
  &:hover {
    opacity: 0.8;
  }
}

.textGray {
  color: #6f7483;

  &:hover {
    color: #138aff;
  }
}

.tipContainer {
  margin-bottom: 6px;
  position: relative;
  background: #f1f7ff;
  border-radius: 3px;
  min-height: 80px;
  display: flex;
  flex-direction: row;
  border: 1px solid #fff;
  box-shadow: 0 0 4px 0 #d4d9e9;
  .leftArea {
    flex-shrink: 0;
    width: 30px;
    height: 94px;
    background: #e4efff;
    border-radius: 3px 0px 0px 3px;
    font-size: 14px;
    font-weight: 500;
    color: #138aff;
    line-height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    span {
      width: 14px;
    }
  }
  .rightArea {
    flex-grow: 1;
    padding-left: 8px;
    color: #4a5c87;
    font-weight: 500;
    .tip-content {
      padding: 0 6px;
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
      flex-wrap: nowrap;
      height: 100%;
      font-size: 14px;
    }
  }
}

.border-right {
  border-right: 1px solid #d4d7dc;
}

.border-left {
  border-left: 1px solid #d4d7dc;
}

.border-bottom {
  border-bottom: 1px solid #c6c6c6 !important;
}

.border-bottom-d4d7dc {
  border-bottom: 1px solid #d4d7dc;
}

/**
** 表头的滑块隐藏
**/
:host ::ng-deep .nz-table-hide-scrollbar::-webkit-scrollbar-thumb {
  visibility: hidden;
}

.no-image {
  min-width: 400px;
  height: 240px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  border: 1px solid rgb(212, 215, 220);
  border-radius: 4px;
}

.charge-no-image {
  min-width: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  height: 100%;
}

.no-image-tip {
  font-size: 14px;
  color: #aab2c5;
  line-height: 20px;
  margin-right: 16px;
}

.add-new {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  width: 80px;
  height: 28px;
  font-size: 14px;
  font-weight: 500;
  color: #138aff;
  line-height: 20px;
  background: #e6f2ff;
  border-radius: 16px;
}

.add-new:hover {
  color: rgba(15, 134, 246, 0.8) !important;
}

.add-new i {
  margin-right: 4px;
}

.delete-hover:hover {
  color: #ff4d4f;
}

.over {
  margin-top: -4px;
  margin-left: 2px;
  display: inline-block;
  width: 22px;
  height: 18px;
  font-size: 12px;
  line-height: 18px;
  text-align: center;
  color: #ff5c33;
  background: #ffeae5;
  border-radius: 9px;
}

.ioption {
  background: #f1f8ff;
  border-radius: 2px;
  font-size: 14px;
  color: #222b3c;
  line-height: 20px;
  padding: 2px;
}

.delete-icon {
  color: #fb5050;
  cursor: pointer;
  font-size: 16px;
  opacity: 0.8;
}

.delete-icon:hover {
  opacity: 1;
}

.add-icon {
  color: #40aaff;
  margin-left: 6px;
  cursor: pointer;
  font-size: 16px;
  opacity: 0.8;
}

.add-icon:hover {
  opacity: 1;
}

.add-line {
  font-size: 12px;
  background-color: #c8c8c8;
  position: absolute;
  left: -1px;
  top: -6px;
  color: #fff;
  &:hover {
    color: #ff830d;
    background: #fff;
  }
}

.del-line {
  font-size: 12px;
  background-color: #c8c8c8;
  position: absolute;
  left: -1px;
  top: -6px;
  color: #fff;
  &:hover {
    color: #ff830d;
    background: #fff;
  }
}

.discharge-no-edit {
  padding: 8px;
  line-height: 26px;
  max-height: 128px;
  overflow-x: hidden;
  overflow-y: auto;
  &.positions {
    display: flex;
    flex-wrap: wrap;
    span {
      margin-bottom: 2px;
    }
  }
}

.col-remark {
  .position-remark {
    position: relative;
    .add-remark {
      display: none;
      width: 20px;
      height: 20px;
      background: #e6f2ff;
      border-radius: 0px 0px 0px 8px;
      opacity: 0.8;
      position: absolute;
      top: 0;
      left: 0;
      line-height: 20px;
      z-index: 99;
    }
    .add-remark1 {
      width: 20px;
      height: 20px;
      background: #e6f2ff;
      border-radius: 0px 0px 0px 8px;
      opacity: 0.8;
      position: absolute;
      top: 0;
      left: 0;
      line-height: 20px;
      z-index: 99;
    }
  }
}

.bom-inline-count {
  position: relative;

  &::after {
    position: absolute;
    right: 8px;
    bottom: 0px;
    font-size: 12px;
  }

  .ant-input {
    padding: 4px 34px 4px 11px;
  }
}

.col-remark:hover {
  .add-remark {
    display: block !important;
  }
}

:host ::ng-deep .clear-header-border .ant-table-thead > tr > th {
  border-bottom: none;
}

.bom-border-right th {
  border-right: none !important;
}

:host ::ng-deep .bom-border-right .ant-table-thead > tr > th,
.ant-table-tbody > tr > td {
  padding: 4px 8px;
}

:host ::ng-deep .ant-table-thead > tr > th,
.ant-table-tbody > tr > td {
  padding: 4px 12px;
}

::ng-deep .ant-table-tbody > .clear-border > td {
  border-bottom-width: 0 !important;
  border-right-width: 0 !important;
  &:last-child {
    border-right-width: 1px !important;
  }
}

.colorRadio {
  display: block;
  height: 42px;
  border-bottom: 1px solid rgba(218, 221, 228, 0.4);
}

.margin-right32 {
  margin-right: 32px;
}

.margin-left-10 {
  margin-left: 10px;
}

.remake-edit {
  position: relative;
  .remake-tip {
    font-size: 12px;
    color: #84879a;
    line-height: 20px;
  }
  .pushpin {
    right: 0;
    bottom: -2px;
    position: absolute;
    cursor: pointer;
    font-size: 12px;
    width: 20px;
    height: 20px;
    background: #c4c9d5;
    border-radius: 10px;
    color: #84879a;
    display: flex;
    align-items: center;
    justify-content: center;
    &:hover {
      color: #2996ff;
    }
  }
}

.remake-no-edit {
  position: absolute;
  z-index: 999;
  .remark-box {
    position: relative;
    width: 360px;
    height: 180px;
    background: #ffffff;
    box-shadow: 0px 2px 4px 0px rgba(220, 237, 250, 0.5);
    border-radius: 4px;
    padding: 6px 8px;
    font-size: 12px;
    color: #354060;
    line-height: 17px;
    overflow: auto;
    border: 1px solid #c4c9d5;
  }
  .pushpin {
    right: 4px;
    bottom: 4px;
    position: absolute;
    cursor: pointer;
    font-size: 12px;
    width: 20px;
    height: 20px;
    background: #ddeeff;
    border-radius: 10px;
    color: #2996ff;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

tr.has-line td {
  border-top: 2px solid #ff830d !important;
}

.icontishi1:hover {
  color: #138aff;
}

.op-td {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.discharge-box {
  display: flex;
  margin-bottom: 8px;
  flex: 1;
  overflow: hidden;
  .discharge-left {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    .discharge-base-info {
      padding: 0 8px;
      border-radius: 4px;
      box-shadow: 0px 0px 4px 0px rgba(197, 204, 240, 0.5);
      background: #fff;
      .base-info-header {
        padding: 8px 8px 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        color: #138aff;
        .title {
          font-size: 16px;
          color: #222b3c;
        }
        .expand {
          cursor: pointer;
          margin-left: 16px;
        }
        .pack-up {
          cursor: pointer;
          margin-left: 16px;
        }
        .expand:hover {
          opacity: 0.8;
        }
        .pack-up:hover {
          opacity: 0.8;
        }
      }
      .table-scroll {
        overflow-y: auto;
        overflow-x: hidden;
        padding-bottom: 12px;
      }
      .detail-header {
        margin-top: 16px;
        padding: 5px 8px 5px 14px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #f5f9ff;
        .detail-header-left {
          font-size: 14px;
          font-weight: 500;
          color: #354060;
          line-height: 20px;
        }
        .detail-header-right {
          .po-item {
            border-right: 1px solid #c1c6d4;
            font-size: 12px;
            font-weight: 500;
            color: #354060;
            line-height: 17px;
          }
          .line {
            padding: 14px 0;
            border-left: 1px solid #dbe0e6;
            font-size: 0;
          }
          .po-item:last-child {
            border-right: none;
          }
        }
      }
      .detail-body {
        margin-top: 4px;
        box-shadow: 0px 2px 4px 0px rgba(228, 234, 241, 0.5);
        border-radius: 4px 4px 3px 3px;
      }
    }
    .discharge-bom-info {
      flex: auto;
      border-radius: 4px;
      background: #fff;
      margin-top: 8px;
      padding: 0 8px;
      .bom-info-header {
        .bom-btn {
          padding-left: 8px;
          display: flex;
          justify-content: space-between;
          height: 32px;
          .left {
            font-size: 16px;
            font-weight: 500;
            color: #222b3c;
            line-height: 32px;
          }
          .right {
            padding-top: 4px;
            display: flex;
            align-items: center;
          }
        }
        .bom-tab {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
      }
      .bom-info-body {
        overflow-y: scroll;
        .average-material {
          margin: 4px 0;
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 30px;
          background: #f5f9ff;
          .left {
            font-size: 14px;
            font-weight: 500;
            color: #354060;
            line-height: 20px;
          }
          .right {
            font-size: 14px;
            font-weight: 500;
            color: #354060;
            line-height: 20px;
            .unit-kg {
              font-size: 12px;
              font-weight: 500;
              color: #178cff;
              line-height: 16px;
              padding: 1px 4px;
              background: #d7ecff;
              border-radius: 2px;
              margin-right: 4px;
            }
            .unit-m {
              font-size: 12px;
              font-weight: 500;
              color: #f49431;
              line-height: 16px;
              background: #ffeddf;
              border-radius: 2px;
              padding: 1px 6px;
              margin-right: 4px;
            }
            .line {
              margin: 0 16px;
              width: 1px;
              height: 14px;
              color: #ccd3e0;
              border-radius: 1px;
            }
            .exclamation-circle {
              color: #cbced8;
              margin-left: 8px;
              font-size: 12px;
              cursor: pointer;
              &:hover {
                color: #138aff;
              }
            }
          }
        }

        .model-name-change {
          width: 32px;
          white-space: nowrap;
          margin-left: 2px;
          padding: 2px 4px;
          background: #e5f0fd;
          border-radius: 10px;
          font-size: 12px;
          color: #138aff;
          line-height: 17px;
          cursor: pointer;
        }
      }
    }
  }

  .bom-zhankai-icon {
    margin-left: 8px;
    font-size: 20px;
  }

  .discharge-right {
    background: #ffffff;
    box-shadow: 0px 2px 4px 1px #e5e6ee;
    border-radius: 4px;
    .discharge-operation-header {
      margin-left: 8px;
      display: flex;
      justify-content: space-between;
      height: 32px;
      white-space: nowrap;
      overflow: hidden;
      .left {
        font-size: 16px;
        font-weight: 500;
        color: #222b3c;
        line-height: 32px;
      }
      .right {
        padding-top: 4px;
        margin-right: 8px;
        display: flex;
        align-items: center;
        > i {
          font-size: 20px;
          margin-left: 6px;
          cursor: pointer;
          color: #54607c;
        }
      }
    }
    .discharge-operation-body {
      margin: 0 8px;
    }
  }

  // 拖拽线
  .line-wrap {
    width: 6px;
    height: 100%;
    display: flex;
    justify-content: center;
    background: #f5f5f5;
    .sider-resize-line {
      height: 100%;
      width: 2px;
      background: #dadce0;
    }
  }
  .line-wrap:hover {
    .sider-resize-line {
      background: #138aff;
    }
  }
}

.red-dot::before {
  content: '*';
  color: red;
  margin: 2px;
}

.remark {
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #455d8b;
  line-height: 20px;
}

.remark-active {
  color: #138aff;
  &:hover {
    color: #ff6263;
  }
}

.io-remark {
  padding: 8px 8px 0 0;
}
.new-spec-container {
  display: flex;
  align-items: center;
  border-top: 1px solid #d4d7dc;
  padding-top: 8px;
  input {
    margin-left: 5px;
    height: 32px;
  }
  a {
    margin: 0 4px;
    display: flex;
    align-items: center;
    word-break: keep-all;
  }
}

.approve_status {
  display: inline-block;
  height: 22px;
  font-size: 14px;
  padding: 0 8px;
  line-height: 22px;
  border-radius: 12px;
  margin-left: 6px;
}

.approve_status0 {
  background: #e7f3fe;
  color: #138aff;
}
.approve_status1 {
  background: #d9f7ef;
  color: #00af7f;
}
.approve_status2 {
  background: #ffe8e4;
  color: #ff4a1d;
}
.approve_status3 {
  background: #e4ebef;
  color: #54607c;
}
.approve_status4 {
  background: #feefe5;
  color: #fb6401;
}

.approve_reason {
  display: flex;
  background: #f1f7ff;
  border-radius: 4px 4px 0px 0px;
  padding: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #fb6401;
  line-height: 24px;
  align-items: center;
  box-shadow: 0 0 4px 0 #d4d9e9;
}

.option_style {
  background: #f1f8ff;
  border-radius: 2px;
  font-size: 14px;
  color: #222b3c;
  width: 30px;
}

.option_style_true {
  border: 1px solid #40aaff;
}
