/*
 * File: user-detail.component.scss
 * Project: elan-web
 * File Created: Wednesday, 24th November 2021 5:48:18 pm
 * Author: liucp
 * Description:
 * -----
 * Last Modified: Thursday, 23rd December 2021 2:02:54 pm
 * Modified By: liucp
 */

.user-detail {
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  height: 24px;
  align-items: center;
  button {
    margin-left: 8px;
  }
}
::ng-deep .ant-btn-dangerous:focus {
  color: #222b3c;
  border-color: #d9d9d9;
}
::ng-deep .ant-btn-dangerous:hover {
  color: #fe5d56;
  background: #fff;
  border-color: #fe5d56;
}
.user-body {
  display: flex;
  justify-content: space-between;
  .user-left {
    flex: 1;
    margin-right: 12px;
    padding: 30px 40px 30px 30px;
    background: #ffffff;
    border-radius: 4px;
    .detail-value {
      display: block;
      font-size: 14px;
      line-height: 32px;
      color: #222b3c;
    }
  }
  .user-right {
    @extend .user-left;
    padding: 16px;
    flex: 0 0 364px;
    margin-right: 0;
    .no-rules {
      display: block;
      margin: 180px auto 0;
      width: 175px;
    }
    .no-desc {
      color: #84879a;
      font-size: 14px;
      line-height: 20px;
      text-align: center;
    }
    .roles-head {
      display: flex;
      justify-content: space-between;
      span {
        display: inline-block;
        margin-right: 24px;
        font-size: 14px;
        color: #515661;
        line-height: 20px;
      }
      .roles-right {
        font-size: 12px;
        line-height: 20px;
        color: #54607c;
      }
    }
  }
  .roles-body {
    position: relative;
    margin-top: 12px;
  }

  .roles-box {
    display: flex;
    .roles-content {
      display: flex;
      align-items: center;
    }
    .roles-num {
      margin: 8px 8px 0 5px;
      width: 32px;
      height: 32px;
      font-size: 16px;
      line-height: 32px;
      color: #fff;
      text-align: center;
      text-align: center;
      background: #4d96ff;
      border-radius: 8px;
      flex-shrink: 0;
    }
    .roles-select {
      width: 160px;
      ::ng-deep app-dynamic-search-select {
        .dy-select {
          width: 100%;
        }
      }
    }
    .roles-icon {
      margin-left: 12px;
    }
    .roles-content {
      margin-bottom: 8px;
      min-width: 250px;
      padding: 8px 16px;
      background: #f5f9ff;
      .read-only {
        display: inline-block;
        font-size: 14px;
        line-height: 32px;
      }
    }
    .roles-handle i {
      margin: 8px 0 0 8px;
      display: block;
      cursor: move;
      width: 32px;
      height: 32px;
      text-align: center;
      background: #edf5ff;
      border-radius: 8px;
      font-size: 16px;
      line-height: 32px;
      color: #69b5ff;
    }
  }
  .info-label {
    width: 120px;
    text-align: right;
  }
  .info-line {
    float: left;
    width: 46%;
    margin-right: 4%;
  }
  .info-line:nth-child(2n) {
    margin-right: 0;
  }
}
.delete-cont {
  text-align: center;
  i {
    color: #fe5d56;
  }
}
.delete-footer {
  margin-top: 16px;
  text-align: right;
}

::ng-deep .modal-outer {
  .ant-modal-content {
    border-radius: 8px;
  }
}

.roles-left {
  display: flex;
  align-items: center;
}

.drag-item-preview {
  display: table;
  vertical-align: middle;
  background-color: #deefff;
  border: 1px solid #4d96ff;
}
