import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LoginComponent } from './login.component';
import { LoginRoutingModule } from './login-routing.module';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { FlUiAngularModule } from 'fl-ui-angular';
import { DirectivesModule } from 'src/app/directive/directives.module';
import { FlcComponentsModule } from 'fl-common-lib';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { HttpClient } from '@angular/common/http';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';

@NgModule({
  declarations: [LoginComponent],
  imports: [
    CommonModule,
    FlcComponentsModule,
    LoginRoutingModule,
    TranslateModule,
    NzButtonModule,
    NzSelectModule,
    NzDropDownModule,
    NzFormModule,
    FormsModule,
    ReactiveFormsModule,
    NzInputModule,
    NzIconModule,
    FlUiAngularModule,
    DirectivesModule,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new MultiTranslateHttpLoader(http, [{ prefix: './assets/i18n/', suffix: '.json' }]);
        },
        deps: [HttpClient],
      },
    }),
  ],
})
export class LoginModule {
  constructor(public translateService: TranslateService) {
    const cl = translateService.currentLang;
    translateService.currentLang = '';
    translateService.use(cl);
  }
}
