<div class="TitleBar">
  <div class="title">
    <label
      [nzDisabled]="!isEdit"
      [ngClass]="{ isDisabled: !isEdit }"
      nz-checkbox
      [(ngModel)]="child.value"
      (ngModelChange)="onStatusChanged($event)"
      >{{ child.name }}</label
    >
  </div>
  <div class="bar">
    <nz-radio-group
      *ngIf="child.value"
      [nzDisabled]="!child.value || !isEdit"
      [ngClass]="{ isDisabled: !isEdit }"
      [(ngModel)]="child.action_type"
      (ngModelChange)="radioChanged($event)">
      <label *ngIf="showEditRoleRadio && (isEdit || child.action_type === 'write')" nz-radio nzValue="write">{{
        translateName + '编辑者' | translate
      }}</label>
      <label *ngIf="isEdit || child.action_type === 'read'" nz-radio nzValue="read">{{ translateName + '查看者' | translate }}</label>
    </nz-radio-group>
  </div>
</div>
<!-- 操作权限 -->
<div *ngIf="child.value && child.action_type === 'write' && showALLDetail" class="buttonContent permission-item">
  <div class="header">
    <div class="left">
      <div class="title">操作权限</div>
    </div>
    <div class="right">
      <label
        *ngIf="isNotEmpty && isEdit"
        [ngClass]="{ isDisabled: !isEdit }"
        [nzDisabled]="!child.value || !isEdit"
        nz-checkbox
        [(ngModel)]="isSelectedAll"
        (ngModelChange)="onUpdateAllChecked()"
        [nzIndeterminate]="indeterminate">
        {{ translateName + '全选' | translate }}
      </label>
    </div>
  </div>
  <div class="content">
    <ng-container *ngFor="let btn of renderBtn">
      <div>
        <label
          [ngClass]="{ isDisabled: !isEdit }"
          [nzDisabled]="!child.value || !isEdit"
          nz-checkbox
          (ngModelChange)="onCheckboxChanged($event, btn)"
          [(ngModel)]="btn.value">
          {{ btn.name }}
        </label>
      </div>
    </ng-container>
    <ng-container *ngIf="renderBtn.length === 0">
      <div [ngClass]="{ isDisabled: !isEdit }">
        <i style="color: #f49231" nz-icon nzType="info-circle" nzTheme="fill"></i> {{ translateName + '无操作按钮可配置' | translate }}
      </div>
    </ng-container>
  </div>
</div>

<!-- 字段编辑权限 -->
<div *ngIf="child.value && child.action_type === 'write' && showALLDetail && renderField?.length > 0" class="buttonContent permission-item">
  <div class="header">
    <div class="left">
      <div class="title">字段编辑权限</div>
    </div>
    <div class="right">
      <label
        *ngIf="fieldIsNotEmpty && isEdit"
        [ngClass]="{ isDisabled: !isEdit }"
        [nzDisabled]="!child.value || !isEdit"
        nz-checkbox
        [(ngModel)]="fieldIsSelectAll"
        (ngModelChange)="updateFieldAllChecked()"
        [nzIndeterminate]="filedIndeterminate">
        {{ translateName + '全选' | translate }}
      </label>
    </div>
  </div>
  <div class="content">
    <ng-container *ngFor="let field of renderField">
      <div>
        <label
          [ngClass]="{ isDisabled: !isEdit }"
          [nzDisabled]="!child.value || !isEdit"
          nz-checkbox
          (ngModelChange)="fieldCheckboxChanged($event, field)"
          [(ngModel)]="field.write_value">
          {{ needTranslate.includes(child.code) ? (child.code + '.' + field.fieldName | translate) : field.fieldName }}
        </label>
      </div>
    </ng-container>
  </div>
</div>

<!-- 价格信息权限 -->
<div *ngIf="child.value && child.action_type === 'write' && canPriceRange && showALLDetail" class="buttonContent permission-item">
  <div class="header">
    <div class="left">
      <div class="title">价格查看权限</div>
    </div>
  </div>
  <div class="content">
    <label nz-checkbox [(ngModel)]="showPrice" [nzDisabled]="!child.value || !isEdit" (ngModelChange)="priceConfigChanged($event)">{{
      translateName + '价格信息' | translate
    }}</label>
  </div>
</div>

<!-- 字段查看权限 -->
<div
  *ngIf="child.value && child.action_type === 'read' && showALLDetail && showReadFieldPermission.includes(child.code)"
  class="buttonContent permission-item">
  <div class="header">
    <div class="left">
      <div class="title">字段查看权限</div>
    </div>
    <div class="right">
      <label
        *ngIf="fieldIsNotEmpty && isEdit"
        [ngClass]="{ isDisabled: !isEdit }"
        [nzDisabled]="!child.value || !isEdit"
        nz-checkbox
        [(ngModel)]="fieldIsSelectAll"
        (ngModelChange)="updateFieldAllChecked()"
        [nzIndeterminate]="filedIndeterminate">
        {{ translateName + '全选' | translate }}
      </label>
    </div>
  </div>
  <div class="content">
    <ng-container *ngFor="let field of renderField">
      <div>
        <label
          [ngClass]="{ isDisabled: !isEdit }"
          [nzDisabled]="!child.value || !isEdit"
          nz-checkbox
          (ngModelChange)="fieldCheckboxChanged($event, field)"
          [(ngModel)]="field.read_value">
          {{ needTranslate.includes(child.code) ? (child.code + '.' + field.fieldName | translate) : field.fieldName }}
        </label>
      </div>
    </ng-container>
  </div>
</div>

<!-- 打样资料包权限 -->
<div *ngIf="child.value && child.action_type === 'write' && showALLDetail && moduleList.length > 0" class="buttonContent permission-item">
  <div class="header">
    <div class="left">
      <div class="title">{{ _packageName() }}资料包权限</div>
      <ng-container *ngIf="isEdit">
        <nz-divider style="margin: 0 4px" nzType="vertical"></nz-divider>
        <div class="link" (click)="openPermissionSetting()">
          权限配置
          <i nz-icon [nzIconfont]="'icon-jiantou'"></i>
        </div>
      </ng-container>
    </div>
    <div class="right">
      <ng-container *ngIf="isEdit">
        <div class="tips">注: 点击标签切换状态</div>
        <label
          [ngClass]="{ isDisabled: !isEdit }"
          [nzDisabled]="!child.value || !isEdit"
          nz-checkbox
          [(ngModel)]="isSelectedAllModule"
          (ngModelChange)="onUpdateAllModuleChecked()"
          [nzIndeterminate]="indeterminateModule">
          {{ translateName + '全选' | translate }}
        </label>
      </ng-container>
    </div>
  </div>
  <div class="content flex-3">
    <ng-container *ngFor="let btn of moduleList">
      <div>
        <label
          [title]="btn.module_name"
          [ngClass]="{ isDisabled: !isEdit }"
          [nzDisabled]="!child.value || !isEdit"
          nz-checkbox
          (ngModelChange)="onModuleCheckboxChanged($event, btn)"
          [(ngModel)]="btn.value">
          {{ btn.module_name }}
        </label>
        <span
          *ngIf="btn.value"
          class="toggle-tag"
          [ngClass]="{ read: btn.permission === 2 }"
          (click)="onToggleModule(btn)"
          [nzTooltipTitle]="isEdit ? (btn.permission === 1 ? '点击切换为只读' : '点击切换为编辑') : null"
          nzTooltipPlacement="top"
          nz-tooltip>
          {{ btn.permission === 1 ? '编辑' : '只读' }}
        </span>
      </div>
    </ng-container>
  </div>
</div>
