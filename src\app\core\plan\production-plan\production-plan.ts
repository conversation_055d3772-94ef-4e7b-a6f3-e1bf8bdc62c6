import { addSeconds, differenceInMilliseconds, isAfter, isBefore, isEqual, subSeconds } from 'date-fns';
import { isNumber, maxBy, minBy, uniqBy } from 'lodash';

export type DimensionType = 'io' | 'po';

export interface SearchOption {
  factoryOptions: { factory_id: any; factory_name: any }[];
  codeOptions: string[];
  materialCodeOptions: string[];
  poOptions: string[];
}

export interface SchedulePlan {
  factory_id: number;
  factory_code: string;
  factory_name: string;
  factory_schedules: FactorySchedules[];
  schedules: Schedules[];
}

export interface FactorySchedules {
  start_time: string | Date;
  end_time: string | Date;
}

export interface Schedules {
  order_code: string;
  po_schedules: PoSchedules[];
}

export interface PoSchedules {
  po_unique_code: string;
  po_code: string;
  start_time: string | Date;
  end_time: string | Date;
  allocated_qty: number;
  output_qty: number;
  scheduled_qty: number;
  overdue: boolean;
  due_date: string | Date;
  style_code: string;
  style_name: string;
  first_style_id: number;
  second_style_id: number;
  order_sam: number;
}

export interface OrderPlanItems {
  order_code: string;
  start_time: string | Date;
  end_time: string | Date;
  allocated_qty: number;
  output_qty: number;
  scheduled_qty: number;
  overdue: boolean;
  style_code: string;
  style_name: string;
  first_style_id: number;
  second_style_id: number;
  order_sam: number;
  po_schedules: PoSchedules[];
}

export class FactoryItem {
  factory_id: number;
  factory_code: string;
  factory_name: string;
  height: number;
  selected: boolean;
  isHighLight: boolean;
  rawData: SchedulePlan;
  schedule_plan_items: PlanGraphItem[];
  order_plan_items: PlanGraphItem[];
  po_plan_items: PlanGraphItem[];
  exitSelected: boolean;

  constructor(rawData: SchedulePlan, graph_color_data: any) {
    this.height = 0;
    this.selected = false;
    this.isHighLight = false;
    this.exitSelected = false;
    this.rawData = rawData;
    this.factory_id = rawData.factory_id;
    this.factory_code = rawData.factory_code;
    this.factory_name = rawData.factory_name;
    this.schedule_plan_items = [];
    this.order_plan_items = [];
    this.po_plan_items = [];
    const colors = ['#FFE3CB', '#C6EDFF', '#C1E8E0'];

    rawData.schedules = (rawData?.schedules ?? [])?.sort((a, b) => {
      const minDataA: any = minBy(a?.po_schedules ?? [], 'start_time');
      const minDataB: any = minBy(b?.po_schedules ?? [], 'start_time');
      let diff: any = differenceInMilliseconds(new Date(minDataA?.start_time), new Date(minDataB?.start_time));
      if (diff === 0) {
        const maxDataA: any = maxBy(a?.po_schedules ?? [], 'end_time');
        const maxDataB: any = maxBy(b?.po_schedules ?? [], 'end_time');
        diff = differenceInMilliseconds(new Date(maxDataA?.end_time), new Date(maxDataB?.end_time));
      }
      return diff;
    });
    rawData.schedules?.forEach((order, index) => {
      const itemColor = colors[index % 3];
      const orderItem: any = {
        plan_type: 1,
        order_code: order?.order_code ?? null,
        start_time: '',
        end_time: '',
        allocated_qty: 0,
        output_qty: 0,
        scheduled_qty: 0,
        overdue: false,
        style_code: '',
        style_name: '',
        po_schedules: [],
        isHeightLight: false,
        is_selected: false,
        first_style_id: null,
        second_style_id: null,
        order_sam: null,
        backgroundColor: null,
        originBackgroundColor: itemColor,
      };
      order?.po_schedules?.forEach((po: PoSchedules) => {
        orderItem.allocated_qty = (orderItem?.allocated_qty ?? 0) + (po?.allocated_qty ?? 0);
        orderItem.output_qty = (orderItem?.output_qty ?? 0) + (po?.output_qty ?? 0);
        orderItem.scheduled_qty = (orderItem?.scheduled_qty ?? 0) + (po?.scheduled_qty ?? 0);
        orderItem.style_code = po.style_code;
        orderItem.style_name = po.style_name;
        orderItem.first_style_id = po.first_style_id;
        orderItem.second_style_id = po.second_style_id;
        orderItem.order_sam = po.order_sam;
        const backgroundColor = this.getColor(orderItem, graph_color_data);
        orderItem.backgroundColor = backgroundColor ?? itemColor;

        orderItem.po_schedules?.push(po);
        if (po?.overdue) {
          orderItem.overdue = true;
        }

        /** 根据交付单的起止时间，获取最早最晚时间，则为订单起止时间 */
        if (orderItem?.start_time) {
          orderItem.start_time = isBefore(new Date(po?.start_time), new Date(orderItem?.start_time))
            ? new Date(po?.start_time)
            : new Date(orderItem?.start_time);
        } else {
          orderItem.start_time = new Date(po?.start_time);
        }

        if (orderItem?.end_time) {
          orderItem.end_time = isAfter(new Date(po?.end_time), new Date(orderItem?.end_time))
            ? new Date(po?.end_time)
            : new Date(orderItem?.end_time);
        } else {
          orderItem.end_time = new Date(po?.end_time);
        }

        this.po_plan_items?.push(
          new PlanGraphItem({
            ...po,
            start_time: new Date(po?.start_time),
            end_time: new Date(po?.end_time),
            plan_type: 2,
            order_code: order?.order_code ?? null,
            backgroundColor: backgroundColor ?? itemColor,
            originBackgroundColor: itemColor,
          })
        );
      });
      this.order_plan_items?.push(new PlanGraphItem(orderItem));
    });

    this.order_plan_items = [...(this.order_plan_items ?? [])]?.sort((a, b) => {
      let diff: any = differenceInMilliseconds(new Date(a?.start_time), new Date(b?.start_time));
      if (diff === 0) {
        diff = differenceInMilliseconds(new Date(a?.end_time), new Date(b?.end_time));
      }
      return diff;
    });

    /** 整合订单进度条，将能首尾相连的进度条进行整合 */
    const merged_order_item: ({ start_time: any; end_time: any } | undefined)[] = [];
    let startTime: any;
    let endTime: any;
    this.order_plan_items?.forEach((order, index: number) => {
      if (index === 0) {
        startTime = new Date(order.start_time);
        endTime = new Date(order.end_time);
      } else {
        if (isAfter(new Date(order?.start_time), new Date(endTime))) {
          merged_order_item.push({
            start_time: startTime,
            end_time: endTime,
          });
          startTime = new Date(order.start_time);
          endTime = new Date(order.end_time);
        } else if (isAfter(new Date(order?.end_time), new Date(endTime))) {
          endTime = new Date(order.end_time);
        }
      }
    });
    if (this.order_plan_items?.length) {
      merged_order_item.push({
        start_time: startTime,
        end_time: endTime,
      });
    }

    /** 将已排程进度条去除订单条 */
    const mergedItem = merged_order_item?.shift();
    this.calcScheduledItem(rawData?.factory_schedules, mergedItem, merged_order_item);
  }

  /** 将已排程进度条去除订单条 */
  calcScheduledItem(arr: any[], mergedItem: any, merged_order_item: any[]) {
    arr?.forEach((fs) => {
      if (mergedItem) {
        if (isBefore(new Date(fs?.end_time), new Date(mergedItem?.end_time))) {
          this.schedule_plan_items?.push(
            new PlanGraphItem({
              start_time: fs?.start_time,
              end_time: fs?.end_time,
              plan_type: 3,
              backgroundColor: '#D4D7DC',
              originBackgroundColor: '#D4D7DC',
            })
          );
        } else {
          let arrs: any[] = [];
          if (isEqual(new Date(fs.start_time), new Date(mergedItem?.start_time))) {
            if (!isEqual(new Date(fs.end_time), new Date(mergedItem?.end_time))) {
              arrs = [{ start_time: addSeconds(new Date(mergedItem?.end_time), 1), end_time: fs?.end_time }];
            }
          } else if (isEqual(new Date(fs.end_time), new Date(mergedItem?.end_time))) {
            arrs = [{ start_time: fs.start_time, end_time: subSeconds(new Date(mergedItem?.start_time), 1) }];
          } else {
            arrs = [
              { start_time: fs.start_time, end_time: subSeconds(new Date(mergedItem?.start_time), 1) },
              { start_time: addSeconds(new Date(mergedItem?.end_time), 1), end_time: fs?.end_time },
            ];
          }
          if (merged_order_item?.length) {
            mergedItem = merged_order_item?.shift();
            this.calcScheduledItem(arrs, mergedItem, merged_order_item);
          } else {
            arrs?.forEach((item) => {
              this.schedule_plan_items?.push(new PlanGraphItem({ ...item, plan_type: 3, backgroundColor: '#D4D7DC' }));
            });
          }
        }
      } else {
        this.schedule_plan_items?.push(
          new PlanGraphItem({
            start_time: fs?.start_time,
            end_time: fs?.end_time,
            plan_type: 3,
            backgroundColor: '#D4D7DC',
            originBackgroundColor: '#D4D7DC',
          })
        );
      }
    });
  }

  getColor(order: any, graph_color_data: any) {
    const colorData = graph_color_data?.find(
      (item: any) => item?.first_style_id === order?.first_style_id && item?.second_style_id === order?.second_style_id
    );
    if (colorData && isNumber(colorData?.simple)) {
      const sam = order?.order_sam ?? 0;
      if (colorData?.simple && sam <= colorData?.simple) {
        return colorData?.simple_color;
      } else if (sam > colorData?.simple && sam <= colorData?.general) {
        return colorData?.general_color;
      } else if (sam > colorData?.general && sam <= colorData?.difficult) {
        return colorData?.difficult_color;
      } else if (sam > colorData?.extremely_difficult) {
        return colorData?.extremely_difficult_color;
      }
    }
    return null;
  }
}

export class PlanGraphItem implements PlanItem {
  plan_type: number; // 订单条：1，交付单条：2，排程条：3
  order_code?: string;
  start_time: string | Date;
  end_time: string | Date;
  allocated_qty?: number;
  output_qty?: number;
  scheduled_qty?: number;
  overdue?: boolean;
  style_code?: string;
  style_name?: string;
  po_schedules?: PoSchedules[];
  po_unique_code?: string;
  po_code?: string;
  po_codes?: any[];
  due_date?: string | Date;
  due_dates?: any[];
  isHighLight: boolean;
  is_selected: boolean;
  left?: number;
  top?: number;
  width?: number;
  backgroundColor?: string;
  originBackgroundColor?: string;
  first_style_id?: number;
  second_style_id?: number;
  order_sam?: number;
  plan_dates?: any[];
  clickable?: boolean;

  constructor(itemData: any) {
    this.clickable = false;
    this.plan_type = itemData.plan_type;
    this.isHighLight = false;
    this.is_selected = false;
    this.plan_dates = [];
    if (this.plan_type === 1) {
      this.po_schedules = itemData.po_schedules;
      this.due_dates = [...(uniqBy(itemData.po_schedules, 'due_date') ?? [])]?.sort((a: any, b: any) => {
        return differenceInMilliseconds(new Date(a?.due_date), new Date(b?.due_date));
      });
      this.po_codes = uniqBy(itemData.po_schedules, 'po_code');
    } else if (this.plan_type === 2) {
      this.po_unique_code = itemData.po_unique_code;
      this.po_code = itemData.po_code;
      this.due_date = itemData.due_date;
    }
    if (this.plan_type !== 3) {
      this.order_code = itemData.order_code;
      this.scheduled_qty = itemData.scheduled_qty;
      this.allocated_qty = itemData.allocated_qty;
      this.output_qty = itemData.output_qty;
      this.overdue = itemData.overdue;
      this.style_code = itemData.style_code;
      this.style_name = itemData.style_name;
      this.first_style_id = itemData.first_style_id;
      this.second_style_id = itemData.second_style_id;
      this.order_sam = itemData.order_sam;
    }
    this.start_time = new Date(itemData.start_time);
    this.end_time = new Date(itemData.end_time);
    this.backgroundColor = itemData?.backgroundColor ?? null;
    this.originBackgroundColor = itemData?.originBackgroundColor ?? null;
  }

  get isSelected(): boolean {
    return this.is_selected;
  }

  setSelected(is_selected: boolean): void {
    this.is_selected = is_selected;
  }
}

export interface PlanItem {
  plan_type: number; // 订单条：1，交付单条：2，排程条：3
  order_code?: string;
  start_time: string | Date;
  end_time: string | Date;
  allocated_qty?: number;
  output_qty?: number;
  scheduled_qty?: number;
  overdue?: boolean;
  style_code?: string;
  style_name?: string;
  po_schedules?: PoSchedules[];
  po_unique_code?: string;
  po_code?: string;
  due_date?: string | Date;
  isHighLight: boolean;
  is_selected: boolean;
  left?: number;
  top?: number;
  width?: number;
  backgroundColor?: string;
  lineIndex?: number;
}
