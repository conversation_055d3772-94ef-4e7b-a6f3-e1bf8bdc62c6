.graph-wrap {
  position: relative;
  width: 100%;
  height: 40px;
  // overflow: hidden;
}

.isSelected {
  background-color: #eaf3ff !important;
}

.isHeightLight {
  background-color: #eaf3ff !important;
}

.day-cell {
  height: 100%;
  border-right: #eff3ff solid 1px;
  border-bottom: #bcc6d0 solid 1px;
}

.day-today {
  height: 100%;
  border-right: 1px solid #2996ff;
}

.isRestDay {
  // background-color: #f6f8fa;
  background-color: #f2f8ff;
  background-size: 25px;
}

.isLastHour {
  border-right-color: #bcc6d0;
}

.isLastDay {
  border-right-color: #bcc6d0;
}

.target-position {
  background-color: #acf5cd !important;
}
