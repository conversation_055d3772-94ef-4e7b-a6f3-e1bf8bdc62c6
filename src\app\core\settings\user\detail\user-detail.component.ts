/*
 * File: user-detail.component.ts
 * Project: elan-web
 * File Created: Wednesday, 24th November 2021 5:48:18 pm
 * Author: liucp
 * Description: 用户详情
 * -----
 * Last Modified: Thursday, 23rd December 2021 4:37:53 pm
 * Modified By: liucp
 */

import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouteReuseStrategy } from '@angular/router';
import { moveItemInArray, CdkDragDrop } from '@angular/cdk/drag-drop';
import { UserService } from '../use.service';
import { finalize } from 'rxjs/operators';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { Subject } from 'rxjs';
import { NzMessageService } from 'ng-zorro-antd/message';
import { AppStorageService } from 'src/app/shared/app-storage.service';
import { FlcRouteReuseStrategy } from 'fl-common-lib';
import { factoryCodes } from 'src/app/shared/common-config';

@Component({
  selector: 'app-user-detail',
  templateUrl: './user-detail.component.html',
  styleUrls: ['./user-detail.component.scss'],
})
export class UserDetailComponent implements OnInit {
  factoryCodes = factoryCodes || [];
  ownRole = true; // 是否是本人
  isCancel = false; // 取消弹窗
  isBack = false; // 返回确定事件
  loading = false; // 是否加载中
  detail: any = {
    login_name: '',
    name: '',
    phone: '',
    status: '启用',
    roles: [],
  }; // 详情数据
  isEdit = false; // 是否可以编辑
  isNew = false; // 是否是新建跳转过来的
  isEditCommit = false; // 是否编辑提交
  isNewCommit = false; // 是否新增提交
  id: any; // 操作用户id
  selectIndex: number | undefined; // 当前选中的下拉列表
  passwordForm!: FormGroup; // 密码表单
  accountForm!: FormGroup; // 操作用户表单
  resetPassword = false; // 重置密码弹窗
  deleUser = false; // 是否展示删除弹窗
  rolesList: any = [{}]; // 详情数据
  allRolesList: any = []; // 所有权限列表
  orginRoles: any = []; // 原始权限数据
  bodyHeight = 0; // 页面高度
  passwordVisible = false;
  passwordComVisible = false;
  passwordNewVisible = false;
  factory_code = localStorage?.getItem('factory_code') || '';
  detailFrom: any[] = [
    { label: '用户编码', code: 'login_name', type: 'input', maxLength: 16, required: true, visible: true },
    {
      label: '用户名称',
      code: 'name',
      type: 'input',
      maxLength: this.factoryCodes.includes(this.factory_code) ? 100 : 16,
      required: true,
      visible: true,
    },
    { label: '初始密码', code: 'password', type: 'password', maxLength: 16, required: true, visible: true },
    { label: '邮箱', code: 'email', type: 'input', maxLength: 100, required: false, visible: true },
    { label: '状态', code: 'status', type: 'switch', maxLength: 16, required: true, visible: true },
  ];
  searchCon = new Subject<{
    limit: number;
    page: number;
    column: any;
    value: any;
  }>();
  searchConValue = {
    limit: 100,
    page: 1,
    column: '',
    value: '',
  };
  btnArr: string[] = [];
  translateName = 'user.';
  lang = localStorage.getItem('lang');

  constructor(
    private _router: Router,
    private fb: FormBuilder,
    private _route: ActivatedRoute,
    private _service: UserService,
    private cdr: ChangeDetectorRef,
    private notification: NzNotificationService,
    private message: NzMessageService,
    private _routeReuseStrategy: RouteReuseStrategy,
    public _storage: AppStorageService
  ) {}

  ngOnInit(): void {
    this.id = this._route.snapshot.paramMap.get('id');
    this.formVaild();
    this.btnArr = this._storage.getUserActions('settings/user');
    if (this.id == 'new') {
      this.isNew = true;
      return;
    }
    if (this.id) {
      this.getDetail(this.id);
    }
  }
  ngAfterViewInit() {
    this.bodyHeight = window.innerHeight - 120;
    this.cdr.detectChanges();
  }

  formVaild() {
    const namePattern = '[\u4e00-\u9fa5A-Za-z0-9 ]*';
    const codePattern = '[A-Za-z0-9 ]*';
    this.accountForm = this.fb.group({
      name: [
        null,
        [
          Validators.required,
          Validators.maxLength(this.factoryCodes.includes(this.factory_code) ? 100 : 16),
          Validators.pattern(namePattern),
        ],
      ],
      login_name: [
        null,
        [Validators.required, Validators.maxLength(100), Validators.pattern(codePattern)],
        [this._service.uniqueValidator(this.detail?.login_name || null)],
      ],
      password: [null, [Validators.required, Validators.maxLength(16)]],
      status: [null, [Validators.required]],
      email: [null, [Validators.maxLength(100)]],
    });
  }
  /**
   * 编辑事件
   */
  edit() {
    this.isEdit = true;
    if (this.rolesList.length === 0) {
      this.rolesList = [{}];
    }
    this.formVaild();
    this.accountForm.get('password')?.clearValidators();
    this.accountForm.get('password')?.updateValueAndValidity();
  }
  /**
   * 获取详情数据
   * @param  {number} id
   */
  getDetail(id: number) {
    return new Promise<void>((resolve) => {
      this._service
        .getUserDetail(id)
        .pipe(finalize(() => (this.loading = false)))
        .subscribe((res) => {
          if (res.code === 200) {
            this.detailFrom.forEach((item: any) => {
              if (item.code == 'password') {
                item.visible = false;
              }
            });
            this.orginRoles = JSON.parse(JSON.stringify(res.data.roles)); // 原始数据
            this.rolesList = res.data.roles;
            res.data.status = res.data.status === 1 ? '启用' : '停用';
            this.detail = res.data;
          }
          resolve();
        });
    });
  }

  /**
   * 权限行拖拽改变顺序
   * @param event
   */
  drop(event: CdkDragDrop<string[]>) {
    moveItemInArray(this.rolesList, event.previousIndex, event.currentIndex);
  }
  /**
   * 点击取消确定事件
   */
  cancelOk() {
    this.isBack = true;
    this.isCancel = false;
    if (this.isNew) {
      this.ownRole = false;
      this.back();
    }
    if (this.isEdit) {
      this.isEdit = false;
      this.getDetail(this.id);
    }
  }
  canLeave() {
    if (this.canCancel() && this.ownRole) {
      return false;
    } else {
      return true;
    }
  }
  canCancel() {
    if (this.isNew) {
      // 判断数据是否有变化
      if (this.accountForm?.dirty || this.rolesList.length > 1 || JSON.stringify(this.rolesList[0]) !== '{}') {
        return true;
      } else {
        return false;
      }
    }
    if (this.isEdit) {
      const newIds = this.orginRoles.map((item: any) => item.role_id); // 原始权限列表的id
      const lastIds = this.rolesList.map((item: any) => item.role_id); // 最后数据
      if (this.accountForm?.dirty || (JSON.stringify(newIds) !== JSON.stringify(lastIds) && lastIds[0] !== undefined)) {
        return true;
      } else {
        return false;
      }
    }
    return false;
  }
  /**
   * 点击取消事件
   */
  cancel() {
    this.isNew ? this.back() : (this.isEdit = false);
  }
  /**
   * 点击重置密码
   */
  password() {
    this.resetPassword = true;
    this.passwordNewVisible = false;
    this.passwordComVisible = false;
    this.passwordForm = this.fb.group({
      password: ['', [Validators.required]],
      confirm: ['', [this.confirmValidator]],
    });
  }
  confirmValidator = (control: FormControl): { [s: string]: boolean } => {
    if (!control.value) {
      return { error: true, required: true };
    } else if (control.value !== this.passwordForm.controls.password.value) {
      return { confirm: true, error: true };
    }
    return {};
  };
  /**
   * 校验确认密码
   */
  validateConfirmPassword(): void {
    setTimeout(() => this.passwordForm.controls.confirm.updateValueAndValidity());
  }
  /**
   * 更新并返回列表
   */
  updateList() {
    (this._routeReuseStrategy as FlcRouteReuseStrategy).clearCachedRoute('/settings/user/list');
    setTimeout(() => {
      this._router.navigate(['/settings/user/list']);
    }, 0);
  }
  /**
   * 返回事件
   */
  back() {
    if (this.isEditCommit || this.isNewCommit) {
      (this._routeReuseStrategy as FlcRouteReuseStrategy).clearCachedRoute('/settings/user/list');
    }
    this._router.navigate(['/settings/user/list']);
  }
  /**
   * 点击删除事件
   */
  deleUserInfo() {
    this.deleUser = true;
  }
  /**
   * 删除用户
   */
  deleUserOk() {
    this._service.deleteUser(this.id).subscribe((res) => {
      if (res.code === 200) {
        this.deleUser = false;
        this.message.create('success', this._service.translateValue(this.translateName + '删除用户成功'));
        this.updateList();
      }
    });
  }
  /**
   * 提交事件
   */
  submit() {
    if (this.accountForm.valid) {
      const commitRoles: any = [];
      const ids: any = [];
      this.rolesList.filter((item: any) => {
        if (item.role_id) {
          ids.push(item.role_id);
        }
      });
      const idsSet: any = new Set(ids);
      if (idsSet.size !== ids.length) {
        this.notification.create('error', this._service.translateValue(this.translateName + '权限名称有重复，请调整～'), '');
        return;
      } else {
        [...idsSet].forEach((item: any, i: number) => {
          commitRoles.push({ role_id: item, priority: i + 1 });
        });
      }
      // 状态改为number类型：1启用 2停用
      const { status } = this.accountForm.value;
      this.accountForm.patchValue({
        status: status ? 1 : 0,
      });
      // 编辑的情况
      if (this.isEdit) {
        delete this.accountForm.value.password;
        let data = { ...this.accountForm.value };
        const newIds = this.orginRoles.map((item: any) => item.role_id); // 原始权限列表的id
        if (JSON.stringify(newIds) !== JSON.stringify([...idsSet])) {
          // 判断是否有改变
          data = { ...this.accountForm.value, ...{ roles: commitRoles } };
        }
        this._service.editUserDetail(this.id, data).subscribe(async (res) => {
          if (res.code === 200) {
            this.message.create('success', this._service.translateValue('success.submit'));
            this.ownRole = false;
            await this.getDetail(res.data);
            this.ownRole = true;
            this.isEdit = false;
            this.isEditCommit = true;
            this._service.detailUpdate = true;
          }
        });
      }
      // 新建操作用户
      if (this.isNew) {
        this._service.newUser({ ...this.accountForm.value, ...{ roles: commitRoles } }).subscribe(async (res) => {
          if (res.code === 200) {
            this.message.create('success', this._service.translateValue(this.translateName + '新建操作用户成功'));
            this.ownRole = false;
            await this.getDetail(res.data);
            this.ownRole = true;
            this.isNew = false;
            this.isNewCommit = true;
            this._service.detailUpdate = true;
            this.id = res.data;
            this._router.navigate(['/settings/user/list', res.data]);
          }
        });
      }
    } else {
      Object.values(this.accountForm.controls).forEach((control) => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }
  /**
   * 重置密码确定
   */
  passwordOk() {
    if (this.passwordForm.valid) {
      this.resetPassword = false;
      const { password } = this.passwordForm.value;
      this._service.resetPassword(this.id, { new_password: password }).subscribe((res) => {
        if (res.code === 200) {
          this.message.create('success', this._service.translateValue(this.translateName + '重置密码成功'));
        }
      });
    } else {
      Object.values(this.passwordForm.controls).forEach((control) => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }
  /**
   * 删除权限行
   */
  deleteRoles(i: number) {
    this.rolesList.splice(i, 1);
  }

  /**
   * 新增权限行
   */
  addRoles(i: any) {
    this.rolesList.splice(i + 1, 0, {});
  }
}
