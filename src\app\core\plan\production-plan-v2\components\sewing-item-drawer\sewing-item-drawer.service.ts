import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Injectable } from '@angular/core';
import { ResultData } from '../../model/production-plan.interface';
import { SewingDetailResponse } from '.';

@Injectable()
export class SewingItemDrawerService {
  temp_session = '';
  constructor(private http: HttpClient) {}

  getLineDetail(param: { group_id: number }): Observable<ResultData<SewingDetailResponse>> {
    return this.http.post<ResultData<SewingDetailResponse>>('/service/procurement-inventory/plan/v1/view-detail', param);
  }

  /**
   * 修改sam或目标效率
   * @param payload
   * @returns
   */
  modifySamOrEffi(payload: { group_id: number; average_daily_production?: number; efficiency?: number }): Observable<any> {
    return this.http.post('/service/procurement-inventory/plan/v1/view-detail-update', payload);
  }
}
