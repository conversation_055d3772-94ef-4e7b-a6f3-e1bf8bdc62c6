import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { getSysParamsConfig, needStringfiedKeys } from '../../sys-parameters.config';
import { SysParametersService } from '../../sys-parameters.service';

@Component({
  selector: 'app-sys-parameters-form',
  templateUrl: './sys-parameters-form.component.html',
  styleUrls: ['./sys-parameters-form.component.scss'],
})
export class SysParametersFormComponent implements OnInit, OnChanges {
  @Input() isEditMode = false;
  @Input() paramsData: Record<string, any> = {};

  paramsForm!: FormGroup;

  constructor(private fb: FormBuilder, private _service: SysParametersService) {}
  chekcedPriceTemplateOptions: { label: string; value: any }[] = [];

  translateSuffix = 'sysSetting.sysParameters.';

  paramsFormConfig = getSysParamsConfig();
  compareFn = (o1: any, o2: any): boolean => (o1 && o2 ? Number(o1) === Number(o2) : o1 === o2);

  priceEvaluationChanged(event: any) {
    const template = this.paramsForm.get('sample_type_auto_gen_rule');
    if (this.isEditMode && event === '3') {
      template?.enable();
    } else {
      template?.disable();
    }
  }
  ngOnInit() {
    this.getOptions();
  }
  getOptions() {
    this._service.getCheckPriceTemplateOption().subscribe((res) => {
      if (res.code === 200) {
        this.chekcedPriceTemplateOptions = res.data.option_list;
      }
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes && changes.paramsData) {
      this.initFormGroup();
    }
    if (changes && changes.isEditMode) {
      this.updateEditMode();
    }
  }

  initFormGroup() {
    if (!this.paramsForm) {
      const ctrlConfig: any = {};
      this.paramsFormConfig.forEach((formCfg) => {
        formCfg.children.forEach((paramCfg) => {
          if (paramCfg.fieldKey != '') {
            const required = !needStringfiedKeys.includes(paramCfg.fieldKey as string);
            paramCfg.fieldKey && (ctrlConfig[paramCfg.fieldKey] = this.fb.control(null, required ? [Validators.required] : null));
          }
        });
      });
      this.paramsForm = this.fb.group(ctrlConfig);
      this.paramsForm.disable();
    }
    this.updateFieldsValue();
  }

  updateFieldsValue() {
    if (this.paramsForm && this.paramsData) {
      for (const key in this.paramsData) {
        if (key === 'delivery_qty_is_required') {
          this.paramsForm.get(key)?.setValue(this.paramsData.delivery_dimension === '1' ? '1' : this.paramsData[key]);
        } else {
          this.paramsForm.get(key)?.setValue(this.paramsData[key] ?? null);
        }
      }
      this.paramsForm.markAsPristine();
    }
  }

  updateEditMode() {
    if (this.paramsForm) {
      this.isEditMode ? this.paramsForm.enable() : this.paramsForm.disable();
    }
    const template = this.paramsForm.get('sample_type_auto_gen_rule');
    if (this.isEditMode && this.paramsForm.value.price_evaluation_auto_gen_rule === '3') {
      template?.enable();
    } else {
      template?.disable();
    }
  }

  get dirty() {
    return this.paramsForm.dirty;
  }

  get valid() {
    return this.paramsForm.valid;
  }

  get formData() {
    return this.paramsForm.value;
  }
}
