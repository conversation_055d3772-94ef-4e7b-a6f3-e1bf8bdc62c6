import { HttpClient } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import { ProductionReportRoutingModule } from './production-report-routing.module';

@NgModule({
  declarations: [],
  imports: [
    ProductionReportRoutingModule,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new MultiTranslateHttpLoader(http, [{ prefix: './assets/i18n/production-report/components/', suffix: '.json' }]);
        },
        deps: [HttpClient],
      },
    }),
  ],
})
export class ProductionReportModule {
  constructor(public translateService: TranslateService) {
    const cl = translateService.currentLang;
    translateService.currentLang = '';
    translateService.use(cl);
  }
}
