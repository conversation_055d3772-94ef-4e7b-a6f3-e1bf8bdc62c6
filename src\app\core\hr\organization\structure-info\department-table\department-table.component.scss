.depart-table-outer {
  border-top: 1px dashed #d4d7dc;
  padding: 8px 0px;
  .depart-table-header {
    display: flex;
    align-items: baseline;
    justify-content: space-between;
    & > div:nth-child(1) {
      font-size: 16px;
      font-weight: 500;
      color: #515661;
    }
    & > div:nth-child(2) {
      display: flex;
      align-items: center;
      gap: 16px;
      a {
        padding: 0px;
      }
      nz-divider {
        margin: 0px;
      }
    }
  }
  .quit td {
    color: #97999c;
  }

  .roles {
    display: inline-block;
    font-size: 12px;
    line-height: 24px;
    color: #515661;
    padding: 0 8px;
    background: #f1f3f6;
    border-radius: 2px;
    margin: 6px 12px 6px 0;
  }

  .quit td .roles {
    color: #97999c;
  }

  .op-td {
    // display: flex;
    gap: 10px;
    align-items: center;
    justify-content: center;
    .delete-btn:hover {
      color: #fe655f;
    }
  }
}
.roles {
  display: inline-block;
  font-size: 12px;
  line-height: 24px;
  color: #515661;
  padding: 0 8px;
  background: #f1f3f6;
  border-radius: 2px;
  margin: 6px 12px 6px 0;
}
