import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { FlcLeaveGuard } from 'fl-common-lib';
import { AssignRoleComponent } from './assign-role/assign-role.component';
import { RoleManagerComponent } from './role-manager/role-manager.component';
const routes: Routes = [
  { path: 'user', loadChildren: () => import('./user/user.module').then((m) => m.UserModule) },
  { path: 'role', component: RoleManagerComponent, canDeactivate: [FlcLeaveGuard] },
  { path: 'assignRole', component: AssignRoleComponent, canDeactivate: [FlcLeaveGuard] },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class SettingsRoutingModule {}
