export enum OrderStatus {
  all = 0,
  toSubmit = 1, // 待提交
  toAudit = 2, // 待审核
  toModify = 4, // 待修改
  toModifyAudit = 9, // 修改待审核
  modifyAuditReturn = 10, //修改审核未通过
  cancelled = 8, // 已取消
  auditPass = 11, // 审核通过
}
// 生产类型，1 成衣加工 2 二次工艺外发, 3 只分发自己不做生产
export enum ProductionType {
  'garments' = 1,
  'extra' = 2,
  'orther' = 3,
}
/* 检验方式 */
export enum CheckTypeEnum {
  allCheck = 1, // 全检
  partCheck = 2, // 抽检
  noCheck = 3, // 免检
}

// 打样单操作按钮
export enum SampleOperateBtnEnum {
  modify = 'modify', // 退回修改
  pass = 'pass', // 审核通过
  outGoing = 'outGoing', // 审核外发工厂通过
  assignPrinter = 'assignPrinter', // 指派
  complete = 'complete',
  assignEmployee = 'assignEmployee', // 指派负责人
  assignMerOrQc = 'assignMerOrQc', // 指派跟单员/QC
  oneClickInbound = 'oneClickInbound', // 一键入库,
  onAdvancePayment = 'onAdvancePayment', // 预付款
  export = 'export', // 导出
}

export enum SampleOperateLabelEnum {
  pass = '审核通过',
  modify = '退回修改',
  assignPrinter = '指派',
  outGoing = '外发工厂',
  complete = '批量完成',
}

// 指派类型
export enum AssignTypeEnum {
  AssignEmployee = 1, // 指派负责人
  AssignMerchandiser = 2, // 指派跟单员
  AssignQC = 3, // 指派QC
  AssignMerOrQc = 4, // 指派跟单员/QC
}
