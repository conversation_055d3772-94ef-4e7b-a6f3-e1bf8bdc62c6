<div *ngIf="detailInfo" class="accept-info">
  <div class="base-info">
    <form nz-form nzLayout="vertical" class="style-picture-wrap">
      <nz-form-item>
        <nz-form-label>款式图：</nz-form-label>
        <nz-form-control>
          <flc-image-gallery [fileList]="detailInfo['order_pictures']" [isSignatureMode]="false"></flc-image-gallery>
        </nz-form-control>
      </nz-form-item>
    </form>

    <form nz-form>
      <div nz-row>
        <div nz-col *ngFor="let item of baseConfig" [nzSpan]="item.span || 8">
          <nz-form-item>
            <nz-form-label class="labelStyle">{{ item.label }}</nz-form-label>
            <nz-form-control>
              <ng-container *ngIf="item.type === 'text'">
                <flc-text-truncated [data]="detailInfo[item.key]"></flc-text-truncated>
              </ng-container>
              <ng-container *ngIf="item.type === 'date'">
                <flc-text-truncated data="{{ detailInfo[item.key] | date: 'yyyy/MM/dd' }}"></flc-text-truncated>
              </ng-container>
              <ng-container *ngIf="item.type === 'datetime'">
                <flc-text-truncated data="{{ detailInfo[item.key] | date: 'yyyy/MM/dd HH:mm:ss' }}"></flc-text-truncated>
              </ng-container>

              <ng-container *ngIf="item.type === 'file'">
                <flc-file-gallery *ngIf="detailInfo[item.key]?.length" [fileList]="detailInfo[item.key] || []"></flc-file-gallery>
                <flc-text-truncated *ngIf="!detailInfo[item.key]?.length" [data]="null"></flc-text-truncated>
              </ng-container>

              <ng-container *ngIf="item.type === 'color-size'">
                <flc-color-size-table *ngIf="detailInfo.io_lines.length" [dataList]="detailInfo.io_lines"></flc-color-size-table>
                <flc-text-truncated *ngIf="!detailInfo.io_lines.length" [data]="null"></flc-text-truncated>
              </ng-container>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>
    </form>
  </div>

  <nz-divider nzText="面辅料及其他要求"></nz-divider>

  <div class="remark-list-info">
    <div nz-row nzType="flex" *ngFor="let item of remarkConfig">
      <div nz-col nzSpan="24">
        <nz-form-item>
          <nz-form-label class="labelStyle" nzSpan="2">{{ item.label }}</nz-form-label>
          <nz-form-control nzSpan="21">
            <div class="reqInfo-info">{{ detailInfo[item.key] }}</div>
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>
  </div>
</div>
