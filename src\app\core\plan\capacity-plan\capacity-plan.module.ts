import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzIconService, NzIconModule } from 'ng-zorro-antd/icon';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzMessageModule } from 'ng-zorro-antd/message';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CapacityPlanComponent } from './capacity-plan.component';
import { DistributeOrderGraphComponent } from './distribute-order-graph/distribute-order-graph.component';
import { TranslateModule, TranslateLoader, TranslateService } from '@ngx-translate/core';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import { FlcComponentsModule } from 'fl-common-lib';
import { HttpClient } from '@angular/common/http';
import { ComponentsModule } from '../../../components/components.module';

const zorroModule = [
  NzSelectModule,
  NzDatePickerModule,
  NzIconModule,
  NzGridModule,
  NzInputModule,
  NzMessageModule,
  NzButtonModule,
  NzDividerModule,
  NzToolTipModule,
  NzSpinModule,
];

@NgModule({
  declarations: [CapacityPlanComponent, DistributeOrderGraphComponent],
  imports: [
    CommonModule,
    zorroModule,
    FlcComponentsModule,
    ComponentsModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new MultiTranslateHttpLoader(http, [
            { prefix: './assets/i18n/elan-components/', suffix: '.json' },
            { prefix: './assets/i18n/plan/', suffix: '.json' },
          ]);
        },
        deps: [HttpClient],
      },
    }),
  ],
  providers: [TranslateService],
})
export class CapacityPlanModule {
  constructor(public translateService: TranslateService, private iconService: NzIconService) {
    // 切换lang必须刷新页面，模块级别i18n文件才能成功加载
    const dl = translateService.defaultLang;
    translateService.defaultLang = '';
    translateService.setDefaultLang(dl);

    this.iconService.fetchFromIconfont({
      scriptUrl: 'https://at.alicdn.com/t/font_2782026_gkrcufahyqc.js',
    });
  }
}
