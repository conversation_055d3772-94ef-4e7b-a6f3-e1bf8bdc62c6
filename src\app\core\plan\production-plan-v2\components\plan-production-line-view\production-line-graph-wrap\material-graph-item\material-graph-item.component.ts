import { formatNumber } from '@angular/common';
import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
  LOCALE_ID,
  Inject,
  AfterViewInit,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { addMinutes, differenceInMinutes, endOfDay, isAfter, isBefore, isWithinInterval, subMinutes, startOfDay } from 'date-fns';
import { interval, Subscription } from 'rxjs';
import {
  GanttCellWidth,
  PlanAlertStatusEnum,
  PlanGraphEvent,
  PlanGraphItem,
  PlanGraphOperationTypeEnum,
  PlanMovedParam,
  PlanRightClickParam,
} from '../../../../interface';
import { ProductionPlanShareService } from '../../../../production-plan-share.service';
@Component({
  selector: 'app-material-graph-item',
  templateUrl: './material-graph-item.component.html',
  styleUrls: ['./material-graph-item.component.scss'],
})
export class MaterialGraphItemComponent implements OnInit, AfterViewInit, OnChanges {
  @Input() isEdit = false;
  @Input() graphOptions: { view: 'order' | 'productionLine'; item_dimension: 'io' | 'po' } = {
    view: 'order',
    item_dimension: 'io',
  };
  @Input() rest_list: number[] | Date[] = []; // 休息日
  @Input() data!: PlanGraphItem;
  @Input() options!: {
    signalWidth: GanttCellWidth;
    dayWidth: GanttCellWidth; // 日维度一天48，小时维度一天24*12
    wrapBodyWidth: number;
    dimension: string;
    per1Px: number;
  };
  @Input() showTag = {
    overdue: false, // 已逾期
    behind_schedule: false, // 进度落后
    over_deadline: false, // 计划超客期
  };
  @Output() hasMove = new EventEmitter<PlanMovedParam>();
  @Output() tapGraphItem = new EventEmitter<PlanGraphItem & PlanGraphEvent>();
  @Output() rightClickGraph = new EventEmitter<PlanRightClickParam>();
  @Output() hoverGraphItem = new EventEmitter<PlanGraphItem>();
  @Output() leaveGraphItem = new EventEmitter();
  @Output() dropGraphItem = new EventEmitter<DragEvent>();
  @ViewChild('graphItem') graphItem!: ElementRef<HTMLElement>;
  originClientX = 0;
  isDraging = false;
  pressBar: 'left' | 'right' | null = null;

  scrolling$?: Subscription;
  isScrolling = false;
  scrollingInterval = 50;
  scrollingStep = 10;
  scrollEdgeWidth = 48;
  wrapBodyDiv: any = null;
  wrapBodyLeft = 0;
  wrapBodyRight = 0;
  wrapbodyScrollLeft = 0;

  planAlertStatusEnum = PlanAlertStatusEnum;

  get backGroundColor() {
    if (!this.data.rawData?.can_edit) {
      return '#d3dde6';
    }
    if (this.data?.rawData?.isSelected) {
      return '#EEF7FF';
    }
    return this.data?.backgroundColor ? this.data.backgroundColor : '#ffe3cb';
  }

  constructor(@Inject(LOCALE_ID) public locale: string, public _sharedService: ProductionPlanShareService) {}
  ngAfterViewInit(): void {
    this.data.graphRef = this.graphItem.nativeElement;
  }

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {}

  tapGraph(e: MouseEvent, ref?: HTMLElement | ElementRef) {
    if (e.button !== 0) {
      return;
    }
    e.preventDefault();
    e.stopPropagation();
    this.tapGraphItem.emit({ ...this.data, ref, x: e?.x, y: e?.y });
  }

  getPlanMovedParam(operation_type: PlanGraphOperationTypeEnum): PlanMovedParam {
    return {
      target_time: this.data.endTime,
      production_line_no: this.data?.rawData?.production_line_no,
      factory_code: this.data.rawData?.factory_code,
      group_id: this.data.group_id,
      operation_type,
    };
  }
  pressLeft(e: MouseEvent) {
    if (e.button !== 0) {
      return;
    }
    this.pressBar = 'left';
    this.originClientX = e.clientX;
    this.isDraging = true;
    this.updateCurrentWrapBody();
    e.stopPropagation();
    document.addEventListener('mousemove', this.moveLeft);
    // 注册在捕获阶段
    document.addEventListener('mouseup', this.removeHandler, true);
  }

  releaseLeft(e: MouseEvent) {
    this.pressBar = null;
    e.stopPropagation();
    document.removeEventListener('mousemove', this.moveLeft);
    this.hasMove.emit(this.getPlanMovedParam(PlanGraphOperationTypeEnum.movePosition));
  }

  moveLeft = (e: MouseEvent) => {
    e.stopPropagation();
    window.requestAnimationFrame(() => {
      if (e.clientX < this.wrapBodyLeft + this.scrollEdgeWidth) {
        // 滚动到左边缘的处理
        if (!this.isScrolling && this.wrapbodyScrollLeft > 0) {
          this.isScrolling = true;
          this.scrolling$ = interval(this.scrollingInterval).subscribe(() => {
            let deltaX = this.scrollingStep;
            if (this.wrapbodyScrollLeft - this.scrollingStep < 0) {
              deltaX = this.wrapbodyScrollLeft;
            }
            this.setGraphItemStartTime(this.data.width + deltaX);
            this.stepScrollToLeft();
          });
        }
      } else if (e.clientX >= this.wrapBodyRight - this.scrollEdgeWidth) {
        // 滚动到右边缘的处理
        if (!this.isScrolling && this.wrapbodyScrollLeft + this.wrapBodyDiv.offsetWidth < this.wrapBodyDiv.scrollWidth) {
          this.isScrolling = true;
          this.scrolling$ = interval(this.scrollingInterval).subscribe(() => {
            let deltaX = this.scrollingStep;
            if (this.wrapbodyScrollLeft + this.scrollingStep > this.wrapBodyDiv.scrollWidth - this.wrapBodyDiv.offsetWidth) {
              deltaX = this.wrapBodyDiv.scrollWidth - this.wrapBodyDiv.offsetWidth - this.wrapbodyScrollLeft;
            }
            this.setGraphItemStartTime(this.data.width - deltaX);
            this.stepScrollToRight();
          });
        }
      } else {
        this.stopScrolling();
      }

      const deltaX = e.clientX - this.originClientX;
      this.originClientX = e.clientX;
      this.setGraphItemStartTime(this.data.width - deltaX);
    });
  };

  setGraphItemStartTime(newWidth: number) {
    const deltaX = newWidth - this.data.width;
    this.graphItem.nativeElement.style.width = newWidth + 'px';
    this.graphItem.nativeElement.style.left = this.graphItem.nativeElement.offsetLeft - deltaX + 'px';

    const deltaMinute = this.graphItem.nativeElement.offsetWidth * this.options.per1Px;
    this.data.width = this.graphItem.nativeElement.offsetWidth;
    this.data.startTime = subMinutes(this.data.endTime, deltaMinute).getTime();
  }

  pressRight(e: MouseEvent) {
    if (e.button !== 0) {
      return;
    }
    this.pressBar = 'right';
    this.originClientX = e.clientX;
    this.isDraging = true;
    this.updateCurrentWrapBody();
    e.stopPropagation();
    document.addEventListener('mousemove', this.moveRight);
    // 注册在捕获阶段
    document.addEventListener('mouseup', this.removeHandler, true);
  }

  releaseRight(e: MouseEvent) {
    this.pressBar = null;
    e.stopPropagation();
    document.removeEventListener('mousemove', this.moveRight);
    this.hasMove.emit(this.getPlanMovedParam(PlanGraphOperationTypeEnum.moveLength));
  }

  moveRight = (e: MouseEvent) => {
    e.stopPropagation();
    window.requestAnimationFrame(() => {
      if (e.clientX >= this.wrapBodyRight - this.scrollEdgeWidth) {
        // 滚动到右边缘的处理
        if (!this.isScrolling && this.wrapbodyScrollLeft + this.wrapBodyDiv.offsetWidth < this.wrapBodyDiv.scrollWidth) {
          this.isScrolling = true;
          this.scrolling$ = interval(this.scrollingInterval).subscribe(() => {
            let deltaX = this.scrollingStep;
            if (this.wrapbodyScrollLeft + this.scrollingStep > this.wrapBodyDiv.scrollWidth - this.wrapBodyDiv.offsetWidth) {
              deltaX = this.wrapBodyDiv.scrollWidth - this.wrapBodyDiv.offsetWidth - this.wrapbodyScrollLeft;
            }
            this.setGraphItemEndTime(this.data.width + deltaX);
            this.stepScrollToRight();
          });
        }
      } else if (e.clientX < this.wrapBodyLeft + this.scrollEdgeWidth) {
        // 滚动到左边缘的处理
        if (!this.isScrolling && this.wrapbodyScrollLeft > 0) {
          this.isScrolling = true;
          this.scrolling$ = interval(this.scrollingInterval).subscribe(() => {
            const deltaX = this.scrollingStep;
            if (this.wrapbodyScrollLeft - this.scrollingStep < 0) {
              const deltaX = this.wrapbodyScrollLeft;
            }
            this.setGraphItemEndTime(this.data.width - deltaX);
            this.stepScrollToLeft();
          });
        }
      } else {
        this.stopScrolling();
      }

      const deltaX = e.clientX - this.originClientX;
      this.originClientX = e.clientX;
      this.setGraphItemEndTime(this.data.width + deltaX);
    });
  };

  // 获取最新的wrapbody信息
  updateCurrentWrapBody() {
    this.wrapBodyDiv = document.getElementsByClassName('wrap-body')[0];
    this.wrapbodyScrollLeft = this.wrapBodyDiv.scrollLeft;
    const rect = this.wrapBodyDiv.getBoundingClientRect();
    this.wrapBodyLeft = rect.left;
    this.wrapBodyRight = rect.right;
  }

  // 设置拖动计划条结束时间后的长度
  setGraphItemEndTime(newWidth: number) {
    this.graphItem.nativeElement.style.width = newWidth + 'px';
    const deltaMinute = this.graphItem.nativeElement.offsetWidth * this.options.per1Px;
    this.data.width = this.graphItem.nativeElement.offsetWidth;
    this.data.endTime = addMinutes(this.data.startTime, deltaMinute).getTime();
  }

  // 向左滚动一次
  stepScrollToLeft() {
    if (this.wrapbodyScrollLeft - this.scrollingStep < 0) {
      this.wrapbodyScrollLeft = 0;
      this.scrolling$?.unsubscribe();
      this.isScrolling = false;
    } else {
      this.wrapbodyScrollLeft -= this.scrollingStep;
    }
    this.wrapBodyDiv.scroll({ left: this.wrapbodyScrollLeft });
  }

  // 向右滚动一次
  stepScrollToRight() {
    if (this.wrapbodyScrollLeft + this.scrollingStep > this.wrapBodyDiv.scrollWidth - this.wrapBodyDiv.offsetWidth) {
      this.wrapbodyScrollLeft = this.wrapBodyDiv.scrollWidth - this.wrapBodyDiv.offsetWidth;
      this.scrolling$?.unsubscribe();
      this.isScrolling = false;
    } else {
      this.wrapbodyScrollLeft += this.scrollingStep;
    }
    this.wrapBodyDiv.scroll({ left: this.wrapbodyScrollLeft });
  }

  // 停止滚动
  stopScrolling() {
    if (this.isScrolling) {
      this.isScrolling = false;
      this.scrolling$?.unsubscribe();
    }
  }

  removeHandler = (e: MouseEvent) => {
    this.stopScrolling();

    this.isDraging = false;
    e.stopPropagation();
    this.pressBar = null;
    this.hasMove.emit(
      this.getPlanMovedParam(this.pressBar === 'left' ? PlanGraphOperationTypeEnum.movePosition : PlanGraphOperationTypeEnum.moveLength)
    );
    document.removeEventListener('mousemove', this.moveLeft);
    document.removeEventListener('mousemove', this.moveRight);
    // document.removeEventListener('mousemove', this.moveWhole);
    // 移除也要在捕获阶段
    document.removeEventListener('mouseup', this.removeHandler, true);
  };

  handleRightClickGraph(e: MouseEvent) {
    e.stopPropagation();
    e.preventDefault();
    this.rightClickGraph.emit({
      postion: 'graphItem',
      productionLineNo: this.data.rawData?.production_line_no,
      ref: this.graphItem,
      rawData: this.data.rawData,
      x: e?.x,
      y: e?.y,
      event: e,
      width: this.data?.width,
    });
  }

  onMouseEnter(e: MouseEvent) {
    this.hoverGraphItem.emit(this.data);
  }

  calTotalTime(start: number, end: number) {
    const targetStartTime = new Date(start);
    const targetEndTime = new Date(end);
    const rawMinutes = differenceInMinutes(targetEndTime, targetStartTime);
    const needSubtractMinutes: number[] = [];
    this.rest_list.forEach((item) => {
      const startTime = startOfDay(new Date(item));
      const endTime = endOfDay(new Date(item));
      // 开始时间在进度条内，包括边界
      const startInItem: boolean = isWithinInterval(startTime, { start: targetStartTime, end: targetEndTime });
      // 结束时间在进度条内，包括边界
      const endInItem: boolean = isWithinInterval(endTime, { start: targetStartTime, end: targetEndTime });

      if (startInItem && endInItem) {
        // 休息日开始时间和结束时间均在进度条内
        needSubtractMinutes.push(differenceInMinutes(endTime, startTime));
      } else if (startInItem) {
        // 休息日开始时间在进度条内，结束不在
        if (isAfter(endTime, targetEndTime)) {
          // 结束时间在进度条结束之后
          // 休息日前部分包含在进度条内
          needSubtractMinutes.push(differenceInMinutes(targetEndTime, startTime));
        }
      } else if (endInItem) {
        // 休息日结束时间在进度条内，开始不在
        if (isBefore(startTime, targetStartTime)) {
          // 开始时间在进度条开始之前
          // 休息日后部分包含在进度条内
          needSubtractMinutes.push(differenceInMinutes(endTime, targetStartTime));
        }
      } else {
        // 休息日开始时间和结束时间均不在进度条内
        if (!isBefore(targetStartTime, startTime) && !isAfter(targetEndTime, endTime)) {
          // 进度条开始时间不在休息日开始时间之前，进度条结束时间不在休息日结束时间之后
          // 进度条被休息日包含在内
          needSubtractMinutes.push(differenceInMinutes(targetEndTime, targetStartTime));
        }
      }
    });
    const totalMinutes = rawMinutes - needSubtractMinutes.reduce((a, b) => a + b, 0);
    const day = Math.floor(totalMinutes / (24 * 60));
    const remainderMinutes = totalMinutes % (24 * 60);
    const hour = remainderMinutes / 60;
    let time = '';
    if (day > 0) {
      time = time + day + '天';
    }
    if (hour > 0) {
      time = time + formatNumber(hour, this.locale, '1.0-2') + '小时';
    }
    return time;
  }

  /** 整体移动进度条 */
  handleDropGraphItem(event: DragEvent) {
    this.isDraging = false;
    this.dropGraphItem.emit(event);
  }

  onDragStart(event: DragEvent) {
    this.isDraging = true;
  }

  onDragEnd(event: DragEvent) {
    this.isDraging = false;
  }

  showOrderTooltip() {
    if (!this.isDraging) {
      return true;
    } else {
      return false;
    }
  }

  showPoTooltip() {
    if (!this.isDraging) {
      return true;
    } else {
      return false;
    }
  }

  /** 鼠标移出进度条 */
  leaveGraph(e: Event) {
    this.leaveGraphItem.emit();
  }

  // /**
  //  * 将输入色与灰色（128，128，128）进行对比
  //  * 当color值大于128时,color值偏向255,即#ffffff,此时字体颜色应为#000000
  //  * 当color值小于128时,color值偏向0,即#000000,此时字体颜色应为#ffffff
  //  * @param colorStr 输入颜色
  //  * @returns 返回的反差色（黑或白）
  //  */
  // getContrastColor(colorStr: string) {
  //   if (!colorStr) {
  //     return '#000000';
  //   }
  //   // 将十六进制颜色码转为rgb颜色值
  //   const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(colorStr);
  //   const rgbArr = result ? [parseInt(result[1], 16), parseInt(result[2], 16), parseInt(result[3], 16)] : null;

  //   const color: boolean = rgbArr ? 0.213 * rgbArr[0] + 0.715 * rgbArr[1] + 0.072 * rgbArr[2] > 255 / 2 : true;
  //   return color ? '#000000' : '#ffffff';
  // }
}
