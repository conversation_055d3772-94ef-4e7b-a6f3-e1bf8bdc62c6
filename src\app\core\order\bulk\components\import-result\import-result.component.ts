import { Component, Input, OnInit } from '@angular/core';

import { BulkService } from '../../bulk.service';

@Component({
  selector: 'app-import-result',
  templateUrl: './import-result.component.html',
  styleUrls: ['./import-result.component.scss'],
})
export class ImportResultComponent implements OnInit {
  @Input() dataList: any[] = [];
  translateName = 'bulk.';
  tableHeader = [
    {
      label: '交付单号',
      key: 'po_unique_code',
      visible: true,
      width: '140px',
      type: 'text',
      isHidePin: true,
      pinned: true,
      disable: false,
    },
    { label: '款式编码', key: 'style_code', visible: true, width: '140px', type: 'text', pinned: false, disable: false },
    { label: '颜色', key: 'color_name', visible: true, width: '140px', type: 'text', pinned: false, disable: false },
    { label: '新建/更新', key: 'handle_type', visible: true, width: '100px', type: 'text', pinned: false, disable: false },
    { label: '导入结果', key: 'result_type', visible: true, width: '80px', type: 'quantity', pinned: false, disable: false },
    { label: '失败原因', key: 'reason', visible: true, width: '200px', type: 'text', pinned: false, disable: false },
  ];

  tableList: any = [];

  constructor(private _service: BulkService) {}

  ngOnInit(): void {}
}
