.outer-container {
  display: flex;
  flex-direction: column;

  .search-item-container {
    display: flex;
    align-items: center;

    .search-name::after {
      content: '：';
    }
  }
  .nodes-item {
    width: 240px;
    border-radius: 4px;
    display: inline-flex;
    flex-direction: column;
    justify-content: flex-start;
    font-size: 12px;
    position: relative;
    padding: 4px 12px;
    row-gap: 6px;
    flex-shrink: 0;
    flex-grow: 0;
    .title {
      display: inline-block;
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      width: 4px;
      border-radius: 8px 0 0 8px;
    }
    & > span {
      text-align: center;
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
  }
}

.table-container {
  margin-top: 8px;
  flex: auto;
  background: #ffffff;
  border-radius: 12px 12px 4px 4px;
  padding: 10px 16px;
}

.processs-body {
  display: flex;
  flex-direction: column;
  justify-content: center;
  & > div {
    margin: 10px 0;
  }
}

:host ::ng-deep {
  .table-container {
    td:has(flc-table-body-render-image) {
      padding: 4px 0;

      .cornersign {
        width: 55px !important;

        .td-img {
          width: 55px !important;
          height: 55px !important;
          background: #fff;
        }

        .img-num {
          transform: scale(0.85);
        }
      }
    }
  }

  .blue-link {
    text-decoration: underline;
    text-underline-offset: 3px;
    cursor: pointer;
    color: #138aff;
    &:hover {
      opacity: 0.8;
    }
  }
}
.change-text {
  color: #138aff;
  font-size: 11px;
  cursor: pointer;
}
.material-info {
  font-size: 12px;
  margin-top: -10px &-status {
    color: #515665;
    margin-bottom: 4px &-value {
      padding: 0px 4px;
      background-color: #f0f3fa;
      border-radius: 2px;
    }
  }
}
.preview-img {
  padding: 0;
  height: unset;
  line-height: unset;
}

.event-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
  background: #e7f3fe;
  padding: 10px 20px;

  .item {
    display: flex;
    flex: 1;
    column-gap: 10px;
  }
}
::ng-deep {
  .product-table-event-confirm-modal {
    .ant-cascader {
      width: 100%;
    }
    .ant-select {
      width: 100%;
    }
    .ant-table-wrapper {
      max-height: 600px;
      overflow-y: scroll;
    }
    .ant-modal-footer {
      justify-content: center;
    }
    .normal-input-group {
      .ant-input-group-addon {
        color: transparent;
      }
    }
    .ant-table-tbody > tr > td {
      height: unset;
      max-height: unset;
      padding: 0;
    }
    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      border: none;
    }
    .ant-select-arrow {
      color: transparent;
    }

    .ant-input-number {
      width: unset;
      border: unset;
      border-radius: unset;
    }
    .ant-input-number-input {
      text-align: left;
    }
    .ant-input {
      border: unset;
      border-radius: unset;
    }
    .ant-input-group-addon {
      border: unset;
    }
    .ant-select-single.ant-select-show-arrow .ant-select-selection-item,
    .ant-select-single.ant-select-show-arrow .ant-select-selection-placeholder {
      padding-right: 0;
    }

    .ant-modal-body {
      padding: 0;
    }

    .delete-icon {
      color: red;
    }
    .delete-icon.disable {
      color: grey;
    }
  }
}

.urgent-tip {
  font-size: 10px;
  position: absolute;
  top: 0px;
  right: 0px;
  background: #f74949;
  color: white;
  padding: 0 3px;
  border-radius: 0 0 0 5px;
}

.custom-input-group-addon {
  color: #222b3c;
  font-weight: normal;
  font-size: 14px;
  text-align: center;
  align-content: center;
  background-color: #fafafa;
  transition: all 0.3s;
  width: 30px;
  height: 30px;
}

.custom-input-group-plain {
  padding: 0 11px;
  align-content: center;
}
.action-td {
  display: flex;
  align-items: center;
  justify-content: center;
  column-gap: 8px;
  .ant-btn {
    padding: 0;
  }
}

.plan-tag,
.actual-tag {
  font-size: 12px;
  width: 44px;
  border-radius: 16px;
  text-align: center;
  margin-right: 3px;
}

.plan-tag {
  background: #e7f3fe;
  color: #138aff;
}

.actual-tag {
  background: #ffe8d9;
  color: #fb6401;
}

.node-field-name {
  padding: 0px 4px;
  background-color: #f0f3fa;
  border-radius: 2px;
  text-align: left;
  white-space: nowrap;
}

.custom-node-field-name {
  font-size: 12px;
  display: inline-flex;
}

.node-field-value {
  font-size: 12px;
  white-space: nowrap;
}

.node-field-line {
  flex-wrap: wrap;
  color: #515665;
}
