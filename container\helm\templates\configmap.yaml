apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "elan-web.fullname" . }}
  labels:
    {{- include "elan-web.labels" . | nindent 4 }}
data:
  frontend.conf: |
    upstream elan_backend_service {
      server {{ .Values.elanBackendService }} fail_timeout=30 max_fails=3;
    }

    upstream backend_common_service {
      server {{ .Values.backendCommonService }}:8000 fail_timeout=30 max_fails=3;
    }

    upstream elan_mobile_service {
      server {{ .Values.elanMobileService }} fail_timeout=30 max_fails=3;
    }

    {{- if .Values.standard_dashboard }}   
    upstream standard-dashboard {
      server elan-standard-dashboard:8000 fail_timeout=30 max_fails=3;
    }
    {{- end }}

    server {
      listen      {{ .Values.service.port }};
      server_name {{ range .Values.elanDomainNames }}{{ . }} {{ end }};
      charset utf-8;

      add_header Access-Control-Allow-Origin *;

      root /frontend/elan-web;
      index index.html index.htm;
      try_files $uri $uri/ /index.html;
      client_max_body_size  20m;

      gzip on;
      gzip_disable "msie6";
      gzip_vary on;
      gzip_min_length 1k;
      gzip_proxied any;
      gzip_comp_level 6;
      gzip_buffers 16 8k;
      gzip_http_version 1.1;
      gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

      location ^~/elan/ {
        proxy_http_version 1.1;

        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Auth-Type elan;
        proxy_read_timeout 300s;
        proxy_connect_timeout 300s;

        proxy_pass http://elan_backend_service;
      }

      location ^~/service/ {
        proxy_http_version 1.1;

        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Auth-Type elan;
        proxy_read_timeout 300s;
        proxy_connect_timeout 300s;

        proxy_pass http://backend_common_service;
      }
   
      location ^~/service/procurement-inventory/openapi/ {
        proxy_http_version 1.1;

        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Auth-Type signature;
        proxy_read_timeout 300s;
        proxy_connect_timeout 300s;

        proxy_pass http://backend_common_service;
      }

      location ^~/service/archive/v1/api/factory/sync {
        proxy_http_version 1.1;

        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Auth-Type signature;
        proxy_read_timeout 300s;
        proxy_connect_timeout 300s;

        proxy_pass http://backend_common_service;
      }

    {{- if  .Values.dataadapter }} 
      location ^~/data-adapter {
        proxy_http_version 1.1;

        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Auth-Type signature;
        proxy_read_timeout 300s;
        proxy_connect_timeout 300s;

        proxy_pass http://data-adapter;
      }
    {{- end }}   

  {{- if .Values.standard_dashboard }}   
      location ^~/standard-dashboard/ {
        proxy_http_version 1.1;

        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Auth-Type signature;
        proxy_read_timeout 300s;
        proxy_connect_timeout 300s;

        proxy_pass http://standard-dashboard/;
      }
    {{- end }}

      location ^~/h5/ {
        proxy_http_version 1.1;

        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Auth-Type elan;
        proxy_read_timeout 300s;
        proxy_connect_timeout 300s;

        proxy_pass http://elan_mobile_service/;
      }

      location ~* \.(png|jpg|jpeg|gif|ico|woff|otf|ttf|eot|svg|txt|pdf|docx?|xlsx?)$ {
          access_log off;
          add_header Pragma public;
          add_header Cache-Control "public";
          add_header Vary "Accept-Encoding";
      }

      location ~* \.(js|css)$ {
          access_log off;
          add_header Pragma public;
          add_header Cache-Control "public";
          add_header Vary "Accept-Encoding";
      }

    }
