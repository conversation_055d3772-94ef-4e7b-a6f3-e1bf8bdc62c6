<div nz-row class="outsourcing-basicinfo-container">
  <div class="main-content">
    <div nz-row>
      <ng-container *ngFor="let item of basicInfoConfig">
        <div nz-col [nzSpan]="item.itemSpan">
          <nz-form-item>
            <nz-form-label>{{ item.label | translate }}</nz-form-label>
            <nz-form-control>
              <flc-text-truncated *ngIf="item.type === 'text'" [data]="_basicInfo[item.key]"></flc-text-truncated>
              <ng-container *ngIf="item.type === 'file'">
                <ng-container *ngIf="_basicInfo.appendix_requirements?.length; else fileTpl">
                  <flc-file-gallery [wrap]="false" [fileList]="_basicInfo.appendix_requirements"></flc-file-gallery>
                </ng-container>
                <ng-template #fileTpl>
                  <span style="color: #b5b8bf">-</span>
                </ng-template>
              </ng-container>

              <div *ngIf="['fast_reply', 'third_quality_check', 'customs_clearance'].includes(item.key)">
                <flc-text-truncated [data]="['', '是', '否'][_basicInfo?.[item.key] ?? 0]"></flc-text-truncated>
              </div>
              <div *ngIf="item.key === 'quality_level'">
                <flc-text-truncated
                  [data]="['', '高（精品货）', '中（一般品牌货）', '低（市场货）'][_basicInfo?.[item.key] ?? 0]"></flc-text-truncated>
              </div>

              <div *ngIf="item.key === 'payment_condition'">
                <flc-text-truncated [data]="_basicInfo?.payment_condition"></flc-text-truncated>
              </div>
            </nz-form-control>
          </nz-form-item>
        </div>
      </ng-container>
    </div>
  </div>
  <div class="img-gallery">
    <flc-image-gallery [maxSize]="20" [ngModel]="_basicInfo.order_pictures"></flc-image-gallery>
  </div>
</div>
