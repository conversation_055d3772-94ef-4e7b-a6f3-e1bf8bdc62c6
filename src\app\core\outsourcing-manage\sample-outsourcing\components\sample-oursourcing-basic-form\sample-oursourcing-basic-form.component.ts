import { Component, Input, OnInit, SimpleChanges } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { NzCascaderOption } from 'ng-zorro-antd/cascader';
import { SampleOutsourcingService } from '../../sample-outsourcing.service';
import { SampleOutsouringStatusEnum } from '../../modal/sample-outsourcing.enum';
import { initBasicFormConfig } from '../../modal/sample-outsourcing.config';
import { IDetailModel, IFormartDetailModel } from '../../modal/sample-outsourcing.interface';
import { RecommendFactoryService } from 'fl-sewsmart-lib/recommend-factory';
import { NzNotificationService } from 'ng-zorro-antd/notification';

@Component({
  selector: 'app-sample-oursourcing-basic-form',
  templateUrl: './sample-oursourcing-basic-form.component.html',
  styleUrls: ['./sample-oursourcing-basic-form.component.scss'],
})
export class SampleOursourcingBasicFormComponent implements OnInit {
  @Input() formInfo: Partial<IFormartDetailModel> = {};
  @Input() simpleData: Partial<IDetailModel> = {};
  @Input() isEdit = false;
  basicFormConfig = initBasicFormConfig();
  translateName = 'sampleOutsourcing.sampleOutsourcingTable.';
  sampleBasicForm: FormGroup = this._fb.group({});
  addressOption: NzCascaderOption[] = [];
  sample_sort_num?: number | null;
  statusEnum = SampleOutsouringStatusEnum; // 打样外发单状态枚举
  constructor(
    private _fb: FormBuilder,
    private _service: SampleOutsourcingService,
    private _notifyService: NzNotificationService,
    private _recommendFactoryService: RecommendFactoryService
  ) {}

  ngOnInit() {
    this.getFactoryList();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes && changes.formInfo && changes.formInfo.currentValue && this.isEdit) {
      this.initBasicFormConfig(changes.formInfo.currentValue);
      this.initBasicForm();
      this.resetFormStatus();
      if (this.addressOption.length === 0) this.initAddress();
      this.setFormData();
      const _data = changes.formInfo.currentValue;
      this.sample_sort_num = _data.sample_sort_num;
    }
    if (changes && changes.isEdit && changes.isEdit.currentValue === false) {
      this.basicFormConfig = initBasicFormConfig();
    }
  }

  onSelectSearch(e: any, item: any) {
    if (item.type === 'select') {
      item.labelKey && this.sampleBasicForm.get(item.labelKey)?.setValue(e?.selectLine?.label || null);
      if (item.key === 'factory_id') {
        this.sampleBasicForm.get(item.subkey)?.setValue(e?.selectLine?.ss_factory_code || null);
      } else {
        item.subkey && this.sampleBasicForm.get(item.subkey)?.setValue(e?.selectLine?.subkey || null);
      }
    } else if (item.type === 'localSelect') {
      const _label = e === 1 ? '包工包料' : e === 2 ? '来料加工' : e === 3 ? '包工半包料' : null;
      item.labelKey = this.sampleBasicForm.get(item.labelKey)?.setValue(_label);
    }
    if (item.key === 'sample_order_id') {
      this._service.changeSampleOrderEvent.emit(e.value);
      this.sample_sort_num = e.selectLine?.sample_sort_num || null;
    }
  }

  /**
   * 初始化表格
   */
  private initBasicForm() {
    this.basicFormConfig.forEach((item: any) => {
      const _control = new FormControl();
      item.required && _control.addValidators(Validators.required);
      this.sampleBasicForm.addControl(item.key, _control);
      item.labelKey && this.sampleBasicForm.addControl(item.labelKey, new FormControl());
      item.subkey && this.sampleBasicForm.addControl(item.subkey, new FormControl());
    });
  }

  private initBasicFormConfig(formInfo: Partial<IFormartDetailModel>) {
    this.basicFormConfig.forEach((item, index) => {
      item.readOnly = index < 3 && formInfo.status === SampleOutsouringStatusEnum.wait_outsourcing_send;
    });
  }

  handleValue() {
    const _value = this.sampleBasicForm.getRawValue();
    const _addressOption = this.getSelectOption(this.addressOption, _value.area);
    delete _value.area;
    if (_addressOption.length > 0) {
      return {
        ..._value,
        country_id: _addressOption[0]?.value,
        country: _addressOption[0]?.label,
        province_id: _addressOption[1]?.value,
        province: _addressOption[1]?.label,
        city_id: _addressOption[2]?.value,
        city: _addressOption[2]?.label,
        district_id: _addressOption[3]?.value,
        district: _addressOption[3]?.label,
      };
    }
    return _value;
  }

  /**
   * 赋值
   */
  private setFormData() {
    this.basicFormConfig.forEach((item: any) => {
      if (item.key === 'area') {
        let _value = [this.formInfo.country_id, this.formInfo.province_id, this.formInfo.city_id, this.formInfo.district_id];
        _value = _value.filter((area) => area != null);
        this.sampleBasicForm.get(item.key)?.setValue(_value);
        item.subkey && this.sampleBasicForm.get(item.subkey)?.setValue(this.formInfo[item.subkey] || null);
        return;
      }
      item.subkey && this.sampleBasicForm.get(item.subkey)?.setValue(this.formInfo[item.subkey] || null);
      this.sampleBasicForm.get(item.key)?.setValue(this.formInfo[item.key]);
      item.labelKey && this.sampleBasicForm.get(item.labelKey)?.setValue(this.formInfo[item.labelKey] || null);
    });
    if (this.formInfo.status === SampleOutsouringStatusEnum.wait_order) {
      this.sampleBasicForm.get('sample_order_id')?.disable();
    }
  }

  /**
   * 获取省市区
   */
  private initAddress(): void {
    if (this.addressOption.length !== 0) return;
    this._service.getAddress().subscribe((res) => {
      this.addressOption = this.handleTransOption(res.data.children ?? []);
    });
  }

  /**
   * 处理is_leaf为isLeaf
   */
  private handleTransOption(value: any) {
    if (value.length) {
      value.forEach((node: any) => {
        node['isLeaf'] = node.is_leaf;
        if (node?.children && node?.children?.length && !node?.is_leaf) {
          this.handleTransOption(node?.children);
        }
      });
      return value;
    }
  }

  // 获取选中的option
  getSelectOption(optionList: NzCascaderOption[], value: any, level = 0, selectOptions: NzCascaderOption[] = []) {
    const _option = optionList.find((item) => item.value === (Array.isArray(value) ? value[level] : value));
    _option && selectOptions.push(_option);
    if (_option && _option.children && _option.children.length) this.getSelectOption(_option.children, value, level + 1, selectOptions);
    return selectOptions;
  }

  resetFormStatus() {
    const _form = this.sampleBasicForm;
    Object.keys(_form.controls).forEach((key) => {
      _form.controls[key].markAsPristine();
      _form.controls[key].markAsUntouched();
      _form.controls[key].updateValueAndValidity();
    });
  }

  isRecommendLoading = false;
  async onSelectFactroy() {
    const _value = this.sampleBasicForm.getRawValue();
    if (!_value.sample_order_id || !_value.process_type) {
      this._notifyService.error('', '请先选择打样单号和外发类型');
      return;
    }

    this.isRecommendLoading = true;
    await new Promise((resolve) => setTimeout(resolve, 1000));
    this.isRecommendLoading = false;

    const _payload = {
      extra: {
        style_class: this.simpleData.first_style_name
          ? [this.simpleData.first_style_name, this.simpleData.second_style_name, this.simpleData.third_style_name]
          : [],
        product_type: this.simpleData.production_category_name,
        quality_level: this.simpleData.quality_level,
        process_type: _value.process_type === 1 ? '包工包料' : _value.process_type === 2 ? '来料加工' : '包工半包料',
        customer_brand: this.simpleData.brand_name,
      },
    };
    if (!(await this._recommendFactoryService.checkRecommendRules(_payload, 2))) return;
    this._recommendFactoryService
      .openDrawer({ isMultiple: false, orderType: 2 }, _payload, [this.sampleBasicForm.get('factory_name')?.value])
      .subscribe((res: any) => {
        if (res) {
          this.sampleBasicForm.get('factory_id')?.setValue(res.id);
          this.sampleBasicForm.get('factory_name')?.setValue(res.factory_name);
          this.sampleBasicForm.get('factory_code')?.setValue(res.ss_factory_code);
        }
      });
  }

  factoryList: any[] = [];
  private getFactoryList() {
    this._service.getFactoryList().subscribe((res) => {
      this.factoryList = res.data.option_list;
    });
  }
}
