<div
  #graphItem
  class="graph-item"
  [ngStyle]="{
    left: data.left + 'px',
    top: data.top + 'px',
    width: data.width + 'px'
  }"
  [ngClass]="{
    isDraging: isDraging,
    isSelected: data.rawData?.isSelected ?? false,
    isHeightLight: data.rawData?.isHeightLight || data.is_pre_order === _sharedService.searchData.is_pre_order,
    disabled: !data.rawData?.can_edit,
    isSewingHover: data.rawData?.isHover || (data.publish_status === 1 && isEdit)
  }"
  nz-tooltip
  (mouseup)="tapGraph($event, graphItem)"
  (mouseleave)="leaveGraph($event)"
  (mouseenter)="onMouseEnter($event)"
  (contextmenu)="!data.rawData?.can_edit ? null : handleRightClickGraph($event)"
  [nzTooltipPlacement]="'top'"
  [nzTooltipOverlayClassName]="'graph-item-tooltip'"
  [nzTooltipTitle]="showOrderTooltip() ? titleTemplate : null">
  <div class="operator-wrap">
    <div
      ganttDragItem
      (dragend)="onDragEnd($event)"
      (dragstart)="onDragStart($event)"
      (drop)="handleDropGraphItem($event)"
      [dragTitle]="data.title"
      [dragData]="data"
      [dragDisabled]="!isEdit || data.disabled"
      class="operator-title"
      [ngStyle]="{
        background: data.disabled ? '#d3dde6' : data.rawData?.isSelected ? '#2996ff' : backGroundColor
      }">
      <div class="alter-status-tag">
        <i nz-icon class="sample-circle" *ngIf="data.tags.includes(planAlertStatusEnum.overdue) && showTag?.overdue">
          <svg>
            <path
              d="M32 32m360 0l240 0q360 0 360 360l0 240q0 360-360 360l-240 0q-360 0-360-360l0-240q0-360 360-360Z"
              fill="#FFFFFF"
              p-id="8186"></path>
            <path
              d="M92 92m300 0l240 0q300 0 300 300l0 240q0 300-300 300l-240 0q-300 0-300-300l0-240q0-300 300-300Z"
              fill="#FF4141"
              p-id="8187"></path>
            <path
              d="M563.6 262.28l-266.16 276.6c-7.44 7.98-9.3 15.6-5.58 22.74 5.58 10.74 17.58 12.72 24.66 12.72h125.58l-68.94 174.72c-2.76 9.24-1.14 16.32 4.92 21.24 9 7.32 25.02 13.86 38.16 2.46 8.76-7.56 114.84-106.02 318.18-295.38 6.48-10.26 7.32-18.72 2.4-25.26-4.8-6.6-14.4-9.48-28.56-8.76L576.92 442.4l36-156c0.84-15.6-3.9-25.62-14.4-30.12-10.44-4.44-22.08-2.4-34.86 6z"
              fill="#FFFFFF"
              p-id="8188"></path>
          </svg>
        </i>
        <i nz-icon class="sample-circle" *ngIf="data.tags.includes(planAlertStatusEnum.backward) && showTag?.behind_schedule">
          <svg>
            <path
              d="M32 32m360 0l240 0q360 0 360 360l0 240q0 360-360 360l-240 0q-360 0-360-360l0-240q0-360 360-360Z"
              fill="#FFFFFF"
              p-id="8318"></path>
            <path
              d="M92 92m300 0l240 0q300 0 300 300l0 240q0 300-300 300l-240 0q-300 0-300-300l0-240q0-300 300-300Z"
              fill="#FF8014"
              p-id="8319"></path>
            <path d="M272 512m-60 0a60 60 0 1 0 120 0 60 60 0 1 0-120 0Z" fill="#FFFFFF" p-id="8320"></path>
            <path d="M512 512m-60 0a60 60 0 1 0 120 0 60 60 0 1 0-120 0Z" fill="#FFFFFF" p-id="8321"></path>
            <path d="M752 512m-60 0a60 60 0 1 0 120 0 60 60 0 1 0-120 0Z" fill="#FFFFFF" p-id="8322"></path>
          </svg>
        </i>
        <i nz-icon class="sample-circle underlying-icon" *ngIf="data.tags.includes(planAlertStatusEnum.surpass) && showTag?.over_deadline">
          <svg>
            <path
              d="M32 32m360 0l240 0q360 0 360 360l0 240q0 360-360 360l-240 0q-360 0-360-360l0-240q0-360 360-360Z"
              fill="#FFFFFF"
              p-id="8452"></path>
            <path
              d="M92 92m300 0l240 0q300 0 300 300l0 240q0 300-300 300l-240 0q-300 0-300-300l0-240q0-300 300-300Z"
              fill="#7975F6"
              p-id="8453"></path>
            <path d="M332 392m-60 0a60 60 0 1 0 120 0 60 60 0 1 0-120 0Z" fill="#FFFFFF" p-id="8454"></path>
            <path d="M692 392m-60 0a60 60 0 1 0 120 0 60 60 0 1 0-120 0Z" fill="#FFFFFF" p-id="8455"></path>
            <path
              d="M512 542c102.36 0 162.36 60 180 180h-60c-11.76-79.98-51.78-120-120-120s-108.24 40.02-120 120H332c17.64-120 77.64-180 180-180z"
              fill="#FFFFFF"
              p-id="8456"></path>
          </svg>
        </i>
      </div>
      <div class="graph-item-text">
        <div>{{ data.title || '-' }}</div>
      </div>
    </div>
    <ng-container *ngIf="data.rawData?.can_edit && isEdit">
      <div (mousedown)="pressRight($event)" (mouseup)="releaseRight($event)" class="drag-handler">
        <i nz-icon class="drag-icon drag-icon-right">
          <svg width="7px" height="18px" viewBox="0 0 360 1024" nz-tooltip>
            <path
              fill="#BABCD3"
              d="m258.44749,1l-256,0l0,1024l256,0a102.4,102.4 0 0 0 102.4,-102.4l0,-819.2a102.4,102.4 0 0 0 -102.4,-102.4z" />
            <path
              fill="#FFFFFF"
              d="m104.84749,205.8m25.6,0l0,0q25.6,0 25.6,25.6l0,563.2q0,25.6 -25.6,25.6l0,0q-25.6,0 -25.6,-25.6l0,-563.2q0,-25.6 25.6,-25.6z" />
            <path
              fill="#FFFFFF"
              d="m207.24749,205.8m25.6,0l0,0q25.6,0 25.6,25.6l0,563.2q0,25.6 -25.6,25.6l0,0q-25.6,0 -25.6,-25.6l0,-563.2q0,-25.6 25.6,-25.6z" />
          </svg>
        </i>

        <div class="time-tip time-tip-right">
          {{ (data.endTime | date: 'MM/dd HH:mm') || '-' }}
        </div>
      </div>
    </ng-container>
  </div>
</div>

<ng-template #titleTemplate let-thing>
  <div>
    <div class="tooltip-item">
      <div *ngIf="data.tags.includes(planAlertStatusEnum.overdue)" style="color: #ff4141">{{ '已逾期' }}</div>
      <div *ngIf="data.tags.includes(planAlertStatusEnum.backward)" style="color: #ff8014">{{ '进度落后' }}</div>
      <div *ngIf="data.tags.includes(planAlertStatusEnum.surpass)" style="color: #7975f6">计划超客期</div>
      <div style="white-space: break-spaces; word-break: break-all">IO:{{ data.order_code || '-' }}</div>
      <div style="color: #ff8014" *ngIf="data.is_pre_order">预排单</div>
      <div>订单件数：{{ data.rawData?.order_qty | number }}</div>
      <div>{{ '产出/分配' }}：{{ data.rawData?.sewing_qty | number }}/{{ data.rawData?.allocated_qty | number }}</div>
      <div>交期：{{ data.due_times }}</div>
      <ng-container *ngIf="data.last_publish_user">
        <div>修改人：{{ data.last_publish_user }}</div>
        <div>最近修改时间：{{ data.last_publish_time | date: 'yyyy/MM/dd HH:mm:ss' }}</div>
      </ng-container>
    </div>
  </div>
</ng-template>
