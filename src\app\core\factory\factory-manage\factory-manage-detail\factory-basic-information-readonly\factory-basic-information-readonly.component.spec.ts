import { ComponentFixture, TestBed } from '@angular/core/testing';

import { FactoryBasicInformationReadonlyComponent } from './factory-basic-information-readonly.component';

describe('FactoryBasicInformationReadonlyComponent', () => {
  let component: FactoryBasicInformationReadonlyComponent;
  let fixture: ComponentFixture<FactoryBasicInformationReadonlyComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [FactoryBasicInformationReadonlyComponent],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(FactoryBasicInformationReadonlyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
