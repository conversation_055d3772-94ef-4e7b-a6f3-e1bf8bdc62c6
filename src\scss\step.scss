// 步骤条
.ant-steps-navigation .ant-steps-item::before {
  display: none;
}

.ant-steps-navigation .ant-steps-item-container {
  display: inline-flex;
  height: 100%;
  margin-left: -16px;
  padding-bottom: 12px;
  text-align: left;
  transition: opacity 0.3s;
  align-items: center;
}

.ant-steps-item-wait > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title {
  color: #84879a;
}

.ant-steps-item-title {
  font-size: 14px;
  font-weight: 500;
  color: #54607c;
}

.ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-description {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #54607c;
}

.ant-steps-item-description {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #54607c;
}

.ant-steps-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: normal;
}

.ant-steps-item-wait .ant-steps-item-icon {
  background: #ffffff;
  border: 1px solid #84879a;
}

.ant-steps-item-wait .ant-steps-item-icon > .ant-steps-icon {
  color: #84879a;
  font-size: 16px;
  font-weight: 400;
}

.ant-steps-item-finish .ant-steps-item-icon > .ant-steps-icon {
  color: #2996ff;
}

.ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title,
.ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title > .ant-steps-item-subtitle,
.ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-description {
  color: #4d96ff;
}
