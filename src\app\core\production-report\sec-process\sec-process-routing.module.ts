import { PackageDetailComponent } from './package-detail/package-detail.component';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SecProcessListComponent } from './list/sec-process-list.component';

const routes: Routes = [
  {
    path: 'list',
    component: SecProcessListComponent,
    data: {
      keepAlive: true,
    },
  },
  {
    path: 'list/monitor',
    component: PackageDetailComponent,
  },
  {
    path: '',
    redirectTo: 'list',
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class SecProcessRoutingModule {}
