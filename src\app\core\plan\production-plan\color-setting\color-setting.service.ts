import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { isNil } from 'lodash';
import { Observable } from 'rxjs';

@Injectable()
export class ColorSettingService {
  constructor(private http: HttpClient) {}

  createHttpParams(payload: any): HttpParams {
    let params = new HttpParams();
    Object.keys(payload).forEach((key: string) => {
      if (!isNil(payload[key]) && String(payload[key]).trim() !== '') {
        params = params.append(key, payload[key]);
      }
    });
    return params;
  }

  /**
   * 获取一级分类二级分类下拉配置
   * @returns Observable
   */
  getOption(payload: { parent_id: number }): Observable<any> {
    return this.http.post('/service/archive/v1/api/style_class/get_by_parent_id', payload);
  }

  /**
   * 获取颜色设置列表
   * @param  {} payload
   * @returns Observable
   */
  getSettingColor(payload: any): Observable<any> {
    return this.http.post('/service/archive/v1/style/sam_color_list', payload);
  }

  /**
   * 获取自定义颜色列表
   * @returns Observable
   */
  getCustomColorList(): Observable<any> {
    return this.http.get('/service/archive/v1/color/get_custom_color_list');
  }

  /**
   * 设置自定义颜色
   * @param  {} payload
   * @returns Observable
   */
  setCustomColor(payload: any): Observable<any> {
    return this.http.post('/service/archive/v1/color/add_custom_color', payload);
  }

  /**
   * 设置sam颜色
   * @param  {} payload
   * @returns Observable
   */
  setColorList(payload: any): Observable<any> {
    return this.http.post('/service/archive/v1/style/update_sam', payload);
  }
}
