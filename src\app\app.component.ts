import { Component, OnInit } from '@angular/core';
import { ActivatedRouteSnapshot, ActivationEnd, NavigationEnd, NavigationStart, Router, RouteReuseStrategy } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { NzIconService } from 'ng-zorro-antd/icon';
import { filter } from 'rxjs/operators';
import pk from '../../package.json';
import { FlcRouteReuseStrategy, FlcRouterEventBusService } from 'fl-common-lib';
import { en_US, NzI18nService, zh_CN } from 'ng-zorro-antd/i18n';
import { HttpClient } from '@angular/common/http';
import { factoryCodes } from './shared/common-config';
import { NzNotificationService } from 'ng-zorro-antd/notification';

const langObj: any = {
  zh: zh_CN,
  en: en_US,
};

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit {
  title = 'elan';
  lang = localStorage.getItem('lang') || 'zh';
  factoryCodes = factoryCodes || [];

  constructor(
    public translateService: TranslateService,
    private iconService: NzIconService,
    private router: Router,
    private _routerEventBus: FlcRouterEventBusService,
    private _routeReuseStrategy: RouteReuseStrategy,
    private i18n: NzI18nService,
    private _http: HttpClient,
    private _nzNotificationService: NzNotificationService
  ) {
    this.translateService.addLangs(['zh', 'en']);
    const brand = localStorage.getItem('brand') || '';
    const lang = localStorage.getItem('lang');
    const currentLang = lang || (this.factoryCodes.includes(brand) ? 'en' : 'zh');

    this.translateService.currentLang = currentLang;
    this.translateService.setDefaultLang(currentLang);
    this.translateService.use(currentLang);
    this.i18n.setLocale(lang ? langObj[lang] : langObj[this.factoryCodes.includes(brand) ? 'en' : 'zh']);

    this.loadIconfont();
    this.initRouteListenter();
  }

  initRouteListenter() {
    this.router.events
      .pipe(
        filter((e) => {
          return (e instanceof ActivationEnd && e?.snapshot != null) || e instanceof NavigationEnd || e instanceof NavigationStart;
        })
      )
      .subscribe((e: any) => {
        if (e instanceof ActivationEnd) {
          const path = this.getRouteKey(e.snapshot);
          this._routerEventBus.emitRouterEvent({ name: path, type: 'ActivationEnd', value: null });
        } else if (e instanceof NavigationEnd) {
          // 清除路由缓存
          // (this._routeReuseStrategy as FlcRouteReuseStrategy).clearCacheOnNewUrl(e.urlAfterRedirects);
          this._routerEventBus.emitRouterEvent({ type: 'NavigationEnd', name: '', value: null });
        } else if (e instanceof NavigationStart) {
          // 锦慧分支特殊逻辑。切换语言， 只针对生产进度报表页面做翻译
          localStorage.getItem('factory_code') === 'JHSCM' && this.useLanguageForJHSCM(e);
        }
      });
  }

  private useLanguageForJHSCM(e: NavigationStart) {
    // 生产进度表页面切换语言
    if (e.url.includes('production-report/production-progress')) {
      const lang = localStorage.getItem('lang') || 'zh'; // 默认中文
      this.translateService.currentLang = '';
      this.translateService.use(lang);

      this.i18n.setLocale(en_US);
    } else {
      if (this.translateService.currentLang === 'zh') {
        return;
      }
      this.i18n.setLocale(zh_CN);
      this.translateService.currentLang = '';
      this.translateService.use('zh');
    }
  }

  getRouteKey(route: ActivatedRouteSnapshot): string {
    const path = route.pathFromRoot
      .filter((u) => u.url.length > 0)
      .map((u) => u.url)
      .join('/');
    return path[0] !== '/' ? '/' + path : path;
  }

  loadIconfont() {
    this.iconService.fetchFromIconfont({
      scriptUrl: 'https://at.alicdn.com/t/c/font_2782026_fezlxpqgun.js',
    });
  }

  ngOnInit() {
    console.log(
      `%c ${pk.name} %c v${pk.version}`,
      'background:#2996FF; color: #FFF; padding: 4px; border-radius: 2px 0 0 2px; font-size: 12px',
      'background:#000000; color: #FFF; padding: 4px; border-radius: 0 2px 2px 0; font-size: 12px'
    );
    this.getToken();
    if (!localStorage.getItem('brand')) {
      this.getBrand();
    }
  }

  getBrand() {
    this._http.post('/common/source_type', null).subscribe((res: any) => {
      if (res.code === 200) {
        localStorage.setItem('brand', res.data);
        window.location.reload();
      }
    });
  }

  getToken() {
    const urlParams = new URLSearchParams(window.location.search);
    const password_need_modify = urlParams.get('password_need_modify');
    if (password_need_modify && password_need_modify !== 'false') {
      this._nzNotificationService.warning('密码安全度较低，请先在web端首次登录！', '');
      setTimeout(() => {
        window.location.href = window.location.origin + '/login';
      }, 2000);
      return;
    }
    const accessToken = urlParams.get('access_token');
    const refreshToken = urlParams.get('refresh_token');
    const path = urlParams.get('path');
    if (accessToken && refreshToken) {
      localStorage.setItem('accessToken', accessToken);
      localStorage.setItem('refreshToken', refreshToken);
      window.location.href = window.location.origin + (path ? path : '/dashboard');
    }
  }
}
