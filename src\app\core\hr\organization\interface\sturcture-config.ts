import { TableHeaderConfig } from 'src/app/services/table-helper/table-helper.service';
import { EmployeeListData } from './structure-data';

type lineItem = TableHeaderConfig<EmployeeListData>;
export const EmployeeListDefaultHeaders: lineItem[] = [
  { label: '员工姓名', key: 'name', visible: true, width: '102px', type: 'text', disable: false, resizeble: true, pinned: false },
  { label: '工号', key: 'code', pinned: false, visible: true, width: '114px', type: 'text', disable: false, resizeble: true },
  {
    label: '操作账号',
    key: 'user_login_name',
    pinned: false,
    visible: true,
    width: '138px',
    type: 'text',
    disable: false,
    resizeble: true,
  },
  {
    label: '权限名称',
    key: 'roles',
    visible: true,
    width: '228px',
    type: 'template',
    template: 'roles',
    disable: false,
    resizeble: true,
    pinned: false,
  },
  { label: '状态', key: 'status', visible: true, width: '71px', type: 'text', disable: false, resizeble: true, pinned: false },
];

export const EmployeeStatusOptions: { label: string; value: number }[] = [
  {
    label: '在职',
    value: 1,
  },
  {
    label: '离职',
    value: 0,
  },
];

export const DepartStatusOptions: { label: string; value: number }[] = [
  {
    label: '已启用',
    value: 1,
  },
  {
    label: '已停用',
    value: 0,
  },
];
