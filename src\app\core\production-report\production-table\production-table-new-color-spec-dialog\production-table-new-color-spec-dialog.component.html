<div class="dashboard">
  <div class="orderInfo">
    <div class="orderItem"><span>IO:</span>{{ orderInfo.bulk_order_code }}</div>
    <nz-divider nzType="vertical"></nz-divider>
    <div class="orderItem">
      <span>{{ translateName + '交期' | translate }}:</span>{{ orderInfo.max_due_time | date: 'yyyy-MM-dd' }}
    </div>
    <nz-divider nzType="vertical"></nz-divider>
    <div class="orderItem">
      <span>{{ translateName + '订单总数' | translate }}:</span>{{ orderInfo.order_quantity }}
    </div>
    <nz-divider nzType="vertical"></nz-divider>
    <div class="orderItem">
      <span>{{ translateName + '款式编码' | translate }}:</span>{{ orderInfo.style_code }}
    </div>
    <nz-divider nzType="vertical"></nz-divider>
    <div class="orderItem">
      <span>{{ translateName + '加工厂' | translate }}:</span>{{ orderInfo.factory_name }}
    </div>
  </div>
  <nz-tabset
    [(nzSelectedIndex)]="topTabsetIndex"
    class="topTabset"
    nzType="card"
    nzSize="small"
    *ngIf="[2, 4].includes(nodeInfo.node_type); else qtyTemplate">
    <nz-tab [nzTitle]="translateName + '巡检' | translate">
      <ng-container *ngTemplateOutlet="inspectionTemplate"></ng-container>
    </nz-tab>
    <nz-tab [nzTitle]="translateName + '产量' | translate">
      <ng-container *ngTemplateOutlet="qtyTemplate"></ng-container>
    </nz-tab>
  </nz-tabset>
</div>
<ng-template #inspectionTemplate>
  <div class="inspacetionBoard">
    <div class="leftArea">
      <nz-form-item>
        <nz-form-label nzRequired [nzSpan]="lang === 'en' ? 8 : 6">{{ translateName + '工序' | translate }}</nz-form-label>
        <nz-form-control [nzSpan]="lang === 'en' ? 16 : 18">
          <input nz-input [placeholder]="'placeholder.input' | translate" [(ngModel)]="inspectionForm.process_desc" />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label nzRequired [nzSpan]="lang === 'en' ? 8 : 6">{{ translateName + '检验数' | translate }}</nz-form-label>
        <nz-form-control [nzSpan]="lang === 'en' ? 16 : 18">
          <nz-input-number
            [nzPlaceHolder]="'placeholder.input' | translate"
            [(ngModel)]="inspectionForm.inspected_qty"
            [nzMin]="0"
            [nzStep]="1"
            (ngModelChange)="handleChangeQty()">
          </nz-input-number>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="lang === 'en' ? 8 : 6">{{ translateName + '疵品数' | translate }}</nz-form-label>
        <nz-form-control [nzSpan]="lang === 'en' ? 16 : 18">
          <nz-input-number
            [nzPlaceHolder]="'placeholder.input' | translate"
            [(ngModel)]="inspectionForm.defected_qty"
            [nzMin]="0"
            [nzMax]="inspectionForm.inspected_qty"
            [nzStep]="1"
            (ngModelChange)="handleChangeQty()">
          </nz-input-number>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="lang === 'en' ? 8 : 6">{{ translateName + '合格数' | translate }}</nz-form-label>
        <nz-form-control [nzSpan]="lang === 'en' ? 16 : 18">
          <nz-input-number
            [nzPlaceHolder]="translateName + '自动计算' | translate"
            nzDisabled
            [(ngModel)]="inspectionForm.qualified_qty"
            [nzMin]="0"
            [nzStep]="1">
          </nz-input-number>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="lang === 'en' ? 8 : 6">{{ translateName + '合格率' | translate }}</nz-form-label>
        <nz-form-control [nzSpan]="lang === 'en' ? 16 : 18">
          <nz-input-number-group nzAddOnAfter="%" style="width: 100%">
            <nz-input-number
              [nzPlaceHolder]="translateName + '自动计算' | translate"
              nzDisabled
              [(ngModel)]="inspectionForm.qualifiedPercent"
              [nzMin]="0"
              [nzStep]="1">
            </nz-input-number>
          </nz-input-number-group>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="lang === 'en' ? 8 : 6">{{ translateName + '疵品率' | translate }}</nz-form-label>
        <nz-form-control [nzSpan]="lang === 'en' ? 16 : 18">
          <nz-input-number-group nzAddOnAfter="%" style="width: 100%">
            <nz-input-number
              [nzPlaceHolder]="translateName + '自动计算' | translate"
              nzDisabled
              [(ngModel)]="inspectionForm.defectedPercent"
              [nzMin]="0"
              [nzStep]="1">
            </nz-input-number>
          </nz-input-number-group>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="lang === 'en' ? 8 : 6">{{ translateName + '备注' | translate }}</nz-form-label>
        <nz-form-control [nzSpan]="lang === 'en' ? 16 : 18">
          <nz-textarea-count [nzMaxCharacterCount]="50">
            <textarea
              style="min-height: 80px"
              rows="3"
              nz-input
              [maxlength]="50"
              [(ngModel)]="inspectionForm.remark"
              [placeholder]="'placeholder.input' | translate"
              nzAutosize
              inputTrim></textarea>
          </nz-textarea-count>
        </nz-form-control>
      </nz-form-item>
    </div>
    <div class="rightArea">
      <nz-form-item>
        <nz-form-label [nzSpan]="lang === 'en' ? 8 : 6">{{ translateName + '图片' | translate }}</nz-form-label>
        <nz-form-control [nzSpan]="lang === 'en' ? 16 : 18">
          <flc-file-gallery
            [fileMaxCount]="10"
            [fileList]="inspectionForm.pictures"
            [isEditMode]="true"
            galleryType="image"
            [wrap]="true"
            [needWatermark]="true"
            [addBtnText]="'common.添加图片' | translate"
            (onDeleted)="uploadImage($event)"
            (onUploaded)="uploadImage($event)">
          </flc-file-gallery>
        </nz-form-control>
      </nz-form-item>
    </div>
  </div>
</ng-template>
<ng-template #qtyTemplate>
  <div class="selectLine" *ngIf="currentPo">
    <div class="selectItem">
      <span>{{ translateName + '日期' | translate }}:</span> <nz-date-picker [(ngModel)]="selectedDate"></nz-date-picker>
    </div>
    <div class="selectItem">
      <span>PO:</span>
      <nz-select nzShowSearch [(ngModel)]="currentPo" (ngModelChange)="changePo($event)">
        <nz-option *ngFor="let po of poList" [nzLabel]="po.po_code" [nzValue]="po"></nz-option>
      </nz-select>
    </div>
    <div class="selectItem">
      <span>{{ translateName + '完成数' | translate }}:</span>
      <nz-input-number
        [nzDisabled]="currentPo.disableInputQty"
        [(ngModel)]="currentPo.input_qty"
        [nzMin]="0"
        [nzStep]="1"></nz-input-number>
    </div>
    <div class="selectItem">
      <span>{{ translateName + '累计完成' | translate }}:</span>
      <label (click)="showPoHistory()" style="color: #4d96ff; cursor: pointer">
        <flc-text2-truncated [data]="currentPo?.total_qty"></flc-text2-truncated>
      </label>
    </div>
  </div>
  <nz-tabset nzType="card" nzSize="small" (nzSelectedIndexChange)="changeColor($event)" [(nzSelectedIndex)]="currentColorIndex">
    <nz-tab *ngFor="let color of currentColorList" [nzTitle]="color.color_name"></nz-tab>
  </nz-tabset>
  <nz-table class="zebra-striped-table" #colorSizeTable nzSize="small" [nzData]="currentColorLineList" [nzFrontPagination]="false">
    <thead>
      <tr>
        <th>{{ translateName + '尺码' | translate }}</th>
        <th>{{ translateName + '订单数' | translate }}</th>
        <th>{{ qtyTitle ? (translateName + qtyTitle | translate) : '' }}</th>
        <th>{{ translateName + '累计' | translate }}{{ qtyTitle ? (translateName + qtyTitle | translate) : '' }}</th>
        <th>{{ translateName + '相差数' | translate }}</th>
        <th>
          <label
            [nzDisabled]="currentPo?.disableAllchildrenQty"
            nz-checkbox
            [(ngModel)]="isCheckAll"
            [nzIndeterminate]="isCheckAll === null"></label>
        </th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let line of colorSizeTable.data">
        <td>{{ line.spec_size }}</td>
        <td>{{ line.order_qty }}</td>
        <td>
          <nz-input-number [nzDisabled]="currentPo?.disableAllchildrenQty" [(ngModel)]="line.qty" nzSize="small" [nzMin]="0" [nzStep]="1">
          </nz-input-number>
        </td>
        <td>
          <button nz-button nzType="link" (click)="showDetail(line)">
            <flc-text2-truncated [data]="line.total_qty"></flc-text2-truncated>
          </button>
        </td>
        <td>{{ line.difference_qty }}</td>
        <td>
          <label [nzDisabled]="currentPo?.disableAllchildrenQty" nz-checkbox [(ngModel)]="line.checked"></label>
        </td>
      </tr>
    </tbody>
  </nz-table>

  <flc-file-gallery
    [fileMaxCount]="10"
    [fileList]="pictures"
    [isEditMode]="true"
    galleryType="image"
    [wrap]="true"
    [needWatermark]="true"
    (onDeleted)="uploadImageCutting($event)"
    (onUploaded)="uploadImageCutting($event)">
  </flc-file-gallery>
</ng-template>
<nz-divider nzType="horizontal"></nz-divider>
<div class="footer">
  <button *ngIf="topTabsetIndex === 0" nz-button nzType="default" (click)="showInspectionHistory()">
    {{ translateName + '历史记录' | translate }}
  </button>
  <button nz-button nzType="default" (click)="cancel()">{{ 'btn.cancel' | translate }}</button>
  <button nz-button nzType="primary" (flcClickStop)="save()">{{ 'btn.save' | translate }}</button>
</div>
<ng-template #qtyHistoryTemplate>
  <div class="orderInfo">
    <div class="orderItem"><span>IO:</span>{{ orderInfo.bulk_order_code }}</div>
    <nz-divider nzType="vertical"></nz-divider>
    <div class="orderItem">
      <span>{{ translateName + '交期' | translate }}:</span>{{ orderInfo.max_due_time | date: 'yyyy-MM-dd' }}
    </div>
    <nz-divider nzType="vertical"></nz-divider>
    <div class="orderItem">
      <span>{{ translateName + '订单总数' | translate }}:</span>{{ orderInfo.order_quantity }}
    </div>
    <ng-container *ngIf="historyLine">
      <nz-divider nzType="vertical"></nz-divider>
      <div class="orderItem">
        <span>{{ translateName + '颜色/尺码' | translate }}:</span>{{ historyLine?.color_name }}/{{ historyLine?.spec_size }}
      </div>
      <nz-divider nzType="vertical"></nz-divider>
      <div class="orderItem">
        <span>{{ translateName + '订单数' | translate }}:</span>{{ historyLine?.order_qty }}
      </div>
      <nz-divider nzType="vertical"></nz-divider>
      <div class="orderItem">
        <span>{{ translateName + '累计' | translate }}{{ qtyTitle ? (translateName + qtyTitle | translate) : '' }}:</span
        >{{ historyLine?.total_qty }}
      </div>
    </ng-container>
  </div>
  <nz-table nzBordered class="zebra-striped-table" #historyTable nzSize="small" [nzData]="historyList" [nzFrontPagination]="false">
    <thead>
      <tr>
        <th>{{ translateName + '提交时间' | translate }}</th>
        <th>{{ translateName + '日期' | translate }}</th>
        <th>{{ translateName + '提交人' | translate }}</th>
        <th>{{ qtyTitle ? (translateName + qtyTitle | translate) : '' }}</th>
        <th>{{ translateName + '图片' | translate }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let line of historyTable.data">
        <td>{{ line.gen_time | date: 'yyyy-MM-dd HH:mm:ss' }}</td>
        <td>{{ line.finished_time | date: 'yyyy-MM-dd' }}</td>
        <td>{{ line.user_name }}</td>
        <td>
          <nz-input-number [(ngModel)]="line.qty" nzSize="small" [nzMin]="0" [nzStep]="1"> </nz-input-number>
        </td>
        <td>
          <flc-table-body-render type="image" [data]="line.pictures"></flc-table-body-render>
        </td>
      </tr>
    </tbody>
  </nz-table>

  <!-- <flc-file-gallery [fileMaxCount]="10" [fileList]="picturesHistory" [isEditMode]="false" galleryType="image" [wrap]="true">
  </flc-file-gallery> -->

  <nz-divider style="margin: 12px 0 0 0" nzType="horizontal"></nz-divider>
  <div class="footer">
    <button nz-button nzType="default" style="margin-right: 4px" (click)="closeHistoryModal()">{{ 'btn.cancel' | translate }}</button>
    <button nz-button nzType="primary" (flcClickStop)="saveHistoryChange()">{{ 'btn.save' | translate }}</button>
  </div>
</ng-template>
<ng-template #inspectionHistoryTemplate>
  <div class="orderInfo">
    <div class="orderItem">
      <span>{{ translateName + '节点名称' | translate }}:</span>{{ nodeInfo.template_node_name }}
    </div>
    <nz-divider nzType="vertical"></nz-divider>
    <div class="orderItem"><span>IO:</span>{{ orderInfo.bulk_order_code }}</div>
    <nz-divider nzType="vertical"></nz-divider>
    <div class="orderItem">
      <span>{{ translateName + '交期' | translate }}:</span>{{ orderInfo.max_due_time | date: 'yyyy-MM-dd' }}
    </div>
    <nz-divider nzType="vertical"></nz-divider>
    <div class="orderItem">
      <span>{{ translateName + '订单总数' | translate }}:</span>{{ orderInfo.order_quantity }}
    </div>
  </div>
  <nz-table
    nzBordered
    class="zebra-striped-table"
    #inspectionHistoryTable
    nzSize="small"
    [nzData]="inspectionHistoryList"
    [nzFrontPagination]="false">
    <thead>
      <tr>
        <th>{{ translateName + '时间' | translate }}</th>
        <th>{{ translateName + '工序' | translate }}</th>
        <th>{{ translateName + '检验数' | translate }}</th>
        <th>{{ translateName + '合格数' | translate }}</th>
        <th>{{ translateName + '合格率' | translate }}</th>
        <th>{{ translateName + '疵品数' | translate }}</th>
        <th>{{ translateName + '疵品率' | translate }}</th>
        <th>{{ translateName + '图片' | translate }}</th>
        <th>{{ translateName + '备注' | translate }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let line of inspectionHistoryTable.data">
        <td>{{ line.finished_time | date: 'yyyy-MM-dd' }}</td>
        <td><flc-table-body-render [data]="line.process_desc" type="text"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="line.inspected_qty" type="quantity"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="line.qualified_qty" type="quantity"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="line.qualifiedPercent + '%'" type="text"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="line.defected_qty" type="quantity"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="line.defectedPercent + '%'" type="text"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="line.pictures" type="image"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="line.remark" type="text"></flc-table-body-render></td>
      </tr>
      <tr *ngIf="inspectionHistoryTable.data.length > 0">
        <td colspan="2">{{ translateName + '合计' | translate }}</td>
        <td><flc-table-body-render [data]="inspectionTotalInfo?.inspected_qty" type="quantity"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="inspectionTotalInfo?.qualified_qty" type="quantity"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="inspectionTotalInfo?.qualifiedPercent + '%'" type="text"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="inspectionTotalInfo?.defected_qty" type="quantity"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="inspectionTotalInfo?.defectedPercent + '%'" type="text"></flc-table-body-render></td>
        <td colspan="2"></td>
      </tr>
    </tbody>
  </nz-table>
</ng-template>
