import { NzDrawerService } from 'ng-zorro-antd/drawer';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  NgZone,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { addDays, addMonths, subDays } from 'date-fns';
import { resizable } from 'fl-common-lib';
import { Observable, Subscription } from 'rxjs';
import { ColorSettingComponent } from './color-setting/color-setting.component';
import { PlanGanttComponent } from './plan-gantt/plan-gantt.component';
import { SearchOption } from './production-plan';
import { ProductionPlanService } from './production-plan.service';
import { AppStorageService } from 'src/app/shared/app-storage.service';
import { pickBy } from 'lodash';

const isNotEmpty = (input: any) => {
  if (Array.isArray(input)) {
    return input.length > 0;
  }
  return input !== undefined && input !== null && input !== '';
};
@Component({
  selector: 'app-production-plan',
  templateUrl: './production-plan.component.html',
  styleUrls: ['./production-plan.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ProductionPlanService],
})
@resizable()
export class ProductionPlanComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild(ColorSettingComponent) graphColorSettingComponent!: ColorSettingComponent;
  @ViewChild(PlanGanttComponent) planGanttComponent!: PlanGanttComponent;
  collapse = false;
  ranges = {
    半个月: [new Date(), addDays(new Date(), 15)],
    一个月: [new Date(), addMonths(new Date(), 1)],
    三个月: [new Date(), addMonths(new Date(), 3)],
  };
  searchOption: SearchOption = {
    factoryOptions: [],
    codeOptions: [],
    materialCodeOptions: [],
    poOptions: [],
  };
  graphOptions: { item_dimension: 'io' | 'po' } = {
    item_dimension: this._service.getProOrderDimension() || 'io',
  };
  dates = {
    start_date: subDays(new Date(), 15),
    end_date: addDays(addMonths(new Date(), 1), 15),
  };
  searchTimeObservable!: Observable<any> | null;
  searchTimeSubscription!: Subscription;
  bodyHeight!: number;

  constructor(
    public _service: ProductionPlanService,
    private _cdr: ChangeDetectorRef,
    private _zone: NgZone,
    private _drawer: NzDrawerService,
    public _storage: AppStorageService
  ) {}

  ngOnInit(): void {
    (this as any).addResizePageListener();
    this.getOptions();
    if (!this._service.btnArr?.length) {
      this._service.btnArr = this._storage.getUserActions('intellect-plan/production-plan');
    }
  }

  ngAfterViewInit(): void {
    this.resizePage();
  }

  ngOnDestroy(): void {
    (this as any).removeResizePageListener();
  }

  resizePage(): void {
    setTimeout(() => {
      const searchClientRect = document.querySelector('.flc-search-container')?.getBoundingClientRect() as any;
      const searchHeight = searchClientRect?.height > 0 ? searchClientRect?.height + 8 : 0;
      this.bodyHeight = window.innerHeight - searchHeight - 48 - 16;

      //content最小高度
      if (this.bodyHeight < 200) {
        this.bodyHeight = 200;
      }
      this._cdr.detectChanges();
    });
  }

  getOptions() {
    this._service.getOptions()?.subscribe((res) => {
      if (res?.code === 200) {
        this.searchOption.codeOptions = res?.data?.io_codes;
        this.searchOption.materialCodeOptions = res?.data?.style_codes;
        this.searchOption.poOptions = res?.data?.po_codes;
      }
    });
    this._service.getAllFactory()?.subscribe((res) => {
      if (res?.code === 200) {
        this.searchOption.factoryOptions = res?.data?.factory_list;
      }
    });
  }

  onSearch(load = false): void {
    this.planGanttComponent?.heightLightList();
  }

  timeChange() {
    const date = this._service.searchData.date;
    if (date.length) {
      const startDate = subDays(new Date(date[0]), 15);
      const endDate = addDays(new Date(date[1]), 15);
      this._service.searchData.start_date = startDate;
      this._service.searchData.end_date = endDate;
      this.dates = {
        start_date: startDate,
        end_date: endDate,
      };
    }
    this.planGanttComponent?.getGanttData().then(() => {
      this.planGanttComponent?.heightLightList();
    });
  }

  reset(): void {
    this._service.searchData = {
      factory_id: null,
      order_code: null,
      po_code: null,
      style_code: null,
      date: [new Date(), addMonths(new Date(), 1)],
      start_date: subDays(new Date(), 15),
      end_date: addDays(addMonths(new Date(), 1), 15),
    };
    this.dates = {
      start_date: subDays(new Date(), 15),
      end_date: addDays(addMonths(new Date(), 1), 15),
    };
    this.onSearch();
  }

  expandChange(): void {
    this.collapse = !this.collapse;
    this.resizePage();
  }

  /** 设置进度条颜色 */
  settingColor(): void {
    setTimeout(() => {
      this.graphColorSettingComponent?.showDrawer();
    });
  }

  getToggleActive(): boolean {
    return this._service.getProOrderDimension() === 'po';
  }

  getToggleTooltipTitle(): string {
    const isPoMode = this.getToggleActive();
    // return isPoMode ? '将取消展示交付单维度的甘特图' : '将展示交付单维度的甘特图';
    return isPoMode ? 'showIoGanttTooltip' : 'showPoGanttTooltip';
  }

  switchPoMode() {
    this._zone.runOutsideAngular(() => {
      const isPoMode = this.getToggleActive();
      const item_dimension = !isPoMode ? 'po' : 'io';
      this._service.setProOrderDimension(item_dimension);
      this.graphOptions.item_dimension = item_dimension;
      this._zone.run(() => {
        this.graphOptions = { ...this.graphOptions };
      });
    });
  }

  /*
   * 搜索大货单号、款式编码，自动填充日期
   * 从过去15天到该产线最后一天，若无日期或日期在过去的过去则过去15天到当天
   */
  associatedChange() {
    this.searchTimeDestroy();
    const payload = {
      order_code: this._service.searchData.order_code,
      po_code: this._service.searchData.po_code,
      style_code: this._service.searchData.style_code,
    };
    const filterPayload = Object.entries(pickBy(payload, isNotEmpty));
    if (filterPayload?.length) {
      this.searchTimeObservable = this._service.getRangeTime(payload);
      this.searchTimeSubscription = this.searchTimeObservable?.subscribe((res) => {
        if (res.code === 200) {
          const date = res?.data;
          const originStartDate = this.isDate(date?.start_date) ? new Date(date.start_date) : new Date();
          const originEndDate = this.isDate(date?.end_date) ? new Date(date.end_date) : addMonths(new Date(), 1);
          this._service.searchData.date = [originStartDate, originEndDate];
          this.timeChange();
        }
      });
    } else {
      this._service.searchData.date = [new Date(), addMonths(new Date(), 1)];
      this._service.searchData.start_date = subDays(new Date(), 15);
      this._service.searchData.end_date = addDays(addMonths(new Date(), 1), 15);
      this.dates = {
        start_date: subDays(new Date(), 15),
        end_date: addDays(addMonths(new Date(), 1), 15),
      };
      this.planGanttComponent?.getGanttData().then(() => {
        this.planGanttComponent?.heightLightList();
      });
    }
  }

  isDate(date: any) {
    return date && date !== 0;
  }

  searchTimeDestroy() {
    this.searchTimeObservable = null;
    if (this.searchTimeSubscription) {
      this.searchTimeSubscription.unsubscribe();
    }
  }

  getNewColor(data: { is_refresh_list: boolean }) {
    this.planGanttComponent?.getNewColor(data);
  }
}
