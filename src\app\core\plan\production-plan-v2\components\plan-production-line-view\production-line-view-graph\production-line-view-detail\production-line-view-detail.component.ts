import { Component, Input, OnInit } from '@angular/core';
import { divide, floor, multiply } from 'lodash';
import { LineOrderInterface } from '../../../../interface';

@Component({
  selector: 'app-production-line-view-detail',
  templateUrl: './production-line-view-detail.component.html',
  styleUrls: ['./production-line-view-detail.component.scss'],
})
export class ProductionLineViewDetailComponent implements OnInit {
  @Input() detailBarItem: LineOrderInterface | null = null;

  constructor() {}

  ngOnInit() {}

  statusMap = {
    1: '待发布',
    2: '已发布',
  };
  calProgress(num1: number, num2: number) {
    const progress = divide(num1 ?? 0, num2 ?? 0) ?? 0;
    const percent = multiply(progress, 100);
    const floor_num = floor(percent, 1);
    return floor_num;
  }
}
