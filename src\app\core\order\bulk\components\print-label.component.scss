.print-item {
  display: flex;
  & > span:nth-child(1) {
    display: inline-block;
    min-width: 80px;
    text-align: right;
    white-space: nowrap;
  }
  & > span:nth-child(2) {
    flex: 1;
  }
}
.print-item + .print-item {
  margin-top: 16px;
}
.print-item-size,
.print-item-size-bu {
  width: 100%;
  padding: 8px 12px;
  background-color: #f5fafe;
  border-radius: 4px;
}
.print-item-size {
  display: flex;
}
.print-item-size + .print-item-size {
  margin-top: 16px;
}
.print-btns {
  display: flex;
  justify-content: space-between;
  align-items: center;
  & > div {
    display: flex;
    align-items: center;
  }
}

.print-item-size-box + .print-item-size-box {
  margin-top: 8px;
}

.card-box {
  width: 10cm;
  height: 5cm;
  padding: 10px;
  background-color: white;
  border-radius: 4px;
  .card-box-content {
    display: flex;
    flex-direction: column;
    font-size: 12px;
    & > span + span {
      margin-top: 8px;
    }
  }
}

@media print {
  @page {
    padding: 0;
    margin: 0;
  }
  .card-box {
    padding: 10px;
    background-color: white;
    border-radius: 4px;
    page-break-before: auto !important;
    page-break-after: always !important; /*强制换页的关键*/
    .card-box-content {
      display: flex;
      flex-direction: column;
      font-size: 12px;
      & > span + span {
        margin-top: 8px;
      }
    }
  }
  .pageBreak {
    page-break-before: auto;
    page-break-after: always;
  }
}

@media print and (color) {
  * {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
}

::ng-deep {
  .print-label-warning-color {
    color: #fc9958;
  }
}
