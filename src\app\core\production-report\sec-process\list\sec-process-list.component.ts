import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { endOfDay, format, startOfDay } from 'date-fns';
import { debounceTime, finalize, fromEvent, startWith, Subject, Subscription } from 'rxjs';
import { NzModalService } from 'ng-zorro-antd/modal';
import { isNotNil } from 'ng-zorro-antd/core/util';
import { FlcRouterEventBusService, resizable } from 'fl-common-lib';
import { DetailDataType, DetailDataTypeTransform, DetailModalMode } from '../models/sec-process.enum';
import { renderModalTitle } from '../models/sec-process.model';
import { SecProcessService } from '../sec-process.service';
import { SecProcessTableComponent } from '../components/sec-process-table/sec-process-table.component';
import { StatusOptions } from '../../production-report.model';
import { DimensionRange, ReportRange } from '../../production-report.enum';

type SearchKey = 'biz_date' | 'io_code' | 'customer_style' | 'factory_name' | 'po_code' | 'status' | 'due_time';
@Component({
  selector: 'app-sec-process-list',
  templateUrl: './sec-process-list.component.html',
  styleUrls: ['./sec-process-list.component.scss'],
})
@resizable()
export class SecProcessListComponent implements OnInit {
  @ViewChild('searchBar') searchBar!: ElementRef;
  selectedTab = ReportRange.daily;
  currentDimension = DimensionRange.io;
  isShowSearchContainer = true;
  searchOptions: Array<{ label: string; key: SearchKey; type: string; visible: boolean; options?: any }> = [
    { label: '日期', key: 'biz_date', type: 'dateRange', visible: true },
    { label: '大货单号', key: 'io_code', type: 'select', visible: true },
    { label: '款式编码', key: 'customer_style', type: 'select', visible: true },
    { label: '交付单', key: 'po_code', type: 'select', visible: false },
    { label: '外发厂', key: 'factory_name', type: 'select', visible: true },
    { label: '状态', key: 'status', type: 'local-select', visible: false, options: StatusOptions },
    { label: '交期', key: 'due_time', type: 'dateRange', visible: true },
  ];
  searchData = {
    customer_style: null,
    io_code: null,
    po_code: null,
    due_time: null,
    biz_date: null,
    factory_name: null,
    status: null,
  };
  _initSearchData: any = {};
  searchOptionUrl = this._service.serviceUrl + '/outsource/options';
  highlightId = 0;
  highlightIds: Array<number | string> = [];
  tableConfig: any = {
    dataList: [],
    count: 0,
    loading: false,
    height: 400,
    orderBy: [],
    pageSize: 20,
    pageIndex: 1,
  };
  sumData: any = {};
  statusValue: any = {
    1: '未开始',
    2: '生产中',
    3: '已完成',
  };
  translateSubscription!: Subscription;
  listSubject$ = new Subject<boolean>();
  listSubscription!: Subscription;
  clickSubscription!: Subscription;
  immediate = false;
  dueSort: any = null;

  constructor(
    private _service: SecProcessService,
    private _router: Router,
    private _modal: NzModalService,
    private element: ElementRef,
    private _routerEventBus: FlcRouterEventBusService
  ) {}

  ngOnInit(): void {
    this._initSearchData = JSON.parse(JSON.stringify(this.searchData));
    this.listSubscription = this.listSubject$
      .pipe(
        startWith(true),
        debounceTime(500),
        finalize(() => {
          this.tableConfig.loading = false;
        })
      )
      .subscribe((res: boolean) => {
        this._getList(res);
      });

    (this as any).addResizePageListener();
    this.translateSubscription = this._service.translateEventEmitter.subscribe(() => {
      this.resizePage();
    });
    this.clickSubscription = fromEvent(this.element.nativeElement, 'click').subscribe(() => {
      // UI：点击任意地方，解除高亮
      this.highlightId = 0;
      this.highlightIds = [];
    });
  }

  ngAfterViewInit(): void {
    this.resizePage();
  }

  ngOnDestroy() {
    (this as any).removeResizePageListener();
    this.translateSubscription?.unsubscribe();
    this.listSubscription?.unsubscribe();
    this.clickSubscription.unsubscribe();
  }

  resizePage() {
    setTimeout(() => {
      const searchBarHeight = this.searchBar?.nativeElement?.offsetHeight ?? 0;
      let _height = window.innerHeight - searchBarHeight - 215;
      if (_height < 200) {
        _height = 200;
      }
      this.tableConfig = { ...this.tableConfig, height: _height };
    }, 0);
  }

  onSizeChange(e: number) {
    this.tableConfig = { ...this.tableConfig, pageSize: e };
    this._getList(true);
  }

  onIndexChange(e: number) {
    this.tableConfig = { ...this.tableConfig, pageIndex: e };
    this._getList(false);
  }

  onSortChange(e: any, key: string) {
    const orderBy = e ? [key + ' ' + e] : [];
    this.tableConfig = { ...this.tableConfig, orderBy };
    this._getList(false);
  }

  onReset() {
    this.searchData = JSON.parse(JSON.stringify(this._initSearchData));
    this.tableConfig = { ...this.tableConfig, pageIndex: 1, orderBy: [] };
    this.dueSort = null;
    this._getList(true);
  }

  onToggle() {
    this.isShowSearchContainer = !this.isShowSearchContainer;
    this.resizePage();
  }

  // 下拉选择搜索
  onSearch(isReset: boolean) {
    this.listSubject$.next(isReset);
  }

  // 切换tab，更换筛选项，清空筛选项，更换表头，tableConfig更换tableName，清除排序，页码重置，行高亮重置
  onTabChange() {
    const opt = this.searchOptions.find((item) => item.key === 'biz_date');
    if (opt) opt.visible = this.selectedTab === ReportRange.daily;
    const statusOpt = this.searchOptions.find((item) => item.key === 'status');
    if (statusOpt) statusOpt.visible = this.selectedTab === ReportRange.summary;
    this.immediate = true;
    this.onReset();
    this.resizePage();
  }

  // 切换维度，更换筛选项，隐藏/显示交付单列，重置页码，重置行高亮
  onDimensionChange() {
    const opt = this.searchOptions.find((item) => item.key === 'po_code');
    if (opt) opt.visible = this.currentDimension === DimensionRange.po;
    this.searchData = { ...this.searchData, po_code: null }; // 清除交付单
    this.highlightIds = [];
    this.highlightId = 0;
    this.immediate = true;
    this._getList(true);
    this.resizePage();
  }

  // 生产外发监控
  onViewDetail(e: any, item: any, po: any, extra: any, sub: any) {
    e.stopPropagation();
    this.highlightId = item?.unique_code;
    this.highlightIds = [item?.unique_code, po.po_code, po.po_code + extra.extra_process_id, sub?.unique_code];
    this._router.navigate(['/production-report/sec-process/list/monitor'], {
      queryParams: { io_code: item?.io_code, po_code: po.po_code, extra_process_id: extra.extra_process_id },
    });
  }

  setTdBackground(item: any, po: any, extra: any, sub: any) {
    if (!this.highlightId || this.highlightId !== item?.unique_code) return false;
    const arr = [item?.unique_code, po.po_code, po.po_code + extra.extra_process_id, sub?.unique_code];
    if (JSON.stringify(this.highlightIds) === JSON.stringify(arr)) {
      return true;
    }
    return false;
  }

  onOpenModal(key: string, item: any, po: any, extra: any, sub: any) {
    let title = renderModalTitle(this.selectedTab, key);
    let mode = DetailModalMode.PartColorSizeMode;
    if (key === 'cutting_qty') mode = DetailModalMode.ColorSizeMode;
    if (key === 'outsource_qty') mode = DetailModalMode.OutsourceMode;
    if (key === 'delivered_qty') {
      title = '外发明细';
      mode = DetailModalMode.DeliveredMode;
    }
    type keyTypes = keyof typeof DetailDataTypeTransform;
    const transformKey = DetailDataTypeTransform[key as keyTypes];
    const payload: any = {
      io_code: item?.io_code,
      po_code: po?.po_code,
      factory_code: sub?.factory_code,
      factory_name: sub?.factory_name,
      biz_date: this.selectedTab === ReportRange.daily ? item?.biz_date : '',
      data_type: DetailDataType[transformKey],
      extra_process_id: extra?.extra_process_id,
      dimension: this.currentDimension,
    };

    this._modal.create({
      nzTitle: title,
      nzContent: SecProcessTableComponent,
      nzComponentParams: {
        mode,
        tab: this.selectedTab,
        dimension: this.currentDimension,
        payload,
        version: new Date().getTime(),
        key,
      },
      nzFooter: null,
      nzWidth: 800,
      nzBodyStyle: { padding: '0 12px 12px' },
    });
  }

  private _getList(reset = false) {
    this.immediate = false;
    this.tableConfig = { ...this.tableConfig, loading: true, pageIndex: reset ? 1 : this.tableConfig.pageIndex };
    const payload = {
      order_by: this.tableConfig.orderBy,
      limit: this.tableConfig.pageSize,
      page: this.tableConfig.pageIndex,
      daily: this.selectedTab,
      dimension: this.currentDimension,
      where: this._handleWhere(),
    };

    let isSuccess = false;
    this._service
      .getSecProcessList(payload)
      .pipe(
        finalize(() => {
          if (!isSuccess) {
            this.tableConfig = { ...this.tableConfig, dataList: [], count: 0 };
            this.sumData = {};
          }
          this.tableConfig = { ...this.tableConfig, loading: false };
        })
      )
      .subscribe((res) => {
        if (res.code === 200) {
          isSuccess = true;
          const dataList = res.data?.list || [];
          dataList.forEach((item: any) => {
            item.po_list.forEach((po: any) => {
              po.extra_process_list.forEach((ep: any) => {
                ep.len = ep.sub_list.length;
              });
              po.len = po.extra_process_list.reduce((prev: any, curr: any) => {
                return prev + curr.sub_list.length;
              }, 0);

              // 分隔交期
              po.due_time_list = po.due_time ? po.due_time.split('、') : [];
              const due_time_len = po.due_time_list.length;
              po.due_time_arr = po.due_time_list.slice(0, po.len * 2);

              if (due_time_len > po.due_time_arr.length) {
                po.due_time_overflow = true;
              }
            });

            item.len = item.po_list.reduce((prev: any, curr: any) => {
              return prev + curr.len;
            }, 0);
          });
          this.tableConfig = { ...this.tableConfig, dataList: dataList, count: res.data.count, loading: false };
          this.sumData = { ...res.data.aggregation?.po_list[0]?.extra_process_list[0]?.sub_list[0] };
        }
      });
  }

  private _handleWhere() {
    const where: any = [];
    Object.entries(this.searchData).forEach((item: any) => {
      if (isNotNil(item[1])) {
        if (['due_time', 'biz_date'].includes(item[0])) {
          if (Array.isArray(item[1]) && !!item[1].length) {
            const startTime = format(startOfDay(item[1][0]), 'T');
            const endTime = format(endOfDay(item[1][1]), 'T');
            where.push({ column: item[0], op: 'between', value: `${startTime},${endTime}` });
          }
        } else {
          where.push({ column: item[0], op: '=', value: item[1]?.toString() });
        }
      }
    });
    return where;
  }
}
