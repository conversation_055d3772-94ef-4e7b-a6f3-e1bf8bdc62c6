<div class="container">
  <div class="left">
    <div class="title">
      <span>模块权限</span>
      <!-- prettier-ignore -->
      <label
        nz-checkbox
        [(ngModel)]="isSelectedAll"
        [nzIndeterminate]="indeterminate"
        (ngModelChange)="onUpdateAllChecked()"
      >全选</label>
    </div>
    <div class="module-list">
      <ng-container *ngFor="let btn of _moduleList">
        <label [title]="btn.module_name" nz-checkbox [(ngModel)]="btn.value" (ngModelChange)="onCheckboxChanged($event, btn)">
          {{ btn.module_name }}
        </label>
      </ng-container>
    </div>
  </div>
  <div class="right">
    <div class="module-item" *ngFor="let item of validModuleList">
      <div class="module">
        <div class="name">{{ item.module_name }}</div>
        <span
          class="toggle-tag bordered"
          [ngClass]="{ read: item.permission === 2 }"
          (click)="onToggleModule(item)"
          [nzTooltipTitle]="item.permission === 1 ? '点击切换为只读' : '点击切换为编辑'"
          nzTooltipPlacement="top"
          nz-tooltip>
          {{ item.permission === 1 ? '编辑' : '只读' }}
        </span>
      </div>
      <div class="children">
        <!-- 操作权限 -->
        <div class="operation">
          <div class="title">操作权限</div>
          <div>
            <label nz-checkbox nzDisabled [ngModel]="true"> {{ item.permission === 1 ? '编辑' : '只读' }} </label>
          </div>
          <ng-container *ngIf="item.permission === 1">
            <ng-container *ngFor="let btn of item.module_action_list">
              <div>
                <label nz-checkbox (ngModelChange)="onActionChanged($event, btn)" [(ngModel)]="btn.value">
                  {{ btn.action_name }}
                </label>
              </div>
            </ng-container>
          </ng-container>
        </div>
        <!-- 字段权限 -->
        <ng-container *ngIf="item.module_field_list.length">
          <nz-divider style="margin: 0" nzDashed></nz-divider>
          <div class="fields-content">
            <div *ngFor="let fileds of item.fields_list" class="fields-group">
              <div class="fields-select-all">
                <label
                  nz-checkbox
                  [(ngModel)]="fileds.isSelectAll"
                  (ngModelChange)="updateFieldAllChecked(fileds, item)"
                  [nzIndeterminate]="fileds.indeterminate">
                  {{ fileds.allLable }}
                </label>
                <nz-divider style="margin: 0 8px 0 0" nzType="vertical"></nz-divider>
              </div>
              <div class="fileds-wrap">
                <ng-container *ngFor="let field of fileds.list">
                  <div>
                    <label nz-checkbox (ngModelChange)="fieldCheckboxChanged($event, field, fileds, item)" [(ngModel)]="field.value">
                      {{ field.field_name }}
                    </label>
                    <span
                      *ngIf="field.value"
                      class="toggle-tag"
                      [ngClass]="{ read: field.permission === 2 }"
                      (click)="onToggleModule(field, item)"
                      [nzTooltipTitle]="item.permission !== 1 ? null : field.permission === 1 ? '点击切换为只读' : '点击切换为编辑'"
                      nzTooltipPlacement="top"
                      nz-tooltip>
                      {{ field.permission === 1 ? '编辑' : '只读' }}
                    </span>
                  </div>
                </ng-container>
              </div>
            </div>
          </div>
        </ng-container>
      </div>
    </div>
  </div>
</div>

<div class="footer">
  <button nz-button [nzShape]="'round'" (click)="onClose()">{{ 'flss.btn.cancel' | translate }}</button>
  <button nz-button nzType="primary" [nzShape]="'round'" (click)="onConfirm()">{{ 'flss.btn.ok' | translate }}</button>
</div>
