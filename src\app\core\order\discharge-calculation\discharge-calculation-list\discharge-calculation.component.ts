import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';

import { DischargeCalculationService } from './discharge-calculation.service';
import { BULK_SUB_STATUS } from './discharge-calculation.model';
import { BulkService } from 'src/app/core/order/bulk/bulk.service';
import { isNotNil } from 'ng-zorro-antd/core/util';
import { endOfDay, format, startOfDay } from 'date-fns';
import { DischargeCommonService } from '../discharge-calculation-common.service';
import { NzNotificationService } from 'ng-zorro-antd/notification';

@Component({
  selector: 'app-discharge-calculation',
  templateUrl: './discharge-calculation.component.html',
  styleUrls: ['./discharge-calculation.component.scss'],
  providers: [DischargeCalculationService],
})
export class DischargeCalculationListComponent implements OnInit {
  @ViewChild('header') hearderComponent!: ElementRef;
  // 下拉option

  bulkSubStatus = BULK_SUB_STATUS;
  styleList: any[] = []; // 下拉列表数据

  isVisibleDistribution = false;

  distributionEmployee = {
    dist_user_id: 0,
    dist_user_label: '',
  };

  deparmentOption: any[] = [];

  data_statics = [
    {
      label: '全部',
      value: '-1',
      checked: true,
      isTotalItem: true,
      key: 'all',
    },
    {
      label: '未开始',
      value: '0',
      checked: false,
      isTotalItem: false,
      key: 'not_start',
    },
    {
      label: '进行中',
      value: '1',
      checked: false,
      isTotalItem: false,
      key: 'processing',
    },
    {
      label: '待审核',
      value: '2',
      checked: false,
      isTotalItem: false,
      key: 'waiting',
    },
    {
      label: '审核通过',
      value: '3',
      checked: false,
      isTotalItem: false,
      key: 'approved',
    },
    {
      label: '待修改',
      value: '4',
      checked: false,
      isTotalItem: false,
      key: 'refuse',
    },
    {
      label: '已取消',
      value: '99',
      checked: false,
      isTotalItem: false,
      key: 'canceled',
    },
  ];

  tableConfig: any = {
    version: '1.10.0',
    detailBtn: false,
    dataList: [],
    count: null,
    height: 500,
    loading: false,
    pageSize: 20,
    actionWidth: '80px',
    pageIndex: 1,
    hasCheckbox: true,
    trCheck: true,
  };

  searchList = [
    {
      label: '订单需求号',
      labelKey: 'io_code',
      valueKey: 'io_code',
      type: 'select',
    },
    {
      label: '款式分类',
      labelKey: 'style',
      valueKey: 'style',
      type: 'cascader',
    },
    {
      label: '客户名称',
      labelKey: 'customer',
      valueKey: 'customer',
      type: 'select',
    },
    {
      label: '销售单号',
      labelKey: 'contract_number',
      valueKey: 'contract_number',
      type: 'select',
    },
    {
      label: '订单类型',
      labelKey: 'order_classification',
      valueKey: 'order_classification',
      type: 'select',
    },
    {
      label: '外发工厂',
      labelKey: 'out_sourcing_factory',
      valueKey: 'out_sourcing_factory',
      type: 'select',
    },
    {
      label: '下单日期',
      labelKey: 'order_date',
      valueKey: 'order_date',
      type: 'date',
    },
    {
      label: '业务员',
      labelKey: 'biz_emp',
      valueKey: 'biz_emp_id',
      type: 'dynamic-select',
      canSearch: true,
      alwaysReload: true,
    },
    {
      label: '算料员',
      labelKey: 'dist_user',
      valueKey: 'dist_user_id',
      type: 'dynamic-select',
      canSearch: true,
      alwaysReload: true,
    },
    {
      label: '交付日期',
      labelKey: 'due_time',
      valueKey: 'due_time',
      type: 'date',
    },
    {
      label: '创建人',
      labelKey: 'gen_user',
      valueKey: 'gen_user',
      type: 'select',
    },
    {
      label: '款式编码',
      labelKey: 'style_code',
      valueKey: 'style_code',
      type: 'select',
    },
  ];

  searchData: any = {
    io_code: null,
    style: null,
    customer: null,
    contract_number: null,
    order_status: null,
    order_date: null,
    due_time: null,
    gen_user: null,
    style_code: null,
    approve_status: null,
    dist_user_id: null,
    biz_emp_id: null,
  };

  tableHeader = [
    { label: '订单需求号', key: 'io_code', visible: true, width: '82px', type: 'template', templateName: 'io_code', disable: false },
    { label: '款式编码', key: 'style_code', visible: true, width: '82px', type: 'text', disable: false },
    { label: '品名', key: 'category', visible: true, width: '70px', type: 'text', disable: false },
    {
      label: '款式分类',
      key: 'material_name',
      visible: true,
      width: '120px',
      type: 'template',
      templateName: 'material_name',
      disable: false,
    },
    { label: '款式图', key: 'order_pictures', visible: true, width: '70px', type: 'image', disable: false },
    { label: '客户名称', key: 'customer', visible: true, width: '70px', type: 'text', disable: false },
    { label: '总件数', key: 'qty', visible: true, width: '70px', type: 'quantity', disable: false },
    { label: '销售单号', key: 'contract_number', visible: true, width: '70px', type: 'text', disable: false },
    { label: '订单类型', key: 'order_classification', visible: true, width: '70px', type: 'text', disable: false },
    { label: '外发工厂', key: 'out_sourcing_factory_name', visible: true, width: '70px', type: 'text', disable: false },
    { label: '下单日期', key: 'order_date', visible: true, width: '80px', type: 'date', disable: false },
    {
      label: '交付日期',
      key: 'due_time',
      type: 'template',
      templateName: 'due_time',
      visible: true,
      width: '80px',
      disable: false,
    },
    { label: '业务员', key: 'biz_user_name', visible: true, width: '70px', type: 'text', disable: false },
    { label: '算料员', key: 'dist_user_label', visible: true, width: '70px', type: 'text', disable: false },
    {
      label: '状态',
      key: 'approve_status_value',
      visible: true,
      width: '70px',
      type: 'text',
      disable: false,
    },
  ];
  constructor(
    private _router: Router,
    private _service: DischargeCalculationService,
    public _bulkService: BulkService,
    private _commonSrv: DischargeCommonService,
    private _notice: NzNotificationService
  ) {}

  ngOnInit(): void {
    this.getTableList(true); // 列表查询
    this.initDepartmentOption();
  }

  ngAfterViewInit() {
    this.resize();
  }

  initDepartmentOption() {
    this._service.getDepartmentOption().subscribe((res) => {
      if (res.code === 200) {
        this.deparmentOption = res.data?.children || [];
      }
    });
  }

  /**
   * 表格数据查询
   * @param  {boolean=false} reset 是否重置页码
   */
  getTableList(reset = false) {
    this.tableConfig = { ...this.tableConfig, loading: true };

    const payload = {
      where: this.handleWhere(reset),
      page: this.tableConfig.pageIndex,
      limit: this.tableConfig.pageSize,
      cache: false,
      dist_user_id: this.searchData.dist_user_id,
      biz_emp_id: this.searchData.biz_emp_id,
    };
    this._service.getList(payload).subscribe((res) => {
      res.data.data.forEach((e: any) => {
        e.disabled = e.order_status == 8;
      });
      if (res.code === 200) {
        this.data_statics.forEach((e: any) => {
          const s = res.data.statistics?.[e.key];
          e.amount = s || 0;
        });
        this.tableConfig = {
          ...this.tableConfig,
          dataList: res.data.data,
          count: res.data.total,
          loading: false,
        };
      }
    });
  }

  /**
   * 获取搜索项
   * @param  {} reset=false
   * @returns object
   */
  handleWhere(reset = false): object {
    let where: any = [{ column: 'layout_cons_type', op: '=', value: null }];
    if (reset) {
      this.searchData = {
        io_code: null,
        style: null,
        customer: null,
        contract_number: null,
        order_status: null,
        order_date: null,
        due_time: null,
        gen_user: null,
        create_time: null,
      };
    } else {
      Object.entries(this.searchData).forEach((item: any) => {
        if (isNotNil(item[1])) {
          if (item[0] === 'due_time' || item[0] === 'create_time' || item[0] === 'order_date') {
            if (item[1].length) {
              const startTime = format(startOfDay(item[1][0]), 'T');
              const endTime = format(endOfDay(item[1][1]), 'T');
              const value = startTime + ',' + endTime;
              where.push({ column: item[0], op: 'between', value: value });
            }
          } else if (item[0] === 'style') {
            if (item[1].length) {
              const materialList = [
                { column: 'first_material_name', op: '=', value: item[1][0] },
                { column: 'second_material_name', op: '=', value: item[1][1] },
                { column: 'third_material_name', op: '=', value: item[1][2] },
              ];
              where = [...where, ...materialList];
            }
          } else if (item[0] !== 'dist_user_id' && item[0] !== 'biz_emp_id') {
            if (item[0] === 'approve_status' && item[1] == '99') {
              where.push({ column: 'order_status', op: '=', value: '8' });
            } else {
              where.push({ column: item[0], op: '=', value: item[1] });
            }
          }
        }
      });
    }
    return where;
  }

  /**
   * 路由去详情
   * @param  {} id
   */
  goDetail(item: any) {
    if (item.disabled) return;
    item.layout_bom_update_times = 0;
    this._router.navigate(['/order/layout/list', item.id], {
      queryParams: {
        io_uuid: item.io_uuid,
        bulk_task_order_id: item.bulk_task_order_id,
      },
    });
  }

  resize() {
    setTimeout(() => {
      const headerHeight = this.hearderComponent?.nativeElement?.offsetHeight;
      this.tableConfig.height = window.innerHeight - headerHeight - 150;
      this.tableConfig = { ...this.tableConfig };
    });
  }

  sortDataLists(e: any) {
    console.log(e);
  }

  /**
   * 打开款式下拉
   */
  openStyle(e: any) {
    if (e) {
      this._bulkService.getMaterial({ cache: false }).subscribe((res: any) => {
        this.styleList = this._bulkService.onTransOption(res.data.info);
      });
    }
  }

  /**
   * 下拉选择搜索
   */
  onSearch() {
    this.tableConfig.pageIndex = 1;
    this.getTableList();
  }
  /**
   * 页码改变
   * @param  {number} e
   */
  indexChanges(e: number) {
    this.tableConfig.pageIndex = e;
    this.getTableList();
  }
  /**
   * 页数改变
   * @param  {number} e
   */
  sizeChanges(e: number) {
    this.tableConfig.pageIndex = 1;
    this.tableConfig.pageSize = e;
    this.getTableList();
  }

  reset() {
    this.getTableList(true);
  }
  getList() {
    this.getTableList();
  }
  resizePage() {
    this.resize();
  }

  // 切换订单状态筛选
  onOrderStatusChanged(data: any) {
    if (data?.length) {
      this.searchData.approve_status = data[0];
    } else {
      this.searchData.approve_status = null;
    }
    this.getList();
  }

  // 模糊搜索列表数据
  onInputSearchValue(value: string) {
    this.searchData.keywords = value.length ? value : null;
    this.getList();
  }

  selectedData: any;
  // 获取选中的数据
  geteSelectedData(data: any) {
    this.selectedData = data;
  }

  // 批量添加算料员
  batchAssignEmployee() {
    if (!this.selectedData?.list?.length) {
      return;
    }
    this.isVisibleDistribution = true;
  }

  // 批量添加算料员
  handleOkDistribution() {
    if (!this.selectedData?.list?.length) {
      return;
    }
    this._service
      .batchSubmitMaterialsEmployeeOption({
        io_uuid: this.selectedData?.list.map((e: any) => e.io_uuid),
        ...this.distributionEmployee,
      })
      .subscribe((res) => {
        if (res.code === 200) {
          // 重新请求详情
          this.isVisibleDistribution = false;
          this.getList();
        }
      });
  }

  _handleChangeValue(e: any) {
    if (e) {
      this.distributionEmployee = {
        dist_user_id: e.id,
        dist_user_label: e.title,
      };
    } else {
      this.distributionEmployee = {
        dist_user_id: 0,
        dist_user_label: '',
      };
    }
  }

  // 批量审核
  audit(pass: boolean) {
    if (!this.selectedData?.count) return;
    if (!pass) {
      this._commonSrv.confirmDialogWithReason().afterClose.subscribe((result: any) => {
        if (result?.success) {
          this._audit({ approve_reason: result?.reason, approve_status: 4 });
        }
      });
    } else {
      this._audit({ approve_status: 3 });
    }
  }
  _audit(result: any) {
    this._commonSrv
      .audit({
        approve_order: this.selectedData?.list.map((e: any) => {
          return { io_uuid: e.io_uuid, order_code: e.io_code };
        }),
        approve_reason: result?.approve_reason,
        approve_status: result?.approve_status,
      })
      .subscribe((res) => {
        if (res.code === 200) {
          if (res.data?.msg?.length) {
            this._notice.error(res.data.msg, '');
          } else {
            this._notice.success('操作成功', '');
          }
          this.getList();
        }
      });
  }
}
