<table-data-view
  #tableDataView
  type="bulk_order"
  [class.disabled-panel-select]="panelSelectDisabled"
  (handleTimeFilter)="handleTimeFilter($event)"
  (handlePanelSelect)="onPanelSelectWrapper($event)"
  (handleFolder)="handleFolder()"></table-data-view>
<div #searchContainer>
  <flc-search-combination
    [btnTpl]="btnTpl"
    [isFold]="true"
    [checkedCount]="checkedInfo?.count || 0"
    [isMultiCheck]="false"
    [showAddBtn]="false"
    [checkData]="orderStatusList"
    (reset)="reset()"
    (refresh)="getDataList()"
    (handleFold)="resizePage()"
    (onInputSearchValue)="onInputSearchValue($event)"
    (onCheckChange)="onOrderStatusChanged($event)">
    <ng-container *ngFor="let item of searchList">
      <div *ngIf="item?.visible">
        <span class="search-name">{{ 'outsourcingTableHeaderAndLabel.' + item.label | translate }}：</span>
        <ng-container *ngIf="item.type === 'input'">
          <div class="form-item-input-custom">
            <input
              flcInputTrim
              nz-input
              [(ngModel)]="searchParams.customer"
              [placeholder]="'placeholder.input' | translate"
              [flcDebounceEvent]="onSearch.bind(this)"
              [controlEvent]="'input'"
              [controlTime]="500"
              [controlType]="'debounce'" />
          </div>
        </ng-container>
        <ng-container *ngIf="item.type === 'select'">
          <flc-dynamic-search-select
            [dataUrl]="searchOptionFetchUrl"
            [(ngModel)]="searchParams[item.valueKey]"
            [column]="item.labelKey"
            (handleSearch)="getDataList(true)"
            [payLoad]="item.payload || {}"
            [canSearch]="item.canSearch ?? true"
            [optAlwaysReload]="item.alwaysReload ?? false">
          </flc-dynamic-search-select>
        </ng-container>
        <nz-range-picker
          *ngIf="item.type === 'date'"
          [(ngModel)]="searchParams[item.labelKey]"
          (ngModelChange)="getDataList()"></nz-range-picker>
        <ng-container *ngIf="item.type === 'local-select'">
          <nz-select
            nzPlaceHolder="请选择"
            nzAllowClear
            [nzShowSearch]="true"
            [nzOptions]="item.options"
            [(ngModel)]="searchParams[item.valueKey]"
            (ngModelChange)="getDataList(true)">
          </nz-select>
        </ng-container>
      </div>
    </ng-container>
  </flc-search-combination>
</div>

<ng-template #btnTpl>
  <!-- 外发工厂 -->
  <button
    *ngIf="_service.btnArr.includes('bulk:garment-outsourcing-finished')"
    nz-button
    [nzShape]="'round'"
    flButton="pretty-default"
    [flcDisableOnClick]="1000"
    (click)="handleButtonAction(sampleOperateBtnEnum.complete)">
    {{ 'outsourcingTableHeaderAndLabel.批量完成' | translate }}
  </button>

  <!-- 外发工厂 -->
  <button
    *ngIf="_service.btnArr.includes('bulk:garment-outsourcing-create')"
    nz-button
    [nzShape]="'round'"
    flButton="pretty-default"
    [flcDisableOnClick]="1000"
    (click)="handleButtonAction(sampleOperateBtnEnum.outGoing)">
    {{ 'outsourcingTableHeaderAndLabel.外发工厂' | translate }}
  </button>

  <!-- 审核通过 -->
  <button
    *ngIf="_service.btnArr.includes('bulk:garment-outsourcing-approval')"
    nz-button
    [nzShape]="'round'"
    flButton="pretty-default"
    [flcDisableOnClick]="1000"
    (click)="handleButtonAction(sampleOperateBtnEnum.pass)">
    {{ 'outsourcingTableHeaderAndLabel.审核通过' | translate }}
  </button>

  <!-- 退回修改 -->
  <button
    *ngIf="_service.btnArr.includes('bulk:garment-outsourcing-approval')"
    nz-button
    [nzShape]="'round'"
    flButton="pretty-default"
    [flcDisableOnClick]="1000"
    (click)="handleButtonAction(sampleOperateBtnEnum.modify)">
    {{ 'outsourcingTableHeaderAndLabel.退回修改' | translate }}
  </button>

  <!-- 指派 -->
  <button
    *ngIf="showAssigneeBtn"
    nz-button
    [nzShape]="'round'"
    nz-popover
    [nzPopoverContent]="assignPopoverContentTemplate"
    nzPopoverTrigger="hover"
    nzPopoverPlacement="bottom"
    flButton="pretty-default">
    {{ 'outsourcingTableHeaderAndLabel.指派' | translate }}
  </button>
  <ng-template #assignPopoverContentTemplate>
    <div class="tag-list">
      <span
        *ngIf="_service.btnArr.includes('bulk:garment-outsourcing-assignee-charge')"
        (click)="handleButtonAction(sampleOperateBtnEnum.assignEmployee)"
        >{{ 'outsourcingTableHeaderAndLabel.指派负责人' | translate }}</span
      >
      <span
        *ngIf="
          _service.btnArr.includes('bulk:garment-outsourcing-assignee-merchandiser') ||
          _service.btnArr.includes('bulk:garment-outsourcing-assignee-qc')
        "
        (click)="handleButtonAction(sampleOperateBtnEnum.assignMerOrQc)"
        >{{ 'outsourcingTableHeaderAndLabel.指派跟单员/QC' | translate }}</span
      >
    </div>
  </ng-template>

  <!-- 新建 -->
  <!-- <button
    *ngIf="_service.btnArr.includes('bulk:garment-outsourcing-create')"
    nz-button
    flButton="pretty-primary"
    [nzShape]="'round'"
    (click)="jumpDetail({ id: 'add' })">
    <i nz-icon [nzIconfont]="'icon-xinjian1'"></i>
    {{ 'btn.add' | translate }}
  </button> -->

  <!-- 一键入库：受权限控制 -->
  <button
    *ngIf="_service.btnArr.includes('bulk:garment-outsourcing-inbound')"
    nz-button
    nzType="primary"
    [nzShape]="'round'"
    (click)="handleButtonAction(sampleOperateBtnEnum.oneClickInbound)"
    [flcDisableOnClick]="1000">
    {{ 'outsourcingTableHeaderAndLabel.一键入库' | translate }}
  </button>

  <!-- 预付款：受权限控制 -->
  <button
    *ngIf="_service.btnArr.includes('bulk:garment-outsourcing-upfront-payment')"
    nz-button
    nzType="primary"
    [nzShape]="'round'"
    (click)="handleButtonAction(sampleOperateBtnEnum.onAdvancePayment)"
    [flcDisableOnClick]="1000">
    {{ 'outsourcingTableHeaderAndLabel.预付款' | translate }}
  </button>

  <!-- 是否整单 -->
  <div>
    {{ 'outsourcingTableHeaderAndLabel.是否整单' | translate }}
    <nz-switch [ngModel]="isFullOrder" (ngModelChange)="switchIsFullOrder($event)"></nz-switch>
  </div>

  <!-- 导出 非整单展示 -->
  <button
    *ngIf="!isFullOrder"
    nz-button
    nzShape="round"
    flButton="pretty-default"
    [nzLoading]="isExporting"
    (click)="handleButtonAction(sampleOperateBtnEnum.export)">
    <i nz-icon nzIconfont="icon-xiazai"></i> {{ 'btn.export' | translate }}
  </button>
</ng-template>
<!-- 去掉tab显示 -->
<nz-tabset
  style="display: none"
  #tabsetContainer
  id="tabsetContainer"
  nzType="card"
  [(nzSelectedIndex)]="tabsetIndex"
  (nzSelectChange)="tabsetChange($event)">
  <nz-tab nzTitle="{{ 'outsourcingTableHeaderAndLabel.全部' | translate }}"></nz-tab>
  <nz-tab nzTitle="{{ 'outsourcingTableHeaderAndLabel.未发单' | translate }}"></nz-tab>
  <nz-tab nzTitle="{{ 'outsourcingTableHeaderAndLabel.已发单' | translate }}"></nz-tab>
</nz-tabset>
<div class="table-box" style="background-color: white">
  <flc-table
    #tableRef
    [tableHeader]="tableHeaders"
    [template]="tdTemplate"
    [tableConfig]="tableConfig"
    (getDetails)="jumpDetail($event)"
    (indexChanges)="onIndexChange($event)"
    (sizeChanges)="onSizeChange($event)"
    (sortDataLists)="sortOrderChange($event)"
    [id]="selectedId"
    (getCount)="getCount($event)">
    <ng-template let-data="data" #tdTemplate>
      <!-- 订单需求号 -->
      <ng-container *ngIf="data.isTd && data.key === 'io_code'">
        <div class="supplier-wrapper">
          <flc-text-truncated [data]="data?.item?.io_code"></flc-text-truncated>
          <span *ngIf="data?.item?.order_change_status === 2" class="index">有更新</span>
          <div class="update-tip" *ngIf="data.item.bulk_bom_update_times">BOM更新+{{ data.item.bulk_bom_update_times }}</div>
          <div class="urgent-tip" *ngIf="data.item.urgent_status === 20">急</div>
        </div>
      </ng-container>
      <!-- 订单标签 -->
      <ng-container *ngIf="data.isTd && data.key === 'order_labels'">
        <flc-text-truncated [data]="data?.item?.order_labels?.join('、')"></flc-text-truncated>
      </ng-container>
      <!-- 大货单号 -->
      <ng-container *ngIf="data.isTd && data.key === 'bulk_codes'">
        <flc-text-truncated [data]="data?.item?.bulk_codes?.join('、')"></flc-text-truncated>
      </ng-container>
      <!-- 加工厂 -->
      <ng-container *ngIf="data.isTd && data.key === 'distribution_factory_name'">
        <flc-text-truncated
          [data]="
            data?.item?.distribution_factory_name &&
            data?.item?.distribution_factory_name.length &&
            data?.item?.distribution_factory_name.join('、')
          "></flc-text-truncated>
      </ng-container>
      <!-- 交付日期 -->
      <ng-container *ngIf="data.isTd && data.key === 'po_due_times'">
        <flc-text-truncated
          [data]="data?.item?.po_due_times && data?.item?.po_due_times.length && data?.item?.po_due_times.join('、')"></flc-text-truncated>
      </ng-container>
      <!-- 跟单员 -->
      <ng-container *ngIf="data.isTd && data.key === 'merchandisers'">
        <flc-text-truncated
          [data]="data?.item?.merchandisers?.length && formatterArr(data?.item?.merchandisers, 'merchandiser_name')"></flc-text-truncated>
      </ng-container>
      <!-- QC -->
      <ng-container *ngIf="data.isTd && data.key === 'qcs'">
        <flc-text-truncated [data]="data?.item?.qcs?.length && formatterArr(data?.item?.qcs, 'qc')"></flc-text-truncated>
      </ng-container>
      <!-- 工段 -->
      <ng-container *ngIf="data.isTd && data.key === 'factory_stage_name'">
        <flc-text-truncated [data]="data?.item?.factory_stage_name?.join('、')"></flc-text-truncated>
      </ng-container>
    </ng-template>
  </flc-table>
</div>

<!--批量归还-->
<nz-modal
  [nzVisible]="modalVisible === 1 || modalVisible === 2"
  [nzTitle]="modalVisible === 1 ? confirmBatchBorrowTitle : ''"
  [nzContent]="confirmBatchBorrowContent"
  [nzFooter]="confirmBatchBorrowFooter"
  (nzOnCancel)="modalHandleCancel()"
  [nzWidth]="modalVisible === 1 ? '1000px' : '500px'"
  nzWrapClassName="design-draft-confirm-modal">
  <ng-template #confirmBatchBorrowTitle>
    <div class="title">选择外发厂</div>
  </ng-template>

  <ng-template #confirmBatchBorrowContent>
    <div *ngIf="modalVisible === 1" class="body">
      <nz-table #basicTable [nzShowPagination]="false" style="max-height: 500px" [nzScroll]="{ y: '500px' }" [nzData]="batchReturnData">
        <thead>
          <tr>
            <th nzWidth="125px">订单号</th>
            <th nzWidth="125px">款号</th>
            <th nzWidth="100px">订单类型</th>
            <th nzWidth="100px">生产类型</th>
            <th nzWidth="150px">大货单号</th>
            <th nzWidth="200px">加工厂</th>
            <th nzWidth="125px">单价</th>
            <th nzWidth="125px">税率</th>
            <th nzWidth="125px">含税单价</th>
            <th nzWidth="100px">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of batchReturnData">
            <td>{{ data.io_code }}</td>
            <td>{{ data.style_code }}</td>
            <td>{{ data.order_category_label }}</td>
            <td>{{ data.order_production_type_label }}</td>
            <td><input nzAllowClear nz-input [(ngModel)]="data.bulk_code" placeholder="请输入" /></td>
            <td>
              <flc-dynamic-search-select
                [dataUrl]="searchOptionFactoryUrl"
                [(ngModel)]="data.ss_factory_code"
                [transData]="{ value: 'ss_factory_code', label: 'label' }"
                [column]="'factory_name'"
                [defaultValue]="{
                  label: data.factory_name,
                  value: data.ss_factory_code
                }"
                [minWidth]="160"
                [payLoad]="{ factory_type: factory_type }"
                (handleSearch)="onFactoryChange($event, data)">
              </flc-dynamic-search-select>
              <div class="backup-select" (click)="onSelectFactory(data)"></div>
            </td>
            <td>
              <nz-input-number-group nzAddOnAfter="元">
                <nz-input-number
                  [nzPrecision]="5"
                  [nzMin]="0"
                  [nzMax]="*********.99999"
                  [nzPlaceHolder]="'placeholder.input' | translate"
                  (ngModelChange)="UnitPriceChange($event, data)"
                  [(ngModel)]="data.unit_price"></nz-input-number>
              </nz-input-number-group>
            </td>
            <td>
              <nz-input-number-group nzAddOnAfter="%">
                <nz-input-number
                  [nzPrecision]="0"
                  [nzMin]="0"
                  [nzMax]="*********"
                  [nzPlaceHolder]="'placeholder.input' | translate"
                  (ngModelChange)="taxRateChange($event, data)"
                  [(ngModel)]="data.tax_rate"></nz-input-number>
              </nz-input-number-group>
            </td>
            <td>
              <nz-input-number-group nzAddOnAfter="元">
                <nz-input-number
                  [nzPrecision]="5"
                  [nzMin]="0"
                  [nzMax]="*********.99999"
                  [nzDisabled]="true"
                  [nzPlaceHolder]="'placeholder.input' | translate"
                  [(ngModel)]="data.tax_price"></nz-input-number>
              </nz-input-number-group>
            </td>
            <td>
              <button
                nz-button
                flButton="border-reset"
                class="btn-radius-8px"
                nz-tooltip
                [nzTooltipTitle]="'一键同步该订单外发的加工厂、含税单价'"
                (click)="onFactoryAsyn(data)">
                <i nz-icon [nzIconfont]="'icon-zhongzhi1'"></i>
              </button>
            </td>
          </tr>
        </tbody>
      </nz-table>
    </div>
    <div *ngIf="modalVisible === 2" nz-row style="padding: 20px">
      <nz-form-label>选择指派人</nz-form-label>
      <nz-form-control [flcErrorTip]="'请选择'">
        <flss-department-select
          [requestInside]="true"
          [canSelectDepartment]="false"
          (onSelectChange)="onSelectChange($event)"
          [isDefaultOpen]="true"
          [selectCurrentUser]="false">
        </flss-department-select>
      </nz-form-control>
    </div>
  </ng-template>
  <ng-template #confirmBatchBorrowFooter>
    <!-- <div style="text-align: center"> -->
    <button nz-button flButton="default" (click)="modalHandleCancel()">取消</button>
    <button nz-button flButton="pretty-primary" (click)="modalHandleConfirmed()">确定</button>
    <!-- </div> -->
  </ng-template>
</nz-modal>
