.container {
  padding-top: 8px;
  .uploader-container {
    height: 124px;
    background: #f4f8fe;
    padding: 0;
    .uploader {
      display: block;
      height: 124px;
    }
  }
  .ant-table-tbody > tr.ant-table-row:hover > td.uploader-container,
  .ant-table-tbody > tr > td.ant-table-cell-row-hover {
    background: #deefff;
  }
}
:host ::ng-deep {
  .ant-form-item,
  .ant-form-item-control,
  .ant-form-item-control-input,
  .ant-form-item-control-input-content {
    margin-bottom: 0;
    background: inherit;
  }
}

.footer {
  position: absolute;
  width: 100%;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  height: 48px;
  background: #ffffff;
  box-shadow: 0 -1px 3px #ced5de80;
  z-index: 10;
  > button {
    width: 80px;
  }
}
