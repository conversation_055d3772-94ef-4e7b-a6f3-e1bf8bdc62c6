import { AfterViewInit, Component, ComponentRef, ElementRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Resizable } from 'src/app/decorator/resizable';
import { AppStorageService } from 'src/app/shared/app-storage.service';
import { OrganizationService } from './organization.service';
import { StructureTreeComponent } from './structure-tree/structure-tree.component';

@Component({
  selector: 'app-organization',
  templateUrl: './organization.component.html',
  styleUrls: ['./organization.component.scss'],
  providers: [OrganizationService],
})
export class OrganizationComponent extends Resizable implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild(StructureTreeComponent) tree!: ComponentRef<StructureTreeComponent>;
  @ViewChild('table') table?: ElementRef;
  @ViewChild('org') org?: ElementRef;
  treeMaxHeight: any;

  constructor(public _storage: AppStorageService, private _service: OrganizationService) {
    super();
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.resize();
      this._service.treeComponent = this.tree;
    });
  }

  ngOnInit(): void {
    super.ngOnInit();
    this._service.btnArr = [...this._storage.getUserActions('hr/org'), ...this._storage.getUserActions('settings/user')];
  }

  ngOnDestroy(): void {
    super.ngOnDestroy();
  }

  resize(): void {
    this.treeMaxHeight = window?.innerHeight - 48 - 24;
  }

  /**
   * 离开时的路由守卫
   * @returns
   */
  canLeave() {
    // 编辑状态并且数据有更改的行为
    if (this._service.editable && this._service.departForm?.dirty) {
      return false;
    } else {
      return true;
    }
  }
}
