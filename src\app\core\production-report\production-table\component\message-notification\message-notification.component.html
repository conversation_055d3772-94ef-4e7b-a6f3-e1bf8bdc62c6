<nz-badge [nzCount]="messageCount" [nzOverflowCount]="999" nzSize="small" [nzOffset]="[0, 8]">
  <button
    nz-button
    [nzLoading]="pageLoading"
    class="badge-box"
    nz-popover
    [(nzPopoverVisible)]="visible"
    nzPopoverTrigger="click"
    [nzPopoverPlacement]="'topLeft'"
    [nzPopoverContent]="messgaeContentTemplate">
    <i nz-icon [nzIconfont]="'icon-xiaoxitongzhi'" style="font-size: 20px"></i>
  </button>
</nz-badge>

<ng-template #messgaeContentTemplate>
  <nz-spin [nzSpinning]="pageLoading">
    <ng-container *ngIf="messageList?.length; else onDataTpl">
      <div style="max-height: 60vh; overflow-y: auto">
        <div class="message-box-item" *ngFor="let item of messageList">
          <div class="message-header">
            <span class="name" [ngStyle]="{ color: item.level ? warningLevel[item.level]?.color : '' }">
              <flc-text-truncated [data]="item?.warning_name"></flc-text-truncated>
            </span>
            <span class="time">{{ item?.created_at > 0 ? (item?.created_at | date: 'yyyy-MM-dd HH:mm:ss') : '' }}</span>
          </div>

          <div class="message-content">
            {{ item?.warning_message }}
          </div>

          <div class="message-footer">
            <a nz-button nzType="link" style="text-decoration: underline" (click)="dealMessage(item)">{{
              translateName + '知道了' | translate
            }}</a>
            <!-- <a nz-button nzType="link" style="text-decoration: underline" (click)="dealMessage(item)">{{
              translateName + (item?.message_type === 1 ? '立即处理' : '知道了') | translate
            }}</a> -->
          </div>
        </div>
      </div>
    </ng-container>
    <ng-template #onDataTpl>
      <div class="message-box-item">
        <flc-no-data></flc-no-data>
      </div>
    </ng-template>
  </nz-spin>
</ng-template>
