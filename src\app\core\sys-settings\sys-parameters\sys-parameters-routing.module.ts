import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SysParametersDetailComponent } from './parameter-detail/sys-parameters-detail.component';
import { FlcLeaveGuard } from 'fl-common-lib';

const routes: Routes = [
  {
    path: '',
    component: SysParametersDetailComponent,
    data: { noKeepAlive: true },
    canDeactivate: [FlcLeaveGuard],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class SysParametersRoutingModule {}
