import { ReportRange } from './../../production-report.enum';

// 颜色尺码明细弹框标题
export function renderModalTitle(tab: number, key: string): string {
  const transferKeyName: { [key: string]: string } = {
    order_count: '分配订单数',
    daily_cutting_qty: '日裁数',
    cutting_qty: tab === ReportRange.daily ? '累计裁数' : '总裁数',
    daily_sewing_qty: '日车缝完成数',
    sewing_qty: tab === ReportRange.daily ? '累计车缝完成数' : '车缝完成数',
    daily_consolidation_qty: '日后道完成数',
    consolidation_qty: tab === ReportRange.daily ? '累计后道完成数' : '后道完成数',
    daily_qualified_qty: '质检-日合格数',
    qualified_qty: tab === ReportRange.daily ? '质检-累计合格数' : '质检-合格数',
    daily_defective_qty: '质检-日不良数',
    defective_qty: tab === ReportRange.daily ? '质检-累计不良数' : '质检-不良数',
    daily_final_qualified_qty: '终检-日合格数',
    final_qualified_qty: tab === ReportRange.daily ? '终检-累计合格数' : '终检-合格数',
    daily_final_defective_qty: '终检-日不良数',
    final_defective_qty: tab === ReportRange.daily ? '终检-累计不良数' : '终检-不良数',
    daily_transport_qty: '日走货数',
    transport_qty: tab === ReportRange.daily ? '累计走货数' : '走货数',
  };
  return transferKeyName[key];
}

// 生产状态
export const ProductStatusOptions: Array<{ label: string; value: number }> = [
  {
    label: '未开始',
    value: 1,
  },
  {
    label: '生产中',
    value: 2,
  },
  {
    label: '已完成',
    value: 3,
  },
  {
    label: '已取消',
    value: 4,
  },
];

// 走货状态
export const TransportStatusOptions: Array<{ label: string; value: number }> = [
  {
    label: '未完成',
    value: 1,
  },
  {
    label: '已完成',
    value: 2,
  },
  {
    label: '已取消',
    value: 3,
  },
];
