/**
--计划进度跟踪表
CREATE TABLE if not exists "tracking_schedule"
(
    id                   bigserial   NOT NULL,
    io_uuid              varchar(255) NOT NULL,
    order_code           varchar(255),                           ---大货单号
    customer             varchar(255),                           ---客户
    category             varchar(255),                           ---品名
    style_code           varchar(255),                           ---款号
    color_names          varchar(255)  NULL,                     ---颜色、隔开
    total_qty            int8 DEFAULT 0,                         ---总件数
    due_times            varchar(255)  NULL,                     ---交期、隔开
    order_date           timestamptz,                            ---下单日期
    material_code        varchar(255),                           ---面料编码
    material_name        varchar(255),                           ---面料名称
    deleted              bool           DEFAULT false,           ---是否删除

    customer_data_plan_time     timestamptz   NULL,                ---客人工艺资料齐备日期 计划
    customer_data_real_time     timestamptz   NULL,                ---客人工艺资料齐备日期 实际
    fitting_plan_time           timestamptz   NULL,                ---fit试穿样提供日期 计划
    fitting_real_time           timestamptz   NULL,                ---fit试穿样提供日期 实际
    fabric_quality_plan_time    timestamptz   NULL,                ---面料色卡品质样日期 计划
    fabric_quality_real_time    timestamptz   NULL,                ---面料色卡品质样日期 实际
    accessory_plan_time         timestamptz   NULL,                ---辅料样提供日期 计划
    accessory_real_time         timestamptz   NULL,                ---辅料样提供日期 实际
    printing_plan_time          timestamptz   NULL,                ---印绣花样提供日期 计划
    printing_real_time          timestamptz   NULL,                ---印绣花样提供日期 实际
    fabric_cylinder_plan_time   timestamptz   NULL,                ---面料头缸确认日期 计划
    fabric_cylinder_real_time   timestamptz   NULL,                ---面料头缸确认日期 实际
    preproduction_plan_time     timestamptz   NULL,                ---产前样确认日期 计划
    preproduction_real_time     timestamptz   NULL,                ---产前样确认日期 实际
    bulk_samle_plan_time        timestamptz   NULL,                ---大货样板日期 计划
    bulk_samle_real_time        timestamptz   NULL,                ---大货样板日期 实际
    first_spreading_plan_time   timestamptz   NULL,                ---客户确认开裁日期 计划
    first_spreading_real_time   timestamptz   NULL,                ---客户确认开裁日期 实际

    fabric_sign_line            varchar(255)  NULL,                ---面料划头
    fabric_instock_plan_time    timestamptz   NULL,                ---面料要求到仓日期 计划
    fabric_instock_real_time    timestamptz   NULL,                ---面料要求到仓日期 实际
    thread_instock_plan_time    timestamptz   NULL,                ---线材要求到仓日期 计划
    thread_instock_real_time    timestamptz   NULL,                ---线材要求到仓日期 实际
    zipper_instock_plan_time    timestamptz   NULL,                ---标牌拉链要求到仓日期 计划
    zipper_instock_real_time    timestamptz   NULL,                ---标牌拉链要求到仓日期 实际
    packing_instock_plan_time   timestamptz   NULL,                ---包装材料到仓日期 计划
    packing_instock_real_time   timestamptz   NULL,                ---包装材料到仓日期 实际
    fabric_requirements         varchar(255)  NULL,                ---供布要求

    created_at           timestamptz   NULL,
    updated_at           timestamptz   NULL,
    gen_user_id          int8          NOT NULL DEFAULT 0,
    gen_user_name        varchar(255)  NULL,
    gen_dept_id          int8          NOT NULL DEFAULT 0,
  version        int8          NOT NULL DEFAULT 1,

    CONSTRAINT tracking_schedule_pkey PRIMARY KEY (id)
);

---计划跟踪子表
CREATE TABLE if not exists "tracking_schedule_detail"
(
    id                          bigserial   NOT NULL,
    io_uuid                     varchar(255) NOT NULL,
    deleted                     bool           DEFAULT false,           ---是否删除

    ---生产计划
    production_line_names       varchar(255)  NULL,                ---组别 、隔开
    sewing_start_plan_time      timestamptz   NULL,                ---车间最早的开始时间 计划
    sewing_start_real_time      timestamptz   NULL,                ---车间最早的开始时间 实际
    sewing_end_plan_time        timestamptz   NULL,                ---车间最晚的结束时间 计划
    sewing_end_real_time        timestamptz   NULL,                ---车间最晚的结束时间 实际
    daily_target_qty            int8          NOT NULL DEFAULT 0,  ---每日目标产量

    ---二次工艺
    type_names                  varchar(255)  NULL,                ---二次工艺类型、隔开
    factory_names               varchar(255)  NULL,                ---配套工厂成衣-二次工艺、隔开
    return_plan_time            timestamptz   NULL,                ---返厂日期 计划
    return_real_time            timestamptz   NULL,                ---返厂取验片PAD最早时间 实际
    daily_return_target_qty     int8          NOT NULL DEFAULT 0,  ---每日返厂目标产量

    ---质量管控
    inspection_mid_plan_time    timestamptz   NULL,                ---客人中期日期 计划
    inspection_mid_real_time    timestamptz   NULL,                ---客人中期日期 实际
    inspection_last_plan_time   timestamptz   NULL,                ---客人尾期日期 计划
    inspection_last_real_time   timestamptz   NULL,                ---客人尾期日期 实际

    ---成品仓库
    outstock_plan_time          timestamptz   NULL,                ---成品出库日期 计划
    outstock_real_time          timestamptz   NULL,                ---成品出库日期 实际

    created_at                  timestamptz   NULL,
    updated_at                  timestamptz   NULL,
    CONSTRAINT tracking_schedule_detail_pkey PRIMARY KEY (id)
);
 */

export interface OrderTrackingLineModel {
  id?: number | null;
  io_uuid?: string | null;
  /// 大货单号
  order_code?: string | null;
  /// 客户
  customer?: string | null;
  /// 品名
  category?: string | null;
  /// 款号
  style_code?: string | null;
  /// 颜色、隔开
  color_names?: string | null;
  /// 总件数
  total_qty?: number | null;
  /// 交期、隔开
  due_times?: number | null;
  /// 下单日期
  order_date?: number | null;
  /// 面料编码
  material_code?: string | null;
  /// 面料名称
  material_name?: string | null;
  /// 辅料名称
  fabric_material_name?: string | null;
  /// 客人工艺资料齐备日期 计划
  customer_data_plan_time?: number | null;
  /// 客人工艺资料齐备日期 实际
  customer_data_real_time?: number | null;
  /// fit试穿样提供日期 计划
  fitting_plan_time?: number | null;
  /// fit试穿样提供日期 实际
  fitting_real_time?: number | null;
  /// 面料色卡品质样日期 计划
  fabric_quality_plan_time?: number | null;
  /// 面料色卡品质样日期 实际
  fabric_quality_real_time?: number | null;
  /// 辅料样提供日期 计划
  accessory_plan_time?: number | null;
  /// 辅料样提供日期 实际
  accessory_real_time?: number | null;
  /// 印绣花样提供日期 计划
  printing_plan_time?: number | null;
  /// 印绣花样提供日期 实际
  printing_real_time?: number | null;
  /// 面料头缸确认日期 计划
  fabric_cylinder_plan_time?: number | null;
  /// 面料头缸确认日期 实际
  fabric_cylinder_real_time?: number | null;
  /// 产前样确认日期 计划
  preproduction_plan_time?: number | null;
  /// 产前样确认日期 实际
  preproduction_real_time?: number | null;
  /// 大货样板日期 计划
  bulk_samle_plan_time?: number | null;
  /// 大货样板日期 实际
  bulk_samle_real_time?: number | null;
  /// 客户确认开裁日期 计划
  first_spreading_plan_time?: number | null;
  /// 客户确认开裁日期 实际
  first_spreading_real_time?: number | null;
  /// 面料要求到仓日期 计划
  fabric_instock_plan_time?: number | null;
  /// 面料要求到仓日期 实际
  fabric_instock_real_time?: number | null;
  /// 线材要求到仓日期 计划
  thread_instock_plan_time?: number | null;
  /// 线材要求到仓日期 实际
  thread_instock_real_time?: number | null;
  /// 标牌拉链要求到仓日期 计划
  zipper_instock_plan_time?: number | null;
  /// 标牌拉链要求到仓日期 实际
  zipper_instock_real_time?: number | null;
  /// 包装材料到仓日期 计划
  packing_instock_plan_time?: number | null;
  /// 包装材料到仓日期 实际
  packing_instock_real_time?: number | null;
  /// 面料划头
  fabric_sign_line?: string | null;
  /// 供布要求
  fabric_requirements?: string | null;
  /// 组别 、隔开
  production_line_names?: string | null;
  /// 车间最早的开始时间 计划
  sewing_start_plan_time?: number | null;
  /// 车间最早的开始时间 实际
  sewing_start_real_time?: number | null;
  /// 车间最晚的结束时间 计划
  sewing_end_plan_time?: number | null;
  /// 车间最晚的结束时间 实际
  sewing_end_real_time?: number | null;
  /// 每日目标产量
  daily_target_qty?: number | null;
  /// 二次工艺类型、隔开
  type_names?: string | null;
  /// 配套工厂成衣-二次工艺、隔开
  factory_names?: string | null;
  /// 返厂日期 计划
  return_plan_time?: number | null;
  /// 返厂取验片PAD最早时间 实际
  return_real_time?: number | null;
  /// 每日返厂目标产量
  daily_return_target_qty?: number | null;
  /// 客人中期日期 计划
  inspection_mid_plan_time?: number | null;
  /// 客人中期日期 实际
  inspection_mid_real_time?: number | null;
  /// 客人尾期日期 计划
  inspection_last_plan_time?: number | null;
  /// 客人尾期日期 实际
  inspection_last_real_time?: number | null;
  /// 成品出库日期 计划
  outstock_plan_time?: number | null;
  /// 成品出库日期 实际
  outstock_real_time?: number | null;
  /// 版本号
  version: number;
}
