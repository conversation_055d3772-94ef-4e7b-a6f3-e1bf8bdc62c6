import { DatePipe } from '@angular/common';
import { Component, ElementRef, EventEmitter, OnInit, ViewChild } from '@angular/core';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { addDays, differenceInDays, endOfDay, format, startOfDay } from 'date-fns';
import { FlcDrawerHelperService, FlcModalService, FlcTableComponent, FlcTableHelperService, resizable } from 'fl-common-lib';
import { flatten, round } from 'lodash';
import { NzImage, NzImageService } from 'ng-zorro-antd/image';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { NzSelectComponent } from 'ng-zorro-antd/select';
import { debounceTime, finalize, Subscription } from 'rxjs';
import { SearchOptionsCustomContainerComponent } from 'src/app/components/search-options-custom-container/search-options-custom-container.component';
import { AppStorageService } from 'src/app/shared/app-storage.service';
import { BatchEditFieldComponent } from '../component/batch-edit-field/batch-edit-field.component';
import { EditNodeFormComponent } from '../component/edit-node-form/edit-node-form.component';
import { MaterialDialogComponent } from '../component/material-dialog/material-dialog.component';
import { MessageNotificationComponent } from '../component/message-notification/message-notification.component';
import { ProductionTableColorSpecDialogComponent } from '../production-table-color-spec-dialog/production-table-color-spec-dialog.component';
import { ProductionTableInspectionDialogComponent } from '../production-table-inspection-dialog/production-table-inspection-dialog.component';
import { ProductionTableMaterialDialogComponent } from '../production-table-material-dialog/production-table-material-dialog.component';
import { ProductionTableNewColorSpecDialogComponent } from '../production-table-new-color-spec-dialog/production-table-new-color-spec-dialog.component';
import {
  initDefaultTableHeaders,
  initSearchOptions,
  MaterialEnum,
  ProductionTableOrderLineInfo,
  ProductionTableOrderNode,
  STATUSMAP,
} from '../production-table.config';
import {
  BatchEditTypeEnum,
  EventTypeEnum,
  FieldEditAuthEnum,
  FieldLabelEnum,
  FieldTypeEnum,
  TemplateTypeEnum,
  TrackStatusEnum,
} from '../production-table.enum';
import { IBatchEditFieldOption, IMessageListItem, ITemplateFields } from '../production-table.interface';
import { ProductionTableService } from '../production-table.service';

@Component({
  selector: 'production-table',
  templateUrl: './production-table.html',
  styleUrls: ['./production-table.scss'],
  providers: [NzImageService],
})
@resizable()
export class ProductionTableComponent implements OnInit {
  @ViewChild('ProductionTableTemplateFiled') ProductionTableTemplateFiled!: FlcTableComponent;
  @ViewChild('searchBar') searchBar!: ElementRef;
  @ViewChild('messageNotificationRef') messageNotificationRef!: MessageNotificationComponent;
  translateName = 'ProductionTableTemplateFiled.';
  lang = localStorage.getItem('lang') || 'zh';
  searchOptionFetchUrl = this._service.serviceUrl + '/customer/operation';

  track_status = 0; // 跟踪状态
  trackStatusStatistics = {}; // 跟踪状态数量
  orderStatusList: Array<any> = [
    { label: '全部', key: 'total', value: 0, checked: true },
    {
      label: '进行中',
      key: 'tracking_total',
      value: 1,
      checked: false,
    },
    {
      label: '跟单结束',
      key: 'track_end_total',
      value: 2,
      checked: false,
    },
  ];

  searchData: any = {};
  material_fileds = ['物料入库', '物料采购', '领料出库'];

  searchOptions = initSearchOptions();
  processTag = [
    {
      text: '未开始',
      color: '#a4a5a7',
    },
    {
      text: '进行中',
      color: '#4d96ff',
    },
    {
      text: '完成',
      color: '#00af7f',
    },
    {
      text: '延期完成',
      color: '#00c78f74',
    },
    {
      text: '预警',
      color: '#fe5d56',
    },
    {
      text: '逾期',
      color: 'red',
    },
  ];

  // 表格配置
  tableConfig = {
    hasAction: true,
    detailBtn: false,
    hasCheckbox: true,
    dataList: [],
    count: 20,
    pageSize: 20,
    pageIndex: 1,
    order_by: [],
    height: 800,
    loading: false,
    uniqueId: 'id',
    translateName: 'ProductionTableTemplateFiled.',
    tableName: 'ProductionTableTemplateFiled',
    actionWidth: '126px',
    inactiveId: 'status',
    inactiveValue: 0,
    settingBtnPos: 'start',
    version: '1.0.0',
    canDragSort: true,
  };
  defaultTableHeader: any[] = initDefaultTableHeaders();
  tableHeader: any[] = [];
  selectedCount = 0;
  selectedIds: number[] = [];
  _searchDataInitState: any;
  visible = false;
  checkBoxVisible = false;
  proceParamsCopy = this.initProceParams();
  proceParams = this.initProceParams();
  FieldLabelEnum = FieldLabelEnum;
  FieldTypeEnum = FieldTypeEnum;
  TrackStatusEnum = TrackStatusEnum;
  TemplateTypeEnum = TemplateTypeEnum;

  @ViewChild('batchSelectRef') batchSelectRef!: NzSelectComponent;

  private _eventSubscription?: Subscription;

  basicInfoConfig: any = [
    { label: '订单需求号', key: 'bulk_order_code' },
    { label: '款式编码', key: 'style_code' },
    { label: '加工厂', key: 'factory_name' },
  ];

  constructor(
    private router: Router,
    public _service: ProductionTableService,
    private route: ActivatedRoute,
    private message: NzMessageService,
    private _modal: NzModalService,
    private _image: NzImageService,
    private _fb: FormBuilder,
    private _flcModalService: FlcModalService,
    private _drawerHelp: FlcDrawerHelperService,
    private datePipe: DatePipe,
    private _storage: AppStorageService,
    private _FlcTableHelperService: FlcTableHelperService
  ) {
    this._service.btnArr = this._storage.getUserActions('production-report/production-table/list');
    this._service.fieldArr = this._storage.getFieldActions('production-report/production-table/list');
    this.renderHeader();
  }

  ngOnInit() {
    this.initSearchListConfig();
    this.getOptions();
    this.getUpdateBatchFieldList();
    this.getTrackStatusStatistics();
    this._getDataList();
    (this as any).addResizePageListener();

    this._eventSubscription = this._service.eventEmitter.subscribe((res: any) => {
      if (res === EventTypeEnum.refreshList) {
        this._getDataList();
      }
    });
  }

  all_fields: string[] = [];
  show_field(name: string) {
    this.all_fields = this._service.fieldArr?.map((field: any) => {
      return field?.fieldName;
    });
    return this.all_fields.includes(name)
      ? this._service.fieldArr?.find((field: any) => field.fieldName === name && field?.viewPermission === 2)
      : true;
  }

  ngAfterViewInit() {
    this.resizePage();
  }

  ngOnDestroy(): void {
    (this as any).removeResizePageListener();
    this._eventSubscription?.unsubscribe();
  }

  resizePage() {
    setTimeout(() => {
      this.tableConfig.height = window.innerHeight - (this.searchBar.nativeElement.clientHeight ?? 0) - 270;
      this.tableConfig = { ...this.tableConfig };
    });
  }

  initSearchListConfig() {
    const searchListConfig = JSON.parse(localStorage.getItem(this.customSearchListConfig.localKey) || 'false');
    if (searchListConfig && searchListConfig.version === this.customSearchListConfig.version) {
      this.searchOptions = searchListConfig.configList;
    }
  }

  initProceParams() {
    return {
      id: null,
      plan_end_time: null,
      actual_start_time: null,
      actual_end_time: null,
      content_type: null,
      plan_qty: null,
      actual_qty: null,
      actual_qty_source: 2,
      // plan_end_time_cascade: false,
      repair_qty: 0,
    };
  }
  previewUploadImgs(currentItem: any, event: MouseEvent) {
    event.stopPropagation();
    const list: NzImage[] = flatten(currentItem.node_list.map((item: any) => item.pictures)).map((item: any) => ({ src: item.url }));
    if (list.length > 0) {
      this._image.preview(list);
    } else {
      this.message.warning(this._service.translateValue(this.translateName + '没有可以预览的图片'));
    }
  }
  // 展示物料modal
  showMaterilModal(item: any, bulk_order_id: any, order?: any) {
    this._modal.create({
      nzClassName: 'material_dialog',
      nzTitle: item.template_node_name,
      nzWidth: 850,
      nzFooter: null,
      nzContent: MaterialDialogComponent,
      nzComponentParams: {
        bulkOrderId: bulk_order_id,
        type: MaterialEnum[item.template_node_name as keyof typeof MaterialEnum],
        orderInfo: order,
      },
    });
  }

  orderLineInfo!: ProductionTableOrderLineInfo;
  onEdit(item: ProductionTableOrderNode, order: ProductionTableOrderLineInfo) {
    const { bulk_order_id, factory_id } = order;
    if (item.node_type) {
      let currentModal: NzModalRef | undefined;
      let modalTitle: string;
      let closeModal: EventEmitter<boolean> | undefined;
      // 1 裁剪 2 车缝 3 巡检 4 后道 8物料
      switch (item.node_type) {
        case 1:
        case 2:
        case 9:
        case 4:
          switch (item.node_type) {
            case 1:
              modalTitle = '裁剪详情';
              break;
            case 2:
              modalTitle = '车缝详情';
              break;
            case 4:
              modalTitle = '后道详情';
              break;
            case 9:
              modalTitle = '送货详情';
              break;
          }
          const qtyModal = this._modal.create({
            nzWidth: 680,
            nzTitle: this._service.translateValue(this.translateName + modalTitle),
            nzMaskClosable: false,
            nzContent: ProductionTableNewColorSpecDialogComponent,
            nzBodyStyle: {
              padding: '0',
            },
            nzComponentParams: {
              nodeInfo: item,
              orderInfo: order,
            },
            nzFooter: null,
          });
          currentModal = qtyModal;
          closeModal = qtyModal.componentInstance?.closeModal;
          break;
        case 8:
          const materialModal = this._modal.create({
            nzWidth: 980,
            nzTitle: this._service.translateValue(this.translateName + '物料详情'),
            nzMaskClosable: false,
            nzContent: ProductionTableMaterialDialogComponent,
            nzBodyStyle: {
              padding: '0',
            },
            nzComponentParams: {
              nodeInfo: item,
              orderInfo: order,
            },
            nzFooter: null,
          });
          currentModal = materialModal;
          closeModal = materialModal.componentInstance?.closeModal;
          break;
      }
      closeModal?.subscribe((e) => {
        if (e) {
          this._getDataList();
        }
        currentModal?.close();
      });
    } else {
      if (item.content_type === 3) {
        if (this.material_fileds.includes(item.template_node_name)) {
          if (!item?.bulk_node_status) {
            this.message.warning(this._service.translateValue(this.translateName + '暂无数据~'));
            return;
          }
          this.showMaterilModal(item, bulk_order_id, order);
          return;
        }
        switch (item.template_node_name) {
          case '裁剪':
          case '送货': {
            const modal = this._modal.create({
              nzTitle: item.template_node_name,
              nzWidth: 850,
              nzFooter: null,
              nzContent: ProductionTableColorSpecDialogComponent,
              nzBodyStyle: {
                padding: '0 16px 16px 16px',
              },
              nzComponentParams: {
                bulkOrderId: bulk_order_id,
                factory_id: factory_id,
                rawNodeList: item.node_list,
                orderInfo: order,
              },
            });
            modal.componentInstance?.onSave.subscribe((result) => {
              if (result) {
                const data = {
                  id: item.id,
                  content_type: item.content_type,
                  node_list: result,
                };
                this._service.updateProduction(data).subscribe((res) => {
                  if (res.code === 200) {
                    this.message.create('success', this._service.translateValue('success.modify'));
                    modal.close();
                    this._getDataList();
                  }
                });
              } else {
                modal.close();
              }
            });
            break;
          }
          case '成品检验':
            this.proceParams = this.initProceParams();
            this.proceParamsCopy = this.initProceParams();
            this.visible = true;
            this.proceParams = JSON.parse(JSON.stringify(item));
            this.proceParamsCopy = JSON.parse(JSON.stringify(item));
            this.orderLineInfo = order;
            break;
          default:
            break;
        }
      } else {
        let currentModal: NzModalRef | undefined;
        let closeModal: EventEmitter<boolean> | undefined;
        if ([TemplateTypeEnum.standardTpl]?.includes(item?.template_type)) {
          this._modal.create({
            nzWidth: 320,
            nzTitle: item?.template_node_name,
            nzMaskClosable: false,
            nzContent: EditNodeFormComponent,
            nzBodyStyle: {
              padding: '0',
            },
            nzComponentParams: {
              node: item,
              isBatch: false,
              orderInfo: order,
            },
            nzFooter: null,
          });
        } else {
          switch (item?.template_type) {
            // 质量模版
            case TemplateTypeEnum.qualityTpl:
              // eslint-disable-next-line no-case-declarations
              const inspectionModal = this._modal.create({
                nzWidth: this.lang === 'en' ? 500 : 380,
                nzTitle: item?.template_node_name,
                nzMaskClosable: false,
                nzContent: ProductionTableInspectionDialogComponent,
                nzBodyStyle: {
                  padding: '0',
                },
                nzComponentParams: {
                  nodeInfo: item,
                  orderInfo: order,
                },
                nzFooter: null,
              });
              currentModal = inspectionModal;
              closeModal = inspectionModal.componentInstance?.closeModal;
          }
          closeModal?.subscribe((e) => {
            if (e) {
              this._getDataList();
            }
            currentModal?.close();
          });
        }
      }
    }
  }

  onModelChange() {
    if (
      this.proceParams?.plan_end_time !== null &&
      this.proceParamsCopy?.plan_end_time !== null &&
      format(startOfDay(this.proceParams?.plan_end_time), 'yyyy-MM-dd') !==
        format(startOfDay(this.proceParamsCopy?.plan_end_time), 'yyyy-MM-dd')
    ) {
      this.checkBoxVisible = true;
    } else {
      this.checkBoxVisible = false;
    }
  }

  numberModelChange() {
    if (this.proceParams.actual_qty !== null && this.proceParams.actual_qty !== this.proceParamsCopy.actual_qty) {
      this.proceParams.actual_qty_source = 1;
    } else {
      this.proceParams.actual_qty_source = 2;
    }
  }

  handleOkModal() {
    // if (this.proceParams.plan_end_time !== null && this.proceParams.plan_end_time !== this.proceParamsCopy.plan_end_time) {
    //   this.proceParams.plan_end_time_cascade = true;
    // }
    let data: any = {};
    if (!this.proceParams.content_type) return;
    switch (this.proceParams.content_type) {
      case 1:
        data = {
          id: this.proceParams?.id,
          content_type: this.proceParams?.content_type,
          // plan_end_time_cascade: this.proceParams.plan_end_time_cascade,
          plan_end_time: this.proceParams?.plan_end_time === null ? null : format(this.proceParams?.plan_end_time, 'T'),
          actual_start_time: this.proceParams?.actual_start_time === null ? null : format(this.proceParams?.actual_start_time, 'T'),
          actual_end_time: this.proceParams?.actual_end_time === null ? null : format(this.proceParams?.actual_end_time, 'T'),
        };
        break;
      case 2:
        data = {
          id: this.proceParams?.id,
          content_type: this.proceParams?.content_type,
          plan_qty: this.proceParams?.plan_qty,
          actual_qty: this.proceParams?.actual_qty,
          actual_qty_source: this.proceParams?.actual_qty_source,
        };
        break;
      case 3:
        data = {
          id: this.proceParams?.id,
          content_type: this.proceParams?.content_type,
          // plan_end_time_cascade: this.proceParams.plan_end_time_cascade,
          plan_end_time: this.proceParams?.plan_end_time === null ? null : format(this.proceParams?.plan_end_time, 'T'),
          actual_start_time: this.proceParams?.actual_start_time === null ? null : format(this.proceParams?.actual_start_time, 'T'),
          actual_end_time: this.proceParams?.actual_end_time === null ? null : format(this.proceParams?.actual_end_time, 'T'),
          repair_qty: this.proceParams.repair_qty,
        };
        break;

      default:
        break;
    }
    this._service.updateProduction(data).subscribe((res) => {
      if (res) {
        this.message.create('success', this._service.translateValue('success.modify'));
        this.visible = false;
        this._getDataList();
      }
    });
  }

  getOptions() {
    this._service.productionOptions().subscribe((res: any) => {
      if (res) {
        this.searchOptions.forEach((item: any) => {
          item.options = res.data[item.relate_key];
          if (item.key === 'style_class') {
            item.options.forEach((f: any) => {
              f.children.forEach((j: any) => {
                j.children.forEach((k: any) => {
                  k.isLeaf = true;
                });
              });
            });
          }
        });
      }
    });
  }

  setProcessColor(item: any) {
    if (this.material_fileds.includes(item.template_node_name)) {
      const statusColors = {
        1: '#B5B8BF',
        2: '#4D96FF',
        3: '#00AF7F',
      };
      return statusColors[item.bulk_node_status] || null;
    }
    let color = '';
    switch (item.status) {
      case 1:
        color = '#B5B8BF';
        break;
      case 2:
        color = '#4D96FF';
        break;
      case 4:
        color = '#00af7f';
        break;
      case 5:
        color = '#7FE3C7';
        break;
      case 3:
        color = '#FC8334';
        break;
      case 6:
        color = '#F74949';
        break;
    }
    return color;
  }

  onGoProcessTemplate() {
    this.router.navigate(['/basic-archive/progress-template/list']);
  }

  setProcessBgColor(item: any) {
    let bgColor = '';
    if (this.material_fileds.includes(item.template_node_name)) {
      const statusColors = {
        0: '#F7F8FA',
        1: '#F7F8FA',
        2: '#F2F9FF',
        3: '#F5FFFC',
      };
      return statusColors[item.bulk_node_status] || null;
    }
    switch (item.status) {
      case 1:
        bgColor = '#F7F8FA';
        break;
      case 2:
        bgColor = '#F2F9FF';
        break;
      case 4:
        bgColor = '#F5FFFC';
        break;
      case 5:
        bgColor = '#F5FFFC';
        break;
      case 3:
        bgColor = '#FEF6F1';
        break;
      case 6:
        bgColor = '#FEECEC';
        break;
    }
    return bgColor;
  }

  transLateTime(time: number) {
    // const date = new Date(time * 1000);
    const days = Math.floor(time / 86400);
    const hours = Math.round((time / 3600 / 24 - days) * 24);
    return `${days}天 ${hours}小时`;
  }

  transHoursTime(time: number) {
    const hours = Math.round(time / 3600);
    return ` ${hours}` + this._service.translateValue(this.translateName + '小时');
  }

  getOrderStatus(type: any) {
    let statusObj = {};
    switch (type) {
      case 1:
        statusObj = {
          current_status: '未接单',
          change_status: '',
        };
        break;
      case 2:
        statusObj = {
          current_status: '进行中',
          change_status: '确认完成',
          dialog_text: '已完成',
        };
        break;
      case 3:
        statusObj = {
          current_status: '已完成',
          change_status: '进行中',
          dialog_text: '进行中',
        };
        break;
    }
    return statusObj;
  }

  // 变更状态二次弹窗
  changeStatus(item?: any) {
    const text = this.getOrderStatus(item.out_sourcing_status)['dialog_text'];
    const translateText = text ? this._service.translateValue(this.translateName + text) : '';
    this._flcModalService
      .confirmCancel({
        content:
          this._service.translateValue(this.translateName + '是否将加工厂订单状态更改', {
            factory_name: item?.factory_name || '',
            bulk_order_code: item?.bulk_order_code || '',
          }) + `"${translateText}"`,
      })
      .afterClose.subscribe((res: boolean) => {
        if (res) {
          // 这里缺一个更改状态的接口
          const data = {
            id: item.id,
            status: item.out_sourcing_status === 2 ? 3 : 2,
          };
          this._service.updateStatus(data).subscribe((res: any) => {
            if (res) {
              this._getDataList();
            }
          });
        }
      });
  }

  onChangeDataList() {
    this.tableConfig = {
      ...this.tableConfig,
      pageIndex: 1,
    };
    this._getDataList();
  }

  onReset() {
    this.searchData = {};
    this.tableConfig = {
      ...this.tableConfig,
      pageSize: 20,
      pageIndex: 1,
    };
    this._getDataList();
  }

  onFold() {
    this.resizePage();
  }

  onIndexChange(e: number) {
    this.tableConfig = { ...this.tableConfig, pageIndex: e };
    this._getDataList();
  }

  onSizeChanges(e: number) {
    this.tableConfig = { ...this.tableConfig, pageSize: e };
    this._getDataList();
  }

  handleWhere() {
    let startTime = null;
    let endTime = null;
    if (this.searchData?.gen_time) {
      startTime = format(startOfDay(this.searchData?.gen_time[0] ?? null), 'T');
      endTime = format(endOfDay(this.searchData?.gen_time[1] ?? null), 'T');
    }
    const data = {
      ...this.searchData,
      gen_time_start: startTime,
      gen_time_end: endTime,
      first_style_class_id: this.searchData?.style_class == null ? null : this.searchData?.style_class[0], // 款式分类
      second_style_class_id: this.searchData?.style_class == null ? null : this.searchData?.style_class[1], // 款式分类
      third_style_class_id: this.searchData?.style_class == null ? null : this.searchData?.style_class[2], // 款式分类
      track_status: this.track_status, // 跟踪状态
    };
    return data;
  }

  getTrackStatusStatistics() {
    const where = this.handleWhere();
    this._service.getTrackStatusStatistics(where).subscribe((res: any) => {
      if (res?.code === 200) {
        this.trackStatusStatistics = res?.data;
      }
    });
  }

  _getDataList() {
    const where = this.handleWhere();
    const data = {
      ...where,
      page: this.tableConfig.pageIndex,
      size: this.tableConfig.pageSize,
    };
    this.tableConfig = { ...this.tableConfig, dataList: [], loading: true };
    this._service
      .getProductionTableList(data)
      .pipe(
        finalize(() => {
          this.tableConfig = { ...this.tableConfig, loading: false };
        })
      )
      .subscribe((res: any) => {
        if (res) {
          let maxNodeNumber = 0;
          this.tableConfig.dataList = res.data.prod_status_infos;
          this.tableConfig.dataList.forEach((item: any) => {
            item.order_pictures =
              item.order_pictures?.map((f: any) => {
                return { url: f };
              }) || [];
            if (item.nodes) {
              if (item.nodes.length > maxNodeNumber) maxNodeNumber = item.nodes.length;
              item.nodes.forEach((node: any) => {
                if (node.consumed_seconds) {
                  node.display_consumed_seconds = this.transHoursTime(node.consumed_seconds);
                }
                if (node.exceed_seconds) {
                  node.display_exceed_seconds = this.transHoursTime(node.exceed_seconds);
                }
              });
            }
          });
          this.tableHeader.find((item) => item.label === '生产进度').width = maxNodeNumber > 0 ? maxNodeNumber * 240 + 16 + 'px' : '120px';

          this.tableHeader = [...this.tableHeader];
          this.tableConfig.count = res.data.count;
          // this.tableConfig = { ...this.tableConfig };
        }
      });
  }
  getMaterialStatus(item: any) {
    const { template_node_name, bulk_node_status } = item;
    return STATUSMAP[template_node_name]?.[bulk_node_status] || '';
  }

  getQualifedQty(inspected_qty: number, defected_qty: number) {
    if (!inspected_qty) return 0;
    return inspected_qty - (defected_qty ?? 0);
  }
  getQualifiedPercent(inspected_qty: number, defected_qty: number) {
    if (!inspected_qty) return 0;
    return round(((inspected_qty - (defected_qty ?? 0)) / inspected_qty) * 100, 2);
  }
  getDefectedPercent(inspected_qty: number, defected_qty: number) {
    if (!inspected_qty) return 0;
    return round(((defected_qty ?? 0) / inspected_qty) * 100, 2);
  }
  formatEventOptions(dataList: any) {
    this.event_options = [];
    dataList.forEach((e: any) => {
      const children = e.child.map((child: any) => {
        return { ...child, label: child.adjust_name, value: child.adjust_id, isLeaf: true };
      });
      this.event_options.push({ label: e.value, value: e.key, children: children });
    });
  }
  event_options: any[] = [];
  default_event_options: any[] = [];
  event_visible = false;
  event_data: any = {};
  event_form: FormGroup = new FormGroup({ event_form_list: new FormArray([]) });

  notify_event_options: any[] = [];

  get event_form_array(): FormArray {
    return this.event_form?.get('event_form_list') as FormArray;
  }
  // 事件汇报详情
  onViewAmountDetail(data: any) {
    this.event_form_array.clear();
    this.event_data = data.item;
    this.event_data.del_list = [];

    this._service.getEventReportOptionsList().subscribe((res: any) => {
      if (res.code === 200) {
        this.formatEventOptions(res.data?.notify);
        this.notify_event_options = res.data?.un_notify ?? [];
      }
    });

    this._service.getEventReportDetail(data.item.id).subscribe((res: any) => {
      if (res.code === 200) {
        res.data?.event_list?.forEach((e: any) => {
          this.addEventForm(e);
        });
        this.event_visible = true;
      }
    });
  }

  addEventForm(event_line: any) {
    let adjust_id_local: any[] = [];
    // let adjust_default_options: any[] = [];
    if (event_line?.adjust_id && event_line?.adjust_type && event_line?.settlement_type) {
      const key = `${event_line?.adjust_type}-${event_line?.settlement_type}`;
      adjust_id_local = [key, event_line?.adjust_id];
      // adjust_default_options = [
      //   {
      //     value: key,
      //     label: `${event_line.adjust_type_value}(${event_line.settlement_type_value})`,
      //     children: [
      //       {
      //         ...event_line,
      //         label: event_line.adjust_name,
      //         value: event_line.adjust_id,
      //       },
      //     ],
      //   },
      // ];
    }

    const group = this._fb.group({
      event_id: [event_line?.event_id ?? 0],
      is_notify: [event_line?.is_notify ?? false],
      adjust_id: [event_line?.adjust_id, [Validators.required]],
      adjust_code: [event_line?.adjust_code],
      adjust_type: [event_line?.adjust_type],
      adjust_type_value: [event_line?.adjust_type_value],
      adjust_name: [event_line?.adjust_name],
      settlement_type: [event_line?.settlement_type],
      settlement_type_value: [event_line?.settlement_type_value],
      amount: [event_line?.amount],
      desc_text: [event_line?.desc_text],
      created_at: [event_line?.created_at],
      can_delete: [event_line?.can_delete ?? true],
      adjust_id_local: [adjust_id_local],
      // adjust_default_options: [adjust_default_options],
    });
    if (event_line) {
      this.event_form_array.push(group);
    } else {
      this.event_form_array.insert(0, group);
    }
  }

  // 是否通知结算更改
  notifyChanged(value: any, index: number) {
    const data = this.event_form_array.controls[index];
    this.setOptionsData(data, null);
  }

  deleteEventForm(index: number) {
    const data = this.event_form_array.controls[index];
    if (data.value.event_id) {
      this.event_data.del_list.push(data.value.event_id);
    }
    this.event_form_array.controls.splice(index, 1);
  }

  adjustIdChangegd(value: any, index: number) {
    const data = this.event_form_array.controls[index];
    this.setOptionsData(data, null);
    if (value.length) {
      const optionLevel1 = this.event_options.find((e) => e.value === value[0]);
      if (optionLevel1) {
        const optionLevel2 = optionLevel1.children.find((e: any) => e.value === value[1]);
        if (optionLevel2) {
          this.setOptionsData(data, optionLevel2);
        }
      }
    }
  }

  setOptionsData(data: any, option: any) {
    data.get('adjust_id')?.setValue(option?.value);
    data.get('adjust_type')?.setValue(option?.adjust_type);
    data.get('adjust_code')?.setValue(option?.adjust_code);
    data.get('adjust_type_value')?.setValue(option?.adjust_type_value);
    data.get('adjust_name')?.setValue(option?.label);
    data.get('settlement_type')?.setValue(option?.settlement_type);
    data.get('settlement_type_value')?.setValue(option?.settlement_type_value);
  }

  adjustIdChangegdUnNotify(value: any, index: number) {
    const data = this.event_form_array.controls[index];
    data.get('adjust_name')?.setValue(null);
    data.get('adjust_code')?.setValue(null);
    if (value) {
      const option = this.notify_event_options.find((e: any) => e.adjust_id === value);
      data.get('adjust_name')?.setValue(option.adjust_name);
      data.get('adjust_code')?.setValue(option.adjust_code);
    }
  }
  // 事件汇报保存
  event_handleOkModal() {
    let invalid = false;
    const event_list: any[] = [];
    this.event_form_array.controls.forEach((e) => {
      const data = e.value;
      const id = data.adjust_id;
      if (id) {
        event_list.push({ ...data, amount: data.amount?.toString() });
      } else {
        if (data.amount || data.desc_text) {
          invalid = true;
          return;
        }
      }
    });
    if (invalid) {
      this.message.create('', this._service.translateValue(this.translateName + '请完善数据'));
      return;
    }
    const payload = {
      id: this.event_data.id,
      del_list: this.event_data.del_list,
      event_list: event_list,
    };
    this._service
      .eventReportUpdate(payload)
      .pipe(debounceTime(500))
      .subscribe((res: any) => {
        if (res.code === 200) {
          this.event_data.event_amount = res.data?.amount ?? 0;
          this.event_visible = false;
          this.message.success(this._service.translateValue('flss.success.save'));
        }
      });
  }
  // 点击大货单号，直接跳转到订单需求详情页，进行编辑修改；
  toOrderPage(item: any) {
    this.router.navigate([`/order/bulk/list/${item?.bulk_order_id}`]);
  }

  // 筛选状态发生变化
  selectedStatusChange() {
    this.tableConfig.pageIndex = 1;
    this._getDataList();
  }

  getSelectedCount(e: any) {
    this.selectedIds = e.list.map((item: any) => item.id);
    this.selectedCount = e.count;
  }

  // 批量导出
  isExporting = false;
  onBatchExport() {
    this.isExporting = true;
    const header = this.ProductionTableTemplateFiled?.headers.slice();
    const where = this.handleWhere();
    const params = {
      ...where,
      ids: this.selectedIds,
      columns: header.filter((item: any) => item.visible && !['order_pictures'].includes(item.key)).map((item: any) => item.key),
    };

    this._service
      .export(params)
      .pipe(
        finalize(() => {
          this.isExporting = false;
        })
      )
      .subscribe((res: any) => {
        if (res.code === 200) {
          const url: any = res?.data?.url;
          const name = this._service.translateValue(this.translateName + '订单进度跟踪');
          const link = document.createElement('a');
          link.style.display = 'none';
          link.href = url;
          link.setAttribute('download', name + '.xlsx');
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          this.message.success(this._service.translateValue('success.export'));
          this.ProductionTableTemplateFiled?.clearAllSelected();
        } else {
          this.message.error(this._service.translateValue('fail.export'));
        }
      });
  }

  // 打开筛选项的配置抽屉
  customSearchListConfig = {
    localKey: 'ProductionProgressReportSearchOptions',
    version: '1.2.0',
    translateName: this.translateName,
  };
  onOpenDrawerCustom() {
    this._drawerHelp.openDrawer({
      title: this._service.translateValue('flss.common.自定义/业务配置'),
      content: SearchOptionsCustomContainerComponent,
      placement: 'right',
      width: '480px',
      contentParams: {
        // noticeText: this._service.translateValue(this.translateName + '筛选项字段'),
        configList: this.searchOptions,
        config: this.customSearchListConfig,
        saveConfig: (data: any) => {
          this.searchOptions = data;
          this._drawerHelp.closeDrawer();
        },
        cancelConfig: () => {
          this._drawerHelp.closeDrawer();
        },
      },
    });
  }

  // 编辑行, 增加操作列，有编辑权限的人，可对以下字段进行编辑
  isEditing = false; // 当前是否正在编辑
  editLine(item: any) {
    item['isEdit'] = true;
  }

  // 取消编辑
  cancelEditLine(item: any) {
    item['isEdit'] = false;
    this.isEditing = false;
    this._getDataList();
  }

  // 保存编辑行
  saveEditLine(item: any) {
    this._service
      .updateItem({
        id: item?.id || null,
        bulk_order_id: item?.bulk_order_id || null,
        shipment_status: item?.shipment_status || null,
        delay_range: item?.delay_range || null,
        ship_performance: item?.ship_performance || null,
        delay_responsibility: item?.delay_responsibility || null,
        delay_reason: item?.delay_reason || null,
        transit_time: item?.transit_time || null,
      })
      .subscribe((res: any) => {
        if (res?.code === 200) {
          this.message.success(this._service.translateValue('success.save'));
          item['isEdit'] = false;
          this.isEditing = false;
          this._getDataList();
        }
      });
  }

  // 恢复跟踪
  recoverTracking(item: any) {
    const _modal = this._flcModalService.confirmCancel({
      content: this._service.translateValue(this.translateName + '确认恢复跟踪？'),
      okText: this._service.translateValue('btn.ok'),
      cancelText: this._service.translateValue('btn.cancel'),
    });
    _modal.afterClose.subscribe((confirm: boolean) => {
      if (confirm) {
        this._service.restoreTrack({ id: item.id }).subscribe((res: any) => {
          if (res?.code === 200) {
            this.message.success(this._service.translateValue('success.operate'));
            this._getDataList();
          }
        });
      }
    });
  }

  // 结束跟踪
  endTracking(item: any) {
    const _modal = this._flcModalService.confirmCancel({
      content: this._service.translateValue(this.translateName + '标记为跟踪结束的订单，使用的跟踪模版保存为快照，且所有节点不可再编辑。'),
      okText: this._service.translateValue('btn.ok'),
      cancelText: this._service.translateValue('btn.cancel'),
    });
    _modal.afterClose.subscribe((confirm: boolean) => {
      if (confirm) {
        this._service.stopTrack({ id: item.id }).subscribe((res: any) => {
          if (res?.code === 200) {
            this.message.success(this._service.translateValue('success.operate'));
            this._getDataList();
          }
        });
      }
    });
  }

  // 自动计算的字段
  automaticCalculate(key: string, item: any) {
    // 延迟天数 = 客户交期 - 交付日期
    if (key === 'delay_no_of_days') {
      if (!item?.customer_due_time || !item?.due_time) return null;
      const revised_due_date = new Date(item?.customer_due_time);
      const orig_due_date = new Date(item?.due_time);
      const a = new Date(revised_due_date?.getFullYear(), revised_due_date?.getMonth() + 1, revised_due_date?.getDate());
      const b = new Date(orig_due_date?.getFullYear(), orig_due_date?.getMonth() + 1, orig_due_date?.getDate());
      return differenceInDays(a, b);
    }
    // 交付日期 + 运输时间
    if (key === 'original_isd') {
      if (!item?.due_time) return null;
      const orig_due_date = new Date(item?.due_time);
      return format(addDays(orig_due_date, item?.transit_time || 0), 'yyyy-MM-dd');
    }
    // 客户交期 + 运输时间
    if (key === 'revised_isd') {
      if (!item?.customer_due_time) return null;
      const revised_due_date = new Date(item?.customer_due_time);
      return format(addDays(revised_due_date, item?.transit_time || 0), 'yyyy-MM-dd');
    }
    return '';
  }

  // 获取批量编辑字段下拉列表
  updateBatchFieldList: Array<IBatchEditFieldOption> = [];
  getUpdateBatchFieldList() {
    this._service.getUpdateBatchFieldList({}).subscribe((res: any) => {
      if (res?.code === 200) {
        // 过滤出节点类型的 和 有查看权限的字段类型的字段
        this.updateBatchFieldList = res?.data?.prod_status_fields?.filter(
          (item: any) =>
            item.type === BatchEditTypeEnum.node || (item.type === BatchEditTypeEnum.field && this.show_field(item?.prod_status_field_name))
        );
      }
    });
  }

  // 打开批量编辑字段的下拉
  openBatchEditFieldSelect(e: boolean) {
    if (e) {
      if (!this.selectedCount) {
        this.message.warning(this._service.translateValue(this.translateName + '请选择需要编辑的行'));
        this.batchSelectRef.setOpenState(false);
        this.batchSelectRef.blur();
        return;
      }
    }
  }

  // 选择批量编辑的字段
  batchEditField = null;
  batchEditTypeEnum = BatchEditTypeEnum;
  templateFields: ITemplateFields[] = []; // 节点模版字段
  selectBatchEditField(e: IBatchEditFieldOption | null, isBatch = false) {
    if (e?.type === BatchEditTypeEnum.field) {
      this.openBatchFieldModal(e);
    }

    if (e?.type === BatchEditTypeEnum.node) {
      if (e?.template_node_id) {
        this._service
          .getNodeTemplateFieldList({
            id: e?.template_node_id,
          })
          .subscribe((res: any) => {
            if (res?.code === 200) {
              this.templateFields = res?.data?.template_fields;
              this.openBatchNodeModal(e, isBatch);
            }
          });
      }
    }
  }

  openBatchFieldModal(e: any) {
    this._modal
      .create({
        nzWidth: 450,
        nzTitle: e?.prod_status_field_name ? this._service.translateValue(this.translateName + e?.prod_status_field_name) : '',
        nzMaskClosable: false,
        nzContent: BatchEditFieldComponent,
        nzBodyStyle: {
          padding: '0',
        },
        nzComponentParams: {
          selectEditItem: e,
          param: {
            ids: this.selectedIds,
          },
        },
        nzFooter: null,
      })
      .afterClose.subscribe(() => {
        this.batchEditField = null;
      });
  }

  openBatchNodeModal(e: any, isBatch = false) {
    this.templateFields?.forEach((item: any) => {
      item['field_data'] = item.data;
      item['data'] = null;
      item['is_edit'] = FieldEditAuthEnum.edit;
    });
    this._modal
      .create({
        nzWidth: 320,
        nzTitle: e?.template_node_name,
        nzMaskClosable: false,
        nzContent: EditNodeFormComponent,
        nzBodyStyle: {
          padding: '0',
        },
        nzComponentParams: {
          node: { node_temp_data_list: this.templateFields, ...e },
          isBatch,
          param: {
            ids: this.selectedIds,
            // warning_message_id: this.warning_message_id ?? null,
          },
        },
        nzFooter: null,
      })
      .afterClose.subscribe(() => {
        this.batchEditField = null;
        // if (this.warning_message_id > 0) {
        //   this.warning_message_id = -1;
        //   this.selectedIds = [];
        //   this?.messageNotificationRef?.getMessageCount();
        //   this?.messageNotificationRef?.getMessageList();
        // }
      });
  }

  formatterFieldValue(item: any, type: FieldTypeEnum) {
    const res = item?.length ? JSON.parse(item) : null;
    if (type === FieldTypeEnum.MultipleChoice) {
      return res?.value?.join('、');
    }
    if (type === FieldTypeEnum.Date) {
      return res?.value ? this.datePipe.transform(res?.value, 'yyyy-MM-dd') : '';
    }
    // 防止数据缺少url导致渲染问题
    if (type === FieldTypeEnum.Attachment) {
      return res?.value?.length ? res?.value?.filter((item: any) => item?.url) : [];
    }

    return res?.value;
  }

  // 处理消息--需求调整了，不需要处理这块了， 后期如果需要， 再放开跟预警消息有关的代码
  // warning_message_id = -1;
  // handleDealMessage(item: IMessageListItem) {
  //   this.warning_message_id = item.warning_message_id;
  //   this.selectedIds = item.prod_status_ids;
  //   this.selectBatchEditField(
  //     {
  //       field_type: 0,
  //       node_template_id: 0,
  //       prod_status_field: '',
  //       prod_status_field_name: '',
  //       template_node_id: item.template_node_id,
  //       template_node_name: item.template_node_name,
  //       type: BatchEditTypeEnum.node,
  //     },
  //     true
  //   );
  // }

  renderHeader() {
    // render
    const header = this._FlcTableHelperService.getFlcTableHeaderConfig(
      this.tableHeader,
      this.tableConfig?.version,
      this.tableConfig?.tableName
    );
    if (header?.length) {
      this._service.fieldArr?.forEach((field: any) => {
        const op = header?.find((item: any) => item?.label === field.fieldName);
        const opIndex = header?.findIndex((item: any) => item?.label === field.fieldName);
        if (!op) {
          if (field?.viewPermission === 2) {
            const originOp = this.defaultTableHeader?.find((d: any) => d?.label === field?.fieldName);
            const originOpIndex = this.defaultTableHeader?.findIndex((d: any) => d?.label === field?.fieldName);
            if (originOp) header.splice(originOpIndex, 0, originOp);
          }
        } else {
          if (field?.viewPermission === 1) {
            header?.splice(opIndex, 1);
          }
        }
      });
      this.tableHeader = header;
      this._FlcTableHelperService?.saveFlcTableHeaderConfig(this.tableHeader, this.tableConfig?.version, this.tableConfig?.tableName);
    } else {
      this.tableHeader = this.defaultTableHeader?.filter((item) => this.show_field(item?.label));
    }
  }

  translateItem(value: any) {
    if (!value) return null;
    return this._service.translateValue(this.translateName + value);
  }
}
