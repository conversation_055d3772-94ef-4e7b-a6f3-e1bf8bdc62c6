export enum RecommendKeyEnum {
  /** 款式分类 */
  style_class = 'style_class',
  /** 产品类型 */
  product_type = 'product_type',
  /** 品质 */
  quality_level = 'quality_level',
  /** 加工方式 */
  process_type = 'process_type',
  /** 起订量 */
  start_order_quantity = 'start_order_quantity',
  /** 快反 */
  fast_reply = 'fast_reply',
  /** 是否接受第三方质检 */
  accept_third_quality_check = 'accept_third_quality_check',
  /** 客户品牌 */
  customer_brand = 'customer_brand',
  /** 付款条件 */
  payment_condition = 'payment_condition',
  /** 报关资质 */
  has_customs_clearance_qualification = 'has_customs_clearance_qualification',
  /** 效率 */
  efficiency = 'efficiency',
  /** 合格率 */
  pass_rate = 'pass_rate',
  /** 准交率 */
  accurate_delivery_rate = 'accurate_delivery_rate',
}
