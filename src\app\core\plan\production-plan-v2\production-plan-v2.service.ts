import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  AllocationDetail,
  AllocationDetailLine,
  AllocationGetPlanEndTimePayload,
  AllocationGetPlanEndTimeResult,
  AllocationInitPlanPaylod,
  AllocationListItem,
  AllocationListPayload,
  AllocationSettingPayload,
  CommonOptionItem,
  ResultData,
} from './model/production-plan.interface';
import { ProductionPlanShareService } from './production-plan-share.service';
import { ForwardConnectRequestPaylod, LockFactoryItem, PlanListPayload, PlanMovedParam, ProductionLineResponse } from './interface';
import { FlcSpKeyConstant, FlcSpUtilService } from 'fl-common-lib';

@Injectable()
export class ProductionPlanV2Service {
  private _baseUrl = '/service/procurement-inventory/plan/v1/';

  constructor(private _http: HttpClient, private _shareService: ProductionPlanShareService, private _spUtil: FlcSpUtilService) {}

  getAllocateOrderListOptions() {
    return this._http.post<ResultData<{ [key: string]: CommonOptionItem[] }>>(`${this._baseUrl}order-allocation-list-option`, {});
  }

  getAllocateOrderList(playLoad: AllocationListPayload) {
    return this._http.post<ResultData<{ list: AllocationListItem[]; total: number }>>(`${this._baseUrl}order-allocation-list`, playLoad);
  }

  /**
   * 设置sam Or 日生产件数值
   * @param payload
   * @returns
   */
  setOrderAllocation(payload: AllocationSettingPayload) {
    return this._http.post<ResultData<null>>(`${this._baseUrl}order-allocation-setting`, payload);
  }

  /**
   * 获取订单分配详情
   * @param order_uuid
   * @returns
   */
  getOrderAllocationDetail(order_uuid: string) {
    return this._http.post<ResultData<AllocationDetail>>(`${this._baseUrl}order-allocation-info`, { order_uuid });
  }

  /**
   * 自动算计划
   * @param payload
   * @returns
   */
  getAllocationInitPlan(payload: AllocationInitPlanPaylod) {
    return this._http.post<ResultData<{ list: AllocationDetailLine[] }>>(`${this._baseUrl}order-allocation-auto-calculate`, payload);
  }

  gePlanEndTime(payLoad: AllocationGetPlanEndTimePayload) {
    return this._http.post<ResultData<AllocationGetPlanEndTimeResult>>(`${this._baseUrl}order-allocation-calculate`, payLoad);
  }

  // 分配订单
  alloacationOrder(payLoad: any) {
    return this._http.post<ResultData<any>>(`${this._baseUrl}order-allocation`, payLoad);
  }

  // 甘特图部分

  // 获取产线视图数据
  getProductionLineList(payload: PlanListPayload) {
    // return of(listData);
    return this._http.post<ResultData<ProductionLineResponse>>(`${this._baseUrl}plan-list`, payload);
  }

  // 获取产线下拉
  getProductionOptions() {
    return this._http.post(`${this._baseUrl}plan-list-option`, {});
  }

  // 获取撤回列表
  getTempSessionList() {
    return this._http.post<ResultData<{ temp_session_list: string[] }>>(`${this._baseUrl}temp-session-list`, {});
  }

  // 撤回
  revoke(temp_session: string) {
    return this._http.post<ResultData<{ temp_session: string }>>(`${this._baseUrl}undo`, { temp_session });
  }

  // 进入编辑状态
  startEdit(list: { factory_code: string; production_line_no: string | null }[]) {
    return this._http.post<ResultData<null>>(`${this._baseUrl}plan-start-edit`, { list });
  }

  // 退出编辑状态
  exitEdit() {
    return this._http.post<ResultData<null>>(`${this._baseUrl}plan-stop-edit`, {});
  }

  // 获取编辑锁详情
  getEditLockDetail() {
    return this._http.post<ResultData<{ locked_list: LockFactoryItem[] }>>(`${this._baseUrl}edit-lock-detail`, {});
  }

  // 快速拆分
  splitOrder(payload: { group_id: number; temp_session: string; split_date: number }) {
    return this._http.post<any>(`${this._baseUrl}split`, payload);
  }

  /**
   * 向前合并
   * @param payload
   * @returns
   */
  forwardMerge(payload: { temp_session: string; group_ids: number[] }) {
    return this._http.post<ResultData<{ temp_session: string }>>(`${this._baseUrl}merge`, payload);
  }

  // 向前衔接and整体向前衔接
  connectOrder(payLoad: ForwardConnectRequestPaylod) {
    return this._http.post<ResultData<{ temp_session: string }>>(`${this._baseUrl}forward-connect`, payLoad);
  }

  // 取消分配
  cancelAllocated(payload: { temp_session: string; group_id: number }) {
    return this._http.post<any>(`${this._baseUrl}cancel`, payload);
  }

  // 移动车缝进度条
  movePlan(payload: PlanMovedParam) {
    return this._http.post<any>(`${this._baseUrl}plan-move`, payload);
  }

  // 发布
  publishProductionPlan(temp_session: string) {
    return this._http.post<ResultData<{ temp_session: string }>>(`${this._baseUrl}publish`, { temp_session });
  }

  // 生产计划、替换订单list
  getReplaceOrderList(group_id: number) {
    return this._http.post<any>(`${this._baseUrl}get-replace-order-info`, { group_id });
  }

  replaceOrder(payload: any) {
    return this._http.post<any>(`${this._baseUrl}replace-order`, payload);
  }

  private userActionMap?: Map<string, Array<string>>;
  getUserActionsMap() {
    if (!this.userActionMap && this._spUtil.containsObjectKey(FlcSpKeyConstant.UserActions)) {
      this.userActionMap = this._spUtil.getObject(FlcSpKeyConstant.UserActions) as Map<string, Array<string>>;
    }

    const userActions = (this.userActionMap?.get('intellect-plan/production-plan') as string[]) || [];
    return {
      hasAllocation: userActions.includes('bulk:production-plan-order-allocation'),
      hasEditPlan: userActions.includes('bulk:production-plan-edit-plan'),
      hasEditSam: userActions.includes('bulk:production-plan-edit-sam'),
    };
  }

  /// 获取消息数
  getNotificationCount() {
    return this._http.post<any>(`${this._baseUrl}plan-change-message-count`, {});
  }
  /// 获取消息列表
  getNotificationList(payload: any) {
    return this._http.post<any>(`${this._baseUrl}plan-change-message-list`, payload);
  }
  /// 获取消息列表options
  getNotificationListOptions() {
    return this._http.post<any>(`${this._baseUrl}plan-change-message-list-option`, {});
  }
  /// 自动调整生产计划
  planScheduleMessageAutoAdjust(payload: any) {
    return this._http.post<any>(`${this._baseUrl}plan-change-message-auto-adjust`, payload);
  }
  /// 使用/不使用 自动调整方案
  applyPlanScheduleMessageAutoAdjust(payload: any) {
    return this._http.post<any>(`${this._baseUrl}plan-change-message-list-adjustment`, payload);
  }
  /// 删除某个消息
  deleteNotification(payload: any) {
    return this._http.post<any>(`${this._baseUrl}plan-change-message-delete`, payload);
  }
}
