<nz-spin [nzSpinning]="loading" style="height: calc(100% - 44px)">
  <div style="height: 100%">
    <app-graph-table
      [date]="dates"
      [flexBasis]="200"
      (optionsChange)="optionsChange($event)"
      (forwardDate)="forwardDate()"
      (backwardsDate)="backwardsDate()">
      <div class="header-left-txt" ngProjectAs="header-left">
        <div class="left-cell" nz-row nzAlign="middle">
          <div nz-col nzSpan="12">{{ 'plan.capacityPlan.加工厂' | translate }}</div>
          <div nz-col nzSpan="12" style="text-align: left; padding: 0px 5px; line-height: 20px">
            <div>{{ 'plan.capacityPlan.产能(分钟/天)' | translate }}</div>
            <div>{{ 'plan.capacityPlan.擅长品类' | translate }}</div>
          </div>
        </div>
      </div>

      <div ngProjectAs="app-left-wrap">
        <ng-container *ngFor="let factory of factoryList">
          <div class="left-cell" nz-row nzAlign="middle">
            <div nz-col nzSpan="12" class="factory-tag">
              <span class="factory-span"><flc-text-truncated [data]="factory?.factory_name"></flc-text-truncated></span>
            </div>
            <div nz-col nzSpan="12" style="padding: 0 5px; text-align: left; line-height: 18px">
              <flc-text-truncated data="{{ factory?.capacity | number: '1.0-5' }}"></flc-text-truncated>

              <div style="font-size: 12px"><flc-text-truncated [data]="factory?.main_category"></flc-text-truncated></div>
            </div>
          </div>
        </ng-container>
      </div>

      <div ngProjectAs="app-graph-wrap">
        <ng-container *ngFor="let factory of factoryList">
          <div class="graph-item-wrap">
            <div class="graph-item-bgc">
              <div
                class="graph-item"
                *ngFor="let item of factory.factory_schedules"
                [ngStyle]="{
                  left: item.left * cellWidth + 'px',
                  width: item.width * cellWidth + 'px'
                }"></div>
            </div>
          </div>
        </ng-container>
      </div>

      <div ngProjectAs="app-graph-wrap-bg">
        <!-- -1 为线自身宽度 -->
        <div
          [ngStyle]="{
            left: currentLineLeft * cellWidth - 1 + 'px'
          }"
          class="current-day-line"></div>
      </div>
    </app-graph-table>
  </div>
</nz-spin>
