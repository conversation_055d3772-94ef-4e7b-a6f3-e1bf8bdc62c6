import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { FactoryManageDetailService } from '../factory-manage-detail.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-factory-mode-readonly',
  templateUrl: './factory-mode-readonly.component.html',
  styleUrls: ['./factory-mode-readonly.component.scss'],
})
export class FactoryModeReadonlyComponent implements OnInit, OnChanges {
  @Input() changeStep = false;
  step = '2.';

  constructor(public _service: FactoryManageDetailService, public _translateService: TranslateService) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes && changes.changeStep && changes.changeStep.currentValue) {
      this.step = this._translateService.instant('第几步', { value: '2' }) + '：';
    } else {
      this.step = '2.';
    }
  }
}
