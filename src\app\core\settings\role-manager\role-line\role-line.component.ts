import { Component, EventEmitter, Input, OnInit, Output, QueryList, ViewChildren } from '@angular/core';
import { RoleNameChildModel } from '../role-manager.model';
import { RoleLineLastMenuLineComponent } from './role-line-last-menu-line/role-line-last-menu-line.component';

@Component({
  selector: 'app-role-line',
  templateUrl: './role-line.component.html',
  styleUrls: ['./role-line.component.scss'],
})
export class RoleLineComponent implements OnInit {
  @Input() roleItem!: RoleNameChildModel;
  @Input() isSaveError!: boolean;
  @Input() isEdit!: boolean;
  @Input() isRangeMode!: boolean;
  @Input() showALLDetail!: boolean;
  @Input() allDepartmentList: { id: number; name: string }[] = [];
  @Output() statusChange = new EventEmitter<boolean>();
  @Output() checkedChange = new EventEmitter<void>();
  @ViewChildren('menuLineRefs') menuLineRefs!: QueryList<RoleLineLastMenuLineComponent>;

  constructor() {}

  ngOnInit(): void {}
}
