<div class="sample-notice">
  <div style="display: flex; justify-content: space-between">
    <flc-order-status-checkbox
      shape="round"
      [checkData]="statusOptions"
      [isMultiCheck]="false"
      (onCheckChange)="onCheckChange($event)"></flc-order-status-checkbox>

    <div>
      <label nz-checkbox [ngModel]="isMySelf" (ngModelChange)="onRadioChange()">仅看自己</label>
      <label nz-checkbox [ngModel]="!isMySelf" (ngModelChange)="onRadioChange()">全部</label>
    </div>
  </div>

  <nz-table
    style="margin-top: 12px; flex: 1; overflow: hidden"
    nzShowSizeChanger
    [nzFrontPagination]="false"
    [nzData]="tableConfig.dataList"
    [nzLoading]="tableConfig.loading"
    [nzTotal]="tableConfig.count"
    [(nzPageIndex)]="tableConfig.pageIndex"
    [(nzPageSize)]="tableConfig.pageSize"
    (nzPageIndexChange)="onIndexChanges($event)"
    (nzPageSizeChange)="onSizeChanges($event)"
    [nzSize]="'small'"
    [nzScroll]="{ x: '0', y: '600px' }"
    [nzShowTotal]="totalTemplate">
    <thead>
      <tr>
        <th nzWidth="100px">#</th>
        <th nzWidth="120px">打样任务单/节点</th>
        <th>创建人</th>
        <th>品牌</th>
        <th>数量</th>
        <th>样板类型</th>
        <th nzWidth="110px">期望交付日期</th>
        <th nzWidth="110px">计划完成时间</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let item of tableConfig.dataList; index as i">
        <td>
          <div style="position: relative; font-weight: 400; font-size: 12px; color: #54607c">
            <div>{{ i + 1 }}</div>

            <i *ngIf="item.status == 3" nz-icon style="position: absolute; left: 0; top: 0">
              <svg t="1709092848952" class="icon" viewBox="0 0 1024 1024" version="1.1" width="14" height="14">
                <path
                  d="M0 0m292.571429 0l438.857142 0q292.571429 0 292.571429 292.571429l0 438.857142q0 292.571429-292.571429 292.571429l-438.857142 0q-292.571429 0-292.571429-292.571429l0-438.857142q0-292.571429 292.571429-292.571429Z"
                  fill="#FC8334"
                  p-id="5844"></path>
                <path
                  d="M512 219.428571a292.571429 292.571429 0 1 1 0 585.142858 292.571429 292.571429 0 0 1 0-585.142858z m-36.571429 146.285715a36.571429 36.571429 0 0 0-36.571428 36.571428v146.285715a36.571429 36.571429 0 0 0 37.302857 36.571428H621.714286a36.571429 36.571429 0 0 0 0-73.142857L512 511.926857V402.285714a36.571429 36.571429 0 0 0-36.571429-36.571428z"
                  fill="#FFFFFF"
                  p-id="5845"></path>
              </svg>
            </i>

            <i *ngIf="item.status == 2" nz-icon style="position: absolute; left: 0; top: 0">
              <svg width="16" height="16">
                <path
                  d="M32 32m360 0l240 0q360 0 360 360l0 240q0 360-360 360l-240 0q-360 0-360-360l0-240q0-360 360-360Z"
                  fill="#FFFFFF"
                  p-id="8186"></path>
                <path
                  d="M92 92m300 0l240 0q300 0 300 300l0 240q0 300-300 300l-240 0q-300 0-300-300l0-240q0-300 300-300Z"
                  fill="#FF4141"
                  p-id="8187"></path>
                <path
                  d="M563.6 262.28l-266.16 276.6c-7.44 7.98-9.3 15.6-5.58 22.74 5.58 10.74 17.58 12.72 24.66 12.72h125.58l-68.94 174.72c-2.76 9.24-1.14 16.32 4.92 21.24 9 7.32 25.02 13.86 38.16 2.46 8.76-7.56 114.84-106.02 318.18-295.38 6.48-10.26 7.32-18.72 2.4-25.26-4.8-6.6-14.4-9.48-28.56-8.76L576.92 442.4l36-156c0.84-15.6-3.9-25.62-14.4-30.12-10.44-4.44-22.08-2.4-34.86 6z"
                  fill="#FFFFFF"
                  p-id="8188"></path>
              </svg>
            </i>
          </div>
        </td>

        <!-- 打样任务单/节点 -->
        <td>
          <div style="font-weight: 500; font-size: 14px; color: #138aff; cursor: pointer" (click)="goToSample(item.sample_order_id)">
            <div>
              {{ item.sample_order_no }}
            </div>
            <div style="background: #e7f3fe; border-radius: 10px">
              {{ item.node_name }}
            </div>
          </div>
        </td>

        <td>
          <flc-table-body-render [data]="item.gen_user_name" type="text"></flc-table-body-render>
        </td>
        <td>
          <flc-table-body-render [data]="item.brand_name" type="text"></flc-table-body-render>
        </td>
        <td>
          <flc-table-body-render [data]="item.sample_num" type="quantity"></flc-table-body-render>
        </td>
        <td>
          <flc-table-body-render [data]="item.sample_type_name" type="text"></flc-table-body-render>
        </td>
        <td>
          <flc-table-body-render
            [data]="(item.expected_delivery_date ?? 0) > 0 ? item.expected_delivery_date : 0"
            type="date"></flc-table-body-render>
        </td>
        <td>
          <flc-table-body-render [data]="item.plan_finish_time" type="date"></flc-table-body-render>
        </td>
      </tr>
    </tbody>
    <ng-template #totalTemplate>
      <div style="margin-right: 16px; color: #000000; font-size: 14px">
        共 {{ tableConfig.count }} 条， 第 <span style="color: #0f86f8">{{ tableConfig.pageIndex }}</span> /
        {{ tableConfig.count / tableConfig.pageSize | flcMathCeil }} 页
      </div>
    </ng-template>
  </nz-table>
</div>
