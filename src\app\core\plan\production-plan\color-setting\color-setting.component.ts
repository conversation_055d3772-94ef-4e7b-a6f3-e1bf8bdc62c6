import { TranslateService } from '@ngx-translate/core';
import { NzDrawerService, NzDrawerRef } from 'ng-zorro-antd/drawer';
import { Component, OnInit, ChangeDetectorRef, EventEmitter, Output, TemplateRef, ViewChild, ChangeDetectionStrategy } from '@angular/core';
import { ColorSettingService } from './color-setting.service';
import { FormGroup, FormArray, FormBuilder, Validators, AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { FlcValidatorService, resizable } from 'fl-common-lib';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { finalize } from 'rxjs';
import { Color, ColorPickerControl } from '@iplab/ngx-color-picker';

@Component({
  selector: 'app-color-setting',
  templateUrl: './color-setting.component.html',
  styleUrls: ['./color-setting.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [NzDrawerService, ColorSettingService],
})
@resizable()
export class ColorSettingComponent implements OnInit {
  @ViewChild('title_tpl') titleTpl!: TemplateRef<any>;
  @ViewChild('content_tpl') contentTpl!: TemplateRef<any>;
  @Output() closeClick = new EventEmitter();
  // 列表参数
  editable = false; // 是否可编辑
  colorDataList: any[] = []; // 颜色设置列表

  // 查询条件相关参数
  queryConditions = {
    first_style_id: null,
    second_style_id: null,
    set_status: null,
  };
  first_material_options: any[] = []; // 一级分类下拉配置
  second_material_options: any[] = []; // 二级分类下拉配置
  // 是否设置下拉项
  isSetOptions = [
    { label: '未设置', value: 0 },
    { label: '已设置', value: 1 },
  ];
  // 颜色选择器相关参数
  checkColor = '#ffffff'; // 自定义默认颜色
  compactControl = new ColorPickerControl(); // 颜色选择器对象
  customColor: any[] = []; // 自定义颜色列表
  // 默认调色板颜色
  defaultColor = [
    {
      simple_color: { color: '#EAE7FF', active: false },
      general_color: { color: '#DCD6FE', active: false },
      difficult_color: { color: '#C1B6FD', active: false },
      extremely_difficult_color: { color: '#A697FB', active: false },
      grounp_active: false,
    },
    {
      simple_color: { color: '#E1EAFF', active: false },
      general_color: { color: '#CDDBFF', active: false },
      difficult_color: { color: '#A7C0FF', active: false },
      extremely_difficult_color: { color: '#81A5FF', active: false },
      grounp_active: false,
    },
    {
      simple_color: { color: '#E1F6FF', active: false },
      general_color: { color: '#CCF0FF', active: false },
      difficult_color: { color: '#A5E5FF', active: false },
      extremely_difficult_color: { color: '#7ED9FF', active: false },
      grounp_active: false,
    },
    {
      simple_color: { color: '#D8F5F1', active: false },
      general_color: { color: '#BDEEE7', active: false },
      difficult_color: { color: '#8CE1D5', active: false },
      extremely_difficult_color: { color: '#5AD4C2', active: false },
      grounp_active: false,
    },
    {
      simple_color: { color: '#DFF6D4', active: false },
      general_color: { color: '#CDF0BC', active: false },
      difficult_color: { color: '#B6E99F', active: false },
      extremely_difficult_color: { color: '#99E078', active: false },
      grounp_active: false,
    },
    {
      simple_color: { color: '#FFF5D1', active: false },
      general_color: { color: '#FFEEB3', active: false },
      difficult_color: { color: '#FFE793', active: false },
      extremely_difficult_color: { color: '#FEDC66', active: false },
      grounp_active: false,
    },
    {
      simple_color: { color: '#FFECDA', active: false },
      general_color: { color: '#FFDFC0', active: false },
      difficult_color: { color: '#FFC691', active: false },
      extremely_difficult_color: { color: '#FFAD61', active: false },
      grounp_active: false,
    },
    {
      simple_color: { color: '#FFE2E0', active: false },
      general_color: { color: '#FFCDCA', active: false },
      difficult_color: { color: '#FFA8A3', active: false },
      extremely_difficult_color: { color: '#FF827A', active: false },
      grounp_active: false,
    },
  ];
  hasCheckedColor = false; // 是否有选中的颜色
  verifyState = false; // 是否通过校验器
  radioValue = 'color'; // 颜色选择模式
  colorPaletteVisible = false; // 是否展示颜色选择板
  isCommit = false; // 是否有提交过
  tooltipTrigger: any = 'click';

  // 表单相关参数
  validateForm!: FormGroup; // form表单
  get formArrayCtrl(): FormGroup[] {
    return (this.validateForm.get('colorSetting') as FormArray)?.controls as FormGroup[];
  }

  height!: number; // 弹窗的高度

  // 是否刷新
  is_refresh_list = false;
  is_refresh_session = false;

  showTip = false;
  drawerRef!: NzDrawerRef;
  loading = false;

  constructor(
    private fb: FormBuilder,
    private message: NzMessageService,
    private _notice: NzNotificationService,
    private modal: NzModalService,
    private _service: ColorSettingService,
    private _cdr: ChangeDetectorRef,
    private _drawer: NzDrawerService,
    private _flcValidatorService: FlcValidatorService,
    private _translate: TranslateService
  ) {}

  ngOnInit(): void {
    (this as any).addResizePageListener();
  }

  ngOnDestroy(): void {
    (this as any).removeResizePageListener();
  }

  ngAfterViewInit() {
    this.resizePage();
  }

  resizePage() {
    setTimeout(() => {
      this.height = window.innerHeight * 0.85;
    });
  }

  /**
   * 获取一级配置
   */
  getOptions() {
    this._service.getOption({ parent_id: 0 }).subscribe((res) => {
      if (res.code === 200) {
        this.first_material_options = res.data.option_list;
      }
    });
  }

  /**
   * 获取对应一级分类下的二级分类下拉配置
   */
  secondMaterialOpen() {
    this.second_material_options = [];
    if (this.queryConditions.first_style_id) {
      this._service.getOption({ parent_id: this.queryConditions.first_style_id! }).subscribe((res) => {
        if (res.code === 200) {
          this.second_material_options = res.data.option_list;
        }
      });
    }
  }

  /**
   * 一级分类选择
   */
  firstMaterialChange() {
    this.queryConditions.second_style_id = null;
    this.search();
  }

  /**
   * 搜索
   */
  search() {
    this.loading = true;
    this._service
      .getSettingColor({
        first_style_id: this.queryConditions.first_style_id,
        second_style_id: this.queryConditions.second_style_id,
        set_status: this.queryConditions.set_status,
      })
      ?.pipe(
        finalize(() => {
          this.loading = false;
        })
      )
      ?.subscribe((res) => {
        if (res.code === 200) {
          this.colorDataList = res?.data?.datalist;
          this.colorDataList.forEach((ele) => {
            if (ele?.simple_color) {
              this.showTip = true;
            }
          });
          this.creatForm();
        }
      });
  }

  /**
   * 获取自定义颜色列表
   */
  getCustomColorList() {
    this.customColor = [];
    this._service.getCustomColorList().subscribe((res) => {
      if (res.code === 200) {
        res.data.datalist.forEach((element: any) => {
          this.customColor.push({
            color: element.color,
            active: false,
          });
        });
      }
    });
  }

  /**
   * 创建表单
   */
  creatForm() {
    this.validateForm = this.fb.group({
      colorSetting: new FormArray([]),
    });
    const formarry: any = this.validateForm.controls['colorSetting'] as FormArray;
    this.colorDataList.forEach((item) => {
      formarry.push(this.createFormItem(item));
    });
  }

  /**
   * 设置表单对应数据
   * @param  {} line=null
   */
  createFormItem(line: any = null) {
    return this.fb.group({
      id: [line?.id ?? null],
      first_style_name: [line?.first_style_name ?? null], // 二级分类label
      first_style_id: [line?.first_style_id ?? null], // 一级分类id
      second_style_name: [line?.second_style_name ?? null], // 一级分类labe
      second_style_id: [line?.second_style_id ?? null], // 二级分类id
      simple: [line?.simple ?? null], // 简单sam值
      simple_color: [line?.simple_color ?? null], // 简单颜色
      general: [line?.general ?? null], // 一般sam值
      general_color: [line?.general_color ?? null], // 一般颜色
      difficult: [line?.difficult ?? null], // 较难sam值
      difficult_color: [line?.difficult_color ?? null], // 较难颜色
      extremely_difficult: [line?.extremely_difficult ?? null], // 极难sam值
      extremely_difficult_color: [line?.extremely_difficult_color ?? null], // 极难颜色
      color_checked1: [false], // 简单选中
      color_checked2: [false], // 一般选中
      color_checked3: [false], // 较难选中
      color_checked4: [false], // 极难选中
      isValidator: [false], // 是否参与检验
    });
  }

  /**
   * 切换编辑只读
   */
  editClick() {
    this.creatForm();
    this.editable = !this.editable;
    this.isCommit = false;
    if (this.editable) {
      this.colorPaletteVisible = true; // 编辑状态时打开颜色颜色器
    }
  }

  /**
   * 取消
   */
  cancel() {
    this.modal.confirm({
      nzTitle: this._translate.instant('plan.productionPlan.未保存确定取消'), // '您所编辑的内容还未保存，确定要取消编辑？'
      nzOnOk: () => {
        this.creatForm();
        this.editable = false;
      },
    });
  }

  showDrawer() {
    this.drawerRef = this._drawer.create({
      nzWrapClassName: 'drawer',
      nzBodyStyle: { padding: '0px' },
      nzPlacement: 'bottom',
      nzTitle: this.titleTpl,
      nzContent: this.contentTpl,
      nzHeight: this.height < 380 ? 380 : this.height,
      nzOnCancel: () => {
        return this.close();
      },
    });
    this.drawerRef.afterOpen?.subscribe(() => {
      this.getOptions(); // 获取下拉配置
      this.search(); // 默认查询全部
      this.getCustomColorList(); // 获取自定义颜色列表
      this.compactControl.hidePresets(); // 隐藏自定义颜色
      this.compactControl.setValueFrom(this.checkColor); // 设置默认颜色
      this.compactControl.valueChanges.subscribe((value: Color) => (this.checkColor = value.toHexString())); // 监听颜色选择器的颜色选择
    });
  }

  /**
   * 关闭
   */
  async close(): Promise<any> {
    this.closeClick.emit({
      is_refresh_list: this.is_refresh_list,
    });
    this.queryConditions = {
      first_style_id: null,
      second_style_id: null,
      set_status: null,
    };
    this.search();
    this.editable = false;
    (this.drawerRef as any) = null;
  }

  /**
   * 切换颜色选择器中的颜色，和颜色组时需清空所有选中的颜色
   */
  toggleColorCheckedMode() {
    this.clearSourceColorActive(); // 清空所有选中的颜色板颜色
    this.checkColor = '#ffffff';
    this.compactControl.setValueFrom(this.checkColor); // 设置默认颜色
  }

  /**
   * 清除全部
   */
  clearAll() {
    this.modal.confirm({
      nzTitle: this._translate.instant('plan.productionPlan.清空全部内容'), // '确定清空全部内容？',
      nzOnOk: () => {
        this.formArrayCtrl.forEach((ele) => {
          this.hasCheckedColor = false;
          // 清空数据&&清空校验
          const colorLevel = ['simple_color', 'general_color', 'difficult_color', 'extremely_difficult_color'];
          const samLevel = ['simple', 'general', 'difficult', 'extremely_difficult'];
          [...colorLevel, ...samLevel]?.forEach((name: string) => {
            ele?.get(name)?.setValue(null);
            if (name !== 'extremely_difficult') {
              ele?.get(name)?.setValidators(null);
              ele?.get(name)?.setErrors(null);
            }
          });

          const colorChecked = ['color_checked1', 'color_checked2', 'color_checked3', 'color_checked4'];
          colorChecked?.forEach((name: string) => {
            ele?.get(name)?.setValue(false);
          });
        });
      },
    });
  }

  /**
   * 清除卡片信息
   * @param  {} item
   */
  clearCardInfo(item: FormGroup) {
    this.hasCheckedColor = false;
    const colorLevel = ['simple_color', 'general_color', 'difficult_color', 'extremely_difficult_color'];
    const samLevel = ['simple', 'general', 'difficult', 'extremely_difficult'];
    [...colorLevel, ...samLevel]?.forEach((name: string) => {
      item?.get(name)?.setValue(null);
      if (name !== 'extremely_difficult') {
        item?.get(name)?.setValidators(null);
        item?.get(name)?.setErrors(null);
      }
    });

    const colorChecked = ['color_checked1', 'color_checked2', 'color_checked3', 'color_checked4'];
    colorChecked?.forEach((name: string) => {
      item?.get(name)?.setValue(false);
    });
  }

  /**
   * 清除对应卡片颜色
   * @param  {} item
   */
  clearCardColor(item: FormGroup) {
    this.hasCheckedColor = false;
    const colorLevel = ['simple_color', 'general_color', 'difficult_color', 'extremely_difficult_color'];
    colorLevel?.forEach((name: string) => {
      item?.get(name)?.setValue(null);
      item?.get(name)?.setValidators(null);
      item?.get(name)?.setErrors(null);
    });
    const colorChecked = ['color_checked1', 'color_checked2', 'color_checked3', 'color_checked4'];
    colorChecked?.forEach((name: string) => {
      item?.get(name)?.setValue(false);
    });
  }

  /**
   * 清除对应卡片Sam
   * @param  {} item
   */
  clearCardSam(item: FormGroup) {
    this.hasCheckedColor = false;
    const samLevel = ['simple', 'general', 'difficult', 'extremely_difficult'];
    samLevel?.forEach((name: string) => {
      item?.get(name)?.setValue(null);
      if (name !== 'extremely_difficult') {
        item?.get(name)?.setValidators(null);
        item?.get(name)?.setErrors(null);
      }
    });
    const colorChecked = ['color_checked1', 'color_checked2', 'color_checked3', 'color_checked4'];
    colorChecked?.forEach((name: string) => {
      item?.get(name)?.setValue(false);
    });
  }

  /**
   * 清除目标区选中的颜色块
   */
  clearTargetColorActive() {
    this.hasCheckedColor = false;
    this.formArrayCtrl.forEach((element) => {
      const colorChecked = ['color_checked1', 'color_checked2', 'color_checked3', 'color_checked4'];
      colorChecked?.forEach((name: string) => {
        element?.get(name)?.setValue(false);
      });
    });
  }

  /**
   * 目标区颜色设置
   * @param  {} item
   * @param  {} flag
   */
  checkTargetColorClick(item: FormGroup, flag: number) {
    if (!this.colorPaletteVisible) {
      // 需设置颜色时，如颜色选择器没打开需打开
      this.colorPaletteVisible = true;
    }
    this.hasCheckedColor = false; // 清空颜色选择区状态
    this.clearTargetColorActive();
    item?.get('color_checked' + flag)?.setValue(true);
    this.hasCheckedColor = true; // 有需要设置的颜色块
  }

  /**
   * 颜色源选择
   * @param  {} index
   * @param  {} item
   */
  colorSourceClick(index: number, item: string) {
    this.clearSourceColorActive(); // 清空所有源数据状态
    this.clearCustomColorActive();
    this.checkColor = '#ffffff';
    this.compactControl.setValueFrom(this.checkColor); // 设置默认颜色
    if (!this.hasCheckedColor) {
      this.message.warning(this._translate.instant('plan.productionPlan.请选择一个目标'));
      return;
    }
    if (this.radioValue === 'color') {
      (this.defaultColor[index] as any)[item].active = true;
      this.formArrayCtrl.forEach((ele) => {
        // 判断哪个是被选中需要设置颜色的颜色块
        const color = (this.defaultColor[index] as any)?.[item]?.color;
        if (ele?.get('color_checked1')?.value) {
          ele?.get('simple_color')?.setValue(color);
        } else if (ele?.get('color_checked2')?.value) {
          ele?.get('general_color')?.setValue(color);
        } else if (ele?.get('color_checked3')?.value) {
          ele?.get('difficult_color')?.setValue(color);
        } else if (ele?.get('color_checked4')?.value) {
          ele?.get('extremely_difficult_color')?.setValue(color);
        }
      });
    } else {
      this.defaultColor[index].grounp_active = true;
      this.formArrayCtrl.forEach((ele) => {
        if (
          // 被选中的是哪个卡片的颜色块
          ele?.get('color_checked1')?.value ||
          ele?.get('color_checked2')?.value ||
          ele?.get('color_checked3')?.value ||
          ele?.get('color_checked4')?.value
        ) {
          const colorLevel = ['simple_color', 'general_color', 'difficult_color', 'extremely_difficult_color'];
          colorLevel?.forEach((name: string) => {
            ele?.get(name)?.setValue((this.defaultColor?.[index] as any)?.[name]?.color);
          });
        }
      });
    }
  }

  /**
   * 清除颜色数据源选中的颜色块
   */
  clearSourceColorActive() {
    this.defaultColor.forEach((ele) => {
      ele.grounp_active = false;
      ele.simple_color.active = false;
      ele.general_color.active = false;
      ele.difficult_color.active = false;
      ele.extremely_difficult_color.active = false;
    });
  }

  /**
   * 清空自定义颜色中选中的颜色块
   */
  clearCustomColorActive() {
    this.customColor.forEach((item: any) => {
      item.active = false;
    });
  }

  /**
   * 自定义颜色选择
   * @param  {} index
   */
  customColorClick(index: number) {
    this.clearSourceColorActive();
    this.clearCustomColorActive();
    this.checkColor = '#ffffff';
    this.compactControl.setValueFrom(this.checkColor); // 设置默认颜色
    if (!this.hasCheckedColor) {
      this.message.warning(this._translate.instant('plan.productionPlan.请选择一个目标'));
      return;
    }
    this.customColor.forEach((item: any, i: number) => {
      if (index === i) {
        item.active = true;
      }
    });
    this.formArrayCtrl.forEach((ele) => {
      const color = (this.customColor[index] as any).color;
      if (ele?.get('color_checked1')?.value) {
        ele?.get('simple_color')?.setValue(color);
      } else if (ele?.get('color_checked2')?.value) {
        ele?.get('general_color')?.setValue(color);
      } else if (ele?.get('color_checked3')?.value) {
        ele?.get('difficult_color')?.setValue(color);
      } else if (ele?.get('color_checked4')?.value) {
        ele?.get('extremely_difficult_color')?.setValue(color);
      }
    });
  }

  /**
   * 是否新增
   * @param  {} $event
   */
  addCustomColor($event: any) {
    this.checkColor = this.compactControl.value.toHexString();
    if ($event === false && this.checkColor !== '#FFFFFF') {
      // 选色板关闭
      this._service.setCustomColor({ color: this.checkColor }).subscribe((res) => {
        if (res.code === 200) {
          this.customColor.unshift({
            // 自己处理自定义颜色列表
            color: this.checkColor,
            active: false,
          });
          this.customColor = this.customColor.slice(0, 16); // 截取前16个自定义颜色
          if (this.hasCheckedColor) {
            this.formArrayCtrl.forEach((ele) => {
              if (ele?.get('color_checked1')?.value) {
                ele?.get('simple_color')?.setValue(this.checkColor);
              } else if (ele?.get('color_checked2')?.value) {
                ele?.get('general_color')?.setValue(this.checkColor);
              } else if (ele?.get('color_checked3')?.value) {
                ele?.get('difficult_color')?.setValue(this.checkColor);
              } else if (ele?.get('color_checked4')?.value) {
                ele?.get('extremely_difficult_color')?.setValue(this.checkColor);
              }
            });
            this.message.success(this._translate.instant('success.operate')); // '操作成功'
          } else {
            this.message.success(this._translate.instant('success.create')); // '创建成功'
          }
          this.checkColor = '#ffffff';
          this.compactControl.setValueFrom(this.checkColor); // 设置默认颜色
        } else if (String(res.code) === '40005') {
          this.is_refresh_session = true;
        }
      });
    } else {
      this.checkColor = '#ffffff';
      this.compactControl.setValueFrom(this.checkColor); // 设置默认颜色
    }
  }

  /**
   * 删除对应颜色块的颜色
   * @param  {} item
   * @param  {} flag
   */
  deleteColor(item: FormGroup, flag: number) {
    const colorLevel = ['simple_color', 'general_color', 'difficult_color', 'extremely_difficult_color'];
    item?.get(colorLevel[flag - 1])?.setValue(null);
    item?.get(colorLevel[flag - 1])?.setValidators(null);
    item?.get(colorLevel[flag - 1])?.setErrors(null);
  }

  /**
   * 关闭颜色选择板
   */
  closeColorPalette() {
    this.colorPaletteVisible = false;
  }

  /**
   * 保存
   */
  save() {
    this.loading = true;
    let isValid = false;
    this.verifyState = false;
    this.formArrayCtrl.forEach((ele) => {
      ele?.get('isValidator')?.setValue(false);
      const colorChecked = ['color_checked1', 'color_checked2', 'color_checked3', 'color_checked4'];
      colorChecked?.forEach((name: string) => {
        ele?.get(name)?.setValue(false);
      });
      if (
        ele?.get('simple_color')?.value ||
        ele?.get('general_color')?.value ||
        ele?.get('difficult_color')?.value ||
        ele?.get('extremely_difficult_color')?.value ||
        ele?.get('simple')?.value ||
        ele?.get('general')?.value ||
        ele?.get('difficult')?.value
      ) {
        const colorLevel = ['simple_color', 'general_color', 'difficult_color', 'extremely_difficult_color'];
        colorLevel?.forEach((name: string) => {
          ele?.get(name)?.setValidators(Validators.required);
        });
        ele
          ?.get('simple')
          ?.setValidators([Validators.required, this.samMoreValidator(ele?.get('simple')?.value, ele?.get('general')?.value)]);
        ele
          ?.get('general')
          ?.setValidators([
            Validators.required,
            this.samMoreValidator(ele?.get('simple')?.value, ele?.get('general')?.value),
            this.samLessValidator(ele?.get('general')?.value, ele?.get('difficult')?.value),
          ]);
        ele
          ?.get('difficult')
          ?.setValidators([Validators.required, this.samLessValidator(ele?.get('general')?.value, ele?.get('difficult')?.value)]);
        ele?.get('isValidator')?.setValue(true);
        isValid = this._flcValidatorService.formIsInvalid(ele as FormGroup);
        if (isValid === true) {
          this.verifyState = true;
        }
      } else {
        const colorLevel = ['simple_color', 'general_color', 'difficult_color', 'extremely_difficult_color'];
        const samLevel = ['simple', 'general', 'difficult', 'extremely_difficult'];
        [...colorLevel, ...samLevel]?.forEach((name: string) => {
          if (name !== 'extremely_difficult') {
            ele?.get(name)?.setValidators(null);
            ele?.get(name)?.setErrors(null);
          }
        });
      }
    });
    this.isCommit = true;
    if (this.verifyState) {
      this._notice.error(this._translate.instant('plan.productionPlan.检查输入项'), ''); // '请检查所有输入项是否正确'
      this.loading = false;
      return;
    }

    const payload: any[] = [];
    this.formArrayCtrl.forEach((ele) => {
      payload.push({
        id: ele.value.id,
        first_style_id: ele.value.first_style_id,
        first_style_name: ele.value.first_style_name,
        second_style_id: ele.value.second_style_id,
        second_style_name: ele.value.second_style_name,
        simple: ele.value.simple,
        general: ele.value.general,
        difficult: ele.value.difficult,
        extremely_difficult: ele.value.difficult,
        simple_color: ele.value.simple_color,
        general_color: ele.value.general_color,
        difficult_color: ele.value.difficult_color,
        extremely_difficult_color: ele.value.extremely_difficult_color,
      });
    });
    this._service
      .setColorList({ datalist: payload })
      ?.pipe(
        finalize(() => {
          this.loading = false;
        })
      )
      ?.subscribe((res) => {
        if (res.code === 200) {
          this._notice.success(this._translate.instant('success.save'), ''); // '保存成功'
          this.is_refresh_list = true;
          this.editable = false;
          this.search();
          this._cdr.detectChanges();
        }
      });
  }

  /**
   * 自定义校验器
   * @param  {} curSam 当前值
   * @param  {} nextSam 下一个值
   * @param  {AbstractControl} {return(control
   * @returns null
   */
  samMoreValidator = (curSam: number, nextSam: number): ValidatorFn => {
    return (control: AbstractControl): ValidationErrors | null => {
      if (curSam && nextSam) {
        let errorStatus = false;
        if (curSam >= nextSam) {
          errorStatus = true;
        }
        return errorStatus ? { samError: true } : null;
      } else {
        return null;
      }
    };
  };

  /**
   * 自定义校验器
   * @param  {} preSam
   * @param  {} curSam
   */
  samLessValidator = (preSam: number, curSam: number): ValidatorFn => {
    return (control: AbstractControl): ValidationErrors | null => {
      if (curSam && preSam) {
        let errorStatus = false;
        if (preSam >= curSam) {
          errorStatus = true;
        }
        return errorStatus ? { samError: true } : null;
      } else {
        return null;
      }
    };
  };

  tooltipVisibleChange(event: any) {
    if (!event) {
      this.tooltipTrigger = null;
    }
  }

  samChange() {
    this.hasCheckedColor = false;
    this.formArrayCtrl.forEach((ele) => {
      const colorChecked = ['color_checked1', 'color_checked2', 'color_checked3', 'color_checked4'];
      colorChecked?.forEach((name: string) => {
        ele?.get(name)?.setValue(false);
      });
    });
  }
}
