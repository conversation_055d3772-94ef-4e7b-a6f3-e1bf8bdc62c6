import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
const routes: Routes = [
  {
    path: 'bulk',
    loadChildren: () => import('./bulk/bulk.module').then((m) => m.BulkModule),
  },
  {
    path: 'datapkg',
    loadChildren: () => import('fl-sewsmart-lib/data-pkg').then((m) => m.DataPkgModule),
  },
  {
    path: 'layout',
    loadChildren: () => import('./discharge-calculation/discharge-calculation.module').then((m) => m.DischargeCalculationModule),
  },
  {
    path: 'tracking',
    loadChildren: () => import('./order-tracking/order-tracking.module').then((m) => m.OrderTrackingModule),
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class OrderRoutingModule {}
