import { Injectable } from '@angular/core';
import { addDays, differenceInMinutes, startOfMinute, subDays } from 'date-fns';
import { OrderDimensionType, PlanGraphItem } from './interface';
import { Subject, Subscription } from 'rxjs';
import { ProductionPlanSubjectEvent } from './model/production-plan.interface';
import { ProductionPlanSubjectEventEnum } from './model/production-plan.enum';

const graphLineHeight = 52;
const graphItemHeight = 20;
@Injectable()
export class ProductionPlanShareService {
  session_version = '1.0.0';
  showTag = {
    overdue: true, // 已逾期
    behind_schedule: true, // 进度落后
    over_deadline: true, // 计划超客期
  };

  rate = 1; // 缩放倍数
  searchData = {
    order_uuids: <any[]>[],
    factory_codes: <any[]>[],
    style_codes: <any[]>[],
    start_date: subDays(new Date(), 60),
    end_date: addDays(new Date(), 120),
    is_pre_order: <undefined | boolean>undefined,
  };

  allowNarrow = true;
  private pro_order_dimension: OrderDimensionType = 'io';
  undo_disabled = true; // 撤销按钮是否置灰
  temp_session = '';

  setDefalutDate(time: number) {
    this.searchData.start_date = subDays(time, 60);
    this.searchData.end_date = addDays(time, 120);
  }

  resetSearDate() {
    this.searchData = {
      order_uuids: [],
      factory_codes: [],
      style_codes: [],
      start_date: subDays(new Date(), 60),
      end_date: addDays(new Date(), 120),
      is_pre_order: undefined,
    };
  }

  // 设置产线视图的显示维度
  setProOrderDimension(dimension: OrderDimensionType) {
    this.pro_order_dimension = dimension;
  }

  // 获取产线视图的显示维度
  getProOrderDimension(): OrderDimensionType {
    return this.pro_order_dimension;
  }
  constructor() {}

  /** 计算每个graph位置 */
  calcGraphPosition(item: PlanGraphItem, signalWidth: number, startDate: Date, isHourMode: boolean): void {
    item.top = (graphLineHeight - graphItemHeight) / 2;
    const startTime = startOfMinute(item.startTime);
    const endTime = startOfMinute(item.endTime);
    const offset = differenceInMinutes(startTime, startDate);
    const width = differenceInMinutes(endTime, startTime);
    item.left = (offset / 60 / 24) * signalWidth * (isHourMode ? 12 : 1);
    item.width = (width / 60 / 24) * signalWidth * (isHourMode ? 12 : 1);
  }

  /**
   * 将每个甘特的顺序排一遍
   * 排完是个二维数组，分多行展示
   * @param arr
   */
  sort(arr: any[]) {
    const resArr: any = [];
    arr.forEach((element) => {
      this.pushFF(resArr, 0, element);
    });
    return resArr;
  }

  /**
   * 将一条甘特图放到二维数组中
   * @param sqArr 存甘特图的二维数组
   * @param index 在二维数组的index处插入甘特图数据
   * @param dict 要插入的甘特图数据
   */
  pushFF(sqArr: any, index: any, dict: any) {
    if (!(sqArr.length > index)) {
      sqArr.push([]);
    }
    if (!sqArr[index].length) {
      sqArr[index].push(dict);
      return;
    }
    for (let i = 0; i < sqArr[index].length; i++) {
      if (i === 0) {
        if (dict.left + dict.width <= sqArr[index][i].left) {
          sqArr[index].unshift(dict);
          return;
        }
      }
      if (dict.left >= sqArr[index][i].left + sqArr[index][i].width) {
        if (i + 1 < sqArr[index].length) {
          if (dict.left + dict.width <= sqArr[index][i + 1].left) {
            sqArr[index].splice(i + 1, 0, dict);
            return;
          }
        } else {
          sqArr[index].push(dict);
          return;
        }
      }
    }
    this.pushFF(sqArr, index + 1, dict);
  }

  /* 事件订阅相关 */

  private _subject = new Subject<ProductionPlanSubjectEvent>();

  private _subscriptions = new Map<string, Subscription>();

  addSubjectListener(key: string, [...type]: Array<ProductionPlanSubjectEventEnum>, cb: (evt: ProductionPlanSubjectEvent) => void) {
    if (this._subscriptions.has(key)) {
      console.warn(`the subscription with the key of ${key} is existed and will be unsubscribed first`);
      this._subscriptions.get(key)?.unsubscribe();
    }
    const subscription: Subscription = this._subject.subscribe((e) => {
      if (type.includes(e.type)) {
        cb(e);
      }
    });
    this._subscriptions.set(key, subscription);
  }

  removeSubjectListener(key: string) {
    this._subscriptions.get(key)?.unsubscribe();
    this._subscriptions.delete(key);
  }

  sendSubjectEvent(type: ProductionPlanSubjectEventEnum, data?: any) {
    const body: ProductionPlanSubjectEvent = {
      type: type,
      data: data,
    };
    this._subject.next(body);
  }
}
