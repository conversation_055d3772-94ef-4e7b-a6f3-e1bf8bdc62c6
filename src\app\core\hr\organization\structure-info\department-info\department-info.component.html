<div class="depart-info">
  <div class="depart-header">
    <div>
      {{ (_service.departForm?.get('parent_id')?.value ? '部门信息' : '公司信息') | translate }}
    </div>
    <div>
      <ng-container *ngIf="_service.departForm">
        <ng-container *ngIf="_service.editable">
          <button nz-button flButton="default" nzShape="round" (click)="cancel()">
            {{ 'btn.cancel' | translate }}
          </button>
          <button nz-button flButton="pretty-minor" nzShape="round" [disableOnClick]="1000" (click)="save()">
            <i nz-icon [nzIconfont]="'icon-baocun'"></i>{{ 'btn.save' | translate }}
          </button>
        </ng-container>

        <ng-container *ngIf="!_service.editable">
          <button
            nz-button
            flButton="default-negative-danger"
            nzShape="round"
            nzDanger
            (click)="delete()"
            *ngIf="_service.departForm?.get('parent_id')?.value && _service.btnArr.includes('settings:org-depart_delete')">
            {{ 'structure-btn.删除部门' | translate }}
          </button>
          <button
            nz-button
            *ngIf="_service.departForm?.get('parent_id')?.value"
            flButton="pretty-minor"
            nzShape="round"
            (click)="shiftSetting()">
            {{ 'structure-btn.班次设置' | translate }}
          </button>
          <button
            nz-button
            flButton="pretty-minor"
            nzShape="round"
            (click)="addSubDepart()"
            *ngIf="_service.btnArr.includes('settings:org-depart_create')">
            <i nz-icon [nzIconfont]="'icon-xinjian1'"></i>
            {{ 'structure-btn.新建子部门' | translate }}
          </button>
          <button
            nz-button
            flButton="pretty-primary"
            nzShape="round"
            (click)="edit()"
            *ngIf="_service.btnArr.includes('settings:org-depart_update')">
            <i nz-icon [nzIconfont]="'icon-caozuolan_bianji1'"></i>{{ 'btn.edit' | translate }}
          </button>
        </ng-container>
      </ng-container>
    </div>
  </div>
  <div class="depart-content">
    <form nz-form *ngIf="_service?.departForm" [formGroup]="_service.departForm" nz-row>
      <div nz-col nzSpan="24">
        <nz-form-item>
          <nz-form-label nzRequired [nzSpan]="lang === 'en' ? 4 : 3">
            {{ (_service.departForm?.get('parent_id')?.value ? 'depart-label.部门名称' : 'depart-label.公司名称') | translate }}
          </nz-form-label>
          <nz-form-control nzSpan="10" [nzErrorTip]="nameErrorTpl" *ngIf="_service.editable; else nameTpl">
            <nz-textarea-count [nzMaxCharacterCount]="_service.departForm?.get('parent_id')?.value ? 16 : 25" class="inline-count">
              <textarea
                rows="1"
                nz-input
                formControlName="name"
                [placeholder]="placeInput"
                [maxLength]="_service.departForm?.get('parent_id')?.value ? 16 : 25"
                nzAutosize
                inputTrim></textarea>
            </nz-textarea-count>
          </nz-form-control>
          <ng-template #nameErrorTpl>
            <ng-container *ngIf="_service.departForm?.get('name')?.dirty && _service.departForm?.get('name')?.hasError('required')">
              {{ 'placeholder.input' | translate
              }}{{ (_service.departForm?.get('parent_id')?.value ? 'depart-label.部门名称' : 'depart-label.公司名称') | translate }}
            </ng-container>
          </ng-template>
          <ng-template #nameTpl>
            <div style="display: flex; align-self: center">{{ _service.departForm?.get('name')?.value | noValue }}</div>
          </ng-template>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="24">
        <nz-form-item>
          <nz-form-label nzRequired [nzSpan]="lang === 'en' ? 4 : 3">
            {{ (_service.departForm?.get('parent_id')?.value ? 'depart-label.部门编码' : 'depart-label.公司编码') | translate }}
          </nz-form-label>
          <nz-form-control nzSpan="10" [nzErrorTip]="codeErrorTpl" *ngIf="_service.editable; else codeTpl">
            <nz-textarea-count [nzMaxCharacterCount]="_service.departForm?.get('parent_id')?.value ? 20 : 15" class="inline-count">
              <textarea
                rows="1"
                nz-input
                formControlName="code"
                [placeholder]="placeInput"
                [maxLength]="_service.departForm?.get('parent_id')?.value ? 20 : 15"
                nzAutosize
                inputTrim></textarea>
            </nz-textarea-count>
          </nz-form-control>
          <ng-template #codeErrorTpl>
            <ng-container *ngIf="_service.departForm?.get('code')?.dirty && _service.departForm?.get('code')?.hasError('required')">
              {{ 'placeholder.input' | translate
              }}{{ (_service.departForm?.get('parent_id')?.value ? 'depart-label.部门编码' : 'depart-label.公司编码') | translate }}
            </ng-container>
            <ng-container *ngIf="_service.departForm?.get('code')?.dirty && _service.departForm?.get('code')?.hasError('duplicated')">
              {{ (_service.departForm?.get('parent_id')?.value ? 'depart-label.部门编码' : 'depart-label.公司编码') | translate
              }}{{ 'form-error.is-exit' | translate }}
            </ng-container>
            <ng-container *ngIf="_service.departForm?.get('code')?.pending">
              {{ (_service.departForm?.get('parent_id')?.value ? 'depart-label.部门编码' : 'depart-label.公司编码') | translate
              }}{{ 'form-error.check-pending' | translate }}
            </ng-container>
            <ng-container *ngIf="_service.departForm.get('code')?.dirty && _service.departForm.get('code')?.hasError('pattern')">
              {{ 'form-error.special-chinese-characters' | translate }}
            </ng-container>
          </ng-template>
          <ng-template #codeTpl>
            <div style="display: flex; align-self: center">{{ _service.departForm?.get('code')?.value | noValue }}</div>
          </ng-template>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="24">
        <nz-form-item>
          <nz-form-label nzRequired [nzSpan]="lang === 'en' ? 4 : 3">{{ 'depart-label.状态' | translate }}</nz-form-label>
          <nz-form-control nzSpan="10" [nzErrorTip]="statusErrorTpl" *ngIf="_service.editable; else statusTpl">
            <nz-select nzShowSearch formControlName="status" style="min-width: 96px" [nzPlaceHolder]="placeSelect">
              <ng-container *ngFor="let item of statusOptions">
                <nz-option [nzValue]="item.value" [nzLabel]="'depart-status.' + item.label | translate"></nz-option>
              </ng-container>
            </nz-select>
          </nz-form-control>
          <ng-template #statusErrorTpl>
            <ng-container *ngIf="_service.departForm?.get('name')?.dirty && _service.departForm?.get('name')?.hasError('required')">
              {{ 'placeholder.input' | translate }}{{ 'depart-label.状态' | translate }}
            </ng-container>
          </ng-template>
          <ng-template #statusTpl>
            <div style="display: flex; align-self: center">
              <span *ngIf="_service.hasDeptDataAuth">
                {{
                  (_service.departForm?.get('status')?.value ? ('depart-status.已启用' | translate) : ('depart-status.已停用' | translate))
                    | noValue
                }}
              </span>
              <span *ngIf="!_service.hasDeptDataAuth"> - </span>
            </div>
          </ng-template>
        </nz-form-item>
      </div>
      <div nz-col nzSpan="24" *ngIf="_service.isEnterprise">
        <nz-form-item class="form-item">
          <nz-form-label class="info-label" [nzSpan]="lang === 'en' ? 4 : 3">{{ 'depart-label.企业logo' | translate }}</nz-form-label>
          <nz-form-control class="info-control">
            <div class="contract-logo">
              <flc-image-gallery
                #imageGalleryRef
                [formControlName]="'logos'"
                [isEdit]="_service.editable"
                [numberLimit]="1"
                [maxSize]="5"
                [imageType]="['png', 'jpg', 'jpeg']"
                [ossCustomConfig]="ossCustomConfig"
                [useCloudImage]="true"></flc-image-gallery>
              <div class="contract-logo-tip" *ngIf="_service.editable">
                <span>{{ 'tips.注意' | translate }}:</span>
                <div>{{ 'tips.logo配置后将显示在合同右上角' | translate }}</div>
                <div>{{ 'tips.建议尺寸：106 x 24px (支持jpg、jpeg、png格式，最大5M)' | translate }}</div>
              </div>
            </div>
          </nz-form-control>
        </nz-form-item>
      </div>
    </form>
  </div>
</div>

<app-delete-modal (handleDelete)="handleDelete($event)"></app-delete-modal>
<app-subdepart-modal></app-subdepart-modal>
