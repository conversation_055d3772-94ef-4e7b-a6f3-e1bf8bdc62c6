import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-production-report-tab',
  templateUrl: './production-report-tab.component.html',
  styleUrls: ['./production-report-tab.component.scss'],
})
export class ProductionReportTabComponent implements OnInit {
  @Input() selectedTab!: number;
  @Input() currentDimension!: number;

  @Output() selectedTabChange = new EventEmitter<any>();
  @Output() handleToggle = new EventEmitter<any>();
  @Output() handleReset = new EventEmitter<any>();
  @Output() currentDimensionChange = new EventEmitter<any>();

  tabs: Array<any> = [
    { name: '日报表', value: 1 },
    { name: '汇总', value: 2 },
  ];
  dimensions = [
    { name: '按大货单', value: 1 },
    { name: '按交付单', value: 2 },
  ];
  isFold = false;
  resetTip = '';
  toggleTip = '';

  constructor(private _translateService: TranslateService) {}

  ngOnInit(): void {
    this.resetTip = this._translateService.instant('btn.reset');
    this.toggleTip = this._translateService.instant(this.isFold ? 'btn.unfold' : 'btn.fold');
  }

  onReset() {
    this.handleReset.emit();
  }

  onToggle(value: boolean) {
    this.isFold = !this.isFold;
    this.toggleTip = this._translateService.instant(this.isFold ? 'btn.unfold' : 'btn.fold');
    this.handleToggle.emit(value);
  }

  onTabChange(value: number) {
    if (value === this.selectedTab) return;
    this.selectedTabChange.emit(value);
  }

  onDimensionChange(value: number) {
    this.currentDimensionChange.emit(value);
  }
}
