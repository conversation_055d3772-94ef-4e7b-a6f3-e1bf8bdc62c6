export interface IBatchEditFieldOption {
  field_type: number;
  node_template_id: number;
  prod_status_field: string;
  prod_status_field_name: string;
  template_node_id: number;
  template_node_name: string;
  type: number;
}

// 模版字段
export interface ITemplateFields {
  id: number;
  node_template_id: number; // 模板id
  field_name: string; // 字段名
  field_type: number; // 字段类型
  index: number; // 排序
  label: number; // 标签：1=无，2=计划，3=实际
  required: number; // 是否必填 1=是，2=不是
  data: string; // 选项值/计算逻辑
}

// 消息通知列表
export interface IMessageListItem {
  warning_message_id: number; // 预警消息id
  warning_name: string;
  warning_message: string; //消息内容
  created_at: number; // 消息创建时间
  message_type: number; // 消息通知类型 1 = 需处理（Deal with now） 2 = 仅查看（I got it）
  level: number; // 预警级别：1 = P1, 2 = P2, 3 = P3
  prod_status_ids: Array<number>; // 生产进度跟踪id
  template_node_id: number; // 模板节点id
  template_node_name: string; // 节点名称
}
