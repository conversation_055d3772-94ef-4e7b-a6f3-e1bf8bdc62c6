import { Component, EventEmitter, OnInit, Output, ViewChild } from '@angular/core';
import { FlcModalService } from 'fl-common-lib';
import { ProductionPlanV2Service } from '../../../production-plan-v2.service';
import { MessageNotificationModalComponent } from '../message-notification-modal/message-notification-modal.component';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-message-notification-drawer',
  templateUrl: './message-notification-drawer.component.html',
  styleUrls: ['./message-notification-drawer.component.scss'],
})
export class MessageNotificationDrawerComponent implements OnInit {
  @ViewChild('appMessageNotificationModal') notiModalComp!: MessageNotificationModalComponent;
  @Output() onSearchWithParams = new EventEmitter();
  @Output() onHideDrawer = new EventEmitter();
  @Output() onAcceptAutoAdjustPlan = new EventEmitter();

  constructor(private _flModal: FlcModalService, private _service: ProductionPlanV2Service, private _msg: NzMessageService) {}
  translateName = 'plan.notification.';
  searchList = [
    { label: '订单需求号', isMultiple: true, code: 'order_codes', optionsKey: 'order_codes' },
    { label: '大货单号', isMultiple: true, code: 'bulk_code', optionsKey: 'bulk_code' },
    { label: '款式编码', isMultiple: true, code: 'style_codes', optionsKey: 'style_codes' },
    { label: '消息类型', isMultiple: false, code: 'change_types', optionsKey: 'change_types' },
  ];

  // 搜索数据
  searchData: any = {};
  // 下拉数据
  optionsData = {};
  // 消息数据
  datalist: any[] = [];

  isDrawerVisible = false;

  ngOnInit(): void {}

  getDatalist() {
    const payload = { ...this.searchData, change_types: this.searchData.change_types ? [this.searchData.change_types] : [] };
    this._service.getNotificationList(payload).subscribe((res: any) => {
      if (res.code == 200) {
        this.datalist = res.data.list ?? [];
      }
    });
  }

  getOptions() {
    this._service.getNotificationListOptions().subscribe((res: any) => {
      if (res.code == 200) {
        this.optionsData = res.data ?? {};
      }
    });
  }
  pinnedParams: any = {};
  onShow(params: any, pinnedParams: any) {
    this.isDrawerVisible = true;
    this.pinnedParams = pinnedParams;
    this.getOptions();
    this.onReset(params);
  }

  onHide() {
    this.isDrawerVisible = false;
    this.onHideDrawer.emit();
  }

  searchDisabled = false;
  onReset(params: any) {
    this.searchData = params ?? {};
    this.searchDisabled = params != null;
    this.onSearch();
  }

  onSearch() {
    this.getDatalist();
  }

  async onManulAdjust(index: number) {
    const item = this.datalist[index];
    const payload = { factory_code: item.factory_code, order_uuid: item.order_uuid, production_line_no: item.production_line_no };
    this._service.deleteNotification(payload).subscribe(() => {});
    this.onSearchWithParams.emit({
      order_uuids: [item.order_uuid],
      factory_codes: [item.factory_code],
      style_codes: [item.style_code],
    });
    this.onHide();
  }

  onAutoAdjust(index: number) {
    const item = this.datalist[index];
    const payload = { factory_code: item.factory_code, order_uuid: item.order_uuid, production_line_no: item.production_line_no };
    this._service.planScheduleMessageAutoAdjust(payload).subscribe((res: any) => {
      if (res.code == 200) {
        this.notiModalComp.onShowAdjustResult(item, res.data);
      }
    });
  }

  onHandleAutoAdjustResult(data: any) {
    this.handleAutoAdjustResult(data);
  }

  public handleAutoAdjustResult(data: any) {
    const params = { ...data, ...this.pinnedParams };
    this._service.applyPlanScheduleMessageAutoAdjust(params).subscribe(async (res: any) => {
      if (res.code === 200) {
        this.notiModalComp.onHide();
        if (!data.confirmed) {
          this.onSearchWithParams.emit({
            order_uuids: [data.order_uuid],
            factory_codes: [data.factory_code],
            style_codes: [data.style_code],
          });
          this.onHide();
        } else {
          this._msg.success('操作成功');
          this.onHide();
          // 更新编辑状态
          this.onAcceptAutoAdjustPlan.emit(data);
        }
      }
    });
  }

  deleteItem(index: number) {
    const item = this.datalist[index];
    this._flModal
      .confirmCancel({
        content: '确定删除该条消息通知吗?',
      })
      .afterClose.subscribe((res: any) => {
        if (res) {
          const payload = { factory_code: item.factory_code, order_uuid: item.order_uuid, production_line_no: item.production_line_no };
          this._service.deleteNotification(payload).subscribe((res: any) => {
            if (res.code == 200) {
              this.datalist.splice(index, 1);
              this._msg.success('删除成功');
            }
          });
        }
      });
  }
}
