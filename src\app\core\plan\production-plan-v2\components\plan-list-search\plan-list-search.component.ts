import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { NzTreeSelectComponent } from 'ng-zorro-antd/tree-select';

@Component({
  selector: 'app-plan-list-search',
  templateUrl: './plan-list-search.component.html',
  styleUrls: ['./plan-list-search.component.scss'],
})
export class PlanListSearchComponent implements OnInit {
  @ViewChild('treeSelect') treeSelect?: NzTreeSelectComponent;
  @Input() searchData: { order_uuids?: string[]; factory_codes?: any[]; style_codes?: any[]; is_pre_order?: boolean } = {};
  @Input() searchOptions: { [key: string]: any[] } = {};
  @Output() onSearch = new EventEmitter();
  @Input() isDisabled = false;

  baseOption = [
    { value: false, label: '否' },
    { value: true, label: '是' },
  ];

  searchForms = [
    {
      label: '加工厂/产线',
      type: 'select',
      valueKey: 'factory_codes',
      optionKey: 'factory_codes',
    },
    {
      label: '大货单号',
      type: 'select',
      valueKey: 'order_uuids',
      optionKey: 'order_uuids',
    },
  ];
  constructor() {}

  ngOnInit() {}

  onHandleWhere() {
    this.onSearch.emit();
  }
  onSearchWithParams(params: any) {
    if (this.isDisabled) return;
    this.searchData.order_uuids = params.order_uuids;
    this.searchData.factory_codes = params.factory_codes;
    this.searchData.style_codes = params.style_codes;
    this.searchData.is_pre_order = params.is_pre_order;
    this.onHandleWhere();
  }
}
