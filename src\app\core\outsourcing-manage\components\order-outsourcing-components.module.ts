import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { OrderOutsourcingBasicInfoComponent } from './order-outsourcing-basic-info/order-outsourcing-basic-info.component';
import { OrderOutsourcingCardContainerComponent } from './order-outsourcing-card-container/order-outsourcing-card-container.component';
import { OrderOutsourcingTabsContainerComponent } from './order-outsourcing-tabs-container/order-outsourcing-tabs-container.component';
import { OrderOutsourcingFactoryItemHeaderComponent } from './order-outsourcing-factory-item-header/order-outsourcing-factory-item-header.component';
import { OrderOutsourcingFactoryPriceListComponent } from './order-outsourcing-factory-item/order-outsourcing-factory-price-list';
import { DepartmentSelectComponent } from './department-select/department-select.component';
import { NzTreeSelectModule } from 'ng-zorro-antd/tree-select';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { OrderPlantContainerComponent } from './order-outsourcing-factory-container/order-outsourcing-factory-container.component';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzFormModule } from 'ng-zorro-antd/form';
import { FlcComponentsModule, FlcDirectivesModule, FlcOssUploadService } from 'fl-common-lib';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import { NzCollapseModule } from 'ng-zorro-antd/collapse';
import { OrderOutsourcingFactoryItemComponent } from './order-outsourcing-factory-item/order-outsourcing-factory-item.component';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { OrderOutsourcingService } from './order-outsourcing.service';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { FlButtonModule } from 'fl-ui-angular';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
const NzModules = [
  NzSelectModule,
  NzTabsModule,
  NzIconModule,
  NzFormModule,
  NzTreeSelectModule,
  NzCollapseModule,
  NzDividerModule,
  NzToolTipModule,
  NzButtonModule,
  NzInputModule,
  NzInputNumberModule,
  NzTableModule,
  NzDatePickerModule,
];
@NgModule({
  providers: [FlcOssUploadService, OrderOutsourcingService],
  imports: [
    CommonModule,
    ...NzModules,
    ReactiveFormsModule,
    FormsModule,
    FlcComponentsModule,
    FlcDirectivesModule,
    FlButtonModule,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new MultiTranslateHttpLoader(http, [
            { prefix: './assets/i18n/outsourcing-manage/garm-sec-outsourcing/', suffix: '.json' },
          ]);
        },
        deps: [HttpClient],
      },
    }),
  ],
  declarations: [
    OrderOutsourcingBasicInfoComponent,
    OrderOutsourcingCardContainerComponent,
    OrderOutsourcingTabsContainerComponent,
    DepartmentSelectComponent,
    OrderPlantContainerComponent,
    OrderOutsourcingFactoryItemComponent,
    OrderOutsourcingFactoryItemHeaderComponent,
    OrderOutsourcingFactoryPriceListComponent,
  ],
  exports: [
    OrderOutsourcingBasicInfoComponent,
    OrderOutsourcingCardContainerComponent,
    OrderOutsourcingTabsContainerComponent,
    DepartmentSelectComponent,
    OrderPlantContainerComponent,
    OrderOutsourcingFactoryItemComponent,
  ],
})
export class OrderOutsourcingComponentsModule {}
