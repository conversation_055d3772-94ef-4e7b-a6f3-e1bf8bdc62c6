import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { ProductionPlanShareService } from '../../production-plan-share.service';
import { ProductionPlanSubjectEventEnum } from '../../model/production-plan.enum';

@Component({
  selector: 'app-plan-list-header',
  templateUrl: './plan-list-header.component.html',
  styleUrls: ['./plan-list-header.component.scss'],
})
export class PlanListHeaderComponent implements OnInit {
  @Output() reset = new EventEmitter();
  @Output() hold = new EventEmitter();
  constructor(public _planShareService: ProductionPlanShareService) {}

  ngOnInit() {}

  onReset() {
    this.reset.emit();
  }

  onExpandChange() {
    this.hold.emit();
  }

  /** 缩放器 */
  onZoomStepChange(step: number) {
    this._planShareService.sendSubjectEvent(ProductionPlanSubjectEventEnum.onZoomChange, step);
  }
}
