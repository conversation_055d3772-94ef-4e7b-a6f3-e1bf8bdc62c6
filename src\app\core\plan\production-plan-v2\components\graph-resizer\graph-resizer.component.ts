import { Component, EventEmitter, OnInit, Output } from '@angular/core';

// 缩放器比例
const stepValue: { [key: number]: number } = {
  1: 0.5,
  2: 0.6,
  3: 0.7,
  4: 0.8,
  5: 0.9,
  6: 1,
  7: 1.5,
  8: 2,
  9: 2.5,
  10: 3,
  11: 3.5,
};

@Component({
  selector: 'app-graph-resizer',
  templateUrl: './graph-resizer.component.html',
  styleUrls: ['./graph-resizer.component.scss'],
})
export class GraphResizerComponent implements OnInit {
  zoomStep = 6;
  @Output() zoomStepChange = new EventEmitter<number>();

  stepFormatter(value: number): string {
    const show_value = stepValue?.[value] * 100;
    return `${show_value}%`;
  }

  constructor() {}

  ngOnInit(): void {}

  /** 缩放器 */
  stepChange(step: number) {
    const value = stepValue?.[step] ?? 1;
    this.zoomStepChange.emit(value);
  }
}
