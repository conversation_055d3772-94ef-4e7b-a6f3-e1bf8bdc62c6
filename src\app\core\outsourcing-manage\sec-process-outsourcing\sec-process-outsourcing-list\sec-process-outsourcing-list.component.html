<div #searchContainer>
  <flc-search-container
    [btnTpl]="btnTpl"
    [headerTitle]="'outsourcingList.二次工艺外发' | translate"
    (reset)="reset()"
    (handleFold)="resizePage()">
    <div *ngFor="let item of searchList">
      <span class="search-name">{{ 'outsourcingTableHeaderAndLabel.' + item.label | translate }}：</span>
      <ng-container *ngIf="item.type === 'input'">
        <div class="form-item-input-custom">
          <input
            flcInputTrim
            nz-input
            [(ngModel)]="searchParams.customer"
            [placeholder]="'placeholder.input' | translate"
            [flcDebounceEvent]="onSearch.bind(this)"
            [controlEvent]="'input'"
            [controlTime]="500"
            [controlType]="'debounce'" />
        </div>
      </ng-container>
      <ng-container *ngIf="item.type === 'select'">
        <flc-dynamic-search-select
          [dataUrl]="searchOptionFetchUrl"
          [(ngModel)]="searchParams[item.valueKey]"
          [column]="item.labelKey"
          (handleSearch)="getDataList(true)"
          [payLoad]="item.payload || {}"
          [canSearch]="item.canSearch ?? true"
          [optAlwaysReload]="item.alwaysReload ?? false">
        </flc-dynamic-search-select>
      </ng-container>
      <nz-range-picker
        *ngIf="item.type === 'date'"
        [(ngModel)]="searchParams[item.labelKey]"
        (ngModelChange)="getDataList()"></nz-range-picker>
    </div>
  </flc-search-container>
</div>

<ng-template #btnTpl>
  <button
    *ngIf="_service.btnArr.includes('bulk:sec-process-outsourcing-create')"
    nz-button
    flButton="pretty-primary"
    [nzShape]="'round'"
    (click)="jumpDetail({ id: 'add' })">
    <i nz-icon [nzIconfont]="'icon-xinjian1'"></i>
    {{ 'btn.add' | translate }}
  </button>
</ng-template>

<div class="table-box">
  <flc-table
    [tableHeader]="tableHeaders"
    [template]="tdTemplate"
    [tableConfig]="tableConfig"
    (getDetails)="jumpDetail($event)"
    (indexChanges)="onIndexChange($event)"
    (sizeChanges)="onSizeChange($event)"
    (sortDataLists)="sortOrderChange($event)"
    [id]="selectedId">
    <ng-template let-data="data" #tdTemplate>
      <ng-container *ngIf="data.isTd && data.key === 'distribution_factory_name'">
        <flc-text-truncated
          [data]="
            data?.item?.distribution_factory_name &&
            data?.item?.distribution_factory_name.length &&
            data?.item?.distribution_factory_name.join('、')
          "></flc-text-truncated>
      </ng-container>
      <ng-container *ngIf="data.isTd && data.key === 'po_due_times'">
        <flc-text-truncated
          [data]="data?.item?.po_due_times && data?.item?.po_due_times.length && data?.item?.po_due_times.join('、')"></flc-text-truncated>
      </ng-container>
      <ng-container *ngIf="data.isTd && data.key === 'extra_process_info'">
        <flc-text-truncated [data]="data?.item?._extra_process_info"></flc-text-truncated>
      </ng-container>
    </ng-template>
  </flc-table>
</div>
