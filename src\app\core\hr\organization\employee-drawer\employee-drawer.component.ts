import { NzNotificationService } from 'ng-zorro-antd/notification';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { NzDrawerRef, NzDrawerService } from 'ng-zorro-antd/drawer';
import { AccountDrawerComponent } from '../account-drawer/account-drawer.component';
import { OrganizationService } from '../organization.service';
import { UtilService } from 'src/app/shared/util.service';
import { EmployeeStatusOptions } from '../interface/sturcture-config';
import { NzMessageService } from 'ng-zorro-antd/message';
import { TreeOptions } from '../interface/structure-data';
import { factoryCodes } from 'src/app/shared/common-config';

@Component({
  selector: 'app-employee-drawer',
  templateUrl: './employee-drawer.component.html',
  styleUrls: ['./employee-drawer.component.scss'],
})
export class EmployeeDrawerComponent implements OnInit {
  @ViewChild(AccountDrawerComponent) accountDrawer: any;
  @ViewChild('titleTpl') titleTpl: TemplateRef<any> | undefined;
  @ViewChild('contentTpl') contentTpl: TemplateRef<any> | undefined;
  isAdd = true;
  employee!: NzDrawerRef;
  placeInput!: string;
  placeSelect!: string;
  employeeForm!: FormGroup;
  statusOptions: { label: string; value: number }[] = EmployeeStatusOptions;
  accountOptions: { label: string; value: number; name: string }[] = [];
  lang = localStorage.getItem('lang') || 'zh';
  userInfo: any = null;
  factoryCodes = factoryCodes || [];

  constructor(
    private _drawer: NzDrawerService,
    private _translateService: TranslateService,
    public _service: OrganizationService,
    private _util: UtilService,
    private _notice: NzNotificationService,
    private _fb: FormBuilder,
    private _msg: NzMessageService
  ) {
    const userInfo: string | null = localStorage.getItem('userInfo');
    this.userInfo = userInfo ? JSON.parse(userInfo) : {};
  }

  ngOnInit(): void {}

  getOptions() {
    this._service
      .geUserList({
        where: { employee: { op: '=', value: null } },
        orderBy: [],
        // page: 1,
        // limit: 20,
      })
      .subscribe((res) => {
        if (res.code === 200) {
          this.accountOptions = res.data.data.map((item: any) => {
            return {
              name: item.name,
              label: item.login_name,
              value: item.id,
            };
          });
        }
      });
  }

  createEmployeeDrawer() {
    this.getOptions();
    this.placeInput = this._translateService.instant('placeholder.input');
    this.placeSelect = this._translateService.instant('placeholder.select');
    this.employee = this._drawer.create({
      nzTitle: this.titleTpl,
      nzContent: this.contentTpl,
      nzWidth: '400px',
      nzWrapClassName: 'drawer-outer',
    });
  }

  /**
   * 员工姓名：最多不能超过16个字符；必填
   * 工号：最多不能超过16个字符；必填,唯一，不可重复
   * 状态：分为在职、离职；默认选择在职，必填
   * 操作账号：下拉显示操作用户里启用的账号；显示格式为用户编码+用户名称；可模糊搜索；选填
   * @param line
   */
  initForm(line: any = null) {
    const codePattern = '[^\u4e00-\u9fa5]*';
    this.employeeForm = this._fb.group({
      id: [line?.id ?? null],
      name: [
        line?.name ?? null,
        [Validators.required, Validators.maxLength(this.factoryCodes.includes(this.userInfo?.factory_code) ? 100 : 16)],
      ],
      code: [
        line?.code ?? null,
        [Validators.required, Validators.maxLength(16), Validators.pattern(codePattern)],
        [this._service.uniqueEmployeeValidator(line?.code ?? null)],
      ],
      status: [line?.status ?? 1, [Validators.required]],
      user_id: [line?.user_id ?? null],
      dept_id: [line?.dept_id ?? this._service.departForm?.get('id')?.value ?? null],
      avatar: [null], // 头像
    });
    if (line && line.user_id) {
      this.accountOptions = [
        {
          name: line.user_name,
          label: line.user_login_name,
          value: line.user_id,
        },
      ];
    }
  }

  handleCancel() {
    this.employee.close();
  }

  handleOk() {
    this._util
      .checkIfFormPassesValidation(this.employeeForm)
      .then((valid) => {
        if (valid) {
          const payload = this.employeeForm.getRawValue();
          if (payload.id) {
            this._service.updateEployee(payload).subscribe((res) => {
              if (res.code === 200) {
                const update_msg = this._translateService.instant('success.update');
                this._msg.success(update_msg);
                this.updateTree();
                const treeNode = this._service.selectTree?.length ? this._service.selectTree[0] : null;
                if (treeNode) {
                  const line = this._service.selectOptions?.find((item) => item.value === treeNode);
                  if (line && line?.is_employee) {
                    this._service.getEmployeeList(payload.dept_id, false, payload.id);
                  } else {
                    this._service.getEmployeeList(payload.dept_id);
                  }
                } else {
                  this._service.getEmployeeList(payload.dept_id);
                }
                this.employee.close();
              }
            });
          } else {
            this._service.addEmployee(payload).subscribe((res) => {
              if (res.code === 200) {
                this._service.addedEmployee = res.data;
                const create_msg = this._translateService.instant('success.create');
                this._msg.success(create_msg);
                this.updateTree();
                this._service.getEmployeeList(payload.dept_id, true);
                this.employee.close();
              }
            });
          }
        } else {
          const msg = this._translateService.instant('form-error.input-right');
          this._notice.error('', msg);
          return;
        }
      })
      .catch((error) => {
        console.log('error', error);
        return;
      })
      .finally(() => {
        return;
      });
  }

  /**
   * 更新架构树的数据、选中节点、展开节点
   * @param depart：当前展示的部门或公司id
   */
  updateTree(depart: any = null) {
    const depart_id = depart ? depart : this._service.departForm.get('id')?.value;
    const treeNode = this._service.selectTree?.length ? this._service.selectTree[0] : depart_id;
    const expandedNode = [...this._service.expandedTree] ?? [];
    this._service.getTree().then((datas: TreeOptions[]) => {
      this._service.selectTree = [this.isAdd ? depart_id : treeNode];
      const line = datas?.find((item) => item.id === depart_id && !item.is_employee);
      this._service.expandedTree = expandedNode?.length ? expandedNode : line?.expanded_list ?? [];
    });
  }

  /**
   * 创建操作账号
   */
  createAccount() {
    this.employee.nzOffsetX = 180;
    this.accountDrawer.initAccountForm({
      name: this.employeeForm.get('name')?.value ?? null,
      login_name: this.employeeForm.get('code')?.value ?? null,
    });
    this.accountDrawer.priorityLists = [{ role_id: null, priority: 1 }];
    this.accountDrawer.createAccountDrawer();
  }

  /**
   * 操作账号创建成功，自动选中新建的操作账号
   * @param data
   */
  handleAccountOk(data: any) {
    this.accountOptions.push({
      value: data.id,
      label: data.login_name,
      name: data.name,
    });
    this.employeeForm.get('user_id')?.setValue(data.id);
    this.employee.nzOffsetX = 0;
    this.accountDrawer.closeAccountDrawer();
  }

  handleReturn() {
    this.employee.nzOffsetX = 0;
    this.accountDrawer.closeAccountDrawer();
  }
}
