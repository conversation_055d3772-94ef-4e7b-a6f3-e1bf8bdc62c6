import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { FlcComponentsModule, FlcDirectivesModule, FlcPipesModule } from 'fl-common-lib';
import { FlButtonModule } from 'fl-ui-angular';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCalendarModule } from 'ng-zorro-antd/calendar';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import { CalendarComponent } from './calendar/calendar.component';
import { OtherSettingsRoutingModule } from './other-settings-routing.module';

const nzModules = [
  NzIconModule,
  NzFormModule,
  NzButtonModule,
  NzDividerModule,
  NzInputModule,
  NzRadioModule,
  NzInputNumberModule,
  NzSelectModule,
  NzSpinModule,
  NzCalendarModule,
];
const flCommonModules = [FlButtonModule, FlcComponentsModule, FlcPipesModule, FlcDirectivesModule];

@NgModule({
  declarations: [CalendarComponent],
  imports: [
    ...nzModules,
    ...flCommonModules,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    OtherSettingsRoutingModule,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new MultiTranslateHttpLoader(http, [{ prefix: './assets/i18n/other-setting/', suffix: '.json' }]);
        },
        deps: [HttpClient],
      },
    }),
  ],
})
export class OtherSettingsModule {
  constructor(public translateService: TranslateService) {
    const cl = translateService.currentLang;
    translateService.currentLang = '';
    translateService.use(cl);
  }
}
