import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { differenceInDays } from 'date-fns';

@Component({
  selector: 'app-message-notification-modal',
  templateUrl: './message-notification-modal.component.html',
  styleUrls: ['./message-notification-modal.component.scss'],
})
export class MessageNotificationModalComponent implements OnInit {
  @Output() onManulAdjust = new EventEmitter();
  @Output() onConfirmedAutoAdjust = new EventEmitter();
  constructor() {}

  translateName = 'plan.notification.';
  adjustResultIsVisible = false;
  tipIsVisible = false;

  adjustResult: any = [];
  orderData: any = {};
  // 后端需要的参数
  pinnedParams = { current_time: null, delete_group_id: null };
  ngOnInit(): void {}

  onShowAdjustResult(item: any, data: any) {
    this.orderData = item;
    this.pinnedParams.current_time = data.current_time;
    this.pinnedParams.delete_group_id = data.delete_group_id;

    this.adjustResult = (data.recodes ?? []).map((e: any) => {
      const before_start_time = e.before_adjustment.start_time;
      const before_end_time = e.before_adjustment.end_time;
      const before_diff_days = differenceInDays(before_end_time, before_start_time);
      const after_start_time = e.after_adjustment.start_time;
      const after_end_time = e.after_adjustment.end_time;
      const after_diff_days = differenceInDays(after_end_time, after_start_time);
      const afterDays = after_start_time && after_start_time ? after_diff_days + 1 : 0;
      const beforeDays = before_start_time && before_end_time ? before_diff_days + 1 : 0;
      return {
        ...e,
        ...e.before_adjustment,
        after_adjustment: {
          ...e.after_adjustment,
          start_time: e.after_adjustment.start_time == 0 ? null : e.after_adjustment.start_time,
          end_time: e.after_adjustment.end_time == 0 ? null : e.after_adjustment.end_time,
          days: afterDays,
        },
        before_adjustment: {
          ...e.before_adjustment,
          start_time: e.before_adjustment.end_time == 0 ? null : e.before_adjustment.start_time,
          end_time: e.before_adjustment.end_time == 0 ? null : e.before_adjustment.end_time,
          days: beforeDays,
        },
        adjust_days: beforeDays - afterDays,
      };
    });
    this.adjustResultIsVisible = true;
  }

  getAdjustDays(days: number) {
    return Math.abs(days);
  }

  getParams(confirmed: boolean) {
    return {
      factory_code: this.orderData.factory_code,
      order_uuid: this.orderData.order_uuid,
      style_code: this.orderData.style_code,
      production_line_no: this.orderData.production_line_no,
      confirmed: confirmed,
      ...this.pinnedParams,
      list: this.adjustResult.map((e: any) => {
        return {
          // color_code: e.after_adjustment.color_code,
          // color_name: e.after_adjustment.color_name,
          // po_unique_code: e.after_adjustment.po_unique_code,
          // qty: e.after_adjustment.qty,
          // plan_start_time: e.after_adjustment.start_time,
          // plan_end_time: e.after_adjustment.end_time,
          // sam: e.after_adjustment.sam,
          // average_daily_production: e.after_adjustment.average_daily_production,
          ...e.after_adjustment,
          plan_start_time: e.after_adjustment.start_time == 0 ? null : e.after_adjustment.start_time,
          plan_end_time: e.after_adjustment.end_time == 0 ? null : e.after_adjustment.end_time,
          plan_schedule_id: e.plan_schedule_id,
        };
      }),
    };
  }

  manulAdjust() {
    this.onManulAdjust.emit(this.getParams(false));
  }

  confirmed() {
    this.onConfirmedAutoAdjust.emit(this.getParams(true));
  }

  tip: string | null = null;
  onShowTips(tip: string | null) {
    if (!tip || !tip.length) return;
    this.tip = tip;
    this.tipIsVisible = true;
  }

  onHide() {
    this.adjustResultIsVisible = false;
    this.tipIsVisible = false;
  }

  handleCancel() {
    this.onHide();
  }
}
