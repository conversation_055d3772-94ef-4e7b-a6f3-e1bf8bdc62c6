import { Injectable } from '@angular/core';
// import { HttpParams } from '@angular/common/http';
import { FormGroup } from '@angular/forms';
import { combineLatest, of } from 'rxjs';
import { map } from 'rxjs/operators';
// import { isNil } from 'ng-zorro-antd/core/util';

@Injectable()
export class UtilService {
  constructor() {}

  // createHttpParams(payload: any): HttpParams {
  //   let params = new HttpParams();
  //   Object.keys(payload).forEach((key: string) => {
  //     if (!isNil(payload[key]) && String(payload[key]).trim() !== '') {
  //       params = params.append(key, payload[key]);
  //     }
  //   });
  //   return params;
  // }

  /**
     * 说明：手动调用每个组件的同步与异步校验，并返回布尔值，表示是否通过了所有校验
     * 使用：
     * checkIfFormPassesValidation(this.formGroup)
     .then(valid => {
          if (valid) {
            // Todo
          }
        });
     * @param formGroup
     */
  checkIfFormPassesValidation(formGroup: FormGroup) {
    const syncValidationErrors = Object.keys(formGroup.controls)
      .map((c) => {
        const control = formGroup.controls[c];
        return !control.validator ? null : control.validator(control);
      })
      .filter((errors) => !!errors);
    return combineLatest(
      Object.keys(formGroup.controls).map((c) => {
        const control = formGroup.controls[c];
        return !control.asyncValidator ? of(null) : control.asyncValidator(control);
      })
    )
      .pipe(
        map((asyncValidationErrors) => {
          const hasErrors = [...syncValidationErrors, ...asyncValidationErrors.filter((errors) => !!errors)].length;
          if (hasErrors) {
            Object.keys(formGroup.controls).forEach((key) => {
              formGroup.controls[key].markAsDirty();
              formGroup.controls[key].updateValueAndValidity();
            });
          }
          return !hasErrors;
        })
      )
      .toPromise();
  }

  // formIsInvalid(validateForm: FormGroup): boolean {
  //   let isInvalid = false;
  //   for (const i in validateForm.controls) {
  //     if (validateForm.controls.hasOwnProperty(i) && !validateForm.controls[i].disabled) {
  //       validateForm.controls[i].markAsDirty();
  //       validateForm.controls[i].updateValueAndValidity();
  //
  //       // async validator 状态为pending
  //       if (validateForm.controls[i].status == 'INVALID') {
  //         isInvalid = true;
  //       }
  //
  //       if (validateForm.controls[i] instanceof FormArray) {
  //         for (const control of (<FormArray>validateForm.controls[i]).controls) {
  //           if (this.formIsInvalid(<FormGroup>control)) {
  //             isInvalid = true;
  //           }
  //         }
  //       }
  //     }
  //   }
  //   return isInvalid;
  // }

  // markFormDirty(validateForm: FormGroup) {
  //   for (const i in validateForm.controls) {
  //     if (validateForm.controls.hasOwnProperty(i) && !validateForm.controls[i].disabled) {
  //       validateForm.controls[i].markAsDirty();
  //       validateForm.controls[i].updateValueAndValidity();
  //       if (validateForm.controls[i] instanceof FormArray) {
  //         for (const control of (<FormArray>validateForm.controls[i]).controls) {
  //           this.markFormDirty(<FormGroup>control);
  //         }
  //       }
  //     }
  //   }
  // }

  /**
   * 去掉值为null的键值对
   * @param {FormGroup} validateForm
   * @returns {{}}
   */
  // formSerialized(validateForm: FormGroup) {
  //   let targetForm = {};
  //   for (const i in validateForm.controls) {
  //     if (
  //       validateForm.controls.hasOwnProperty(i) &&
  //       !validateForm.controls[i].disabled &&
  //       validateForm.controls[i].value !== null &&
  //       validateForm.controls[i].value !== void 0
  //     ) {
  //       Reflect.defineProperty(targetForm, i, {
  //         configurable: true,
  //         enumerable: true,
  //         writable: true,
  //         value: validateForm.controls[i].value,
  //       });
  //     }
  //   }
  //   return targetForm;
  // }

  /**
   * 加法函数，用于计算浮点数精度计算有误
   * @param arg1
   * @param arg2
   */
  // accAdd(arg1: number, arg2: number) {
  //   let r1, r2, m, c;
  //   try {
  //     r1 = arg1.toString().split('.')[1].length;
  //   } catch (e) {
  //     r1 = 0;
  //   }
  //   try {
  //     r2 = arg2.toString().split('.')[1].length;
  //   } catch (e) {
  //     r2 = 0;
  //   }
  //   c = Math.abs(r1 - r2);
  //   m = Math.pow(10, Math.max(r1, r2));
  //   if (c > 0) {
  //     let cm = Math.pow(10, c);
  //     if (r1 > r2) {
  //       arg1 = Number(arg1.toString().replace('.', ''));
  //       arg2 = Number(arg2.toString().replace('.', '')) * cm;
  //     } else {
  //       arg1 = Number(arg1.toString().replace('.', '')) * cm;
  //       arg2 = Number(arg2.toString().replace('.', ''));
  //     }
  //   } else {
  //     arg1 = Number(arg1.toString().replace('.', ''));
  //     arg2 = Number(arg2.toString().replace('.', ''));
  //   }
  //   return (arg1 + arg2) / m;
  // }

  /**
   * 减法函数，用于计算浮点数精度计算有误
   * @param arg1
   * @param arg2
   */
  // accSub(arg1: number, arg2: number) {
  //   let r1, r2, m, n;
  //   try {
  //     r1 = arg1.toString().split('.')[1].length;
  //   } catch (e) {
  //     r1 = 0;
  //   }
  //   try {
  //     r2 = arg2.toString().split('.')[1].length;
  //   } catch (e) {
  //     r2 = 0;
  //   }
  //   m = Math.pow(10, Math.max(r1, r2));
  //   n = r1 >= r2 ? r1 : r2;
  //   return ((arg1 * m - arg2 * m) / m).toFixed(n);
  // }

  /**
   * 乘法函数，用于计算浮点数精度计算有误
   * @param arg1
   * @param arg2
   */
  // accMul(arg1: number, arg2: number) {
  //   let m = 0,
  //     s1 = arg1.toString(),
  //     s2 = arg2.toString();
  //   try {
  //     m += s1.split('.')[1].length;
  //   } catch (e) {}
  //   try {
  //     m += s2.split('.')[1].length;
  //   } catch (e) {}
  //   return (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) / Math.pow(10, m);
  // }

  /**
   * 除法
   * @param arg1
   * @param arg2
   */
  // accDiv(arg1: number, arg2: number) {
  //   let t1 = 0,
  //     t2 = 0,
  //     r1,
  //     r2;
  //   try {
  //     t1 = arg1.toString().split('.')[1].length;
  //   } catch (e) {}
  //   try {
  //     t2 = arg2.toString().split('.')[1].length;
  //   } catch (e) {}
  //   // with (Math) {
  //   //
  //   // }
  //   r1 = Number(arg1.toString().replace('.', ''));
  //   r2 = Number(arg2.toString().replace('.', ''));
  //   return (r1 / r2) * Math.pow(10, t2 - t1);
  // }

  /**
   * 三级款式分类的必填校验
   * @param control
   */
  // materialValidator(control: AbstractControl): ValidationErrors | null {
  //   /**
  //    * 如果有一项为''或null，则返回{'required': true}
  //    */
  //   if (control.value && (control.value as Array<string | null>).some((v) => !v)) {
  //     return { required: true };
  //   } else {
  //     return null;
  //   }
  // }
}
