<ng-template #title_tpl>
  <div class="header-title">
    <div class="left">{{ 'plan.productionPlan.订单条颜色设置' | translate }}</div>
    <div class="right">
      <ng-container *ngIf="!editable; else otherTemp">
        <button nz-button (click)="editClick()" [nzType]="'primary'">
          <i nz-icon nzType="edit" nzTheme="outline"></i>{{ 'btn.edit' | translate }}
        </button>
      </ng-container>
      <ng-template #otherTemp>
        <button (click)="clearAll()" nz-button>{{ 'plan.productionPlan.全部清空' | translate }}</button>
        <div class="line"></div>
        <button (click)="cancel()" nz-button>{{ 'btn.cancel' | translate }}</button>
        <button (click)="save()" nz-button [nzType]="'primary'" [disabled]="loading">
          <i nz-icon style="margin-right: 5px">
            <svg>
              <path
                d="M847.9 97H176c-43.7 0-79 35.4-79 79v671.9c0 43.7 35.4 79 79 79h671.9c43.7 0 79-35.4 79-79V176.1c0.1-43.7-35.3-79.1-79-79.1z m-147.3 75.5v264.1H323.4V172.5h377.2z m150.9 675.4c0 2-1.6 3.6-3.6 3.6H176.1c-2 0-3.6-1.6-3.6-3.6V176.1c0-2 1.6-3.6 3.6-3.6H248v264.1c0 41.7 33.8 75.5 75.5 75.5h377.3c41.7 0 75.5-33.8 75.5-75.5V172.5h71.9c2 0 3.6 1.6 3.6 3.6v671.8z"></path>
              <path d="M587.5 304.5m-37.7 0a37.7 37.7 0 1 0 75.4 0 37.7 37.7 0 1 0-75.4 0Z"></path>
            </svg>
          </i>
          {{ 'btn.save' | translate }}
        </button>
      </ng-template>
    </div>
  </div>
</ng-template>

<ng-template #content_tpl>
  <div class="example-boundary" style="overflow: hidden; display: flex; flex-direction: column; height: 100%; position: relative">
    <div *ngIf="colorPaletteVisible && editable" class="drag-color-box" cdkDragBoundary=".example-boundary" cdkDrag>
      <div class="header">
        <div>
          <i nz-icon class="icon iconfont iconshouzhi">
            <svg class="icon">
              <path
                d="M833.41189576 341.93766276h-3.5472107c-17.64953613 0-33.56872559 5.27755738-47.67105103 14.10232545-12.02590943-37.63504029-46.97891236-63.33068848-86.51733399-63.41720582-17.64953613 0-35.29907227 5.27755738-51.13174438 14.10232545-12.02590943-37.63504029-46.97891236-63.33068848-86.51733398-63.41720582-15.91918945 0-31.83837891 3.46069336-44.12384033 10.55511475V158.78046671C513.81686402 106.00489298 474.97058105 63.69791667 421.93545532 63.69791667c-52.9486084 0-95.34210205 42.30697632-95.34210204 95.08255005v366.31439208l-58.31268311-58.05313109c-37.02941895-37.02941895-102.43652344-31.66534424-134.18838501 0-31.75186158 31.66534424-52.9486084 95.16906738-7.00790406 140.93673706l264.82955933 264.13742065c5.36407471 5.27755738 12.37197876 10.55511475 17.64953613 14.10232544 49.40139771 38.75976563 104.25338746 63.33068848 220.705719 63.33068847 268.37677002 0 293.12072754-144.39743042 293.12072753-324.09393309V437.10673015c0.08651734-52.77557373-38.75976563-95.16906738-89.97802733-95.16906739m40.57662963 283.6038208c0 151.57836914 0 272.96218873-241.90246582 272.96218873-102.35000611 0-164.2098999-22.92709351-210.15060425-68.60824585L169.47787475 579.77381389c-23.01361084-22.92709351-15.91918945-51.13174439 1.73034669-68.69476319 17.64953613-17.64953613 51.13174439-19.37988281 68.86779785-1.81686401 0 0 44.12384033 44.037323 82.97012329 81.06674194l54.76547242 54.59243775V167.69175212c0-24.65744019 19.46640015-44.037323 45.94070434-44.037323 24.65744019 0 42.39349365 19.37988281 42.39349365 44.037323v308.17474365c0 14.10232544 10.55511475 24.65744019 24.65744019 24.65744019s24.65744019-10.55511475 24.65744018-24.65744019V345.57139079c0-24.65744019 17.64953613-44.037323 42.48001099-44.037323 0 0 44.12384033 0 44.12384033 44.037323v169.05487061c0 14.10232544 10.55511475 24.65744019 24.65744018 24.65744019s24.65744019-10.55511475 24.65744019-24.65744019V394.88627116c0-24.65744019 17.64953613-44.037323 42.48001099-44.037323 0 0 44.12384033 3.46069336 44.12384033 44.037323v149.6749878c0 14.01580811 10.64163209 24.65744019 24.65744019 24.65744019 14.18884277 0 24.74395752-10.64163209 24.74395752-24.65744019V440.65394084c0-24.65744019 19.46640015-44.12384033 44.12384033-44.12384033 0 0 44.12384033-1.73034668 44.12384033 44.12384033l-1.64382935 184.88754272"></path>
            </svg>
          </i>
          <span>
            <!-- 点击颜色/颜色组至对应分类中 -->
            {{ 'plan.productionPlan.clickColorIntoCategory' | translate }}
          </span>
        </div>
        <i class="icon iconfont icon-cuohao" nzTooltipTitle="关闭" nz-tooltip (click)="closeColorPalette()"></i>
      </div>
      <div class="body">
        <div class="top-area">
          <div class="left">
            <span class="title">{{ 'plan.productionPlan.默认颜色' | translate }}</span>
            <span class="icon iconfont icon-yiwen" [nzTooltipTitle]="titleTemplate" nz-tooltip></span>
            <ng-template #titleTemplate>
              <div class="color-tooltip">
                <p>
                  <!-- 颜色：以单个颜色进行设置 -->
                  <span class="color-word">{{ 'plan.productionPlan.颜色' | translate }}</span>
                  ：{{ 'plan.productionPlan.colorDescription' | translate }}
                </p>
                <p>
                  <!-- 颜色组：以组为单位批量设置 -->
                  <span class="color-word">{{ 'plan.productionPlan.颜色组' | translate }}</span>
                  ：{{ 'plan.productionPlan.colorGroupDescription' | translate }}
                </p>
              </div>
            </ng-template>
          </div>
          <div class="right">
            <nz-radio-group [(ngModel)]="radioValue" [nzSize]="'small'" (ngModelChange)="toggleColorCheckedMode()">
              <label nz-radio nzValue="color">{{ 'plan.productionPlan.颜色' | translate }}</label>
              <label nz-radio nzValue="colorGrounp">{{ 'plan.productionPlan.颜色组' | translate }}</label>
            </nz-radio-group>
          </div>
        </div>
        <div class="middle-area">
          <div class="default-color-box">
            <ng-container *ngFor="let item of defaultColor; index as i">
              <div class="color-grounp" [ngClass]="{ 'color-grounp-active': radioValue === 'colorGrounp' }">
                <div style="border: 1px solid #fff" class="source-active" [ngClass]="{ 'source-active': radioValue === 'color' }">
                  <div
                    class="color-item"
                    (click)="colorSourceClick(i, 'simple_color')"
                    [ngStyle]="{ 'background-color': item.simple_color.color }"></div>
                </div>
                <div style="border: 1px solid #fff" [ngClass]="{ 'source-active': radioValue === 'color' }">
                  <div
                    class="color-item"
                    (click)="colorSourceClick(i, 'general_color')"
                    [ngStyle]="{ 'background-color': item.general_color.color }"></div>
                </div>
                <div style="border: 1px solid #fff" [ngClass]="{ 'source-active': radioValue === 'color' }">
                  <div
                    class="color-item"
                    (click)="colorSourceClick(i, 'difficult_color')"
                    [ngStyle]="{ 'background-color': item.difficult_color.color }"></div>
                </div>
                <div style="border: 1px solid #fff" [ngClass]="{ 'source-active': radioValue === 'color' }">
                  <div
                    class="color-item"
                    (click)="colorSourceClick(i, 'extremely_difficult_color')"
                    [ngStyle]="{ 'background-color': item.extremely_difficult_color.color }"></div>
                </div>
              </div>
            </ng-container>
          </div>
          <div class="custom-color">
            <span class="custom-color-title">{{ 'plan.productionPlan.最近自定义颜色' | translate }}</span>
            <div class="custom-color-grounp">
              <div class="color-item-box source-active" *ngFor="let item of customColor; index as i" (click)="customColorClick(i)">
                <div class="color-item" [ngStyle]="{ 'background-color': item.color }"></div>
              </div>
            </div>
          </div>
          <div class="color-picker">
            <span class="color-picker-title">{{ 'plan.productionPlan.颜色' | translate }}</span>
            <div
              class="box"
              [nzTooltipTitle]="'plan.productionPlan.点击定义颜色' | translate"
              nz-tooltip
              nz-popover
              (nzPopoverVisibleChange)="addCustomColor($event)"
              nzPopoverTrigger="click"
              nzPopoverOverlayClassName="graph-color"
              [nzPopoverContent]="contentTemplate">
              <div class="color-picker-area" [ngStyle]="{ 'background-color': checkColor }"></div>
              <i nz-icon class="iconfont iconyanse">
                <svg>
                  <path
                    d="M473.96176455 935.58235273C306.85294092 935.58235273 108.3235291 808.55 69.59705908 573.17352911c-28.53529453-173.38235273 30.75882364-315.15882364 171.50294092-410.02941182C316.96470547 111.9764709 398.81176455 86.06176455 484.39117637 86.06176455c261.21176455 0 475.49117636 241.54411729 477 399.1764709 0.60882364 64.32352911-19.90588271 120.78529453-57.70588184 158.98235273-33.14117636 33.45882363-76.71176455 51.08823545-126 51.08823545a219.46764727 219.46764727 0 0 1-50.50588271-6.06176455c-37.21764727-8.73529453-64.77352911-4.65882363-78.88235274 12.15-11.32941182 13.5-12.6264709 32.37352911-11.32941181 39.28235274 10.45588271 61.06764727 1.69411729 109.66764727-26.1 144.29117636-32.05588271 40.07647089-78.53823545 46.16470547-88.38529454 47.03823545a327.97058818 327.97058818 0 0 1-48.52058818 3.5735291z m6.3264709-796.71176455c-74.03823545 0-145.24411729 22.65882363-211.57941182 67.44705909C145.93823545 289.06470547 96.41176455 408.10294092 121.4 560.1235291c33.67058818 204.59117636 204.67058818 315 348.51176455 315.00000001 13.92352911 0 27.74117637-0.97941182 41.13529453-2.99117638l2.75294092-0.26470546c0.05294092-0.05294092 30.25588271-2.38235273 49.26176455-26.62941182 16.96764727-21.52058818 21.65294092-54.74117637 14.00294092-98.78823545-3.97058818-23.58529453 1.8-59.02941182 23.8764709-85.31470547 14.10882364-16.75588271 40.73823545-36.71470547 87.22058818-36.71470635 14.74411729 0 30.81176455 2.01176455 47.83235274 6.06176455 48.70588271 11.38235273 93.17647089 0.60882364 123.3-29.88529365 27.13235273-27.39705908 41.8764709-69.51176455 41.4-118.64117637-1.24411729-132.3264709-194.13529453-343.08529453-420.40588184-343.08529453z"></path>
                  <path
                    d="M277.28529453 408.42058818m-52.94117724 0a52.94117637 52.94117637 0 1 0 105.8823536 0 52.94117637 52.94117637 0 1 0-105.8823536 0Z"></path>
                  <path
                    d="M402.30588271 305.74117637m-52.94117724 0a52.94117637 52.94117637 0 1 0 105.88235361 0 52.94117637 52.94117637 0 1 0-105.88235361 0Z"></path>
                  <path
                    d="M562.29411729 305.74117637m-52.94117637 0a52.94117637 52.94117637 0 1 0 105.88235361 0 52.94117637 52.94117637 0 1 0-105.88235361 0Z"></path>
                  <path
                    d="M708.2 368.26470547m-52.94117637 0a52.94117637 52.94117637 0 1 0 105.88235274 0 52.94117637 52.94117637 0 1 0-105.88235274 0Z"></path>
                  <path
                    d="M277.28529453 573.30588271m-52.94117724 0a52.94117637 52.94117637 0 1 0 105.8823536 0 52.94117637 52.94117637 0 1 0-105.8823536 0Z"></path>
                </svg>
              </i>
            </div>
            <ng-template #contentTemplate>
              <compact-picker [control]="compactControl"></compact-picker>
            </ng-template>
          </div>
        </div>
      </div>
      <div class="drag-area" [nzTooltipTitle]="'plan.productionPlan.点击推拽组件' | translate" nz-tooltip>
        <i nz-icon nzType="drag" nzTheme="outline"></i>
      </div>
    </div>
    <div nz-row class="serachWrap">
      <div nz-col [nzSpan]="14">
        <div nz-row nzType="flex">
          <div nz-col [nzSpan]="8">
            <nz-form-item>
              <nz-form-label nzSpan="8">{{ 'plan.productionPlan.一级分类' | translate }}</nz-form-label>
              <nz-form-control nzSpan="16">
                <nz-select
                  [nzDisabled]="editable ? true : false"
                  [(ngModel)]="queryConditions.first_style_id"
                  [nzPlaceHolder]="'placeholder.select' | translate"
                  style="width: 100%"
                  [nzDropdownMatchSelectWidth]="false"
                  [nzDropdownClassName]="'select-options'"
                  (ngModelChange)="firstMaterialChange()"
                  nzShowSearch
                  nzAllowClear>
                  <nz-option *ngFor="let item of first_material_options" [nzValue]="item.value" [nzLabel]="item.label"> </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-col [nzSpan]="8">
            <nz-form-item>
              <nz-form-label nzSpan="8">{{ 'plan.productionPlan.二级分类' | translate }}</nz-form-label>
              <nz-form-control nzSpan="16">
                <nz-select
                  [nzDisabled]="editable || !queryConditions.first_style_id ? true : false"
                  [(ngModel)]="queryConditions.second_style_id"
                  [nzPlaceHolder]="'placeholder.select' | translate"
                  [nzDropdownMatchSelectWidth]="false"
                  [nzDropdownClassName]="'select-options'"
                  (nzOpenChange)="secondMaterialOpen()"
                  (ngModelChange)="search()"
                  style="width: 100%"
                  nzShowSearch
                  nzAllowClear>
                  <nz-option *ngFor="let item of second_material_options" [nzValue]="item.value" [nzLabel]="item.label"></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-col [nzSpan]="8">
            <nz-form-item>
              <nz-form-label nzSpan="8">{{ 'plan.productionPlan.设置状态' | translate }}</nz-form-label>
              <nz-form-control nzSpan="16">
                <nz-select
                  [nzDisabled]="editable ? true : false"
                  [(ngModel)]="queryConditions.set_status"
                  [nzPlaceHolder]="'placeholder.select' | translate"
                  [nzDropdownMatchSelectWidth]="false"
                  [nzDropdownClassName]="'select-options'"
                  (ngModelChange)="search()"
                  style="width: 100%"
                  nzShowSearch
                  nzAllowClear>
                  <nz-option
                    *ngFor="let item of isSetOptions"
                    [nzValue]="item.value"
                    [nzLabel]="'plan.productionPlan.' + item.label | translate">
                  </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>
      </div>
    </div>
    <nz-spin [nzSpinning]="loading" style="overflow: hidden; height: 100%">
      <div class="serachContent">
        <ng-container *ngIf="this.colorDataList.length > 0; else noImage">
          <form nz-form *ngIf="validateForm" [formGroup]="validateForm">
            <div formArrayName="colorSetting">
              <ng-container *ngFor="let data of formArrayCtrl; index as i" [formGroupName]="i">
                <div class="card">
                  <div class="title">
                    <span class="left">{{ data.get('first_style_name')?.value }} / {{ data.get('second_style_name').value }}</span>
                  </div>
                  <div class="content">
                    <div class="wrap">
                      <div class="item">
                        <div class="left" *ngIf="!editable">
                          <span class="card-text">{{ 'plan.productionPlan.简单' | translate }}：</span>
                          <div style="border: 1px solid #fff">
                            <div
                              class="color-box"
                              [ngStyle]="{
                                background: data.get('simple_color').value ? data.get('simple_color').value : '#F1F2F5',
                                'border-width': data.get('simple_color').value ? '2px' : '0'
                              }">
                              <i
                                nz-icon
                                *ngIf="!editable && !data.get('simple_color').value"
                                class="iconfont iconyanse"
                                style="color: #ced3de">
                                <svg>
                                  <path
                                    d="M473.96176455 935.58235273C306.85294092 935.58235273 108.3235291 808.55 69.59705908 573.17352911c-28.53529453-173.38235273 30.75882364-315.15882364 171.50294092-410.02941182C316.96470547 111.9764709 398.81176455 86.06176455 484.39117637 86.06176455c261.21176455 0 475.49117636 241.54411729 477 399.1764709 0.60882364 64.32352911-19.90588271 120.78529453-57.70588184 158.98235273-33.14117636 33.45882363-76.71176455 51.08823545-126 51.08823545a219.46764727 219.46764727 0 0 1-50.50588271-6.06176455c-37.21764727-8.73529453-64.77352911-4.65882363-78.88235274 12.15-11.32941182 13.5-12.6264709 32.37352911-11.32941181 39.28235274 10.45588271 61.06764727 1.69411729 109.66764727-26.1 144.29117636-32.05588271 40.07647089-78.53823545 46.16470547-88.38529454 47.03823545a327.97058818 327.97058818 0 0 1-48.52058818 3.5735291z m6.3264709-796.71176455c-74.03823545 0-145.24411729 22.65882363-211.57941182 67.44705909C145.93823545 289.06470547 96.41176455 408.10294092 121.4 560.1235291c33.67058818 204.59117636 204.67058818 315 348.51176455 315.00000001 13.92352911 0 27.74117637-0.97941182 41.13529453-2.99117638l2.75294092-0.26470546c0.05294092-0.05294092 30.25588271-2.38235273 49.26176455-26.62941182 16.96764727-21.52058818 21.65294092-54.74117637 14.00294092-98.78823545-3.97058818-23.58529453 1.8-59.02941182 23.8764709-85.31470547 14.10882364-16.75588271 40.73823545-36.71470547 87.22058818-36.71470635 14.74411729 0 30.81176455 2.01176455 47.83235274 6.06176455 48.70588271 11.38235273 93.17647089 0.60882364 123.3-29.88529365 27.13235273-27.39705908 41.8764709-69.51176455 41.4-118.64117637-1.24411729-132.3264709-194.13529453-343.08529453-420.40588184-343.08529453z"></path>
                                  <path
                                    d="M277.28529453 408.42058818m-52.94117724 0a52.94117637 52.94117637 0 1 0 105.8823536 0 52.94117637 52.94117637 0 1 0-105.8823536 0Z"></path>
                                  <path
                                    d="M402.30588271 305.74117637m-52.94117724 0a52.94117637 52.94117637 0 1 0 105.88235361 0 52.94117637 52.94117637 0 1 0-105.88235361 0Z"></path>
                                  <path
                                    d="M562.29411729 305.74117637m-52.94117637 0a52.94117637 52.94117637 0 1 0 105.88235361 0 52.94117637 52.94117637 0 1 0-105.88235361 0Z"></path>
                                  <path
                                    d="M708.2 368.26470547m-52.94117637 0a52.94117637 52.94117637 0 1 0 105.88235274 0 52.94117637 52.94117637 0 1 0-105.88235274 0Z"></path>
                                  <path
                                    d="M277.28529453 573.30588271m-52.94117724 0a52.94117637 52.94117637 0 1 0 105.8823536 0 52.94117637 52.94117637 0 1 0-105.8823536 0Z"></path>
                                </svg>
                              </i>
                            </div>
                          </div>
                        </div>
                        <div
                          *ngIf="editable"
                          class="left"
                          [nzTooltipTitle]="!showTip ? toolTipTitle : null"
                          [nzTooltipColor]="'#2996FF'"
                          nz-tooltip
                          [nzTooltipTrigger]="tooltipTrigger"
                          (nzTooltipVisibleChange)="tooltipVisibleChange($event)"
                          [nzTooltipVisible]="!showTip && i === 0 && j === 0 && this.tooltipTrigger ? true : false">
                          <ng-template #toolTipTitle>
                            {{ 'plan.productionPlan.点击' | translate }}
                            <i nz-icon class="icon iconfont iconshouzhi" style="color: #fff">
                              <svg class="icon">
                                <path
                                  d="M833.41189576 341.93766276h-3.5472107c-17.64953613 0-33.56872559 5.27755738-47.67105103 14.10232545-12.02590943-37.63504029-46.97891236-63.33068848-86.51733399-63.41720582-17.64953613 0-35.29907227 5.27755738-51.13174438 14.10232545-12.02590943-37.63504029-46.97891236-63.33068848-86.51733398-63.41720582-15.91918945 0-31.83837891 3.46069336-44.12384033 10.55511475V158.78046671C513.81686402 106.00489298 474.97058105 63.69791667 421.93545532 63.69791667c-52.9486084 0-95.34210205 42.30697632-95.34210204 95.08255005v366.31439208l-58.31268311-58.05313109c-37.02941895-37.02941895-102.43652344-31.66534424-134.18838501 0-31.75186158 31.66534424-52.9486084 95.16906738-7.00790406 140.93673706l264.82955933 264.13742065c5.36407471 5.27755738 12.37197876 10.55511475 17.64953613 14.10232544 49.40139771 38.75976563 104.25338746 63.33068848 220.705719 63.33068847 268.37677002 0 293.12072754-144.39743042 293.12072753-324.09393309V437.10673015c0.08651734-52.77557373-38.75976563-95.16906738-89.97802733-95.16906739m40.57662963 283.6038208c0 151.57836914 0 272.96218873-241.90246582 272.96218873-102.35000611 0-164.2098999-22.92709351-210.15060425-68.60824585L169.47787475 579.77381389c-23.01361084-22.92709351-15.91918945-51.13174439 1.73034669-68.69476319 17.64953613-17.64953613 51.13174439-19.37988281 68.86779785-1.81686401 0 0 44.12384033 44.037323 82.97012329 81.06674194l54.76547242 54.59243775V167.69175212c0-24.65744019 19.46640015-44.037323 45.94070434-44.037323 24.65744019 0 42.39349365 19.37988281 42.39349365 44.037323v308.17474365c0 14.10232544 10.55511475 24.65744019 24.65744019 24.65744019s24.65744019-10.55511475 24.65744018-24.65744019V345.57139079c0-24.65744019 17.64953613-44.037323 42.48001099-44.037323 0 0 44.12384033 0 44.12384033 44.037323v169.05487061c0 14.10232544 10.55511475 24.65744019 24.65744018 24.65744019s24.65744019-10.55511475 24.65744019-24.65744019V394.88627116c0-24.65744019 17.64953613-44.037323 42.48001099-44.037323 0 0 44.12384033 3.46069336 44.12384033 44.037323v149.6749878c0 14.01580811 10.64163209 24.65744019 24.65744019 24.65744019 14.18884277 0 24.74395752-10.64163209 24.74395752-24.65744019V440.65394084c0-24.65744019 19.46640015-44.12384033 44.12384033-44.12384033 0 0 44.12384033-1.73034668 44.12384033 44.12384033l-1.64382935 184.88754272"></path>
                              </svg>
                            </i>
                            {{ 'plan.productionPlan.可设置颜色哦' | translate }}
                          </ng-template>
                          <span class="card-text">{{ 'plan.productionPlan.简单' | translate }}：</span>
                          <div
                            class="edit-color-box"
                            [nzTooltipTitle]="
                              (!data.get('simple_color').value
                                ? 'plan.productionPlan.点击可设置颜色'
                                : 'plan.productionPlan.点击可替换颜色'
                              ) | translate
                            "
                            nz-tooltip
                            style="border: 1px solid #fff"
                            [ngClass]="{
                              active: data.get('color_checked1').value,
                              error: isCommit && data.get('simple_color').status === 'INVALID' && data.get('isValidator').value
                            }">
                            <span
                              *ngIf="data.get('simple_color').value"
                              class="icon iconfont icon-cuowu"
                              (click)="deleteColor(data, 1)"></span>
                            <div
                              class="color-box"
                              [ngClass]="{
                                'background-hover': !data.get('simple_color').value
                              }"
                              (click)="checkTargetColorClick(data, 1)"
                              [ngStyle]="{
                                background: data.get('simple_color').value
                                  ? data.get('simple_color').value
                                  : data.get('color_checked1').value
                                  ? '#EDF6FF'
                                  : isCommit && data.get('simple_color').status === 'INVALID' && data.get('isValidator').value
                                  ? '#FFECEC'
                                  : '#f1f2f5',
                                'border-width': data.get('simple_color').value ? '2px' : '0'
                              }">
                              <i
                                *ngIf="editable && !data.get('simple_color').value"
                                nz-icon
                                class="icon iconfont iconshouzhi"
                                style="color: #ced3de">
                                <svg class="icon">
                                  <path
                                    d="M833.41189576 341.93766276h-3.5472107c-17.64953613 0-33.56872559 5.27755738-47.67105103 14.10232545-12.02590943-37.63504029-46.97891236-63.33068848-86.51733399-63.41720582-17.64953613 0-35.29907227 5.27755738-51.13174438 14.10232545-12.02590943-37.63504029-46.97891236-63.33068848-86.51733398-63.41720582-15.91918945 0-31.83837891 3.46069336-44.12384033 10.55511475V158.78046671C513.81686402 106.00489298 474.97058105 63.69791667 421.93545532 63.69791667c-52.9486084 0-95.34210205 42.30697632-95.34210204 95.08255005v366.31439208l-58.31268311-58.05313109c-37.02941895-37.02941895-102.43652344-31.66534424-134.18838501 0-31.75186158 31.66534424-52.9486084 95.16906738-7.00790406 140.93673706l264.82955933 264.13742065c5.36407471 5.27755738 12.37197876 10.55511475 17.64953613 14.10232544 49.40139771 38.75976563 104.25338746 63.33068848 220.705719 63.33068847 268.37677002 0 293.12072754-144.39743042 293.12072753-324.09393309V437.10673015c0.08651734-52.77557373-38.75976563-95.16906738-89.97802733-95.16906739m40.57662963 283.6038208c0 151.57836914 0 272.96218873-241.90246582 272.96218873-102.35000611 0-164.2098999-22.92709351-210.15060425-68.60824585L169.47787475 579.77381389c-23.01361084-22.92709351-15.91918945-51.13174439 1.73034669-68.69476319 17.64953613-17.64953613 51.13174439-19.37988281 68.86779785-1.81686401 0 0 44.12384033 44.037323 82.97012329 81.06674194l54.76547242 54.59243775V167.69175212c0-24.65744019 19.46640015-44.037323 45.94070434-44.037323 24.65744019 0 42.39349365 19.37988281 42.39349365 44.037323v308.17474365c0 14.10232544 10.55511475 24.65744019 24.65744019 24.65744019s24.65744019-10.55511475 24.65744018-24.65744019V345.57139079c0-24.65744019 17.64953613-44.037323 42.48001099-44.037323 0 0 44.12384033 0 44.12384033 44.037323v169.05487061c0 14.10232544 10.55511475 24.65744019 24.65744018 24.65744019s24.65744019-10.55511475 24.65744019-24.65744019V394.88627116c0-24.65744019 17.64953613-44.037323 42.48001099-44.037323 0 0 44.12384033 3.46069336 44.12384033 44.037323v149.6749878c0 14.01580811 10.64163209 24.65744019 24.65744019 24.65744019 14.18884277 0 24.74395752-10.64163209 24.74395752-24.65744019V440.65394084c0-24.65744019 19.46640015-44.12384033 44.12384033-44.12384033 0 0 44.12384033-1.73034668 44.12384033 44.12384033l-1.64382935 184.88754272"></path>
                                </svg>
                              </i>
                            </div>
                          </div>
                        </div>
                        <span class="right" *ngIf="!editable && data.get('simple').value"
                          ><span class="card-text" style="font-size: 16px">≤</span> {{ data.get('simple').value }}
                          {{ 'plan.productionPlan.分钟' | translate }}</span
                        >
                        <span *ngIf="!editable && !data.get('simple').value" class="right-no-data">{{
                          'plan.productionPlan.暂无数据' | translate
                        }}</span>
                        <div *ngIf="editable" style="height: 32px">
                          <nz-form-item>
                            <nz-form-control>
                              <span class="card-text" style="margin-right: 10px; font-size: 16px">≤</span>
                              <nz-input-number
                                style="width: 50px"
                                [nzSize]="'small'"
                                (nzFocus)="samChange()"
                                formControlName="simple"
                                [nzMin]="0.1"
                                [nzMax]="999999999.9"
                                [nzStep]="0.1"
                                [nzPrecision]="1">
                              </nz-input-number>
                              <span class="card-text" style="margin-left: 2px">{{ 'plan.productionPlan.分钟' | translate }}</span>
                            </nz-form-control>
                          </nz-form-item>
                        </div>
                      </div>
                      <div class="item">
                        <span class="left">
                          <span class="card-text">{{ 'plan.productionPlan.一般' | translate }}：</span>
                          <ng-container *ngIf="!editable; else editTemp2">
                            <div style="border: 1px solid #fff" [ngClass]="{ active: data.get('color_checked2').value }">
                              <div
                                class="color-box"
                                [ngStyle]="{
                                  background: data.get('general_color').value ? data.get('general_color').value : '#F1F2F5',
                                  'border-width': data.get('general_color').value ? '2px' : '0'
                                }">
                                <i nz-icon *ngIf="!data.get('general_color').value" class="iconfont iconyanse" style="color: #ced3de">
                                  <svg>
                                    <path
                                      d="M473.96176455 935.58235273C306.85294092 935.58235273 108.3235291 808.55 69.59705908 573.17352911c-28.53529453-173.38235273 30.75882364-315.15882364 171.50294092-410.02941182C316.96470547 111.9764709 398.81176455 86.06176455 484.39117637 86.06176455c261.21176455 0 475.49117636 241.54411729 477 399.1764709 0.60882364 64.32352911-19.90588271 120.78529453-57.70588184 158.98235273-33.14117636 33.45882363-76.71176455 51.08823545-126 51.08823545a219.46764727 219.46764727 0 0 1-50.50588271-6.06176455c-37.21764727-8.73529453-64.77352911-4.65882363-78.88235274 12.15-11.32941182 13.5-12.6264709 32.37352911-11.32941181 39.28235274 10.45588271 61.06764727 1.69411729 109.66764727-26.1 144.29117636-32.05588271 40.07647089-78.53823545 46.16470547-88.38529454 47.03823545a327.97058818 327.97058818 0 0 1-48.52058818 3.5735291z m6.3264709-796.71176455c-74.03823545 0-145.24411729 22.65882363-211.57941182 67.44705909C145.93823545 289.06470547 96.41176455 408.10294092 121.4 560.1235291c33.67058818 204.59117636 204.67058818 315 348.51176455 315.00000001 13.92352911 0 27.74117637-0.97941182 41.13529453-2.99117638l2.75294092-0.26470546c0.05294092-0.05294092 30.25588271-2.38235273 49.26176455-26.62941182 16.96764727-21.52058818 21.65294092-54.74117637 14.00294092-98.78823545-3.97058818-23.58529453 1.8-59.02941182 23.8764709-85.31470547 14.10882364-16.75588271 40.73823545-36.71470547 87.22058818-36.71470635 14.74411729 0 30.81176455 2.01176455 47.83235274 6.06176455 48.70588271 11.38235273 93.17647089 0.60882364 123.3-29.88529365 27.13235273-27.39705908 41.8764709-69.51176455 41.4-118.64117637-1.24411729-132.3264709-194.13529453-343.08529453-420.40588184-343.08529453z"></path>
                                    <path
                                      d="M277.28529453 408.42058818m-52.94117724 0a52.94117637 52.94117637 0 1 0 105.8823536 0 52.94117637 52.94117637 0 1 0-105.8823536 0Z"></path>
                                    <path
                                      d="M402.30588271 305.74117637m-52.94117724 0a52.94117637 52.94117637 0 1 0 105.88235361 0 52.94117637 52.94117637 0 1 0-105.88235361 0Z"></path>
                                    <path
                                      d="M562.29411729 305.74117637m-52.94117637 0a52.94117637 52.94117637 0 1 0 105.88235361 0 52.94117637 52.94117637 0 1 0-105.88235361 0Z"></path>
                                    <path
                                      d="M708.2 368.26470547m-52.94117637 0a52.94117637 52.94117637 0 1 0 105.88235274 0 52.94117637 52.94117637 0 1 0-105.88235274 0Z"></path>
                                    <path
                                      d="M277.28529453 573.30588271m-52.94117724 0a52.94117637 52.94117637 0 1 0 105.8823536 0 52.94117637 52.94117637 0 1 0-105.8823536 0Z"></path>
                                  </svg>
                                </i>
                              </div>
                            </div>
                          </ng-container>
                          <ng-template #editTemp2>
                            <div
                              style="border: 1px solid #fff"
                              class="edit-color-box"
                              [nzTooltipTitle]="
                                (!data.get('general_color').value
                                  ? 'plan.productionPlan.点击可设置颜色'
                                  : 'plan.productionPlan.点击可替换颜色'
                                ) | translate
                              "
                              nz-tooltip
                              [ngClass]="{
                                active: data.get('color_checked2').value,
                                error: isCommit && data.get('general_color').status === 'INVALID' && data.get('isValidator').value
                              }">
                              <span
                                *ngIf="data.get('general_color').value"
                                class="icon iconfont icon-cuowu"
                                (click)="deleteColor(data, 2)"></span>
                              <div
                                class="color-box"
                                [ngClass]="{
                                  'background-hover': !data.get('general_color').value
                                }"
                                (click)="checkTargetColorClick(data, 2)"
                                [ngStyle]="{
                                  background: data.get('general_color').value
                                    ? data.get('general_color').value
                                    : data.get('color_checked2').value
                                    ? '#EDF6FF'
                                    : isCommit && data.get('simple_color').status === 'INVALID' && data.get('isValidator').value
                                    ? '#FFECEC'
                                    : '#f1f2f5',
                                  'border-width': data.get('general_color').value ? '2px' : '0'
                                }">
                                <i
                                  *ngIf="editable && !data.get('general_color').value"
                                  nz-icon
                                  class="icon iconfont iconshouzhi"
                                  style="color: #ced3de">
                                  <svg class="icon">
                                    <path
                                      d="M833.41189576 341.93766276h-3.5472107c-17.64953613 0-33.56872559 5.27755738-47.67105103 14.10232545-12.02590943-37.63504029-46.97891236-63.33068848-86.51733399-63.41720582-17.64953613 0-35.29907227 5.27755738-51.13174438 14.10232545-12.02590943-37.63504029-46.97891236-63.33068848-86.51733398-63.41720582-15.91918945 0-31.83837891 3.46069336-44.12384033 10.55511475V158.78046671C513.81686402 106.00489298 474.97058105 63.69791667 421.93545532 63.69791667c-52.9486084 0-95.34210205 42.30697632-95.34210204 95.08255005v366.31439208l-58.31268311-58.05313109c-37.02941895-37.02941895-102.43652344-31.66534424-134.18838501 0-31.75186158 31.66534424-52.9486084 95.16906738-7.00790406 140.93673706l264.82955933 264.13742065c5.36407471 5.27755738 12.37197876 10.55511475 17.64953613 14.10232544 49.40139771 38.75976563 104.25338746 63.33068848 220.705719 63.33068847 268.37677002 0 293.12072754-144.39743042 293.12072753-324.09393309V437.10673015c0.08651734-52.77557373-38.75976563-95.16906738-89.97802733-95.16906739m40.57662963 283.6038208c0 151.57836914 0 272.96218873-241.90246582 272.96218873-102.35000611 0-164.2098999-22.92709351-210.15060425-68.60824585L169.47787475 579.77381389c-23.01361084-22.92709351-15.91918945-51.13174439 1.73034669-68.69476319 17.64953613-17.64953613 51.13174439-19.37988281 68.86779785-1.81686401 0 0 44.12384033 44.037323 82.97012329 81.06674194l54.76547242 54.59243775V167.69175212c0-24.65744019 19.46640015-44.037323 45.94070434-44.037323 24.65744019 0 42.39349365 19.37988281 42.39349365 44.037323v308.17474365c0 14.10232544 10.55511475 24.65744019 24.65744019 24.65744019s24.65744019-10.55511475 24.65744018-24.65744019V345.57139079c0-24.65744019 17.64953613-44.037323 42.48001099-44.037323 0 0 44.12384033 0 44.12384033 44.037323v169.05487061c0 14.10232544 10.55511475 24.65744019 24.65744018 24.65744019s24.65744019-10.55511475 24.65744019-24.65744019V394.88627116c0-24.65744019 17.64953613-44.037323 42.48001099-44.037323 0 0 44.12384033 3.46069336 44.12384033 44.037323v149.6749878c0 14.01580811 10.64163209 24.65744019 24.65744019 24.65744019 14.18884277 0 24.74395752-10.64163209 24.74395752-24.65744019V440.65394084c0-24.65744019 19.46640015-44.12384033 44.12384033-44.12384033 0 0 44.12384033-1.73034668 44.12384033 44.12384033l-1.64382935 184.88754272"></path>
                                  </svg>
                                </i>
                              </div>
                            </div>
                          </ng-template>
                        </span>
                        <span class="right" *ngIf="!editable && data.get('general').value">
                          {{ data.get('simple').value }} - {{ data.get('general').value }}
                          {{ 'plan.productionPlan.分钟' | translate }}
                        </span>
                        <span *ngIf="!editable && !data.get('general').value" class="right-no-data">
                          {{ 'plan.productionPlan.暂无数据' | translate }}
                        </span>
                        <div *ngIf="editable" class="edit-input">
                          <div class="no-input-value">{{ data.get('simple').value }}</div>
                          <div>-</div>
                          <nz-form-item>
                            <nz-form-control>
                              <nz-input-number
                                style="width: 50px"
                                (nzFocus)="samChange()"
                                formControlName="general"
                                [nzSize]="'small'"
                                [nzMin]="0.1"
                                [nzMax]="999999999.9"
                                [nzStep]="0.1"
                                [nzPrecision]="1">
                              </nz-input-number>
                            </nz-form-control>
                          </nz-form-item>
                          <div class="card-text" style="margin-left: 2px">{{ 'plan.productionPlan.分钟' | translate }}</div>
                        </div>
                      </div>
                      <div class="item">
                        <span class="left">
                          <span class="card-text">{{ 'plan.productionPlan.较难' | translate }}：</span>
                          <ng-container *ngIf="!editable; else editTemp3">
                            <div style="border: 1px solid #fff" [ngClass]="{ active: data.get('color_checked3').value }">
                              <div
                                class="color-box"
                                [ngStyle]="{
                                  background: data.get('difficult_color').value ? data.get('difficult_color').value : '#F1F2F5',
                                  'border-width': data.get('difficult_color').value ? '2px' : '0'
                                }">
                                <i nz-icon *ngIf="!data.get('difficult_color').value" class="iconfont iconyanse" style="color: #ced3de">
                                  <svg>
                                    <path
                                      d="M473.96176455 935.58235273C306.85294092 935.58235273 108.3235291 808.55 69.59705908 573.17352911c-28.53529453-173.38235273 30.75882364-315.15882364 171.50294092-410.02941182C316.96470547 111.9764709 398.81176455 86.06176455 484.39117637 86.06176455c261.21176455 0 475.49117636 241.54411729 477 399.1764709 0.60882364 64.32352911-19.90588271 120.78529453-57.70588184 158.98235273-33.14117636 33.45882363-76.71176455 51.08823545-126 51.08823545a219.46764727 219.46764727 0 0 1-50.50588271-6.06176455c-37.21764727-8.73529453-64.77352911-4.65882363-78.88235274 12.15-11.32941182 13.5-12.6264709 32.37352911-11.32941181 39.28235274 10.45588271 61.06764727 1.69411729 109.66764727-26.1 144.29117636-32.05588271 40.07647089-78.53823545 46.16470547-88.38529454 47.03823545a327.97058818 327.97058818 0 0 1-48.52058818 3.5735291z m6.3264709-796.71176455c-74.03823545 0-145.24411729 22.65882363-211.57941182 67.44705909C145.93823545 289.06470547 96.41176455 408.10294092 121.4 560.1235291c33.67058818 204.59117636 204.67058818 315 348.51176455 315.00000001 13.92352911 0 27.74117637-0.97941182 41.13529453-2.99117638l2.75294092-0.26470546c0.05294092-0.05294092 30.25588271-2.38235273 49.26176455-26.62941182 16.96764727-21.52058818 21.65294092-54.74117637 14.00294092-98.78823545-3.97058818-23.58529453 1.8-59.02941182 23.8764709-85.31470547 14.10882364-16.75588271 40.73823545-36.71470547 87.22058818-36.71470635 14.74411729 0 30.81176455 2.01176455 47.83235274 6.06176455 48.70588271 11.38235273 93.17647089 0.60882364 123.3-29.88529365 27.13235273-27.39705908 41.8764709-69.51176455 41.4-118.64117637-1.24411729-132.3264709-194.13529453-343.08529453-420.40588184-343.08529453z"></path>
                                    <path
                                      d="M277.28529453 408.42058818m-52.94117724 0a52.94117637 52.94117637 0 1 0 105.8823536 0 52.94117637 52.94117637 0 1 0-105.8823536 0Z"></path>
                                    <path
                                      d="M402.30588271 305.74117637m-52.94117724 0a52.94117637 52.94117637 0 1 0 105.88235361 0 52.94117637 52.94117637 0 1 0-105.88235361 0Z"></path>
                                    <path
                                      d="M562.29411729 305.74117637m-52.94117637 0a52.94117637 52.94117637 0 1 0 105.88235361 0 52.94117637 52.94117637 0 1 0-105.88235361 0Z"></path>
                                    <path
                                      d="M708.2 368.26470547m-52.94117637 0a52.94117637 52.94117637 0 1 0 105.88235274 0 52.94117637 52.94117637 0 1 0-105.88235274 0Z"></path>
                                    <path
                                      d="M277.28529453 573.30588271m-52.94117724 0a52.94117637 52.94117637 0 1 0 105.8823536 0 52.94117637 52.94117637 0 1 0-105.8823536 0Z"></path>
                                  </svg>
                                </i>
                              </div>
                            </div>
                          </ng-container>
                          <ng-template #editTemp3>
                            <div
                              class="edit-color-box"
                              style="border: 1px solid #fff"
                              [nzTooltipTitle]="
                                (!data.get('difficult_color').value
                                  ? 'plan.productionPlan.点击可设置颜色'
                                  : 'plan.productionPlan.点击可替换颜色'
                                ) | translate
                              "
                              nz-tooltip
                              [ngClass]="{
                                active: data.get('color_checked3').value,
                                error: isCommit && data.get('difficult_color').status === 'INVALID' && data.get('isValidator').value
                              }">
                              <span
                                *ngIf="data.get('difficult_color').value"
                                class="icon iconfont icon-cuowu"
                                (click)="deleteColor(data, 3)"></span>
                              <div
                                class="color-box"
                                [ngClass]="{
                                  'background-hover': !data.get('difficult_color').value
                                }"
                                (click)="checkTargetColorClick(data, 3)"
                                [ngStyle]="{
                                  background: data.get('difficult_color').value
                                    ? data.get('difficult_color').value
                                    : data.get('color_checked3').value
                                    ? '#EDF6FF'
                                    : isCommit && data.get('difficult_color').status === 'INVALID' && data.get('isValidator').value
                                    ? '#FFECEC'
                                    : '#f1f2f5',
                                  'border-width': data.get('difficult_color').value ? '2px' : '0'
                                }">
                                <i
                                  *ngIf="!data.get('difficult_color').value"
                                  nz-icon
                                  class="icon iconfont iconshouzhi"
                                  style="color: #ced3de">
                                  <svg class="icon">
                                    <path
                                      d="M833.41189576 341.93766276h-3.5472107c-17.64953613 0-33.56872559 5.27755738-47.67105103 14.10232545-12.02590943-37.63504029-46.97891236-63.33068848-86.51733399-63.41720582-17.64953613 0-35.29907227 5.27755738-51.13174438 14.10232545-12.02590943-37.63504029-46.97891236-63.33068848-86.51733398-63.41720582-15.91918945 0-31.83837891 3.46069336-44.12384033 10.55511475V158.78046671C513.81686402 106.00489298 474.97058105 63.69791667 421.93545532 63.69791667c-52.9486084 0-95.34210205 42.30697632-95.34210204 95.08255005v366.31439208l-58.31268311-58.05313109c-37.02941895-37.02941895-102.43652344-31.66534424-134.18838501 0-31.75186158 31.66534424-52.9486084 95.16906738-7.00790406 140.93673706l264.82955933 264.13742065c5.36407471 5.27755738 12.37197876 10.55511475 17.64953613 14.10232544 49.40139771 38.75976563 104.25338746 63.33068848 220.705719 63.33068847 268.37677002 0 293.12072754-144.39743042 293.12072753-324.09393309V437.10673015c0.08651734-52.77557373-38.75976563-95.16906738-89.97802733-95.16906739m40.57662963 283.6038208c0 151.57836914 0 272.96218873-241.90246582 272.96218873-102.35000611 0-164.2098999-22.92709351-210.15060425-68.60824585L169.47787475 579.77381389c-23.01361084-22.92709351-15.91918945-51.13174439 1.73034669-68.69476319 17.64953613-17.64953613 51.13174439-19.37988281 68.86779785-1.81686401 0 0 44.12384033 44.037323 82.97012329 81.06674194l54.76547242 54.59243775V167.69175212c0-24.65744019 19.46640015-44.037323 45.94070434-44.037323 24.65744019 0 42.39349365 19.37988281 42.39349365 44.037323v308.17474365c0 14.10232544 10.55511475 24.65744019 24.65744019 24.65744019s24.65744019-10.55511475 24.65744018-24.65744019V345.57139079c0-24.65744019 17.64953613-44.037323 42.48001099-44.037323 0 0 44.12384033 0 44.12384033 44.037323v169.05487061c0 14.10232544 10.55511475 24.65744019 24.65744018 24.65744019s24.65744019-10.55511475 24.65744019-24.65744019V394.88627116c0-24.65744019 17.64953613-44.037323 42.48001099-44.037323 0 0 44.12384033 3.46069336 44.12384033 44.037323v149.6749878c0 14.01580811 10.64163209 24.65744019 24.65744019 24.65744019 14.18884277 0 24.74395752-10.64163209 24.74395752-24.65744019V440.65394084c0-24.65744019 19.46640015-44.12384033 44.12384033-44.12384033 0 0 44.12384033-1.73034668 44.12384033 44.12384033l-1.64382935 184.88754272"></path>
                                  </svg>
                                </i>
                              </div>
                            </div>
                          </ng-template>
                        </span>
                        <span class="right" *ngIf="data.get('difficult').value && !editable">
                          {{ data.get('general').value }} - {{ data.get('difficult').value }}
                          {{ 'plan.productionPlan.分钟' | translate }}
                        </span>
                        <span *ngIf="!editable && !data.get('difficult').value" class="right-no-data">
                          {{ 'plan.productionPlan.暂无数据' | translate }}
                        </span>
                        <div *ngIf="editable" class="edit-input">
                          <div class="no-input-value">{{ data.get('general').value }}</div>
                          <span>-</span>
                          <nz-form-item>
                            <nz-form-control>
                              <nz-input-number
                                style="width: 50px"
                                formControlName="difficult"
                                (nzFocus)="samChange()"
                                [nzSize]="'small'"
                                [nzMin]="0.1"
                                [nzMax]="999999999.9"
                                [nzStep]="0.1"
                                [nzPrecision]="1">
                              </nz-input-number>
                            </nz-form-control>
                          </nz-form-item>
                          <span class="card-text" style="margin-left: 2px">{{ 'plan.productionPlan.分钟' | translate }}</span>
                        </div>
                      </div>
                      <div class="item">
                        <span class="left">
                          <span class="card-text">{{ 'plan.productionPlan.极难' | translate }}：</span>
                          <ng-container *ngIf="!editable; else editTemp4">
                            <div style="border: 1px solid #fff">
                              <div
                                class="color-box"
                                [ngStyle]="{
                                  background: data.get('extremely_difficult_color').value
                                    ? data.get('extremely_difficult_color').value
                                    : '#F1F2F5',
                                  'border-width': data.get('extremely_difficult_color').value ? '2px' : '0'
                                }">
                                <i
                                  nz-icon
                                  *ngIf="!data.get('extremely_difficult_color').value"
                                  class="iconfont iconyanse"
                                  style="color: #ced3de">
                                  <svg>
                                    <path
                                      d="M473.96176455 935.58235273C306.85294092 935.58235273 108.3235291 808.55 69.59705908 573.17352911c-28.53529453-173.38235273 30.75882364-315.15882364 171.50294092-410.02941182C316.96470547 111.9764709 398.81176455 86.06176455 484.39117637 86.06176455c261.21176455 0 475.49117636 241.54411729 477 399.1764709 0.60882364 64.32352911-19.90588271 120.78529453-57.70588184 158.98235273-33.14117636 33.45882363-76.71176455 51.08823545-126 51.08823545a219.46764727 219.46764727 0 0 1-50.50588271-6.06176455c-37.21764727-8.73529453-64.77352911-4.65882363-78.88235274 12.15-11.32941182 13.5-12.6264709 32.37352911-11.32941181 39.28235274 10.45588271 61.06764727 1.69411729 109.66764727-26.1 144.29117636-32.05588271 40.07647089-78.53823545 46.16470547-88.38529454 47.03823545a327.97058818 327.97058818 0 0 1-48.52058818 3.5735291z m6.3264709-796.71176455c-74.03823545 0-145.24411729 22.65882363-211.57941182 67.44705909C145.93823545 289.06470547 96.41176455 408.10294092 121.4 560.1235291c33.67058818 204.59117636 204.67058818 315 348.51176455 315.00000001 13.92352911 0 27.74117637-0.97941182 41.13529453-2.99117638l2.75294092-0.26470546c0.05294092-0.05294092 30.25588271-2.38235273 49.26176455-26.62941182 16.96764727-21.52058818 21.65294092-54.74117637 14.00294092-98.78823545-3.97058818-23.58529453 1.8-59.02941182 23.8764709-85.31470547 14.10882364-16.75588271 40.73823545-36.71470547 87.22058818-36.71470635 14.74411729 0 30.81176455 2.01176455 47.83235274 6.06176455 48.70588271 11.38235273 93.17647089 0.60882364 123.3-29.88529365 27.13235273-27.39705908 41.8764709-69.51176455 41.4-118.64117637-1.24411729-132.3264709-194.13529453-343.08529453-420.40588184-343.08529453z"></path>
                                    <path
                                      d="M277.28529453 408.42058818m-52.94117724 0a52.94117637 52.94117637 0 1 0 105.8823536 0 52.94117637 52.94117637 0 1 0-105.8823536 0Z"></path>
                                    <path
                                      d="M402.30588271 305.74117637m-52.94117724 0a52.94117637 52.94117637 0 1 0 105.88235361 0 52.94117637 52.94117637 0 1 0-105.88235361 0Z"></path>
                                    <path
                                      d="M562.29411729 305.74117637m-52.94117637 0a52.94117637 52.94117637 0 1 0 105.88235361 0 52.94117637 52.94117637 0 1 0-105.88235361 0Z"></path>
                                    <path
                                      d="M708.2 368.26470547m-52.94117637 0a52.94117637 52.94117637 0 1 0 105.88235274 0 52.94117637 52.94117637 0 1 0-105.88235274 0Z"></path>
                                    <path
                                      d="M277.28529453 573.30588271m-52.94117724 0a52.94117637 52.94117637 0 1 0 105.8823536 0 52.94117637 52.94117637 0 1 0-105.8823536 0Z"></path>
                                  </svg>
                                </i>
                              </div>
                            </div>
                          </ng-container>
                          <ng-template #editTemp4>
                            <div
                              style="border: 1px solid #fff"
                              class="edit-color-box"
                              [nzTooltipTitle]="
                                (!data.get('extremely_difficult_color').value
                                  ? 'plan.productionPlan.点击可设置颜色'
                                  : 'plan.productionPlan.点击可替换颜色'
                                ) | translate
                              "
                              nz-tooltip
                              [ngClass]="{
                                error:
                                  isCommit && data.get('extremely_difficult_color').status === 'INVALID' && data.get('isValidator').value,
                                active: data.get('color_checked4').value
                              }">
                              <span
                                *ngIf="data.get('extremely_difficult_color').value"
                                class="icon iconfont icon-cuowu"
                                (click)="deleteColor(data, 4)"></span>
                              <div
                                class="color-box"
                                [ngClass]="{
                                  'background-hover': !data.get('extremely_difficult_color').value
                                }"
                                (click)="checkTargetColorClick(data, 4)"
                                [ngStyle]="{
                                  background: data.get('extremely_difficult_color').value
                                    ? data.get('extremely_difficult_color').value
                                    : data.get('color_checked4').value
                                    ? '#EDF6FF'
                                    : isCommit &&
                                      data.get('extremely_difficult_color').status === 'INVALID' &&
                                      data.get('isValidator').value
                                    ? '#FFECEC'
                                    : '#f1f2f5',
                                  'border-width': data.get('extremely_difficult_color').value ? '2px' : '0'
                                }">
                                <i
                                  *ngIf="!data.get('extremely_difficult_color').value"
                                  nz-icon
                                  class="icon iconfont iconshouzhi"
                                  style="color: #ced3de">
                                  <svg class="icon">
                                    <path
                                      d="M833.41189576 341.93766276h-3.5472107c-17.64953613 0-33.56872559 5.27755738-47.67105103 14.10232545-12.02590943-37.63504029-46.97891236-63.33068848-86.51733399-63.41720582-17.64953613 0-35.29907227 5.27755738-51.13174438 14.10232545-12.02590943-37.63504029-46.97891236-63.33068848-86.51733398-63.41720582-15.91918945 0-31.83837891 3.46069336-44.12384033 10.55511475V158.78046671C513.81686402 106.00489298 474.97058105 63.69791667 421.93545532 63.69791667c-52.9486084 0-95.34210205 42.30697632-95.34210204 95.08255005v366.31439208l-58.31268311-58.05313109c-37.02941895-37.02941895-102.43652344-31.66534424-134.18838501 0-31.75186158 31.66534424-52.9486084 95.16906738-7.00790406 140.93673706l264.82955933 264.13742065c5.36407471 5.27755738 12.37197876 10.55511475 17.64953613 14.10232544 49.40139771 38.75976563 104.25338746 63.33068848 220.705719 63.33068847 268.37677002 0 293.12072754-144.39743042 293.12072753-324.09393309V437.10673015c0.08651734-52.77557373-38.75976563-95.16906738-89.97802733-95.16906739m40.57662963 283.6038208c0 151.57836914 0 272.96218873-241.90246582 272.96218873-102.35000611 0-164.2098999-22.92709351-210.15060425-68.60824585L169.47787475 579.77381389c-23.01361084-22.92709351-15.91918945-51.13174439 1.73034669-68.69476319 17.64953613-17.64953613 51.13174439-19.37988281 68.86779785-1.81686401 0 0 44.12384033 44.037323 82.97012329 81.06674194l54.76547242 54.59243775V167.69175212c0-24.65744019 19.46640015-44.037323 45.94070434-44.037323 24.65744019 0 42.39349365 19.37988281 42.39349365 44.037323v308.17474365c0 14.10232544 10.55511475 24.65744019 24.65744019 24.65744019s24.65744019-10.55511475 24.65744018-24.65744019V345.57139079c0-24.65744019 17.64953613-44.037323 42.48001099-44.037323 0 0 44.12384033 0 44.12384033 44.037323v169.05487061c0 14.10232544 10.55511475 24.65744019 24.65744018 24.65744019s24.65744019-10.55511475 24.65744019-24.65744019V394.88627116c0-24.65744019 17.64953613-44.037323 42.48001099-44.037323 0 0 44.12384033 3.46069336 44.12384033 44.037323v149.6749878c0 14.01580811 10.64163209 24.65744019 24.65744019 24.65744019 14.18884277 0 24.74395752-10.64163209 24.74395752-24.65744019V440.65394084c0-24.65744019 19.46640015-44.12384033 44.12384033-44.12384033 0 0 44.12384033-1.73034668 44.12384033 44.12384033l-1.64382935 184.88754272"></path>
                                  </svg>
                                </i>
                              </div>
                            </div>
                          </ng-template>
                        </span>
                        <ng-container *ngIf="!editable; else samLevel4Temp">
                          <span *ngIf="data.get('extremely_difficult').value" class="right">
                            <span class="card-text" style="font-size: 16px">></span> {{ data.get('extremely_difficult').value }}
                            {{ 'plan.productionPlan.分钟' | translate }}
                          </span>
                          <span *ngIf="!data.get('extremely_difficult').value" class="right-no-data">
                            {{ 'plan.productionPlan.暂无数据' | translate }}
                          </span>
                        </ng-container>
                        <ng-template #samLevel4Temp>
                          <div *ngIf="editable" class="edit-input">
                            <div class="card-text" style="margin-right: 10px; font-size: 16px">></div>
                            <div class="no-input-value">{{ data.get('difficult').value }}</div>
                            <div class="card-text" style="margin-left: 2px">{{ 'plan.productionPlan.分钟' | translate }}</div>
                          </div>
                        </ng-template>
                      </div>
                    </div>
                    <div *ngIf="editable" class="foot">
                      <div
                        class="left"
                        nz-popconfirm
                        [nzPopconfirmTitle]="'plan.productionPlan.确定清空卡片' | translate"
                        (nzOnConfirm)="clearCardInfo(data)">
                        <i nz-icon class="icon iconfont iconqingkong1">
                          <svg>
                            <path
                              d="M566 908s-4.5 0 0 0l-90-49.5v-4.5c9-18 85.5-180 76.5-175.5-9 9-63 67.5-103.5 126l-22.5 31.5S336.5 773 305 737c18-27 117-153 117-153L255.5 683c-9-9-63-67.5-81-99 13.5-13.5 103.5-90 103.5-90l-126 45-45-85.5v-4.5c22.5-4.5 207-40.5 292.5-103.5h9L665 602s4.5 4.5 0 9c-9 22.5-81 139.5-99 297z m333-702L737 368v4.5c36 63 36 135-18 189h-4.5L458 305v-4.5c54-54 126-54 189-18h4.5L813.5 120.5c22.5-22.5 63-22.5 85.5 0s22.5 63 0 85.5z"></path>
                          </svg>
                        </i>
                        <span>{{ 'plan.productionPlan.清空卡片' | translate }}</span>
                      </div>
                      <div class="right">
                        <span class="clear-color" (click)="clearCardColor(data)">{{ 'plan.productionPlan.清空颜色' | translate }}</span>
                        <span class="line"></span>
                        <span class="clear-sam" (click)="clearCardSam(data)">{{ 'plan.productionPlan.清空时间' | translate }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </ng-container>
            </div>
          </form>
        </ng-container>
        <ng-template #noImage>
          <div class="no-image">
            <flc-no-data></flc-no-data>
          </div>
        </ng-template>
      </div>
    </nz-spin>
  </div>
</ng-template>
