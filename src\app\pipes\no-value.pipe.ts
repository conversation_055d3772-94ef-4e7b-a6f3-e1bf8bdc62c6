import { isNotNil } from 'ng-zorro-antd/core/util';
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'noValue',
})
export class NoValuePipe implements PipeTransform {
  transform(value: any, replace: any = '-'): any {
    if (isNotNil(value) && value !== '') {
      if (value === 0) {
        return 0;
      } else {
        return value;
      }
    } else {
      return replace;
    }
  }
}
