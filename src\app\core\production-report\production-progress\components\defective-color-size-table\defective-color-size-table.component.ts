import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import {
  FlcColorSizeTableCell,
  FlcStdTableColumnItem,
  FlcStdTableDef,
  FlcStdTableHeaderDef,
  FlcStdTableRowItem,
} from './defective-color-size-table.interface';

type cascadeOptionItem = {
  label: string;
  value: string;
  code: string;
  isLeaf: boolean;
  children?: Array<cascadeOptionItem>;
  disabled: boolean;
};

@Component({
  selector: 'defective-color-size-table',
  templateUrl: './defective-color-size-table.component.html',
  styleUrls: ['./defective-color-size-table.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DefectiveColorSizeTableComponent implements OnInit, AfterViewInit, OnChanges {
  @ViewChild('tableOuterContainer') tableOuterContainerRef!: ElementRef<HTMLElement>;

  readonly defaultDescCellWidth = 144;
  readonly defaultColWidth = 96;
  readonly defaultSumColWidth = 64;
  readonly defaultScrollHeight = 164;
  readonly defaultScrollBarWidth = 8;

  @Input() scrollHeight: number = this.defaultScrollHeight;

  /** The bunch of FlcColorSizeTableCell or FlcColorSizeTableCellV2, which can help to construct the color-size table */
  @Input() dataList: Array<FlcColorSizeTableCell> | null = null;

  @Input() isShowSum: boolean = true;

  @Input() fetchCellData: ((val: { sizeId?: number; colorId?: number }) => Array<FlcColorSizeTableCell>) | undefined;

  translateName = 'flc.colorSizeTable.';

  private _initialized = false;
  private _isShowTable = false;
  private _tableDef!: FlcStdTableDef;
  private _outerContainerWidth!: number;
  private _rawDataList: Array<FlcColorSizeTableCell> | undefined;

  private _isSizeOptionsLoading = false;
  private _sizeOptions!: { label: string; value: string; code: string; disabled?: boolean; used?: boolean }[];
  private _colorOptions!: Array<cascadeOptionItem>;

  /** The definition of table */
  get tableDef() {
    return this._tableDef;
  }

  get isShowTable() {
    return this._isShowTable;
  }

  get isSizeOptionsLoading() {
    return this._isSizeOptionsLoading;
  }

  get sizeOptions() {
    return this._sizeOptions;
  }

  get colorOptions() {
    return this._colorOptions;
  }

  constructor(private _cd: ChangeDetectorRef) {}

  ngOnInit() {}

  ngAfterViewInit(): void {
    setTimeout(() => {
      this._outerContainerWidth = this.tableOuterContainerRef.nativeElement.clientWidth - 16;
      if (this.dataList !== null) {
        this._tableDef = this.constructStdTableDef();
      }
      this._isShowTable = this.dataList !== null && this._tableDef?.data.length > 0;
      this._cd.detectChanges();
      this._initialized = true;
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes && changes.dataList && changes.dataList.currentValue) {
      this._rawDataList = this.normalizeData(changes.dataList.currentValue);
    }
    if (this._initialized && this.dataList !== null) {
      this._tableDef = this.constructStdTableDef();
    }
    this._isShowTable = this.dataList !== null && this._tableDef?.data.length > 0;
    this._cd.detectChanges();
  }

  /** Normalize the dataList and convert to the array of FlcColorSizeTableCell */
  normalizeData(data: Array<FlcColorSizeTableCell>) {
    let _data = JSON.parse(JSON.stringify(data));
    return _data;
  }

  /**
   * Whether table can be scrolled in x/y direction.
   *
   * Notes: y scroll bar won't be displayed until exceeding 4 rows (excluding the sum row)
   */
  private getScrollConfig(_w: number): { x?: string; y: string } {
    const _x = _w > this._outerContainerWidth ? { x: `${this._outerContainerWidth}px` } : {};
    const _y = { y: `${this.scrollHeight}px` };
    return { ..._x, ..._y };
  }

  /**
   * Construct the definition of standard table via the bunch of FlcColorSizeTableCell data
   * @returns FlcStdTableDef
   */
  private constructStdTableDef(): FlcStdTableDef {
    const d: Array<FlcColorSizeTableCell> = this._rawDataList ?? [];
    const _std: Array<FlcStdTableRowItem> = [];
    const _hd: Array<FlcStdTableHeaderDef> = [];
    const _rs: Array<number> = [];
    const _cs: Array<number> = [];
    let _ts = 0;
    let _w = 0;

    d.forEach((cell) => {
      const { id, line_uuid, qty, indexing, deletable, changeable, over, extraParams } = cell;
      const { color_name, color_code } = cell.color_info;
      const { spec_size, spec_code } = cell.size_info;
      const color_id = Number(cell.color_info.color_id);
      const spec_id = Number(cell.size_info.spec_id);
      const inspection_item_list = cell.inspection_item_list;

      // Construct row data
      let ri = _std.findIndex((_r) => {
        return color_id < 0 ? _r.rowId === color_id : _r.rowCode === color_code;
      });
      let r: FlcStdTableRowItem;
      if (ri === -1) {
        r = {
          rowId: color_id,
          rowName: color_name,
          rowCode: color_code,
          columns: [],
          deletable: deletable,
          changeable: changeable ?? true,
          frozen: !(extraParams?.cellEditable ?? true),
          over: over ?? false,
        };
        _std.push(r);
        ri = _std.length - 1;
      } else {
        r = _std[ri];
        if (r.deletable) {
          r.deletable = deletable;
        }
        if (r.frozen) {
          r.frozen = !(extraParams?.cellEditable ?? true);
        }
      }

      // Construct column data
      const ci = indexing - 1; // (indexing from 1)
      const c: FlcStdTableColumnItem = {
        id: id,
        uuid: line_uuid ?? '',
        qty: qty,
        sortIndex: ci,
        columnId: spec_id,
        columnName: spec_size,
        columnCode: spec_code,
        deletable: deletable,
        changeable: changeable ?? true,
        frozen: !(extraParams?.cellEditable ?? true),
        maxQty: extraParams?.maxNum,
        minQty: extraParams?.minNum,
        over: over ?? false,
        inspectionItems:
          (inspection_item_list ?? []).length === 0
            ? '-'
            : (inspection_item_list ?? []).map((e: any) => e.inspection_item_name ?? '').join('、'),
      };
      r.columns[ci] = c;

      // Calculate the sum of row and column data
      const _qty = typeof qty === 'string' ? 0 : qty ?? 0;
      _rs[ri] = (_rs[ri] ?? 0) + (_qty ?? 0);
      _cs[ci] = (_cs[ci] ?? 0) + (_qty ?? 0);
      _ts += _qty ?? 0;
      // Construct header definition
      let h = _hd.find((_h) => _h !== undefined && (spec_id < 0 ? _h.columnId === spec_id : _h.columnCode === spec_code));
      if (!h) {
        h = {
          columnId: spec_id,
          columnName: spec_size,
          columnCode: spec_code,
          sortIndex: ci,
          deletable: deletable,
          changeable: changeable ?? true,
          frozen: !(extraParams?.cellEditable ?? true),
          over: over ?? false,
        };
        _hd[ci] = h;
      } else {
        if (h.deletable) {
          h.deletable = deletable;
        }
        if (h.frozen) {
          h.frozen = !(extraParams?.cellEditable ?? true);
        }
      }
    });

    _w = this.calculateTableWidth(_hd.length);

    return {
      mode: 'readOnly',
      tableCls: this.getTableStyleClass(_w),
      width: _w,
      data: _std,
      header: _hd,
      rowSum: _rs,
      colSum: _cs,
      totalSum: _ts,
      scroll: this.getScrollConfig(_w),
    };
  }

  private calculateTableWidth(hc: number) {
    const _w = this.defaultDescCellWidth + this.defaultColWidth * hc + this.defaultSumColWidth + this.defaultScrollBarWidth + 1; //(offset: 1);
    return _w;
  }

  private getTableStyleClass(width: number) {
    let _cls = 'read-only-mode';
    if (width > this._outerContainerWidth) {
      _cls += ' has-horizontal-scroll';
    }
    return _cls;
  }
}
