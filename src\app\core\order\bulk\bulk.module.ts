import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BulkListComponent } from './bulk-list/bulk-list.component';
import { FlButtonModule } from 'fl-ui-angular/button';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzCascaderModule } from 'ng-zorro-antd/cascader';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { FlcComponentsModule, FlcDirectivesModule, FlcOssUploadService } from 'fl-common-lib';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BulkRoutingModule } from './bulk-routing.module';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import { BulkDetailComponent } from './bulk-detail/bulk-detail.component';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';
import { NotpassReasonComponent } from './notpass-reason/notpass-reason.component';
import { BulkService } from './bulk.service';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { printLabelComponentsModule } from './components/components.module';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { BulkPrintComponent } from './bulk-print/bulk-print.component';
import { NzUploadModule } from 'ng-zorro-antd/upload';
import { NzMessageModule } from 'ng-zorro-antd/message';
import { UploadImagesComponent } from './components/upload-images/upload-images.component';
import { CommonComponentModule } from 'fl-sewsmart-lib/common-component';
import { ImportResultComponent } from './components/import-result/import-result.component';
import { BatchUpdateLeadtimeComponent } from './components/batch-update-leadtime/batch-update-leadtime.component';
import { BatchUpdateFobPriceComponent } from './components/batch-update-fob-price/batch-update-fob-price.component';
const nzModules = [
  NzIconModule,
  NzButtonModule,
  NzTableModule,
  NzDatePickerModule,
  NzFormModule,
  NzInputModule,
  NzRadioModule,
  NzCheckboxModule,
  NzPopoverModule,
  NzSelectModule,
  NzInputNumberModule,
  NzCascaderModule,
  NzToolTipModule,
  NzTabsModule,
  NzDividerModule,
  NzSpinModule,
  NzModalModule,
  NzAutocompleteModule,
  NzGridModule,
  NzUploadModule,
  NzMessageModule,
];

@NgModule({
  declarations: [
    BulkListComponent,
    BulkDetailComponent,
    NotpassReasonComponent,
    BulkPrintComponent,
    UploadImagesComponent,
    ImportResultComponent,
    BatchUpdateLeadtimeComponent,
    BatchUpdateFobPriceComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    BulkRoutingModule,
    printLabelComponentsModule,
    FlButtonModule,
    FlcComponentsModule,
    FlcDirectivesModule,
    CommonComponentModule,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new MultiTranslateHttpLoader(http, [
            { prefix: './assets/i18n/order/bulk/', suffix: '.json' },
            { prefix: './assets/i18n/table-data-view/', suffix: '.json' },
          ]);
        },
        deps: [HttpClient],
      },
    }),
    ...nzModules,
  ],
  providers: [FlcOssUploadService, BulkService],
})
export class BulkModule {
  constructor(public translateService: TranslateService, public ossUploadService: FlcOssUploadService, private _service: BulkService) {
    const cl = translateService.currentLang;
    translateService.currentLang = '';
    translateService.use(cl);

    // oss upload service模块级别配置信息
    ossUploadService.setOssNamespace('bulk-order');
    ossUploadService.setOssAuthUrl('/service/frontapi/v1/oss');
    ossUploadService.setOssAuthParams({
      biz_type: 'order',
    });
    ossUploadService.setFilePath('order-bulk');
  }
}
