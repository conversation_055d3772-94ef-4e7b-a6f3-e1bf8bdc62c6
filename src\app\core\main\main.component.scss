@import 'variables';

.logo {
  height: 64px;

  &.text {
    background: #fff;
    color: $fl-pretty-color-dark;
    font-size: 18px;
    font-weight: 500;
    line-height: 48px;
    padding-left: 16px;
  }

  &.image {
    padding: 4px;
    background-color: white;
    background-image: url(/assets/image/elan_logo.png);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    background-origin: content-box;
  }
}
:host ::ng-deep .logo .bold {
  font-size: 22px;
}

nz-sider {
  background-color: #f7f8fa;
}

nz-header {
  padding: 0 28px 0 12px;
  height: 48px;
  line-height: 48px;
  background-color: #fff;
  display: inline-flex;
  justify-content: space-between;

  .ant-breadcrumb {
    line-height: 48px;
  }

  a.logout {
    color: #000000;
  }
}

.logout-btn {
  padding: 5px 30px;
}

nz-content {
  height: calc(100vh - 48px);
  overflow: auto;

  .content-container {
    margin: 8px 12px;
  }
}

.ant-dropdown-menu-item:hover {
  background-color: #fff;
  color: $fl-pretty-color-dark;
}

.user-name {
  color: #2b3848;
  padding: 0 4px;
}

.user-role {
  background-color: #ebecf0;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 500;
  color: #54607c;
  padding: 2px 4px;
  margin: 0 4px;
}

.side-menu::-webkit-scrollbar {
  display: none;
}

.side-menu {
  background-color: #ffffff;
  height: calc(100vh - 64px);
  overflow: auto;
}
.g-toggle {
  position: absolute;
  bottom: 11px;
  left: 11px;
  height: 32px;
  width: 32px;
  background: #f7f8fa;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;

  opacity: 0.8;
  i {
    color: #515665;
  }
  &:hover {
    cursor: pointer;
    background: #e7f3fe;
    i {
      color: #138aff;
    }
  }
}
.g-fold-icon {
  position: absolute;
  bottom: 12px;
  right: -12px;
  width: 24px;
  height: 24px;
  background: #ffffff;
  border: 1px solid #e3e4e7;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  &:hover {
    cursor: pointer;
    color: #40aaff;
  }
}
.collapsed {
  width: 31px;
  height: 24px;
  background: #007aff;
  border-radius: 0px 100px 100px 0px;
  right: -30px;
  i {
    color: #ffffff;
  }
}
.not-collapsed {
  &:hover {
    border-color: #40aaff;
  }
}
:host #switchBar {
  align-self: end;
  flex: 1;
  ::ng-deep {
    nz-tabs-nav {
      margin: 0;
    }
    .ant-tabs-tab-remove {
      margin-left: unset;
      color: #b5b8bf;
    }
    .ant-tabs-nav .ant-tabs-tab {
      padding: 8px 12px;
      min-width: 60px;
      justify-content: center;
      background-color: transparent;
      &.ant-tabs-tab-active {
        background-color: #f0f2f5;
        a {
          color: #222b3c;
        }
      }
      a {
        color: #54607c;
      }
    }
  }
}
.closeAllTabIcon {
  font-size: 18px;
  color: #515665;
  cursor: pointer;
  vertical-align: middle;
  margin: 0 12px;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  &:hover {
    color: #fe5d56;
  }
}

// 二维码
.icon-shouji1-class {
  font-size: 18px;
  color: #515665;
  cursor: pointer;
  vertical-align: middle;
}
::ng-deep .mobile-ngx-qrcode-class {
  img {
    margin: 12px 12px 4px 12px;
  }
}
.qrcode-box {
  width: 104px;
  min-height: 120px;
  background: #ffffff;
  box-shadow: 0px 5px 32px 0px rgba(0, 0, 0, 0.06);
  border-radius: 2px;
  text-align: center;

  .qrcode-tip-info {
    font-size: 12px;
    font-weight: 400;
    color: #515665;
    line-height: 16px;
  }
}
