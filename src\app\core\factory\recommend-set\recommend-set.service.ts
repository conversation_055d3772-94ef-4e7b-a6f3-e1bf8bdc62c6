import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ResData } from './models/common-res';
import { RecommendKeyEnum } from './models/recommend.enum';
import { Recommends, RecommendsItem } from './models/recommend.interface';

@Injectable()
export class RecommendSetService {
  baseUrl = '/service/procurement-inventory/factory-recommend';
  constructor(private http: HttpClient) {}

  getList(): Observable<ResData<Recommends>> {
    return this.http.post<ResData<Recommends>>(`${this.baseUrl}/get-config`, {});
  }

  delete(keys: RecommendKeyEnum[]): Observable<ResData<object>> {
    return this.http.post<ResData<object>>(`${this.baseUrl}/del-config`, {
      keys,
    });
  }

  update(list: any[]): Observable<ResData<object>> {
    return this.http.post<ResData<object>>(`${this.baseUrl}/set-config`, {
      list,
    });
  }
}
