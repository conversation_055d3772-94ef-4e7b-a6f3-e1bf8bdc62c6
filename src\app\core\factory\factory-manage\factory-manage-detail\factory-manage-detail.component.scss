.container-outer {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .detail-title {
      font-size: 16px;
      font-weight: 500;
      color: #515661;
    }

    .detail-btn {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .result-notice {
    border-radius: 6px 6px 0px 0px;
    font-size: 16px;
    font-weight: 500;
    padding: 8px 16px;
    margin-bottom: -8px;
  }

  .red-notice {
    background: #ffeeee;
    color: #fe5d56;
  }

  .yellow-notice {
    background: #fff3e7;
    color: #ff912a;
  }

  .step-container {
    background: #ffffff;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;

    nz-steps {
      width: 100%;
    }
  }

  .content-container {
    background: #ffffff;
    border-radius: 4px;
    padding: 16px;
  }

  nz-skeleton {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
}

::ng-deep .modal-outer {
  .ant-modal-content {
    border-radius: 8px;
  }
}
