<div id="mainBoard">
  <div id="menuArea">
    <div id="firstMenuBar">
      <div class="firstLevelTitle">{{ translateName + '全部应用' | translate }}</div>
      <nz-tabset [(nzSelectedIndex)]="currentMenuIndex">
        <nz-tab *ngFor="let menu of menuList; let index = index" [nzTitle]="menu.name" (nzClick)="changeSubMenu(index)"></nz-tab>
      </nz-tabset>
    </div>
    <div id="secondMenuBoard">
      <div class="secondMenuArea" *ngFor="let group of currentSubMenu">
        <div class="secondGroupTitle">{{ group.groupName }}</div>
        <div class="secondMenuChildrenArea">
          <!-- <a class="secondMenuItem" *ngFor="let second of group.children" (click)="clickLink(second)" [routerLink]="second.path"> -->
          <a class="secondMenuItem" *ngFor="let second of group.children" (click)="clickLink(second)">
            <div class="iconArea">
              <i class="menuIcon" nz-icon [nzIconfont]="second.icon"></i>
            </div>
            <span> {{ second.name }}</span>
          </a>
        </div>
      </div>
    </div>
  </div>
  <div id="notificationArea">
    <nz-tabset [nzTabBarExtraContent]="searchBar" [(nzSelectedIndex)]="messageeIndex">
      <nz-tab [nzTitle]="(translateName + '待办通知' | translate) + '·' + totalUnfinishMessageCount" (nzClick)="messageIndexChange(0)">
        <div class="messageBoard">
          <a
            class="messageLine"
            [ngClass]="{ isRead: line.status === 2 }"
            (click)="readMessage(line.id)"
            *ngFor="let line of unfinishMessageList">
            <span class="module">{{ line.type_name }}</span>
            <!-- <span class="order">{{ line.related_code }}</span> -->
            <span class="other">{{ line.content }}</span>
          </a>
          <div style="display: flex; justify-content: center" *ngIf="unfinishMessageList.length < totalUnfinishMessageCount">
            <button nz-button nzType="text" (flcClickStop)="getMessageList(false, false)">
              {{ translateName + '加载更多' | translate }}
            </button>
          </div>
          <div style="height: 100%; display: flex; justify-content: center; align-items: center" *ngIf="totalUnfinishMessageCount === 0">
            <flc-no-data></flc-no-data>
          </div>
        </div>
      </nz-tab>
      <nz-tab [nzTitle]="(translateName + '已办' | translate) + '·' + totalFinishedMessageCount" (nzClick)="messageIndexChange(1)">
        <div class="messageBoard">
          <a
            class="messageLine"
            [ngClass]="{ isRead: line.status === 2 }"
            (click)="readMessage(line.id)"
            *ngFor="let line of finishedMessageList">
            <span class="module">{{ line.type_name }}</span>
            <!-- <span class="order">{{ line.related_code }}</span> -->
            <span class="other">{{ line.content }}</span>
          </a>
          <div style="display: flex; justify-content: center" *ngIf="finishedMessageList.length < totalFinishedMessageCount">
            <button nz-button nzType="text" (flcClickStop)="getMessageList(true, false)">
              {{ translateName + '加载更多' | translate }}
            </button>
          </div>
          <div style="height: 100%; display: flex; justify-content: center; align-items: center" *ngIf="totalFinishedMessageCount === 0">
            <flc-no-data></flc-no-data>
          </div>
        </div>
      </nz-tab>
    </nz-tabset>
    <ng-template #searchBar>
      <nz-input-group [nzPrefix]="prefixTpl" [nzSuffix]="inputClearTpl">
        <input
          nz-input
          [placeholder]="translateName + '搜索单号' | translate"
          nzSize="small"
          [(ngModel)]="messageSearchString"
          (flcDebouncedModelChange)="messageSearchStringChange($event)" />
        <ng-template #prefixTpl><span nz-icon nzType="search"></span> </ng-template>
        <ng-template #inputClearTpl>
          <i
            nz-icon
            class="ant-input-clear-icon"
            nzTheme="fill"
            nzType="close-circle"
            *ngIf="messageSearchString"
            (click)="messageSearchString = null"></i>
        </ng-template>
      </nz-input-group>
    </ng-template>
  </div>
</div>
