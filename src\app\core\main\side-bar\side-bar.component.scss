@import 'variables';

:host ::ng-deep .ant-menu-submenu-title {
  font-size: 14px;
  font-weight: 500;
  color: #515661;
}

:host ::ng-deep .ant-menu-submenu-title:hover {
  color: $fl-pretty-color-dark;
}

:host ::ng-deep .ant-menu-submenu-title:active {
  background-color: #f2fcf9;
}

:host ::ng-deep .ant-menu-submenu:hover .ant-menu-submenu-arrow {
  color: $fl-pretty-color-dark;
}

.ant-menu-inline {
  border-right: 0;
}

:host ::ng-deep .ant-menu-inline .ant-menu-item {
  width: 100%;
  background-color: white;
  margin: 0;
  font-size: 14px;
}

.ant-menu-item:hover,
.ant-menu-item:focus,
.ant-menu .ant-menu-item-selected {
  color: $fl-pretty-color-dark;
  background-color: #f2fcf9;
}

.ant-menu .ant-menu-item-selected {
  font-weight: 500;
}

.ant-menu-item::after {
  border-right: 0;
}

.ant-menu-item-selected::before {
  border-left: 2px solid;
  border-color: $fl-pretty-color-dark;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  content: '';
}

.sub-num {
  font-size: 11px;
  color: #54607c;
  background-color: #e3e5eb;
  padding: 0 6px;
  border-radius: 8px;
  line-height: 16px;
  right: 8px;
  position: absolute;
  height: 16px;
  margin-top: 12px;

  &.double-num {
    right: 4px;
  }
}

:host ::ng-deep .ant-menu-submenu-arrow {
  right: 32px;
}

#newMenu {
  list-style: none;
  display: flex;
  flex-direction: column;
  overflow-x: auto;
}
:host ::ng-deep {
  .menuIcon {
    svg {
      height: 22px;
      width: 22px;
    }
  }
}
.firstLevelMenu {
  padding: 8px;
  text-align: center;
  color: #515665;
  font-size: 14px;
  height: 100%;
  flex: 0 0 80px;
  cursor: pointer;
  .menuWrapper {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    transition: all 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
  }
  .menuNameEn {
    font-size: 12px;
    word-wrap: break-word;
    white-space: normal;
    width: 100%;
  }
  &.selected,
  &:hover {
    .menuWrapper {
      background-color: #d9f7ef;
      color: $fl-pretty-color-dark;
    }
  }
}
