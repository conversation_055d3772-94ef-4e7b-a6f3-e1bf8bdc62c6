<div class="modal-body">
  <form nz-form [formGroup]="assignForm">
    <!-- 批量选择 -->
    <div class="header-content">
      <div *ngFor="let item of batchConfig">
        <span style="padding-right: 10px">{{ item?.label }}:</span>
        <flss-department-select
          *ngIf="item?.type === 'department-select'"
          [requestInside]="true"
          [canSelectDepartment]="false"
          (onSelectChange)="onEmployeeChange($event, null, item)"
          [selectCurrentUser]="false">
        </flss-department-select>
      </div>
    </div>
    <div #tableBox style="background-color: #fff" formArrayName="lines">
      <flc-table [tableHeader]="tableHeaders" [tableConfig]="flcTableConfig" [template]="tdTemplate" [thTemplate]="thTemplate">
        <ng-template #thTemplate let-data="data">
          <ng-container *ngIf="data.isTh && data.key === 'employee_id'"> <span class="required-icon">*</span>负责人 </ng-container>
        </ng-template>

        <ng-template #tdTemplate let-data="data">
          <ng-container *ngIf="data.isTd" [formGroupName]="data.index">
            <!-- 订单需求号 -->
            <ng-container *ngIf="data.isTd && data.key === 'io_code'">
              <flc-text-truncated [data]="data?.item?.io_code"></flc-text-truncated>
            </ng-container>
            <!-- 大货单号 -->
            <ng-container *ngIf="data.isTd && data.key === 'bulk_codes'">
              <flc-text-truncated [data]="data?.item?.bulk_codes?.join('、')"></flc-text-truncated>
            </ng-container>
            <!-- 加工厂 -->
            <ng-container *ngIf="data.isTd && data.key === 'distribution_factory_name'">
              <flc-text-truncated
                [data]="
                  data?.item?.distribution_factory_name &&
                  data?.item?.distribution_factory_name.length &&
                  data?.item?.distribution_factory_name.join('、')
                "></flc-text-truncated>
            </ng-container>
            <!-- 交付日期 -->
            <ng-container *ngIf="data.isTd && data.key === 'po_due_times'">
              <flc-text-truncated
                [data]="
                  data?.item?.po_due_times && data?.item?.po_due_times.length && data?.item?.po_due_times.join('、')
                "></flc-text-truncated>
            </ng-container>

            <ng-container *ngIf="['employee_id', 'merchandiser_id', 'qc_id'].includes(data.key)">
              <nz-form-item>
                <nz-form-control [flcErrorTip]="data?.col?.label">
                  <flss-department-select
                    [canSelectDepartment]="false"
                    (onSelectChange)="onEmployeeChange($event, data.index, data?.col)"
                    [formControlName]="data.key"
                    [selectCurrentUser]="false"
                    [treeOptions]="departmentOptions">
                  </flss-department-select>
                </nz-form-control>
              </nz-form-item>
            </ng-container>
          </ng-container>

          <ng-container *ngIf="data.isAction">
            <i
              nz-icon
              nz-popover
              nz-tooltip
              [nzTooltipTitle]="'flss.btn.delete' | translate"
              [ngClass]="lines.length > 1 ? 'delete-icon' : 'delete-icon-disable'"
              (flcClickStop)="lines.length > 1 && handleDeleteRow(data.index)"
              [nzIconfont]="'icon-caozuolan_shanchu1'"></i>
          </ng-container>
        </ng-template>
      </flc-table>
    </div>
  </form>
</div>

<div class="bottomBar">
  <button nz-button flButton="default" nzShape="round" type="button" (click)="onCancel()">{{ 'flss.btn.cancel' | translate }}</button>
  <button [flcDisableOnClick]="1000" nz-button nzType="primary" nzShape="round" type="button" (click)="onOk()">
    {{ 'flss.btn.ok' | translate }}
  </button>
</div>
