import { Component, EventEmitter, Inject, Input, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { resizable } from 'fl-common-lib';
import { MaterialPackageComponent } from 'fl-sewsmart-lib/material-package';
import { ColorSizeModel } from 'fl-sewsmart-lib/material-package/models/material-package.interface';
import { SampleOutsouringStatusEnum } from '../../modal/sample-outsourcing.enum';

@Component({
  selector: 'app-sample-oursourcing-tech-archive',
  templateUrl: './sample-oursourcing-tech-archive.component.html',
  styleUrls: ['./sample-oursourcing-tech-archive.component.scss'],
})
@resizable()
export class SampleOursourcingTechArchiveComponent implements OnInit {
  @ViewChild('materialPackageRef') materialPackageRef!: MaterialPackageComponent;

  @Input() pkgData: any;
  @Output() toggleFullScreen = new EventEmitter<boolean>();
  @Output() editPackage = new EventEmitter();
  env_project = 'elan'; //默认系统环境变量

  isFullScreenMode = false; // 是否全屏模式
  isTooltipVisible = true; // 是否显示tooltip

  orderInfo!: {
    id: string;
    code: string;
    uuid: string;
    style_uuid: string;
    color: Array<ColorSizeModel>;
    size: Array<ColorSizeModel>;
    io_lines: any[];
  };

  materialPackageHeight = 0;

  ngOnInit(): void {
    (this as any).addResizePageListener();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes && changes.pkgData && changes.pkgData.currentValue) {
      this.handleData();
    }
  }

  ngOnDestroy(): void {
    (this as any).removeResizePageListener();
  }

  ngAfterViewInit() {
    this.resizePage();
  }

  resizePage() {
    setTimeout(() => {
      const windowHeight = window.innerHeight;
      // 页面上方有提示信息块的时候，要减去对应高度
      const hasTip = [SampleOutsouringStatusEnum.wait_modify].includes(this.pkgData.status);
      const height = this.isFullScreenMode ? windowHeight - (hasTip ? 240 : 200) : 500;
      this.materialPackageHeight = height > 500 ? height : 500;
    });
  }

  handleData() {
    const colorData: ColorSizeModel[] = [];
    const sizeData: ColorSizeModel[] = [];
    const colorSet = new Set();
    const sizeSet = new Set();
    this.pkgData?.color_size?.forEach((item: any) => {
      if (!colorSet.has(item.color_info.color_code)) {
        colorSet.add(item.color_info.color_code);
        const colorItem: { name: string; value: number; code: string; sizes: Array<{ name: string; value: number; code: string }> } = {
          name: item.color_info.color_name,
          value: item.color_info.color_id,
          code: item.color_info.color_code,
          sizes: [],
        };
        colorItem.sizes.push({ name: item.size_info.spec_size, value: item.size_info.spec_id, code: item.size_info.spec_code });
        colorData.push(colorItem);
      } else {
        const color = colorData.find((color: any) => color.code === item.color_info.color_code);
        color?.sizes?.push({ name: item.size_info.spec_size, value: item.size_info.spec_id, code: item.size_info.spec_code });
      }
      if (!sizeSet.has(item.size_info.spec_code)) {
        sizeSet.add(item.size_info.spec_code);
        sizeData.push({
          name: item.size_info.spec_size,
          value: item.size_info.spec_id,
          code: item.size_info.spec_code,
        });
      }
    });

    this.orderInfo = {
      id: this.pkgData.sample_order_id,
      code: this.pkgData.sample_order_no,
      uuid: this.pkgData.uuid,
      style_uuid: this.pkgData.style_uuid,
      io_lines: this.pkgData.color_size,
      color: colorData,
      size: sizeData,
    };
  }

  onToggleFullScreen() {
    this.resetFullScreen();
    this.toggleFullScreen.emit(this.isFullScreenMode);
  }

  resetFullScreen() {
    this.isFullScreenMode = !this.isFullScreenMode;
    this.isTooltipVisible = false;
    setTimeout(() => {
      this.isTooltipVisible = true;
    }, 150);
    this.resizePage();
  }

  onViewVersions() {
    this.materialPackageRef.openHistoryDrawer();
  }
}
