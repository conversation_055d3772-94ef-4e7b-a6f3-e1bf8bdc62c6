import { DatePipe } from '@angular/common';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { Router } from '@angular/router';
import { TranslatePipe } from '@ngx-translate/core';
import { endOfDay, format, startOfDay } from 'date-fns';
import {
  resizable,
  FlcTableComponent,
  FlcUtilService,
  FlcDrawerHelperService,
  FlcTableHelperService,
  FlcModalService,
} from 'fl-common-lib';
import { isNotNil } from 'ng-zorro-antd/core/util';
import { finalize, Subscription } from 'rxjs';
import { FormBuilder } from '@angular/forms';
import { OrderOutsourcingService } from '../../components/order-outsourcing.service';
import { AppStorageService } from 'src/app/shared/app-storage.service';
import { AssignTypeEnum, OrderStatus, SampleOperateBtnEnum, SampleOperateLabelEnum } from '../models/order-garment-outsourcing.enum';
import { OutsourcingListInterface } from '../models/order-garment-outsourcing.interface';
import { OrderGarmentOutsourcingService } from '../order-garment-outsourcing.service';
import { DepartmentData } from '../../components/department-select/department-select.component';
import { RecommendFactoryService } from 'fl-sewsmart-lib/recommend-factory';
import { NzTabChangeEvent, NzTabSetComponent } from 'ng-zorro-antd/tabs';
import { TableDataViewComponent } from 'fl-sewsmart-lib/common-component';
import { AssignEmpDrawer } from '../components/assign-emp-drawer/assign-emp-drawer.component';
import { AssignComponentConfig, commonColumn } from './config';
import { NzMessageService } from 'ng-zorro-antd/message';
const _LOCAL_STORAGE_TABSET_INDEX_KEY = 'order-garment-outsourcing-list_tabset-index';

@Component({
  selector: 'app-order-garment-outsourcing-list',
  templateUrl: './order-garment-outsourcing-list.component.html',
  styleUrls: ['./order-garment-outsourcing-list.component.scss'],
  providers: [TranslatePipe, DatePipe],
})
@resizable()
export class OrderGarmentOutsourcingListComponent implements OnInit {
  searchOptionFetchUrl = this._service.optionsUrl;
  searchOptionFactoryUrl = this.outsourcingService.plantOptions;
  sampleOperateBtnEnum = SampleOperateBtnEnum;
  AssignTypeEnum = AssignTypeEnum;
  OrderStatus = OrderStatus;
  searchList = [
    {
      label: '订单需求号',
      labelKey: 'io_code',
      valueKey: 'io_code',
      type: 'select',
    },
    {
      label: '大货单号',
      labelKey: 'bulk_code',
      valueKey: 'bulk_code',
      type: 'select',
    },
    {
      label: '批次',
      labelKey: 'order_category',
      valueKey: 'order_category',
      type: 'select',
    },
    {
      label: '订单标签',
      labelKey: 'order_label',
      valueKey: 'order_label',
      type: 'select',
    },
    {
      label: '生产类型',
      labelKey: 'order_production_type',
      valueKey: 'order_production_type',
      type: 'select',
    },
    {
      label: '品牌',
      labelKey: 'brand',
      valueKey: 'brand',
      type: 'select',
    },
    {
      label: '款号',
      labelKey: 'style_code',
      valueKey: 'style_code',
      type: 'select',
    },
    {
      label: '加工厂',
      labelKey: 'factory_code',
      valueKey: 'factory_code',
      payload: { production_type: 1 },
      type: 'select',
    },
    {
      label: '状态',
      labelKey: 'status',
      valueKey: 'status',
      type: 'select',
      canSearch: false,
      alwaysReload: true,
    },
    {
      label: '客户名称',
      labelKey: 'customer',
      valueKey: 'customer',
      type: 'input',
    },
    {
      label: '创建人',
      labelKey: 'gen_user',
      valueKey: 'gen_user',
      type: 'select',
    },
    { label: '业务员', labelKey: 'biz_user_emp', valueKey: 'biz_user_emp_id', type: 'select' },
    { label: '负责人', labelKey: 'employee_name', valueKey: 'employee_id', type: 'select' },
    { label: '跟单员', labelKey: 'merchandiser', valueKey: 'merchandiser_id', type: 'select' },
    { label: 'QC', labelKey: 'qc', valueKey: 'qc_id', type: 'select' },
    {
      label: '是否预排单',
      labelKey: 'is_pre_order',
      valueKey: 'is_pre_order',
      type: 'local-select',
      options: [
        { label: '是', value: '1' },
        { label: '否', value: '2' },
      ],
    },
    {
      label: '是否使用生产计划',
      labelKey: 'is_use_plan',
      valueKey: 'is_use_plan',
      type: 'local-select',
      options: [
        { label: '是', value: 'true' },
        { label: '否', value: 'false' },
      ],
    },
    // {
    //   label: '创建时间',
    //   labelKey: 'gen_time',
    //   valueKey: 'gen_time',
    //   type: 'date',
    // },
    // 工段 非整单下展示
    {
      label: '工段',
      labelKey: 'factory_stage_name',
      valueKey: 'factory_stage_name',
      type: 'select',
    },
  ]?.map((item) => {
    return {
      visible: true,
      ...item,
    };
  });

  searchParams: any = {
    status: null,
    io_code: null,
    order_category: null,
    order_label: null,
    order_production_type: null,
    brand: null,
    style_code: null,
    customer_code: null,
    style: null,
    customer: null,
    contract_number: null,
    order_status: null,
    order_date: null,
    due_time: null,
    geb_user: null,
    create_time: null,
    keywords: null,
    employee_name: null,
    biz_user_emp_id: null,
    merchandiser_id: null,
    qc_id: null,
  };
  _searchParams: any;

  tableConfig: any = {
    version: '1.0.3',
    translateName: 'outsourcingTableHeaderAndLabel.',
    hasCheckbox: true,
    trCheck: true,
    detailBtn: true,
    dataList: [],
    count: 40,
    height: 500,
    loading: false,
    pageSize: 20,
    actionWidth: '112px',
    pageIndex: 1,
    uniqueId: 'id',
  };
  commonTableHeaderConfig = {
    visible: true,
    pinned: false,
    disable: false,
    resizeble: true,
    sort: false,
  };
  tableHeaders = [
    {
      label: '订单需求号',
      key: 'io_code',
      width: '120px',
      visible: true,
      isHidePin: true,
      pinned: true,
      disable: false,
      type: 'template',
      templateName: 'io_code',
    },
    {
      label: '大货单号',
      key: 'bulk_codes',
      width: '120px',
      visible: true,
      isHidePin: true,
      pinned: true,
      disable: false,
      type: 'template',
      templateName: 'bulk_codes',
    },
    { label: '批次', key: 'order_category_label', width: '96px', type: 'text', ...this.commonTableHeaderConfig },
    {
      label: '订单标签',
      key: 'order_labels',
      width: '96px',
      type: 'template',
      templateName: 'order_labels',
      ...this.commonTableHeaderConfig,
    },
    { label: '生产类型', key: 'order_production_type_label', width: '96px', type: 'text', ...this.commonTableHeaderConfig },
    { label: '品牌', key: 'brand_name', width: '96px', type: 'text', ...this.commonTableHeaderConfig },
    { label: '款号', key: 'style_code', width: '112px', type: 'text', ...this.commonTableHeaderConfig },
    {
      label: '款式分类',
      key: 'material_name',
      formatter: this.categoryFormatter.bind(this),
      width: '136px',
      type: 'text',
      ...this.commonTableHeaderConfig,
    },
    {
      label: '款式图片',
      key: 'order_pictures',
      width: '80px',
      type: 'image',
      ...this.commonTableHeaderConfig,
    },
    { label: '客户名称', key: 'customer', width: '96px', type: 'text', ...this.commonTableHeaderConfig },
    { label: '总件数', key: 'qty', width: '112px', type: 'quantity', ...this.commonTableHeaderConfig },
    { label: '已入库件数', key: 'incoming_total', width: '112px', type: 'quantity', ...this.commonTableHeaderConfig }, // 已入库件数
    {
      label: '交付日期',
      key: 'po_due_times',
      width: '144px',
      type: 'template',
      templateName: 'po_due_times',
      ...this.commonTableHeaderConfig,
      sort: true,
      sortOrderBy: null,
    },
    {
      label: '加工厂',
      key: 'distribution_factory_name',
      width: '160px',
      type: 'template',
      ...this.commonTableHeaderConfig,
      templateName: 'distribution_factory_name',
    },
    {
      label: '状态',
      key: 'order_status_value',
      style: this.setStyle.bind(this),
      width: '96px',
      minWidth: 88,
      type: 'text',
      ...this.commonTableHeaderConfig,
    },
    { label: '负责人', key: 'employee_name', width: '96px', type: 'text', ...this.commonTableHeaderConfig },
    {
      label: '跟单员',
      key: 'merchandisers',
      width: '96px',
      ...this.commonTableHeaderConfig,
      type: 'template',
      templateName: 'merchandisers',
    },
    { label: 'QC', key: 'qcs', width: '96px', ...this.commonTableHeaderConfig, type: 'template', templateName: 'qcs' },
    {
      label: '创建时间',
      key: 'gen_time',
      width: '104px',
      type: 'datetime',
      minWidth: 88,
      ...this.commonTableHeaderConfig,
      sort: true,
      sortOrderBy: null,
    },
    { label: '创建人', key: 'gen_user', width: '96px', type: 'text', ...this.commonTableHeaderConfig },
    { label: '业务员', key: 'biz_user_name', width: '96px', type: 'text', ...this.commonTableHeaderConfig },
    {
      label: '接单状态',
      key: 'accept_status',
      width: '96px',
      type: 'text',
      ...this.commonTableHeaderConfig,
      formatter: (item: any) => {
        return ['', '未接单', '部分接单', '已接单'][item.accept_status];
      },
    },
    {
      label: '是否预排单',
      key: 'is_pre_order',
      visible: true,
      width: '120px',
      type: 'text',
      pinned: false,
      disable: false,
      formatter: (row: any) => {
        return row.is_pre_order === 1 ? '是' : '否';
      },
    },
    {
      label: '是否使用生产计划',
      key: 'is_use_plan',
      visible: true,
      width: '130px',
      type: 'text',
      pinned: false,
      disable: false,
      formatter: (row: any) => {
        return row.is_use_plan ? '是' : '否';
      },
    },
    { label: '备注', key: 'remark', width: '130px', type: 'text', ...this.commonTableHeaderConfig },
  ];
  constructor(
    private _router: Router,
    public _service: OrderGarmentOutsourcingService,
    private outsourcingService: OrderOutsourcingService,
    private _notice: NzNotificationService,
    private datepipe: DatePipe,
    private _fb: FormBuilder,
    private _storage: AppStorageService,
    private _utildator: FlcUtilService,
    private _recommendFactoryService: RecommendFactoryService,
    private _drawer: FlcDrawerHelperService,
    private _msg: NzMessageService,
    private _tableHelper: FlcTableHelperService,
    private _flcModalService: FlcModalService
  ) {}
  tabsetIndex = 0;
  @ViewChild('tabsetContainer') tabsetContainer!: NzTabSetComponent;
  @ViewChild('tableDataView') tableDataView!: TableDataViewComponent;
  @ViewChild('searchContainer') searchContainer!: ElementRef<HTMLElement>;
  @ViewChild('tableRef') tableRef!: FlcTableComponent;
  translateSubject?: Subscription; // 国际化监听
  detailBackSubject?: Subscription; // 订阅详情返回事件
  orderBy: Array<string> = []; // 排序
  selectedId: any = 0;
  warehouseData = [];
  checkedInfo!: { count: number; list: any[] };
  orderStatusList: Array<any> = [];
  modalVisible = 0;
  selectedEmployee?: DepartmentData;
  batchReturnData: any[] = [];
  factoryData = [];
  private isLoadingData = false; // 防重复请求标志
  private requestSequence = 0; // 请求序列号，用于处理竞态条件
  private isUpdatingTabIndex = false; // 防止tab索引更新时的循环调用
  private requestTimeoutTimer: any; // 请求超时定时器
  private lastPanelSelectValue: any = undefined; // 记录上次面板选择的值
  public panelSelectDisabled = false; // 临时禁用面板选择
  tabsetChange(event: NzTabChangeEvent) {
    const newIndex = event.index ?? 0;

    // 防止循环调用
    if (this.isUpdatingTabIndex) {
      return;
    }

    // 如果tab索引没有变化，不需要重新获取数据
    if (this.tabsetIndex === newIndex) {
      return;
    }

    this.isUpdatingTabIndex = true;
    this.panelSelectDisabled = true; // 临时禁用面板选择

    // 简单地递增请求序列号，使之前的请求失效
    this.requestSequence++;
    // 强制重置加载状态，确保可以发送新请求
    this.forceResetLoadingState();

    this.tabsetIndex = newIndex;
    const status = this.searchList.find((i) => i.valueKey === 'status')!;
    switch (this.tabsetIndex) {
      case 0:
        status.labelKey = 'status';
        break;
      case 1:
        status.labelKey = 'unsend_status';
        break;
      case 2:
        status.labelKey = 'send_status';
        break;
      default:
        break;
    }
    this.saveIndex();
    this.initOrderStatus();
    this.reset();

    // 重置标志，允许后续的tab切换
    setTimeout(() => {
      this.isUpdatingTabIndex = false;
      this.panelSelectDisabled = false; // 重新启用面板选择
    }, 300); // 300ms后重新启用
  }
  getIndex() {
    const index = localStorage.getItem('orderGarmentOutsourcingIndex');
    if (index) {
      this.tabsetIndex = Number(index);
    } else {
      this.tabsetIndex = 0;
    }
  }
  saveIndex() {
    localStorage.setItem('orderGarmentOutsourcingIndex', this.tabsetIndex.toString());
  }

  // 是否展示指派按钮
  get showAssigneeBtn() {
    return (
      this._service.btnArr.includes('bulk:garment-outsourcing-assignee-charge') ||
      this._service.btnArr.includes('bulk:garment-outsourcing-assignee-merchandiser') ||
      this._service.btnArr.includes('bulk:garment-outsourcing-assignee-qc')
    );
  }

  ngOnInit(): void {
    // 判断是否整单，控制是否显示工段筛选项和工段表头
    const localIsFullOrder = localStorage.getItem('isFullOrder');
    this.isFullOrder = localIsFullOrder ? localIsFullOrder === '1' : this.isFullOrder;
    this.switchIsFullOrder(this.isFullOrder);

    this.getIndex();
    this._service.btnArr = [...this._storage.getUserActions('outsourcing-manage/garment-outsourcing')];
    this._searchParams = JSON.parse(JSON.stringify(this.searchParams));
    this.initOrderStatus();
    // this.getDataList();
    this.translateSubject = this._service.translateEventEmitter.subscribe(() => {
      this.resizePage();
    });
    (this as any).addResizePageListener();
    /*
     * 订阅详情页返回事件
     */
    this.detailBackSubject = this._service.eventEmitter.subscribe((res) => {
      if (res === 'refresh') {
        this.getDataList();
      }
    });

    this.getDepartment();
  }

  /**
   * 获取所有部门人员
   */
  departmentOptions: any[] = [];
  getDepartment() {
    this._service.getDepartmentTree().subscribe((res: any) => {
      if (res?.code === 200) {
        this.departmentOptions = res?.data?.children || [];
      }
    });
  }

  resizePage() {
    setTimeout(() => {
      const staticViewContentH = this.tableDataView?.contentHeight() ?? 0;
      const height =
        window.innerHeight -
        this.searchContainer?.nativeElement?.clientHeight -
        (this.tabsetContainer.tabNavBarRef.navWarpRef?.nativeElement?.clientHeight ?? 0) -
        147 -
        staticViewContentH;

      this.tableConfig = { ...this.tableConfig, height: height };
    });
  }

  initOrderStatus() {
    const orderStatusList = [
      {
        label: '全部',
        value: OrderStatus.all,
        checked: true,
        isTotalItem: true,
        key: 'all',
      },
      {
        label: '待提交',
        value: OrderStatus.toSubmit,
        key: 'to_submit',
        send_status: 1,
      },
      {
        label: '待审核',
        value: OrderStatus.toAudit,
        key: 'to_audit',
        send_status: 1,
      },
      {
        label: '待修改',
        value: OrderStatus.toModify,
        key: 'to_modify',
        send_status: 1,
      },
      {
        label: '审核通过',
        value: OrderStatus.auditPass,
        key: 'audit_pass',
        send_status: 2,
      },
      {
        label: '修改待审核',
        value: OrderStatus.toModifyAudit,
        key: 'to_modify_audit',
        send_status: 2,
      },
      {
        label: '修改未通过',
        value: OrderStatus.modifyAuditReturn,
        key: 'modify_audit_return',
        send_status: 2,
      },
      {
        label: '已取消',
        value: OrderStatus.cancelled,
        key: 'cancelled',
        send_status: 2,
      },
    ];
    const index = this.tabsetIndex;
    this.orderStatusList = index === 0 ? orderStatusList : orderStatusList.filter((item) => item.isTotalItem || item.send_status === index);
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.resizePage();
    }, 0);
  }

  ngOnDestroy(): void {
    this.translateSubject?.unsubscribe();
    this.detailBackSubject?.unsubscribe();
    (this as any).removeResizePageListener();
    // 清理所有定时器
    if (this.requestTimeoutTimer) {
      clearTimeout(this.requestTimeoutTimer);
    }
  }
  /**
   *重置
   */
  reset() {
    const keywords = this.searchParams.keywords;
    this.searchParams = JSON.parse(JSON.stringify(this._searchParams));
    this.searchParams.keywords = keywords;
    this.tableHeaders.forEach((item: any) => {
      item.sortOrderBy = null;
    });
    this.orderBy = [];
    this.tableHeaders = [...this.tableHeaders];
    this.initOrderStatus();
    this.getDataList(true);
  }
  /**
   * input 输入框搜索
   */
  onSearch() {
    this.tableConfig.pageIndex = 1;
    this.getDataList();
  }
  onInputSearchValue(value: string) {
    this.searchParams.keywords = value.length ? value : null;
    this.tableConfig.pageIndex = 1;
    this.getDataList();
  }
  onOrderStatusChanged(val: any) {
    if (val) {
      this.searchParams.status = val;
    } else {
      this.searchParams.status = [];
    }
    this.tableConfig.pageIndex = 1;
    this.getDataList();
  }
  getDataList(reset = false) {
    // 防重复请求
    if (this.isLoadingData) {
      return;
    }

    // 生成新的请求序列号
    const currentSequence = ++this.requestSequence;

    this.isLoadingData = true;
    this.tableConfig = { ...this.tableConfig, loading: true, pageIndex: reset ? 1 : this.tableConfig.pageIndex };

    // 设置请求超时，防止请求卡住
    if (this.requestTimeoutTimer) {
      clearTimeout(this.requestTimeoutTimer);
    }
    this.requestTimeoutTimer = setTimeout(() => {
      if (this.isLoadingData && currentSequence === this.requestSequence) {
        this.forceResetLoadingState();
      }
    }, 30000); // 30秒超时
    const params = {
      page: this.tableConfig.pageIndex,
      limit: this.tableConfig.pageSize,
      order_by: this.orderBy,
      cache: true,
      where: this.handleWhere(),
      send_status: this.tabsetIndex,
    };
    const req = this.isFullOrder ? this._service.list(params) : this._service.getList2(params); // 整单&非整单
    req.subscribe({
      next: (res) => {
        // 清除超时定时器
        if (this.requestTimeoutTimer) {
          clearTimeout(this.requestTimeoutTimer);
        }

        // 检查是否是最新的请求，防止竞态条件
        if (currentSequence !== this.requestSequence) {
          return;
        }

        this.isLoadingData = false; // 请求完成，重置标志
        if (res.code === 200) {
          this.tableRef.clearAllSelected();
          res.data.data.forEach((item: any) => {
            item.po_due_times?.forEach((po_time: string, index: number) => {
              item.po_due_times[index] = this.datepipe.transform(po_time, 'yyyy/MM/dd');
            });
          });
          this.orderStatusList.forEach((item) => {
            item.amount = res?.data?.statistics[item.key];
          });
          this.tableConfig = { ...this.tableConfig, loading: false, count: res.data.total, dataList: [...res.data.data] };
        }
      },
      error: () => {
        // 清除超时定时器
        if (this.requestTimeoutTimer) {
          clearTimeout(this.requestTimeoutTimer);
        }

        // 即使请求失败，也要检查序列号
        if (currentSequence === this.requestSequence) {
          this.isLoadingData = false; // 请求失败，也要重置标志
        }
      },
    });
  }

  // 强制重置加载状态
  private forceResetLoadingState() {
    this.isLoadingData = false;
    this.tableConfig = { ...this.tableConfig, loading: false };
  }

  getCount(data: any) {
    this.checkedInfo = data;
  }
  // Tool
  //  分页
  onIndexChange(page: number) {
    this.tableConfig = { ...this.tableConfig, pageIndex: page };
    this.getDataList();
  }
  onSizeChange(size: number) {
    this.tableConfig = { ...this.tableConfig, pageSize: size };
    this.getDataList(true);
  }
  // 更改了排序方式
  sortOrderChange({ value, key }: { value: 'desc' | 'asc' | null; key: string }) {
    this.orderBy = value ? [key + ' ' + value] : [];
    this.getDataList();
  }
  // 跳转详情页面
  jumpDetail(event: any) {
    this.selectedId = event?.id;
    this._router.navigate(['/outsourcing-manage/garment-outsourcing/list/', event?.id], { queryParams: { io_id: event?.io_id || 'add' } });
  }
  // 查询参数打包
  handleWhere() {
    if (this.isFullOrder) {
      delete this.searchParams?.factory_stage_name;
    }
    const where: object[] = [];
    Object.entries(this.searchParams).forEach((item: any) => {
      if (
        (Array.isArray(item[1]) && item[1].length) ||
        ((typeof item[1] === 'string' || typeof item[1] === 'number') && isNotNil(item[1]) && item[1] !== '')
      ) {
        if ('gen_time' === item[0]) {
          if (Array.isArray(item[1]) && item[1].length > 0) {
            const startTime = format(startOfDay(item[1][0]), 'T');
            const endTime = format(endOfDay(item[1][1]), 'T');
            where.push({ column: item[0], op: 'between', value: [startTime, endTime].toString() });
          }
        } else if ('status' === item[0]) {
          where.push({ column: 'status', op: '=', value: `${item[1][0]}` });
        } else if (item[0] === 'order_label') {
          where.push({ column: item[0], op: '=', value: JSON.stringify([item[1]]) });
        } else {
          if ('customer' === item[0]) {
            where.push({ column: item[0], op: 'like', value: item[1] });
          } else {
            where.push({ column: item[0], op: '=', value: item[1] });
          }
        }
      }
    });
    return where;
  }
  setStyle(item: OutsourcingListInterface) {
    const styleObj: any = {};
    switch (item.order_status) {
      case OrderStatus.toSubmit: // 待提交
        styleObj.color = '#138AFF';
        break;
      case OrderStatus.toAudit: // 待审核
        styleObj.color = '#FB6401';
        break;
      case OrderStatus.toModifyAudit: // 修改待审核
        styleObj.color = '#FB6401';
        break;
      case OrderStatus.modifyAuditReturn: // 修改审核未通过
        styleObj.color = '#FB6401';
        break;
      case OrderStatus.toModify: // 待修改
        styleObj.color = '#FF4A1D';
        break;
      case OrderStatus.cancelled: // 已取消
        styleObj.color = '#97999C';
        break;
      default:
        styleObj.color = '#515661';
        break;
    }
    return styleObj;
  }
  // 款式分类
  categoryFormatter(item: OutsourcingListInterface) {
    let text = '';
    if (item.first_material_name) {
      text += item.first_material_name;
    }
    if (item.second_material_name) {
      text += '-' + item.second_material_name;
    }
    if (item.third_material_name) {
      text += '-' + item.third_material_name;
    }
    return text;
  }

  handleButtonAction(label: string) {
    if (!this.checkedInfo?.count) {
      this._notice.error(label === SampleOperateBtnEnum.export ? '请先选择需要导出的订单！' : '请至少选择一条数据', '');
      return;
    }
    const isInvalidCodes: string[] = [];
    const list: any[] = [];
    const ids: string[] = [];
    const io_uuids: string[] = [];

    switch (label) {
      case SampleOperateBtnEnum.pass:
        this.checkedInfo?.list?.forEach((item) => {
          if ([OrderStatus.toAudit, OrderStatus.toModifyAudit].includes(item.order_status)) {
            ids.push(item.id);
            io_uuids.push(item.io_uuid);
          } else {
            isInvalidCodes.push(item.io_code);
          }
        });
        this.onAuditBatch(ids, io_uuids);
        break;
      case SampleOperateBtnEnum.modify:
        this.checkedInfo?.list.forEach((item) => {
          [OrderStatus.toAudit, OrderStatus.toModifyAudit].includes(item.order_status)
            ? ids.push(item.id)
            : isInvalidCodes.push(item.io_code);
        });
        this._openOperateModal(ids);
        break;
      case SampleOperateBtnEnum.outGoing:
        this.checkedInfo?.list.forEach((item) => {
          [OrderStatus.toSubmit, OrderStatus.toAudit].includes(item.order_status) && item.distribution_factory_info.length <= 1
            ? list.push(item)
            : isInvalidCodes.push(item.io_code);
        });
        this._batchOutGoing(list);
        break;
      case SampleOperateBtnEnum.assignEmployee:
        this._openAssignDrawer(AssignTypeEnum.AssignEmployee);
        break;
      case SampleOperateBtnEnum.assignMerOrQc:
        // eslint-disable-next-line no-case-declarations
        const type = this.getAssignMerOrQcType();
        type && this._openAssignDrawer(type);
        break;
      case SampleOperateBtnEnum.complete:
        this.checkedInfo?.list.forEach((item) => {
          [3, 2].includes(item.accept_status) ? ids.push(item.id) : isInvalidCodes.push(item.io_code);
        });
        this.onComplete(ids);
        break;
      // 一键入库
      case SampleOperateBtnEnum.oneClickInbound:
        this.checkedInfo?.list.forEach((item) => {
          [OrderStatus.auditPass].includes(item.order_status) ? ids.push(...item.bulk_codes) : isInvalidCodes.push(...item.bulk_codes);
        });
        ids?.length > 0 && this.oneClickInbound(ids);
        break;
      //  预付款
      case SampleOperateBtnEnum.onAdvancePayment:
        this.checkedInfo?.list.forEach((item) => {
          [OrderStatus.auditPass].includes(item.order_status) ? list.push(item) : isInvalidCodes.push(item.io_code);
        });
        list?.length > 0 && this.onAdvancePayment(list);
        break;
      // 导出
      case SampleOperateBtnEnum.export:
        this.checkedInfo?.list.forEach((item) => {
          list.push(item);
        });
        list?.length && this.onExport(list);
        break;
    }

    if (isInvalidCodes.length) {
      let msg = '';
      switch (label) {
        case SampleOperateBtnEnum.oneClickInbound:
          msg = `${isInvalidCodes.join('、')}大货单未审核，无法一键入库！`;
          break;
        case SampleOperateBtnEnum.onAdvancePayment:
          msg = `订单需求${isInvalidCodes.join('、')}未审核，无法预付款！`;
          break;
        default:
          msg = `大货单号${isInvalidCodes.join('、')}不能操作${SampleOperateLabelEnum[label]}`;
      }
      this._notice.warning(msg, '');
    }
  }

  // 根据权限获取当前是指派跟单员还是QC
  getAssignMerOrQcType() {
    const merchandiserPermission = this._service.btnArr.includes('bulk:garment-outsourcing-assignee-merchandiser');
    const qcPermission = this._service.btnArr.includes('bulk:garment-outsourcing-assignee-qc');
    if (merchandiserPermission && qcPermission) {
      return AssignTypeEnum.AssignMerOrQc;
    } else if (merchandiserPermission && !qcPermission) {
      return AssignTypeEnum.AssignMerchandiser;
    } else if (!merchandiserPermission && qcPermission) {
      return AssignTypeEnum.AssignQC;
    } else {
      return null;
    }
  }

  // 批量审核
  private onAuditBatch(ids: string[], io_uuids: string[]) {
    if (!ids.length) return;
    this.outsourcingService
      .confirmDialog('确定审核通过？', '审核通过后订单状态变为待外发厂接单', true, false)
      .afterClose.subscribe((confirm) => {
        if (confirm) {
          this._service.passList({ ids: ids }).subscribe((res: any) => {
            if (res.code === 200) {
              if (res.data?.msg?.length) {
                this._notice.error(res.data.msg, '');
              } else {
                this._notice.success('操作成功', '');
              }
              this.getDataList();
              this._service.batchCreateProductionTable({ io_out_sourcing_ids: ids, operation_type: 1 }).subscribe((res) => {});
              this._service.createMaterialShipmentPlan(io_uuids).subscribe((res) => {});
            }
          });
        }
      });
  }

  /**
   * 退回修改
   */
  private _openOperateModal(ids: string[]) {
    if (!ids.length) return;
    this.outsourcingService.confirmDialogWithReason().afterClose.subscribe((result) => {
      if (result?.success) {
        this._service.modifyList({ ids: ids, reason: result.reason }).subscribe((res) => {
          if (res.code === 200) {
            if (res.data?.msg?.length) {
              this._notice.error(res.data.msg, '');
            } else {
              this._notice.success('操作成功', '');
            }
            this.getDataList();
          }
        });
      }
    });
  }

  handleTreeOptions(value: any[]): any[] {
    const ret: any[] = [];
    if (value.length) {
      value.forEach((node: any) => {
        const selectable = !node.children?.length && node.user_id;
        ret.push({
          value: node.user_id ?? node.key,
          label: node.title,
          isLeaf: node.isLeaf,
          selectable: selectable,
          disabled: !selectable && node.isLeaf,
          children: this.handleTreeOptions(node?.children ?? []),
        });
      });
    }
    return ret || [];
  }

  // 大货单号自动生成
  autoFillBulkcode(item: any, index: number) {
    let bulkOrderCode = item?.bulk_code;
    if (!bulkOrderCode || bulkOrderCode === '') {
      bulkOrderCode = item.io_code + (index + 1).toString().padStart(3, '0');
    }
    return bulkOrderCode;
  }

  // 外发工厂
  private _batchOutGoing(list: any[]) {
    if (!list.length) return;
    this.batchReturnData = list.map((item: any) => {
      return {
        id: item.id,
        bulk_code: this.autoFillBulkcode(item, 0),
        factory_code: item.distribution_factory_info?.length === 1 ? item.distribution_factory_info[0].factory_name : null,
        ss_factory_code: item.distribution_factory_info?.length === 1 ? item.distribution_factory_info[0].ss_factory_code : null,
        unit_price: item.distribution_factory_info?.length === 1 ? item.distribution_factory_info[0].unit_price : null,
        tax_rate: item.distribution_factory_info?.length === 1 ? item.distribution_factory_info[0].tax_rate : 0,
        tax_price: item.distribution_factory_info?.length === 1 ? item.distribution_factory_info[0].tax_price : null,
        ...item,
      };
    });
    this.modalVisible = 1;
  }

  // 处理为大货单维度
  formatterToIODemission() {
    const validList: any[] = [];
    const valueToArray = (value: string | number) => {
      return value ? [value] : null;
    };
    this.checkedInfo?.list?.forEach((item: any) => {
      item?.bulk_codes?.forEach((bulk_code: string) => {
        validList.push({
          ...item,
          bulk_codes: valueToArray(bulk_code),
          merchandiser_id: item?.merchandisers?.find((mer: any) => mer?.bulk_code === bulk_code)?.merchandiser_id,
          merchandiser_name: item?.merchandisers?.find((mer: any) => mer?.bulk_code === bulk_code)?.merchandiser_name,
          qc_id: item?.qcs?.find((mer: any) => mer?.bulk_code === bulk_code)?.qc_id,
          qc: item?.qcs?.find((mer: any) => mer?.bulk_code === bulk_code)?.qc,
          distribution_factory_name: valueToArray(item?.merchandisers?.find((mer: any) => mer?.bulk_code === bulk_code)?.factory_name),
          factory_id: item?.merchandisers?.find((mer: any) => mer?.bulk_code === bulk_code)?.factory_id,
          factory_out_sourcing_id: item?.merchandisers?.find((mer: any) => mer?.bulk_code === bulk_code)?.factory_out_sourcing_id ?? null,
        });
      });
    });
    return validList;
  }

  // 指派负责人 或 指派跟单员/QC
  private _openAssignDrawer(type: AssignTypeEnum) {
    let validList: any[] = [];
    const inValidList: any[] = [];
    if (type === AssignTypeEnum.AssignEmployee) {
      // 指派业务员,需校验订单需求关联的生产进度表的进度
      this.checkedInfo?.list.forEach((item) => {
        !item?.is_start_production ? validList.push(item) : inValidList.push(item);
      });
      if (inValidList?.length) {
        const strs = inValidList?.map((e: any) => `【${e?.io_code}】`);
        this._notice.error('', '订单需求:' + strs.join('') + '已开始生产，不能切换负责人');
      }
    } else {
      // 指派跟单员/QC时，按大货单号维度
      const list = this.formatterToIODemission();
      list?.forEach((item: any) => {
        item?.distribution_factory_name?.[0] ? validList.push(item) : inValidList.push(item);
      });
      if (inValidList?.length) {
        const strs = inValidList?.map((e: any) => `大货单号【${e?.bulk_codes?.[0] || ''}】`);
        // 【大货单号：xxxx】【大货单号：xxx】未分配加工厂，不可指派跟单员/QC
        this._notice.error('', strs.join('') + '未分配加工厂，不可指派跟单员/QC');
      }
    }
    if (!validList?.length) return;
    const tableHeaders = this.tableHeaders?.filter((e: any) => e.visible && commonColumn.includes(e.key));
    this._drawer
      .openDrawer({
        title: '指派' + AssignComponentConfig[type]?.title,
        height: 'calc(100% - 48px)',
        bodyStyle: {
          padding: '13px 16px 0 16px',
        },
        content: AssignEmpDrawer,
        contentParams: {
          departmentOptions: this.departmentOptions,
          assignType: type,
          list: validList,
          batchConfig: AssignComponentConfig[type]?.batchConfig || [],
          tableHeaders: [...tableHeaders, ...AssignComponentConfig[type].editColumn],
        },
      })
      .subscribe((res) => {
        if (res?.isSave) {
          // 指派后，需要更新生产跟踪（过滤已取消的）
          const ids =
            validList
              ?.filter((line: any) => line?.order_status !== OrderStatus.cancelled)
              ?.map((item: any) => {
                return item?.id;
              }) || [];
          if (ids?.length) {
            this._service.batchCreateProductionTable({ io_out_sourcing_ids: ids, operation_type: 1 }).subscribe((res) => {});
          }

          this.getDataList();
          this.tableRef.clearAllSelected();
        }
      });
  }

  private onComplete(ids: string[]) {
    if (!ids.length) return;
    this.outsourcingService.confirmDialog('确定批量完成？', '', true, false).afterClose.subscribe((confirm: boolean) => {
      if (confirm) {
        this._service.complete(ids).subscribe((res: any) => {
          if (res.code === 200) {
            if (res.data?.msg?.length) {
              this._notice.error(res.data.msg, '');
            } else {
              this._notice.success('操作成功', '');
            }
            this.getDataList();
          }
        });
      }
    });
  }
  /**
   * 外发工厂一键同步
   */
  onFactoryChange(e: any, item: any) {
    item.factory_code = e.selectLine?.label;
    // item.ss_factory_code = e.selectLine?.value;
  }

  onFactoryAsyn(e: any) {
    this.batchReturnData.forEach((item: any) => {
      item.id = e.id;
      item.factory_code = e.factory_code;
      item.ss_factory_code = e.ss_factory_code;
      item.tax_price = e.tax_price;
      item.tax_rate = e.tax_rate || 0;
      item.unit_price = e.unit_price;
    });
  }

  modalHandleCancel() {
    this.modalVisible = 0;
  }

  // 版师下拉
  onSelectChange(e: DepartmentData) {
    this.selectedEmployee = e;
  }
  priceChange(e: any, item: any, type: 'unit' | 'tax') {
    const rate = this._utildator.accDiv(type === 'unit' ? item.tax_rate || 0 : e || 0, 100);
    const percent = this._utildator.accAdd(1, rate);
    item.tax_price = this._utildator.accMul(type === 'unit' ? e || 0 : item.unit_price || 0, percent);
  }
  // 修改单价
  UnitPriceChange(e: any, item: any) {
    this.priceChange(e, item, 'unit');
  }
  // 修改税率
  taxRateChange(e: any, item: any) {
    this.priceChange(e, item, 'tax');
  }
  modalHandleConfirmed() {
    if (this.modalVisible === 1) {
      this.handleBatchReturnOkModal();
    } else if (this.modalVisible === 2) {
      this.handleBatchToSbOkModal();
    }
  }

  handleBatchReturnOkModal() {
    const data = this.batchReturnData.map((item: any) => {
      return {
        id: item.id,
        bulk_code: item.bulk_code,
        factory_code: item.ss_factory_code,
        tax_price: item.tax_price ? `${item.tax_price}` : '0',
        unit_price: item.unit_price ? `${item.unit_price}` : '0',
        tax_rate: item.tax_rate ? `${item.tax_rate}` : '0',
      };
    });
    const params = {
      items: data,
    };
    this._service.createList(params).subscribe((res) => {
      if (res.code === 200) {
        if (res.data?.msg?.length) {
          this._notice.error(res.data.msg, '');
        } else {
          this._notice.success('操作成功', '');
        }
        this.getDataList();
        this.tableRef.clearAllSelected();
        this.modalHandleCancel();
      }
    });
  }

  handleBatchToSbOkModal() {
    if (!this.checkedInfo?.count) {
      this._notice.error('请至少选择一条数据', '');
      return;
    }
    const ids = (this.checkedInfo?.list ?? []).map((item) => item.id);
    const employeeId = this.selectedEmployee?.user_id;
    const params = {
      ios_ids: ids,
      employee_id: employeeId,
    };
    this._service.assigneeEmployee(params).subscribe((res) => {
      if (res.code === 200) {
        if (res.data?.msg?.length) {
          this._notice.error(res.data.msg, '');
        } else {
          this._notice.success('操作成功', '');
        }
        this.getDataList();
        this.tableRef.clearAllSelected();
        this.modalHandleCancel();
      }
    });
  }

  // 选择推荐工厂
  async onSelectFactory(data: any) {
    const _payload = {
      extra: {
        order_uuid: data.io_uuid,
      },
    };
    if (!(await this._recommendFactoryService.checkRecommendRules(_payload))) return;
    this._recommendFactoryService
      .openDrawer({ isMultiple: false, orderType: 3 }, _payload, [data.ss_factory_code])
      .subscribe((res: any) => {
        if (res) {
          data.ss_factory_code = res.ss_factory_code;
          data.factory_name = res.factory_name;
        }
      });
  }

  handleTimeFilter(value: any) {
    const times = value.length ? [Number(value[0]), Number(value[1])] : null;
    this.searchParams.gen_time = times;
    this._searchParams.gen_time = times;
    this.onSearch();
  }

  // 包装方法，在模板中调用
  onPanelSelectWrapper(value: any) {
    // 在这里我们可以完全控制是否调用实际的处理方法
    if (!this.panelSelectDisabled) {
      this.handlePanelSelect(value);
    }
  }

  handlePanelSelect(value: any) {
    // 值去重：如果值没有变化，直接返回
    if (this.lastPanelSelectValue === value) {
      return;
    }

    // 立即执行，不需要防抖延迟
    this.handlePanelSelectInternal(value);
  }

  private handlePanelSelectInternal(value: any) {
    const statusMap: { [key: number]: number } = { 100: 1, 200: 2, 300: 0 };
    const newIndex = value == null ? 0 : statusMap[value] ?? 0;

    // 更新记录的值
    this.lastPanelSelectValue = value;

    // 防止循环调用
    if (this.isUpdatingTabIndex) {
      return;
    }

    // 如果tab索引没有变化，不需要重新获取数据
    if (this.tabsetIndex === newIndex) {
      return;
    }

    this.isUpdatingTabIndex = true;
    this.panelSelectDisabled = true; // 临时禁用面板选择

    // 简单地递增请求序列号，使之前的请求失效
    this.requestSequence++;
    // 强制重置加载状态，确保可以发送新请求
    this.forceResetLoadingState();

    this.tabsetIndex = newIndex;
    this.onSearch();

    // 重置标志，允许后续的tab切换
    setTimeout(() => {
      this.isUpdatingTabIndex = false;
      this.panelSelectDisabled = false; // 重新启用面板选择
    }, 300); // 300ms后重新启用
  }

  handleFolder() {
    this.resizePage();
  }

  // 格式化跟单员/QC
  formatterArr(arr = [], key: string) {
    const new_arr = new Set();
    arr?.forEach((item: any) => {
      new_arr.add(item[key]);
    });
    return Array.from(new_arr)?.join('、');
  }

  /************************ 是否整单 ************************/
  isFullOrder = true;
  switchIsFullOrder(e: boolean) {
    this.isFullOrder = e;
    localStorage.setItem('isFullOrder', e ? '1' : '0');
    this.tableConfig = { ...this.tableConfig, uniqueId: e ? 'id' : 'factory_out_sourcing_id' };
    // 筛选项工段是否展示
    const target = this.searchList?.find((item: any) => item.valueKey === 'factory_stage_name');
    if (target) target.visible = !e;

    // 表头工段是否展示：非整单下展示工段
    let headers: any[] = [];
    const path =
      this.tableConfig?.tableName == null
        ? `${this.tableConfig?.pathname ?? window.location.pathname}.table_config`
        : `${this.tableConfig?.pathname ?? window.location.pathname}.${this.tableConfig?.tableName}.table_config`;
    const localHeaderString = localStorage.getItem(path);

    headers = localHeaderString ? JSON.parse(localHeaderString)?.headers || [] : this.tableHeaders;
    const distribution_factory_name_index = headers.findIndex((item: any) => item.key === 'distribution_factory_name');
    const factory_stage_name_index = headers.findIndex((item: any) => item.key === 'factory_stage_name');
    if (distribution_factory_name_index > -1 && !e) {
      if (factory_stage_name_index > -1) return;
      // 在加工厂后添加工段列
      headers.splice(distribution_factory_name_index + 1, 0, {
        label: '工段',
        key: 'factory_stage_name',
        width: '160px',
        ...this.commonTableHeaderConfig,
        type: 'template',
        templateName: 'factory_stage_name',
      });
    } else {
      if (factory_stage_name_index !== -1) headers.splice(factory_stage_name_index, 1); // 删除工段列
    }
    if (localHeaderString) {
      localStorage.setItem(path, JSON.stringify({ headers, version: this.tableConfig.version }));
    }
    this.tableHeaders = [...this.tableHeaders];
    this.getDataList(true);
  }

  /************************ 一键入库 ************************/
  oneClickInbound(bulk_codes: string[] = []) {
    this._flcModalService.confirmCancel({ content: '确定一键入库？' }).afterClose.subscribe((res) => {
      if (!res) return;
      this._service.handleOneClickInbound({ bulk_codes: bulk_codes }).subscribe((res: any) => {
        if (res.code === 200) {
          this._msg.success('入库成功');
          this.tableRef?.clearAllSelected();
          // 非整单下单条记录生成入库单，自动跳转到入库单详情页；
          if (!this.isFullOrder && res?.data?.ids?.length === 1) {
            this._router.navigate(['/product-inventory/product-in-stock/scm/list', res?.data?.ids[0]]);
          } else {
            // 停留在当前页
          }
        }
      });
    });
  }

  /************************ 预付款 ************************/
  onAdvancePayment(list: any[] = []) {
    if (this.isFullOrder) {
      this._notice.error('请在非整单模式下操作', '');
      return;
    }
    const orders = list?.map((item: any) => {
      return {
        factory_out_sourcing_id: item?.factory_out_sourcing_id,
        io_uuid: item?.io_uuid,
      };
    });
    // eslint-disable-next-line no-debugger
    debugger;
    this._service.generateAdvancePayment({ orders: orders }).subscribe((_serviceRes: any) => {
      console.log(_serviceRes, '1234569');
      if (_serviceRes.code == 200) {
        const id = _serviceRes?.data?.id ?? 'new';
        this._router.navigate(['/settlement/manufacture-payment-request/list', id]); // 跳转到物料付款详情页
      } else {
        this._notice.error('预付款生成失败', '');
      }
    });
  }

  /************************ 导出 ************************/
  isExporting = false;
  onExport(list: any[] = []) {
    if (this.isExporting) return;
    this.isExporting = true;
    const io_ids = list.map((item: any) => item.id) || [];
    const factory_out_sourcing_ids = list.map((item: any) => item.factory_out_sourcing_id) || [];
    this._service
      .exportList2({ io_ids, factory_out_sourcing_ids })
      .pipe(
        finalize(() => {
          this.isExporting = false;
        })
      )
      .subscribe((res: any) => {
        if (res.code === 200) {
          const url: any = res?.data?.url;
          const link = document.createElement('a');
          link.style.display = 'none';
          link.href = url;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          this._notice.success('导出成功', '');
          this.tableRef?.clearAllSelected();
        } else {
          this._notice.error('导出失败', '');
        }
      });
  }
}
