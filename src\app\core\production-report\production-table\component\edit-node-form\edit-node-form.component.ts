import { Component, Input, OnInit } from '@angular/core';
import { ProductionTableService } from '../../production-table.service';
import {
  BatchEditTypeEnum,
  EventTypeEnum,
  FieldLabelEnum,
  FieldTypeEnum,
  NodeEditAuthEnum,
  FieldEditAuthEnum,
  FieldAuthEnum,
  AuthorityTypeEnum,
} from '../../production-table.enum';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import { FlcValidatorService } from 'fl-common-lib';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { differenceInDays } from 'date-fns';
import { ProductionTableOrderLineInfo } from '../../production-table.config';

@Component({
  selector: 'app-edit-node-form',
  templateUrl: './edit-node-form.component.html',
  styleUrls: ['./edit-node-form.component.scss'],
})
export class EditNodeFormComponent implements OnInit {
  @Input() node: any;
  @Input() isBatch = false;
  @Input() param: any; // 额外的参数
  @Input() orderInfo!: ProductionTableOrderLineInfo;

  validateForm = this._fb.group({});
  formConfig: any = [];
  basicInfoConfig: any = [
    { label: '订单需求号', key: 'bulk_order_code' },
    { label: '款式编码', key: 'style_code' },
    { label: '加工厂', key: 'factory_name' },
  ];
  template: any = null;
  translateName = 'ProductionTableTemplateFiled.';
  FieldTypeEnum = FieldTypeEnum;
  FieldLabelEnum = FieldLabelEnum;
  NodeEditAuthEnum = NodeEditAuthEnum;
  FieldEditAuthEnum = FieldEditAuthEnum;
  AuthorityTypeEnum = AuthorityTypeEnum;
  FieldAuthEnum = FieldAuthEnum;

  constructor(
    private _service: ProductionTableService,
    private _fb: FormBuilder,
    private _validator: FlcValidatorService,
    private _modal: NzModalService,
    private _message: NzMessageService,
    private _notify: NzNotificationService
  ) {}

  ngOnInit(): void {
    this.initForm();
  }

  getOptions(item: any) {
    if ([FieldTypeEnum.SingleChoice, FieldTypeEnum.MultipleChoice].includes(item.field_type)) {
      const attr = JSON.parse(item?.field_data);
      return attr?.option_name?.map((op: any) => {
        return {
          label: op?.label,
          value: op?.label,
        };
      });
    } else {
      return [];
    }
  }

  initForm() {
    this.formConfig = this.node?.node_temp_data_list?.map((item: any) => {
      return {
        label: item.field_name,
        key: item.field_name,
        tag: item.label,
        required: item.require === 1 || item.required === 1,
        type: item.field_type,
        is_edit: item?.is_edit,
        edit_auth: item?.edit_auth,
        field_data: item?.field_data,
        data: item?.data,
        options: this.getOptions(item),
      };
    });

    this.formConfig?.forEach((item: any) => {
      const _control = new FormControl();
      item.required && _control.addValidators(Validators.required);
      this.validateForm?.addControl(item.key, _control);

      const val: any = item?.data ? JSON.parse(item?.data) : null;
      if (item.type === FieldTypeEnum.Date) {
        this.validateForm?.get(item.key)?.setValue(val?.value ? new Date(Number(val?.value)) : null);
      } else {
        this.validateForm?.get(item.key)?.setValue(val?.value ?? null);
      }

      if (item.type === FieldTypeEnum.Percentage) {
        this.validateForm?.addControl(item.key, new FormControl());
      }
    });
  }

  getPercent(item: any) {
    const attr = JSON.parse(item?.field_data);
    const numerator = attr?.numerator; // 分子
    const denominator = attr?.denominator; // 分母
    const munValue = this.validateForm?.get(numerator)?.value;
    const denValue = this.validateForm?.get(denominator)?.value;
    if (!munValue || !denValue) {
      this.validateForm?.get(item.key)?.setValue(0, { emitViewToModelChange: false });
      return 0;
    }

    const val = ((Number(munValue) / Number(denValue)) * 100).toFixed(2);
    this.validateForm?.get(item.key)?.setValue(val, { emitViewToModelChange: false });
    return val;
  }

  // 附件上传
  onUploaded(val: any, item: any) {
    this.validateForm.get(item.key)?.setValue(val);
  }
  // 附件删除
  onDeleted(val: any, item: any) {
    this.validateForm.get(item.key)?.setValue(val);
  }

  isSave = false;
  async onSave() {
    this.isSave = true;
    const isInvalid = await this._validator.formIsAsyncInvalid(this.validateForm);
    if (isInvalid) {
      return;
    }
    this.isSave = false;
    const formData = this.validateForm?.getRawValue();

    const _data_list = (diffDay = 0) => {
      return this.node?.node_temp_data_list?.map((item: any) => {
        const val = formData?.[item.field_name];
        const it = { ...item };
        if (val || val === 0) {
          it['data'] = JSON.stringify({
            value: item.field_type === FieldTypeEnum.Date ? val.getTime() : val,
            delay: diffDay || 0,
          });
        } else {
          it['data'] = '';
        }

        return it;
      });
    };

    const payload = {
      id: this.node?.id,
      fixed_template: this.node?.fixed_template,
      content_type: this.node?.content_type,
    };

    // 计算计划时间和实际时间的时间差
    const actualDate = this.formConfig?.find((item: any) => item.tag === FieldLabelEnum.Actual && item.type === FieldTypeEnum.Date);
    const planDate = this.formConfig?.find((item: any) => item.tag === FieldLabelEnum.Plan && item.type === FieldTypeEnum.Date);
    const diff = actualDate && planDate ? this.calculateDateDifferenceInDays(formData?.[actualDate?.key], formData?.[planDate?.key]) : 0;

    // 校验计划数量和实际数量
    const actualNumber = this.formConfig?.find((item: any) => item.tag === FieldLabelEnum.Actual && item.type === FieldTypeEnum.Number);
    const planNumber = this.formConfig?.find((item: any) => item.tag === FieldLabelEnum.Plan && item.type === FieldTypeEnum.Number);
    const isLessThanPlan: boolean =
      actualNumber && planNumber && Number(formData?.[actualNumber?.key] || 0) < Number(formData?.[planNumber?.key] || 0);

    const _openNumberTipModal = (diffDay = 0) => {
      const _modal = this._modal.create({
        nzTitle: this._service.translateValue(this.translateName + '确认弹窗'),
        nzContent: this._service.translateValue(this.translateName + '实际数量小于计划数量，是否要确认提交？'),
        nzFooter: [
          {
            label: this._service.translateValue('btn.cancel'),
            onClick: () => {
              _modal.close();
            },
          },
          {
            label: this._service.translateValue('btn.confirm'),
            type: 'primary',
            onClick: () => {
              if (this.isBatch) {
                this.batchSave(diffDay);
              } else {
                this.save({
                  ...payload,
                  data_list: _data_list(diffDay),
                });
              }
            },
          },
        ],
      });
    };

    if (diff > 0) {
      const _delayModal = this._modal.create({
        nzTitle: this._service.translateValue(this.translateName + '确认弹窗'),
        nzContent: this._service.translateValue(this.translateName + '当前节点已逾期X天，是否要延期交货？', { day: diff }),
        nzFooter: [
          {
            label: this._service.translateValue(this.translateName + '确认不延期'),
            type: 'primary',
            onClick: () => {
              if (isLessThanPlan) {
                _delayModal.close();
                _openNumberTipModal(0);
              } else {
                this.isBatch
                  ? this.batchSave(0)
                  : this.save({
                      ...payload,
                      data_list: _data_list(0),
                    });
              }
            },
          },
          {
            label: this._service.translateValue(this.translateName + '确认延期'),
            type: 'primary',
            onClick: () => {
              if (isLessThanPlan) {
                _delayModal.close();
                _openNumberTipModal(diff);
              } else {
                this.isBatch
                  ? this.batchSave(diff)
                  : this.save({
                      ...payload,
                      data_list: _data_list(diff),
                    });
              }
            },
          },
        ],
      });
      return;
    }

    if (isLessThanPlan) {
      _openNumberTipModal();
      return;
    }

    if (this.isBatch) {
      this.batchSave(0);
    } else {
      this.save({
        ...payload,
        data_list: _data_list(0),
      });
    }
  }

  // 批量编辑节点
  batchSave(diffDay = 0) {
    const value = this.validateForm?.getRawValue();
    const dataList: any[] = [];
    this.node?.node_temp_data_list?.forEach((item: any) => {
      const val: any = {
        id: null,
        template_node_template_id: item?.node_template_id,
        template_node_template_field_id: item?.id,
        prod_status_node_id: null,
        is_edit: FieldEditAuthEnum.edit,
        field_name: item?.field_name,
        field_type: item?.field_type,
        label: item?.label,
      };
      if (value?.[item.field_name] || value?.[item.field_name] === 0) {
        val['data'] = JSON.stringify({
          value: item.field_type === FieldTypeEnum.Date ? value?.[item.field_name]?.getTime() : value?.[item.field_name],
          delay: diffDay || 0,
        });
      } else {
        val['data'] = '';
      }
      dataList.push(val);
    });
    this._service
      .updateBatchFieldOrNode({
        ids: this.param?.ids || [],
        type: BatchEditTypeEnum.node,
        dataList,
        template_node_id: this.node.template_node_id,
      })
      .subscribe((res: any) => {
        if (res?.code === 200) {
          this._message.success(this._service.translateValue('success.save'));
          this._service.eventEmitter.next(EventTypeEnum.refreshList);
          this._modal.closeAll();
        }
      });
  }

  save(payload: any) {
    this._service.updateNode({ ...payload, warning_message_id: this.param?.warning_message_id }).subscribe((res: any) => {
      if (res?.code === 200) {
        this._message.success(this._service.translateValue('success.save'));
        this._service.eventEmitter.next(EventTypeEnum.refreshList);
        this._modal.closeAll();
      }
    });
  }

  onCancel() {
    this._modal.closeAll();
  }

  calculateDateDifferenceInDays(date1: any, date2: any): number {
    return date1 && date2 ? differenceInDays(date1, date2) : 0;
  }
}
