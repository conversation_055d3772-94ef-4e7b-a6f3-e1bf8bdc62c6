export function initBasicFormConfig() {
  return [
    {
      label: '打样单号',
      key: 'sample_order_id',
      labelKey: 'sample_order_no',
      type: 'select',
      required: true,
      column: 'sample_order',
      url: '/service/scm/sample_outsourcing/sample_option',
      readOnly: true,
    },
    {
      label: '外发类型',
      key: 'process_type',
      labelKey: 'process_type_value',
      type: 'localSelect',
      required: true,
      options: [
        { label: '包工包料', value: 1 },
        { label: '来料加工', value: 2 },
        { label: '包工半包料', value: 3 },
      ],
      readOnly: true,
    },
    {
      label: '外发工厂',
      key: 'factory_id',
      labelKey: 'factory_name',
      subkey: 'factory_code',
      type: 'select',
      required: true,
      column: 'factory_name',
      url: '/service/archive/v1/api/factory/auth/bind/basic_option',
      readOnly: true,
    },
    { label: '收货人', key: 'recipient', labelKey: 'recipient', type: 'input', maxLength: 16, readOnly: true },
    { label: '联系方式', key: 'contact_information', labelKey: 'contact_information', type: 'input', maxLength: 50, readOnly: true },
    { label: '收货地址', key: 'area', labelKey: 'area', type: 'address', subkey: 'detailed_address', maxLength: 255, readOnly: true },
  ];
}

export const basicInfoConfig = [
  { label: '款式编码', code: 'style_code' },
  { label: '品名', code: 'category' },
  { label: '图稿编码', code: 'design_code' },
  { label: '款式分类', code: 'styleName' },
  { label: '品牌', code: 'brand_name' },
  { label: '年份', code: 'year' },
  { label: '季节', code: 'season_name' },
  { label: '月份', code: 'monthName' },
  { label: '波段', code: 'band_name' },
  { label: '系列', code: 'series_name' },
  { label: '来源', code: 'from_type_value' },
  { label: '是否套装', code: 'outfitName' },
  { label: '设计师', code: 'designer_name' },
  { label: '二次工艺', code: 'extra_process_name' },
  { label: '品质', code: 'quality_level_name' },
  { label: '产品类型', code: 'production_category_name' },
] as const;

export const sampleInfoConfig = [
  { label: '样板类型', code: 'sample_type_name', colSpan: 8 },
  { label: '期望交付日期', code: 'expected_delivery_dateline', colSpan: 8 },
  {
    label: '款式要求',
    code: 'style_requirements',
    colSpan: 24,
  },
  {
    label: '物料要求',
    code: 'material_requirements',
    colSpan: 24,
  },
  {
    label: '制版要求',
    code: 'plate_requirements',
    colSpan: 24,
  },
  {
    label: '工艺要求',
    code: 'craft_requirements',
    colSpan: 24,
  },
  { label: '备注', code: 'remark', colSpan: 24 },
] as const;
