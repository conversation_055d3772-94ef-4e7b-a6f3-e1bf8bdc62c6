import {
  FactoryAllocationTypeEnum,
  OrderAllocationTypeEnum,
  OrderCalculationTypeEnum,
  ProductionPlanSubjectEventEnum,
} from './production-plan.enum';

/**
 * @description
 * api数据返回基本格式
 */
export type ResultData<T> = {
  code: number | string;
  message: string;
  data: T;
  [key: string]: any;
};

export type CommonOptionItem = {
  value: string | number;
  label: string;
  [key: string]: any;
};
// ------------ 订单分配 start --------------
/**
 * @description
 * 订单分配列表请求参数
 */
export interface AllocationSettingPayload {
  style_lib_uuid: string;
  sam?: number;
  average_daily_production?: number;
  selected: OrderCalculationTypeEnum;
}

/**
 * @description
 * 订单分配列表请求参数
 */
export interface AllocationListPayload {
  size: number;
  page: number;
  customer?: string;
  due_time_end?: string;
  due_time_start?: string;
  factory_code?: string;
  order_uuid?: string;
  pre_material_completed_time_end?: string;
  pre_material_completed_time_start?: string;
}

/**
 * @description
 * 订单分配列表
 */
export interface AllocationListItem {
  id: number;
  order_code: string;
  order_uuid: string;
  style_code: string;
  category: string;
  first_style_class: string;
  second_style_class: string;
  third_style_class: string;
  customer: string;
  factory_code: string;
  factory_name: string;
  /* 交期/件数 */
  due_time_qty_list: { due_time: number; qty: number }[];
  /* 预计物料齐套日期 */
  pre_material_completed_time: number;
  /* SAM(分钟) */
  sam: number;
  /* 人均日台产 */
  average_daily_production: number;
  /* 待分配数量 */
  to_be_allocated: number;
  style_lib_uuid: string;
  /* 是否预徘单 */
  is_pre_order: boolean;
  style_pic_list?: string[];

  selected?: OrderCalculationTypeEnum;
}

/**
 * @description
 * 订单分配详情
 */

export interface AllocationDetail {
  factory_list: AllocationDetailFactoryItem[];
  color_list: AllocationDetailColorItem[];
  po_list: AllocationDetailPoItem[];
  pre_material_completed_time: number;
  to_be_allocated: number; // 待分配数
  order_code: string;
  due_time: number;
  due_times: string;
}

/**
 * @description
 * 订单分配详情工厂列表
 */
export interface AllocationDetailFactoryItem {
  code: string;
  name: string;
  order_distribute_type: FactoryAllocationTypeEnum; // 工厂类型
  recent_idle_date: number; // 工厂维度最近空闲日
  production_lines: AllocationDetailFactoryLine[];
}

/**
 * @description
 * 订单分配详情工厂产线列表
 */
export interface AllocationDetailFactoryLine {
  production_line_no: string;
  production_line_name: string;
  recent_idle_date: number; // 产线维度最近空闲日
}

/**
 * @description
 * 订单分配详情颜色列表
 */
export interface AllocationDetailColorItem {
  color_code: string;
  color_name: string;
  to_be_allocated: number; // 待分配数
}

/**
 * @description
 * 订单分配详情工厂列表
 */
export interface AllocationDetailPoItem {
  po_code: string;
  po_unique_code: string;
  to_be_allocated: number; // 待分配数
  due_time: number;
}

export interface AllocationInitPlanPaylod {
  allocate_method: OrderAllocationTypeEnum;
  order_uuid: string;
  qty: number;
  factory_code: string;
  production_nos: string[];
}

export interface AllocationDetailLine {
  break_days?: number;
  /**
   * 加工厂档案code
   */
  factory_code?: string;
  plan_end_time?: number;
  plan_start_time?: number;
  /**
   * 产线编码
   */
  production_line_no?: string;
  production_line_name?: string;
  /**
   * 分配数
   */
  qty?: number;
  work_days?: number;

  sam?: number;
  /* 人均日台产 */
  average_daily_production?: number;
}

export interface AllocationGetPlanEndTimePayload extends AllocationInitPlanPaylod {
  color_code?: string;
  po_unique_code?: string;
  production_line_no?: string;
  average_daily_production?: number;
  sam?: number;
  selected: OrderCalculationTypeEnum;
  plan_start_time: number;
}

export interface AllocationGetPlanEndTimeResult {
  plan_end_time: number;
  work_days: number;
  break_days: number;
  is_occupied: boolean;
  plan_start_time: number;
}
// ------------ 订单分配 end --------------

export interface ProductionPlanSubjectEvent {
  type: ProductionPlanSubjectEventEnum;
  data?: any;
}
