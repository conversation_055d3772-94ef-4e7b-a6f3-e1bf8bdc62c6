import { Component, Inject, <PERSON><PERSON><PERSON>roy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, ActivatedRouteSnapshot, RouteReuseStrategy, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { BroadcastService, FlcHeaderComponent, FlcDrawerHelperService, FlcRouterEventBusService, BroadcastDefualtKey } from 'fl-common-lib';
import { Subscription } from 'rxjs';
import { AppStorageService } from 'src/app/shared/app-storage.service';
import { AuthService, UserInfo } from 'src/app/shared/auth.service';
import { MainService } from './main.service';
import { SideBarComponent } from './side-bar/side-bar.component';
import { FlcRouteReuseStrategy, MenuItem, MenuService } from 'fl-common-lib';
import { SampleNoticeComponent } from './sample-notice/sample-notice.component';
import { SysSettingsServices } from 'fl-sewsmart-lib/common-services';
import { factoryCodes } from 'src/app/shared/common-config';

const _DashBoardMenu: ShadowMenuItem = {
  name: '首页',
  url: '/dashboard',
  path: '/dashboard',
  index: 0,
  actions: [],
  code: 'dashboard',
  children: [],
  value: true,
  isDetailMode: false,
  snapshot: null,
};

const langObj: any = {
  zh: 'zh_CN',
  en: 'en_XX',
};

@Component({
  selector: 'app-main',
  templateUrl: './main.component.html',
  styleUrls: ['./main.component.scss'],
})
export class MainComponent implements OnInit, OnDestroy {
  @ViewChild('sidebar') sidebar!: SideBarComponent;

  logoText?: string;
  user!: UserInfo;
  roleName = '';
  roles = [];
  searchMenuInput?: MenuItem | null;
  flattedMenu: { value: any; label: string }[] = [];
  searchedMenu: { value: any; label: string }[] = [];

  menu!: MenuItem[];
  openMenu: ShadowMenuItem[] = [_DashBoardMenu];
  currentOpeningMenuIndexing = 0;
  // breadcrum: string[] = [];
  routeNavigationEndListerner?: Subscription;
  routeNavigationCanceledListerner?: Subscription;
  @ViewChild('noticeTitle') noticeTitle!: TemplateRef<any>;

  brand = 'fl';
  searchMenuVisible = false;
  // groupedSearchMenu: MenuItem[] = [];
  // compareFun = (o1: MenuItem, o2: MenuItem) => {
  //   if (o1) {
  //     return typeof o1 === 'string' ? o1 === o2.name : o1.path === o2.path;
  //   } else {
  //     return false;
  //   }
  // };

  h5QrCodeUrl = window.location.protocol + '//' + window.location.hostname + '/h5'; // H5二维码页面地址

  private readonly _SessionOpenMenuKey = 'Dashboard_OpenMenu';

  isVisibleNotice = false;
  isShowBadge = false;

  showSwitchLang = false; // 是否显示切换语言
  lang = localStorage.getItem('lang') || 'zh';
  userInfo: any;
  factoryCodes = factoryCodes || [];

  constructor(
    public _translateService: TranslateService,
    private _mainService: MainService,
    private _auth: AuthService,
    private _router: Router,
    private _flcDrawer: FlcDrawerHelperService,
    private _activatedRoute: ActivatedRoute,
    private _storage: AppStorageService,
    private _routerEventBus: FlcRouterEventBusService,
    private _routeReuseStrategy: RouteReuseStrategy,
    private _menuService: MenuService,
    private _broadcast: BroadcastService,
    private _systemSettingService: SysSettingsServices,
    @Inject('environment') env: any
  ) {
    env.brand && (this.brand = env.brand);
    const userInfo: string | null = localStorage.getItem('userInfo');
    this.userInfo = userInfo ? JSON.parse(userInfo) : {};
    this.lang = localStorage.getItem('lang') || (this.factoryCodes.includes(env.brand) ? 'en' : 'zh');
  }
  clickSearchMenu($event: boolean) {
    if ($event) {
      setTimeout(() => {
        const target = document.querySelector('input#searchMenuInput') as HTMLElement;
        target.focus();
      }, 10);
    } else {
      this.searchMenuInput = null;
      this.searchMenu('');
    }
  }
  searchMenu(event: any) {
    if (typeof event === 'string') {
      this.searchedMenu = this.flattedMenu.filter((item) => item.label.includes(event));
      // this.groupedSearchMenu = [];
      // this.menu.forEach((itemMenu) => {
      //   const children: MenuItem[] = [];
      //   itemMenu.children.forEach((child) => {
      //     if (child.name.includes(event)) {
      //       children.push(child);
      //     }
      //   });
      //   if (children.length) {
      //     this.groupedSearchMenu.push({ ...itemMenu, children });
      //   }
      // });
    } else {
      const selected: MenuItem = event.value;
      if (this._mainService.isDashboardLink(selected)) {
        window.open(selected.path, '_blank');
        return;
      }
      this._router.navigate([selected.path]);
      this.searchMenuVisible = false;
    }
  }
  handleSearchData() {
    this.flattedMenu = [];
    this.menu.forEach((itemMenu) => {
      this.handleSingleMenuItem(itemMenu);
    });
    this.searchedMenu = [...this.flattedMenu];
  }
  handleSingleMenuItem(item: MenuItem) {
    if (item.children && item.children.length) {
      item.children.forEach((child) => {
        this.handleSingleMenuItem(child);
      });
    } else {
      if (item.value) {
        this.flattedMenu.push({ value: item, label: item.name });
      }
    }
  }

  ngOnInit(): void {
    this._activatedRoute.data.subscribe((d) => {
      this.menu = d.initData.menu;
      this._menuService.updateMenuList(this.menu);
      this.handleSearchData();
      this.user = d.initData.user;

      this.getMenu();
      this.getCurrentUser();

      if (!this.isAuthorizedUrl()) {
        this._router.navigate(['/not-found'], { skipLocationChange: true });
      }

      this._systemSettingService.init();
    });
    this.logoText = this._translateService.instant('elanSystem');
    this.addRouterListener();
    // F5刷新展开当前路由
    // this.expandCurrentMenu();
    // this.getNoticeList();
    this.changeTabTitleListener();
    this.closeTabListener();
  }
  changeTabTitleListener() {
    this._broadcast.eavesdropChannel(BroadcastDefualtKey.changeTabTitle, (message:any) => {
      const targetUrl = message.message.targetUrl;
      const targetIndex = this.openMenu.findIndex((item) => item.url === targetUrl);
      if (targetIndex > -1) {
        this.openMenu[targetIndex].name = message.message.title;
      }
    });
  }

  /// 监听关闭指定url的tab
  closeTabListener() {
    this._broadcast.eavesdropChannel(BroadcastDefualtKey.closeSpecifiedUrl, (message:any) => {
      const targetCloseUrl = this.openMenu.findIndex((item) => item.url === message.message?.url);
      if (targetCloseUrl > -1) {
        this.closeTab({ index: targetCloseUrl });
      }
    });
  }

  // 刷新页面 默认展开当前路由菜单
  expandCurrentMenu(path?: string) {
    const targetPath = path || this._router.url;
    this.menu.forEach((itemMenu: any) => {
      if (itemMenu.children && itemMenu.children.length) {
        itemMenu.children.forEach((childMenu: any) => {
          // fix 手动更改路由this._router.url可能为'/'
          if (path && childMenu?.path.startsWith(targetPath)) {
            itemMenu.isOpen = true;
          }
          // 正常刷新页面
          if (!path && targetPath.startsWith(childMenu?.path)) {
            itemMenu.isOpen = true;
          }
        });
      }
    });
  }

  getCurrentUser(): void {
    this.user && this._storage.saveUserRoleInfo(this.user);
    if (this.user?.roles && this.user?.roles.length > 3) {
      this.roleName = this.user.roles.map((v) => v.role_name).join(',');
    }
  }

  getMenu(): void {
    // this.setBreadScrum(this._router.url.split('/'));
    this.readUserActions();
    const hasSession = this.recoverOpenMenuFromSessionStorage();
    if (!hasSession && this._router.url === '/') {
      this._router.navigate(['/dashboard']);
      // this.checkNavigation();
    }
    this.handleRouterChange();
    // this.setBreadScrum(this._router.url.split('/'));
  }

  checkNavigation() {
    const navUrl = this.getInitialNavUrl();

    if (navUrl) {
      this._router.navigate([navUrl]);
    } else {
      this._router.navigate(['/dashboard']);
    }
  }

  getInitialNavUrl() {
    const sub = this.menu.filter((sub) => {
      return sub.value;
    });

    if (sub.length > 0) {
      const leaf = sub[0].children?.filter((leaf) => {
        return leaf.value;
      });

      if (leaf && leaf.length > 0) {
        return leaf[0].path;
      }
    }
    return null;
  }

  isAuthorizedUrl() {
    const url = this._router.url;
    return (
      ['/', '/dashboard'].includes(url) || this.menu?.some((sub) => sub.children?.some((leaf) => leaf.value && url.includes(leaf.path)))
    );
  }

  getNoticeList() {
    const payload = {
      page: 1,
      size: 20,
      viewAll: false,
      status: 1,
    };
    this._mainService.getSampleNotice(payload).subscribe((res) => {
      if (res.code === 200) {
        this.isShowBadge = res.data.datalist.length > 0;
      }
    });
  }

  showNotice() {
    this._flcDrawer.openDrawer({
      title: this.noticeTitle,
      content: SampleNoticeComponent,
      placement: 'right',
      width: 800,
    });
  }

  logout(): void {
    this.closeAllTab();
    this._router.navigate(['/login']).then((val) => {
      if (val) {
        this._auth.logout().subscribe(() => {
          sessionStorage.removeItem(this._SessionOpenMenuKey);
          this._storage.signout();
          window.location.reload();
        });
      }
    });
  }

  readUserActions() {
    const userActions = new Map<string, []>();
    const userVisualType = new Map<string, number>();
    const userFields = new Map<any, []>();
    const userModules = new Map<any, []>();
    this.menu.forEach((sub) => {
      sub.children?.forEach((item: any) => {
        const uaKey = item.path.substring(1);
        uaKey && userActions.set(uaKey, item.actions);
        uaKey && userVisualType.set(uaKey, item.visualType || 1);
        uaKey && userFields.set(uaKey, item.fieldList);
        uaKey && userModules.set(uaKey, item.moduleList);
      });
    });
    this._storage.saveUserActions(userActions, userVisualType, userFields, userModules);
  }

  // setBreadScrum(url: any) {
  //   this.breadcrum = [];
  //   this.menu.filter((sub) => {
  //     const subPaths = sub.path.split('/');
  //     if (subPaths[subPaths.length - 1].trim() === url[1]) {
  //       this.breadcrum.push(sub.name);

  //       sub.children.filter((leaf) => {
  //         const leafPaths = leaf.path.split('/');
  //         const isLeafPathMatch = leafPaths.slice(2, leafPaths.length).join('/') === url.slice(2, leafPaths.length > 3 ? 4 : 3).join('/');
  //         if (isLeafPathMatch) {
  //           this.breadcrum.push(leaf.name);
  //           this._storage.updateResourceCode(leaf.code);
  //         }
  //       });
  //     }
  //   });
  // }

  addRouterListener() {
    this.routeNavigationEndListerner = this._routerEventBus.onRouterNavigationEnd((e: any) => {
      // this.setBreadScrum(this._router.url.split('/'));
      // 重置路由为 / 展开默认第一个路由
      this.expandCurrentMenu();
      this.handleRouterChange();
      this.recoverFlcHeader();
    });

    this.routeNavigationCanceledListerner = this._routerEventBus.onRouterNavigationCanceled((e: any) => {
      // this.sidebar.resetMenu();
      this.handleRouterChange();
    });
  }
  ngOnDestroy() {
    this.routeNavigationEndListerner?.unsubscribe();
    this.routeNavigationCanceledListerner?.unsubscribe();
  }
  isAllExpand = false;
  // 展开全部，收起全部
  toggleAllMenu() {
    this.menu.forEach((item: any) => {
      item.isOpen = this.isAllExpand;
    });
    this.isAllExpand = !this.isAllExpand;
    document.body.click();
  }
  // 展开菜单，刷新展开收起状态
  onRefreshCollapsedStatus() {
    this.isAllExpand = !this.menu.some((item: any) => item.isOpen);
  }
  isCollapsed = false;
  // 展开 收起 菜单栏
  toggleCollapsed() {
    this.isCollapsed = !this.isCollapsed;
    document.body.click();
  }
  closeTab($event: { index: number }) {
    const deleteItem = this.openMenu.splice($event.index, 1)[0];
    setTimeout(() => {
      (this._routeReuseStrategy as FlcRouteReuseStrategy).clearCachedRoute(deleteItem.url);
      this.saveOpenMenuToSessionStorage();
    }, 0);
    if (this.openMenu.length === 0) {
      this._router.navigate(['/dashboard']);
    } else if ($event.index === this.currentOpeningMenuIndexing) {
      this.currentOpeningMenuIndexing = this.openMenu.length - 1;
      this._router.navigate([this.openMenu[this.currentOpeningMenuIndexing].url], {
        queryParams: this.openMenu[this.currentOpeningMenuIndexing].params,
      });
    }
  }
  closeAllTab() {
    const shadowList = [...this.openMenu];
    setTimeout(() => {
      shadowList.forEach((item) => {
        (this._routeReuseStrategy as FlcRouteReuseStrategy).clearCachedRoute(item.url);
      });
    });
    this.openMenu = [_DashBoardMenu];
    this.currentOpeningMenuIndexing = 0;
    this._router.navigate(['/dashboard']);
    this.saveOpenMenuToSessionStorage();
  }
  handleRouterChange() {
    let route = this._activatedRoute;
    while (route.firstChild) {
      route = route.firstChild;
    }
    const urls = this._router.url.split('/');
    if (urls[1] === 'dashboard') {
      this.currentOpeningMenuIndexing = 0;
      this.menu.forEach((sub) => {
        sub.isCurrent = false;
        sub.children?.forEach((child) => (child.isCurrent = false));
      });
    } else {
      this.menu.filter((sub) => {
        sub.isCurrent = false;
        sub.children?.forEach((leaf) => {
          leaf.isCurrent = false;
          const leafPaths = leaf.path.split('/');
          const isLeafPathMatch = leafPaths.slice(1, leafPaths.length).join('/') === urls.slice(1, leafPaths.length > 3 ? 4 : 3).join('/');
          if (isLeafPathMatch) {
            sub.isCurrent = true;
            leaf.isCurrent = true;
            const lastPath = urls[urls.length - 1].trim().split(/\?\w+=\w+/)[0];
            const isNewMode = ['new', 'add'].includes(lastPath);
            const isDetailMode = isNewMode || !Number.isNaN(Number.parseInt(lastPath, 10));
            const url = this._router.url.split(/\?\w+=\w+/)[0];
            if (isDetailMode) {
              const detailIndex = this.openMenu.findIndex((item) => item.path === leaf.path && item.isDetailMode);
              if (detailIndex > -1) {
                this.currentOpeningMenuIndexing = detailIndex;
                (this._routeReuseStrategy as FlcRouteReuseStrategy).clearCachedRoute(this.openMenu[detailIndex]?.url ?? '');
                this.openMenu[detailIndex].url = url;
                this.openMenu[detailIndex].name = `${leaf.name} - ${this._translateService.instant(
                  isNewMode ? 'main.filed.新建' : 'main.filed.详情'
                )}`;
              } else {
                const detailMenu: ShadowMenuItem = {
                  ...leaf,
                  name: `${leaf.name} - ${this._translateService.instant(isNewMode ? 'main.filed.新建' : 'main.filed.详情')}`,
                  url: url,
                  isDetailMode: true,
                  snapshot: route.snapshot,
                  params: route.snapshot.queryParams,
                };
                this.openMenu.splice(this.currentOpeningMenuIndexing + 1, 0, detailMenu);
                this.currentOpeningMenuIndexing++;
              }
            } else {
              const index = this.openMenu.findIndex((item) => item.path === leaf.path && !item.isDetailMode);
              if (index > -1) {
                this.currentOpeningMenuIndexing = index;
              } else {
                this.openMenu.push({
                  ...leaf,
                  isDetailMode: false,
                  url: url,
                  snapshot: route.snapshot,
                  params: route.snapshot.queryParams,
                });
                this.currentOpeningMenuIndexing = this.openMenu.length - 1;
              }
            }
            this._storage.updateResourceCode(leaf.code);
          }
        });
      });
      this.saveOpenMenuToSessionStorage();
    }
  }
  recoverOpenMenuFromSessionStorage(): boolean {
    const openMenuStr = sessionStorage.getItem(this._SessionOpenMenuKey);
    if (openMenuStr) {
      const shadowItem: { currentIndex: number; menuList: ShadowMenuItem[] } = JSON.parse(openMenuStr);
      this.openMenu = shadowItem.menuList.map((item) => ({ ...item, snapshot: null }));
      this.currentOpeningMenuIndexing = shadowItem.currentIndex;
      return true;
    }
    return false;
  }
  saveOpenMenuToSessionStorage() {
    const shadowItem: { currentIndex: number; menuList: ShadowMenuItem[] } = {
      currentIndex: this.currentOpeningMenuIndexing,
      menuList: this.openMenu.map((item) => ({ ...item, snapshot: null })),
    };
    sessionStorage.setItem(this._SessionOpenMenuKey, JSON.stringify(shadowItem));
  }
  toDashboard() {
    this._router.navigate(['/dashboard']);
  }
  recoverFlcHeader() {
    const target = document.getElementById('nz-content-container');
    target!.style.width = '99.99%';
    setTimeout(() => {
      target!.style.width = '100%';
    }, 0);
    this._broadcast.yell({ channel: FlcHeaderComponent.BroadCastRecoverKey, message: {} });
  }
  switchMenu(index: number) {
    const currentMenu = this.openMenu[index];
    this._broadcast.yell({ channel: 'SwitchTabGlobalKey', message: currentMenu });
  }

  // 切换语言
  switchLanguage(language: string) {
    localStorage.setItem('lang', language);
    this._translateService.use(language);
    window.location.reload();
  }
}
interface ShadowMenuItem extends MenuItem {
  snapshot: ActivatedRouteSnapshot | null;
  params?: object;
  isDetailMode: boolean;
  url: string;
}
