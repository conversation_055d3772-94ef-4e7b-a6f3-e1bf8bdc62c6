import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class CalendarService {
  constructor(private http: HttpClient) {}
  _serviceUrl = '/service/procurement-inventory/vacation/v1';

  /**
   * 获取国家列表
   */
  getCountryList() {
    return this.http.post(`${this._serviceUrl}/country-list`, {});
  }

  /**
   * 获取设置的日期
   */
  getcalendarSettingList(payload: any) {
    return this.http.post(`${this._serviceUrl}/vacation-list`, payload);
  }

  /**
   * 设置日期
   */
  setcalendar(payload: any) {
    return this.http.post(`${this._serviceUrl}/vacation-set`, payload);
  }
}
