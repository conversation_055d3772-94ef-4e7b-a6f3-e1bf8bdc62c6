<div style="padding: 15px 10px">
  <form nz-form [formGroup]="validateForm" style="margin-top: 24px">
    <ng-container *ngFor="let item of formConfig">
      <nz-form-item>
        <nz-form-label [nzRequired]="item.required" class="info-label" [nzSpan]="8">
          {{ translateName + item.label | translate }}
        </nz-form-label>
        <nz-form-control [nzSpan]="14" [flcErrorTip]="translateName + item.label | translate">
          <ng-container *ngIf="item.type === 'number-input'">
            <nz-input-number
              style="width: 100%"
              [formControlName]="item.key"
              flcStringifyFmCtrlValue
              [nzMin]="0"
              [nzMax]="999999999.99999"
              [nzStep]="1"
              [nzPrecision]="5"
              [nzPlaceHolder]="'flss.placeholder.input' | translate"></nz-input-number>
          </ng-container>
        </nz-form-control>
      </nz-form-item>
    </ng-container>
  </form>

  <div class="action-btn-container">
    <button nz-button flButton="default" [nzShape]="'round'" (click)="onCancel()">{{ 'btn.cancel' | translate }}</button>
    <button nz-button nzType="primary" [nzShape]="'round'" (click)="onSave()">
      {{ 'btn.save' | translate }}
    </button>
  </div>
</div>
