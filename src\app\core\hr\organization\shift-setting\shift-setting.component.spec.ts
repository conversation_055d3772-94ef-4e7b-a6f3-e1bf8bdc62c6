/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { ShiftSettingComponent } from './shift-setting.component';

describe('ShiftSettingComponent', () => {
  let component: ShiftSettingComponent;
  let fixture: ComponentFixture<ShiftSettingComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ShiftSettingComponent],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ShiftSettingComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
