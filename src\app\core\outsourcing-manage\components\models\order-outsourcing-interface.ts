// io的详情信息
export interface IoBasic {
  id: 0; // 大货单id, 新建时为0
  io_uuid: any;
  commit: true; // 是否提交
  io_code: 'string'; // 大货单号
  customer: 'string'; // 客户名称
  customer_style: 'string'; // 客户货号
  category: 'string'; // 品名
  first_material_name: 'string'; // 一级分类名称
  first_material_code: 'string'; // 一级分类code
  first_material_id: 0; // 一级分类id
  second_material_name: 'string'; // 二级分类名称
  second_material_code: 'string'; // 二级分类code
  second_material_id: 0; // 二级分类id
  third_material_id: 0; // 三级分类id
  third_material_name: 'string'; // 三级分类名称
  third_material_code: 'string'; // 三级分类code
  order_date: 'string'; // 下单日期，13位时间戳
  contract_number: 'string'; // 销售单号
  production_type: 0; //生产类型，
  remark: 'string'; // 备注
  customer_io_code: 'string'; // 客户生产单号
  deletable: true; // 是否可删除
  order_status: 0; // 订单状态，前端不用填写
  order_status_value: 'string'; // 订单状态，前端不用填写
  reason: 'string'; // 退回原因
  qty: 0; // 订单所有po件数加和，前端不用填写
  due_time: 'string'; // 最早的po交期，前端不用填写
  style_code: string;
  style_code_uuid: string;
  order_pictures: Array<{
    // 订单图片
    name: string;
    url: string;
  }>;
  extra_process_info: Array<{
    extra_process_id: number; // 二次工艺id
    extra_process_name: string; // 二次工艺name
    position_list: Array<{
      position_id: number; // 部位id
      position_name: string; // 部位名称
    }>;
    deletable: true; // 是否可删除
  }>;
  appendix_requirements: Array<{
    name: string;
    url: string;
  }>;

  process_factory_name: string;
  process_factory_code: string;
  is_use_plan: boolean;
}
export type IoLine = Line;

// po集合的接口
export type Pos = Array<Po>;
export type Po = {
  po_basic: PoBasic;
  po_lines: Array<Line>;
};
export interface PoBasic {
  id: number; // po id，新建时为0
  po_code: string; // po号
  due_time: string | Date; // po交期，13位时间戳
  receiver: string; // 收获人
  contact: string; // 联系方式
  country_id: number; // 收货国家id
  province_id: number; // 省份id
  city_id: number; // 城市id
  district_id: number; // 地区id
  country_name: string; // 国家名称
  province_name: string; // 省份名称
  city_name: string; // 城市名称
  district_name: string; // 地区名称
  address: string; // 详细地址
  indexing: number; // po索引
  deletable: boolean; // 是否可删除
  spec_group_id: number; // 尺码组id
  spec_group_name: string; // 尺码组名称
  loss_count_v2: string; // 损耗件数
  po_unique_code: string;
  have_changed_line: boolean; // 是否有更新的颜色尺码
}
export interface Line {
  id: number; // po_line id，新建时为0
  po_ids: Array<number>;
  po_id?: number; //记录po的id
  po_line_id?: number; //当前line的id
  factory_id?: number; //加工厂id
  order_id?: number; // 分配单id
  specSizeIndex?: number; // 尺码的顺序
  color_info: {
    color_id: number; // 颜色id
    color_name: 'string'; // 颜色名称
    color_code: 'string'; // 颜色code
  };
  colorIndex?: number;
  size_info: {
    spec_id: number; // 尺码id
    spec_size: 'string'; // 尺码size
    spec_code: 'string'; // 尺码code
  };
  qty: any; // 颜色尺码数量
  _qty?: any; // 颜色尺码数量,用于记录原有的line,大货单更爱为0 成衣外发 二次工艺会手动重置为null
  indexing: number; // po line索引
  deletable: boolean; // 是否可删除
  over: boolean; // 是否超出
  extraParams?: {
    cellEditable?: boolean;
  };
}
