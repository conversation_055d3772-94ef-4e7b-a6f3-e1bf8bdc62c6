import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { NzResizeEvent } from 'ng-zorro-antd/resizable';
import { AbstractControl, FormArray, FormBuilder, FormControl, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { DischargeCalculationDetailService } from './discharge-calculation-detail.service';
import { ActivatedRoute, Router } from '@angular/router';
import { NzTableComponent } from 'ng-zorro-antd/table';
import { cloneDeep, isArray, isEqual, isNil, last, sortBy, uniq, uniqBy } from 'lodash';
import { NzMessageService } from 'ng-zorro-antd/message';
import { FlcDrawerHelperService, FlcModalService, FlcTableHelperService, FlcValidatorService } from 'fl-common-lib';
import { forkJoin, firstValueFrom, finalize } from 'rxjs';
import { format } from 'date-fns';
import { MaterialPackageMaterialSelectorComponent } from 'fl-sewsmart-lib/material-package';
import { AppStorageService } from 'src/app/shared/app-storage.service';
import { bomInfoTabelHeaderTitles } from './discharge-calculation-detail.config';
import { DischargeCommonService } from '../discharge-calculation-common.service';

@Component({
  selector: 'app-discharge-calculation-detail',
  templateUrl: './discharge-calculation-detail.component.html',
  styleUrls: ['./discharge-calculation-detail.component.scss'],
  providers: [DischargeCalculationDetailService],
  host: {
    class: 'mainBoard',
  },
})
export class DischargeCalculationDetailComponent implements OnInit {
  // @ViewChild('otherMaterial') otherMaterial: any;
  @ViewChild('dischargeBox') dischargeBox!: ElementRef;
  @ViewChild('dischargeTable') dischargeTable!: NzTableComponent<any>;
  @ViewChild('basicTable') basicTable!: NzTableComponent<any>;
  @ViewChild('baseWrap') baseWrap!: ElementRef;
  @ViewChild('nzModalContentDistribution') nzModalContentDistribution!: ElementRef<any>;
  // 小贴士相关参数
  _showTip = false; // 是否展开小贴士
  fullScreen = true;
  bomFullScreen = false;
  saving = false;

  // 基础信息相关参数
  dischargeId!: number; // 排料列表带过来的id
  moreFlag = false; // 点击更多
  colTotalArr: any = []; // 纵向计算求和
  ioBaseInfo: any = {
    // io部分求和
    order_qty: [],
    done_qty: [],
    unfinish_qty: [],
  };
  deparmentOption: any[] = [];

  // bom 区域
  validateForm!: FormGroup; // form表单
  bomEditable = false; // bom单是否可编辑
  positionOption: any = []; // 部位下拉
  colorList: any = []; // 颜色列表
  unitOption: any = []; // 单位下拉选项
  selectedValue = 4; // 默认为全部
  tabCurIndex = 0; // 对应表格的对应下标
  layoutStatusOption = [
    // 排料状态下拉
    {
      label: '全部',
      value: 4,
    },
    {
      label: '未开始',
      value: 0,
    },
    {
      label: '已开始',
      value: 1,
    },
    {
      label: '已完成',
      value: 2,
    },
  ];
  colorCascadeOption: any = []; // 颜色级联
  colorTotalData: any = []; // 其他颜色
  isVisiblePurchaseNum = false; // 采购明细弹窗
  baseData: any = []; // 采购数量表格数据
  bomData: any;
  radioBtn!: boolean; // 是否批量支持批量
  activeColor = '';
  specOptions: any = {};
  repetitiveList: any = []; // 重复的数据
  synchronousColor = false; // 是否同步颜色
  showOtherMaterial = false; // 面料档案库
  tabIndex = 0; // 对应颜色的formArray
  baseInfo: any; // 基础信息
  io_uuid: any;
  bulk_task_order_id: any;
  combineOptions = {};
  updateUnitCode = false; // 是否更新了采购单位
  isShowUpdateMadel = false;

  // 同步颜色相关参数
  isVisibleColor = false; // 同步颜色弹窗
  checkedColor!: any; // 同步颜色选中的颜色

  // 备注部分参数
  remark = '';
  showRemark = false;

  // 导出
  exportDisabled = false;

  // 拖拽相关参数
  siderWidth = 88; // 拖拽区默认右侧宽度
  id = -1; // 拖拽区域id

  // 排料操作区
  dischargeEditable = false; // 操作区域是否可编辑
  dischargeValidateForm!: FormGroup; // 排料form表单
  disChargeInfo: any; // 排料列表
  dischargeBoxHeight: any;
  listListener: any;
  isVisibleDistribution = false;
  btnArr: any;
  optionCustomer: any;

  get formArrayCtrl(): FormGroup[] {
    return (<FormArray>this.dischargeValidateForm?.get('discharge'))?.controls as FormGroup[];
  }

  constructor(
    private activeRoute: ActivatedRoute,
    private fb: FormBuilder,
    private modal: NzModalService,
    private _notice: NzNotificationService,
    private _service: DischargeCalculationDetailService,
    private msg: NzMessageService,
    private router: Router,
    private _drawer: FlcDrawerHelperService,
    private _flcValidator: FlcValidatorService,
    private _tableHelper: FlcTableHelperService,
    private _flcModal: FlcModalService,
    private _commonSrv: DischargeCommonService,
    public _storage: AppStorageService
  ) {}
  headers: any = [];
  renderHeaders: any = [];
  renderScrollX = '100%';
  version = '1.3.0';
  ngOnInit(): void {
    this.headers = this._tableHelper.getFlcTableHeaderConfig(bomInfoTabelHeaderTitles, this.version);
    this.getRenderHeaders();
    this.activeRoute.params.subscribe((res) => {
      this.dischargeId = Number(res.id); // 获取排料计算列表带过来的id
    });
    this.activeRoute.queryParamMap.subscribe((res: any) => {
      if (res.params.io_uuid) {
        this.io_uuid = res.params.io_uuid;
      }
    });
    const tipShow = JSON.parse(localStorage.getItem('tipShow') as any); // 小贴士是否打开
    if (tipShow) {
      if (tipShow === 'true') {
        this._showTip = true;
      } else {
        this._showTip = false;
      }
    }

    const siderWidth = JSON.parse(localStorage.getItem('siderWidth') as any); // 记住拖拽线的位置
    if (siderWidth) {
      this.siderWidth = Number(siderWidth);
    }

    this._service.getPositionSelect(1).subscribe((res) => {
      // 获取部位下拉
      this.positionOption = res.data.option_list;
    });

    this._service.getColorCascade().subscribe((res) => {
      // 获取物料颜色级联
      this.colorCascadeOption = res.data.children;
      this.getDischargeDetail(); // 获取基础信息
    });

    this.listListener = this._drawer.eventEmit.subscribe((res: any) => {
      if (res.type === 1) {
        this.handleOtherMaterial(res.list);
      }
    });
    this.btnArr = this._storage.getUserActions('order/layout');
    this.initDepartmentOption();
  }

  onToggleFullScreen() {
    this.fullScreen = !this.fullScreen;
    setTimeout(() => {
      document.body.click();
    });
  }

  onBomFullScreen() {
    this.bomFullScreen = !this.bomFullScreen;
    if (this.bomFullScreen) {
      this.siderWidth = 0;
    } else {
      const siderWidth = JSON.parse(localStorage.getItem('siderWidth') as any); // 记住拖拽线的位置
      if (siderWidth) {
        this.siderWidth = Number(siderWidth);
      }
    }
    setTimeout(() => {
      document.body.click();
    });
  }

  /**
   * 获取基础信息详情
   */
  async getDischargeDetail() {
    this.synchronousColor = false;
    const res = await firstValueFrom(this._service.getDischargeDetail(this.io_uuid));
    if (res.code === 200) {
      this.baseInfo = res.data;
      this.remark = this.baseInfo.remark; // 获取备注
      if (this.remark) {
        // 有备注的情况，默认展开
        this.showRemark = true;
        this.moreFlag = true;
      }
      // 采购单位处理
      this.unitOption = [
        {
          unit_code: 'kg',
          unit_name: '公斤',
        },
        {
          unit_code: 'm',
          unit_name: '米',
        },
      ];
      // 颜色选项处理
      this.colorList = []; // 颜色tab
      // 计算所有颜色，尺码
      const allColors: any = [];
      const allSpecs: any = [];
      this.optionCustomer = { dist_user_id: this.baseInfo.io_info?.dist_user_id, dist_user_label: this.baseInfo.io_info?.dist_user_label };
      this.baseInfo.io_info.po_color_list.forEach((poColorItem: any) => {
        allColors.push({
          color_name: poColorItem.color_name,
          color_code: poColorItem.color_code,
          color_id: poColorItem.color_id,
          spec_options: poColorItem.size_list,
        });
        allSpecs.push(...poColorItem.size_list);
      });
      this.baseInfo.color_list = sortBy(uniqBy(allColors, 'color_code'), 'color_code');
      this.baseInfo.size_list = sortBy(uniqBy(allSpecs, 'spec_id'), 'indexing');
      this.baseInfo.color_list.forEach((element: any) => {
        this.colorList.push({
          colorName: element.color_name,
          colorCode: element.color_code,
          specOptions: uniqBy(element.spec_options.concat(this.baseInfo.size_list), 'spec_name'),
        });
      });
      this.colorList.unshift({
        colorName: '全部',
        colorCode: '',
      });

      this.ioBaseInfo = {
        // 清空数据
        order_qty: [],
        done_qty: [],
        unfinish_qty: [],
      };

      // io部分展示合计
      this.baseInfo.size_list.forEach(() => {
        // 根据尺码生成初始求和数据
        this.ioBaseInfo.order_qty.push(0);
        this.ioBaseInfo.done_qty.push(0);
        this.ioBaseInfo.unfinish_qty.push(0);
      });
      this.baseInfo.lines_by_color = [];
      this.baseInfo.io_info.po_color_list.forEach((poColorItem: any) => {
        let colorItem = this.baseInfo.lines_by_color.find((i: any) => i.color_code === poColorItem.color_code);
        if (!colorItem) {
          colorItem = {
            color_code: poColorItem.color_code,
            color_name: poColorItem.color_name,
            po_ucode_list: [poColorItem.po_unique_code],
            order_qty: this.baseInfo.size_list.map(
              (size: any) => poColorItem.size_list.find((i: any) => i.spec_code === size.spec_code)?.qty || 0
            ),
            done_qty: this.baseInfo.size_list.map(
              (size: any) =>
                this.baseInfo.done_info
                  .find((i: any) => i.color_code === poColorItem.color_code)
                  .size_list.find((i: any) => i.spec_code === size.spec_code)?.done_qty || 0
            ),
          };
          this.baseInfo.lines_by_color.push(colorItem);
        } else {
          // 同颜色不同po
          colorItem.po_ucode_list.push(poColorItem.po_unique_code);
          // 累加qty
          this.baseInfo.size_list.forEach((size: any, idx: any) => {
            colorItem.order_qty[idx] += poColorItem.size_list.find((i: any) => i.spec_id === size.spec_id)?.qty || 0;
          });
          this.baseInfo.size_list.forEach((size: any, idx: any) => {
            colorItem.done_qty[idx] +=
              this.baseInfo.done_info
                .find((i: any) => i.color_code === poColorItem.color_code)
                .size_list.find((i: any) => i.spec_id === size.spec_id)?.done_qty || 0;
          });
        }
      });
      this.baseInfo.lines_by_color.forEach((item: any) => {
        item.order_qty.forEach((ele: any, index: any) => {
          this.ioBaseInfo.order_qty[index] = ele + this.ioBaseInfo.order_qty[index]; // 订单数
        });
        item.done_qty.forEach((ele: any, index: any) => {
          this.ioBaseInfo.done_qty[index] = ele + this.ioBaseInfo.done_qty[index]; // 已排数据
        });
      });

      this.ioBaseInfo.order_qty.forEach((ele: any, index: any) => {
        this.ioBaseInfo.unfinish_qty[index] = ele - this.ioBaseInfo.done_qty[index]; // 未排数据
      });

      this.ioBaseInfo.order_qty.push(
        // 订单数求和
        this.ioBaseInfo.order_qty.reduce((pre: any, cur: any) => {
          return pre + cur;
        })
      );

      this.ioBaseInfo.done_qty.push(
        // 已排求和
        this.ioBaseInfo.done_qty.reduce((pre: any, cur: any) => {
          return pre + cur;
        })
      );
      this.ioBaseInfo.unfinish_qty.push(
        // 未排求和
        this.ioBaseInfo.unfinish_qty.reduce((pre: any, cur: any) => {
          return (pre < 0 ? 0 : pre) + (cur < 0 ? 0 : cur);
        })
      );

      this.baseInfo.lines_by_color.forEach((item: any) => {
        item.unFinish_qty = [];
        item.order_qty.forEach((ele: any, index: any) => {
          // 每个订单颜色对应的未排件数
          item.unFinish_qty.push(ele - item.done_qty[index]);
        });
      });

      this.baseInfo.lines_by_color.forEach((item: any) => {
        let orderSum = 0;
        let doneSum = 0;
        let unfinishSum = 0;
        orderSum = item.order_qty.reduce((pre: any, cur: any) => {
          // 订单件数求和
          return pre + cur;
        });
        item.order_qty.push(orderSum);

        doneSum = item.done_qty.reduce((pre: any, cur: any) => {
          // 已排件数求和
          return pre + cur;
        });
        item.done_qty.push(doneSum);

        unfinishSum = item.unFinish_qty.reduce((pre: any, cur: any) => {
          // 未排件数求和
          if (pre >= 0 && cur >= 0) {
            return (pre < 0 ? 0 : pre) + (cur < 0 ? 0 : cur);
          }
        });
        item.unFinish_qty.push(unfinishSum);
      });

      this.colTotalArr = [0];
      this.baseInfo.size_list.forEach(() => {
        this.colTotalArr.push(0);
      });
      this.baseInfo.lines_by_color_po = [];
      this.baseInfo.io_info.po_color_list.forEach((poColorItem: any) => {
        this.baseInfo.lines_by_color_po.push({
          color_name: poColorItem.color_name,
          due_time: format(poColorItem.due_time, 'yyyy-MM-dd'),
          po_code: poColorItem.po_code,
          qty_list: this.baseInfo.size_list.map(
            (size: any) => poColorItem.size_list.find((i: any) => i.spec_id === size.spec_id)?.qty || 0
          ),
        });
      });
      this.baseInfo.lines_by_color_po.forEach((ele: any) => {
        // po维度纵向求和以及横向求和
        let sum = 0;
        sum = ele.qty_list.reduce((pre: any, cur: any) => {
          return pre + cur;
        });
        ele.qty_list.push(sum);
        ele.qty_list.forEach((item: any, i: any) => {
          this.colTotalArr[i] = this.colTotalArr[i] + item;
        });
      });
      this.baseInfo.po_list = {};
      this.baseInfo.io_info.po_color_list.forEach((poColorItem: any) => {
        this.baseInfo.po_list[poColorItem.po_unique_code] = {
          country_name: poColorItem.country,
          due_time: format(poColorItem.due_time, 'yyyy-MM-dd'),
          po_code: poColorItem.po_code,
          unique_code: poColorItem.po_unique_code,
        };
      });
      await this.getBomList();
    }
  }

  getMatchSpecOptions(color_code: any) {
    return uniqBy(this.colorList.find((i: any) => i.colorCode === color_code).specOptions.concat(this.totalSize), 'spec_name');
  }

  /**
   * 获取bom操作区列表
   * @param  {} code iocode
   */
  async getBomList() {
    const res = await firstValueFrom(this._service.getLcBom(this.io_uuid));

    if (res.code === 200) {
      this.bomData = res.data;
      // 当lc_bom_list为空数组时，说明要新建，这时候需要我们自己遍历详情接口给的color_list
      const bomArr: any = [];
      this.baseInfo.color_list.forEach((item: any) => {
        const obj: any = {};
        const tempArr = this.bomData.lc_bom_list.filter((ele: any) => ele.color_code === item.color_code);
        if (tempArr.length > 0) {
          obj.color_name = tempArr[0].color_name;
          obj.color_code = tempArr[0].color_code;
          obj.color_cons = tempArr[0].color_cons;
          obj.list = tempArr[0].list;
        } else {
          obj.color_name = item.color_name;
          obj.color_code = item.color_code;
          obj.color_cons = [];
          obj.list = [];
        }
        bomArr.push(obj);
      });
      this.bomData.lc_bom_list = bomArr;
      this.bomData.lc_bom_list.forEach((element: any) => {
        // 计算单耗和平均用料
        element.color_doz_cons_m = 0;
        element.color_piece_cons_m = 0;
        element.color_doz_cons_kg = 0;
        element.color_piece_cons_kg = 0;
        element.dozConsListMi = []; // 平均用料采购单位为米的集合
        element.pieceConsListMi = []; // 单耗采购单位为米的集合
        element.dozConsListKg = []; // 平均用料采购单位为千克的集合
        element.pieceConsListKg = []; // 单耗采购单位为千克的集合
        element.dozConsMiSum = 0; // 平均用料采购单位为米求和
        element.dozConsKgSum = 0; // 平均用料采购单位为千克求和
        element.pieceConsMiSum = 0; // 单耗采购单位为米求和
        element.pieceConsKgSum = 0; // 单耗采购单位为千克求和
        element.color_cons.forEach((ele: any) => {
          if (ele.unit_code === 'm') {
            element.color_doz_cons_m = ele.color_doz_cons;
            element.color_piece_cons_m = ele.color_piece_cons;
          } else if (ele.unit_code === 'kg') {
            element.color_doz_cons_kg = ele.color_doz_cons;
            element.color_piece_cons_kg = ele.color_piece_cons;
          }
        });
        element.list.forEach((item: any) => {
          for (const k of ['doz_cons', 'done_qty', 'piece_cons', 'purchase_amount', 'raw_width']) {
            if (k === 'raw_width') {
              item[k] = parseFloat(item[k] || item['bom_width']) || 0;
            } else {
              item[k] = parseFloat(item[k]) || 0;
            }

            item.width_list?.forEach((i: any) => {
              i.purchase_amount = parseFloat(i.purchase_amount) || 0;
              i.seg_list?.forEach((i: any) => {
                i.purchase_amount = parseFloat(i.purchase_amount) || 0;
                i.seg = parseInt(i.seg) || 0;
              });
            });
          }
          if (item.unit_code === 'm') {
            if (item.doz_cons) {
              element.dozConsListMi.push(item);
              element.dozConsMiSum += item.doz_cons;
            }
            if (item.piece_cons) {
              element.pieceConsListMi.push(item);
              element.pieceConsMiSum += item.piece_cons;
            }
          } else if (item.unit_code === 'kg') {
            if (item.doz_cons) {
              element.dozConsListKg.push(item);
              element.dozConsKgSum += item.doz_cons;
            }
            if (item.piece_cons) {
              element.pieceConsListKg.push(item);
              element.pieceConsKgSum += item.piece_cons;
            }
          }
        });
      });

      this.creatGroup(this.bomData.lc_bom_list); // 根据返回的列表生成表单
      // 如果bomlist没有提交过  提交一次
      if (this.bomData.submitted == false) {
        await this.save(false);
      }
      await this.getDisChargeList();
      await this.batchGetSpecByMaterial();
    }
  }

  async batchGetSpecByMaterial() {
    return new Promise<any>((resolve) => {
      // 获取面料的规格
      forkJoin(
        uniq(
          this.bomData.lc_bom_list.reduce(
            (prev: any, _: any, idx: any) => [...prev, ...this.validateForm.value[`bom_accessories${idx}`].map((i: any) => i.material_id)],
            []
          )
        ).map((item) => this._service.batchGetSpecByMaterial(item))
      ).subscribe((res) => {
        this.specOptions = {};
        res.forEach((resItem) => {
          if (resItem.code === 200) {
            this.specOptions[resItem.data.id] = resItem.data.specification_list;
            this.genCombineOption();
            resolve(1);
          }
        });
      });
    });
  }
  /**
   * 获取排料操作区数据
   * @param  {} code
   */
  async getDisChargeList() {
    return new Promise<void>((resolve) => {
      this._service.getDischargeList(this.io_uuid).subscribe((res) => {
        if (res.code === 200) {
          this.disChargeInfo = res.data;
          this.creatDisChargeGroup();
          if (this.updateUnitCode) {
            // 采购单位有变化
            // this.disChargeSave();
          }
          this.resize();
          resolve();
        }
      });
    });
  }

  /**
   * po信息
   * @param  {} data po信息
   */
  getPoInfo(data: any) {
    return this.baseInfo.po_list[data];
  }

  /**
   * 切换颜色
   * @param  {} item
   */
  switchColor(item: any) {
    this.activeColor = item.colorCode;
    this.colorTotalData = this.colorList.filter((item: any) => {
      // 除了全部和选中的颜色之外的所有颜色
      return item.colorCode !== '' && item.colorCode !== this.activeColor;
    });
  }

  /**
   * 复用其他颜色
   */
  reuseOtherColor() {
    // 复用前切换状态
    this.checkedColor = null;
    this.isVisibleColor = true;
  }

  /**
   * 生成对应的表单数据
   * @param  {} i
   */
  getBomFormArray(i: any) {
    return (<FormArray>this.validateForm.get('bom_accessories' + i))?.controls as FormGroup[];
  }

  /**
   * 排料状态筛选
   */
  dischargeModelChange() {
    this.validateForm = this.fb.group({});
    this.bomData.lc_bom_list.forEach((element: any, index: any) => {
      this.validateForm.addControl('bom_accessories' + index, new FormArray([]));
      element.list.forEach((item: any) => {
        const newItem = { ...item, position_listTotal: item.position_list, position_list: item.position_list.map((i: any) => i.label) };
        if (this.selectedValue === 4) {
          (this.validateForm.get('bom_accessories' + index) as FormArray).push(this.createBomFormItem(newItem, element.color_code));
        } else {
          if (newItem.layout_status === this.selectedValue && newItem.with_breakdown) {
            (this.validateForm.get('bom_accessories' + index) as FormArray).push(this.createBomFormItem(newItem, element.color_code));
          }
        }
      });
    });
  }

  /**
   * 取消复用其他颜色弹窗
   */
  handleColorCancel() {
    this.isVisibleColor = false;
  }

  /**
   * 确认选择一个颜色同步
   */
  handleColorOk() {
    if (!this.checkedColor) {
      this._notice.error('请选择一个颜色进行同步', '');
      return;
    }
    const checkColorData: any = cloneDeep(this.bomData.lc_bom_list.filter((color: any) => color.color_code === this.checkedColor)[0]); // 复用目标的数据
    checkColorData.list.forEach((newData: any) => {
      // 复用相当于新增跟后端沟通需将material_id设置为null
      // element.material_id = null;
      delete newData.unique_code;
      delete newData.doz_cons;
      delete newData.piece_cons;
      delete newData.purchase_amount;
      delete newData.layout_status;
      newData.done_qty = 0;
      newData.position_list = [];
    });
    this.bomData.lc_bom_list.forEach((item: any) => {
      if (item.color_code === this.activeColor) {
        item.list = [...item.list, ...checkColorData.list];
      }
    });
    this.creatGroup(this.bomData.lc_bom_list);
    this.batchGetSpecByMaterial();
    this.isVisibleColor = false;
    this.synchronousColor = true;
  }

  /**
   * 创建formAray
   * @param  {} baseList
   */
  creatGroup(baseList: any) {
    this.validateForm = this.fb.group({});
    baseList.forEach((element: any, index: any) => {
      this.validateForm.addControl('bom_accessories' + index, new FormArray([]));
      element.list.forEach((item: any) => {
        (this.validateForm.get('bom_accessories' + index) as FormArray).push(
          this.createBomFormItem(
            { ...item, position_listTotal: item.position_list, position_list: item.position_list.map((i: any) => i.label) },
            element.color_code
          )
        );
      });
    });
  }

  arrayMustNotEmpty = (ctrlName: string): ValidatorFn => {
    return (control: AbstractControl): ValidationErrors | null => {
      if (isArray(control.value)) {
        return control.value.length === 0 ? { customError: { message: `至少选择一个${ctrlName}` } } : null;
      } else {
        return null;
      }
    };
  };

  /**
   * 创建bom对应的formArray的数据
   * @param  {} line=null
   * @param  {} flag=true
   * @returns FormGroup
   */
  totalSize: any = [];
  createBomFormItem(line: any = null, colorCode: string): FormGroup {
    let total_qty: any = null;
    this.baseInfo.lines_by_color.forEach((ele: any) => {
      // 获取对应订单颜色下适用尺码的订单件数
      if (colorCode === ele.color_code) {
        for (const [index, item] of this.baseInfo.size_list.entries()) {
          if (line?.match_specs?.find((i: any) => i.spec_id === item.spec_id)) {
            total_qty += ele.order_qty[index];
          }
        }
      }
    });
    let unit_code = null;
    if (line?.unit_name) {
      if (line.unit_name === '米') {
        unit_code = 'm';
      } else if (line.unit_name === '公斤') {
        unit_code = 'kg';
      }
    }
    const defaultSupplier = line.suppliers?.find((i: any) => i.is_default === true);
    this.totalSize.push(
      ...(line.match_specs?.map((item: any) => ({ ...item, spec_code: item.spec_code ? item.spec_code : item.spec_name })) || [])
    );
    this.totalSize = uniqBy(this.totalSize, 'spec_name');
    const form = this.fb.group({
      unique_code: [line && line.unique_code],
      position_list: [line?.position_list || []], // 部位
      position_listTotal: [line?.position_listTotal || []], // 部位
      material_code: [line && line.material_code], // 物料编码
      material_name: [line && line.material_name], // 物料名称
      material_id: [line && line.material_id], // 物料id
      material_color_id: [null, [Validators.required]], // 物料颜色id
      material_color_code: [(line && line.material_color_code) || null], // 物料颜色编码
      material_color_name: [(line && line.material_color_name) || null], // 物料颜色名称
      color_hex: [(line && line.color_hex) || null], // 物料颜色hex
      comp: [line && (line.comp || line.composition)], // 成分
      match_specs: [
        (line && line.match_specs?.map((i: any) => i.spec_name || i)) || this.getMatchSpecOptions(colorCode).map((i: any) => i.spec_name),
        [Validators.required, this.arrayMustNotEmpty('适用尺码')],
      ], // 适用尺码
      raw_width: [Number(line?.raw_width) ? line.raw_width : line.bom_width?.length ? line.bom_width : 0], // 毛门幅
      bom_width: [(line && line.bom_width) || (line && line.width)], // 门幅
      doz_cons: [line && line.doz_cons], // 每打平均用料
      piece_cons: [line && line.piece_cons], // 单件平均用料
      unit: [line && line.unit], // 单位名称
      unit_id: [line && line.unit_id], // 单位名称
      unit_name: [line && line.unit_name], // 单位名称
      unit_code: [unit_code], // 单位code
      dosage_unit_id: [line && line.dosage_unit_id], // 单位名称
      dosage_unit_name: [line && line.dosage_unit_name], // 单位code
      weight: [line && line.weight], // 克重
      source_unit_code: [line && line.unit_code], // 源单位code
      done_qty: [(line && line.done_qty) || 0], // 已排件数
      total_qty: [total_qty || 0], // 订单件数
      layout_status: [line && line.layout_status], // 排料状态
      purchase_amount: [line && line.purchase_amount], // 总采购数量
      remark: [line && line.remark], // 备注
      bom_remark: [line && line.bom_remark], // 备注
      add_remark: [line && line.remark ? true : false], // 是否存在备注
      with_breakdown: [(line && line.with_breakdown) ?? true], // 是否参与排料
      by_seg_len: [(line && line.by_seg_len) ?? false], // 按段长采购
      width_list: [line && line.width_list], // 总采购数量详情
      loss: [line && line.loss], //损耗
      unit_consumption: [line && line.unit_consumption], //单耗
      purchase_type: [line && line.purchase_type], //采购类型
      source_type: [line && line.source_type], //工厂
      origin_source_type: [line && line.origin_source_type], //源工厂
      fabric_type: [line && line.fabric_type], //面料类型

      supplier_id: [(line && line.supplier_id) || (defaultSupplier && defaultSupplier.id)], //供应商id
      supplier_name: [(line && line.supplier_name) || (defaultSupplier && defaultSupplier.name)], //供应商名称
      supplier_color_code: [(line && line.supplier_color_code) || (defaultSupplier && defaultSupplier.color_list[0])], //供应商色号
      supplier_art_num: [(line && line.supplier_art_num) || (defaultSupplier && defaultSupplier.supplier_art_num)], //供应商货号

      composition: [line && line.composition], //成分
      from_bom_sync: [line ? line.from_bom_sync ?? false : false], // 是否可以更新
    });
    let material_color_id = null;

    if (line?.material_color_id) {
      this.colorCascadeOption.forEach((item: any) => {
        item.children.some((element: any) => {
          if (element.value === line.material_color_id) {
            material_color_id = [item.value, element.value];
            return true;
          } else {
            return false;
          }
        });
      });
    }
    form.get('material_color_id')?.setValue(material_color_id);
    return form;
  }

  /**
   * 创建排料formAray
   */
  creatDisChargeGroup() {
    this.dischargeValidateForm = this.fb.group({
      discharge: new FormArray([]),
    });
    this.disChargeInfo?.lc_detail?.forEach((item: any) => {
      this.formArrayCtrl.push(this.createDischargeFormItem(item));
    });
  }

  /**
   * 创建排料对应的表单
   * @param  {} line=null
   * @returns FormGroup
   */
  createDischargeFormItem(line: any = null, flag?: any): FormGroup {
    if (line && line.combine_id) {
      line.material_id = line.combine_id[0];
      line.material_color_id = line.combine_id[1];
    }
    if (line) {
      line.combine_id = line.combine_id || [line.material_id, line.material_color_id];
    }
    const combineOptions = this.combineOptions[line?.color_code];
    if (this.dischargeEditable) {
      if (line && line.combine_id) {
        if (!combineOptions?.find((opt: any) => isEqual(opt.value, line.combine_id))) {
          line.combine_id = null;
        }
      }
    }

    // 嵌套行
    const specsArr = [];
    if (line && line.specs.length > 0) {
      line.specs.forEach((element: any) => {
        specsArr.push(
          this.fb.group({
            spec_code: [(element && element.spec_code) || null], // 尺码编码
            spec_id: [(element && element.spec_id) || null], // 尺码id
            spec_name: [(element && element.spec_code) || null], // 尺码名称
            size_ratio: [(element && element.size_ratio) || null], // 配比
            done_qty: [flag ? null : (element && element.done_qty) || null, null], // 件数
            position_ids: [(element && element.position_ids) || null], // 部位
            position_ids_copy: [this.getFlcDynamicDefaultPartOptions(element && element.position_ids), null], // 部位
          })
        );
      });
    } else {
      specsArr.push(
        this.fb.group({
          spec_code: [null], // 尺码编码
          spec_id: [null], // 尺码id
          spec_name: [null], // 尺码名称
          size_ratio: [null], // 配比
          done_qty: [null], // 物料颜色编码
          position_ids: [null], // 物料颜色名称
        })
      );
    }
    this.isShowUpdateMadel = false;
    let doz_cons = (line && line.doz_cons) || null;
    let doz_cons2 = (line && line.doz_cons2) || null;
    const unitCode = line?.unitCode ?? line?.unit_code;
    if (this.updateUnitCode) {
      if (unitCode) {
        if (unitCode === 'kg') {
          if (!line?.gsm) {
            this.isShowUpdateMadel = true;
            doz_cons = null;
          } else {
            doz_cons2 = null;
            doz_cons = (line.doz_area * line.loss_multi * line.gsm) / 10000000;
          }
        } else {
          doz_cons = null;
          doz_cons2 = (line?.seg_len * line?.loss_multi * line?.seg_qty) / 100;
        }
      } else {
        if (!line?.gsm) {
          this.isShowUpdateMadel = true;
          doz_cons = null;
        }
        if (line?.doz_area && line?.loss_multi && line?.gsm) {
          doz_cons = (line.doz_area * line.loss_multi * line.gsm) / 10000000;
        }
        doz_cons2 = (line?.seg_len * line?.loss_multi * line?.seg_qty) / 100;
      }
      if (this.isShowUpdateMadel) {
        this.modal.create({
          nzTitle: '提示',
          nzContent: '更改采购单位需到排料操作区维护对应数据的克重',
          nzFooter: [
            {
              label: '我知道了',
              onClick: () => {
                this.modal.closeAll();
              },
            },
          ],
        });
      }
      if (doz_cons) {
        doz_cons = doz_cons && Number(doz_cons.toFixed(3));
      }

      if (doz_cons2) {
        doz_cons2 = doz_cons2 && Number(parseFloat(doz_cons2).toFixed(3));
      }
    }
    return this.fb.group({
      spec_options: [line ? this.genMatchSpecOption(line.color_code, line.material_id, line.material_color_id) : []], // 可供选择的尺码
      color_code: [line && line.color_code, [Validators.required]],
      color_name: [line && this.baseInfo.color_list.find((i: any) => i.color_code === line.color_code)?.color_name],
      combine_id: [(line && line.combine_id) || null, [Validators.required]], // 唯一标识
      material_name: [line && line.material_name],
      material_color_name: [line && line.material_color_name],
      width: [line && (Number(line.width || 0) || 0)], // 毛门幅
      seg_len: [line && line.seg_len], // 段长
      seg_qty: [line && line.seg_qty], // 段数
      doz_area: [line && line.doz_area], // 每打用料面积
      doz_cons: [doz_cons], // 每打平均用料公斤
      doz_cons2: [doz_cons2], // 单件平均用料米
      layers_number: [line && line.layers_number], // 层数
      // gsm: [(line && line.gsm) || null, unitCode !== 'm' ? [Validators.required] : []], // 克重
      gsm: [(line && line.gsm) || null], // 克重
      loss_multi: [line && line.loss_multi], // 损耗
      with_divider: [line && (flag ? false : line.with_divider)], // 是否有分割线
      remark: [(line && line.remark) || null], // 备注
      add_remark: [line && line.remark ? true : false], // 是否增加备注
      specs: new FormArray(specsArr),
      unitCode: [unitCode],
      unit_name: [(line && line.unit_name) || null],
      checked: [false],
    });
  }

  /**
   * 复制一行
   * @param  {} data
   * @param  {number} formArrIndex
   * @param  {} index
   * @returns void
   */
  copyLine(data: any, formArrIndex: number, index: any, colorCode: any): void {
    const newData = data.getRawValue();
    delete newData.unique_code;
    delete newData.doz_cons;
    delete newData.piece_cons;
    delete newData.purchase_amount;
    delete newData.layout_status;
    newData.done_qty = 0;
    newData.position_list = [];
    newData.from_bom_sync = false;
    (this.validateForm.get('bom_accessories' + formArrIndex) as FormArray).insert(index + 1, this.createBomFormItem(newData, colorCode));
  }

  /**
   * 新增数据
   * @param  {} data
   * @param  {number} formArrIndex
   * @param  {string} flag? 更改时需要传值
   */
  // openMaterial(data: any, formArrIndex: number, flag?: string, index?: any) {
  //   this.tabIndex = formArrIndex;
  //   this.tabCurIndex = index;
  //   this.radioBtn = false;
  //   if (flag === 'change') {
  //     data.addControl('replace', new FormControl(true));
  //     this.radioBtn = true;
  //   }
  //   this.showOtherMaterial = true;
  //   setTimeout(() => {
  //     this.otherMaterial.showDrawer();
  //   }, 100);
  // }

  onOpenMaterialSelector(data: any, formArrIndex: number, flag?: string, index?: any) {
    this.tabIndex = formArrIndex;
    this.tabCurIndex = index;
    this.radioBtn = false;
    if (flag === 'change') {
      data.addControl('replace', new FormControl(true));
      this.radioBtn = true;
    }

    const title = (flag === 'change' ? '更改' : '新增') + '面料';
    this._drawer
      .openDrawer({
        title: title,
        content: MaterialPackageMaterialSelectorComponent,
        contentParams: { fabricType: 1, singleChoice: !(!isNil(index) && index >= 0) },
      })
      .subscribe();
  }

  /**
   * 更新数据
   * @param  {any[]} data
   */
  handleOtherMaterial(data: any[]) {
    const shadow = (this.validateForm.get('bom_accessories' + this.tabIndex) as FormArray).controls as FormGroup[];
    data.forEach((formItem) => {
      formItem.raw_width = formItem.width;
      let insertIndex = this.tabCurIndex;
      // 找到需要替换的那条数据的index，并在该数据后插入一条新数据
      shadow.forEach((F, index) => {
        if (F.get('replace')?.value) {
          insertIndex = index;
          if (!F.value.with_breakdown) {
            formItem.with_breakdown = false;
          }
          formItem.material_color_id = null;
          formItem.match_specs = this.getMatchSpecOptions(this.colorList[this.tabIndex + 1].colorCode);
          formItem = Object.assign(F.value, formItem);
        }
      });

      (this.validateForm.get('bom_accessories' + this.tabIndex) as FormArray).insert(
        insertIndex + 1,
        this.createBomFormItem(formItem, this.colorList[this.tabIndex + 1].colorCode)
      );
    });
    this.batchGetSpecByMaterial();
    // 删除需要替换的数据
    const finalShadow = ((this.validateForm.get('bom_accessories' + this.tabIndex) as FormArray).controls as FormGroup[]).filter(
      (item) => !item.get('replace')?.value
    );
    // finalShadow的方式是为了保持数据变更时from表单的响应式，勿动！
    (this.validateForm.get('bom_accessories' + this.tabIndex) as FormArray).controls = [...finalShadow];
    this.showOtherMaterial = false;
    // this.otherMaterial.closeMaterial();
  }

  /**
   * 删除一条数据
   * @param  {number} formArrIndex
   * @param  {number} index
   */
  deleteBom(formArrIndex: number, index: number) {
    (this.validateForm.get('bom_accessories' + formArrIndex) as FormArray).removeAt(index);
    const shadow = (this.validateForm.get('bom_accessories' + formArrIndex) as FormArray).controls;
    (this.validateForm.get('bom_accessories' + formArrIndex) as FormArray).controls = [...shadow];
  }

  /**
   * 增加备注
   * @param  {} data
   */
  bomAddRemak(data: any) {
    if (data.controls.add_remark.value) {
      data.get('remark').setValue(null);
      data.setControl('add_remark', new FormControl(false));
    } else {
      data.setControl('add_remark', new FormControl(true));
    }
  }

  /**
   * 增加一行
   */
  addDischargeLine(index: any) {
    (this.dischargeValidateForm.get('discharge') as FormArray).insert(index + 1, this.createDischargeFormItem());
    setTimeout(() => {
      const tableBody = this.dischargeTable.nzTableInnerScrollComponent.tableBodyElement.nativeElement;
      tableBody.scrollTo(0, tableBody.scrollTop + 50);
    });
  }

  /**
   * 复制一行
   * @param  {} data
   * @param  {} index
   */
  copyDischargeLine(data: any, index: any, e: any) {
    const d = this.createDischargeFormItem(data.getRawValue(), true);
    (this.dischargeValidateForm.get('discharge') as FormArray).insert(index + 1, d);
    (d.get('specs') as FormArray).controls.forEach((e) => {
      this.calulationDoneQty(d, e as FormGroup, false);
    });
    if (e) {
      const rowHeight = e.currentTarget.parentElement.parentElement.offsetHeight;
      setTimeout(() => {
        const tableBody = this.dischargeTable.nzTableInnerScrollComponent.tableBodyElement.nativeElement;
        tableBody.scrollTo(0, tableBody.scrollTop + rowHeight - 10);
      });
    }
  }

  /**
   * 删除一行
   * @param  {} index
   */
  deleteaddDischargeLine(index: any) {
    (this.dischargeValidateForm.get('discharge') as FormArray).removeAt(index);
  }

  /**
   * 套牌时增加行
   * @param  {} data
   * @param  {} i
   */
  add(i: any) {
    const control = this.formArrayCtrl[i];
    (control.get('specs') as FormArray).push(
      this.fb.group({
        spec_code: [null], // 尺码编码
        spec_id: [null], // 尺码id
        spec_name: [null], // 尺码名称
        size_ratio: [null], // 配比
        done_qty: [null], // 物料颜色编码
        position_ids: [null], // 物料颜色名称
      })
    );
  }

  copySizeLine(i: any, j: any) {
    const control = this.formArrayCtrl[i];
    const arr = control.get('specs') as FormArray;
    const value = arr.value[j];
    const fb = this.fb.group({
      spec_code: [value.spec_code], // 尺码编码
      spec_id: [value.spec_id], // 尺码id
      spec_name: [value.spec_name], // 尺码名称
      size_ratio: [value.size_ratio], // 配比
      done_qty: [], // 物料颜色编码
      position_ids: [value.position_ids], // 物料颜色名称
      position_ids_copy: [this.getFlcDynamicDefaultPartOptions(value.position_ids)], // 物料颜色名称
    });
    arr.insert(j + 1, fb);
    this.calulationDoneQty(control, fb);
  }

  /**
   * 套牌时删除行
   * @param  {} index
   * @param  {} j
   */
  delete(index: any, j: any) {
    if ((this.formArrayCtrl[index].get('specs') as FormArray).length === 1) {
      return;
    }
    (this.formArrayCtrl[index].get('specs') as FormArray).removeAt(j);
  }

  /**
   * 只读状态下部位展示
   * @param  {} value
   */
  transforCodeToLabel(value: any) {
    let label = '';
    if (typeof value === 'string') {
      // 复制一个部位后 value == label
      return value;
    }
    this.positionOption.forEach((ele: any) => {
      if (value === ele.value) {
        label = ele.label;
      }
    });
    return label;
  }

  /**
   * 只读状态下适用尺码展示
   */
  transforCodeToLabelMatchSpec(code: any) {
    let label = '';
    this.baseInfo.size_list.concat(this.totalSize).forEach((ele: any) => {
      if (code === ele.spec_name) {
        label = ele.spec_name;
      }
    });
    return label;
  }

  // 同步保存bom
  async syncBom(data: any, lc_bom_list: any = null) {
    lc_bom_list = lc_bom_list || this.bomData.lc_bom_list;
    const positionMap = [];
    for (const item of this.disChargeInfo.lc_detail) {
      const positions = item.specs
        .reduce((prev: any, curr: any) => prev.concat(...curr.position_ids.map((i: any) => parseInt(i))), [])
        .filter((id: any) => !isNaN(id));
      let found: any = positionMap.find(
        (i) => i.material_id === item.material_id && i.material_color_id === item.material_color_id && i.color_code === item.color_code
      );
      if (!found) {
        found = {
          material_id: item.material_id,
          material_color_id: item.material_color_id,
          color_code: item.color_code,
          position_ids: [],
        };
        positionMap.push(found);
      }
      found.position_ids.push(...positions);
    }
  }

  /**
   * 保存bom数据
   */
  async save(show_dialog = true) {
    this.validateForm.updateValueAndValidity();
    let isValid = false; // 表单校验状态
    this.selectedValue = 4; // 编辑时，排料状态为全部
    isValid = this._flcValidator.formIsInvalid(this.validateForm);
    if (isValid) {
      this._notice.error('请将所有必填项填写完整', '');
      return;
    }
    this.updateUnitCode = false;
    this.repetitiveList = []; // 重复数据清空
    const obj: any = {}; //
    obj.io_uuid = this.io_uuid;
    obj.lc_bom_list = [];
    for (const [index, element] of this.bomData.lc_bom_list.entries()) {
      const bomListObj: any = {};
      bomListObj.color_name = element.color_name;
      bomListObj.color_code = element.color_code;
      bomListObj.list = [];
      const formData = (this.validateForm.get('bom_accessories' + index) as FormArray).controls as FormGroup[];
      this.isRepetitiveData(formData, element.color_name);

      if (formData.length > 0) {
        formData.forEach((ele) => {
          const formcontrolObj: any = {};
          const formcontrol = ele.value;
          if (formcontrol.material_id || formcontrol.material_id === 0) {
            if (!formcontrol.source_unit_code) {
              // 原来没有维护采购单位,现在维护了
              if (formcontrol.unit_code === 'm' || formcontrol.unit_code === 'kg') {
                this.updateUnitCode = true && show_dialog;
              }
            } else {
              // 原来有单位，但更新了单位
              if (formcontrol.unit_code !== formcontrol.source_unit_code) {
                this.updateUnitCode = true && show_dialog;
              }
            }
          }
          formcontrolObj.material_code = formcontrol.material_code;
          formcontrolObj.material_name = formcontrol.material_name;
          formcontrolObj.material_color_id = formcontrol.material_color_id?.length ? formcontrol.material_color_id.slice(-1)[0] : null;
          formcontrolObj.material_color_code = formcontrol.material_color_code;
          formcontrolObj.material_color_name = formcontrol.material_color_name;
          formcontrolObj.doz_cons = formcontrol.doz_cons ? formcontrol.doz_cons : null;
          formcontrolObj.piece_cons = formcontrol.piece_cons ? formcontrol.piece_cons : null;
          formcontrolObj.unit_name = formcontrol.unit_name;
          formcontrolObj.unit_code = formcontrol.unit_code;
          formcontrolObj.done_qty = formcontrol.done_qty ? formcontrol.done_qty : null;
          formcontrolObj.purchase_amount = formcontrol.purchase_amount ? formcontrol.purchase_amount : null;
          formcontrolObj.remark = formcontrol.remark;
          formcontrolObj.with_breakdown = formcontrol.with_breakdown;
          formcontrolObj.material_id = formcontrol.material_id;
          formcontrolObj.raw_width = formcontrol.raw_width.toString();
          formcontrolObj.composition = formcontrol.composition;
          formcontrolObj.by_seg_len = formcontrol.by_seg_len;

          formcontrolObj.supplier_id = formcontrol.supplier_id;
          formcontrolObj.supplier_name = formcontrol.supplier_name;
          formcontrolObj.supplier_color_code = formcontrol.supplier_color_code;
          formcontrolObj.supplier_art_num = formcontrol.supplier_art_num;

          formcontrolObj.weight = formcontrol.weight;
          formcontrolObj.bom_width = formcontrol.bom_width;
          formcontrolObj.specification_id = null;
          formcontrolObj.specification_name = '';
          formcontrolObj.loss = formcontrol.loss;
          formcontrolObj.unit_consumption = formcontrol.unit_consumption;
          formcontrolObj.unit_id = formcontrol.unit_id;
          formcontrolObj.purchase_type = formcontrol.purchase_type;
          formcontrolObj.source_type = formcontrol.source_type;
          formcontrolObj.origin_source_type = formcontrol.origin_source_type;
          formcontrolObj.fabric_type = formcontrol.fabric_type;
          formcontrolObj.fabric_type_value = formcontrol.fabric_type === 1 ? '主布' : '辅布';
          formcontrolObj.position_list = formcontrol.position_list
            .map((item: any) => formcontrol.position_listTotal.find((i: any) => i.label === item))
            .filter((item: any) => !!item);
          formcontrolObj.match_specs = formcontrol.match_specs
            .filter((data: any) => typeof data === 'string')
            .map((size: any) => {
              return this.getMatchSpecOptions(element.color_code).find((item: any) => item.spec_name === size);
            })
            .filter((item: any) => item);
          for (const k of ['doz_cons', 'done_qty', 'piece_cons', 'purchase_amount', 'raw_width', 'weight', 'bom_width', 'loss']) {
            formcontrolObj[k] = formcontrolObj[k]?.toString();
          }
          bomListObj.list.push(formcontrolObj);
        });
        obj.lc_bom_list.push(bomListObj);
      } else if (!this.activeColor || this.activeColor === element.color_code) {
        if (show_dialog) {
          this._notice.error('至少有一条BOM数据', '');
          return;
        }
      }
    }

    let tip = '';
    this.repetitiveList.forEach((item: any) => {
      // 物料重复提示
      if (item.list.length > 0) {
        tip = tip + item.colorName + ',';
        item.list.forEach((element: any) => {
          tip = tip + `物料: ${element.material_name} 物料颜色: ${element.material_color_name ?? '无'}`;
        });
      }
    });
    if (tip) {
      this._notice.error(tip + '物料重复,请重新操作！', '');
      return;
    }
    for (const item of this.formArrayCtrl || []) {
      const value = item.value;
      const material_id = (value.combine_id || [])[0];
      const material_color_id = (value.combine_id || [])[1];
      const newWidth =
        +obj.lc_bom_list
          .find((i: any) => i.color_code === value.color_code)
          ?.list.find((i: any) => i.material_id === material_id && i.material_color_id === material_color_id)?.raw_width || 0;
      if (value.width !== newWidth) {
        item.patchValue({
          width: newWidth,
        });
        if (!this.caculationDozArea(item)) {
          return;
        }
      }
    }
    this.saving = true;
    try {
      if (show_dialog) {
        const ref = this._flcModal.confirmCancel({ content: '请确认是否将物料同步至大货资料包？', cancelText: '同步', okText: '不同步' });
        ref.afterClose.subscribe((res: any) => {
          // await this.syncBom([], obj.lc_bom_list);
          if (null == res) return;
          obj['is_sync'] = res == false;
          this.bomSave(obj);
        });
      } else {
        obj['is_sync'] = false;
        this.bomSave(obj, show_dialog);
      }
    } finally {
      this.saving = false;
    }
  }

  bomSave(obj: any, show_dialog = true) {
    this._service.saveBomInfo(obj).subscribe(async (res) => {
      if (res.code === 200 && show_dialog) {
        this.getDischargeDetail();
        this.bomEditable = !this.bomEditable;
        this._notice.success('保存成功', '');
        this.resize();
      }
    });
  }

  /**
   * 判断物料是否重复 SKU
   * @param  {} formData
   * @param  {} colorName
   */
  _sku(formData: any) {
    //同物料 同物料颜色 同规格spec是规格 同门幅 同克重
    return '' + formData.value.material_id + formData.value.material_color_id + formData.value.raw_width + formData.value.weight;
  }
  /**
   * 判断物料是否重复
   * @param  {} formData
   * @param  {} colorName
   */
  isRepetitiveData(formData: any, colorName: any) {
    const arr: any = [];
    const obj: any = {};
    obj.colorName = colorName;
    formData.forEach((item: any) => {
      const filterArr = formData.filter((ele: any) => this._sku(ele) === this._sku(item));
      if (filterArr.length > 1) {
        const id = this._sku(item);
        if (!arr.find((i: any) => i.id === id)) {
          arr.push({
            id,
            material_name: item.value.material_name,
            material_color_name: item.value.material_color_name,
          });
        }
      }
    });
    obj.list = arr;
    this.repetitiveList.push(obj);
  }

  /**
   * 将部位转换成后端需要的
   * @param  {} positionList
   */
  getPosition(positionList: any) {
    const positionArr: any = [];
    positionList.forEach((element: any) => {
      const tempObj: any = {};
      this.positionOption.forEach((item: any) => {
        if (item.value === element) {
          tempObj.label = item.label;
          tempObj.value = item.value;
          positionArr.push(tempObj);
        }
      });
    });
    return positionArr;
  }

  /**
   * 展示采购数量详情
   * @param  {} data
   */
  purchaseClick(data: any) {
    if (this.bomEditable || data.value.width_list.length === 0) {
      return;
    }
    this.baseData = [];
    this.isVisiblePurchaseNum = true;
    let index = 0;
    data.value.width_list.forEach((item: any) => {
      index = item.seg_list.length + index;
    });
    if (index !== 0) {
      data.value.row = index;
      this.baseData = [data.value];
    }
  }

  /**
   * 单位改动
   * @param  {} e
   * @param  {FormGroup} fg
   */
  unitChange(e: any, fg: FormGroup) {
    let unitName = null;
    this.unitOption.forEach((item: any) => {
      if (item.unit_code === e) {
        unitName = item.unit_name;
      }
    });
    fg.get('unit_name')?.setValue(unitName);
  }

  /**
   * 物料颜色更改
   * @param  {} e
   * @param  {FormGroup} fg
   */
  colorCascadeChange(e: any, fg: FormGroup) {
    if (e?.length) {
      const data = this.getCascadeLeafData(e.slice(-1)[0], this.colorCascadeOption);

      fg.get('material_color_name')?.setValue(data.label, { emitViewToModelChange: false });
      fg.get('material_color_code')?.setValue(data.code);
      fg.get('color_hex')?.setValue(data.color_hex);
    } else {
      fg.get('material_color_name')?.setValue(null, { emitViewToModelChange: false });
      fg.get('material_color_code')?.setValue(null);
    }
  }

  /**
   * 在级联候选中找出选中的数据
   * @param  {} key
   * @param  {} cascadeOption
   * @returns any
   */
  getCascadeLeafData(key: any, cascadeOption: any): any {
    let data;
    const fn = (arr: any) => {
      const res = arr.filter((entity: any) => entity.value === key);
      if (res.length) {
        const { children, parent, isLeaf, ...k } = res[0];
        data = k;
      } else {
        arr.forEach((entity: any) => {
          if (entity.children) {
            fn(entity.children);
          }
        });
      }
    };
    const fn2 = (arr: any) => {
      for (const item of arr) {
        if (!item.isLeaf) {
          const res: any = fn2(item.children);
          if (res) {
            return res;
          }
        } else {
          if (item.value === key) {
            const { children, parent, isLeaf, ...k } = item;
            console.log(item, k);
            return k;
          }
        }
      }
    };
    if (key) {
      return fn2(cascadeOption);
    } else {
      return {};
    }
  }

  /**
   * 关闭弹窗
   */
  handleCancel() {
    // 全部弹窗关闭并且清空状态
    this.isVisiblePurchaseNum = false;
    this.modal.closeAll();
  }
  /**
   * 获取面料下拉: 物料-物料颜色-规格
   * @param  {} colorName
   */
  genCombineOption() {
    const specOptions = this.specOptions;
    for (const { color_code } of this.baseInfo.color_list) {
      const matOption: any = [];
      this.bomData.lc_bom_list.forEach((item: any) => {
        if (item.color_code === color_code) {
          item.list.forEach((item: any) => {
            if (item.with_breakdown) {
              const matItem: any = {};
              matItem.label = item.material_name + (item.material_color_name?.length ? '-' + item.material_color_name : '');
              matItem.value = [item.material_id, item.material_color_id];
              if (!matOption.filter((e: any) => isEqual(e.value, matItem.value)).length) {
                matOption.push(matItem);
              }
            }
          });
        }
      });
      this.combineOptions[color_code] = matOption;
    }
  }

  // 获取适用尺码选择器
  genMatchSpecOption(color_code: any, material_id: any, material_color_id: any) {
    return this.getBomMaterialInfo(color_code, material_id, material_color_id)?.match_specs || [];
  }

  // 获取面料信息
  getBomMaterialInfo(color_code: any, material_id: any, material_color_id: any) {
    return this.bomData.lc_bom_list
      .find((i: any) => i.color_code === color_code)
      ?.list.find((i: any) => i.material_id === material_id && i.material_color_id === material_color_id);
  }

  compareCombineOption = isEqual;

  /**
   * 订单颜色选择
   * @param  {} event
   * @param  {FormGroup} fg
   */
  colorModelChange(event: any, fg: FormGroup) {
    const bomListOfColor = this.bomData.lc_bom_list.find((i: any) => i.color_code === event).list;
    const option = this.combineOptions[event]; // 物料选择项
    // 1. 该颜色下只有一个物料 自动填充
    if (option.length == 1) {
      const e = bomListOfColor[0];
      const combine_id = [e.material_id, e.material_color_id];
      fg.get('combine_id')?.setValue(combine_id);
      this.matModelChange(combine_id, fg);
      return;
    }
    // 2. 该颜色下有多个物料
    if (option.length > 1) {
      // 选中相同物料相同颜色的物料
      const lastCombineId = fg.get('combine_id')?.value;
      const b = bomListOfColor.find((e: any) => isEqual(lastCombineId, [e.material_id, e.material_color_id]));
      if (b) {
        const combine_id = [b.material_id, b.material_color_id];
        fg.get('combine_id')?.setValue(combine_id);
        this.matModelChange(combine_id, fg);
        return;
      }
      // 没有相同物料相同颜色 选中相同物料
      const c = bomListOfColor.find((e: any) => isEqual(lastCombineId ? lastCombineId[0] : null, e.material_id));
      if (c) {
        const combine_id = [c.material_id, c.material_color_id];
        fg.get('combine_id')?.setValue(combine_id);
        this.matModelChange(combine_id, fg);
        return;
      }
    }
    // 3. 其他情况 清空 面料信息 尺码信息
    fg.get('combine_id')?.setValue(null);
    const shadow = (fg.get('specs') as FormArray).controls as FormGroup[];
    shadow.forEach((element) => {
      element.get('position_ids')?.setValue(null);
      element.get('done_qty')?.setValue(null);
      element.get('spec_code')?.setValue(null);
      element.get('spec_id')?.setValue(null);
      element.get('spec_name')?.setValue(null);
      element.get('size_ratio')?.setValue(null);
    });
  }

  /**
   * 面料选择
   * @param  {} event
   * @param  {FormGroup} fg
   */
  matModelChange(event: any, fg: FormGroup) {
    if (!event) return;
    // 1. 若所选面料在BOM中的适用尺码，保留面料适用尺码里面的尺码 并且重新计算尺码对应的数量
    // 适用尺码赋值
    const incomeSpecs = Array.isArray(event) ? this.genMatchSpecOption(fg.get('color_code')?.value, event[0], event[1]) : [];
    fg.get('spec_options')?.setValue(incomeSpecs);

    let shadow = (fg.get('specs') as FormArray).controls as FormGroup[];
    shadow = shadow.filter(
      (e) => incomeSpecs.find((i: any) => i.spec_code == e.get('spec_code')?.value) || e.get('spec_code')?.value == null
    );
    if (shadow.length == 0) {
      shadow.push(
        this.fb.group({
          spec_code: [null], // 尺码编码
          spec_id: [null], // 尺码id
          spec_name: [null], // 尺码名称
          size_ratio: [null], // 配比
          done_qty: [null], // 物料颜色编码
          position_ids: [null], // 物料颜色名称
        })
      );
    } else {
      shadow.forEach((e) => {
        this.calulationDoneQty(fg, e);
      });
    }
    (fg.get('specs') as FormArray).controls = shadow;
    const materialInfo = this.getBomMaterialInfo(fg.get('color_code')?.value, event[0], event[1]);
    // 单位
    fg.get('unitCode')?.setValue(materialInfo.unit_code);
    fg.get('unit_name')?.setValue(materialInfo.unit_name);
    fg.get('width')?.setValue(materialInfo.raw_width);
    this.caculationDozArea(fg);
  }

  /**
   * 尺码选择
   * @param  {} event
   * @param  {FormGroup} data
   * @param  {FormGroup} item
   */
  sizeModelChange(event: any, data: FormGroup, item: FormGroup) {
    this.calulationDoneQty(data, item);
  }

  positionChange(event: any, data: FormGroup, item: FormGroup) {
    this.calulationDoneQty(data, item);
  }

  /**
   * 计算件数
   * @param  {FormGroup} data
   * @param  {FormGroup} item
   */
  calulationDoneQty(data: FormGroup, item: FormGroup, changeLayer = true) {
    item.get('done_qty')?.setValue(null);
    // 2024年11月13日18:10:41  产品需求改为自动填充 订单件数
    if (item.get('spec_code')?.value) {
      const curIndex = this.baseInfo.size_list.findIndex((value: any) => value.spec_name === item.get('spec_code')?.value);
      let order_qty: any = null;
      this.baseInfo.lines_by_color.forEach((element: any) => {
        if (element.color_code === data.get('color_code')?.value) {
          if (curIndex > -1) {
            order_qty = element.order_qty[curIndex];
            item.get('done_qty')?.setValue(order_qty);
          }
        }
      });
    }
    // if (item.get('spec_code')?.value && item.get('position_ids')?.value) {
    //   const curIndex = this.baseInfo.size_list.findIndex((value: any) => value.spec_name === item.get('spec_code')?.value);
    //   let order_qty: any = null;
    //   this.baseInfo.lines_by_color.forEach((element: any) => {
    //     if (element.color_code === data.get('color_code')?.value) {
    //       if (curIndex > -1) {
    //         order_qty = element.order_qty[curIndex];
    //       }
    //     }
    //   });

    //   const tempArr: any = [];
    //   item.get('position_ids')?.value.forEach(() => {
    //     tempArr.push(order_qty);
    //   });
    //   this.formArrayCtrl.forEach((element) => {
    //     element.value.specs.forEach((ele: any) => {
    //       if (
    //         element.value.color_code === data.get('color_code')?.value &&
    //         this.compareCombineOption(element.value.combine_id, data.get('combine_id')?.value)
    //       ) {
    //         if (item.get('spec_code')?.value === ele.spec_code) {
    //           item.get('position_ids')?.value.forEach((item1: any, index: any) => {
    //             // 按照部位计算剩余件数
    //             if (ele.position_ids && ele.position_ids.includes(item1)) {
    //               if (ele.done_qty) {
    //                 tempArr[index] = tempArr[index] - ele.done_qty;
    //               }
    //             }
    //           });
    //         }
    //       }
    //     });
    //   });
    //   if (uniq(tempArr).length > 1) {
    //     item.get('done_qty')?.setValue(null);
    //   } else {
    //     item.get('done_qty')?.setValue(tempArr[0] > 0 ? tempArr[0] : null);
    //   }
    // }
    if (changeLayer) this.calulationLayersNumber(data, item);
  }

  /**
   * 计算层数
   * @param  {FormGroup} data
   * @param  {FormGroup} item
   */
  calulationLayersNumber(data: FormGroup, item: FormGroup) {
    if (item.get('done_qty')?.value && item.get('size_ratio')?.value) {
      const tempArr: any = [];
      let don_qty: any = null;
      data.value.specs.forEach((element: any) => {
        if (element.size_ratio) {
          tempArr.push(element.size_ratio);
        }
      });

      data.value.specs.forEach((element: any) => {
        if (this.mathMin(tempArr) === element.size_ratio) {
          don_qty = element.done_qty;
        }
      });
      const layersNumber = don_qty / this.mathMin(tempArr);
      data.get('layers_number')?.setValue(Number(layersNumber.toFixed(0)));
    }
  }

  /**
   * 获取数组中的最小值
   * @param  {} arrs
   */
  mathMin(arrs: any) {
    let min: any = arrs[0];
    for (let i = 1, ilen = arrs.length; i < ilen; i += 1) {
      if (arrs[i] < min) {
        min = arrs[i];
      }
    }
    return min;
  }

  /**
   * 计算用料面积
   * @param  {FormGroup} data
   */
  caculationDozArea(data: FormGroup) {
    data.get('doz_area')?.setValue(null);
    if (data.get('width')?.value && data.get('seg_len')?.value && data.get('seg_qty')?.value) {
      const dozArea = data.get('width')?.value * data.get('seg_len')?.value * data.get('seg_qty')?.value;
      data.get('doz_area')?.setValue(Number(dozArea.toFixed(2)));
    }
    return this.caculationDozCons(data); // 更改面料需重新计算用料面积和没打用料
  }

  /**
   * 获取采购单位
   * @param  {} colorCode
   * @param  {} matCode
   */
  getUnitCode(colorCode: any, material_id: any) {
    let unit_code = '';
    if (colorCode && material_id) {
      this.bomData.lc_bom_list.forEach((item: any) => {
        if (colorCode === item.color_code) {
          item.list.forEach((element: any) => {
            if (element.material_id === material_id) {
              unit_code = element.unit_code;
              if (!unit_code?.length) {
                unit_code = this.unitOption.find((i: any) => i.unit_name === element.unit_name)?.unit_code;
              }
            }
          });
        }
      });
    }
    return unit_code;
  }

  /**
   * 计算每打用料
   * @param  {FormGroup} fg
   */
  caculationDozCons(fg: FormGroup | any) {
    const unitCode = this.getUnitCode(fg.get('color_code').value, (fg.get('combine_id').value || [])[0]);

    if (fg.get('unitCode').value) {
      fg.get('unitCode').setValue(unitCode);
    } else {
      fg.addControl('unitCode', this.fb.control(unitCode));
    }

    fg.get('doz_cons').setValue(null);
    fg.get('doz_cons2').setValue(null);
    if (unitCode === 'm') {
      if (fg.get('seg_len').value && fg.get('seg_qty').value && fg.get('loss_multi').value) {
        const dozCons2 = (fg.get('seg_len').value * fg.get('loss_multi').value * fg.get('seg_qty').value) / 100;
        fg.get('doz_cons2').setValue(Number(dozCons2.toFixed(3)));
      }
    } else {
      // 不是米的单位
      if (fg.get('gsm').value && fg.get('doz_area').value && fg.get('loss_multi').value) {
        const dozCons = (fg.get('doz_area').value * fg.get('loss_multi').value * fg.get('gsm').value) / 10000000;
        fg.get('doz_cons').setValue(Number(dozCons.toFixed(3)));
        if (Number(dozCons.toFixed(3)) == 0) {
          this.modal.create({
            nzTitle: '提示',
            nzContent: '本行数据每打用料计算为0，请检查',
            nzFooter: [
              {
                label: '我知道了',
                onClick: () => {
                  this.modal.closeAll();
                },
              },
            ],
          });
          return false;
        }
      }
    }
    return true;
  }

  /**
   * 是否有分割线
   * @param  {FormGroup} fg
   */
  isShowDivider(fg: FormGroup | any) {
    if (fg.get('with_divider').value) {
      fg.get('with_divider').setValue(false);
    } else {
      fg.get('with_divider').setValue(true);
    }
  }

  /**
   * 排料部分批量复制
   */
  multiDisChargeLineCopy() {
    const datalist = this.formArrayCtrl.filter((e: any) => e.value.checked);
    if (!datalist.length) return;
    datalist.forEach((data: any) => {
      this.copyDischargeLine(data, this.formArrayCtrl.length, null);
      data.get('checked').setValue(false);
    });
    setTimeout(() => {
      const tableBody = this.dischargeTable.nzTableInnerScrollComponent.tableBodyElement.nativeElement;
      tableBody.scrollTo(0, tableBody.scrollHeight);
    });
  }

  /**
   * 排料数据 == BOM数据
   */
  bomDataSameWithDischarge(bom: FormGroup, disCharge: FormGroup) {
    return (
      isEqual(disCharge.value.combine_id, [bom.value.material_id, last(bom.value.material_color_id) || 0, bom.value.spec_name || '']) &&
      disCharge.value.width === bom.value.raw_width &&
      disCharge.value.gsm?.toString() === bom.value.weight
    );
  }

  /**
   * 排料部分保存
   */
  async disChargeSave(alsoSaveBom = true) {
    if (!this.updateUnitCode) {
      let isValid = false;
      isValid = this._flcValidator.formIsInvalid(this.dischargeValidateForm);
      if (isValid) {
        this._notice.error('请将所有必填项填写完整', '');
        return;
      }
    }

    const obj: any = {};
    obj.io_uuid = this.io_uuid;
    obj.lc_detail = [];
    // 记录需要新增的BOMlist
    // const needCreateLines: any = [];
    // const existLines: any = {};
    this.formArrayCtrl.forEach((element) => {
      // // 找BOM里面存在的
      // const hasSizeData = element.value.specs.filter((e: any) => e.spec_code).length;
      // const colorIndex = this.bomData?.lc_bom_list?.findIndex((e: any) => e.color_code === element.value.color_code);
      // if (element.value.combine_id && hasSizeData && colorIndex != -1) {
      //   const bomData = this.getBomFormArray(colorIndex)?.find((e: any) => this.bomDataSameWithDischarge(e, element));
      //   if (!bomData) {
      //     needCreateLines.push(element);
      //   } else {
      //     existLines[element.value.color_code] = existLines[element.value.color_code] ?? [];
      //     existLines[element.value.color_code].push(bomData);
      //   }
      // }
      const formControl = element.value;
      const specsList: any = [];
      const lcDetailItem: any = {};
      formControl.specs.forEach((item: any) => {
        const specsItem: any = {};
        specsItem.spec_code = item.spec_code;
        specsItem.spec_name = formControl.spec_options.find((i: any) => i.spec_code === item.spec_code)?.spec_name || item.spec_code;
        specsItem.size_ratio = item.size_ratio ? item.size_ratio.toString() : null;
        specsItem.done_qty = item.done_qty ? item.done_qty.toString() : null;
        specsItem.position_ids = [];
        specsItem.positions_new = [];
        item.position_ids?.forEach((e: any) => {
          if (typeof e === 'number') {
            specsItem.position_ids.push(e.toString());
          } else {
            specsItem.positions_new.push(e);
          }
        });
        specsList.push(specsItem);
      });
      lcDetailItem.color_code = formControl.color_code;
      const [material_id, material_color_id] = formControl.combine_id;
      lcDetailItem.material_id = material_id;
      lcDetailItem.material_color_id = material_color_id;
      const bomItem = this.bomData.lc_bom_list
        .find((i: any) => i.color_code === lcDetailItem.color_code)
        .list.find((i: any) => i.material_id === lcDetailItem.material_id && i.material_color_id === lcDetailItem.material_color_id);
      lcDetailItem.material_name = bomItem?.material_name;
      lcDetailItem.material_color_code = bomItem?.materialmaterial_color_code_name;
      lcDetailItem.material_color_name = bomItem?.material_color_name;
      lcDetailItem.specification_name = '';
      lcDetailItem.specification_id = null;
      lcDetailItem.unit_code = formControl?.unitCode;
      lcDetailItem.unit_name = formControl?.unit_name;

      lcDetailItem.width = formControl.width?.toString()?.length ? formControl.width?.toString() : '0';
      lcDetailItem.seg_len = formControl.seg_len?.toString();
      lcDetailItem.layers_number = formControl.layers_number ? formControl.layers_number?.toString() : null;
      lcDetailItem.seg_qty = formControl.seg_qty?.toString();
      lcDetailItem.doz_area = formControl.doz_area?.toString();
      lcDetailItem.doz_cons = formControl.doz_cons ? formControl.doz_cons.toString() : null;
      lcDetailItem.loss_multi = formControl.loss_multi?.toString();
      lcDetailItem.with_divider = !!formControl.with_divider;
      lcDetailItem.remark = formControl.remark;
      lcDetailItem.specs = specsList;
      lcDetailItem.gsm = formControl.gsm ? formControl.gsm?.toString() : null;
      lcDetailItem.doz_cons2 = formControl.doz_cons2 ? formControl.doz_cons2?.toString() : null;

      obj.lc_detail.push(lcDetailItem);
    });

    this.saving = true;
    try {
      const ref = this._flcModal.confirmCancel({ content: '请确认是否将数据同步至大货资料包？', cancelText: '同步', okText: '不同步' });
      ref.afterClose.subscribe(async (is_sync: any) => {
        // await this.syncBom([], obj.lc_bom_list);
        if (null == is_sync) return;
        obj.is_sync = is_sync == false;
        const res = await firstValueFrom(this._service.saveDidChargeInfo(obj));
        if (res.code === 200) {
          if (alsoSaveBom) {
            if (this.updateUnitCode) {
              this.updateUnitCode = false;
            } else {
              this._notice.success('保存成功', '');
              this.dischargeEditable = !this.dischargeEditable;
              // this._handleExtraBomData(needCreateLines, existLines);
            }
            this._service.getPositionSelect(1).subscribe(async (res) => {
              // 获取部位下拉
              this.positionOption = res.data.option_list;
              // 在同步前，先更新一下bom和排料
              await this.getDischargeDetail();
              await this.syncBom(res.data.list);
              // 同步后重新更新bom和排料
              this.getDischargeDetail();
            });
          } else {
            return res.data.list;
          }
        }
      });
    } finally {
      this.saving = false;
    }
  }

  /**
   * 处理 排料过程中 不存在的BOM 物料信息
   * 改为后端处理 ！！！！！！！！！！！
   */
  _handleExtraBomData(needCreateLines: FormGroup[], bomExistLine: any) {
    if (!needCreateLines.length) return;
    needCreateLines.forEach((line) => {
      const colorIndex = this.bomData?.lc_bom_list?.findIndex((e: any) => e.color_code === line.value.color_code);
      const leftList = this.getBomFormArray(colorIndex).filter((e) => !(bomExistLine[line.value.color_code] ?? []).includes(e));
      const filterList = leftList.filter(
        (e: any) => e.value.material_id === line.value.combine_id[0] && e.value.material_color_id.pop() === line.value.combine_id[1]
      );
      if (filterList?.length) {
        const data = filterList[0];
        data.get('raw_width')?.setValue(line.value.width);
        data.get('weight')?.setValue(line.value.gsm);
        filterList.splice(0, 1);
      } else {
        const newBomData = {
          ...line.value,
        };
        console.log(line.value);
        console.log(leftList);
        (this.validateForm.get('bom_accessories' + colorIndex) as FormArray).push(
          this.createBomFormItem(newBomData, line.value.color_code)
        );
      }
    });
  }

  /**
   * 保存备注
   */
  saveRemark() {
    this._service
      .saveRemark({
        remark: this.remark,
        io_uuid: this.io_uuid,
      })
      .subscribe((res) => {
        if (res.code === 200) {
          this._notice.success('备注保存成功', '');
        }
      });
  }

  /**
   * 拖拽
   * @param  {NzResizeEvent} {width}
   * @returns void
   */
  onSideResize({ width }: NzResizeEvent): void {
    cancelAnimationFrame(this.id);
    this.id = requestAnimationFrame(() => {
      this.siderWidth = width!;
      localStorage.setItem('siderWidth', JSON.stringify(this.siderWidth + ''));
    });
  }

  /**
   * 基础信息展开
   */
  toggleExpanded() {
    this.moreFlag = !this.moreFlag;
  }

  /**
   * 展开tip
   */
  toggleShowTip() {
    this._showTip = !this._showTip;
    setTimeout(() => {
      this.resize();
    });
    localStorage.setItem('tipShow', JSON.stringify(this._showTip + ''));
  }

  /**
   * bom编辑切换
   */
  toggleBomEditState() {
    if (this.dischargeEditable) {
      this._notice.error('请先保存排料操作区数据', '');
      return;
    }
    this.selectedValue = 4; // 编辑时，排料状态为全部
    this.bomEditable = !this.bomEditable;
    if (this.synchronousColor) {
      this.getDischargeDetail();
    }
    this.creatGroup(this.bomData.lc_bom_list);
  }

  /**
   * 排料编辑切换
   */
  saveBtnDisable = false;
  toggleDischargeEditState() {
    if (this.bomEditable) {
      this._notice.error('请先保存BOM信息区数据', '');
      return;
    }
    const isInValid = this._flcValidator.formIsInvalid(this.validateForm);
    if (isInValid) {
      this._notice.error('请先完善BOM信息区数据', '');
      return;
    }
    this.saveBtnDisable = true;
    setTimeout(() => {
      this.saveBtnDisable = false;
    }, 2000);
    this.dischargeEditable = !this.dischargeEditable;
    this.creatDisChargeGroup();
    if (this.dischargeEditable && this.formArrayCtrl.length === 0) {
      this.addDischargeLine(0);
      this.add(0);
      this.add(0);
      this.add(0);
      this.add(0);
    }
  }

  /**
   * 转换排料状态
   * @param  {} value
   */
  transforLayoutStatus(value: any) {
    switch (value) {
      case 0:
        return '未开始';
      case 1:
        return '已开始';
      case 2:
        return '已完成';
      default:
        return '';
    }
  }

  /**
   * 返回列表页
   */
  back() {
    if (this.dischargeEditable || this.bomEditable) {
      this.modal.info({
        nzTitle: '您所编辑的内容还未保存，确定返回上一页面？',
        nzOnOk: () => {
          this.router.navigate(['/order/layout']);
        },
      });
    } else {
      this.router.navigate(['/order/layout']);
    }
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.resize();
    });
  }

  resize() {
    setTimeout(() => {
      this.dischargeBoxHeight = window.innerHeight - 32 - 16 - 32 - (this._showTip ? 100 : 0) - 32 - 50 - 44 - 20;
    });
  }

  /**
   * 添加删除备注
   */
  remarkClick() {
    if (this.showRemark) {
      this.modal.confirm({
        nzTitle: '确认删除备注？',
        nzOnOk: () => {
          this.remark = '';
          this._service
            .saveRemark({
              remark: this.remark,
              io_uuid: this.io_uuid,
            })
            .subscribe((res) => {
              if (res.code === 200) {
                this._notice.success('删除成功', '');
                this.showRemark = !this.showRemark;
              }
            });
        },
      });
    } else {
      this.showRemark = !this.showRemark;
      const tableHead = this.basicTable.nzTableInnerScrollComponent.tableHeaderElement.nativeElement.clientHeight;
      const tableBody = this.basicTable.nzTableInnerScrollComponent.tableBodyElement.nativeElement.clientHeight;
      setTimeout(() => {
        this.baseWrap.nativeElement.scrollTo(0, tableHead + tableBody);
      });
    }
  }

  /**
   * 导出
   */
  export() {
    this.exportDisabled = true;
    this._service
      .exportExcel({
        io_uuid: this.io_uuid,
      })
      .subscribe({
        next: (res) => {
          if (res.code === 200) {
            const link = document.createElement('a');
            link.style.display = 'none';
            link.href = res.data.url;
            link.setAttribute('download', '订单排料.xls');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          }
        },
        error: (err) => {
          console.log(err);

          this.msg.error('导出失败');
          this.exportDisabled = false;
        },
        complete: () => {
          this.exportDisabled = false;
        },
      });
  }
  distributionEmployee = {
    dist_user_id: 0,
    dist_user_label: '',
  };
  // 分配算料员弹窗

  showDistributionModal() {
    this.isVisibleDistribution = true;
  }
  handleOkDistribution() {
    this._service
      .submitMaterialsEmployeeOption({
        io_uuid: [this.io_uuid],
        ...this.distributionEmployee,
      })
      .subscribe((res) => {
        if (res.code === 200) {
          // 重新请求详情
          this.isVisibleDistribution = false;
          this.getDischargeDetail();
        }
      });
  }

  _handleChangeValue(e: any) {
    if (e) {
      this.distributionEmployee = {
        dist_user_id: e.id,
        dist_user_label: e.title,
      };
    } else {
      this.distributionEmployee = {
        dist_user_id: 0,
        dist_user_label: '',
      };
    }
  }

  initDepartmentOption() {
    this._service.getDepartmentOption().subscribe((res) => {
      if (res.code === 200) {
        this.deparmentOption = res.data?.children || [];
      }
    });
  }

  btnHighLight = false;
  getRenderHeaders() {
    this.renderHeaders = this._tableHelper.getRenderHeader(this.headers);
    this.renderScrollX = this._tableHelper.getRenderHeaderScrollWidth(this.renderHeaders, 36) + 'px';
  }
  changeHeader(event: MouseEvent): void {
    this.btnHighLight = true;
    const shadow = JSON.parse(JSON.stringify(this.headers));
    this._tableHelper
      .openTableHeaderMidifyDialog(shadow, event.target as HTMLElement, 'start')
      .pipe(finalize(() => (this.btnHighLight = false)))
      .subscribe((res) => {
        if (res) {
          for (const item of res) {
            item.label = bomInfoTabelHeaderTitles.find((i) => i.key === item['key'])?.label ?? '';
          }
          this.headers = res;
          this.getRenderHeaders();
          this._tableHelper.saveFlcTableHeaderConfig(this.headers, this.version);
        }
      });
  }

  columnIsDisplay(str: string) {
    return this.headers.find((e: any) => e.key === str).visible;
  }

  bomRowCanEdit(data: any) {
    return !data.get('from_bom_sync')?.value;
  }

  getFlcDynamicDefaultPartOptions(positionList: []) {
    return positionList?.map((e) => ({ label: this.transforCodeToLabel(e), value: e, hide: true, active: true, disabled: false }));
  }

  getUnitNameDisplay(data: any) {
    return data?.get('unitCode').value === 'm' ? '米' : '公斤';
  }

  // filterSpecificationsOptions: [] = [];
  // lastTimeSpecificationsInput = null;
  // onSpecificationsFocus(data: FormGroup) {
  //   this.filterSpecificationsOptions = [];
  //   const inputText = data.get('spec_name')?.value;
  //   this.lastTimeSpecificationsInput = inputText;
  //   this.filterSpecificationsOptions = this.specOptions[data?.get('material_id')?.value];
  // }
  // onSpecificationsInput(event: Event, data: FormGroup) {
  //   const target = event.target as HTMLInputElement;
  //   const value: string | null = target.value;
  //   const list = this.specOptions[data?.get('material_id')?.value];
  //   if (value && value.length > 0) {
  //     this.filterSpecificationsOptions = list.filter((item: any) => item.specification_name?.includes(value));
  //   } else {
  //     this.filterSpecificationsOptions = list;
  //   }
  // }
  // onSpecificationsBlur(data: FormGroup) {
  //   const inputText = data.get('spec_name')?.value;
  //   if (this.lastTimeSpecificationsInput === inputText) {
  //     return;
  //   }
  // }

  // 审核
  onBomAudit(pass: boolean) {
    if (!pass) {
      this._commonSrv.confirmDialogWithReason().afterClose.subscribe((result: any) => {
        if (result?.success) {
          this._onBomAudit({ approve_status: 4, approve_reason: result.reason });
        }
      });
    } else {
      this._onBomAudit({ approve_status: 3 });
    }
  }
  _onBomAudit(result: any = null) {
    this._commonSrv
      .audit({
        approve_order: [{ io_uuid: this.io_uuid, order_code: this.baseInfo?.io_info?.io_code }],
        approve_reason: result.approve_reason,
        approve_status: result.approve_status,
      })
      .subscribe((res) => {
        if (res.code === 200) {
          if (res.data?.msg?.length) {
            this._notice.error(res.data.msg, '');
          } else {
            this._notice.success('操作成功', '');
          }
          this.getBomList();
        }
      });
  }
}
