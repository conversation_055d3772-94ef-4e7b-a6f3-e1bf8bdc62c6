export interface assignRoleDepartmentModel {
  id: number;
  name: string;
  code: string;
  status: boolean;
  type: 'dept';
  count: number;
  isLeaf: boolean;
  children: assignRoleDepartmentModel[];
  employees: assignRoleEmployeeModel[];
  showEmployee?: boolean;
  isSelected?: boolean;
  seletedChild?: assignRoleDepartmentModel;
  partent?: assignRoleDepartmentModel | null;
  roles?: assignRoleModel[];
}
export class assignRoleDepartmentItem {
  id: number;
  name: string;
  code: string;
  status: boolean;
  type: 'dept';
  count: number;
  isLeaf: boolean;
  children: assignRoleDepartmentItem[];
  employees: assignRoleEmployeeItem[];
  showEmployee: boolean;
  isSelected: boolean;
  seletedChild: assignRoleDepartmentItem | null;
  partent: assignRoleDepartmentItem | null;
  roles: assignRoleModel[];
  isInit: boolean;
  get invalidEmployeeCount(): number {
    let invalidEmployeeCount = 0;
    this.employees.forEach((x) => {
      if (x.user_id === null) {
        invalidEmployeeCount++;
      }
    });
    return invalidEmployeeCount;
  }
  get isChanged(): boolean {
    return this.employees.some((x) => x.isChanged);
  }
  handleInvalidEmployees() {}
  handleEmployeeRolesChanged() {
    this.roles = [];
    const EmployeeLength = this.employees.length;
    const RoleMap = new Map<string, any[]>();
    this.employees.forEach((item) => {
      item.roles.forEach((role) => {
        const key = `${role.role_name}-${role.role_id}`;
        const value = RoleMap.get(key) ? RoleMap.get(key)?.[0] : 0;
        RoleMap.set(key, [value + 1, role.role_name, role.role_id]);
      });
    });
    RoleMap.forEach((x: any) => {
      if (x[0] >= EmployeeLength - this.invalidEmployeeCount) {
        this.roles.push({
          role_name: x[1],
          role_id: x[2],
          priority: 0,
        });
      }
    });
  }
  getAllChangedUser(): userAssignRoleModel[] {
    const shadowList: userAssignRoleModel[] = [];
    this.children.forEach((child) => {
      shadowList.push(...child.getAllChangedUser());
    });
    this.employees.forEach((employee) => {
      if (employee.isChanged && employee.user_id !== null && employee.user_id !== undefined) {
        shadowList.push({ user_id: employee.user_id, roles: employee.roles });
      }
    });
    return shadowList;
  }
  constructor(data: assignRoleDepartmentModel, partent: assignRoleDepartmentItem | null) {
    this.id = data.id;
    this.name = data.name;
    this.code = data.code;
    this.status = data.status;
    this.type = data.type;
    this.count = data.count;
    this.isLeaf = data.isLeaf;
    this.showEmployee = false;
    this.isSelected = false;
    this.seletedChild = null;
    this.partent = partent;
    this.roles = [];
    this.isInit = true;

    this.children = [];
    this.employees = [];
    if (data.children && data.children.length > 0) {
      data.children.forEach((x) => {
        this.children?.push(new assignRoleDepartmentItem(x, this));
      });
    }
    if (data.employees && data.employees.length > 0) {
      data.employees.forEach((x) => {
        this.employees?.push(new assignRoleEmployeeItem(x, this));
      });
    }
    this.handleInvalidEmployees();
    this.handleEmployeeRolesChanged();
  }
}
export interface assignRoleEmployeeModel {
  id: number;
  name: string;
  code: string;
  status: boolean;
  user_id: number;
  user_name: string;
  roles: assignRoleModel[];
  type: 'emp';
  count: number;
  partent?: assignRoleDepartmentModel;
}
export class assignRoleEmployeeItem {
  id: number;
  name: string;
  code: string;
  status: boolean;
  user_id: number | null;
  user_name: string | null;
  roles: assignRoleModel[];
  type: 'emp';
  count: number;
  partent: assignRoleDepartmentItem;
  isChanged: boolean;
  get showIndex() {
    return this.roles.length > 1;
  }
  handleRoleChanged() {
    this.roles.forEach((x, index) => {
      x.priority = index + 1;
    });
    this.isChanged = true;
  }
  constructor(data: assignRoleEmployeeModel, partent: assignRoleDepartmentItem) {
    this.id = data.id;
    this.name = data.name;
    this.code = data.code;
    this.status = data.status;
    this.user_id = data.user_id;
    this.user_name = data.user_name;
    this.roles = data.roles;
    this.roles = JSON.parse(JSON.stringify(this.roles));
    this.type = data.type;
    this.count = data.count;
    this.partent = partent;
    this.isChanged = false;
  }
}
export interface assignRoleModel {
  role_id: number;
  role_name: string;
  priority: number;
}

export interface searchItemModel {
  id: number;
  name: string;
  type: 'dept' | 'emp';
  is_leaf: boolean;
  child: searchItemModel;
}
export interface userAssignRoleModel {
  user_id: number;
  roles: assignRoleModel[];
}
