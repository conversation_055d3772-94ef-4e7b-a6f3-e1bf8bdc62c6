<nz-drawer
  [nzVisible]="isDrawerVisible"
  [nzTitle]="translateName + '消息通知' | translate"
  (nzOnClose)="onHide()"
  [nzPlacement]="'bottom'"
  [nzHeight]="'80%'">
  <ng-container *nzDrawerContent>
    <flc-screen-container [showFold]="false" (reset)="onReset()">
      <div nz-row style="flex: 1">
        <ng-container *ngFor="let item of searchList">
          <div nz-col nzSpan="6" nzXXl="4" nz-row class="search-line-notification">
            <span class="search-label" nz-col nzSpan="7">{{ translateName + item.label | translate }}：</span>
            <div nz-col nzSpan="17">
              <nz-select
                [nzMode]="item.isMultiple ? 'multiple' : 'default'"
                [(ngModel)]="searchData[item.code]"
                [nzAllowClear]="true"
                [nzDisabled]="item.isMultiple && searchDisabled"
                [nzPlaceHolder]="'flss.placeholder.select' | translate"
                (ngModelChange)="onSearch()">
                <nz-option *ngFor="let option of optionsData[item.optionsKey] ?? []" [nzLabel]="option" [nzValue]="option"> </nz-option>
              </nz-select>
            </div>
          </div>
        </ng-container>
      </div>
    </flc-screen-container>
    <div style="background-color: #f0f3fa; padding: 10px">
      <div *ngFor="let bulk of datalist; let index = index" class="bulk-order-item">
        <i nz-icon [nzIconfont]="'icon-cuowu'" class="icon-cuowu" (flcClickStop)="deleteItem(index)"></i>
        <div nz-row style="justify-content: space-between; padding: 4px 0">
          <div nz-row style="align-items: center">
            <span class="desc-label">{{ translateName + '订单需求号' | translate }}：</span>
            <span class="value-label">{{ bulk.order_code }}</span>
            <span style="margin: 0 10px"></span>
            <span class="desc-label">{{ translateName + '大货单号' | translate }}：</span>
            <span class="value-label">{{ bulk.bulk_code }}</span>
            <span *ngIf="bulk.is_pre_order === 1" class="desc-label-tag">预排单</span>
            <span style="margin: 0 10px"></span>
            <span class="desc-label">{{ translateName + '款式编码' | translate }}：</span>
            <span class="value-label">{{ bulk.style_code }}</span>
          </div>
          <div nz-row>
            <button style="margin-right: 10px" nz-button nzShape="round" flButton="minor" (click)="onManulAdjust(index)">
              {{ translateName + '手动调整' | translate }}
            </button>
            <button *ngIf="bulk.auto_adjust" nz-button nzShape="round" flButton="minor" (click)="onAutoAdjust(index)">
              {{ translateName + '自动调整' | translate }}
            </button>
          </div>
        </div>
        <nz-table nzBordered #basicTable [nzData]="bulk.records ?? []" [nzShowPagination]="false">
          <thead>
            <tr>
              <th nzWidth="15%">{{ translateName + '消息类型' | translate }}</th>
              <th nzWidth="40%">{{ translateName + '变更范围' | translate }}</th>
              <th nzWidth="15%">{{ translateName + '变更前' | translate }}</th>
              <th nzWidth="15%">{{ translateName + '变更后' | translate }}</th>
              <th nzWidth="15%">{{ translateName + '生成时间' | translate }}</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let data of basicTable.data">
              <td style="color: #138aff">{{ data.change_type }}</td>
              <td>{{ data.change_scope }}</td>
              <td>{{ data.old_value_str }}</td>
              <td style="color: #138aff">{{ data.new_value_str }}</td>
              <td>{{ data.updated_at | date: 'yyyy/MM/dd HH:mm' }}</td>
            </tr>
          </tbody>
        </nz-table>
      </div>
    </div>
  </ng-container>
</nz-drawer>

<app-message-notification-modal
  #appMessageNotificationModal
  (onConfirmedAutoAdjust)="onHandleAutoAdjustResult($event)"
  (onManulAdjust)="onHandleAutoAdjustResult($event)"></app-message-notification-modal>
