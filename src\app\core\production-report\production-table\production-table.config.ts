import { factoryCodes } from 'src/app/shared/common-config';

const userInfo: string | null = localStorage.getItem('userInfo');
const user = userInfo ? JSON.parse(userInfo) : {};

const dictUrl = '/service/scm/dict_category/dict_option';

export function initSearchOptions() {
  const Standard = [
    {
      type: 'local-select',
      label: '订单需求号',
      options: [],
      key: 'bulk_order_ids',
      relate_key: 'bulk_orders',
      placeHolder: '请选择',
      visible: true,
    },
    {
      type: 'local-select',
      label: '款式编码',
      options: [],
      key: 'style_lib_ids',
      relate_key: 'style_codes',
      placeHolder: '请输入',
      visible: true,
    },
    {
      type: 'local-select',
      label: '订单状态',
      key: 'order_status',
      relate_key: 'order_status',
      placeHolder: '请选择',
      options: [],
      visible: true,
    },
    {
      type: 'dateRange',
      label: '创建时间',
      key: 'gen_time',
      relate_key: 'gen_time',
      placeHolder: ['开始', '结束'],
      visible: true,
    },
    {
      type: 'cascader',
      label: '款式分类',
      key: 'style_class',
      relate_key: 'style_classes',
      placeHolder: '请选择',
      options: [],
      visible: true,
    },
    {
      type: 'local-select',
      label: '加工厂',
      key: 'factory_ids',
      relate_key: 'factories',
      placeHolder: '请选择',
      options: [],
      visible: true,
    },
    {
      type: 'local-select',
      label: '业务员',
      options: [],
      key: 'biz_emp_ids',
      relate_key: 'biz_emp_users',
      placeHolder: '请选择',
      visible: true,
    },
    {
      type: 'local-select',
      label: '负责人',
      key: 'person_in_charge_io_ids',
      relate_key: 'person_in_charge',
      placeHolder: '请选择',
      options: [],
      visible: true,
    },
    {
      type: 'local-select',
      label: '跟单员',
      key: 'merchandiser_ids',
      relate_key: 'merchandisers',
      placeHolder: '请选择',
      options: [],
      visible: true,
    },
    { type: 'local-select', label: 'QC', options: [], key: 'qc', relate_key: 'qc_list', placeHolder: '请选择', visible: true },
    {
      type: 'local-select',
      label: '批次',
      key: 'order_category',
      relate_key: 'order_category_list',
      placeHolder: '请选择',
      options: [],
      visible: true,
    },
    {
      type: 'local-select',
      label: '生产类型',
      key: 'order_production_type',
      relate_key: 'order_production_type_list',
      placeHolder: '请选择',
      options: [],
      visible: true,
    },
    {
      type: 'local-select',
      label: '品牌',
      key: 'brand_list',
      relate_key: 'brand_list',
      placeHolder: '请选择',
      options: [],
      visible: true,
    },
    {
      type: 'local-select',
      label: '订单类型',
      key: 'order_classification',
      relate_key: 'order_classification_list',
      placeHolder: '请选择',
      options: [],
      visible: true,
    },

    {
      type: 'local-select',
      label: '交付单号',
      options: [],
      key: 'po_code',
      relate_key: 'po_codes',
      placeHolder: '请选择',
      visible: false,
    },

    {
      type: 'local-select',
      label: '客户名称',
      options: [],
      key: 'customer_name',
      relate_key: 'custom_names',
      placeHolder: '请选择',
      visible: false,
    },
    {
      type: 'local-select',
      label: '所属办事处',
      options: [],
      key: 'pro_office',
      relate_key: 'pro_offices',
      placeHolder: '请选择',
      visible: false,
    },
    {
      type: 'local-select',
      label: '品类',
      options: [],
      key: 'category',
      relate_key: 'categories',
      placeHolder: '请选择',
      visible: false,
    },
    {
      type: 'local-select',
      label: '生产周期',
      options: [],
      key: 'lead_time',
      relate_key: 'lead_time',
      placeHolder: '请选择',
      visible: false,
    },
    {
      type: 'local-select',
      label: '季节',
      options: [],
      key: 'seasons',
      relate_key: 'seasons',
      placeHolder: '请选择',
      visible: false,
    },
    {
      type: 'local-select',
      label: '生产国',
      options: [],
      key: 'country_of_origin',
      relate_key: 'country_of_origins',
      placeHolder: '请选择',
      visible: false,
    },
  ];

  const CTC = [
    {
      type: 'local-select',
      label: '交付单号',
      options: [],
      key: 'po_code',
      relate_key: 'po_codes',
      placeHolder: '请选择',
      visible: true,
    },
    {
      type: 'local-select',
      label: '业务员',
      options: [],
      key: 'biz_emp_ids',
      relate_key: 'biz_emp_users',
      placeHolder: '请选择',
      visible: true,
    },
    {
      type: 'local-select',
      label: '负责人',
      key: 'person_in_charge_io_ids',
      relate_key: 'person_in_charge',
      placeHolder: '请选择',
      visible: false,
    },
    {
      type: 'local-select',
      label: '客户名称',
      options: [],
      key: 'customer_name',
      relate_key: 'custom_names',
      placeHolder: '请选择',
      visible: true,
    },
    {
      type: 'local-select',
      label: '品牌',
      key: 'brand_list',
      relate_key: 'brand_list',
      placeHolder: '请选择',
      options: [],
      visible: true,
    },
    {
      type: 'local-select',
      label: '所属办事处',
      options: [],
      key: 'pro_office',
      relate_key: 'pro_offices',
      placeHolder: '请选择',
      visible: true,
    },
    {
      type: 'local-select',
      label: '品类',
      options: [],
      key: 'category',
      relate_key: 'categories',
      placeHolder: '请选择',
      visible: true,
    },
    {
      type: 'local-select',
      label: '生产周期',
      options: [],
      key: 'lead_time',
      relate_key: 'lead_time',
      placeHolder: '请选择',
      visible: true,
    },
    {
      type: 'local-select',
      label: '订单需求号',
      options: [],
      key: 'bulk_order_ids',
      relate_key: 'bulk_orders',
      placeHolder: '请选择',
      visible: false,
    },

    {
      type: 'local-select',
      label: '款式编码',
      options: [],
      key: 'style_lib_ids',
      relate_key: 'style_codes',
      placeHolder: '请输入',
      visible: true,
    },
    {
      type: 'local-select',
      label: '季节',
      options: [],
      key: 'seasons',
      relate_key: 'seasons',
      placeHolder: '请选择',
      visible: true,
    },
    {
      type: 'local-select',
      label: '生产国',
      options: [],
      key: 'country_of_origin',
      relate_key: 'country_of_origins',
      placeHolder: '请选择',
      visible: true,
    },
    {
      type: 'local-select',
      label: '订单状态',
      key: 'order_status',
      relate_key: 'order_status',
      placeHolder: '请选择',
      options: [],
      visible: false,
    },
    {
      type: 'dateRange',
      label: '创建时间',
      key: 'gen_time',
      relate_key: 'gen_time',
      placeHolder: ['开始', '结束'],
      visible: false,
    },
    {
      type: 'cascader',
      label: '款式分类',
      key: 'style_class',
      relate_key: 'style_classes',
      placeHolder: '请选择',
      options: [],
      visible: false,
    },
    {
      type: 'local-select',
      label: '跟单员',
      key: 'merchandiser_ids',
      relate_key: 'merchandisers',
      placeHolder: '请选择',
      options: [],
      visible: true,
    },
    { type: 'local-select', label: 'QC', options: [], key: 'qc', relate_key: 'qc_list', placeHolder: '请选择', visible: false },
    {
      type: 'local-select',
      label: '加工厂',
      key: 'factory_ids',
      relate_key: 'factories',
      placeHolder: '请选择',
      options: [],
      visible: true,
    },
    {
      type: 'local-select',
      label: '批次',
      key: 'order_category',
      relate_key: 'order_category_list',
      placeHolder: '请选择',
      options: [],
      visible: false,
    },
    {
      type: 'local-select',
      label: '生产类型',
      key: 'order_production_type',
      relate_key: 'order_production_type_list',
      placeHolder: '请选择',
      options: [],
      visible: false,
    },

    {
      type: 'local-select',
      label: '订单类型',
      key: 'order_classification',
      relate_key: 'order_classification_list',
      placeHolder: '请选择',
      options: [],
      visible: false,
    },
  ];

  return factoryCodes.includes(user?.factory_code) ? CTC : Standard;
}

const commonTableHeaderConfig = {
  visible: true,
  pinned: false,
  disable: false,
  resizeble: true,
  sort: false,
  hasAction: true,
  detailBtn: false,
  sortOrderBy: null,
};

export const DefaultTableHeaders: any[] = [
  {
    label: '订单需求号',
    key: 'bulk_order_code',
    width: '130px',
    type: 'template',
    templateName: 'bulk_order_code',
    ...commonTableHeaderConfig,
    pinned: true,
  },
  { label: '批次', key: 'order_category_value', width: '80px', type: 'text', ...commonTableHeaderConfig, pinned: true },
  { label: '订单类型', key: 'order_classification', width: '80px', type: 'text', ...commonTableHeaderConfig, pinned: true },
  { label: '生产类型', key: 'order_production_type_value', width: '80px', type: 'text', ...commonTableHeaderConfig, pinned: true },
  { label: '品牌', key: 'brand_name', width: '120px', type: 'text', ...commonTableHeaderConfig, pinned: true },
  { label: '款式图', key: 'order_pictures', width: '80px', type: 'image', ...commonTableHeaderConfig, pinned: true },
  { label: '款式编码', key: 'style_code', width: '168px', type: 'text', ...commonTableHeaderConfig, pinned: true },
  { label: '加工厂', key: 'factory_name', width: '118px', type: 'text', ...commonTableHeaderConfig, pinned: true },
  { label: '进度模版', key: 'template_name', width: '120px', type: 'text', ...commonTableHeaderConfig },
  { label: '下单日期', key: 'order_time', width: '124px', type: 'date', ...commonTableHeaderConfig },
  { label: '品名', key: 'category', width: '120px', type: 'text', ...commonTableHeaderConfig },
  { label: '款式分类', key: 'style_class', width: '168px', type: 'template', templateName: 'style_class', ...commonTableHeaderConfig },
  { label: '订单总数', key: 'order_quantity', width: '100px', type: 'text', ...commonTableHeaderConfig },
  { label: '下单数量', key: 'assigned_qty', width: '120px', type: 'text', ...commonTableHeaderConfig }, // 原 分配订单数量
  { label: '订单占有数', key: 'order_number', width: '120px', type: 'text', ...commonTableHeaderConfig },
  { label: '交付时间', key: 'due_time', width: '148px', type: 'date', ...commonTableHeaderConfig },
  { label: '二次工艺类型', key: 'extra_processes', width: '108px', type: 'text', ...commonTableHeaderConfig },
  {
    label: '二次配套工厂',
    key: 'extra_process_factories',
    width: '148px',
    type: 'template',
    templateName: 'extra_process_factories',
    ...commonTableHeaderConfig,
  },
  { label: '订单进度', key: 'order_production_status', width: '148px', type: 'text', ...commonTableHeaderConfig },
  {
    label: '订单状态',
    key: 'out_sourcing_status',
    width: '80px',
    type: 'template',
    templateName: 'out_sourcing_status',
    ...commonTableHeaderConfig,
  },
  { label: '业务员', key: 'biz_emp_name', width: '120px', type: 'text', ...commonTableHeaderConfig, visible: true },
  { label: '负责人', key: 'person_in_charge_name', width: '120px', type: 'text', ...commonTableHeaderConfig, visible: true },
  { label: '跟单员', key: 'merchandiser_name', width: '88px', type: 'text', ...commonTableHeaderConfig },
  { label: 'QC', key: 'qc', width: '88px', type: 'text', ...commonTableHeaderConfig },
  { label: '事件汇报', key: 'event_amount', width: '88px', type: 'template', templateName: 'link_button', ...commonTableHeaderConfig },
  {
    label: '生产进度',
    key: 'nodes',
    width: '1600px',
    thType: 'template',
    thTemplateName: 'nodes',
    type: 'template',
    templateName: 'nodes',
    noUseLocal: true,
    ...commonTableHeaderConfig,
    isHidePin: true,
  },

  { label: '客户名称', key: 'customer_name', width: '120px', type: 'text', ...commonTableHeaderConfig, visible: false },
  { label: '所属办事处', key: 'pro_office', width: '120px', type: 'text', ...commonTableHeaderConfig, visible: false },
  { label: '品类', key: 'category_type', width: '120px', type: 'text', ...commonTableHeaderConfig, visible: false },
  { label: '品类名称', key: 'cat_name', width: '120px', type: 'text', ...commonTableHeaderConfig, visible: false },
  { label: '品类编码', key: 'cat_no', width: '120px', type: 'text', ...commonTableHeaderConfig, visible: false },
  { label: '生产周期', key: 'lead_time', width: '120px', type: 'text', ...commonTableHeaderConfig, visible: false },
  { label: '季节', key: 'season', width: '120px', type: 'text', ...commonTableHeaderConfig, visible: false },
  { label: '生产国', key: 'country_of_origin', width: '120px', type: 'text', ...commonTableHeaderConfig, visible: false },
  { label: '颜色', key: 'colors', width: '120px', type: 'template', templateName: 'colors', ...commonTableHeaderConfig, visible: false },
  {
    label: '客单号',
    key: 'customer_io_codes',
    width: '88px',
    type: 'template',
    templateName: 'customer_io_codes',
    ...commonTableHeaderConfig,
    visible: false,
  },
  {
    label: '交付单号',
    key: 'po_codes',
    width: '100px',
    type: 'template',
    templateName: 'po_codes',
    ...commonTableHeaderConfig,
    visible: false,
  },
  { label: '客户交期', key: 'customer_due_time', width: '148px', type: 'date', ...commonTableHeaderConfig, visible: false },
  {
    label: '单价',
    key: 'fob_prices',
    width: '88px',
    type: 'template',
    templateName: 'fob_prices',
    ...commonTableHeaderConfig,
    visible: false,
  },
  { label: '总金额', key: 'contract_price', width: '98px', type: 'text', ...commonTableHeaderConfig, visible: false },
  { label: '总销售金额', key: 'total_sales_cad', width: '130px', type: 'text', ...commonTableHeaderConfig, visible: false },
  {
    label: '出货港',
    key: 'shipment_ports',
    width: '110px',
    type: 'template',
    templateName: 'shipment_ports',
    ...commonTableHeaderConfig,
    visible: false,
  },
  {
    label: '发货状态',
    key: 'shipment_status',
    width: '150px',
    type: 'template',
    templateName: 'shipment_status',
    ...commonTableHeaderConfig,
    visible: false,
    dataUrl: dictUrl,
    column: 'shipmentstatus',
  },
  {
    label: '延迟天数',
    key: 'delay_no_of_days',
    width: '120px',
    type: 'template',
    templateName: 'delay_no_of_days',
    ...commonTableHeaderConfig,
    visible: false,
  },
  {
    label: '延迟范围',
    key: 'delay_range',
    width: '150px',
    type: 'template',
    templateName: 'delay_range',
    ...commonTableHeaderConfig,
    visible: false,
    dataUrl: dictUrl,
    column: 'delayrange',
  },
  {
    label: '延迟原因',
    key: 'delay_reason',
    width: '180px',
    type: 'template',
    templateName: 'delay_reason',
    ...commonTableHeaderConfig,
    visible: false,
  },
  {
    label: '发货评级',
    key: 'ship_performance',
    width: '150px',
    type: 'template',
    templateName: 'ship_performance',
    ...commonTableHeaderConfig,
    visible: false,
    dataUrl: dictUrl,
    column: 'shipperformance',
  },
  {
    label: '延迟责任',
    key: 'delay_responsibility',
    width: '150px',
    type: 'template',
    templateName: 'delay_responsibility',
    ...commonTableHeaderConfig,
    visible: false,
    dataUrl: dictUrl,
    column: 'delayresponsibility',
    thType: 'template',
    thTemplateName: 'delay_responsibility',
  },
  {
    label: '运输时间',
    key: 'transit_time',
    width: '120px',
    type: 'template',
    templateName: 'transit_time',
    ...commonTableHeaderConfig,
    visible: false,
  },
  {
    label: '原始到仓日',
    key: 'original_isd',
    width: '140px',
    type: 'template',
    templateName: 'original_isd',
    ...commonTableHeaderConfig,
    visible: false,
  },
  {
    label: '更新到仓日',
    key: 'revised_isd',
    width: '140px',
    type: 'template',
    templateName: 'revised_isd',
    ...commonTableHeaderConfig,
    visible: false,
  },
];

const CTCDefaultTableHeaders: any[] = [
  {
    label: '订单需求号',
    key: 'bulk_order_code',
    width: '130px',
    type: 'template',
    templateName: 'bulk_order_code',
    ...commonTableHeaderConfig,
    pinned: true,
    visible: true,
  },
  { label: '业务员', key: 'biz_emp_name', width: '120px', type: 'text', ...commonTableHeaderConfig, visible: true },
  { label: '负责人', key: 'person_in_charge_name', width: '120px', type: 'text', ...commonTableHeaderConfig, visible: false },
  { label: '客户名称', key: 'customer_name', width: '120px', type: 'text', ...commonTableHeaderConfig, visible: true },
  { label: '品牌', key: 'brand_name', width: '120px', type: 'text', ...commonTableHeaderConfig, visible: true },
  { label: '所属办事处', key: 'pro_office', width: '120px', type: 'text', ...commonTableHeaderConfig, visible: true },
  { label: '品类', key: 'category_type', width: '120px', type: 'text', ...commonTableHeaderConfig, visible: true },
  { label: '品类名称', key: 'cat_name', width: '120px', type: 'text', ...commonTableHeaderConfig, visible: true },
  { label: '品类编码', key: 'cat_no', width: '120px', type: 'text', ...commonTableHeaderConfig, visible: true },
  { label: '生产周期', key: 'lead_time', width: '120px', type: 'text', ...commonTableHeaderConfig, visible: true },
  { label: '季节', key: 'season', width: '120px', type: 'text', ...commonTableHeaderConfig, visible: true },
  { label: '款式编码', key: 'style_code', width: '168px', type: 'text', ...commonTableHeaderConfig, visible: true },
  { label: '品名', key: 'category', width: '120px', type: 'text', ...commonTableHeaderConfig, visible: true },
  { label: '生产国', key: 'country_of_origin', width: '120px', type: 'text', ...commonTableHeaderConfig, visible: true },
  { label: '跟单员', key: 'merchandiser_name', width: '88px', type: 'text', ...commonTableHeaderConfig, visible: true },
  { label: 'QC', key: 'qc', width: '88px', type: 'text', ...commonTableHeaderConfig, visible: false },
  { label: '加工厂', key: 'factory_name', width: '118px', type: 'text', ...commonTableHeaderConfig, visible: true },
  { label: '颜色', key: 'colors', width: '120px', type: 'template', templateName: 'colors', ...commonTableHeaderConfig, visible: true },
  {
    label: '客单号',
    key: 'customer_io_codes',
    width: '88px',
    type: 'template',
    templateName: 'customer_io_codes',
    ...commonTableHeaderConfig,
    visible: true,
  },
  {
    label: '交付单号',
    key: 'po_codes',
    width: '100px',
    type: 'template',
    templateName: 'po_codes',
    ...commonTableHeaderConfig,
    visible: true,
  },
  { label: '交付时间', key: 'due_time', width: '148px', type: 'date', ...commonTableHeaderConfig, visible: true },
  { label: '客户交期', key: 'customer_due_time', width: '148px', type: 'date', ...commonTableHeaderConfig, visible: true },
  {
    label: '单价',
    key: 'fob_prices',
    width: '88px',
    type: 'template',
    templateName: 'fob_prices',
    ...commonTableHeaderConfig,
    visible: true,
  },
  { label: '订单总数', key: 'order_quantity', width: '100px', type: 'text', ...commonTableHeaderConfig, visible: true },
  { label: '总金额', key: 'contract_price', width: '98px', type: 'text', ...commonTableHeaderConfig, visible: true },
  { label: '总销售金额', key: 'total_sales_cad', width: '130px', type: 'text', ...commonTableHeaderConfig, visible: true },
  {
    label: '出货港',
    key: 'shipment_ports',
    width: '110px',
    type: 'template',
    templateName: 'shipment_ports',
    ...commonTableHeaderConfig,
    visible: true,
  },
  { label: '进度模版', key: 'template_name', width: '120px', type: 'text', ...commonTableHeaderConfig, visible: true },

  { label: '批次', key: 'order_category_value', width: '80px', type: 'text', ...commonTableHeaderConfig, visible: false },
  { label: '订单类型', key: 'order_classification', width: '80px', type: 'text', ...commonTableHeaderConfig, visible: false },
  {
    label: '生产类型',
    key: 'order_production_type_value',
    width: '80px',
    type: 'text',
    ...commonTableHeaderConfig,
    visible: false,
  },

  { label: '款式图', key: 'order_pictures', width: '80px', type: 'image', ...commonTableHeaderConfig, visible: false },
  { label: '下单日期', key: 'order_time', width: '124px', type: 'date', ...commonTableHeaderConfig, visible: false },
  {
    label: '款式分类',
    key: 'style_class',
    width: '168px',
    type: 'template',
    templateName: 'style_class',
    ...commonTableHeaderConfig,
    visible: false,
  },
  { label: '下单数量', key: 'assigned_qty', width: '120px', type: 'text', ...commonTableHeaderConfig, visible: false }, // 原 分配订单数量
  { label: '订单占有数', key: 'order_number', width: '120px', type: 'text', ...commonTableHeaderConfig, visible: false },

  { label: '二次工艺类型', key: 'extra_processes', width: '108px', type: 'text', ...commonTableHeaderConfig, visible: false },
  {
    label: '二次配套工厂',
    key: 'extra_process_factories',
    width: '148px',
    type: 'template',
    templateName: 'extra_process_factories',
    ...commonTableHeaderConfig,
    visible: false,
  },
  { label: '订单进度', key: 'order_production_status', width: '148px', type: 'text', ...commonTableHeaderConfig, visible: false },
  {
    label: '订单状态',
    key: 'out_sourcing_status',
    width: '80px',
    type: 'template',
    templateName: 'out_sourcing_status',
    ...commonTableHeaderConfig,
    visible: false,
  },
  {
    label: '事件汇报',
    key: 'event_amount',
    width: '88px',
    type: 'template',
    templateName: 'link_button',
    ...commonTableHeaderConfig,
    visible: false,
  },
  {
    label: '生产进度',
    key: 'nodes',
    width: '1600px',
    thType: 'template',
    thTemplateName: 'nodes',
    type: 'template',
    templateName: 'nodes',
    noUseLocal: true,
    ...commonTableHeaderConfig,
    visible: true,
    isHidePin: true,
  },

  {
    label: '发货状态',
    key: 'shipment_status',
    width: '150px',
    type: 'template',
    templateName: 'shipment_status',
    ...commonTableHeaderConfig,
    visible: true,
    dataUrl: dictUrl,
    column: 'shipmentstatus',
  },
  {
    label: '延迟天数',
    key: 'delay_no_of_days',
    width: '120px',
    type: 'template',
    templateName: 'delay_no_of_days',
    ...commonTableHeaderConfig,
    visible: true,
  },
  {
    label: '延迟范围',
    key: 'delay_range',
    width: '150px',
    type: 'template',
    templateName: 'delay_range',
    ...commonTableHeaderConfig,
    visible: true,
    dataUrl: dictUrl,
    column: 'delayrange',
  },
  {
    label: '延迟原因',
    key: 'delay_reason',
    width: '180px',
    type: 'template',
    templateName: 'delay_reason',
    ...commonTableHeaderConfig,
    visible: true,
  },
  {
    label: '发货评级',
    key: 'ship_performance',
    width: '150px',
    type: 'template',
    templateName: 'ship_performance',
    ...commonTableHeaderConfig,
    visible: true,
    dataUrl: dictUrl,
    column: 'shipperformance',
  },
  {
    label: '延迟责任',
    key: 'delay_responsibility',
    width: '150px',
    type: 'template',
    templateName: 'delay_responsibility',
    ...commonTableHeaderConfig,
    visible: true,
    dataUrl: dictUrl,
    column: 'delayresponsibility',
    thType: 'template',
    thTemplateName: 'delay_responsibility',
  },
  {
    label: '运输时间',
    key: 'transit_time',
    width: '120px',
    type: 'template',
    templateName: 'transit_time',
    ...commonTableHeaderConfig,
    visible: true,
  },
  {
    label: '原始到仓日',
    key: 'original_isd',
    width: '140px',
    type: 'template',
    templateName: 'original_isd',
    ...commonTableHeaderConfig,
    visible: true,
  },
  {
    label: '更新到仓日',
    key: 'revised_isd',
    width: '140px',
    type: 'template',
    templateName: 'revised_isd',
    ...commonTableHeaderConfig,
    visible: true,
  },
];

export function initDefaultTableHeaders() {
  return factoryCodes.includes(user?.factory_code) ? CTCDefaultTableHeaders : DefaultTableHeaders;
}

// 详情页按钮集合
export const BtnConfig = [
  { type: 'default', label: 'back', text: '返回' },
  { type: 'default', label: 'cancel', text: '取消' },
  { type: 'pretty-minor', label: 'edit', text: '编辑' },
  { type: 'pretty-minor', label: 'save', text: '保存', icon: 'icon-baocun' },
];

// 操作模式
export enum FormModeEnum {
  ReadOnly,
  Create,
  Edit,
}

export const DetailFormList = [
  {
    type: 'input',
    label: '客户编码',
    code: 'code',
    columnkey: '',
    value: 'code',
    require: true,
    maxlength: 20,
  },
  {
    type: 'input',
    label: '客户全称',
    code: 'name',
    columnkey: '',
    value: 'name',
    require: true,
    maxlength: 50,
  },
  {
    type: 'input',
    label: '客户简称',
    code: 'short_name',
    columnkey: '',
    value: 'short_name',
    require: true,
    maxlength: 20,
  },
  {
    type: 'switch',
    label: '状态',
    code: 'status',
    columnkey: '',
    value: 'status',
    require: true,
  },
  {
    type: 'input',
    label: '统一社会信用代码',
    code: 'usci',
    columnkey: '',
    value: 'usci',
    require: false,
    maxlength: 100,
  },
  {
    type: 'local-select',
    label: '货币单位',
    code: 'unit_id',
    columnkey: 'unit',
    value: 'unit_name',
    require: false,
    options: [],
  },
  {
    type: 'number',
    label: '直接汇率',
    code: 'rate',
    tooltip: '直接汇率=外币价格/人民币价格',
    columnkey: '',
    value: 'rate',
    require: false,
  },
  {
    type: 'local-select',
    label: '运输方式',
    code: 'shipping_method',
    columnkey: 'shipping_method',
    value: 'shipping_method_name',
    require: false,
    options: [],
  },
  {
    type: 'cascader',
    label: '收货国',
    code: 'receiver_country',
    columnkey: 'receiver_country_name',
    value: 'receiver_country_name',
    require: false,
    options: [],
  },
  {
    type: 'input',
    label: '收货港',
    code: 'receiver_port',
    columnkey: '',
    value: 'receiver_port',
    require: false,
    maxlength: 100,
  },
  {
    type: 'input',
    label: '详细地址',
    code: 'address',
    columnkey: '',
    require: false,
    value: 'address',
    maxlength: 255,
  },
  {
    type: 'input',
    label: '联系人',
    code: 'contact_name',
    value: 'contact_name',
    columnkey: '',
    require: false,
    maxlength: 100,
  },
  {
    type: 'input',
    label: '联系电话',
    code: 'contact_phone',
    value: 'contact_phone',
    columnkey: '',
    require: false,
    maxlength: 20,
  },
  {
    type: 'input',
    label: '开户行',
    code: 'deposit_bank',
    value: 'deposit_bank',
    columnkey: '',
    require: false,
    maxlength: 100,
  },
  {
    type: 'input',
    label: '银行账号',
    code: 'bank_account',
    value: 'bank_account',
    columnkey: '',
    require: false,
    maxlength: 100,
  },
  {
    type: 'input',
    label: '备注',
    code: 'remark',
    value: 'remark',
    columnkey: '',
    require: false,
    maxlength: 255,
  },
];
// 物料模式
export enum MaterialEnum {
  物料采购 = 1,
  物料入库 = 2,
  领料出库 = 3,
}
// 物料出入库状态枚举
export const STATUSMAP = {
  物料采购: {
    1: '未开始',
    2: '采购中',
    3: '采购完成',
  },
  物料入库: {
    0: '待入库',
    1: '待入库',
    2: '已入库',
  },
  领料出库: {
    0: '待入库',
    1: '待出库',
    2: '已出库',
  },
};

export interface ProductionTableNodePoItem {
  po_unique_code: string;
  po_code: string;
  total_qty: number;
}

export interface ProductionTableNodeDetailItem {
  line_list: ProductionTableColorSizeLine[];
  material_list: ProductionTableMaterialLine[];
  po_finish_list: ProductionTableNodePoItem[];
}
// 裁剪, 车缝, 后道
export interface ProductionTableColorSizeLine {
  color_id: number;
  color_name: string;
  spec_id: number;
  spec_size: string;
  qty: number;
  order_qty: number;
  po_code: string;
  po_unique_code: string;
  gen_user_name: string;
  finished_time: number;
  total_qty: number;
  checked: boolean;
}
//物料
export interface ProductionTableMaterialLine {
  material_name: string; //面料名称
  material_color_name: string; // 面料颜色
  supplier_name: string; // 供应商
  specification_name: string; // 规格
  specifications: string; // 规格
  material_type: number; // 面料类型
  qty: string; // 已入库数
  unit_name: string; // 单位
  total_qty: string; // 总数量
  material_qty: string; // 物料总需求数量
  sku_id: number;
  order_color_id: number; // 订单颜色
  order_color_name: string; // 订单颜色
}

export interface ProductionTableOrderLineInfo {
  assigned_qty: number;
  band_id: number;
  band_name: string;
  brand_id: number;
  brand_name: string;
  bulk_order_code: string;
  bulk_order_id: number;
  category: string;
  extra_process_factories: any[];
  extra_processes: string[];
  factory_id: number;
  factory_name: string;
  factory_out_sourcing_id: number;
  first_style_class_id: number;
  first_style_class_name: string;
  id: number;
  max_due_time: number;
  due_time: number;
  merchandiser_id: number;
  merchandiser_name: string;
  nodes: ProductionTableOrderNode[];
  order_category: number;
  order_category_value: string;
  order_classification: string;
  order_number: number;
  order_pictures: string[];
  order_production_status: string;
  order_production_type: number;
  order_production_type_value: string;
  order_quantity: number;
  order_time: number;
  out_sourcing_status: number;
  qc: string;
  second_style_class_id: number;
  second_style_class_name: string;
  style_code: string;
  style_lib_id: number;
  template_id: number;
  template_name: string;
  third_style_class_id: number;
  third_style_class_name: string;
}

export interface ProductionTableOrderNode {
  actual_end_time: number;
  actual_qty: number;
  actual_qty_source: number;
  actual_start_time: number;
  bulk_node_status: number;
  complete_status: boolean;
  consumed_seconds: number;
  content_type: number;
  exceed_seconds: number;
  id: number;
  index: number;
  inspected: boolean;
  line_list: any[];
  material_list: any[];
  node_list: any[];
  /** 1 裁剪 2 车缝 3 巡检 4 后道   5首件首包 6中期 7尾期 8物料 */
  node_type: number;
  plan_end_time: number;
  plan_qty: number;
  plan_start_time: number;
  plan_warn_time: number;
  repair_qty: number;
  rest_seconds: number;
  status: number;
  template_node_alias_name: string;
  template_node_factory_authority: number;
  template_node_name: string;
  total_qty: number;
  defected_qty: number;
  inspected_qty: number;
  fixed_template: number;
  node_temp_data_list: any[];
  template_type: number; // 模版类型
}
