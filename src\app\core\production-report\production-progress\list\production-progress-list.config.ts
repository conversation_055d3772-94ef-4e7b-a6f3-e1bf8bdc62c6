import { DimensionRange, ReportRange } from '../../production-report.enum';
import { ProductionHeaderType } from '../models/production-progress-interface';

export function getTableHeaders(tab: ReportRange, dimension: DimensionRange): ProductionHeaderType[] {
  const baseHedears = [
    {
      key: 'biz_date',
      label: '日期',
      width: '88px',
      type: 'day',
      pinned: true,
      disabled: true,
      tab: ReportRange.daily,
      isPoIndex: true,
    },
    {
      key: 'io_code',
      label: '大货单号',
      width: '144px',
      type: 'text',
      pinned: true,
      disabled: true,
      isPoIndex: true,
    },
    {
      key: 'customer_style',
      label: '款式编码',
      width: '144px',
      type: 'text',
      pinned: true,
      isPoIndex: true,
    },
    {
      key: 'category',
      label: '品名',
      width: '104px',
      type: 'text',
      isPoIndex: true,
    },
    {
      key: 'po_code',
      label: '交付单',
      width: '144px',
      type: 'text',
      dimension: DimensionRange.po,
      subIndex: true,
    },
    {
      key: 'due_time_list',
      label: '交期',
      width: '104px',
      type: 'day',
      sort: true,
      subIndex: true,
    },
    {
      key: 'order_total',
      label: '订单数',
      width: '144px',
      type: 'number',
      subIndex: true,
      hasTotal: true,
    },
    {
      key: 'customer',
      label: '客户',
      width: '144px',
      type: 'text',
      subIndex: true,
    },
    {
      key: 'factory_name',
      label: '加工厂',
      width: '160px',
      type: 'text',
    },
    {
      key: 'country_name',
      label: '加工厂国别',
      width: '160px',
      type: 'text',
    },
    {
      key: 'line_names',
      label: '产线',
      width: '100px',
      type: 'text',
    },
    {
      key: 'plan_times',
      label: '生产计划起止日期',
      width: '200px',
      type: 'template',
    },
    {
      key: 'order_count',
      label: '分配订单数',
      width: '104px',
      type: 'cellTpl',
      hasTotal: true,
    },
    {
      key: 'material_status_name',
      label: '物料进度',
      width: '144px',
      type: 'text',
    },
  ];
  const _or = tab === ReportRange.daily ? dayTableHeader : summaryTableHeader;

  return [
    ...baseHedears.filter((item) => !item.tab || item.tab === tab).filter((item) => !item.dimension || item.dimension === dimension),
    ..._or,
    {
      key: 'status',
      label: dimension === DimensionRange.io ? '生产状态' : '走货状态',
      type: 'template',
      width: '144px',
    },
  ].map((item) => ({ ...item, visible: true }));
}

const dayTableHeader = [
  {
    key: 'daily_cutting_qty',
    label: '日裁数',
    width: '144px',
    type: 'cellTpl',
    hasTotal: true,
  },
  {
    key: 'cutting_qty',
    label: '累计裁数',
    width: '144px',
    type: 'cellTpl',
    hasTotal: true,
  },
  {
    key: 'daily_sewing_qty',
    label: '日车缝完成',
    width: '144px',
    type: 'cellTpl',
    hasTotal: true,
  },
  {
    key: 'sewing_qty',
    label: '累计车缝完成',
    width: '144px',
    type: 'cellTpl',
    hasTotal: true,
  },
  {
    key: 'daily_consolidation_qty',
    label: '日后道完成',
    width: '144px',
    type: 'cellTpl',
    hasTotal: true,
  },
  {
    key: 'consolidation_qty',
    label: '累计后道完成',
    width: '144px',
    type: 'cellTpl',
    hasTotal: true,
  },
  {
    key: 'daily_qualified_qty',
    subKey: 'daily_defective_qty',
    label: '质检-日合格/不良数',
    width: '164px',
    type: 'qualityTpl',
    hasTotal: true,
  },
  {
    key: 'qualified_qty',
    subKey: 'defective_qty',
    label: '质检-累计合格/不良数',
    width: '164px',
    type: 'qualityTpl',
    hasTotal: true,
  },
  {
    key: 'daily_final_qualified_qty',
    subKey: 'daily_final_defective_qty',
    label: '终检-日合格/不良数',
    width: '164px',
    type: 'qualityTpl',
    hasTotal: true,
  },
  {
    key: 'final_qualified_qty',
    subKey: 'final_defective_qty',
    label: '终检-累计合格/不良数',
    width: '164px',
    type: 'qualityTpl',
    hasTotal: true,
  },
  {
    key: 'daily_transport_qty',
    label: '日走货数',
    width: '144px',
    type: 'cellTpl',
    hasTotal: true,
  },
  {
    key: 'transport_qty',
    label: '累计走货数',
    width: '144px',
    type: 'cellTpl',
    hasTotal: true,
  },
];

const summaryTableHeader = [
  {
    key: 'cutting_qty',
    label: '总裁数',
    width: '144px',
    type: 'cellTpl',
    hasTotal: true,
  },
  {
    key: 'sewing_qty',
    label: '车缝完成数',
    width: '144px',
    type: 'cellTpl',
    hasTotal: true,
  },
  {
    key: 'consolidation_qty',
    label: '后道完成数',
    width: '144px',
    type: 'cellTpl',
    hasTotal: true,
  },
  {
    key: 'qualified_qty',
    subKey: 'defective_qty',
    label: '质检-合格/不良数',
    width: '164px',
    type: 'qualityTpl',
    hasTotal: true,
  },
  {
    key: 'final_qualified_qty',
    subKey: 'final_defective_qty',
    label: '终检-合格/不良数',
    width: '164px',
    type: 'qualityTpl',
    hasTotal: true,
  },
  {
    key: 'transport_qty',
    label: '走货数',
    width: '144px',
    type: 'cellTpl',
    hasTotal: true,
  },
  {
    key: 'progress',
    label: '进度',
    width: '144px',
    type: 'template',
    hasTotal: true,
  },
];
