import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { finalize } from 'rxjs';
import { Component, OnInit } from '@angular/core';
import { addDays, addMonths, format, subDays } from 'date-fns';
import { CapacityPlanService } from './capacity-plan.service';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { FlcTableHeaderConfig } from 'fl-common-lib';
import { CapacityFilterList, CapacityList, CapacityListParam, SearchData } from './capacity-plan.interface';
import { AppStorageService } from 'src/app/shared/app-storage.service';

@Component({
  selector: 'app-capacity-plan',
  templateUrl: './capacity-plan.component.html',
  styleUrls: ['./capacity-plan.component.scss'],
})
export class CapacityPlanComponent implements OnInit {
  dates = {
    start_date: subDays(new Date(format(new Date(), 'yyyy-MM-dd') + ' 00:00:00'), 15),
    end_date: addDays(new Date(format(addMonths(new Date(), 1), 'yyyy-MM-dd') + ' 00:00:00'), 15),
  };
  collapse = false;
  isEdit = false;

  ranges = {
    [this.translate.instant('plan.capacityPlan.半个月')]: [new Date(), addDays(new Date(), 15)],
    [this.translate.instant('plan.capacityPlan.一个月')]: [new Date(), addMonths(new Date(), 1)],
    [this.translate.instant('plan.capacityPlan.三个月')]: [new Date(), addMonths(new Date(), 3)],
  };

  // 表格配置
  tableConfig: any = {
    tableName: 'code', // 订单维度
    hasAction: false,
    dataList: [],
    count: 0,
    pageSize: 20,
    pageIndex: 1,
    order_by: [],
    height: 400,
    loading: false,
    uniqueId: 'id',
    canResizeHeader: true,
  };
  headerBaseConfig = { visible: true, type: 'text', width: '112px', sort: false, pinned: false };
  renderHeader: Partial<FlcTableHeaderConfig<CapacityList>>[] = [
    { label: this.translate.instant('plan.capacityPlan.大货单号'), key: 'io_code', ...this.headerBaseConfig, width: '136px', type: 'text' },
    {
      label: this.translate.instant('plan.capacityPlan.交付单号'),
      key: 'po_code',
      ...this.headerBaseConfig,
      width: '136px',
      type: 'text',
      visible: false,
    },
    {
      label: this.translate.instant('plan.capacityPlan.款式编码'),
      key: 'style_code',
      ...this.headerBaseConfig,
      type: 'text',
      width: '136px',
    },
    { label: this.translate.instant('plan.capacityPlan.品名'), key: 'category', ...this.headerBaseConfig, width: '160px', type: 'text' },
    {
      label: this.translate.instant('plan.capacityPlan.款式分类'),
      key: 'style_name',
      ...this.headerBaseConfig,
      width: '120px',
      type: 'text',
    },
    {
      label: this.translate.instant('plan.capacityPlan.款式图'),
      ...this.headerBaseConfig,
      width: '80px',
      type: 'template',
      templateName: 'order_picture',
    },
    {
      label: this.translate.instant('plan.capacityPlan.二次工艺'),
      key: 'secondary_process',
      ...this.headerBaseConfig,
      width: '120px',
      type: 'text',
    },
    {
      label: this.translate.instant('plan.capacityPlan.交期/件数'),
      ...this.headerBaseConfig,
      width: '150px',
      type: 'template',
      templateName: 'do_lines',
    },
    {
      label: this.translate.instant('plan.capacityPlan.已分配'),
      ...this.headerBaseConfig,
      width: '120px',
      type: 'template',
      templateName: 'sourced_lines',
    },
    {
      label: this.translate.instant('plan.capacityPlan.预估SAM(分钟)'),
      ...this.headerBaseConfig,
      width: '120px',
      type: 'template',
      templateName: 'sam',
    },
  ];

  searchOptions: CapacityFilterList = {
    io_codes: [],
    po_codes: [],
    style_codes: [],
  };
  searchData: SearchData = {
    io_code: null,
    style_code: null,
    po_code: null,
    date: [new Date(), addMonths(new Date(), 1)],
    factory: null,
  };
  isPo = false;
  showFullOrder = false;
  showFullCapacity = false;
  btnArr: string[] = [];
  tooltipVisible = false;

  constructor(
    public _service: CapacityPlanService,
    private translate: TranslateService,
    private _notice: NzNotificationService,
    public _storage: AppStorageService,
    private _msg: NzMessageService
  ) {}

  ngOnInit() {
    this.btnArr = this._storage.getUserActions('intellect-plan/capacity-plan');

    this._service.filterList().subscribe((res) => {
      if (res.code === 200) {
        this.searchOptions = res.data;
      }
    });

    this.getOrderList();
  }

  reset() {
    this.searchData = {
      io_code: null,
      style_code: null,
      po_code: null,
      date: [new Date(), addMonths(new Date(), 1)],
      factory: null,
    };
    this.tableConfig.pageIndex = 1;

    this.getOrderList();
  }

  expandChange() {
    this.collapse = !this.collapse;
  }

  getOrderList() {
    const payload: CapacityListParam = {
      io_code: this.searchData.io_code,
      style_code: this.searchData.style_code,
      size: this.tableConfig.pageSize,
      page: this.tableConfig.pageIndex,
    };

    if (this.isPo) {
      Object.assign(payload, {
        po_code: this.searchData.po_code,
      });
    }

    this.tableConfig.loading = true;

    const method = this.isPo ? this._service.poList(payload) : this._service.ioList(payload);
    method.pipe(finalize(() => (this.tableConfig.loading = false))).subscribe((res) => {
      if (res.code === 200) {
        this.tableConfig.count = res.data.total;
        this.tableConfig.dataList = res.data.order_infos || res.data.po_infos;

        this.tableConfig = { ...this.tableConfig };
      }
    });
  }

  filterList() {
    this.tableConfig.pageIndex = 1;

    this.getOrderList();
  }

  dateChange($event: [Date, Date]) {
    const [startDate, endDate] = $event;

    this.dates = {
      start_date: subDays(new Date(format(startDate, 'yyyy-MM-dd') + ' 00:00:00'), 15),
      end_date: addDays(new Date(format(endDate, 'yyyy-MM-dd') + ' 00:00:00'), 15),
    };
  }

  editSam(data: CapacityList) {
    data.edit = true;
  }

  cancelChange(data: CapacityList) {
    data.edit = false;
    this.getOrderList();
  }

  saveSam(data: CapacityList) {
    const sam = Number(data.sam);

    if (isNaN(sam)) {
      this._notice.error(this.translate.instant('plan.capacityPlan.请输入数字'), '');
      return;
    }

    if (sam >= 1000000000) {
      this._notice.error(this.translate.instant('plan.capacityPlan.不可超过9位数'), '');
      return;
    }

    if (!/^(-)?\d+(\.[0-9]{1,5})?$/.test(String(sam))) {
      this._notice.error(this.translate.instant('plan.capacityPlan.不可超过5位小数'), '');
      return;
    }

    data.edit = false;

    this._service
      .editSam({
        uuid: data.style_code_uuid,
        sam: sam,
      })
      .subscribe(
        (res) => {
          if (res.code === 200) {
            this._msg.success(this.translate.instant('plan.capacityPlan.修改成功'));
            this.getOrderList();
          } else {
            this._msg.error(this.translate.instant('plan.capacityPlan.修改失败'));
          }
        },
        () => {
          this._msg.error(this.translate.instant('plan.capacityPlan.修改失败'));
        }
      );
  }

  changeDimension() {
    this.isPo = !this.isPo;
    this.tableConfig.pageIndex = 1;
    this.tableConfig.pageSize = 20;
    this.tableConfig.tableName = this.tableConfig.tableName === 'code' ? 'poCode' : 'code';

    this.renderHeader[1].visible = this.isPo;
    this.renderHeader = [...this.renderHeader];

    this.getOrderList();
  }

  onSizeChanges(pageSize: number) {
    this.tableConfig.pageSize = pageSize;
    this.getOrderList();
  }
  onIndexChange(pageIndex: number) {
    this.tableConfig.pageIndex = pageIndex;
    this.getOrderList();
  }
}
