import { Compo<PERSON>, <PERSON>ementR<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { NzResizeEvent } from 'ng-zorro-antd/resizable';
import { isNotNil } from 'ng-zorro-antd/core/util';
import { format } from 'date-fns';
import { TableHeaderConfig, TableHelperService } from 'src/app/services/table-helper/table-helper.service';
import { EmployeeService } from '../employee.service';
import { Resizable } from 'src/app/decorator/resizable';
import { debounceTime, finalize, switchMap } from 'rxjs/operators';
import { Subject, Subscription } from 'rxjs';
import { NzNotificationService } from 'ng-zorro-antd/notification';
const version = '1.0.2';
type lineItem = TableHeaderConfig<any>;
const defaultHeaders: lineItem[] = [
  { label: '工号', key: 'code', pinned: true, visible: true, width: 'auto', type: 'text', disable: false, resizeble: true },
  { label: '员工姓名', key: 'name', visible: true, width: 'auto', type: 'text', disable: false, resizeble: true, pinned: false },
  {
    label: '权限名称',
    key: 'roles',
    visible: true,
    width: '220px',
    type: 'template',
    template: 'roles',
    disable: false,
    resizeble: true,
    pinned: false,
  },
  { label: '状态', key: 'status', visible: true, width: 'auto', type: 'text', disable: false, resizeble: true, pinned: false },
  { label: '创建人', key: 'gen_user', visible: true, width: 'auto', type: 'text', disable: false, resizeble: true, pinned: false },
  { label: '创建日期', key: 'gen_time', visible: true, width: 'auto', type: 'datetime', disable: false, resizeble: true, pinned: false },
];
@Component({
  selector: 'app-employee-list.',
  templateUrl: './employee-list.component.html',
  styleUrls: ['./employee-list.component.scss'],
})
export class EmployeeListComponent extends Resizable implements OnInit, OnDestroy {
  @ViewChild('header') hearderComponent?: ElementRef;
  translateName = 'employee.';
  tableHeight = 0;
  headers: lineItem[] = [];
  renderHeaders: lineItem[] = [];
  loading = false; //
  optionLoading = false;
  total = 0;
  pageIndex = 1;
  pageSize = 20;
  isVisibleDetail = false; // 是否显示弹窗按钮
  detail: any; // 头像
  tableData: any; // 表格数据
  searchData: any = {
    code: null,
    name: null,
    roles: null,
    status: null,
    gen_user: null,
    gen_time: [],
  };
  orderBy: any = [];
  searchList: any = [
    {
      label: '工号',
      code: 'code',
    },
    {
      label: '员工姓名',
      code: 'name',
    },
    {
      label: '权限名称',
      code: 'roles',
    },
    {
      label: '状态',
      code: 'status',
      options: [
        { label: '在职', value: 1 },
        { label: '离职', value: 0 },
      ],
    },
    {
      label: '创建人',
      code: 'gen_user',
    },
  ];
  btnHighLight = false;
  gen_time: 'desc' | 'asc' | null = null; // 创建日期排序
  subject$ = new Subject<boolean>();
  subscription?: Subscription;

  constructor(
    private _service: EmployeeService,
    private _tableHelper: TableHelperService,
    private _translate: TranslateService,
    private _notice: NzNotificationService
  ) {
    super();
  }

  ngOnInit(): void {
    super.ngOnInit();
    this.headers = this._tableHelper.getTableHeaderConfig<lineItem>(defaultHeaders, version);
    this.getRenderHeaders();

    this.subscription = this.subject$.pipe(debounceTime(500)).subscribe((res) => {
      this.getAllemployee(res);
    });
    this.subject$.next(false);
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.resize();
    });
  }

  ngOnDestroy() {
    this.subscription?.unsubscribe();
  }

  resize() {
    const headerHeight = this.hearderComponent?.nativeElement?.offsetHeight ?? 32;
    this.tableHeight = window.innerHeight - headerHeight - 180;
    //table最小高度
    if (this.tableHeight < 200) {
      this.tableHeight = 200;
    }
  }
  /**
   * 排序
   * @param  {any} e
   */
  sortOrderChange(e: any) {
    this.orderBy = [];
    if (e) {
      this.orderBy.push('gen_time' + ' ' + e);
    }
    this.subject$.next(false);
  }
  /**
   * 详情弹窗
   * @param  {} item
   */
  todetail(item: any) {
    this.isVisibleDetail = true;
    this.detail = item;
  }

  /**
   * 表格头部伸缩
   * @param  {NzResizeEvent} {width}
   * @param  {string} col
   * @returns void
   */
  onResize({ width }: NzResizeEvent, col: string): void {
    this.headers = this._tableHelper.tableResize<lineItem>(width, col, this.headers, version);
    this.getRenderHeaders();
  }
  /**
   * 表头数据
   */
  getRenderHeaders() {
    this.renderHeaders = this._tableHelper.getRenderHeader<lineItem>(this.headers);
  }

  /**
   * 日期选择
   * @param  {any} e
   */
  onChange() {
    this.subject$.next(false);
  }
  /**
   * 下拉选择搜索
   */
  onSearch() {
    this.pageIndex = 1;
    this.subject$.next(false);
  }

  /**
   * 切页码
   */
  onChangePageIndex() {
    this.subject$.next(false);
  }

  /**
   * 获取列表数据
   * @param  {} reset=false
   */
  getAllemployee(reset = false) {
    this.loading = true;
    this.pageIndex = reset ? 1 : this.pageIndex;
    const where = this.handleWhere(reset) ?? {};
    this._service
      .getAllemployee({
        where: where,
        orderBy: this.orderBy,
        page: this.pageIndex,
        limit: this.pageSize,
      })
      .subscribe((res) => {
        if (res.code === 200) {
          this.loading = false;
          this.tableData = res.data.data;
          this.total = res.data.len;
        }
      });
  }

  /**
   * 重置
   */
  reset() {
    this.searchData = {
      code: null,
      name: null,
      roles: null,
      status: null,
      gen_user: null,
      gen_time: null,
    };
    this.subject$.next(true);
  }
  /**
   * 获取搜索项
   * @param  {} reset=false
   * @returns object
   */
  handleWhere(reset = false): object {
    const where: { [key: string]: any } = {};
    if (!reset) {
      Object.entries(this.searchData).forEach((item: any) => {
        if (isNotNil(item[1])) {
          if (item[0] === 'roles') {
            if (item[1].length) {
              where[item[0]] = {
                op: 'in',
                value: item[1],
              };
            }
          } else if (item[0] === 'gen_time') {
            if (item[1].length) {
              const startTime = format(item[1][0], 'yyyy-MM-dd');
              const endTime = format(item[1][1], 'yyyy-MM-dd');
              where[item[0]] = {
                op: 'between',
                value: [startTime, endTime],
              };
            }
          } else {
            item[0] = item[0] === 'gen_user' ? 'gen_user_id' : item[0];
            where[item[0]] = {
              op: '=',
              value: item[1],
            };
          }
        }
      });
    }
    return where;
  }
  /**
   * 表头隐藏项
   * @param  {MouseEvent} event
   * @returns void
   */
  changeHeader(event: MouseEvent): void {
    this.btnHighLight = true;
    const shadow = JSON.parse(JSON.stringify(this.headers));
    for (const item of shadow) {
      this._translate
        .get(this.translateName + item.label)
        .subscribe((res: string) => {
          item.label = res;
        })
        .unsubscribe();
    }
    this._tableHelper
      .openTableHeaderMidifyDialog<lineItem>(shadow, event.target as HTMLElement)
      .pipe(finalize(() => (this.btnHighLight = false)))
      .subscribe((res) => {
        if (res) {
          for (const item of res) {
            item.label = defaultHeaders.find((i) => i.key === item.key)?.label ?? '';
          }
          this.headers = res;
          this.getRenderHeaders();
          this._tableHelper.saveTableHeaderConfig(this.headers, version);
        }
      });
  }
}
