import { Component, OnInit, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { addDays, addMonths, format, startOfDay, startOfMonth } from 'date-fns';
import { NzCalendarComponent } from 'ng-zorro-antd/calendar';
import { CalendarService } from './calendar.service';
import { finalize } from 'rxjs';
import { NzMessageService } from 'ng-zorro-antd/message';
import { FlcModalService } from 'fl-common-lib';
import { AppStorageService } from 'src/app/shared/app-storage.service';

@Component({
  selector: 'app-calendar',
  templateUrl: './calendar.component.html',
  styleUrls: ['./calendar.component.scss'],
})
export class CalendarComponent implements OnInit {
  @ViewChild('calendar') calendar!: NzCalendarComponent;
  constructor(
    private _translate: TranslateService,
    private _message: NzMessageService,
    private _service: CalendarService,
    private _flcModalService: FlcModalService,
    private _storage: AppStorageService
  ) {
    this.fieldArr = this._storage.getFieldActions('sys-settings/calendar');
  }
  format = 'yyyy-MM-dd';
  treeLoading = false;
  editable = false;
  currentDate!: Date;
  translateName = 'calendarSetting.';
  months: string[] = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];

  countryList: any[] = [];
  selectedCountryCode = null;
  selectedCountryName = '';

  // 已经提交过的日期列表
  calendarSettedDate: any = {};

  // 当前改变的日期列表
  changedDateList: any[] = [];

  get isLastMonthOfYear(): boolean {
    return this.currentDate.getMonth() == 11;
  }

  get isFirstMonthOfYear(): boolean {
    return this.currentDate.getMonth() == 0;
  }

  fieldArr: any[] = [];

  can_edit_field(name: string) {
    const all_fields = this.fieldArr?.map((field: any) => {
      return field?.fieldName;
    });
    return all_fields.includes(name) ? this.fieldArr?.find((field: any) => field.fieldName === name && field?.editPermission === 2) : true;
  }

  canEdit() {}

  async ngOnInit(): Promise<void> {
    this.currentDate = new Date();
    await this.getCountryList();
    this.getcalendarSettings(this.currentDate.getFullYear());
    this.getOtherYearcalendarSetting();
  }

  // 每年最后一个月或者第一个月 获取其他年份的数据
  getOtherYearcalendarSetting() {
    if (this.isLastMonthOfYear) {
      this.getcalendarSettings(this.currentDate.getFullYear() + 1);
    }
    if (this.isLastMonthOfYear) {
      this.getcalendarSettings(this.currentDate.getFullYear() - 1);
    }
  }

  // 获取国家列表
  async getCountryList() {
    return new Promise((resolve) => {
      this.treeLoading = true;
      this._service
        .getCountryList()
        .pipe(
          finalize(() => {
            this.treeLoading = false;
            resolve(true);
          })
        )
        .subscribe((res: any) => {
          if (res.code == 200) {
            this.countryList = res.data?.list ?? [];
            if (!this.selectedCountryCode && this.countryList.length) {
              this.selectedCountryCode = this.countryList[0].country_code;
              this.selectedCountryName = this.countryList[0].country_name;
            }
          }
        });
    });
  }

  // 获取后端的日历设置
  getcalendarSettings(year: number) {
    if (!this.selectedCountryCode) return;
    this.changedDateList = [];
    this._service.getcalendarSettingList({ country_code: this.selectedCountryCode, year: year }).subscribe((res: any) => {
      if (res.code == 200) {
        const map: any = {};
        res.data?.list.forEach((element: any) => {
          const dateStr = element.date.substring(0, 7);
          const l = map[dateStr] ?? [];
          l.push(element);
          map[dateStr] = l;
        });
        this.calendarSettedDate = { ...this.calendarSettedDate, ...map };
      }
    });
  }

  // 回到今天
  goToday() {
    this.currentDate = new Date();
    this.getOtherYearcalendarSetting();
  }

  // 编辑
  editClick() {
    this.editable = true;
  }

  // 取消编辑
  cancel() {
    if (this.changedDateList.length) {
      const ref = this._flcModalService.confirmCancel({
        content: this._translate.instant(this.translateName + '取消编辑提示'),
        okText: this._translate.instant('flss.btn.ok'),
        cancelText: this._translate.instant('flss.btn.cancel'),
      });
      ref.afterClose.subscribe((res: any) => {
        if (res) {
          this.editable = false;
          this.changedDateList = [];
        }
      });
    } else {
      this.editable = false;
    }
  }

  // 提交
  submit() {
    // if (!this.changedDateList.length) {
    //   this._message.info(this._translate.instant(this.translateName + '暂无数据更改'));
    //   return;
    // }
    // 2024年12月30日11:52:30  每次提交需要给后端当月全部设置的日期
    const month = this.currentDate.getMonth();
    const timeStamp = startOfDay(Date.now()).getTime();
    const payloadDateList = [];
    let date = startOfMonth(this.currentDate);
    while (date.getMonth() === month) {
      const ts = date.getTime();
      if (ts >= timeStamp) {
        const dateStr = format(date, this.format);
        const changedDate = this.changedDateList.find((e: any) => e.date === dateStr);
        if (changedDate) {
          payloadDateList.push(changedDate);
        } else {
          payloadDateList.push({ date: dateStr, date_type: this.getDateType(date) });
        }
      }
      date = addDays(date, 1);
    }
    this._service.setcalendar({ country_code: this.selectedCountryCode, list: payloadDateList }).subscribe((res: any) => {
      if (res.code == 200) {
        this.getcalendarSettings(this.currentDate.getFullYear());
        this._message.success(this._translate.instant('flss.success.submit'));
        this.editable = false;
      }
    });
  }

  // 切换国家
  clickCountry(item: any) {
    if (this.selectedCountryCode === item.country_code) return;
    if (this.editable) {
      this._message.info(this._translate.instant(this.translateName + '编辑再切换'));
      return;
    }
    this.selectedCountryCode = item.country_code;
    this.selectedCountryName = item.country_name;
    // 清空数组
    this.calendarSettedDate = {};
    this.changedDateList = [];
    // 刷新数据
    this.getcalendarSettings(this.currentDate.getFullYear());
  }

  previousMonth() {
    this.currentDate = addMonths(this.currentDate, -1);
    this.getOtherYearcalendarSetting();
  }

  nextMonth() {
    this.currentDate = addMonths(this.currentDate, 1);
    this.getOtherYearcalendarSetting();
  }

  disabledDate = (date: Date): boolean => !this.validDate(date);

  validDate(date: Date): boolean {
    const curMonth = this.currentDate.getMonth() === date.getMonth();
    const after = startOfDay(new Date()).getTime() <= date.getTime();
    return curMonth && after;
  }

  onSelectedDateChanged(date: Date) {
    console.log(date);
  }

  onDateTypeChanged(value: any, date: Date) {
    const dateStr = format(date, this.format);
    const substring = dateStr.substring(0, 7);
    const submittedDate = this.calendarSettedDate[substring]?.find((e: any) => e.date === dateStr);
    if (submittedDate) {
      // 后端存在数据 需要传参
      this.changedDateList.push({ date: dateStr, date_type: value });
    } else {
      // 后端存在数据
      const defaultType = this.getDateDefaultType(date);
      const index = this.changedDateList.findIndex((e: any) => e.date === dateStr);
      if (index >= 0) {
        this.changedDateList.splice(index, 1);
      }
      // 且类型不是默认类型 需要传参
      if (defaultType != value) {
        this.changedDateList.push({ date: dateStr, date_type: value });
      }
    }
  }

  // calendar 头部 获取月份显示
  getCurrentMonth(): string {
    const year = this.currentDate.getFullYear();
    if (this._translate.currentLang == 'zh') {
      return year + '/' + (this.currentDate.getMonth() + 1);
    }
    const month = this._translate.instant(this.translateName + this.months[this.currentDate.getMonth()]);
    return month + '   ' + year;
  }

  // 是否跟选中日期是同一天
  isCurrentDate(date: Date) {
    return startOfDay(date).getTime() === startOfDay(this.currentDate).getTime();
  }

  // 计算 是工作日 还是 休息日
  getDateTypeString(date: Date): string {
    const type = this.getDateType(date);
    if (type == 2) return this._translate.instant(this.translateName + '休息日');
    if (type == 1) return this._translate.instant(this.translateName + '工作日');
    return '';
  }

  // 获取类型
  getDateType(date: Date): number {
    const substring = format(date, this.format).substring(0, 7);
    const settedDateList = this.calendarSettedDate[substring] ?? [];
    const settedDate = settedDateList.find((e: any) => e.date === format(date, this.format));
    if (settedDate) {
      return settedDate.date_type;
    }
    // 不可编辑时 没保存过的数据不显示
    if (!this.editable) return 0;
    if (!this.validDate(date)) return 0;
    return this.getDateDefaultType(date);
  }

  // 默认类型
  getDateDefaultType(date: Date): number {
    const day = date.getDay();
    if (day == 6 || day == 0) return 2;
    return 1;
  }
}
