import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { MenuItem } from 'fl-common-lib';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class MainService {
  constructor(private http: HttpClient) {}

  getUser(): Observable<any> {
    return this.http.get('/user/info/base');
  }

  getMenu(): Observable<any> {
    return this.http.get('/menu');
    /*return of({
      actions: [340001],
      resources: [
        {
          index: 80,
          name: '工厂管理',
          path: '/factory',
          value: true,
          children: [
            {
              index: 81,
              name: '工厂上线管理',
              path: '/factory/factoryManage',
              value: true,
              action: ['a', 'b', 'c'],
            },
          ],
        },
        {
          index: 90,
          name: '人员管理',
          path: '/hr',
          value: true,
          children: [
            {
              index: 91,
              name: '组织架构',
              path: '/hr/org',
              value: true,
              action: ['a', 'b', 'c'],
            },
            {
              index: 92,
              name: '员工档案',
              path: '/hr/employee',
              value: true,
              action: ['a', 'b', 'c'],
            },
          ],
        },
        {
          index: 100,
          name: '权限管理',
          path: '/settings',
          value: true,
          children: [
            {
              index: 101,
              name: '操作用户',
              path: '/settings/user',
              value: true,
              action: ['a', 'b', 'c'],
            },
            {
              index: 102,
              name: '权限名称',
              path: '/settings/role',
              value: true,
              action: ['a', 'b', 'c'],
            },
            {
              index: 102,
              name: '权限配置',
              path: '/settings/assignRole',
              value: true,
              action: ['a', 'b', 'c'],
            },
          ],
        },
      ],
    });*/
  }

  isDashboardLink(item: MenuItem) {
    //是否大屏菜单链接
    // 目前先写死standard-dashboard开头为大屏链接，后续需再菜单接口加字段区分
    return item.path.startsWith('/standard-dashboard');
  }

  getSampleNotice(payload: any): Observable<any> {
    return this.http.post('/service/procurement-inventory/sample/v1/msg_notifications', payload);
  }
}
