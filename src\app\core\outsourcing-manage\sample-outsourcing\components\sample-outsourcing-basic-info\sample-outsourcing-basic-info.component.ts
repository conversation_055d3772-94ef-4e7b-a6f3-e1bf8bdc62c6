import { Component, Input, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { format } from 'date-fns';
import { basicInfoConfig, sampleInfoConfig } from '../../modal/sample-outsourcing.config';
import { IDetailModel, IFormartDetailModel } from '../../modal/sample-outsourcing.interface';
import { SampleOursourcingBasicFormComponent } from '../sample-oursourcing-basic-form/sample-oursourcing-basic-form.component';

@Component({
  selector: 'app-sample-outsourcing-basic-info',
  templateUrl: './sample-outsourcing-basic-info.component.html',
  styleUrls: ['./sample-outsourcing-basic-info.component.scss'],
})
export class SampleOutsourcingBasicInfoComponent implements OnInit {
  @ViewChild(SampleOursourcingBasicFormComponent) basicFormCom!: SampleOursourcingBasicFormComponent;
  @Input() isEdit = false;
  @Input() detailData: IDetailModel | null = null;
  @Input() simpleData: Partial<IDetailModel> = {};
  translateName = 'sampleOutsourcing.detailField.';
  translateTableName = 'sampleOutsourcing.sampleOutsourcingTable.';
  basicInfoConfig = basicInfoConfig;
  sampleInfoConfig = sampleInfoConfig;

  staticData: Partial<IFormartDetailModel> = {};
  constructor() {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes && changes.detailData && changes.detailData.currentValue) {
      this.staticData = this._tranlateData(changes.detailData.currentValue);
    }

    if (changes && changes.simpleData && changes.simpleData.currentValue) {
      this.simpleData = this._tranlateData(changes.simpleData.currentValue);
    }
  }

  ngOnInit() {}

  private _tranlateData(data: Partial<IDetailModel>) {
    const _data = JSON.parse(JSON.stringify(data));
    if (_data.first_style_name) {
      _data.styleName = `${_data.first_style_name}/${_data.second_style_name}/${_data.third_style_name}`;
    }
    _data.monthName = _data.month ? _data.month + '月' : '';
    _data.outfitName = _data.outfit === 0 ? '否' : _data.outfit === 1 ? '是' : '';
    _data.expected_delivery_date && (_data.expected_delivery_dateline = format(new Date(_data.expected_delivery_date), 'yyyy-MM-dd'));
    const _area = `${_data.country ?? ''}${_data.province ?? ''}${_data.city ?? ''}${_data.district ?? ''}`;
    const _ardress = _data.detailed_address || '';
    _data.area = _area + _ardress;

    const _quality_level_name = ['', '高（精品货)', '中（一般品牌货）', '低（市场货）'][_data.quality_level];
    _data.quality_level_name = _quality_level_name;

    return _data;
  }
}
