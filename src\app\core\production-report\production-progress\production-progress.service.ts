import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { EventEmitter, Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { IDetailParams, IListParams } from './models/production-progress-interface';

@Injectable()
export class ProductionProgressService {
  public serviceUrl = '/service/order/v1/bi-elan';
  public translateEventEmitter = new EventEmitter<void>();

  public get searchOptionUrl(): string {
    return this.serviceUrl + '/production-progress/options';
  }

  constructor(private _http: HttpClient, private _translate: TranslateService) {}

  // 列表查询
  getProgressList(model: IListParams): Observable<any> {
    return this._http.post<any>(`${this.serviceUrl}/production-progress`, model);
  }

  // 生产进度报表详情
  getProgressDetail(model: IDetailParams): Observable<any> {
    return this._http.post<any>(`${this.serviceUrl}/production-progress-detail`, model);
  }

  translateValue(key: string): string {
    return this._translate.instant(key);
  }
}
