import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { OrderGarmentOutsourcingListComponent } from './order-garment-outsourcing-list/order-garment-outsourcing-list.component';
import { OrderGarmentOutsourcingDetailComponent } from './order-garment-outsourcing-detail/order-garment-outsourcing-detail.component';
import { FlcLeaveGuard } from 'fl-common-lib';
const routes: Routes = [
  {
    path: 'list',
    component: OrderGarmentOutsourcingListComponent,
    data: {
      keepAlive: true,
    },
  },
  {
    // 订单详情
    path: 'list/:id',
    component: OrderGarmentOutsourcingDetailComponent,
    canDeactivate: [FlcLeaveGuard],
  },
  { path: '', redirectTo: 'list' },
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class OrderGarmentOutsourcingRoutingModule {}
