import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'component-table-body-render-date',
  template: `
    <ng-container *ngIf="type === 'date'">{{ data ? (data | date: 'yyyy/MM/dd') : '-' }}</ng-container>
    <ng-container *ngIf="type === 'datetime'">{{ data ? (data | date: 'yyyy/MM/dd HH:mm:ss') : '-' }}</ng-container>
  `,
  styles: [],
})
export class TableBodyRenderDateComponent implements OnInit {
  @Input('data') inputData?: any;
  @Input() type?: 'date' | 'datetime';
  data?: string | number;
  constructor() {}

  ngOnInit(): void {
    if (this.inputData && typeof this.inputData === 'string') {
      this.data = this.inputData;
    }
  }
}
