/*
 * File: input copy.scss
 * Project: elan-web
 * File Created: Friday, 26th November 2021 4:05:12 pm
 * Author: liucp
 * Description: 全局样式
 * -----
 * Last Modified: Wednesday, 29th December 2021 2:41:46 pm
 * Modified By: liucp
 */

ul,
li {
  padding: 0;
  margin: 0;
  list-style: none;
}
.affix-top {
  .affix-right {
    button {
      margin-left: 8px;
    }
  }
}

.nz-resizable-preview {
  border: 1px dashed $fl-primary-blue;
}

.nz-table-hide-scrollbar {
  scrollbar-color: inherit;
}

.ant-cascader-menu-item {
  span,
  div {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 400px;
  }
}
