<flc-title-bar style="flex-shrink: 0" [title]="title" [action]="action" (reset)="onReset()" (expandChange)="onExpandChange()">
  <ng-template #title>
    <div class="left-title-bar">
      <div class="title">生产计划</div>
      <div
        class="example-btn"
        nz-popover
        [nzPopoverPlacement]="'topRight'"
        [nzPopoverTitle]="undefined"
        [nzPopoverContent]="expContentTemplate">
        预警示例
      </div>
      <app-graph-resizer (zoomStepChange)="onZoomStepChange($event)"></app-graph-resizer>
    </div>
  </ng-template>

  <ng-template #action>
    <ng-content></ng-content>
  </ng-template>
</flc-title-bar>

<ng-template #expContentTemplate>
  <div class="example-content">
    <div class="example-line">
      <div>
        <i nz-icon class="sample-circle">
          <svg>
            <path
              d="M32 32m360 0l240 0q360 0 360 360l0 240q0 360-360 360l-240 0q-360 0-360-360l0-240q0-360 360-360Z"
              fill="#FFFFFF"
              p-id="8186"></path>
            <path
              d="M92 92m300 0l240 0q300 0 300 300l0 240q0 300-300 300l-240 0q-300 0-300-300l0-240q0-300 300-300Z"
              fill="#FF4141"
              p-id="8187"></path>
            <path
              d="M563.6 262.28l-266.16 276.6c-7.44 7.98-9.3 15.6-5.58 22.74 5.58 10.74 17.58 12.72 24.66 12.72h125.58l-68.94 174.72c-2.76 9.24-1.14 16.32 4.92 21.24 9 7.32 25.02 13.86 38.16 2.46 8.76-7.56 114.84-106.02 318.18-295.38 6.48-10.26 7.32-18.72 2.4-25.26-4.8-6.6-14.4-9.48-28.56-8.76L576.92 442.4l36-156c0.84-15.6-3.9-25.62-14.4-30.12-10.44-4.44-22.08-2.4-34.86 6z"
              fill="#FFFFFF"
              p-id="8188"></path>
          </svg>
        </i>
        已逾期
      </div>

      <nz-switch class="example-switch" nzSize="small" [(ngModel)]="_planShareService.showTag.overdue"></nz-switch>
    </div>
    <div class="example-line">
      <div>
        <i nz-icon class="sample-circle">
          <svg>
            <path
              d="M32 32m360 0l240 0q360 0 360 360l0 240q0 360-360 360l-240 0q-360 0-360-360l0-240q0-360 360-360Z"
              fill="#FFFFFF"
              p-id="8318"></path>
            <path
              d="M92 92m300 0l240 0q300 0 300 300l0 240q0 300-300 300l-240 0q-300 0-300-300l0-240q0-300 300-300Z"
              fill="#FF8014"
              p-id="8319"></path>
            <path d="M272 512m-60 0a60 60 0 1 0 120 0 60 60 0 1 0-120 0Z" fill="#FFFFFF" p-id="8320"></path>
            <path d="M512 512m-60 0a60 60 0 1 0 120 0 60 60 0 1 0-120 0Z" fill="#FFFFFF" p-id="8321"></path>
            <path d="M752 512m-60 0a60 60 0 1 0 120 0 60 60 0 1 0-120 0Z" fill="#FFFFFF" p-id="8322"></path>
          </svg>
        </i>
        进度落后
      </div>

      <nz-switch class="example-switch" nzSize="small" [(ngModel)]="_planShareService.showTag.behind_schedule"></nz-switch>
    </div>
    <div class="example-line">
      <div>
        <i nz-icon class="sample-circle">
          <svg>
            <path
              d="M32 32m360 0l240 0q360 0 360 360l0 240q0 360-360 360l-240 0q-360 0-360-360l0-240q0-360 360-360Z"
              fill="#FFFFFF"
              p-id="8452"></path>
            <path
              d="M92 92m300 0l240 0q300 0 300 300l0 240q0 300-300 300l-240 0q-300 0-300-300l0-240q0-300 300-300Z"
              fill="#7975F6"
              p-id="8453"></path>
            <path d="M332 392m-60 0a60 60 0 1 0 120 0 60 60 0 1 0-120 0Z" fill="#FFFFFF" p-id="8454"></path>
            <path d="M692 392m-60 0a60 60 0 1 0 120 0 60 60 0 1 0-120 0Z" fill="#FFFFFF" p-id="8455"></path>
            <path
              d="M512 542c102.36 0 162.36 60 180 180h-60c-11.76-79.98-51.78-120-120-120s-108.24 40.02-120 120H332c17.64-120 77.64-180 180-180z"
              fill="#FFFFFF"
              p-id="8456"></path>
          </svg>
        </i>
        计划超客期
      </div>

      <nz-switch class="example-switch" nzSize="small" [(ngModel)]="_planShareService.showTag.over_deadline"></nz-switch>
    </div>
  </div>
</ng-template>
