.wrapper {
  display: flex;
  flex-direction: column;
  height: calc(100% - 48px);
}

.sort-list {
  flex: 1;
  overflow: auto;
  margin: 8px 0;
  border: 1px solid #eee;
  border-radius: 8px;
}

.notice-text {
  font-size: 16px;
  color: #54607c;
  background: #f0f2f5;
  padding: 8px 8px;
  margin: 8px -12px 0;
}

.sort-box {
  height: 100%;
  height: 100%;
  overflow: auto;
}

.list-item {
  display: flex;
  align-items: center;
  padding: 8px;
  color: #223c4d;
}

.list-item-hover {
  transition: background-color 0.2s;
  &:hover {
    background-color: #e7f3fe;
    .handler {
      color: #138aff;
    }
  }
}

.sortable-ghost {
  display: flex;
  align-items: center;
  color: #223c4d;
  background-color: #e7f3fe;
  padding: 8px;
  border: 1px solid #007aff;
  opacity: 0.6;
}

.cdk-drag-preview {
  background-color: #e7f3fe;
  border: 1px solid #007aff;
  box-shadow: 0 2px 6px #d4d7dc;
}

.handler {
  color: #97999c;
  cursor: move;
  margin-right: 20px;
}

.arrow {
  color: #54607c;
  width: 24px;
  cursor: pointer;
}

.disabled-arrow {
  color: #b5b8bf;
  width: 24px;
  cursor: not-allowed;
}

.label {
  flex: 1;
  display: flex;
}

.dragging {
  color: #007aff;
  .arrow {
    color: #007aff;
  }
}

.hiden {
  height: 0;
  overflow: hidden;
}

.rotate-arrow {
  transform: rotate(-90deg);
}

.no-data {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.footer {
  display: flex;
  justify-content: flex-end;
  padding: 8px;
  height: 48px;
  gap: 12px;
}
