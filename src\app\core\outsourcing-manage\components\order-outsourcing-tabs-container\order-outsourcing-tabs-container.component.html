<div class="order-tabs-container">
  <div class="order-tabs-header">
    <nz-tabset
      #tabsRef
      style="width: 100%"
      [(nzSelectedIndex)]="selectedIndex"
      nzType="editable-card"
      nzHideAdd
      nzForceRender
      [nzTabBarExtraContent]="extraTemplate"
      (nzClose)="closeTab($event)"
      (nzSelectedIndexChange)="onSelectedIndexChange($event)">
      <nz-tab
        *ngFor="let tab of tabs; let i = index"
        [nzClosable]="modelType && tab.po_basic?.deletable && !is_use_plan"
        [nzTitle]="tabTitle">
        <div class="order-tabs-body">
          <div *ngIf="tab?.po_basic?.spec_group_name" class="order-tabs-body-banner">
            <div class="order-tabs-body-banner-item">
              <span>{{ 'outsourcingComponents.尺码组' | translate }}：</span>
              <span>{{ tab?.po_basic?.spec_group_name }}</span>
            </div>
            <div class="order-tabs-body-banner-item">
              <nz-form-label [nzRequired]="factory_type === 1 && modelType">{{
                'outsourcingComponents.交付日期' | translate
              }}</nz-form-label>
              <!-- 成衣外发可编辑交付日期且必填 -->
              <ng-container>
                <nz-date-picker
                  *ngIf="factory_type === 1 && modelType; else dueTimeReadtpl"
                  [ngModel]="parentGroup?.get('factory_po_due_times')?.value?.[tab?.po_basic?.po_unique_code] || tab?.po_basic?.due_time"
                  (ngModelChange)="dueTimeChange($event, i, tab?.po_basic?.po_unique_code)"
                  [nzFormat]="'yyyy/MM/dd'"></nz-date-picker>
                <ng-template #dueTimeReadtpl>
                  <span>{{
                    (parentGroup?.get('factory_po_due_times')?.value?.[tab?.po_basic?.po_unique_code] ||
                    tab?.po_basic?.due_time) | date: 'yyyy/MM/dd'
                  }}</span>
                </ng-template>
              </ng-container>
            </div>
            <div *ngIf="showLossCount" class="order-tabs-body-banner-item">
              <span style="flex-shrink: 0">{{ 'outsourcingComponents.损耗件数' | translate }}：</span>
              <ng-container *ngIf="modelType; else onlyContact" class="inline-count">
                <input
                  [type]="'text'"
                  nz-input
                  [placeholder]="'请输入'"
                  style="width: 250px"
                  [ngModel]="parentGroup?.get('loss_counts_v2')?.value?.[tab?.po_basic?.po_unique_code]"
                  (input)="lossCountChange($event, i, tab?.po_basic?.po_unique_code)"
                  [maxLength]="50"
                  inputTrim />
              </ng-container>
              <ng-template #onlyContact>
                <div class="fontW500">
                  <flc-text-truncated [data]="parentGroup?.get('loss_counts_v2')?.value?.[tab?.po_basic?.po_unique_code]">
                  </flc-text-truncated></div
              ></ng-template>
            </div>
          </div>
          <div class="order-tabs-body-table" ngProjectAs="tpl">
            <ng-template *ngTemplateOutlet="tabsBodyTpl; context: { data: tab }"></ng-template>
            <div class="personal-info" *ngIf="showPersonalInfo">
              <div>
                <span>{{ 'outsourcingComponents.收货人' | translate }}：</span>
                <flc-text-truncated [data]="tab?.po_basic?.receiver"></flc-text-truncated>
              </div>
              <div>
                <span>{{ 'outsourcingComponents.联系方式' | translate }}：</span>
                <flc-text-truncated [data]="tab?.po_basic?.contact"></flc-text-truncated>
              </div>
              <div>
                <span>{{ 'outsourcingComponents.收货地址' | translate }}：</span>
                <flc-text-truncated
                  [data]="
                    tab?.po_basic?.country_name +
                    tab?.po_basic?.province_name +
                    tab?.po_basic?.city_name +
                    tab?.po_basic?.district_name +
                    tab?.po_basic?.address
                  "></flc-text-truncated>
              </div>
            </div>
          </div>
        </div>
        <ng-template #tabTitle>
          <div
            class="red-dot"
            *ngIf="orderStatusEnum.toModifyAudit === orderStatus && !modelType && tab?.po_basic?.have_changed_line && !is_use_plan"></div>
          <div class="order-tabs-header-item">
            <ng-container *ngIf="tab?.po_basic?.po_code; else selectPlant">
              <span>{{ tab?.po_basic?.po_code }}</span>
              <span>{{ tab?.po_basic?.due_time | date: 'yyyy/MM/dd' }}</span>
            </ng-container>
            <ng-template #selectPlant>
              <nz-select
                class="select-po"
                nzShowSearch
                [nzAllowClear]="false"
                [nzPlaceHolder]="'placeholder.select' | translate"
                [ngModel]="selectedPo"
                (ngModelChange)="onSelectedPo($event, i)">
                <nz-option
                  *ngFor="let tab of selectableList || tabs"
                  [nzLabel]="tab?.po_basic?.po_code"
                  [nzValue]="tab?.po_basic?.po_code"></nz-option>
              </nz-select>
            </ng-template>
          </div>
        </ng-template>
      </nz-tab>
    </nz-tabset>

    <ng-template #extraTemplate>
      <div class="order-tabs-header-roll">
        <nz-select
          nzShowSearch
          nzAllowClear
          [nzPlaceHolder]="'outsourcingComponents.请选择交付单' | translate"
          [(ngModel)]="poCode"
          (ngModelChange)="onSelectChange($event)">
          <nz-option *ngFor="let tab of tabs" [nzLabel]="tab?.po_basic?.po_code" [nzValue]="tab?.po_basic?.po_code"></nz-option>
        </nz-select>
      </div>
    </ng-template>
  </div>
</div>
