<div class="operate-bts">
  <button
    *ngIf="!isEdit && actionMap.hasEditPlan"
    nz-button
    flButton="pretty-primary"
    [nzShape]="'round'"
    [flcDisableOnClick]="1000"
    (click)="handleButtonAction(planOperateBtnEnum.edit)">
    <i nz-icon [nzIconfont]="'icon-caozuolan_bianji1'"></i>{{ 'btn.edit' | translate }}
  </button>

  <ng-container *ngIf="isEdit">
    <div class="eidt-btn">
      <button
        nz-button
        [nzShape]="'round'"
        flButton="pretty-default"
        (click)="handleButtonAction(planOperateBtnEnum.exitEdit)"
        [flcDisableOnClick]="1000">
        退出编辑
      </button>
      <div class="tip-content" *ngIf="isHold && !isCloseTooltip">
        <div class="div">
          <div style="position: absolute; right: 4px; top: 6px">
            <i nz-icon nzIconfont="icon-cuohao" style="cursor: pointer" (click)="closeTooltip()"></i>
          </div>
          <div style="font-weight: 500; color: #ffffff">退出后他人才可编辑哟</div>
        </div>
        <div class="ant-tooltip-arrow">
          <span class="ant-tooltip-arrow-content"></span>
        </div>
      </div>
    </div>

    <button
      nz-button
      [nzShape]="'round'"
      flButton="pretty-default"
      (click)="handleButtonAction(planOperateBtnEnum.revoke)"
      [disabled]="shareService.undo_disabled"
      [flcDisableOnClick]="1000">
      <i nz-icon>
        <svg>
          <path
            d="M622.650611 284.901749 447.745069 284.901749 447.745069 142.823869 63.980685 334.705038l383.76336 191.882192L447.744046 384.834762l189.391465 0c149.914358 0 224.855164 62.789045 224.855164 188.368158 0 129.928165-77.435627 194.876386-232.338602 194.876386L187.952184 768.079306l0 99.93199L634.146433 868.011296c211.184817 0 316.777737-95.104031 316.777737-285.311071C950.924169 384.178823 841.510224 284.901749 622.650611 284.901749z"></path>
        </svg>
      </i>
      撤销
    </button>

    <button
      nz-button
      flButton="pretty-primary"
      [nzShape]="'round'"
      [flcDisableOnClick]="1000"
      flcErrorScroll
      (click)="handleButtonAction(planOperateBtnEnum.publish)">
      发布
    </button>
  </ng-container>

  <nz-divider nzType="vertical" style="margin: auto 0; background: #d4d7dc"></nz-divider>
  <button
    *ngIf="actionMap.hasAllocation"
    nz-button
    flButton="pretty-minor"
    [nzShape]="'round'"
    flcErrorScroll
    (click)="handleButtonAction(planOperateBtnEnum.allocateOrder)">
    待分配订单·{{ allocateNum }}
  </button>
</div>
