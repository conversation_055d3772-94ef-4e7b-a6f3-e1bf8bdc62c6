import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { FlcSpKeyConstant, FlcSpUtilService } from 'fl-common-lib';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class AppStorageService {
  userActions?: Map<string, []>;
  visualType?: Map<string, number>;
  userFields?: Map<any, []>;
  userModules?: Map<any, []>;
  private ResourceCodeSubject: BehaviorSubject<string> = new BehaviorSubject<string>('');
  private readonly _SessionOpenMenuKey = 'Dashboard_OpenMenu';
  get ResourceCode(): string {
    return this.ResourceCodeSubject.value;
  }

  constructor(private http: HttpClient, private _spUtil: FlcSpUtilService) {}
  updateResourceCode(code: string) {
    this.ResourceCodeSubject.next(code ?? '');
  }
  getAccessToken() {
    return localStorage.getItem('accessToken');
  }

  getRefreshToken() {
    return localStorage.getItem('refreshToken');
  }

  getUser() {
    const user = localStorage.getItem('userInfo');
    if (user) {
      return JSON.parse(user);
    }
    return {};
  }

  saveAccessToken(token: string) {
    localStorage.setItem('accessToken', token);
  }

  saveRefreshToken(token: string) {
    localStorage.setItem('refreshToken', token);
  }

  saveUser(userInfo: any) {
    localStorage.setItem('userInfo', JSON.stringify(userInfo));
    localStorage.setItem('factory_code', userInfo.factory_code);
  }

  saveUserRoleInfo(userInfo: any) {
    localStorage.setItem('userRoleInfo', JSON.stringify(userInfo));
  }

  saveUserActions(userActions: any, visualType: any, userFields: any, userModules: any) {
    this.userActions = userActions;
    this._spUtil.putObject(FlcSpKeyConstant.UserActions, userActions);
    if (visualType) {
      this.visualType = visualType;
      // this._spUtil.putObject(FlcSpKeyConstant.VisualType, visualType);
    }
    this.userFields = userFields;
    this._spUtil.putObject(FlcSpKeyConstant.UserFields, userFields);

    this.userModules = userModules;
    this._spUtil.putObject(FlcSpKeyConstant.UserModules, userModules);
  }

  getUserActions(pageName: string) {
    return this.userActions?.get(pageName) || [];
  }
  getUserVisualType(pageName: string) {
    return this.visualType?.get(pageName) || 1;
  }
  getFieldActions(pageName: string) {
    return this.userFields?.get(pageName) || [];
  }
  signout() {
    sessionStorage.removeItem(this._SessionOpenMenuKey);
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('userInfo');
    this.userActions?.clear();
    this.visualType?.clear();
    this.userFields?.clear();
    this.userModules?.clear();
    this._spUtil.clear();
  }
}
