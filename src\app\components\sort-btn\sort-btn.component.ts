import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-sort-btn',
  templateUrl: './sort-btn.component.html',
  styleUrls: ['./sort-btn.component.scss'],
})
export class SortBtnComponent implements OnInit {
  @Input() sortOrder: 'desc' | 'asc' | null = null;
  @Output() sortOrderChange = new EventEmitter<'desc' | 'asc' | null>();

  constructor() {}

  ngOnInit(): void {}

  sort() {
    if (!this.sortOrder) {
      this.sortOrder = 'asc';
      this.sortOrderChange.emit('asc');
    } else if (this.sortOrder === 'asc') {
      this.sortOrder = 'desc';
      this.sortOrderChange.emit('desc');
    } else {
      this.sortOrder = null;
      this.sortOrderChange.emit(null);
    }
  }
}
