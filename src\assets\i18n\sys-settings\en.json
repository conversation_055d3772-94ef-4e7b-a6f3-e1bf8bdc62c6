{"sysSetting.sysParameters.common": {"pageTitle": "System Parameter Setting", "optYes": "yes", "optNo": "no", "requiredPromptMsg": "Please verify the required fields"}, "sysSetting.sysParameters.options": {"自购": "Self-Purchase", "客供": "Customer-Provided", "厂供": "Factory-Provided", "打样审核通过后": "After Sample Approval", "样衣评审通过后": "After Sample Garment Evaluation", "一个款式只自动生成一次": "One style is automatically generated only once", "每个打样单均自动生成": "Each sample order is automatically generated", "根据样板类型生成": "Generated based on sample type", "物料采购计划->物料采购需求->采购单": "Material Purchase Plan -> Material Purchase Request -> Purchase Order", "物料采购计划->采购单": "Material Purchase Plan -> Purchase Order", "物料采购需求->采购单": "Material Purchase Request -> Purchase Order", "物料入库后检验": "Inspection after Material Warehousing", "物料入库前检验": "Inspection before Material Warehousing", "采购单": "Purchase Order", "物料": "Material", "订单": "Order", "面料类型": "Fabric Type", "部位": "Part", "物料颜色": "Material Color", "样衣评审通过": "Sample Garment Evaluation Approved", "样衣制作完成": "Sample Garment Production Completed", "流程结束": "Process Ended", "允许换款": "允许换款", "锁定款式(不可修改)": "锁定款式(不可修改)", "完成件数为0时，支持取消打样单": "完成件数为0时，支持取消打样单"}, "sysSetting.sysParameters.sectionTitle": {"dataPackage": "Data Package", "checkPrice": "Price Verification", "materialPurchase": "Material Purchase", "materialInspection": "Material Quality Inspection", "materialInventory": "Material Inventory", "sampleTask": "Sample Request", "designDraft": "Design Draft", "styleCenter": "Style Library", "merchandiseValuation": "Merchandise Pricing", "order": "Order", "sampleAdjustment": "Sample Request", "materialProcurement": "Material Purchase Order", "elanDashboard": "Supply chain collaborative data big screen"}, "sysSetting.sysParameters.sectionSubTitle": {"快捷创建": "Quick Creation", "款式档案生成打样单规则": "款式档案生成打样单规则", "打样单取消规则": "打样单取消规则", "面料必填字段": "Required Fields for <PERSON><PERSON><PERSON>", "辅料必填字段": "Required Fields for Auxiliary Materials", "datePackageTitle": "BOM - De<PERSON>ult Display of Purchase Type", "checkPriceTitle1": "Automatic Generation Time for Sample Price Verification", "checkPriceTitle2": "Rules for Automatic Generation of Sample Price Verification", "materialPurchaseTitle": "Material Purchase Process", "materialRulesByLayoutTitle": "Enable Purchase Based on Layout", "materialInspectionTitle": "Material Inspection Process", "title1": "Warning Time for Material Warehousing", "title2": "Whether to Maintain <PERSON><PERSON><PERSON>/Roll Numbers for Fabric Inventory", "title3": "Potential Overtime", "title4": "Planned Completion Time", "merchandiseValuation": "Whether the tag price in the Style Library, Product File, and Certificate of Conformity is taken from the Merchandise Pricing and cannot be modified", "title5": "Mandatory Assignment of Outsourced Processing Factory for Order Outsourcing", "title6": "Mandatory Assignment of Outsourced Processing Factory for Production Plan", "交期维度": "Delivery Time Dimension", "交期是否必填": "Is Delivery Time Required", "交付数量是否必填": "Is Delivery Quantity Required", "打样自动入库节点": "Sample automatic storage node", "elanDashboardTitle": "Configure the factory name displayed on the large screen", "是否启用调样单": "Enable Sample Request", "1. 采购类型: 正单": "1. Purchase Type: Regular Order", "2. 采购类型: 备料&采购": "2. Purchase Type: Preparation & Purchase"}, "sysSetting.sysParameters.input": {"label1": "Before Expected Warehousing Date", "suffix1": "days", "label2": "Earlier than Planned Completion Time", "suffix2": "hours", "elanDashboardLabel": "Factory name"}, "sysSetting.sysParameters.radio": {"启用": "enable", "禁用": "disable", "item1": "yes", "item2": "no", "item3": "Based on Base Time", "item4": "Based on Target Time", "是": "yes", "否": "no"}}