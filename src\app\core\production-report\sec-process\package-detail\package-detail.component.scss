.search-head {
  display: flex;
  align-items: center;

  > .selects {
    display: flex;
    flex-wrap: wrap;

    > div {
      margin-right: 8px;

      > span {
        margin-right: 2px;
      }
    }
  }

  h2 {
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #515661;
    margin: 0;
  }

  > .btns {
    margin-left: auto;

    button {
      border-radius: 8px;
      margin-right: 0;

      &:first-of-type {
        margin-right: 8px;
      }

      &.back {
        border-radius: 16px;
        width: 80px;
      }
    }
  }

  nz-select {
    width: 100px;
  }
}

.content {
  background: white;
  border-radius: 4px 4px 0 0;

  .filt {
    display: flex;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid #d4d7dc;

    nz-radio-group {
      display: flex;
      flex-wrap: nowrap;

      label {
        border-radius: 16px;
        width: 88px;

        &:last-of-type {
          margin-left: 6px;
          border-left-width: 1px;

          &::before {
            content: none;
          }
        }
      }
    }

    nz-tabset {
      ::ng-deep {
        .ant-tabs-nav::before {
          display: none;
        }

        .ant-tabs-ink-bar {
          display: none;
        }

        .ant-tabs-tab {
          border: none;
          background: #f7fbff;
          font-size: 14px;
          font-weight: 500;
          color: #54607c;
          line-height: 20px;
          height: 24px;
          border-radius: 12px;
          padding: 2px 10px;

          &-active {
            background: #e7f3fe;
            color: #138aff;
          }
        }

        .ant-tabs-nav .ant-tabs-nav-list {
          align-items: center;
        }
      }
    }
  }

  nz-spin {
    min-height: 400px;
  }

  nz-empty {
    height: 400px;
    display: flex;
    justify-content: center;
    flex-direction: column;
  }

  .content-detail {
    padding: 0 12px;

    > div {
      > .head {
        background-color: #f7fbff;
        height: 28px;
        border-left: 2px solid #4d96ff;
        display: flex;
        align-items: center;
        margin: 8px 0;
        padding-left: 10px;

        label {
          color: #54607c;
          font-weight: 500;
          line-height: 20px;
          font-size: 14px;
          margin-right: 32px;

          span {
            margin-left: 6px;
          }

          &:last-of-type {
            margin-left: auto;
            margin-right: 16px;

            span {
              color: #138aff;
            }
          }
        }
      }

      > .body {
        display: flex;
        padding-bottom: 10px;

        .col {
          max-width: 204px;

          &:not(:last-of-type) {
            margin-right: 12px;
          }

          &-info {
            background: #f7fbff;
            display: flex;
            padding: 2px 8px;

            b {
              font-size: 16px;
              font-weight: 500;
              color: #222b3c;
              line-height: 22px;
            }

            span {
              margin-left: auto;
            }
          }

          &-row {
            box-shadow: 0px 0px 4px 0px #dce6f0;
            background: white;
            overflow: hidden;
            border-radius: 4px;

            &:not(:last-of-type) {
              margin-bottom: 12px;
            }

            .danger {
              color: #f96d6d;
            }

            .card {
              border: 1px solid #7fe3c7;
              border-radius: inherit;
              padding: 4px 4px 8px 6px;

              &-status {
                &-1 {
                  border-color: #d4d7dc;
                }

                &-2 {
                  border-color: #fdb180;
                }

                &-3 {
                  border-color: #7fbcff;
                }
              }
            }

            .pkg-head {
              align-items: center;
              display: flex;
              justify-content: space-between;

              flc-text-truncated {
                font-size: 18px;
                font-weight: 500;
                color: #222b3c;
                line-height: 25px;
              }

              span {
                border-radius: 4px;
                font-size: 12px;
                font-weight: 500;
                line-height: 16px;
                padding: 2px 5px;
                margin-left: 2px;
              }

              .status {
                &-1 {
                  background: #eaecef;
                  color: #54607c;
                }

                &-2 {
                  background: #feefe5;
                  color: #fc8334;
                }

                &-3 {
                  background: #deefff;
                  color: #138aff;
                }

                &-4 {
                  background: #e5f9f3;
                  color: #00af7f;
                }
              }
            }

            .pkg-detail {
              display: flex;
              justify-content: space-between;
              flex-wrap: wrap;
              font-size: 12px;
              font-weight: 500;
              color: #54607c;
              line-height: 17px;
              margin-top: 4px;

              div {
                display: flex;
                flex-direction: column;
                text-align: right;

                .defective {
                  margin-top: 2px;
                }
              }
            }

            .pkg-row {
              display: flex;
              align-items: center;
              height: 32px;
              padding: 0 6px 0 8px;
              font-size: 12px;
              font-weight: 500;
              color: #54607c;
              line-height: 16px;

              &:not(:last-of-type) {
                border-bottom: 1px dashed #eeeeee;
              }

              span:first-of-type {
                margin-right: auto;
              }

              nz-divider {
                margin: 0 4px;
                height: 8px;
                background: #d4d7dc;
              }

              b {
                margin-right: 2px;
                font-size: 16px;
                font-weight: 500;
                color: #222b3c;
                line-height: 22px;
              }
            }
          }
        }
      }
    }
  }
}

.position-tooltip {
  display: flex;
  flex-direction: column;
  align-items: stretch;

  tr {
    td {
      &:first-of-type {
        text-align: right;
      }
    }
  }
}

:host ::ng-deep {
  .ant-tabs-top > .ant-tabs-nav {
    margin: 0;
  }
}
