<nz-modal [(nzVisible)]="isVisible" nzWidth="800px" [nzTitle]="printTitle" (nzOnCancel)="onCancel()">
  <ng-container *nzModalContent>
    <div class="print-item">
      <span><span style="color: red">*</span>{{ translateName + '选择扎号' | translate }}：</span>
      <nz-radio-group [(ngModel)]="printValue">
        <label nz-radio [nzValue]="1">{{ translateName + '按尺码打印' | translate }}</label>
      </nz-radio-group>
    </div>
    <div class="print-item" *ngFor="let item of bulk_order_card_list">
      <span>{{ translateName + '大货单号' | translate }}：</span>
      <div style="flex: 1">
        <span>{{ item.order_code }}</span>
        <div *ngIf="printType === 1">
          <div class="print-item-size">
            <span><span style="color: red">*</span>{{ translateName + '尺码选择' | translate }} ：</span>
            <nz-checkbox-wrapper (nzOnChange)="printSizeChange($event, item, item?.line_code_list[0].size)" style="flex: 1">
              <div nz-row>
                <ng-container *ngFor="let sizeItem of item?.line_code_list">
                  <div nz-col nzSpan="3" *ngIf="sizeItem?.line_card_list?.length">
                    <label nz-checkbox [nzValue]="sizeItem?.line_card_list">{{ sizeItem?.spec_size }}</label>
                  </div>
                </ng-container>
              </div>
            </nz-checkbox-wrapper>
          </div>
        </div>
        <div *ngIf="printType === 2">
          <div class="print-item-size-bu">
            <div style="display: flex">
              <span><span style="color: red">*</span>{{ translateName + '尺码选择' | translate }} ：</span>
              <nz-checkbox-wrapper (nzOnChange)="printSizeChange($event, item, item?.line_code_list[0].size)" style="flex: 1">
                <div nz-row>
                  <ng-container *ngFor="let sizeItem of item?.line_code_list">
                    <div nz-col nzSpan="3" *ngIf="sizeItem?.line_card_list?.length">
                      <label nz-checkbox [nzValue]="sizeItem?.line_card_list">{{ sizeItem?.spec_size }}</label>
                    </div>
                  </ng-container>
                </div>
              </nz-checkbox-wrapper>
            </div>
            <nz-divider nzDashed style="margin: 8px 0px"> </nz-divider>
            <ng-container *ngFor="let sizeItem of item?.line_code_list">
              <div
                class="print-item-size-box"
                *ngIf="sizeItem?.line_card_list?.length && item.printOptionsOne.includes(sizeItem.line_card_list)"
                style="display: flex; align-items: center">
                <span
                  ><span style="color: red">*</span>{{ translateName + '尺码' | translate }}{{ sizeItem?.spec_size }}
                  {{ translateName + '打印区间' | translate }} ：</span
                >
                <div style="display: flex; align-items: center">
                  <nz-input-number-group>
                    <nz-input-number
                      [nzPrecision]="5"
                      style="width: 64px"
                      [nzMin]="0"
                      [nzMax]="999999999.99999"
                      (ngModelChange)="getPrintCard()"
                      [nzPlaceHolder]="'placeholder.input' | translate"
                      [(ngModel)]="sizeItem.printMinQty"></nz-input-number>
                  </nz-input-number-group>
                  <span style="margin: 0 6px; color: #97999c">-</span>
                  <nz-input-number-group>
                    <nz-input-number
                      [nzPrecision]="5"
                      style="width: 64px"
                      [nzMin]="0"
                      [nzMax]="999999999.99999"
                      (ngModelChange)="getPrintCard()"
                      [nzPlaceHolder]="'placeholder.input' | translate"
                      [(ngModel)]="sizeItem.printMaxQty"></nz-input-number>
                  </nz-input-number-group>

                  <span
                    style="
                      display: inline-block;
                      margin-left: 4px;
                      border-radius: 10px;
                      padding: 3px 6px;
                      background-color: #e8eef4;
                      font-size: 12px;
                      color: #54607c;
                    "
                    >{{ translateName + '数量' | translate }}：{{ sizeItem.printQtyList?.length }}</span
                  >
                </div>
              </div>
            </ng-container>
          </div>
        </div>
      </div>
    </div>
  </ng-container>
  <div *nzModalFooter>
    <div class="print-btns">
      <div>
        <span style="white-space: nowrap"><span style="color: red">*</span>{{ translateName + '标签数量' | translate }}：</span>
        <nz-input-group style="width: 130px" [nzAddOnAfter]="translateName + '张' | translate">
          <input
            type="text"
            nz-input
            [(ngModel)]="printTotalQty"
            [nzDisabled]="true"
            [placeholder]="translateName + '自动生成' | translate" />
        </nz-input-group>
        <ng-template #printSuffix>
          <span style="background-color: #d4d7dc; display: inline-block; width: 32px">{{ translateName + '张' | translate }}</span>
        </ng-template>
      </div>
      <div>
        <div style="padding: 2px 8px; border-radius: 11px; color: #54607c; background-color: #feefe5; margin-right: 4px">
          <span nz-icon nzType="exclamation-circle" nzTheme="fill" style="color: #fc9958; margin-right: 4px"></span
          ><span [innerHTML]="translateName + '标签数量超过, 请耐心等候' | translate: { count: 100 }">
            <!-- 标签数量超过<span style="color: #fc9958">100张</span>时将等待较长时间，请耐心等候 -->
          </span>
        </div>
        <button nz-button flButton="default" nzShape="round" (click)="onCancel()" style="margin-right: 10px; color: #54607c">
          {{ 'flss.btn.cancel' | translate }}
        </button>
        <button nz-button flButton="pretty-primary" nzShape="round" [nzLoading]="comLoading" (click)="handleOk()">
          {{ 'flss.btn.print' | translate }}
        </button>
      </div>
    </div>
  </div>
</nz-modal>
<!-- 打印列表 -->

<div id="printPage" style="display: none">
  <div
    class="card-box"
    [ngStyle]="{ width: pageSize?.size?.split(',')[0], height: pageSize?.size?.split(',')[1] }"
    *ngFor="let cardItem of this.getPrintCard()">
    <div style="display: flex; justify-content: space-between">
      <ng-container *ngFor="let item of cardItem.option_label_list; index as i">
        <span *ngIf="i == 0" style="font-size: 14px; font-weight: 600">{{ item.label }}：{{ item.value }}</span>
      </ng-container>

      <ng-container *ngFor="let item of cardItem.option_label_list; index as i">
        <span style="font-size: 14px; font-weight: 600" *ngIf="i == 1">{{ item.value }}</span>
      </ng-container>
    </div>
    <nz-divider style="margin: 10px 0"></nz-divider>
    <div style="display: flex; justify-content: space-between; margin-top: 10px">
      <div class="card-box-content">
        <ng-container *ngFor="let item of cardItem.option_label_list; index as j">
          <span *ngIf="j > 1">{{ item.label }}：{{ item.value }}</span>
        </ng-container>
      </div>
      <div>
        <div>
          <ng-container *ngFor="let item of cardItem.option_label_list; index as q">
            <ngx-qrcode *ngIf="item.label_id === '3'" [width]="106" [value]="item.label + '：' + item.value" cssClass="qr-code">
            </ngx-qrcode>
            <span *ngIf="item.label_id === '3'" style="font-size: 12px"
              >{{ translateName + '技术支持' | translate }}-{{ translateName + '飞榴科技' | translate }}</span
            >
          </ng-container>
        </div>
      </div>
    </div>
    <div class="pageBreak"></div>
  </div>
</div>
