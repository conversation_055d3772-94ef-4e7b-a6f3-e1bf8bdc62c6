<div class="outer-container">
  <div #searchBar>
    <flc-search-container
      [headerTitle]="translateName + '模版进度报表' | translate"
      (reset)="onReset()"
      (handleFold)="onFold()"
      [btnTpl]="searchContainerBtnTpl">
      <ng-container *ngFor="let items of searchOptions">
        <div class="search-item-container" *ngIf="items?.visible">
          <span class="search-name"> {{translateName+ items.label | translate }} </span>
          <div class="search-option-{{ items.type }}" [ngSwitch]="items.type">
            <ng-container *ngSwitchCase="'local-select'">
              <nz-select
                [(ngModel)]="searchData[items.key]"
                style="min-width: 120px"
                nzAllowClear
                nzShowSearch
                (ngModelChange)="onChangeDataList(true)"
                [nzPlaceHolder]="'placeholder.select' | translate">
                <nz-option *ngFor="let item of items?.options" [nzValue]="item?.value" [nzLabel]="item?.label"></nz-option>
              </nz-select>
            </ng-container>
            <ng-container *ngSwitchCase="'dateRange'">
              <nz-range-picker
                [(ngModel)]="searchData[items.key]"
                [nzPlaceHolder]="['flss.placeholder.start' | translate, 'flss.placeholder.end' | translate]"
                (ngModelChange)="onChangeDataList(true)"
                [nzSuffixIcon]="''"></nz-range-picker>
            </ng-container>
            <ng-container *ngSwitchCase="'cascader'">
              <nz-cascader
                style="width: 100%"
                [(ngModel)]="searchData[items.key]"
                [nzOptions]="items?.options"
                (ngModelChange)="onChangeDataList(true)"
                [nzPlaceHolder]="'placeholder.select' | translate"
                [nzShowSearch]="false"></nz-cascader>
            </ng-container>
          </div>
        </div>
      </ng-container>
    </flc-search-container>

    <ng-template #searchContainerBtnTpl>
      <button nz-button flButton="pretty-minor" nz-tooltip [nzTooltipTitle]="''" (click)="onOpenDrawerCustom()">
        <i nz-icon [nzIconfont]="'icon-xitongpeizhi'"></i>
      </button>
    </ng-template>
  </div>

  <div class="table-container">
    <div style="display: flex; justify-content: space-between; color: #222b3c">
      <div style="display: flex; align-items: center">
        <span style="font-size: 16px; font-weight: 500">{{translateName + '生产进度报表' | translate}}</span>
        <div style="margin-left: 10px">
          <span>{{translateName + '跟踪状态' | translate}}：</span>
          <nz-select
            style="min-width: 100px"
            nzShowSearch
            [nzDropdownMatchSelectWidth]="false"
            [nzPlaceHolder]="'placeholder.select' | translate"
            [(ngModel)]="track_status"
            (ngModelChange)="_getDataList()">
            <nz-option
              *ngFor="let item of orderStatusList"
              [nzValue]="item?.value"
              [nzLabel]="(translateName + item?.label | translate) + ('·' + (trackStatusStatistics[item.key] || 0))"></nz-option>
          </nz-select>
        </div>
      </div>

      <div style="display: flex; align-items: center">
        <span style="margin-right: 10px" [innerHTML]="translateName + '已选中数据' | translate: {count: selectedCount}"></span>
        <nz-select
          #batchSelectRef
          style="min-width: 100px; max-width: 240px"
          nzAllowClear
          nzShowSearch
          [nzDropdownMatchSelectWidth]="false"
          [(ngModel)]="batchEditField"
          [nzPlaceHolder]="translateName + '请选择批量编辑字段' | translate"
          (ngModelChange)="selectBatchEditField($event, true)"
          (nzOpenChange)="openBatchEditFieldSelect($event)">
          <!--type 1=字段，2=节点 -->
          <nz-option
            *ngFor="let item of updateBatchFieldList;"
            [nzValue]="item"
            [nzLabel]="item?.type === batchEditTypeEnum.field ? (translateName + item?.prod_status_field_name | translate) : item?.template_node_name"></nz-option>
        </nz-select>
        <nz-divider nzType="vertical" style="margin: 0 12px"></nz-divider>
        <button
          *ngIf="_service.btnArr?.includes('bulk:prod-status-report-list-export') "
          nz-button
          nzShape="round"
          flButton="pretty-default"
          style="margin: 0 8px"
          [nzLoading]="isExporting"
          (click)="onBatchExport()">
          <i nz-icon nzIconfont="icon-xiazai"></i>{{'btn.batch_export' | translate}}
        </button>
        <button (click)="onGoProcessTemplate()" flButton="pretty-primary" [nzShape]="'round'" nz-button>
          {{translateName + '进度模版档案'| translate}}
        </button>
      </div>
    </div>
    <div style="color: #515665; display: flex; align-items: center; margin: 10px 0">
      {{translateName + '生产进度状态'| translate}}：<span
        *ngFor="let item of processTag;index as i"
        style="display: inline-flex; align-items: center"
        ><span
          style="display: inline-flex; border-radius: 2px; margin-right: 4px; width: 10px; height: 4px"
          [style]="{backgroundColor: item.color, marginLeft: i ===0? 0: '12px'}"></span
        >{{translateName + item.text | translate}}</span
      >
    </div>

    <flc-table
      #ProductionTableTemplateFiled
      [tableHeader]="tableHeader"
      [tableConfig]="tableConfig"
      [template]="tableTemplate"
      [thTemplate]="thTemplate"
      [batchActionTemplate]="batchActionTemplate"
      (getCount)="getSelectedCount($event)"
      (sizeChanges)="onSizeChanges($event)"
      (indexChanges)="onIndexChange($event)">
    </flc-table>

    <ng-template #thTemplate let-data="data">
      <ng-container *ngIf="data.isTh && data.key === 'nodes'"> {{ translateName + '生产进度'| translate }} </ng-container>
      <ng-container *ngIf="data.isTh && data.key === 'delay_responsibility'">
        {{ translateName + '延迟责任' | translate }} ({{translateName + '交期更改原因' | translate}})
      </ng-container>
    </ng-template>

    <ng-template #tableTemplate let-data="data">
      <!-- 事件汇报 -->
      <ng-container *ngIf="data.isTd && data.key === 'link_button'">
        <span class="blue-link" (click)="onViewAmountDetail(data)"
          >{{ (data.item.event_amount?.length ? data.item.event_amount : 0) | number: '1.0-5' }}</span
        >
      </ng-container>
      <!-- 大货单号 -->
      <ng-container *ngIf="data.isTd && data.key === 'bulk_order_code'">
        <div class="urgent-tip" *ngIf="data.item.urgent_status === 20">急</div>
        <a style="text-decoration: underline" (click)="toOrderPage(data.item)">
          <flc-text-truncated [data]="data.item.bulk_order_code "></flc-text-truncated>
        </a>
        <div class="urgent-tip" *ngIf="data.item.urgent_status === 20">{{translateName + '急' | translate}}</div>
      </ng-container>
      <!-- 款式分类 -->
      <ng-container *ngIf="data.isTd && data.key === 'style_class'">
        <flc-text-truncated
          [data]="
              data.item.first_style_class_name + '-' + data.item.second_style_class_name + '-' + data.item.third_style_class_name
            "></flc-text-truncated>
      </ng-container>
      <!-- 二次配套工厂 -->
      <ng-container *ngIf="data.isTd && data.key === 'extra_process_factories'">
        <flc-text-truncated
          [data]="
              (data.item.extra_process_factories || []).join('、')
            "></flc-text-truncated>
      </ng-container>
      <!-- 订单状态 -->
      <ng-container *ngIf="data.isTd && data.key === 'out_sourcing_status'">
        <flc-text-truncated [data]="translateItem(getOrderStatus(data.item.out_sourcing_status).current_status)"></flc-text-truncated>
        <!-- 未维护针聪明地址的可切换状态 -->
        <span class="change-text" (click)="changeStatus(data.item)"
          >{{translateItem(getOrderStatus(data.item.out_sourcing_status).change_status)}}</span
        >
      </ng-container>

      <ng-container *ngIf="data.isTd && ['po_codes', 'shipment_ports', 'fob_prices', 'customer_io_codes', 'colors'].includes( data.key)">
        <flc-text-truncated [data]="data?.item?.[data.key]?.join('、')"></flc-text-truncated>
      </ng-container>

      <ng-container *ngIf="data.isTd && data.key === 'nodes'">
        <div style="width: 100%; display: flex; justify-content: flex-start" *ngIf="data.item.nodes?.length; else noNodesTpl">
          <div
            class="nodes-item"
            *ngFor="let item of data.item.nodes"
            [style]="{backgroundColor: setProcessBgColor(item)}"
            (click)="onEdit(item,data.item)">
            <span [style]="{backgroundColor: setProcessColor(item)}" class="title"></span>
            <span style="font-size: 14px; color: #222b3c"
              >{{item.template_node_alias_name.length>0?item.template_node_alias_name:item.template_node_name}}
              <i
                *ngIf="item?.auto_delay > 0"
                nz-icon
                [nzIconfont]="'icon-jinggao'"
                nz-tooltip
                [nzTooltipTitle]="(translateName + '已自动延期' | translate ) + item?.auto_delay + (translateName + '天' | translate)"
                style="color: #fe5d56; margin-left: 2px"></i
              >:
            </span>
            <ng-container [ngSwitch]="item.node_type">
              <!-- 1 裁剪 2 车缝 3 巡检 4 后道 8物料 -->
              <ng-container *ngSwitchCase="1">
                <span class="node-field-line">
                  <span class="node-field-name">{{translateName + '累计裁数' | translate}}:</span>
                  <span class="node-field-value" *ngIf="item.total_qty">{{ item.total_qty }}</span>
                </span>
                <span style="justify-content: flex-start">
                  <a nz-button class="preview-img" nzType="link" (click)="previewUploadImgs(item,$event)">
                    {{translateName + '查看图片' | translate}}
                  </a>
                </span>
              </ng-container>
              <ng-container *ngSwitchCase="[2,4].includes(item.node_type)?item.node_type:null">
                <span class="node-field-line">
                  <span class="node-field-name">{{translateName + "累计产量" | translate}}:</span>
                  <span class="node-field-value">{{ item.total_qty}}</span>
                </span>
                <span class="node-field-line">
                  <span class="node-field-name">{{translateName + '是否巡检' | translate}}:</span>
                  <span class="node-field-value">{{translateName + (item.inspected?'是':'否') | translate}}</span>
                </span>
              </ng-container>
              <ng-container *ngSwitchCase="8">
                <ng-container [ngSwitch]="item.complete_status">
                  <ng-container *ngSwitchCase="1"><span>{{translateName + "未齐全" | translate}}</span></ng-container>
                  <ng-container *ngSwitchCase="2"
                    ><span style="color: #fb6401">{{translateName + '部分齐全' | translate}}</span></ng-container
                  >
                  <ng-container *ngSwitchCase="3"
                    ><span style="color: #52c41a">{{translateName + "已齐全" | translate}}</span></ng-container
                  >
                </ng-container>
              </ng-container>
              <ng-container *ngSwitchCase="0">
                <ng-container *ngIf="item.content_type===3;else customNodeTpl">
                  <ng-container [ngSwitch]="item.template_node_name">
                    <ng-container *ngSwitchCase="'裁剪'">
                      <span class="node-field-line">
                        <span class="node-field-name">{{translateName + '累计裁数' | translate}}:</span>
                        <span class="node-field-value" *ngIf="item.actual_qty">{{ item.actual_qty }}</span>
                      </span>
                      <span style="justify-content: flex-end">
                        <a nz-button nzType="link" (click)="previewUploadImgs(item,$event)"> {{translateName + '查看图片' | translate}} </a>
                      </span>
                    </ng-container>
                    <ng-container *ngSwitchCase="'送货'">
                      <span class="node-field-line">
                        <span class="node-field-name">{{translateName + "实际数量" | translate}}:</span>
                        <span class="node-field-value" *ngIf="item.actual_qty">{{ item.actual_qty}}</span>
                      </span>
                      <span style="justify-content: flex-end">
                        <a nz-button nzType="link" (click)="previewUploadImgs(item,$event)"> {{translateName + "查看图片" | translate}} </a>
                      </span>
                    </ng-container>
                    <ng-container *ngSwitchCase="'成品检验'">
                      <span class="node-field-line">
                        <span class="actual-tag">{{translateName + "实际" | translate}}</span>
                        <span class="node-field-value" *ngIf="item.actual_end_time">{{item.actual_end_time | date: 'yyyy/MM/dd'}}</span>
                      </span>
                      <span style="color: #515665; margin-bottom: 3px">
                        <span class="plan-tag">{{translateName + "计划" | translate}}</span>
                        <span style="font-size: 12px" *ngIf="item.plan_end_time">{{item.plan_end_time | date: 'yyyy/MM/dd'}}</span>
                      </span>
                      <span style="font-size: 12px; color: #97999c">
                        <span *ngIf="item.consumed_seconds" style="margin-right: 8px"
                          >{{translateName + '耗时' | translate}}：{{ item.display_consumed_seconds }}</span
                        >
                        <span *ngIf="item.exceed_seconds">{{translateName + '超时'| translate}}：{{ item.display_exceed_seconds }}</span>
                      </span>
                      <span class="node-field-line">
                        <span class="node-field-name">{{translateName + "返修数" | translate}}:</span>
                        <span class="node-field-value" *ngIf="item.repair_qty">{{ item.repair_qty }}</span>
                      </span>
                    </ng-container>
                    <ng-container *ngSwitchCase="'物料采购'">
                      <span class="material-info">
                        <span class="material-info-status-value">{{translateName + getMaterialStatus(item) | translate }}</span>
                      </span>
                    </ng-container>
                    <ng-container *ngSwitchCase="'物料入库'">
                      <span class="material-info" *ngIf="item?.bulk_node_status === 2"
                        >{{ item?.actual_qty > -1 ? item?.actual_qty : '-' }}{{translateName + "公斤" | translate}}</span
                      >
                      <span class="material-info-status">
                        <span class="material-info-status-value">{{translateName + getMaterialStatus(item) | translate}}</span>
                      </span>
                    </ng-container>
                    <ng-container *ngSwitchCase="'领料出库'">
                      <span class="material-info" *ngIf="item?.bulk_node_status === 2"
                        >{{ item?.actual_qty > -1 ? item?.actual_qty : '-' }}{{translateName + '公斤' | translate}}</span
                      >
                      <span class="material-info-status">
                        <span class="material-info-status-value">{{ translateName +getMaterialStatus(item) | translate }}</span>
                      </span>
                    </ng-container>
                  </ng-container>
                </ng-container>
                <ng-template #customNodeTpl>
                  <!-- 质量模版 -->
                  <ng-container *ngIf="item?.template_type === TemplateTypeEnum.qualityTpl; else configurableTpl">
                    <span class="node-field-line">
                      <span class="node-field-name">{{translateName + "检验数" | translate}}:</span>
                      <span class="node-field-value">{{ item.inspected_qty}}</span>
                    </span>
                    <span class="node-field-line">
                      <span class="node-field-name">{{translateName + '合格数/合格率' | translate}}:</span>
                      <span class="node-field-value"
                        >{{
                        getQualifedQty(item.inspected_qty,item.defected_qty)}}/{{getQualifiedPercent(item.inspected_qty,item.defected_qty)}}%</span
                      >
                    </span>
                    <span class="node-field-line">
                      <span class="node-field-name">{{translateName + "不合格数/不良率" | translate}}:</span>
                      <span class="node-field-value"
                        >{{ item.defected_qty}}/{{getDefectedPercent(item.inspected_qty,item.defected_qty)}}%</span
                      >
                    </span>
                  </ng-container>
                  <!-- 可配置 -->
                  <ng-template #configurableTpl>
                    <ng-container *ngFor="let it of item?.node_temp_data_list?.slice(0, 3)">
                      <ng-container *ngIf="it.field_type ===FieldTypeEnum.Attachment">
                        <flc-text2-truncated style="text-align: left" [data]="it?.field_name + '：'"></flc-text2-truncated>
                        <flc-file-gallery
                          [wrap]="false"
                          [isEditMode]="false"
                          [needWatermark]="true"
                          [fileList]="formatterFieldValue(it?.data, it.field_type) || []"
                          [galleryType]="'any'"
                          [allowDownload]="false"></flc-file-gallery>
                      </ng-container>

                      <ng-container *ngIf="it.field_type !==FieldTypeEnum.Attachment">
                        <span style="margin-bottom: 4px" class="node-field-line">
                          <span class="custom-node-field-name">
                            <span class="actual-tag" *ngIf="it?.label === FieldLabelEnum.Actual"
                              >{{translateName + '实际' | translate}}</span
                            >
                            <span class="plan-tag" *ngIf="it?.label === FieldLabelEnum.Plan">{{translateName + "计划" | translate}}</span>
                            <flc-text2-truncated [data]="it?.field_name + '：'"></flc-text2-truncated>
                          </span>
                          <flc-text2-truncated [data]="formatterFieldValue(it?.data, it.field_type)"></flc-text2-truncated>
                        </span>
                      </ng-container>
                    </ng-container>
                  </ng-template>
                </ng-template>
              </ng-container>
            </ng-container>
          </div>
        </div>
        <ng-template #noNodesTpl>
          <span style="color: #b5b8bf">-</span>
        </ng-template>
      </ng-container>

      <ng-container
        *ngIf="data.isTd && ['shipment_status', 'delay_no_of_days', 'delay_range', 'delay_reason', 'ship_performance', 'delay_responsibility','transit_time', 'original_isd', 'revised_isd'].includes(data.key)">
        <ng-container *ngIf="['shipment_status', 'delay_range', 'ship_performance', 'delay_responsibility']?.includes(data.key)">
          <!-- 下拉， 数据取自字典 -->
          <flc-dynamic-search-select
            *ngIf="data.item?.isEdit"
            [dataUrl]="'/service/scm/dict_category/dict_option'"
            [transData]="{ value: 'label', label: 'label' }"
            [column]="data?.col?.column || data?.key"
            [(ngModel)]="data.item[data.key]">
          </flc-dynamic-search-select>

          <flc-text-truncated *ngIf="!data.item?.isEdit" [data]="data.item[data.key]"></flc-text-truncated>
        </ng-container>
        <ng-container *ngIf="data.key === 'delay_no_of_days'">
          <!-- 自动计算-->
          <span>{{automaticCalculate('delay_no_of_days', data.item) ?? '-'}}</span>
        </ng-container>

        <ng-container *ngIf="data.key === 'delay_reason'">
          <textarea
            *ngIf="data.item?.isEdit"
            [nzAutosize]="{ minRows: 1, maxRows: 4 }"
            nz-input
            [(ngModel)]="data.item[data.key]"
            [placeholder]="'placeholder.input' | translate"
            [maxLength]="100"
            nzAutosize
            flcInputTrim></textarea>
          <flc-text-truncated *ngIf="!data.item?.isEdit" [data]="data.item[data.key]"></flc-text-truncated>
        </ng-container>

        <ng-container *ngIf="data.key === 'transit_time'">
          <nz-input-number
            *ngIf="data.item?.isEdit"
            style="width: 100%"
            [(ngModel)]="data.item[data.key]"
            [nzPlaceHolder]="'placeholder.input' | translate"
            [nzMin]="0"
            [nzMax]="99999"
            [nzPrecision]="0">
          </nz-input-number>
          <flc-text-truncated *ngIf="!data.item?.isEdit" [data]="data.item[data.key]"></flc-text-truncated>
        </ng-container>

        <ng-container *ngIf="data.key === 'original_isd'">
          <!-- 自动计算  -->
          <span>{{automaticCalculate('original_isd', data?.item) ?? '-' }}</span>
        </ng-container>

        <ng-container *ngIf="data.key === 'revised_isd'">
          <!-- 自动计算  -->
          <span>{{automaticCalculate('revised_isd', data?.item) ?? '-'}}</span>
        </ng-container>
      </ng-container>

      <!-- 操作列 -->
      <ng-container *ngIf="data.isAction">
        <div class="action-td">
          <ng-container *ngIf="data?.item?.isEdit && data?.item?.track_status === TrackStatusEnum.in_progress">
            <a nz-button nzType="link" nzDanger (click)="cancelEditLine(data.item)">{{ 'btn.cancel' | translate}}</a>
            <a nz-button nzType="link" (click)="saveEditLine(data.item)" [flcDisableOnClick]="1000">{{ 'btn.save' | translate}}</a>
          </ng-container>

          <!-- 跟踪进行中的单据 -->
          <ng-container *ngIf="!data?.item?.isEdit && data?.item?.track_status === TrackStatusEnum.in_progress">
            <a nz-button nzType="link" (click)="editLine(data.item)" [flcDisableOnClick]="1000">{{'btn.edit' | translate}}</a>

            <nz-divider nzType="vertical" style="margin: 0; background-color: #d4d7dc"></nz-divider>

            <a nz-button nzType="link" nzDanger (click)="endTracking(data.item)" [flcDisableOnClick]="1000"
              >{{translateName + '结束跟踪' | translate}}</a
            >
          </ng-container>

          <!-- 跟踪结束的单据 -->
          <a
            *ngIf="data?.item?.track_status === TrackStatusEnum.end_tracking"
            nz-button
            nzType="link"
            (click)="recoverTracking(data.item)"
            [flcDisableOnClick]="1000"
            >{{translateName + '恢复跟踪' | translate}}</a
          >
        </div>
      </ng-container>
    </ng-template>

    <ng-template #batchActionTemplate>
      <app-message-notification #messageNotificationRef></app-message-notification>
    </ng-template>
  </div>
</div>
<nz-modal
  [(nzVisible)]="visible"
  [nzTitle]="confirmTitle"
  [nzFooter]="confirmFooter"
  [nzContent]="confirmContent"
  (nzOnCancel)="visible = false"
  nzWidth="304px"
  nzWrapClassName="design-draft-confirm-modal">
  <ng-template #confirmTitle>
    <div class="title">{{proceParams.template_node_name}}</div>
  </ng-template>

  <ng-template #confirmContent>
    <div class="processs-body">
      <div *ngFor="let cf of basicInfoConfig" style="display: flex">
        <span>{{translateName + cf?.label | translate}}：</span>
        <flc-text-truncated [data]="orderLineInfo?.[cf.key]"></flc-text-truncated>
      </div>

      <div *ngIf="[1,3].includes(proceParams.content_type!)">
        <span>{{translateName +"计划完成时间" | translate}}：</span>
        <nz-date-picker
          [(ngModel)]="proceParams.plan_end_time"
          [nzShowTime]="{ nzFormat: 'HH 时' }"
          nzFormat="yyyy-MM-dd HH 时"
          [nzPlaceHolder]="'placeholder.input' | translate"
          (ngModelChange)="onModelChange($event)"></nz-date-picker>
      </div>
      <div *ngIf="[1,3].includes(proceParams.content_type!)">
        <span>{{translateName +'实际开始时间' | translate}}：</span>
        <span *ngIf="proceParams.actual_start_time  &&  proceParams.actual_start_time> 0">
          {{proceParams.actual_start_time | date: 'yyyy/MM/dd HH'}}</span
        >
      </div>
      <div *ngIf="[1,3].includes(proceParams.content_type!)">
        <span>{{translateName +"实际完成时间" | translate}}：</span>
        <nz-date-picker
          [(ngModel)]="proceParams.actual_end_time"
          [nzShowTime]="{ nzFormat: 'HH 时' }"
          nzFormat="yyyy-MM-dd HH 时"
          [nzPlaceHolder]="'placeholder.input' | translate"></nz-date-picker>
      </div>
      <!-- <div *ngIf="[1,3].includes(proceParams.content_type!) && checkBoxVisible">
        <label nz-checkbox [(ngModel)]="proceParams.plan_end_time_cascade">重新计算后直接点计划时间</label>
      </div> -->
      <div *ngIf="proceParams.content_type === 2">
        <span>{{translateName +'计划完成数量' | translate}}：</span>
        <nz-input-number
          [(ngModel)]="proceParams.plan_qty"
          [nzPlaceHolder]="'placeholder.input' | translate"
          [nzMax]="99999999"
          [nzStep]="1"
          (ngModelChange)="numberModelChange($event, 'scale')"></nz-input-number>
      </div>
      <div *ngIf="proceParams.content_type === 2">
        <span>{{translateName +'实际完成数量' | translate}}：</span>
        <nz-input-number
          [(ngModel)]="proceParams.actual_qty"
          [nzPlaceHolder]="'placeholder.input' | translate"
          [nzMax]="99999999999"
          [nzStep]="1"
          (ngModelChange)="numberModelChange($event, 'scale')"></nz-input-number>
      </div>
      <div *ngIf="proceParams.content_type === 3">
        <span>{{translateName + '返修数' | translate}}：</span>
        <nz-input-number
          [(ngModel)]="proceParams.repair_qty"
          [nzPlaceHolder]="'placeholder.input' | translate"
          [nzMax]="99999999999"
          [nzStep]="1"></nz-input-number>
      </div>
    </div>
  </ng-template>
  <ng-template #confirmFooter>
    <div style="text-align: center">
      <button nz-button nzType="default" flButton="default-negative" [nzShape]="'round'" (click)="visible = false">
        {{'btn.cancel' | translate}}
      </button>
      <button nz-button nzType="primary" flButton="default-positive" [nzShape]="'round'" (click)="handleOkModal()">
        {{'btn.ok' | translate}}
      </button>
    </div>
  </ng-template>
</nz-modal>

<!-- 事件汇报弹出框 -->
<nz-modal
  [(nzVisible)]="event_visible"
  [nzTitle]="(tableConfig.translateName + '事件汇报') | translate"
  [nzFooter]="event_confirmFooter"
  [nzContent]="event_confirmContent"
  (nzOnCancel)="event_visible = false"
  nzWidth="1200px"
  nzClassName="product-table-event-confirm-modal">
  <ng-template #event_confirmContent>
    <div>
      <div class="event-modal-header">
        <div class="item">
          <div>{{translateName + '订单需求号'| translate}}: {{event_data?.bulk_order_code ?? '-'}}</div>
          <div>{{translateName + '生产类型' | translate}}: {{event_data?.order_production_type_value ?? '-'}}</div>
          <div>{{translateName + '加工厂' | translate}}: {{event_data?.factory_name ?? '-'}}</div>
        </div>
        <button nz-button flButton="minor" [nzShape]="'round'" (click)="addEventForm()">
          <i nz-icon [nzIconfont]="'icon-xinjian1'"></i>
          {{translateName + '新增' | translate}}
        </button>
      </div>
      <div style="min-height: 176px; padding: 0 20px">
        <form nz-form [formGroup]="event_form">
          <nz-table #eventTable nzBordered [nzData]="event_form_array.value" [nzShowPagination]="false">
            <thead>
              <tr>
                <th>{{translateName + '序号' | translate}}</th>
                <th nzWidth="120px">{{translateName + '是否通知财务'| translate}}</th>
                <th nzWidth="250px">
                  <span style="color: red">*</span>
                  {{translateName + '事件类型' | translate}}<i
                    nz-icon
                    nz-tooltip
                    [nzTooltipTitle]="tooltipsTpl"
                    nzType="exclamation-circle"
                    nzTheme="outline">
                  </i>
                  <ng-template #tooltipsTpl>
                    {{translateName + "支持维护正负数"| translate}}<br />
                    {{translateName + '正数：建议需增加的金额'| translate}}<br />
                    {{translateName + '负数：建议需扣减的金额'| translate}}
                  </ng-template>
                </th>
                <th nzWidth="120px">{{translateName + '金额'| translate}}</th>
                <th nzWidth="300px">{{translateName + '事件描述' | translate}}</th>
                <th>{{translateName + '汇报时间' | translate}}</th>
                <th nzWidth="80px">{{translateName + '操作' | translate}}</th>
              </tr>
            </thead>
            <tbody formArrayName="event_form_list">
              <tr *ngFor="let data of event_form_array.controls; index as i" [formGroupName]="i">
                <td>{{ i + 1 }}</td>
                <td>
                  <nz-switch
                    *ngIf="data.get('can_delete').value"
                    [formControlName]="'is_notify'"
                    [nzCheckedChildren]="translateName + '是'| translate"
                    [nzUnCheckedChildren]="translateName + '否'| translate"
                    (ngModelChange)="notifyChanged($event, i)"></nz-switch>
                  <span *ngIf="!data.get('can_delete').value">{{translateName +'已参与'| translate}}</span>
                </td>
                <td>
                  <ng-container *ngIf="data.get('can_delete').value">
                    <nz-cascader
                      *ngIf="data.get('is_notify').value"
                      [nzOptions]="event_options"
                      [nzDefaultValue]="data.get('adjust_default_options')?.value ?? []"
                      [formControlName]="'adjust_id_local'"
                      (ngModelChange)="adjustIdChangegd($event, i)">
                    </nz-cascader>
                    <nz-select
                      *ngIf="!data.get('is_notify').value"
                      [formControlName]="'adjust_id'"
                      [nzPlaceHolder]="'placeholder.select' | translate"
                      (ngModelChange)="adjustIdChangegdUnNotify($event, i)">
                      <nz-option
                        *ngFor="let item of notify_event_options"
                        [nzValue]="item?.adjust_id"
                        [nzLabel]="item?.adjust_name"></nz-option>
                    </nz-select>
                  </ng-container>
                  <ng-container *ngIf="!data.get('can_delete').value">
                    <span *ngIf="data.get('is_notify').value">
                      {{data.get('adjust_type_value').value + '/' + data.get('settlement_type_value').value + '/' +
                      data.get('adjust_name').value}}
                    </span>
                    <span *ngIf="!data.get('can_delete').value"> {{data.get('adjust_name').value}} </span>
                  </ng-container>
                </td>
                <td>
                  <nz-input-group
                    *ngIf="data.get('can_delete').value && data.get('is_notify').value"
                    [nzAddOnBefore]="data.get('adjust_type').value == 1 ? '-' : '+'">
                    <nz-input-number
                      [nzPlaceHolder]="'flss.placeholder.input' | translate"
                      [nzPrecision]="2"
                      [nzMin]="0"
                      [formControlName]="'amount'"></nz-input-number>
                  </nz-input-group>
                  <nz-input-group
                    class="normal-input-group"
                    *ngIf="data.get('can_delete').value && !data.get('is_notify').value"
                    [nzAddOnBefore]="'+'">
                    <nz-input-number
                      [nzPlaceHolder]="'flss.placeholder.input' | translate"
                      [nzPrecision]="2"
                      [formControlName]="'amount'"></nz-input-number>
                  </nz-input-group>
                  <div nz-row *ngIf="!data.get('can_delete').value">
                    <span class="custom-input-group-addon">{{data.get('adjust_type').value == 1 ? '-' : '+'}} </span>
                    <span class="custom-input-group-plain">{{data.get('amount').value}} </span>
                  </div>
                </td>
                <td>
                  <input
                    nz-input
                    *ngIf="data.get('can_delete').value"
                    [placeholder]="'flss.placeholder.input' | translate"
                    maxlength="50"
                    [formControlName]="'desc_text'" />
                  <div style="text-align: left; padding: 4px 11px" *ngIf="!data.get('can_delete').value">
                    {{data.get('desc_text').value}}
                  </div>
                </td>
                <td>
                  <span>{{data.get('created_at').value | date: 'yyyy-MM-dd HH:mm:ss'}}</span>
                </td>
                <td>
                  <button
                    class="delete-icon"
                    [ngClass]="{disable: !data.get('can_delete').value}"
                    nz-button
                    nzType="link"
                    [disabled]="!data.get('can_delete').value"
                    (click)="deleteEventForm(i)">
                    <i nz-icon [nzIconfont]="'icon-caozuolan_shanchu1'"></i>
                  </button>
                </td>
              </tr>
            </tbody>
          </nz-table>
        </form>
      </div>
    </div>
  </ng-template>
  <ng-template #event_confirmFooter>
    <div style="text-align: center">
      <button nz-button nzType="default" flButton="default-negative" [nzShape]="'round'" (click)="event_visible = false">
        {{"flss.btn.cancel" | translate}}
      </button>
      <button
        nz-button
        nzType="primary"
        flButton="default-positive"
        [flcDisableOnClick]="1000"
        [nzShape]="'round'"
        (click)="event_handleOkModal()">
        {{'flss.btn.save' | translate}}
      </button>
    </div>
  </ng-template>
</nz-modal>
