export enum DetailDataTypeTransform {
  daily_cutting_qty = 'OS_Daily_Cutting_Qty',
  cutting_qty = 'OS_Cutting_Qty',
  daily_outsource_qty = 'OS_Daily_Outsource_Qty',
  outsource_qty = 'OS_Outsource_Qty',
  daily_received_qty = 'OS_Daily_Received_Qty',
  received_qty = 'OS_received_Qty',
  daily_transit_qty = 'OS_Daily_Transit_Qty',
  transit_qty = 'OS_Transit_Qty',
  daily_finished_qty = 'OS_Daily_Finished_Qty',
  finished_qty = 'OS_Finished_Qty',
  daily_qualified_qty = 'OS_Daily_Qualified_Qty',
  qualified_qty = 'OS_Qualified_Qty',
  daily_defective_qty = 'OS_Daily_Defective_Qty',
  defective_qty = 'OS_Defective_Qty',
  daily_delivered_qty = 'OS_Daily_Delivered_Qty',
  delivered_qty = 'Delivered_Qty',
}

export enum DetailDataType {
  OS_Daily_Cutting_Qty = 1, // 日已裁数 (保留字)
  OS_Cutting_Qty = 2, // 总已裁数
  OS_Daily_Outsource_Qty = 3, // 日外发数 (保留字)
  OS_Outsource_Qty = 4, // 总外发数
  OS_Daily_Received_Qty = 5, // 日收货数
  OS_received_Qty = 6, // 总收货数
  OS_Daily_Transit_Qty = 7, // 日在途数  (保留字)
  OS_Transit_Qty = 8, // 总在途数
  OS_Daily_Finished_Qty = 9, // 日完成数
  OS_Finished_Qty = 10, // 总完成数
  OS_Daily_Qualified_Qty = 11, // 日合格数
  OS_Qualified_Qty = 12, // 总合格数
  OS_Daily_Defective_Qty = 13, // 日不良数
  OS_Defective_Qty = 14, // 总不良数
  OS_Daily_Delivered_Qty = 15, // 日发货数
  Delivered_Qty = 16, // 总发货数
}

export enum DetailModalMode {
  ColorSizeMode = 0,
  PartColorSizeMode = 1,
  OutsourceMode = 2,
  DeliveredMode = 3,
}
