.basicForm {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  column-gap: 8px;
  row-gap: 12px;
  margin-bottom: 12px;
}
.basicItem {
  display: flex;
  font-size: 14px;
  .title {
    display: inline-block;
    width: 122px;
    flex-shrink: 0;
    text-align: right;
    color: #515665;
    &::after {
      content: ':';
    }
  }
  .value {
    color: #222b3c;
    font-weight: 500;
  }
}
.tabForm {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  column-gap: 8px;
  row-gap: 12px;
  align-items: center;
}
:host nz-form-item {
  margin-bottom: 0;
  align-items: center;
  ::ng-deep nz-form-control span.displayValue {
    margin-left: 4px;
  }
}

.title-widget {
  font-size: 16px;
  height: 40px;
  justify-content: space-between;
  display: flex;
  align-content: center;
  align-items: center;
}
