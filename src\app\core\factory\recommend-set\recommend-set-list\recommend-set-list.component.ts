import { Component, OnInit } from '@angular/core';
import { RecommendKeyEnum } from '../models/recommend.enum';
import { AppStorageService } from 'src/app/shared/app-storage.service';
import { RecommendSetService } from '../recommend-set.service';
import { RecommendsItem } from '../models/recommend.interface';
import { RecommendElementObj } from '../models/recommend.config';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Subject, debounce, distinctUntilChanged, timer } from 'rxjs';
import { FlcModalService } from 'fl-common-lib';

@Component({
  selector: 'app-recommend-set-list',
  templateUrl: './recommend-set-list.component.html',
  styleUrls: ['./recommend-set-list.component.scss'],
  providers: [RecommendSetService],
})
export class RecommendSetListComponent implements OnInit {
  recommendElementObj = RecommendElementObj;
  recommendElementOptions = Object.values(this.recommendElementObj);

  checked = false;
  indeterminate = false;
  listOfData: readonly RecommendsItem[] = [];
  setOfChecked = new Set<RecommendKeyEnum>();
  alreadyAdd: RecommendKeyEnum[] = [];
  editCache: { [key in RecommendKeyEnum]?: RecommendsItem } = {};

  valChange$ = new Subject<RecommendsItem>();

  constructor(
    public _service: RecommendSetService,
    public _storage: AppStorageService,
    private _msg: NzMessageService,
    private _flcModal: FlcModalService
  ) {}

  ngOnInit(): void {
    this.getList();
    this.listenChange();
  }
  ngOnDestroy() {
    this.valChange$.unsubscribe();
  }

  getList() {
    this._service.getList().subscribe((res) => {
      if (res.code === 200) {
        this.listOfData = res.data.list;
        this.alreadyAdd = this.listOfData.map((d) => d.key);
        this.listOfData.forEach((d) => {
          this.editCache[d.key] = {
            key: d.key,
            value: d.value,
            remark: d.remark,
          };
        });
      }
    });
  }

  updateCheckedSet(key: RecommendKeyEnum, checked: boolean): void {
    if (checked) {
      this.setOfChecked.add(key);
    } else {
      this.setOfChecked.delete(key);
    }
  }

  refreshCheckedStatus(): void {
    this.checked = this.listOfData.every(({ key }) => this.setOfChecked.has(key));
    this.indeterminate = this.listOfData.some(({ key }) => this.setOfChecked.has(key)) && !this.checked;
  }

  onItemChecked(key: RecommendKeyEnum, checked: boolean): void {
    this.updateCheckedSet(key, checked);
    this.refreshCheckedStatus();
  }

  onAllChecked(checked: boolean): void {
    this.listOfData.forEach(({ key }) => this.updateCheckedSet(key, checked));
    this.refreshCheckedStatus();
  }

  onBatchDelete() {
    const del_keys = [...this.setOfChecked];
    if (!del_keys.length) {
      this._msg.warning('请选择一条数据');
      return;
    }

    this._flcModal
      .confirmCancel({
        content: '确定删除吗？',
      })
      .afterClose.subscribe((res: any) => {
        if (!res) {
          return;
        } else {
          this.handleDelete(del_keys);
        }
      });
  }

  handleDelete(key: RecommendKeyEnum[]) {
    this._service.delete(key).subscribe((res) => {
      if (res.code === 200) {
        this.getList();
        this.setOfChecked.clear();
        this.refreshCheckedStatus();
      }
    });
  }

  handelAdd(setOfChecked: Set<RecommendKeyEnum>) {
    const payload: RecommendsItem[] = [];
    [...setOfChecked].forEach((d) => {
      if (!this.alreadyAdd.includes(d)) {
        payload.push({
          key: d,
          value: '0',
          remark: '',
        });
      }
    });

    this._service.update(payload).subscribe((res) => {
      if (res.code === 200) {
        this.getList();
        // 滚动到最新行
        setTimeout(() => {
          const new_row = document.getElementsByClassName('last-element-item')[0];
          if (new_row) {
            new_row.scrollIntoView();
          }
        }, 300);
      }
    });
  }

  handelTextChange(key: RecommendKeyEnum) {
    this.valChange$.next({ ...this.editCache[key]! });
  }
  listenChange() {
    this.valChange$
      .pipe(
        debounce(() => timer(1000)), //  等待，直到用户停止输入
        distinctUntilChanged() //  等待，直到搜索内容发生了变化
      )
      .subscribe((payload) => {
        this._service.update([payload]).subscribe(() => {});
      });
  }

  maxValidate(key: RecommendKeyEnum, i: number) {
    if (this.editCache[key]!.value !== this.listOfData[i].value) {
      const total = Object.values(this.editCache).reduce((acc, cur) => acc + Number(cur.value), 0);
      if (total > 100) {
        this._msg.error('各元素权重之和不得超过100%');
        setTimeout(() => {
          this.editCache[key]!.value = this.listOfData.find((d) => d.key === key)?.value;
        }, 100);
      }
    }
  }

  handelNumberBlur(key: RecommendKeyEnum, i: number) {
    if (this.editCache[key]!.value !== this.listOfData[i].value) {
      const params = {
        ...this.editCache[key],
        value: this.editCache[key]?.value?.toString(),
      };
      this._service.update([params]).subscribe(() => {});
    }
  }
}
