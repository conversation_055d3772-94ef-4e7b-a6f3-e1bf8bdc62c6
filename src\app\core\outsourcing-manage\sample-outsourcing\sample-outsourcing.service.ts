import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { EventEmitter, Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { FlcSpKeyConstant, FlcSpUtilService } from 'fl-common-lib';
import { NzCascaderOption } from 'ng-zorro-antd/cascader';
import { IBaseDetailModel, IDetailModel, ResultData } from './modal/sample-outsourcing.interface';

@Injectable()
export class SampleOutsourcingService {
  public serviceUrl = '/service/scm/sample_outsourcing';
  public archiveBaseUrl = '/service/archive/v1';
  public searchOptionUrl = '/service/scm/sample_outsourcing/list_option';
  public refreshEvent = new EventEmitter();
  public translateEventEmitter: EventEmitter<void> = new EventEmitter();
  public changeSampleOrderEvent: EventEmitter<number | null> = new EventEmitter();

  constructor(private http: HttpClient, private _spUtil: FlcSpUtilService, private _translate: TranslateService) {}

  /**
   * 打样列表
   */
  sampleList(payload: any): Observable<any> {
    return this.http.post<any>(`${this.serviceUrl}/list`, payload);
  }

  /**
   * 款式分类下拉
   * @returns
   */
  getStyleOptions() {
    return this.http.post<ResultData<NzCascaderOption[]>>(`${this.serviceUrl}/style_option`, {
      column: 'style',
      value: '',
      limit: 0,
      page: 1,
    });
  }

  /**
   * 新建or编辑保存外发单
   * @param id
   * @param params
   * @returns
   */
  saveSampleOutsouring(id: string | number, params: IBaseDetailModel): Observable<ResultData<number>> {
    const _url = id === 'new' ? '/new/save' : '/edit/save';
    return this.http.post<any>(`${this.serviceUrl}${_url}`, {
      id: id === 'new' ? null : id,
      ...params,
    });
  }

  /**
   * 新建or编辑提交外发单
   * @param id
   * @param params
   * @returns
   */
  submitSampleOutsouring(id: string | number, params: IBaseDetailModel) {
    const _url = id === 'new' ? '/new/submit' : '/edit/submit';
    return this.http.post<ResultData<number>>(`${this.serviceUrl}${_url}`, {
      id: id === 'new' ? null : id,
      ...params,
    });
  }

  /**
   * 外发详情
   * @param id
   * @returns
   */
  getSampleOutersouringDetail(id: string | number) {
    return this.http.get<ResultData<IDetailModel>>(`${this.serviceUrl}/${id}`);
  }

  /**
   * 打样单详情
   * @param id
   * @returns
   */
  getSampleDetail(id: string | number) {
    return this.http.get<ResultData<IDetailModel>>(`/service/scm/api/sample_order/${id}`);
  }

  /**
   * 外发进度
   * @param sample_order_id 打样需求id
   */
  getSampleProgress(sample_order_id: number | string) {
    return this.http.get(`/service/scm/sample_order/outsourced_progress/${sample_order_id}`);
  }

  /**
   * 取消外发
   * @param id 外发单id
   */
  cancelSampleOutsourcing(id: number | string) {
    return this.http.put<ResultData<boolean>>(`${this.serviceUrl}/${id}/cancel`, {});
  }

  /**
   * 审核
   * @param payload
   * @returns
   */
  auditSample(payload: { sample_order_id?: number | string; status?: number; reason_for_return?: string }) {
    return this.http.put<ResultData<boolean>>(`${this.serviceUrl}/audit`, payload);
  }

  /**
   * 样衣收货
   * @param payload
   * @returns
   */
  receiveSample(id: number | string) {
    return this.http.put<ResultData<boolean>>(`${this.serviceUrl}/${id}/receive`, {});
  }

  /**
   * 获取历史版本
   * @returns
   */
  getVersions(id: number) {
    return this.http.get(`${this.serviceUrl}/history/list/${id}`);
  }

  /**
   * 获取历史版本详情
   */
  getHistoryDetail(histoyr_id: number) {
    return this.http.get<ResultData<IDetailModel>>(`${this.serviceUrl}/history/detail/${histoyr_id}`);
  }

  //详情省市区
  getAddress() {
    return this.http.get<any>('/service/archive/v1/region?level=0');
  }

  getFactoryList() {
    return this.http.post<any>('/service/archive/v1/api/factory/auth/bind/basic_option', {
      limit: 999,
      page: 1,
      column: 'factory_name',
      value: '',
    });
  }

  userActions: Array<string> = [];
  getUserActions() {
    if (!this.userActions.length && this._spUtil.containsObjectKey(FlcSpKeyConstant.UserActions)) {
      const actionMap = this._spUtil.getObject(FlcSpKeyConstant.UserActions) as Map<string, []>;
      const uaKey = 'outsourcing-manage/sample-outsourcing';
      this.userActions = (actionMap.get(uaKey) as string[]) || [];
    }
    return this.userActions;
  }

  translateValue(key: string) {
    return this._translate.instant(key);
  }

  /**  材料企划打通    **/
  // 材料企划详情
  getMaterialPlanDetail(id: number) {
    return this.http.get<any>(`/service/scm/material_plan/${id}`);
  }
}
