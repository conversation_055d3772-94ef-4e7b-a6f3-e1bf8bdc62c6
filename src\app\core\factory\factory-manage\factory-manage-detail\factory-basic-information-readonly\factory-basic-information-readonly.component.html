<div class="basic-container">
  <div class="title-label">{{ step }}&nbsp;&nbsp;{{ '工厂基本信息' | translate }}</div>
  <nz-divider></nz-divider>
  <div style="display: flex; flex-wrap: wrap; row-gap: 16px">
    <div class="info-line" *ngFor="let data of detailConfig">
      <div class="text-label" [ngClass]="{ 'text-label-required': data?.required }">
        {{ data.label | translate }}
      </div>
      <div *ngIf="_service.factoryForm">
        <ng-container *ngIf="!data?.template">
          {{ _service.factoryForm.getRawValue()[data.labelKey ?? data.key] | noValue: '-' }}
        </ng-container>
        <ng-container *ngIf="data?.template && data.template === 'region'">
          <ng-container *ngIf="_service.factoryForm.getRawValue()[data.key]?.province?.name; else noRegion">
            {{ _service.factoryForm.getRawValue()[data.key]?.province?.name }}
            {{ _service.factoryForm.getRawValue()[data.key]?.city?.name }}
          </ng-container>
          <ng-template #noRegion>
            {{ '-' }}
          </ng-template>
        </ng-container>
        <ng-container *ngIf="data?.template && data.template === 'factoryType'">
          <ng-container [ngSwitch]="_service.factoryForm.getRawValue()[data.key]">
            <ng-container *ngSwitchCase="1">深铺</ng-container>
            <ng-container *ngSwitchCase="2">Sewsmart</ng-container>
            <ng-container *ngSwitchCase="4">LMS</ng-container>
          </ng-container>
        </ng-container>
        <ng-container *ngIf="_service.factoryForm.getRawValue()[data.key] && data.unit">
          {{ data.unit | translate }}
        </ng-container>
        <i
          nz-tooltip
          nzTooltipTitle="最近修改"
          class="update-icon"
          *ngIf="_service.factoryForm.get('changelog')?.value?.includes(data.key)"
          nz-icon
          [nzIconfont]="'icon-jinggao'"></i>
      </div>
    </div>
  </div>
</div>
