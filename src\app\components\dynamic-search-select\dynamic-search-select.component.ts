import { HttpClient } from '@angular/common/http';
import { Component, Input, OnInit, Output, EventEmitter, OnDestroy, OnChanges, SimpleChanges, forwardRef } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { Observable, Subject, timer } from 'rxjs';
import { debounce, distinctUntilChanged, switchMap } from 'rxjs/operators';

@Component({
  selector: 'app-dynamic-search-select',
  templateUrl: './dynamic-search-select.component.html',
  styleUrls: ['./dynamic-search-select.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DynamicSearchSelectComponent),
      multi: true,
    },
  ],
})
export class DynamicSearchSelectComponent implements ControlValueAccessor, OnInit, OnDestroy, OnChanges {
  @Input() payLoad: any = {}; // 输入字段
  @Input() column!: string;
  @Input() disabled = false; // 是否可选
  @Output() handleSearch = new EventEmitter<any>();
  @Input() dataUrl = ''; // 获取数据的接口地址
  @Input() transData?: { value: string; label: string }; // 需转换成对应结构的字段
  @Input() cpnMode: 'multiple' | 'tags' | 'default' = 'default';
  @Input() cpnOptionOverflowSize = 8; // 下拉菜单中最多展示的 Option 个数，超出部分滚动
  @Input() defaultOptions: { label: any; value: any; hide: boolean }[] = [];
  @Input() dataParam = 'data';
  payloadChange = false;
  public selectVlaue: any;
  @Input() defaultValue: any; // 传入默认值（label,value不一样）
  selectOptions: { label: any; value: any; hide: boolean }[] = [];
  value: any = '';
  placeSelect!: string;
  pageSize = 300;
  pageIndex = 1;
  selectPages = 1;
  data: any; // 返回数据
  isSelectLoading = false;
  searchChange$ = new Subject<{
    limit: number;
    page: number;
    column: string;
    value: any;
  }>();
  isFirst = true;

  onChange: any = () => {};
  onTouch: any = () => {};

  writeValue(obj: any): void {
    this.selectVlaue = obj;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouch = fn;
  }

  setDisabledState?(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  constructor(private _http: HttpClient, private _translate: TranslateService) {}

  ngOnInit(): void {
    this.placeSelect = this._translate.instant('placeholder.select');
    this.debounceList();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes && changes.payLoad && changes.payLoad.currentValue) {
      this.payloadChange = true;
    }
    if (changes && changes.defaultOptions && changes.defaultOptions.currentValue) {
      this.selectOptions = [...this.selectOptions, ...this.defaultOptions];
    }
  }

  ngOnDestroy() {
    this.searchChange$.unsubscribe();
  }

  loadMore() {
    const payload = {
      limit: this.pageSize,
      page: this.pageIndex,
      column: this.column,
      value: this.value,
      ...this.payLoad,
    };
    if (!(this.pageIndex > this.selectPages)) {
      this.isSelectLoading = true;
      this.searchChange$.next(payload);
    }
  }

  debounceList() {
    this.searchChange$
      .pipe(
        debounce(() => timer(this.isFirst ? 0 : 500)), //  等待，直到用户停止输入(500ms)
        distinctUntilChanged(), //  等待，直到搜索内容发生了变化
        switchMap((obj) => this.getOptionLists({ ...obj, ...this.payLoad })) // 把搜索请求发送给服务
      )
      .subscribe(this.searchObserver());
  }

  /**
   * 获取搜索下拉数据
   */
  getOptionLists(payload: any): Observable<any> {
    return this._http.post<any>(this.dataUrl, payload);
  }

  searchObserver() {
    return {
      next: (res: any) => {
        this.isFirst = false;
        if (res.code === 200) {
          this.data = res.data;
          // const data: { data: any[]; len: number } = res.data ?? { data: [], len: 0 };
          const data: any[] = this.dataParam ? res.data[this.dataParam] : res.data;
          const options = data.map((item: any) => {
            return {
              label: this.transData ? item[this.transData?.label] : item,
              value: this.transData ? item[this.transData?.value] : item,
              hide: false,
              ...item,
            };
          });
          this.selectPages = Math.ceil(this.data.len / this.pageSize);
          this.selectOptions = this.pageIndex === 1 ? [...options] : [...this.selectOptions, ...options];
          this.pageIndex = this.pageIndex + 1;
          this.isSelectLoading = false;
        } else {
          this.isSelectLoading = false;
        }
      },
      error: () => {
        this.isSelectLoading = false;
      },
      complete: () => {
        this.isSelectLoading = false;
      },
    };
  }

  onSelectSearch(e: any) {
    this.isSelectLoading = true;
    this.pageIndex = 1;
    this.value = e;
    const payload = {
      limit: this.pageSize,
      page: this.pageIndex,
      column: this.column,
      value: e,
    };
    this.searchChange$.next(payload);
  }

  onSearch(e: any) {
    if (this.disabled) {
      return;
    }
    this.onChange(e);
    const selectLine = this.selectOptions.find((item) => item.value === e);
    this.handleSearch.emit({ column: this.column, value: this.selectVlaue, data: this.data, selectLine: selectLine });
  }

  openChange(e: boolean) {
    if (e) {
      if (this.selectOptions.length === 0) {
        this.onSelectSearch(null);
        this.payloadChange = false;
      } else if (this.payloadChange) {
        this.onSelectSearch(null);
        this.payloadChange = false;
      }
    }
  }
}
