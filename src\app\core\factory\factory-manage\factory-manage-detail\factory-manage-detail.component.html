<ng-container>
  <div class="container-outer">
    <div class="header-container">
      <div class="detail-title">{{ '工厂详情' | translate }}</div>
      <div class="detail-btn">
        <!--                待审核/待初始化/初始化完成/已撤销/已注销-->
        <ng-container
          *ngIf="
            ![
              _service.factoryStatusEnum.Create,
              _service.factoryStatusEnum.DRAFT,
              _service.factoryStatusEnum.MODIFY_PENDING,
              _service.factoryStatusEnum.DEPLOY_PENDING
            ].includes(_service.factoryStatus) ||
            (!editable &&
              [
                _service.factoryStatusEnum.Create,
                _service.factoryStatusEnum.DRAFT,
                _service.factoryStatusEnum.MODIFY_PENDING,
                _service.factoryStatusEnum.DEPLOY_PENDING
              ].includes(_service.factoryStatus))
          ">
          <button nz-button flButton="default" nzShape="round" (click)="back()">
            {{ 'btn.back' | translate }}
          </button>
        </ng-container>

        <!--                新建/待激活/待修改/待部署-->
        <ng-container
          *ngIf="
            editable &&
            [
              _service.factoryStatusEnum.Create,
              _service.factoryStatusEnum.DRAFT,
              _service.factoryStatusEnum.MODIFY_PENDING,
              _service.factoryStatusEnum.DEPLOY_PENDING
            ].includes(_service.factoryStatus)
          ">
          <button nz-button flButton="default" nzShape="round" (click)="cancel(cancelTpl)">
            {{ 'btn.cancel' | translate }}
          </button>
        </ng-container>

        <!--                待激活/待修改-->
        <ng-container
          *ngIf="
            [_service.factoryStatusEnum.DRAFT, _service.factoryStatusEnum.MODIFY_PENDING].includes(_service.factoryStatus) && !editable
          ">
          <button
            nz-button
            flButton="default-negative-danger"
            nzShape="round"
            nzDanger
            (click)="delete()"
            [disableOnClick]="1000"
            *ngIf="_listService.btnArr.includes('settings:factoryManage-delete')">
            {{ 'btn.delete' | translate }}
          </button>
        </ng-container>

        <!--                待审核/待部署-->
        <ng-container
          *ngIf="
            [_service.factoryStatusEnum.APPROVE_PENDING, _service.factoryStatusEnum.DEPLOY_PENDING].includes(_service.factoryStatus) &&
            !editable
          ">
          <button
            nz-button
            flButton="default"
            nzShape="round"
            (click)="undo()"
            [disableOnClick]="1000"
            *ngIf="_listService.btnArr.includes('settings:factoryManage-withdraw')">
            {{ 'btn.undo' | translate }}
          </button>
        </ng-container>

        <!--                待审核-->
        <ng-container *ngIf="_service.factoryStatus === _service.factoryStatusEnum.APPROVE_PENDING">
          <button
            nz-button
            flButton="fault-minor"
            nzShape="round"
            (click)="modify()"
            [disableOnClick]="1000"
            *ngIf="_listService.btnArr.includes('settings:factoryManage-back')">
            {{ 'btn.modify' | translate }}
          </button>
          <button
            nz-button
            nzType="primary"
            nzShape="round"
            (click)="pass()"
            [disableOnClick]="1000"
            *ngIf="_listService.btnArr.includes('settings:factoryManage-audit')">
            {{ 'btn.pass' | translate }}
          </button>
        </ng-container>

        <!--                激活/待激活/待部署/待修改-->
        <ng-container
          *ngIf="
            editable &&
            [
              _service.factoryStatusEnum.Create,
              _service.factoryStatusEnum.DRAFT,
              _service.factoryStatusEnum.MODIFY_PENDING,
              _service.factoryStatusEnum.DEPLOY_PENDING
            ].includes(_service.factoryStatus)
          ">
          <button nz-button flButton="pretty-minor" nzShape="round" (click)="save()" [disableOnClick]="1000">
            <i nz-icon [nzIconfont]="'icon-baocun'"></i>{{ 'btn.save' | translate }}
          </button>
          <button nz-button flButton="pretty-primary" nzShape="round" (click)="commit()" [disableOnClick]="3000">
            <i nz-icon [nzIconfont]="'icon-tijiao'"></i>{{ 'btn.commit' | translate }}
          </button>
        </ng-container>

        <!--                待初始化/初始化完成-->
        <ng-container *ngIf="[_service.factoryStatusEnum.INIT_PENDING, _service.factoryStatusEnum.READY].includes(_service.factoryStatus)">
          <button
            nz-button
            nzType="primary"
            nzShape="round"
            nzDanger
            (click)="cancellation()"
            [disableOnClick]="1000"
            *ngIf="_listService.btnArr.includes('settings:factoryManage-logout')">
            {{ 'btn.cancellation' | translate }}
          </button>
        </ng-container>

        <!--                待激活/待修改/待部署-->
        <ng-container
          *ngIf="
            !editable &&
            [_service.factoryStatusEnum.Create, _service.factoryStatusEnum.DRAFT, _service.factoryStatusEnum.MODIFY_PENDING].includes(
              _service.factoryStatus
            )
          ">
          <button
            nz-button
            flButton="pretty-primary"
            nzShape="round"
            (click)="edit()"
            [disableOnClick]="1000"
            *ngIf="_listService.btnArr.includes('settings:factoryManage-update')">
            <i nz-icon [nzIconfont]="'icon-caozuolan_bianji1'"></i>{{ 'btn.edit' | translate }}
          </button>
        </ng-container>

        <!--                /待部署-->
        <ng-container *ngIf="!editable && _service.factoryStatus === _service.factoryStatusEnum.DEPLOY_PENDING">
          <button
            nz-button
            flButton="pretty-primary"
            nzShape="round"
            (click)="edit()"
            [disableOnClick]="1000"
            *ngIf="_listService.btnArr.includes('settings:factoryManage-deploy')">
            <i nz-icon [nzIconfont]="'icon-caozuolan_bianji1'"></i>{{ 'btn.deploy' | translate }}
          </button>
        </ng-container>

        <!--                已撤销-->
        <ng-container *ngIf="_service.factoryStatus === _service.factoryStatusEnum.WITHDRAWED">
          <button
            nz-button
            flButton="pretty-primary"
            nzShape="round"
            (click)="activate()"
            [disableOnClick]="1000"
            *ngIf="_listService.btnArr.includes('settings:factoryManage-active')">
            {{ 'btn.activate' | translate }}
          </button>
        </ng-container>
      </div>
    </div>

    <!--        步骤条-->
    <div class="step-container" *ngIf="processLines.length">
      <nz-steps nzType="navigation">
        <ng-container *ngFor="let item of processLines">
          <ng-container *ngIf="item.status !== 'process'">
            <nz-step [nzTitle]="item.name" [nzDescription]="item.gen_time" [nzStatus]="item.status"> </nz-step>
          </ng-container>
          <ng-container *ngIf="item.status === 'process'">
            <nz-step [nzTitle]="item.name" [nzDescription]="item.gen_time" [nzStatus]="item.status" [nzIcon]="iconTemplate"></nz-step>
            <ng-template #iconTemplate>
              <i nz-icon [nzIconfont]="'icon-daibushu'" style="font-size: 32px"></i>
            </ng-template>
          </ng-container>
        </ng-container>
      </nz-steps>
    </div>

    <!--    各种提示-->
    <ng-container *ngIf="!loading">
      <ng-container *ngIf="_service.factoryStatus === _service.factoryStatusEnum.MODIFY_PENDING">
        <div class="result-notice red-notice">
          <i nz-icon [nzIconfont]="'icon-jinggao'"></i>
          {{ 'factory-message.back-reason' | translate }}：{{ _service.factoryForm?.get('reason')?.value }}
        </div>
      </ng-container>
      <ng-container *ngIf="_service.factoryStatus === _service.factoryStatusEnum.WITHDRAWED">
        <div class="result-notice yellow-notice">
          <i nz-icon [nzIconfont]="'icon-jinggao'"></i>
          {{ 'factory-message.undo-notice' | translate }}
        </div>
      </ng-container>
      <ng-container *ngIf="_service.factoryStatus === _service.factoryStatusEnum.CANCELLED">
        <div class="result-notice red-notice">
          <i nz-icon [nzIconfont]="'icon-jinggao'"></i>
          {{ 'factory-message.cancellation-notice' | translate: { time: _service.factoryForm?.get('retain_time')?.value } }}
        </div>
      </ng-container>
    </ng-container>

    <div class="content-container">
      <nz-skeleton [nzLoading]="loading">
        <!--            待激活/待修改-->
        <ng-container
          *ngIf="
            [_service.factoryStatusEnum.Create, _service.factoryStatusEnum.DRAFT, _service.factoryStatusEnum.MODIFY_PENDING].includes(
              _service.factoryStatus
            ) && editable
          ">
          <app-factory-basic-information-edit></app-factory-basic-information-edit>
          <app-factory-mode-edit></app-factory-mode-edit>
        </ng-container>

        <ng-container
          *ngIf="
            ![_service.factoryStatusEnum.Create, _service.factoryStatusEnum.DRAFT, _service.factoryStatusEnum.MODIFY_PENDING].includes(
              _service.factoryStatus
            ) ||
            ([_service.factoryStatusEnum.DRAFT, _service.factoryStatusEnum.MODIFY_PENDING].includes(_service.factoryStatus) && !editable)
          ">
          <app-factory-basic-information-readonly
            [changeStep]="
              _service.factoryStatus === _service.factoryStatusEnum.DEPLOY_PENDING && editable
            "></app-factory-basic-information-readonly>
          <app-factory-mode-readonly
            [changeStep]="_service.factoryStatus === _service.factoryStatusEnum.DEPLOY_PENDING && editable"></app-factory-mode-readonly>
        </ng-container>

        <!--            待部署-->
        <ng-container *ngIf="_service.factoryStatus === _service.factoryStatusEnum.DEPLOY_PENDING && editable">
          <app-deploy-results-edit></app-deploy-results-edit>
        </ng-container>

        <ng-container
          *ngIf="
            [_service.factoryStatusEnum.INIT_PENDING, _service.factoryStatusEnum.READY, _service.factoryStatusEnum.CANCELLED].includes(
              _service.factoryStatus
            ) ||
            (_service.factoryStatus === _service.factoryStatusEnum.DEPLOY_PENDING && !editable)
          ">
          <app-deploy-results-readonly [id]="id"></app-deploy-results-readonly>
        </ng-container>
      </nz-skeleton>
    </div>
  </div>
</ng-container>

<app-undo-modal (handleUndoOk)="handleUndoOk()"></app-undo-modal>
<app-cancellation-modal (handleCancelOk)="handleCancelOk()"></app-cancellation-modal>
<app-return-modal (handleReturnOk)="handleReturnOk($event)"></app-return-modal>

<ng-template #cancelTpl>
  <div style="display: flex; align-items: center; gap: 8px">
    <i nz-icon [nzIconfont]="'icon-jinggao'" style="color: #ff5c33"></i>
    <div style="font-size: 16px; font-weight: 500; color: #222b3c">{{ 'sure-notice.cancel' | translate }}</div>
  </div>
  <div style="display: flex; align-items: center; justify-content: flex-end; margin-top: 24px; gap: 8px">
    <button nz-button nzType="text" nzShape="round" (click)="cancelStop()">{{ 'btn.cancel' | translate }}</button>
    <button nz-button nzType="primary" nzShape="round" (click)="cancelOk()">{{ 'btn.ok' | translate }}</button>
  </div>
</ng-template>
