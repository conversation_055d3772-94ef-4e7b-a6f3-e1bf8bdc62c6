* {
  user-select: none;
}

[row-no-start] {
  display: flex;
  flex-flow: row nowrap;
  justify-content: flex-start;
}

[row-no-center] {
  display: flex;
  flex-flow: row nowrap;
  justify-content: center;
}

[row-center] {
  display: flex;
  justify-content: center;
  align-items: center;
}

.plan-back {
  position: relative;
  height: 100%;
  width: 100%;

  .plan-wrap {
    position: relative;
    width: 100%;
    height: 100%;
    border-top: 1px solid #bcc6d0;
    display: flex;
    flex-direction: column;

    .wrap-top {
      display: flex;
      flex-shrink: 0;
      box-shadow: 0px 2px 4px 0px rgba(207, 213, 217, 0.5);

      .wrap-left-top {
        width: 130px;
        height: 44px;
        background: #f7f9fe;
        border-right: 1px solid #dbe0e6;
        flex-grow: 0;
        flex-shrink: 0;

        .top-left-title-text {
          width: 88px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 14px;
          color: #797e89;
        }
      }

      .wrap-right-top {
        position: relative;
        height: 44px;
        overflow: hidden;
        background: #f7f9fe;
        z-index: 5;
        flex-grow: 1;

        .ward-icon {
          position: absolute;
          color: #54607c;
          font-size: 12px;
          line-height: 44px;
          top: 1px;

          &:hover {
            cursor: pointer;
            color: #2996ff;
          }
        }
      }
    }

    .modal-out-overlay {
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      position: absolute;
      pointer-events: none;
      .mask {
        height: 100%;
        width: 100%;
        background-color: #e6f4ff;
        opacity: 0.6;
        pointer-events: auto;
      }
    }
  }
}

.today-btn-disable {
  cursor: not-allowed;
  background: #dbe2e9;
  border-radius: 9px;
  font-size: 12px;
  font-weight: 500;
  color: #54607c;
  border: none;
  padding: 0px 6px;
  margin: 0px;
  height: 18px;
  position: absolute;
  right: 16px;
  top: 1px;
}

.to-today-btn {
  background: #dbe2e9;
  border-radius: 9px;
  font-size: 12px;
  font-weight: 500;
  color: #54607c;
  border: none;
  padding: 0px 6px;
  margin: 0px;
  height: 18px;
  position: absolute;
  right: 16px;
  top: 1px;
  cursor: pointer;
  &:hover {
    background: #d8ebff;
    color: #2996ff;
  }
}

.wrap-top-scroll {
  width: calc(100% - 10px);
  overflow: hidden;
  position: relative;
}

.wrap-left {
  width: 130px;
  overflow-x: hidden;
  overflow-y: auto;
  box-shadow: 1px 0px 4px 1px rgb(233 238 247 / 50%);
  flex-grow: 0;
  flex-shrink: 0;

  &::-webkit-scrollbar {
    // width: 10px;
    // height: 10px;
    display: none;
  }
}

.wrap-left {
  & > *:nth-child(odd) > ::ng-deep div {
    background: white;
  }

  & > *:nth-child(even) > ::ng-deep div {
    background: #f8fafc;
  }
}

.gantt-header-item {
  flex-shrink: 0;
  font-size: 12px;
  font-weight: 600;
  color: #797e89;

  .gantt-header-item-top {
    border-bottom: 1px solid #dbe0e6;
    height: 22px;
    padding-left: 4px;
  }

  .gantt-header-item-top-hour {
    &:hover {
      background: #d2e4fe;
      color: #218dfe;
      .fold-icon {
        display: block;
        color: #218dfe;
        cursor: pointer;
        line-height: 0px;
        position: relative;
      }
    }
  }

  &:not(:last-child) {
    border-right: 1px solid #bcc6d0;
  }

  .fold-icon {
    display: none;
    font-size: 18px;
  }

  .gantt-header-single {
    text-align: center;
    &:not(:last-child) {
      border-right: 1px solid #eff3ff;
    }
  }

  .gantt-header-single-day {
    &:hover {
      background: #d2e4fe;
      color: #218dfe;
      .fold-icon {
        display: block;
        color: #218dfe;
        cursor: pointer;
        line-height: 0px;
        position: relative;
      }
    }
  }

  .today-title-text {
    color: #2996ff;
  }
}

.wrap-body {
  overflow: scroll;
  // height: calc(100vh - 300px);
}

.wrap-body-inner {
  overflow-x: hidden;
  position: relative;

  & > *:nth-child(odd) > ::ng-deep div {
    background: white;
  }

  & > *:nth-child(even) > ::ng-deep div {
    background: #f2f8ff;
  }
}

.today-triangle {
  border: 7px #2996ff solid;
  width: 0;
  border-right-color: transparent;
  border-left-color: transparent;
  border-bottom: none;
}
