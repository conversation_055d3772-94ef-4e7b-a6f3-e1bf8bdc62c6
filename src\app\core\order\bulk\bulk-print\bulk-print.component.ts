import { Component, EventEmitter, Input, OnChanges, OnInit, Output, Renderer2, SimpleChanges } from '@angular/core';
import { detailModel } from '../models/bulk-detail.interface';
import { HttpClient } from '@angular/common/http';
import { firstValueFrom } from 'rxjs';
import { flatten, groupBy, sortBy, take, uniq, uniqBy } from 'lodash';
import { BulkPrintServiceService } from './bulk-print-service.service';
@Component({
  selector: 'app-bulk-print',
  templateUrl: './bulk-print.component.html',
  styleUrls: ['./bulk-print.component.scss'],
  providers: [BulkPrintServiceService],
})
export class BulkPrintComponent implements OnInit, OnChanges {
  @Input() detail: detailModel | any;
  @Output() bomMaterialDescription = new EventEmitter();
  dataList!: any[];
  sizeDataList!: any[];
  sizeTotal = new Map();
  rawBomColorList: BOMColorItem[] = [];
  summaryPartList: DisplayMaterialPart[] = [];
  ioBasicPicList: { url: string }[] = [];
  translateName = 'bulkPrint.';
  constructor(private _renderer: Renderer2, private _service: BulkPrintServiceService, private _http: HttpClient) {}

  ngOnInit(): void {}
  ngOnChanges(changes: SimpleChanges) {
    this.rawBomColorList = [];
    this.summaryPartList = [];
    if (changes.detail && changes.detail.currentValue) {
      if (this.detail.io_basic.order_status === 0) return;
      this.ioBasicPicList = take(this.detail.io_basic.pictures, 3);
      this.dataList = [];
      this.sizeDataList = [];
      this.sizeTotal = new Map();
      const colorAndPo_unique_codeList = new Set<string>();
      const totalList: any[] = [];
      this.detail.pos.forEach((data: any) => {
        data.po_lines.forEach((item: any) => {
          this.sizeTotal.set(item.size_info.spec_id, (this.sizeTotal.get(item.size_info.spec_id) || 0) + item.qty);
          this.sizeTotal.set('total', (this.sizeTotal.get('total') || 0) + item.qty);
          this.sizeDataList.push(item.size_info);
          totalList.push({
            ...item,
            ...item.color_info,
            ...item.size_info,
            po_unique_code: data.po_basic.po_unique_code,
            po_code: data.po_basic.po_code,
            due_time: data.po_basic.due_time,
          });
          colorAndPo_unique_codeList.add(item.color_info.color_id + item.color_info.color_code + data.po_basic.po_unique_code);
        });
      });
      // lodash根据spec_id去重
      this.sizeDataList = uniqBy(this.sizeDataList, 'spec_id');
      [...colorAndPo_unique_codeList].forEach((key: string) => {
        const poColorList = totalList.filter((item: any) => key === item.color_id + item.color_code + item.po_unique_code);
        const poLine = {
          ...poColorList[0],
          totalQty: poColorList.reduce((total: number, item: any) => total + item.qty, 0),
        };
        poColorList.forEach((poData: any) => {
          poLine[poData.spec_id] = poData.qty;
        });
        this.dataList.push(poLine);
      });

      if (this.detail.pos.length > 0) {
        this._service.getBomDetail(this.detail.io_basic.io_uuid).subscribe((res) => {
          if (res.code === 200) {
            this.rawBomColorList = Object.values(res.data);
            if (this.rawBomColorList.length > 0) {
              this.transformBomDetail();
            }
          }
        });
      }
    }
  }
  transformBomDetail() {
    const materialPartList: SingleMaterialLineWithColors[] = [];
    const materialNameList: string[] = [];
    const materialGroupUniqueCode: string[] = [];
    this.rawBomColorList.forEach((orderColor) => {
      const targetOrderColorList = this.dataList.filter((po) => po.color_id === orderColor.order_color_id);
      targetOrderColorList.forEach((targetOrderColor) => {
        targetOrderColor.fabricColor = orderColor.fabric;
        orderColor.fabric.forEach((fabric) => {
          const materialInfoList: string[] = [];
          if (!materialGroupUniqueCode.includes(fabric.group_unique_code)) {
            if (fabric.parts?.length) {
              materialInfoList.push(fabric.parts.map((item) => item.part_name).join('、'));
            }
            if (fabric.material_color_name?.length) {
              materialInfoList.push(fabric.material_color_name);
            }
            if (fabric.supplier_name?.length) {
              materialInfoList.push(fabric.supplier_name);
            }
            if (fabric.weight) {
              materialInfoList.push(fabric.weight.toString());
            }
            if (fabric.remark?.length) {
              materialInfoList.push(fabric.remark);
            }
            const infoString = (fabric.material_name ?? '-') + ':' + materialInfoList.join(' ');
            materialNameList.push(infoString);
            materialGroupUniqueCode.push(fabric.group_unique_code);
          }
          if (fabric.parts.length === 0) return;
          fabric.parts = sortBy(fabric.parts, 'part_id');
          const sameMaterialPart = materialPartList.find(
            (item) => item.material_id === fabric.material_id && item.partIds === fabric.parts.map((item) => item.part_id).join('-')
          );
          if (sameMaterialPart) {
            sameMaterialPart.color_ids.add(fabric.material_color_id);
          } else {
            materialPartList.push({
              partIds: fabric.parts.map((item) => item.part_id).join('-'),
              partList: fabric.parts,
              material_id: fabric.material_id,
              material_code: fabric.material_code,
              color_ids: new Set([fabric.material_color_id]),
            });
          }
        });
      });
    });
    if (materialNameList.length) {
      const value = materialNameList.join('。');
      this.bomMaterialDescription.emit(value);
      this.detail.io_basic.fabric = value;
    }
    const groupedByMaterial = groupBy(materialPartList, 'material_id');
    const groupedBySamePart = new Map<
      string,
      {
        materialInfo: { material_id: number; material_code: string; color_ids: Set<number> }[];
        partInfo: { part_id: number; part_name: string }[];
      }
    >();
    Object.values(groupedByMaterial).forEach((value) => {
      value.forEach((item) => {
        item.partList = sortBy(item.partList, 'part_id');
        const targetParts = groupedBySamePart.get(item.partIds);
        if (targetParts) {
          targetParts.materialInfo.push({
            material_id: value[0].material_id,
            material_code: value[0].material_code,
            color_ids: item.color_ids,
          });
        } else {
          groupedBySamePart.set(item.partIds, {
            materialInfo: [
              {
                material_id: value[0].material_id,
                material_code: value[0].material_code,
                color_ids: item.color_ids,
              },
            ],
            partInfo: item.partList,
          });
        }
      });
    });
    this.summaryPartList = [...groupedBySamePart.values()].map((item) => {
      return {
        displayMaterialId: new Set(item.materialInfo.map((item) => item.material_id)),
        partList: item.partInfo,
        displayPartName: uniq(item.partInfo.map((item) => item.part_name)).join('/'),
        displayPartId: new Set(item.partInfo.map((item) => item.part_id)),
        colorIds: flatten(item.materialInfo.map((item) => [...item.color_ids])),
      };
    });
    this.dataList.forEach((po) => {
      const shadowList: DisplayMaterialPartColor[] = [];
      this.summaryPartList.forEach((part) => {
        const materialPartList: MaterialColorItem[] = po.fabricColor?.filter(
          (item: MaterialColorItem) =>
            part.displayMaterialId.has(item.material_id) &&
            part.colorIds.includes(item.material_color_id) &&
            item.parts.length === part.partList.length &&
            item.parts.every((partItem) => part.displayPartId.has(partItem.part_id))
        );
        if (materialPartList?.length > 0) {
          shadowList.push({
            displayPartName: part.displayPartName,
            material_color_list: materialPartList.map((item) => ({
              material_id: item.material_id,
              material_code: item.material_code,
              color_id: item.material_color_id,
              color_name: item.material_color_name,
            })),
          });
        } else {
          shadowList.push({ displayPartName: null, material_color_list: [] });
        }
      });
      po.partList = shadowList;
    });
  }

  departmentName = '';
  logoUrl = '';
  async onPrint() {
    const getLogs = () => {
      return this._http.get('/department/-1');
    };
    const res: any = await firstValueFrom(getLogs());
    if (res.code === 200) {
      this.departmentName = res.data.name;
      this.logoUrl = res.data.logos?.[0]?.url;
    }

    setTimeout(() => {
      const printEle = (document as any).getElementById('print').cloneNode(true);
      this._renderer.setStyle(printEle, 'display', 'block');
      this._renderer.setStyle(document.body, 'display', 'none');
      this._renderer.appendChild(document.body.parentElement, printEle);

      setTimeout(() => {
        window.print();
        setTimeout(() => {
          this._renderer.removeChild(document.body.parentElement, printEle);
          this._renderer.setStyle(printEle, 'display', 'none');
          this._renderer.setStyle(document.body, 'display', 'block');
          setTimeout(() => {
            (document as any).getElementsByClassName('ant-layout-content')[0]?.scrollTo({ top: 0 });
          }, 200);
        }, 160);
      }, 500);
    }, 100);
  }
}
interface BOMColorItem {
  order_color_id: number;
  order_color_code: string;
  order_color_name: string;
  fabric: MaterialColorItem[];
  accessory: MaterialColorItem[];
}

interface MaterialColorItem {
  material_id: number;
  material_code: string;
  material_name: string;
  fabric_type: number | null;
  fabric_type_value: string;
  material_color_group_id: number;
  material_color_group_name: string;
  material_color_id: number;
  material_color_name: string;
  cylinder_number: string;
  supplier_id: number | null;
  supplier_name: string;
  supplier_color_code: string;
  supplier_art_num: string;
  weight: number | null;
  width: number | null;
  composition: string;
  specifications: string;
  specification_id: number | null;
  loss: number;
  unit_consumption: number | null;
  unit_id: number;
  unit_name: string;
  remark: string;
  purchase_type: string;
  parts: Part[];
  sizes: Size[];
  source_type: string;
  origin_source_type: string;
  group_unique_code: string;
}

interface Part {
  part_id: number;
  part_name: string;
  color_id?: number;
  color_name?: string;
  material_code?: string;
}

interface Size {
  size_id: number;
  size_code: string;
  size_name: string;
}

interface SingleMaterialLineWithColors {
  partIds: string;
  partList: { part_id: number; part_name: string }[];
  material_id: number;
  material_code: string;
  color_ids: Set<number>;
}
interface DisplayMaterialPart {
  partList: { part_id: number; part_name: string }[];
  displayMaterialId: Set<number>;
  displayPartName: string;
  displayPartId: Set<number>;
  colorIds: number[];
}
interface DisplayMaterialPartColor {
  displayPartName: string | null;
  material_color_list: { material_id: number; material_code: string; color_id: number; color_name: string }[];
}
