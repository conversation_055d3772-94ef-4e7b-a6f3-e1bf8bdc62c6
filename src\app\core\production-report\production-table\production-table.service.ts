import { HttpClient } from '@angular/common/http';
import { EventEmitter, Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { FlcSpUtilService } from 'fl-common-lib';

@Injectable({
  providedIn: 'root',
})
export class ProductionTableService {
  public serviceUrl = '/service/procurement-inventory/prod_status/v1/prod_status';

  public eventEmitter = new EventEmitter();
  public translateEventEmitter = new EventEmitter<void>();
  btnArr: any[] = []; // 按钮权限
  fieldArr: any[] = [];
  constructor(private http: HttpClient, private _translate: TranslateService, private _spUtil: FlcSpUtilService) {}

  // 生产进度报表
  getProductionTableList(data: any) {
    return this.http.post<any>(`${this.serviceUrl}/list`, data);
  }
  // 生产进度报表更新个别项
  updateProduction(data: any) {
    return this.http.post<any>(`${this.serviceUrl}/node/update`, data);
  }

  // 生产进度报表options
  productionOptions() {
    return this.http.post<any>(`${this.serviceUrl}/options`, {});
  }

  translateValue(key: string, param?: any): string {
    return this._translate.instant(key, param);
  }
  // 获取对应加工厂获取的订单颜色/尺码/qty详情
  getFactoryColorSizeQty(data: any) {
    return this.http.post<any>(`${this.serviceUrl}/order_info`, data);
  }
  // 生产进度报表更新状态
  updateStatus(data: any) {
    return this.http.post<any>(`${this.serviceUrl}/update_status`, data);
  }
  // 生产进度报表卡片信息
  getNodeDetail(data: any) {
    return this.http.post<any>(`${this.serviceUrl}/bulk_node_detail`, data);
  }
  getNodeInfo(data: { bulk_order_id: number; node_id: number; node_type: number; factory_out_sourcing_id: number }) {
    return this.http.post<any>(`${this.serviceUrl}/node_detail`, data);
  }
  updateNode(data: any) {
    return this.http.post<any>(`${this.serviceUrl}/node/update`, data);
  }
  getNodeHistory(data: { node_id: number; color_id?: number; spec_id?: number; node_type: number; po_unique_code?: string }) {
    return this.http.post<any>(`${this.serviceUrl}/node_history`, data);
  }
  updateNodeHistory(data: any) {
    return this.http.post<any>(`${this.serviceUrl}/update_history`, data);
  }

  public serviceUrl2 = '/service/procurement-inventory/prod_status/v1/event';
  // 事件汇报-结算调整项
  getEventReportOptionsList() {
    return this.http.get<any>(`${this.serviceUrl2}/pull`);
  }
  // 事件汇报-详情
  getEventReportDetail(id: any) {
    return this.http.get<any>(`${this.serviceUrl2}/detail`, { params: { id: id } });
  }
  // 事件汇报-更新
  eventReportUpdate(payload: any) {
    return this.http.post<any>(`${this.serviceUrl2}/edit`, payload);
  }
  /**********************************************/
  // 生产进度报表-跟踪状态统计
  getTrackStatusStatistics(payload: any) {
    return this.http.post<any>(`${this.serviceUrl}/trackStatus/statistics`, payload);
  }

  // 生产进度报表-批量编辑字段列表（下拉）
  getUpdateBatchFieldList(payload: any) {
    return this.http.post<any>(`${this.serviceUrl}/updateBatch/FieldList`, payload);
  }

  // 生产进度报表-批量编辑
  updateBatchFieldOrNode(payload: any) {
    return this.http.post<any>(`${this.serviceUrl}/updateBatch`, payload);
  }

  // 生产进度报表-编辑(本次新加的字段)
  updateItem(payload: any) {
    return this.http.post<any>(`${this.serviceUrl}/update`, payload);
  }

  // 生产进度报表-结束跟踪
  stopTrack(payload: any) {
    return this.http.post<any>(`${this.serviceUrl}/stop`, payload);
  }

  // 生产进度报表-恢复跟踪
  restoreTrack(payload: any) {
    return this.http.post<any>(`${this.serviceUrl}/restore`, payload);
  }

  //生产进度报表-批量导出
  export(payload: any) {
    return this.http.post<any>(`${this.serviceUrl}/list/export`, payload);
  }

  // 模版-进度节点-模版字段列表
  getNodeTemplateFieldList(payload: any) {
    return this.http.post<any>('/service/procurement-inventory/prod_status/v1/template/node/template/field/List', payload);
  }

  // 生产进度跟踪列表-预警未读消息统计
  getMessageCount(payload: any) {
    return this.http.post<any>('/service/procurement-inventory/warning/v1/prod-status-message-count', payload);
  }

  // 生产进度跟踪列表-预警待处理消息-列表
  getMessageList(payload: any) {
    return this.http.post<any>('/service/procurement-inventory/warning/v1/prod-status-message-list', payload);
  }

  // 生产进度跟踪列表-预警消息已读
  setMessageRead(payload: any) {
    return this.http.post<any>('/service/procurement-inventory/warning/v1/prod-status-message-read', payload);
  }
}
