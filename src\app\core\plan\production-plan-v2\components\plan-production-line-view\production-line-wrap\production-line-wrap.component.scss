.wrap {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  border-bottom: #bcc6d0 solid 1px;
  border-right: #dbe0e6 solid 1px;
  position: relative;
  &.isHeightLight {
    background-color: #eaf3ff !important;
  }
  &.isSelected {
    background-color: #eaf3ff !important;
  }

  > div {
    display: inline-block;
    text-align: center;
    box-sizing: border-box;
    line-height: 52px;
  }

  .checkbox {
    width: 36px;
    border-left: 1px solid #dbe0e6;
    border-right: 1px solid #dbe0e6;
  }
  .factory-name {
    position: relative;
    width: 120px;
    border-right: 1px solid #dbe0e6;
  }
  .line-name {
    width: 82px;
  }
}
:host ::ng-deep .checkbox {
  width: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  height: 100%;

  .ant-checkbox-disabled .ant-checkbox-inner {
    background-color: #f5f5f5 !important;
    border-color: #d9d9d9 !important;
  }
}

.un-publish {
  position: absolute;
  top: 0;
  right: 0;
  background: #ffe2c0;
  border-radius: 0px 0px 0px 4px;
  font-size: 10px;
  font-weight: 500;
  color: #f3710f;
  line-height: 14px;
}

.editing-tag {
  position: absolute;
  top: 0;
  left: 0;
  background: #ffe2c0;
  border-radius: 0px 0px 0px 4px;
  font-size: 10px;
  font-weight: 500;
  color: #f3710f;
  line-height: 14px;
}
