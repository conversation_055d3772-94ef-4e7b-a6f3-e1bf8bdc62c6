/* To learn more about this file see: https://angular.io/config/tsconfig. */
{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    "outDir": "./out-tsc/app",
    "types": [],
    "paths": {
      "fl-sewsmart-lib/*": [
        "./src/fl-sewsmart-lib/*"
      ],
      "fl-common-lib": [
        "./src/fl-common-lib"
      ],
      "fl-sewsmart-lib/common-services": [
        "./src/fl-sewsmart-lib/common-services"
      ]
    }
  },
  "files": [
    "src/main.ts",
    "src/polyfills.ts"
  ],
  "include": [
    "src/**/*.d.ts"
  ]
}