<ng-container>
  <nz-select
    [nzMode]="cpnMode"
    [(ngModel)]="selectVlaue"
    [nzDisabled]="disabled"
    [nzShowArrow]="true"
    (nzScrollToBottom)="loadMore()"
    [nzPlaceHolder]="placeSelect"
    [nzDropdownMatchSelectWidth]="false"
    [nzDropdownRender]="renderTemplate"
    [nzOptionOverflowSize]="cpnOptionOverflowSize"
    (ngModelChange)="onSearch($event)"
    (nzOnSearch)="onSelectSearch($event)"
    (nzOpenChange)="openChange($event)"
    (nzBlur)="onTouch()"
    nzServerSearch
    nzShowSearch
    nzAllowClear
    class="dy-select">
    <ng-container *ngFor="let item of selectOptions">
      <nz-option [nzValue]="item.value" [nzLabel]="item.label" nzCustomContent [nzHide]="item.hide">
        <div class="dy-option">{{ item.label }}</div>
      </nz-option>
    </ng-container>
    <nz-option *ngIf="!defaultValue" [nzLabel]="selectVlaue" [nzValue]="selectVlaue" nzHide></nz-option>
    <nz-option *ngIf="defaultValue" [nzLabel]="defaultValue.label" [nzValue]="defaultValue.value" nzHide></nz-option>
  </nz-select>
  <ng-template #renderTemplate>
    <nz-spin *ngIf="isSelectLoading"></nz-spin>
  </ng-template>
</ng-container>
