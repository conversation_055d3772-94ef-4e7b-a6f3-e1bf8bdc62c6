// 选择的批量编辑字段的类型
export enum BatchEditTypeEnum {
  field = 1,
  node = 2,
}

export enum EventTypeEnum {
  refreshList,
  resetBatchField,
}

// 字段类型枚举
export enum FieldTypeEnum {
  Date = 1,
  Number,
  TextBox,
  SingleChoice,
  MultipleChoice,
  Percentage,
  Attachment,
}

// 字段标签枚举
export enum FieldLabelEnum {
  Null = 1,
  Plan = 2,
  Actual = 3,
}

// 节点权限
export enum NodeEditAuthEnum {
  review = 1, // 仅查看
  edit = 2, // 可编辑
}

// 字段权限
export enum FieldAuthEnum {
  review = 1, // 仅查看
  edit = 2, // 可编辑
}

// 字段是否可编辑(自动取值)
export enum FieldEditAuthEnum {
  edit = 1,
  read = 2,
}

// 节点权限维度
export enum AuthorityTypeEnum {
  node = 1,
  field = 2,
}

// 跟踪状态
export enum TrackStatusEnum {
  // 1=进行中，2=结束跟踪
  in_progress = 1,
  end_tracking = 2,
}

// 模版类型
export enum TemplateTypeEnum {
  specialTpl, // 未改造的写死的模版
  standardTpl, // 之前内置的两个标准模版 + 自定义模版
  qualityTpl, // 固定特殊模板-质量模版
}
