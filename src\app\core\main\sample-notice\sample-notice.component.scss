::ng-deep {
  .drawer-bottom-wrap {
    .sample-notice {
      height: 100%;
      padding: 8px 0px;
      display: flex;
      flex-direction: column;

      .flc-outer-container .status-button-item:nth-child(4) {
        display: none !important;
      }

      th {
        font-size: 14px;
        color: #54607c;
        font-weight: 500;
      }

      td {
        font-weight: 500;
        font-size: 14px;
        color: #222b3c;
      }

      .ant-table-thead
        > tr
        > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
        content: none;
      }

      // 表格自适应显示滚动条
      nz-spin,
      nz-table,
      .ant-spin-container,
      nz-table-inner-scroll > .ant-table-content,
      nz-table-inner-scroll {
        height: 100%;

        .ant-table.ant-table-fixed-header {
          // 36px => 页码高度+上边距
          max-height: calc(100% - 36px);
        }

        .ant-table-body {
          max-height: calc(100% - 32px) !important;
        }
      }
      // 表格自适应显示滚动条
      .ant-spin-container {
        display: flex;
        flex-direction: column;
      }
    }
  }
}
