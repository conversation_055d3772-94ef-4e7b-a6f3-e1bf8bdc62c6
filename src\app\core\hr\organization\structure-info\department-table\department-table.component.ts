import { Component, OnInit, ViewChild, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzResizeEvent } from 'ng-zorro-antd/resizable';
import { NzUploadFile, NzUploadXHRArgs } from 'ng-zorro-antd/upload';
import { Observable, Observer } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { TableHeaderConfig, TableHelperService } from 'src/app/services/table-helper/table-helper.service';
import { DeleteModalComponent } from '../../delete-modal/delete-modal.component';
import { EmployeeDrawerComponent } from '../../employee-drawer/employee-drawer.component';
import { EmployeeListData, TreeOptions } from '../../interface/structure-data';
import { EmployeeListDefaultHeaders } from '../../interface/sturcture-config';
import { OrganizationService } from '../../organization.service';

const version = '1.0.0';
type lineItem = TableHeaderConfig<EmployeeListData>;
const defaultHeaders = EmployeeListDefaultHeaders;
@Component({
  selector: 'app-department-table',
  templateUrl: './department-table.component.html',
  styleUrls: ['./department-table.component.scss'],
})
export class DepartmentTableComponent implements OnInit {
  @ViewChild(EmployeeDrawerComponent) employeeDrawer: any;
  @ViewChild(DeleteModalComponent) deleteModal: any;
  @Input() tableMaxTable = 200;
  @Input() searchData: any;
  headers: lineItem[] = [];
  renderHeaders: lineItem[] = [];
  fileList: any[] = [];
  btnHighLight = false;
  lang = localStorage.getItem('lang') || 'zh';

  constructor(
    private _tableHelper: TableHelperService,
    private _translate: TranslateService,
    public _service: OrganizationService,
    private _msg: NzMessageService,
    private _notice: NzNotificationService
  ) {}

  ngOnInit(): void {
    this.headers = this._tableHelper.getTableHeaderConfig<lineItem>(defaultHeaders, version);
    this.getRenderHeaders();
  }

  onResize({ width }: NzResizeEvent, col: string): void {
    this.headers = this._tableHelper.tableResize<lineItem>(width, col, this.headers, version);
    this.getRenderHeaders();
  }

  getRenderHeaders() {
    this.renderHeaders = this._tableHelper.getRenderHeader<lineItem>(this.headers);
  }

  changeHeader(event: MouseEvent): void {
    this.btnHighLight = true;
    const shadow = JSON.parse(JSON.stringify(this.headers));
    for (const item of shadow) {
      this._translate
        .get('employee-drawer.' + item.label)
        .subscribe((res: string) => {
          item.label = res;
        })
        .unsubscribe();
    }
    this._tableHelper
      .openTableHeaderMidifyDialog<lineItem>(shadow, event.target as HTMLElement)
      .pipe(finalize(() => (this.btnHighLight = false)))
      .subscribe((res) => {
        if (res) {
          for (const item of res) {
            item.label = defaultHeaders.find((i) => i.key === item.key)?.label ?? '';
          }
          this.headers = res;
          this.getRenderHeaders();
          this._tableHelper.saveTableHeaderConfig(this.headers, version);
        }
      });
  }

  checkDeptDataAuth() {
    this._service.checkDeptDataAuth();
  }

  /**
   * 编辑员工信息
   * @param data：员工原始数据
   */
  editEmployee(data: any) {
    this._service.addedEmployee = null;
    this.employeeDrawer.isAdd = false;
    this.employeeDrawer.initForm(data);
    this.employeeDrawer.createEmployeeDrawer();
  }

  /**
   * 新增员工
   */
  addEmployee() {
    if (this._service.checkDeptDataAuth()) {
      this._service.addedEmployee = null;
      this.employeeDrawer.isAdd = true;
      this.employeeDrawer.initForm();
      this.employeeDrawer.createEmployeeDrawer();
    }
  }

  /**
   * 删除员工
   * @param data：员工数据
   */
  deleteEmployee(data: any) {
    this._service.addedEmployee = null;
    this.deleteModal.is_employee = true;
    this.deleteModal.data = data ?? {};
    this.deleteModal.createDeleteModal();
  }

  /**
   * 确认删除员工后回调，重新获取遍员工列表，刷新架构树
   * @param data
   */
  handleDelete(data: any) {
    const dept_id = this._service.departForm.get('id')?.value;
    this._service.deleteEmployee({ id: data?.id ?? null }).subscribe((res) => {
      if (res.code === 200) {
        const delete_msg = this._translate.instant('success.delete');
        this._msg.success(delete_msg);
        if (this._service.tableData.length === 1) {
          this._service.pageIndex = this._service.pageIndex - 1 < 1 ? 1 : this._service.pageIndex;
        }
        this._service.getEmployeeList(dept_id);
        this.updateTree(dept_id);
        this.deleteModal.closeDeleteModal();
      }
    });
  }

  /**
   *
   * @param file 导入前判断文件大小与格式
   * @param _fileList
   * @returns
   */
  beforeUpload = (file: NzUploadFile, _fileList: NzUploadFile[]): Observable<boolean> =>
    new Observable((observer: Observer<boolean>) => {
      this._service.addedEmployee = null;
      console.log(_fileList);
      this.fileList = _fileList;
      const isFile =
        file.type === 'application/vnd.ms-excel' ||
        file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.type === '';
      if (!isFile) {
        this._msg.error(this._translate.instant('tips.只能xls，xlsx格式文件'));
        observer.complete();
        return;
      }
      // const isLt2M = (file.size as number) / 1024 / 1024 < 2;
      // if (!isLt2M) {
      //   this._msg.error('文件大小必须小于2MB!');
      //   observer.complete();
      //   return;
      // }
      observer.next(isFile);
      observer.complete();
    });

  /**
   * 导入员工文件
   * @param item
   * @returns
   */
  handleUpload = (item: NzUploadXHRArgs) => {
    console.log(item);
    const formData = new FormData();
    this.fileList.forEach((file: any) => {
      formData.append('file', file);
    });
    const dept_id = this._service.departForm.get('id')?.value;
    formData.append('dept_id', String(dept_id));
    return this._service
      .employeeImport('/employee/batch', formData)
      .pipe(
        finalize(() => {
          this._service.getEmployeeList(dept_id, true);
          this.updateTree();
          this.fileList = [];
        })
      )
      .subscribe((res: any) => {
        if (res.code === 200) {
          const import_msg = this._translate.instant('success.import');
          this._msg.success(import_msg);
        } else {
          this._notice.error('错误', res.message);
        }
      });
  };

  /**
   * 更新架构树的数据、选中节点、展开节点
   * @param depart：当前展示的部门或公司id
   */
  updateTree(depart: any = null) {
    const depart_id = depart ? depart : this._service.departForm.get('id')?.value;
    const expandedNode = [...this._service.expandedTree] ?? [];
    this._service.getTree().then((datas: TreeOptions[]) => {
      this._service.selectNode(depart_id);
      const line = datas?.find((item) => item.id === depart_id && !item.is_employee);
      this._service.expandedTree = expandedNode?.length ? expandedNode : [...(line?.expanded_list as any[]), depart_id] ?? [];
    });
  }
}
