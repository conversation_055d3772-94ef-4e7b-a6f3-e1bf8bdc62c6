'use strict';

const os = require('os');

if (os.type() == 'Windows_NT') {
  //windows
  clearLibWindows();
} else if (os.type() == 'Darwin') {
  //mac
  clearLibLinux();
} else if (os.type() == 'Linux') {
  //Linux
  clearLibLinux();
} else {
  console.log('=== os system is not supported ===');
}

function clearLibWindows() {
  let exec = require('child_process').execSync;
  // 清除软连接
  exec('rmdir /s /q src\\fl-sewsmart-lib', { stdio: 'inherit' });
  exec('rmdir /s /q src\\fl-common-lib', { stdio: 'inherit' });
}

function clearLibLinux() {
  let exec = require('child_process').execSync;
  // 清除软连接
  exec('rm -rf src/fl-sewsmart-lib', { stdio: 'inherit' });
  exec('rm -rf src/fl-common-lib', { stdio: 'inherit' });
}
