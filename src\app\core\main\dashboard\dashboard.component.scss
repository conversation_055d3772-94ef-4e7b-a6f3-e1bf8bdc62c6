@use 'sass:color';
:host {
  ::ng-deep {
    .ant-tabs > .ant-tabs-nav .ant-tabs-nav-operations {
      display: none;
    }
    .ant-tabs-ink-bar {
      background-color: var(--fl-pretty-color);
    }
    .ant-tabs-tab:hover {
      color: var(--fl-pretty-color);
    }
    .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
      color: var(--fl-pretty-color);
    }
    .ant-tabs-top > .ant-tabs-nav {
      margin: 0;
      &::before {
        border-bottom: unset;
      }
    }
  }
  #mainBoard {
    display: flex;
    height: calc(100vh - 48px - 16px);
    overflow: hidden;
    column-gap: 8px;
  }
  #menuArea {
    flex: 768;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  #firstMenuBar {
    flex-shrink: 0;
    background-color: white;
    border-radius: 4px;
    padding: 12px 16px;
    padding-bottom: 0;
  }
  #notificationArea {
    flex: 400;
    flex-shrink: 0;
    background-color: white;
    border-radius: 4px;
    overflow: auto;
    padding: 12px 16px;
  }
  .firstLevelTitle {
    font-weight: 500;
    font-size: 16px;
    color: #222b3c;
    line-height: 22px;
    text-align: left;
    font-style: normal;
  }
  #secondMenuBoard {
    margin: 8px 0;
    display: flex;
    flex-direction: column;
    row-gap: 8px;
    flex: 1;
    overflow-x: auto;
  }
  .secondMenuArea {
    background: white;
    border-radius: 8px;
    padding: 16px 12px;
  }
  .secondGroupTitle {
    font-size: 14px;
    color: #222b3c;
    font-weight: 500;
    margin-bottom: 16px;
  }
  .secondMenuChildrenArea {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    column-gap: 32px;
    row-gap: 16px;
  }
  .secondMenuItem {
    height: 48px;
    color: #515665;
    font-size: 14px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    transition: all 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
    &:hover {
      background-color: var(--fl-pretty-color-bg);
      color: var(--fl-pretty-color);
      .iconArea {
        background: var(--fl-pretty-color-bg);
        color: var(--fl-pretty-color);
      }
    }
    .iconArea {
      height: 48px;
      width: 48px;
      display: flex;
      font-size: 18px;
      align-items: center;
      justify-content: center;
      // background: #f5ffff;
      // color: #4dbfbf;
      background: color-mix(in srgb, var(--fl-pretty-color-bg), #ffffff22);
      color: var(--fl-pretty-color);
      border-radius: 8px;
      transition: all 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
      flex-shrink: 0;
    }
    span {
      flex: 1;
      text-align: center;
    }
  }
  .messageBoard {
    height: calc(100vh - 48px - 16px - 24px - 46px);
    overflow-y: auto;
    padding: 10px 0;
  }
  .messageLine {
    height: 36px;
    border-radius: 4px;
    padding: 0 8px;
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #54607c;
    font-weight: 500;
    transition: all 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
    &:hover,
    &:active {
      background-color: var(--fl-pretty-color-bg) !important;
    }
    &:nth-of-type(2n) {
      background: #fafbfd;
    }
    > .module {
      font-size: 12px;
      padding: 2px 4px;
      border-radius: 2px;
      background-color: #f0f2f5;
    }
    > .order {
      margin: 0 12px;
      color: #222b3c;
    }
    > .other {
      margin-left: 12px;
    }
    &.isRead {
      color: #b5b8bf !important;
    }
  }
}
