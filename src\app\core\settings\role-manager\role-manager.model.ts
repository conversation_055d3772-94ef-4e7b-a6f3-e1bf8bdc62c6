export interface RoleLogModel {
  gen_time: Date;
  operation: '新增' | '修改' | '删除';
  role_name: string;
  gen_user: string;
}
export interface RoleNameModel {
  id?: number;
  code: string;
  name: string;
  status: boolean;
}
export interface RoleNameListSearchOptionModel<T> {
  column: T;
  value?: string;
  page: number;
  limit: number;
}

export interface RoleNameListModel {
  code: string;
  id: number;
  name: string;
  status: boolean;
  gen_user_name: string;
  gen_time: Date;
  modified_time: Date;
  gen_user: string;
}
export interface RoleNameItemModel {
  active: boolean;
  children: RoleNameChildModel[];
  code: string;
  description: string;
  gen_dept_id: number;
  gen_time: Date;
  gen_user: string;
  gen_user_id: number;
  id: number;
  modified_time: string;
  name: string;
  status?: boolean;
}
export interface RoleNameChildModel {
  action_type: 'write' | 'read';
  children?: RoleNameChildModel[];
  isLastMenu: boolean | null;
  hasSelectedMenu: boolean;
  isEmptyWrite?: boolean;
  code: string;
  name: string;
  payload: {
    data_type: 1 | 2 | 3 | 4; // 1: 全部 2:本人 3:部门/下级 4:自定义
    data_range: number[]; // 自定义才会加入的 部门的id
    visual_type?: 1 | 3; // 1-基础信息 2-价格信息 3-全选
  };
  type: 'menu' | 'btn';
  value: boolean;
  visible: boolean;
  isHideForRangeMode: boolean;
  fieldList?: FieldPermissionModel[] | null;
  moduleList?: any[] | null;
}

export interface FieldPermissionModel {
  id: number | null;
  resourceId: number | null;
  roleId: number | null;
  fieldRelId?: number | null;
  tableName: string;
  fieldName: string;
  viewPermission: number | null; // 1不允许 2允许
  editPermission: number | null; // 1不允许 2允许
  write_value?: boolean;
  read_value?: boolean;
}
