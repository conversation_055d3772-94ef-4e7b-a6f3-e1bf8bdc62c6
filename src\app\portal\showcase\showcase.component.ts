import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { NzResizeEvent } from 'ng-zorro-antd/resizable';

@Component({
  selector: 'app-showcase',
  templateUrl: './showcase.component.html',
  styleUrls: ['./showcase.component.scss'],
})
export class ShowcaseComponent implements OnInit {
  constructor(public _translateService: TranslateService) {}

  testLabel1: any;
  testLabel2: any;

  ngOnInit(): void {}

  ngAfterViewInit() {
    setTimeout(() => {
      this.testLabel1 = this._translateService.instant('email');
      this.testLabel2 = this._translateService.instant('showcase');
    });
  }

  click() {
    console.log('awfhaisohfioawfhoa');
  }

  cols = [
    {
      title: 'Name',
      width: '180px',
    },
    {
      title: 'Age',
      width: '180px',
    },
    {
      title: 'Address0',
      width: '300px',
    },
    {
      title: 'Address1',
      width: '300px',
    },
    {
      title: 'Address2',
      width: '300px',
    },
    {
      title: 'Address3',
      width: '300px',
    },
    {
      title: 'Actions',
      width: '100px',
    },
  ];

  listOfData = [
    {
      key: '1',
      name: 'John Brown',
      age: 32,
      address0: 'New York No. 1 Lake Park',
      address1: 'New York No. 1 Lake Park',
      address2: 'New York No. 1 Lake Park',
      address3: 'New York No. 1 Lake Park',
    },
    {
      key: '2',
      name: 'Jim Green',
      age: 42,
      address0: 'London No. 1 Lake Park',
      address1: 'London No. 1 Lake Park',
      address2: 'London No. 1 Lake Park',
      address3: 'London No. 1 Lake Park',
    },
    {
      key: '3',
      name: 'Joe Black',
      age: 32,
      address0: 'Sidney No. 1 Lake Park',
      address1: 'Sidney No. 1 Lake Park',
      address2: 'Sidney No. 1 Lake Park',
      address3: 'Sidney No. 1 Lake Park',
    },
  ];
  onResize({ width }: NzResizeEvent, col: string): void {
    this.cols = this.cols.map((e) => (e.title === col ? { ...e, width: `${width}px` } : e));
  }
}
