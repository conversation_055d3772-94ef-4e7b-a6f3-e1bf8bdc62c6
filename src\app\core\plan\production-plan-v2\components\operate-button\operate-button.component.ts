import { Component, EventEmitter, Input, OnInit, Output, ViewChildren } from '@angular/core';
import { PlanOperateBtnEnum } from '../../model/production-plan.enum';
import { ProductionPlanShareService } from '../../production-plan-share.service';
import { Subscription } from 'rxjs';
import { NzToolTipComponent } from 'ng-zorro-antd/tooltip';
import { ProductionPlanV2Service } from '../../production-plan-v2.service';

@Component({
  selector: 'app-operate-button',
  templateUrl: './operate-button.component.html',
  styleUrls: ['./operate-button.component.scss'],
})
export class OperateButtonComponent implements OnInit {
  @Input() isEdit = false;
  @Input() isHold = false;
  @Input() allocateNum = 0;
  @ViewChildren(NzToolTipComponent) tooltipRef?: NzToolTipComponent;
  @Output() onButtonAction = new EventEmitter<PlanOperateBtnEnum>();

  isCloseTooltip = false;

  planOperateBtnEnum = PlanOperateBtnEnum;
  private _routerSubscription?: Subscription;

  actionMap = {
    hasAllocation: false,
    hasEditPlan: false,
    hasEditSam: false,
  };

  constructor(public shareService: ProductionPlanShareService, private _service: ProductionPlanV2Service) {}

  ngOnInit() {
    this.actionMap = this._service.getUserActionsMap();
  }

  ngOnDestroy(): void {
    this._routerSubscription?.unsubscribe();
  }

  handleButtonAction(action: PlanOperateBtnEnum) {
    this.onButtonAction.emit(action);
  }

  closeTooltip() {
    this.isCloseTooltip = true;
  }
}
