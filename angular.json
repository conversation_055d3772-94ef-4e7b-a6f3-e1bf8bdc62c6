{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"elan-web": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"preserveSymlinks": true, "outputPath": "dist/elan-web", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "./node_modules/@ant-design/icons-angular/src/inline-svg/", "output": "/assets/"}, {"glob": "**/*", "input": "./node_modules/fl-sewsmart-lib/assets/i18n/", "output": "/assets/i18n/"}, {"glob": "**/*", "input": "./node_modules/fl-common-lib/lib/assets/i18n/", "output": "/assets/i18n/"}, {"glob": "**/*", "input": "./node_modules/fl-sewsmart-lib/assets/image/", "output": "/assets/image/"}, {"glob": "**/*", "input": "./node_modules/fl-common-lib/lib/assets/font/", "output": "/assets/font/"}, {"glob": "**/*", "input": "src/fl-sewsmart-lib/assets/i18n/", "output": "/assets/i18n/"}, {"glob": "**/*", "input": "src/fl-sewsmart-lib/assets/image/", "output": "/assets/image/"}, {"glob": "**/*", "input": "src/fl-common-lib/assets/font/", "output": "/assets/font/"}, {"glob": "**/*", "input": "src/fl-common-lib/assets/i18n/", "output": "/assets/i18n/"}, {"glob": "**/*", "input": "./node_modules/fl-common-lib/lib/assets/font/tinymce/", "output": "/assets/js/tinymce/langs/"}, {"glob": "**/*", "input": "src/fl-common-lib/assets/font/tinymce/", "output": "/assets/js/tinymce/langs/"}, {"glob": "**/*", "input": "node_modules/tinymce/", "output": "/assets/js/tinymce/"}], "styles": ["src/theme.less", "src/styles.scss"], "stylePreprocessorOptions": {"includePaths": ["src"]}, "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "30kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "production-ty": {"budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "15kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod-ty.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "development-link": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "tsConfig": "tsconfig.dev.json"}, "development-ty": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.ty.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "elan-web:build:production"}, "production-ty": {"browserTarget": "elan-web:build:production-ty"}, "development": {"browserTarget": "elan-web:build:development"}, "development-link": {"browserTarget": "elan-web:build:development-link"}, "development-ty": {"browserTarget": "elan-web:build:development-ty"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "elan-web:build", "proxyConfig": "proxy.config.json"}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/app/**/*.ts"]}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}}}}, "defaultProject": "elan-web", "cli": {"analytics": false, "defaultCollection": "@angular-eslint/schematics"}}