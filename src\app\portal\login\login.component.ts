import { AfterViewInit, ChangeDetectorRef, Component, ElementRef, Inject, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { AppStorageService } from 'src/app/shared/app-storage.service';
import { AuthService } from 'src/app/shared/auth.service';
import { factoryCodes } from 'src/app/shared/common-config';
import { ValidationService } from 'src/app/shared/validation.service';

const langObj: any = {
  zh: 'zh_CN',
  en: 'en_XX',
};

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent implements OnInit, AfterViewInit {
  @ViewChild('userNameInput') userNameInput?: ElementRef;
  @ViewChild('passwordInput') passwordInput?: ElementRef;

  title?: string;

  userName = '';
  password = '';
  isLoginFormValid = false;
  isPwdVisible = false;

  pwdForm!: FormGroup;
  isPwd1Visible = false;
  isPwd2Visible = false;
  pwdHint = '';

  state = 'login'; // state: login, resetPwd, resetDone

  // pwdPattern = '^(?=.*\\d)(?=.*[A-Za-z])(?=.*[~!@#$¥%&*)(,-.+<=>?^_:…/])(?!.*\\s).{8,16}$';
  pwdPattern = '[^\\u4e00-\\u9fa5\\s]{1,16}';

  brand = 'fl';

  mobileMode = false;

  lang = localStorage.getItem('lang') || 'zh';
  showSwitchLang = false; // 是否显示切换语言
  factoryCodes: string[] = factoryCodes || [];

  constructor(
    public _translateService: TranslateService,
    private _fb: FormBuilder,
    private _validation: ValidationService,
    private _auth: AuthService,
    private _router: Router,
    private _storage: AppStorageService,
    @Inject('environment') env: any
  ) {
    env.brand && (this.brand = env.brand);
    this.mobileMode = this.isMobileDevice();
    this.lang = localStorage.getItem('lang') || (this.factoryCodes.includes(env.brand) ? 'en' : 'zh');
  }

  ngOnInit(): void {
    const nlsKey = this.brand === 'FLKJ_ELAN' ? 'login.welcomeTitle' : 'login.welcomeTitleImg';
    this._translateService.get(nlsKey).subscribe((text) => {
      this.title = text;
    });
    this.resetPwdForm();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      if (this.password.length > 0) {
        this.passwordInput?.nativeElement.focus();
      } else {
        this.userNameInput?.nativeElement.focus();
      }
    }, 500);
  }

  submitLoginForm(): Promise<'loginSuccess' | 'loginFail' | 'captchaFail'> {
    return new Promise((resolve) => {
      this._auth
        .login({
          login_name: this.userName,
          password: this.password,
          captcha_type: 'blockPuzzle',
          point_json: this.captchaPointJSON,
          token: this.captchaToken,
        })
        .subscribe(
          (res) => {
            if (res.code === 200) {
              //首次登录，需要重置密码
              if (res.data.password_need_modify) {
                this.resetPwdForm();
                this.state = 'resetPwd';
                resolve('loginSuccess');
              } else {
                if (res.data.captcha_res) {
                  resolve('loginSuccess');
                  //成功后转跳
                  this.saveLogin(res.data.user_info, res.data.jwt);
                  this._router.navigate(['/']);
                } else {
                  resolve('captchaFail');
                }
              }
            }
          },
          (error) => {
            resolve('loginFail');
            this.goLogin();
          }
        );
    });
  }

  saveLogin(userInfo: any, jwt: any) {
    this._storage.saveAccessToken(jwt.access_token);
    this._storage.saveRefreshToken(jwt.refresh_token);
    this._storage.saveUser(userInfo);
    localStorage?.setItem('lang', this.lang);
    localStorage.setItem('factory_code', userInfo.factory_code);
  }

  resetPwdForm(): void {
    this.pwdForm = this._fb.group(
      {
        password1: ['', { updateOn: 'blur', validators: Validators.pattern(this.pwdPattern) }],
        password2: ['', [Validators.required]],
      },
      { validators: this._validation.notMatchingValidator('password1', 'password2') }
    );
  }

  onPwd1Click(): void {
    // this.pwdHint.length === 0 && (this.pwdHint = this._translateService.instant('login.pwdHint'));
  }

  submitPwdForm(): void {
    const params = {
      login_name: this.userName,
      password: this.password,
      new_password: this.pwdForm.controls.password1.value,
    };

    this._auth.resetPwd(params).subscribe((res) => {
      if (res.code === 200) {
        this.state = 'resetDone';
        this.userName = '';
        this.password = '';
      }
    });
  }

  goLogin(): void {
    this.state = 'login';
  }

  goCaptcha(): void {
    // this.state = 'captcha';
    this.submitLoginForm();
  }

  captchaSecretKey?: string;
  captchaToken?: string;
  captchaPointJSON?: string;
  onFetchPuzzleImage = () => {
    return new Promise((resolve) => {
      this._auth.getCaptchaPuzzleImage().subscribe((res) => {
        if (res && res.repCode === '0000') {
          this.captchaSecretKey = res.repData.secretKey;
          this.captchaToken = res.repData.token;
          resolve({
            bgImageBase64: res.repData.originalImageBase64,
            pieceImageBase64: res.repData.jigsawImageBase64,
          });
        }
      });
    });
  };

  onCaptchaVerify = async (offsetX: number) => {
    if (this.captchaSecretKey !== undefined && this.captchaToken !== undefined) {
      this.captchaPointJSON = this._auth.aesEncrypt(
        JSON.stringify({ x: offsetX - (this.mobileMode ? 0 : 5), y: 5 }),
        this.captchaSecretKey
      );
      const res = await this.submitLoginForm();
      this.captchaSecretKey = undefined;
      this.captchaToken = undefined;
      this.captchaPointJSON = undefined;
      return res !== 'captchaFail';
    }
    return false;
  };

  isMobileDevice() {
    const regExp =
      /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i;
    return regExp.test(navigator.userAgent);
  }

  // 切换语言
  switchLanguage(language: string) {
    console.log('switch lang: ' + language);
    localStorage.setItem('lang', language);
    this._translateService.use(language);
    window.location.reload();
  }
}
