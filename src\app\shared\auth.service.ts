import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { ResponseData } from '../core/common/interface/http';
import { AppStorageService } from './app-storage.service';
import { v4 as uuidV4 } from 'uuid';
import * as CryptoJS from 'crypto-js';

export interface loginData {
  user_info: UserInfo;
  password_need_modify: boolean;
  jwt: any;
  captcha_res: boolean;
}
export interface UserInfo {
  login_name: string;
  name: string;
  roles: any[];
  employee_name: string;
}

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  constructor(private http: HttpClient, private _storage: AppStorageService, private _router: Router) {}

  login(payload: any): Observable<ResponseData<loginData>> {
    return this.http.post<ResponseData<loginData>>('/user/login', payload);
  }

  logout(): Observable<any> {
    return this.http.get('/user/logout');
  }

  resetPwd(payload: any): Observable<ResponseData<loginData>> {
    return this.http.put<ResponseData<any>>('/user/initial-password', payload);
  }

  // 刷新access token
  refreshAccessToken() {
    return this.http.post('/user/token/refresh', {
      refresh_token: this._storage.getRefreshToken(),
    });
  }

  isLoggedIn() {
    return this._storage.getAccessToken() != null;
  }

  getCaptchaPuzzleImage() {
    return this.http.post<any>('/service/captcha/v1/captcha/get', { captchaType: 'blockPuzzle', clientUid: uuidV4(), ts: Date.now() });
  }

  aesEncrypt(data: string, secretKey: string) {
    return CryptoJS.AES.encrypt(CryptoJS.enc.Utf8.parse(data), CryptoJS.enc.Utf8.parse(secretKey), {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    }).toString();
  }
}
