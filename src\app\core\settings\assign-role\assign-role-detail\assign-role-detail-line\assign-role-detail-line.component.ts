import { Component, Input, OnInit } from '@angular/core';
import { RoleNameChildModel } from '../../../role-manager/role-manager.model';

@Component({
  selector: 'app-assign-role-detail-line',
  templateUrl: './assign-role-detail-line.component.html',
  styleUrls: ['./assign-role-detail-line.component.scss'],
})
export class AssignRoleDetailLineComponent implements OnInit {
  @Input() item!: RoleNameChildModel;
  @Input() isRangeMode!: boolean;
  @Input() allDepartmentList: { id: number; name: string }[] = [];
  showBtnArea = false;
  showRangeArea = true;
  translateName = 'assign.role.';
  constructor() {}

  nonRangeConfigKey: string[] = [
    'bulk-material-preparation',
    'material-preparation',
    'material-preparation-report',
    'prototyping',
    'bulkorder',
    'other',
    'material-procurement:order',
    'autocheck-price',
    'sys-parameters',
    'in-stock',
    'out-stock',
    'stock-on-hand',
    'quote-price-upstream',
    'delivery-plan:packing-list',
    'material-settlement',
    'manufacture-settlement',
    'material-payment-request',
    'manufacture-payment-request',
    'payable-report',
    'settlement-adjustment',
    'material_manage',
    'garment_check',
    'coop:material-delivery-plan',
    'coop:material-tag',
    'coop:sending-material',
    'coop:returning-material',
    'material:material-delivery-plan',
    'material:receiving-material',
    'basic-archive:supplier-access', // 供应商准入
    'basic-archive:supplier-evaluation-scm', // 供应商考核
    'basic-archive:supplier-style', // 供应商类型
    'basic-archive:supplier-evaluation-criteria', // 供应商评估标准
    'coop:supplier-evaluation-collab', // 供应商绩效
  ];

  ngOnInit(): void {
    this.showBtnArea = (this.item.children ?? []).length > 0 && this.item.children!.some((x) => x.visible);
    for (const key of this.nonRangeConfigKey) {
      if (this.item.code.endsWith(key)) {
        this.showRangeArea = false;
        break;
      }
    }
  }
  getDepartmentName(id: number): string | undefined {
    return this.allDepartmentList.find((x) => x.id === id)?.name;
  }
}
