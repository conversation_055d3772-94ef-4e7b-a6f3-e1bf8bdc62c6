<div class="dashboard">
  <div class="orderInfo">
    <div class="orderItem"><span>IO:</span>{{ orderInfo.bulk_order_code }}</div>
    <nz-divider nzType="vertical"></nz-divider>
    <div class="orderItem">
      <span>{{ translateName + '交期' | translate }}:</span>{{ orderInfo.max_due_time | date: 'yyyy-MM-dd' }}
    </div>
    <nz-divider nzType="vertical"></nz-divider>
    <div class="orderItem">
      <span>{{ translateName + '订单总数' | translate }}:</span>{{ orderInfo.order_quantity }}
    </div>
    <nz-divider nzType="vertical"></nz-divider>
    <div class="orderItem">
      <span>{{ translateName + '款式编码' | translate }}:</span>{{ orderInfo?.style_code }}
    </div>
    <nz-divider nzType="vertical"></nz-divider>
    <div class="orderItem">
      <span>{{ translateName + '加工厂' | translate }}:</span>{{ orderInfo?.factory_name }}
    </div>
  </div>
  <nz-tabset nzType="card" nzSize="small" [(nzSelectedIndex)]="currentColorIndex">
    <nz-tab *ngFor="let color of colorList" [nzTitle]="color.color_name">
      <ng-container *ngIf="color.fabricList.length > 0">
        <span>{{ translateName + '面料' | translate }}</span
        ><br />
        <ng-container *ngTemplateOutlet="materialTableTemplate; context: { $implicit: color.fabricList }"></ng-container>
      </ng-container>
      <ng-container *ngIf="color.accessoryList.length > 0">
        <span>{{ translateName + '辅料' | translate }}</span
        ><br />
        <ng-container *ngTemplateOutlet="materialTableTemplate; context: { $implicit: color.accessoryList }"></ng-container>
      </ng-container>
    </nz-tab>
  </nz-tabset>
  <nz-divider style="margin: 12px 0 0 0" nzType="horizontal"></nz-divider>
  <div class="footer">
    <button nz-button nzType="default" (click)="cancel()">{{ 'btn.cancel' | translate }}</button>
    <button nz-button nzType="primary" (flcClickStop)="save()">{{ 'btn.save' | translate }}</button>
  </div>
</div>
<ng-template #materialTableTemplate let-materialList>
  <nz-table nzBordered class="zebra-striped-table" #materialTable nzSize="small" [nzData]="materialList" [nzFrontPagination]="false">
    <thead>
      <tr>
        <th nzWidth="170px">{{ translateName + '名称' | translate }}</th>
        <th nzWidth="80px">{{ translateName + '颜色' | translate }}</th>
        <th nzWidth="80px">{{ translateName + '供应商' | translate }}</th>
        <th nzWidth="80px">{{ translateName + '规格' | translate }}</th>
        <th nzWidth="80px">{{ translateName + '总用量' | translate }}</th>
        <th nzWidth="80px">{{ translateName + '累计到厂' | translate }}</th>
        <th nzWidth="128px">{{ translateName + '到厂数量' | translate }}</th>
        <th nzWidth="128px">{{ translateName + '到厂时间' | translate }}</th>
        <th nzWidth="100px">{{ translateName + '状态进度' | translate }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let line of materialTable.data">
        <td><flc-table-body-render [data]="line.material_name" type="text"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="line.material_color_name" type="text"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="line.supplier_name" type="text"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="line.specification_name" type="text"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="line.material_qty" type="text"></flc-table-body-render></td>
        <td>
          <button nz-button nzType="link" size="small" (click)="showDetail(line)">
            <flc-table-body-render [data]="line.total_qty + line.unit_name" type="text"></flc-table-body-render>
          </button>
        </td>
        <td>
          <nz-input-number-group [nzAddOnAfter]="line.unit_name" style="width: 150px">
            <nz-input-number [nzPlaceHolder]="'placeholder.input' | translate" [(ngModel)]="line.qty" [nzMin]="0"> </nz-input-number>
          </nz-input-number-group>
        </td>
        <td><nz-date-picker [(ngModel)]="line.finished_time"></nz-date-picker></td>
        <td>
          <div [style.background]="line.statusBgColor" [style.color]="line.statusTextColor" class="statusBar">
            {{ translateName + line.statusName | translate }}·{{ line.finishedPercent }}%
          </div>
        </td>
      </tr>
    </tbody>
  </nz-table>
</ng-template>
<ng-template #historyTemplate>
  <div class="orderInfo">
    <div class="orderItem"><span>IO:</span>{{ orderInfo.bulk_order_code }}</div>
    <nz-divider nzType="vertical"></nz-divider>
    <div class="orderItem">
      <span>{{ translateName + '交期' | translate }}:</span>{{ orderInfo.max_due_time | date: 'yyyy-MM-dd' }}
    </div>
    <nz-divider nzType="vertical"></nz-divider>
    <div class="orderItem">
      <span>{{ translateName + '订单总数' | translate }}:</span>{{ orderInfo.order_quantity }}
    </div>
    <ng-container *ngIf="historyLine">
      <nz-divider nzType="vertical"></nz-divider>
      <div class="orderItem">
        <span>{{ translateName + '供应商' | translate }}:</span>{{ historyLine!.supplier_name }}
      </div>
      <nz-divider nzType="vertical"></nz-divider>
      <div class="orderItem">
        <span>{{ translateName + '规格' | translate }}:</span>{{ historyLine!.specification_name }}
      </div>
      <nz-divider nzType="vertical"></nz-divider>
      <div class="orderItem">
        <span>{{ translateName + '累计到厂' | translate }}:</span>{{ historyLine!.total_qty }}
      </div>
    </ng-container>
  </div>
  <nz-table
    nzBordered
    class="zebra-striped-table"
    #historyTable
    nzSize="small"
    [nzData]="historyLine?.historyList ?? []"
    [nzFrontPagination]="false">
    <thead>
      <tr>
        <th>{{ translateName + '提交时间' | translate }}</th>
        <th>{{ translateName + '到厂时间' | translate }}</th>
        <th>{{ translateName + '提交人' | translate }}</th>
        <th>{{ translateName + '到厂数量' | translate }}</th>
        <!-- <th>图片</th> -->
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let line of historyTable.data">
        <td>{{ line.gen_time | date: 'yyyy-MM-dd HH:mm:ss' }}</td>
        <td>{{ line.finished_time | date: 'yyyy-MM-dd' }}</td>
        <td>{{ line.user_name }}</td>
        <td>
          <nz-input-number-group [nzAddOnAfter]="historyLine?.unit_name">
            <nz-input-number [nzPlaceHolder]="'placeholder.input' | translate" [(ngModel)]="line.qty" [nzMin]="0"> </nz-input-number>
          </nz-input-number-group>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <nz-divider style="margin: 12px 0 0 0" nzType="horizontal"></nz-divider>
  <div class="footer">
    <button nz-button nzType="default" style="margin-right: 4px" (click)="closeHistoryModal()">{{ 'btn.cancel' | translate }}</button>
    <button nz-button nzType="primary" (flcClickStop)="saveHistoryChange()">{{ 'btn.save' | translate }}</button>
  </div>
</ng-template>
