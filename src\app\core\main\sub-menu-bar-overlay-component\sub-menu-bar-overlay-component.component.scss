@import 'variables';
#mainBoard {
  width: 100%;
  height: calc(100% - 48px);
  // margin-top: 48px;
  background-color: white;
  box-shadow: 4px 0px 8px 0px rgba(0, 0, 0, 0.04), inset 1px -2px 0px 0px rgba(0, 0, 0, 0.1);
  border-radius: 0px 4px 4px 0px;
  display: flex;
  flex-direction: column;
  row-gap: 24px;
  padding: 24px 40px;
  overflow-x: auto;
}
.secondMenu {
}
.secondMenuTitle {
  font-weight: 500;
  font-size: 16px;
  color: #222b3c;
  margin-bottom: 16px;
  pointer-events: none;
}
.thirdMenuWrapper {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  column-gap: 12px;
  row-gap: 4px;
}
.thirdMenuLine {
  cursor: pointer;
  padding: 10px;
  border-radius: 4px;
  font-size: 14px;
  width: 100%;
  height: 100%;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  &.selected,
  &:hover {
    background-color: #d9f7ef;
    color: $fl-pretty-color-dark;
  }
}
.menuIcon {
  width: 12px;
  height: 12px;
  margin-right: 6px;
}
