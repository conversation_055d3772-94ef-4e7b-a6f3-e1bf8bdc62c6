import { NzMessageService } from 'ng-zorro-antd/message';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FactoryManageListComponent } from './factory-manage/factory-manage-list/factory-manage-list.component';
import { ComponentsModule } from '../../components/components.module';
import { FactoryRoutingModule } from './factory-routing.module';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule, NzIconService } from 'ng-zorro-antd/icon';
import { FlUiAngularModule } from 'fl-ui-angular';
import { FactoryManageDetailComponent } from './factory-manage/factory-manage-detail/factory-manage-detail.component';
import { NzStepsModule } from 'ng-zorro-antd/steps';
import { FactoryBasicInformationReadonlyComponent } from './factory-manage/factory-manage-detail/factory-basic-information-readonly/factory-basic-information-readonly.component';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { HttpClient } from '@angular/common/http';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { NzPipesModule } from 'ng-zorro-antd/pipes';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { FactoryModeReadonlyComponent } from './factory-manage/factory-manage-detail/factory-mode-readonly/factory-mode-readonly.component';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FactoryModeEditComponent } from './factory-manage/factory-manage-detail/factory-mode-edit/factory-mode-edit.component';
import { FactoryBasicInformationEditComponent } from './factory-manage/factory-manage-detail/factory-basic-information-edit/factory-basic-information-edit.component';
import { DeployResultsEditComponent } from './factory-manage/factory-manage-detail/deploy-results-edit/deploy-results-edit.component';
import { DeployResultsReadonlyComponent } from './factory-manage/factory-manage-detail/deploy-results-readonly/deploy-results-readonly.component';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzCascaderModule } from 'ng-zorro-antd/cascader';
import { NzTypographyModule } from 'ng-zorro-antd/typography';
import { UndoModalComponent } from './factory-manage/factory-manage-detail/undo-modal/undo-modal.component';
import { NzModalService } from 'ng-zorro-antd/modal';
import { CancellationModalComponent } from './factory-manage/factory-manage-detail/cancellation-modal/cancellation-modal.component';
import { ReturnModalComponent } from './factory-manage/factory-manage-detail/return-modal/return-modal.component';
import { NzResizableModule } from 'ng-zorro-antd/resizable';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { PipesModule } from '../../pipes/pipes.module';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzSkeletonModule } from 'ng-zorro-antd/skeleton';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { UtilService } from 'src/app/shared/util.service';
import { DirectivesModule } from 'src/app/directive/directives.module';
import { FlcComponentsModule, FlcRouteReuseStrategy } from 'fl-common-lib';
import { RecommendSetListComponent } from './recommend-set/recommend-set-list/recommend-set-list.component';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { RecommendElementComponent } from './recommend-set/components/recommend-element/recommend-element.component';

@NgModule({
  declarations: [
    FactoryManageListComponent,
    FactoryManageDetailComponent,
    FactoryBasicInformationReadonlyComponent,
    FactoryModeReadonlyComponent,
    FactoryModeEditComponent,
    FactoryBasicInformationEditComponent,
    DeployResultsEditComponent,
    DeployResultsReadonlyComponent,
    UndoModalComponent,
    CancellationModalComponent,
    ReturnModalComponent,
    RecommendSetListComponent,
    RecommendElementComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ComponentsModule,
    FactoryRoutingModule,
    NzButtonModule,
    FlUiAngularModule,
    FlcComponentsModule,
    NzIconModule,
    NzStepsModule,
    NzDividerModule,
    NzToolTipModule,
    NzRadioModule,
    NzInputModule,
    NzFormModule,
    NzLayoutModule,
    NzSelectModule,
    NzInputNumberModule,
    NzCascaderModule,
    NzTypographyModule,
    NzPopoverModule,
    NzCheckboxModule,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new TranslateHttpLoader(http, './assets/i18n/factory/', '.json');
        },
        deps: [HttpClient],
      },
    }),
    NzPipesModule,
    NzResizableModule,
    NzTableModule,
    ComponentsModule,
    PipesModule,
    NzDatePickerModule,
    NzSpinModule,
    NzPopconfirmModule,
    NzSkeletonModule,
    DirectivesModule,
  ],
  providers: [NzModalService, NzMessageService, NzNotificationService, UtilService, FlcRouteReuseStrategy],
})
export class FactoryModule {
  constructor(public translateService: TranslateService, private iconService: NzIconService) {
    // 切换lang必须刷新页面，模块级别i18n文件才能成功加载
    const dl = translateService.defaultLang;
    translateService.defaultLang = '';
    translateService.setDefaultLang(dl);

    this.iconService.fetchFromIconfont({
      scriptUrl: 'https://at.alicdn.com/t/font_2782026_gkrcufahyqc.js',
    });
  }
}
