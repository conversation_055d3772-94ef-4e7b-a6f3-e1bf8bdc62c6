$cellHeight: 50px;

:host {
  .graph-item-wrap {
    width: 100%;
    height: $cellHeight;
    position: relative;
    .graph-item-bgc {
      background: #fafbfd;
      box-shadow: inset 0px 1px 3px 1px rgba(227, 236, 247, 0.5);
      height: 50%;
      top: 25%;
      position: relative;

      .graph-item {
        height: 50%;
        top: 25%;
        background-color: #d4d7dc;
        position: absolute;
        border-radius: 10px;
      }
    }
  }

  .left-cell {
    height: $cellHeight;
    text-align: center;
  }

  .factory-tag {
    padding: 0 5px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: start;
  }

  .factory-span {
    display: inline-block;
    background: #eef7ff;
    border-radius: 20px;
    border: 1px solid #b8ddff;
    width: 100%;
    max-width: 200px;
    padding: 0px 5px;
    color: #007aff;
    font-weight: 500;
  }

  .current-day-line {
    background-color: #2996ff;
    width: 1px;
    height: 100%;
    position: absolute;
    z-index: 1;
  }
}
