<div class="wrapper" style="padding: 0">
  <div *ngFor="let plan of data; index as i; last as last">
    <div class="content-wrapper">
      <div class="sort">{{ i + 1 }}</div>
      <div class="content">
        <nz-form-item style="margin-bottom: 0">
          <nz-form-label class="label-wrapper" style="width: 103px">
            <span class="label-color">{{ translateLabel + '关联材料企划' | translate }}</span>
          </nz-form-label>
          <nz-form-control style="flex: 1">
            <flc-text-truncated
              style="display: block; width: 200px"
              (click)="plan?.material_plan_name && canLinkDetail && openMaterialPlanDetail(plan?.material_plan_id)"
              [class]="plan?.material_plan_name && canLinkDetail ? 'link-text' : ''"
              [data]="plan?.material_plan_name"></flc-text-truncated>
          </nz-form-control>
          <div class="toggle" (click)="onChangeToggleRead(plan)">
            <span>{{ translateAction + (plan?.is_open ? '收起' : '展开') | translate }}</span>
            <i nz-icon [nzIconfont]="plan?.is_open ? 'icon-shouqi1' : 'icon-zhankai2'" style="margin-left: 5px"></i>
          </div>
        </nz-form-item>

        <!-- 关键物料 -->
        <div class="inner-content toggle-zhankai" [ngClass]="{ 'toggle-shouqi': !plan?.is_open }">
          <div *ngFor="let material of plan.material_lines; index as lineIndex; last as isLast">
            <div>
              <nz-form-item style="margin-bottom: 8px">
                <nz-form-label class="label-wrapper">
                  <span class="label-color">{{ translateLabel + '关键物料' | translate }}{{ lineIndex + 1 }}</span>
                </nz-form-label>
                <nz-form-control style="flex: 1">
                  <flc-text-truncated
                    (click)="material?.raw_material_name && canLinkDetail && openMaterialDetail(material)"
                    [data]="material?.raw_material_name"
                    [class]="material?.raw_material_name && canLinkDetail ? 'link-text' : ''"></flc-text-truncated>
                </nz-form-control>
              </nz-form-item>
              <nz-form-item style="margin-bottom: 8px">
                <nz-form-label class="label-wrapper">
                  <span class="label-color">{{ translateLabel + '描述' | translate }}</span>
                </nz-form-label>
                <nz-form-control style="flex: 1">
                  <flc-text-truncated [data]="material.description"></flc-text-truncated>
                </nz-form-control>
              </nz-form-item>

              <div class="file-wrapper">
                <flc-file-gallery
                  #fileGallery
                  [addBtnType]="'link'"
                  [isEditMode]="false"
                  [galleryType]="'image'"
                  [fileList]="material?.reference_image_url || []"
                  [wrap]="false"
                  [fileMaxSize]="20"
                  [fileMaxCount]="100"></flc-file-gallery>
              </div>
            </div>
            <nz-divider *ngIf="!isLast" nzDashed style="margin: 8px; border-color: #d4d7dc"> </nz-divider>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
