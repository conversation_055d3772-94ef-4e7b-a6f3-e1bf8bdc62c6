import { Component, Input, OnInit } from '@angular/core';
import { RoleNameChildModel, RoleNameItemModel } from '../../role-manager/role-manager.model';

@Component({
  selector: 'app-assign-role-detail',
  templateUrl: './assign-role-detail.component.html',
  styleUrls: ['./assign-role-detail.component.scss'],
})
export class AssignRoleDetailComponent implements OnInit {
  @Input() selectedItem!: RoleNameItemModel;
  @Input() allDepartmentList: { id: number; name: string }[] = [];
  isRangeMode = false;
  firstMenuList: RoleNameChildModel[] = [];
  selectedFirstMenu?: RoleNameChildModel;
  translateName = 'assign.role.';
  constructor() {}

  ngOnInit(): void {
    this.firstMenuList = this.selectedItem.children;
    this.selectedFirstMenu = this.firstMenuList[0];
    this.LastMenuCheck(this.firstMenuList);
    this.handleRangeList(this.firstMenuList ?? []);
  }

  LastMenuCheck(itemlist: RoleNameChildModel[]) {
    itemlist.forEach((child) => {
      if ((child.children?.length ?? 0) > 0) {
        child.isLastMenu = child.children![0].type === 'btn';
      }
      if (!child.isLastMenu) {
        this.LastMenuCheck(child.children ?? []);
      }
    });
  }
  handleRangeList(list: RoleNameChildModel[]) {
    list.forEach((item) => {
      if (item.isLastMenu) {
        if (item.value) {
          item.hasSelectedMenu = true;
        } else {
          item.hasSelectedMenu = false;
        }
      } else {
        this.handleRangeList(item.children ?? []);
        item.hasSelectedMenu = (item.children ?? []).some((item) => item.hasSelectedMenu);
        item.value = item.hasSelectedMenu;
        item.isHideForRangeMode = ([] as string[]).includes(item.code);
      }
    });
  }
  toggleRangMode(rangeMode: boolean) {
    this.isRangeMode = rangeMode;
    if (this.isRangeMode) {
      if (this.selectedFirstMenu?.hasSelectedMenu !== true) {
        // this.selectedFirstMenu = undefined;
        this.selectedFirstMenu = this.firstMenuList.find((item) => item.hasSelectedMenu);
      }
    } else {
      if (this.selectedFirstMenu === undefined) {
        this.selectedFirstMenu = this.firstMenuList[0];
      }
    }
  }
}
