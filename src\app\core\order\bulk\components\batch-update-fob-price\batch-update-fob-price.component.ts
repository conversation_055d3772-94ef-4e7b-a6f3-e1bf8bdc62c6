import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormControl, Validators } from '@angular/forms';

import { FlcValidatorService } from 'fl-common-lib';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';

import { BulkService } from '../../bulk.service';
import { EventTypeEnum } from '../../models/bulk.enum';
import { AppStorageService } from 'src/app/shared/app-storage.service';

@Component({
  selector: 'app-batch-update-fob-price',
  templateUrl: './batch-update-fob-price.component.html',
  styleUrls: ['./batch-update-fob-price.component.scss'],
})
export class BatchUpdateFobPriceComponent implements OnInit {
  @Input() order_ids: number[] = [];
  validateForm = this._fb.group({});
  formConfig: any = [
    {
      label: 'FOB价格',
      key: 'fob_price',
      type: 'number-input',
      required: true,
    },
  ];
  translateName = 'bulk.';

  constructor(
    private _service: BulkService,
    private _fb: FormBuilder,
    private _validator: FlcValidatorService,
    private _modal: NzModalService,
    private _message: NzMessageService,
    public _storage: AppStorageService
  ) {
    this._service.fieldArr = this._storage.getFieldActions('order/bulk');
  }

  ngOnInit(): void {
    this.initForm();
  }

  initForm() {
    this.formConfig?.forEach((item: any) => {
      const _control = new FormControl();
      item.required && _control.addValidators(Validators.required);
      this.validateForm?.addControl(item.key, _control);
    });
  }

  async onSave() {
    const isInvalid = await this._validator.formIsAsyncInvalid(this.validateForm);
    if (isInvalid) {
      return;
    }

    const rawValue = this.validateForm?.getRawValue();

    this._service
      .batchUpdateFobPrice({
        fob_price: rawValue?.fob_price ?? null,
        order_ids: this.order_ids,
      })
      .subscribe((res: any) => {
        if (res?.code === 200) {
          this._message.success(this._service.translateValue('success.update'));
          this._service.eventEmitter.next(EventTypeEnum.refreshList);
          this._modal.closeAll();
        }
      });
  }

  onCancel() {
    this._modal.closeAll();
  }
}
