<ng-template #titleTpl>
  <div class="return-title">{{ 'factory-modal.return-title' | translate }}</div>
</ng-template>

<ng-template #contentTpl>
  <form nz-form [formGroup]="returnForm">
    <nz-form-item class="return-content">
      <div class="return-label">{{ 'factory-modal.return-content' | translate }}</div>
      <nz-form-control class="return-msg" [nzErrorTip]="reasonErrTpl">
        <nz-textarea-count [nzMaxCharacterCount]="50" class="inline-count">
          <textarea rows="3" nz-input formControlName="reason" [maxLength]="50" [placeholder]="placeInput" nzAutosize inputTrim> </textarea>
        </nz-textarea-count>
      </nz-form-control>
      <ng-template #reasonErrTpl>
        <ng-container *ngIf="returnForm?.get('reason')?.dirty && returnForm?.get('reason')?.hasError('required')">
          {{ 'placeholder.input' | translate }}{{ 'factory-modal.return-content' | translate }}
        </ng-container>
      </ng-template>
    </nz-form-item>
  </form>
</ng-template>

<ng-template #footerTpl>
  <div class="return-footer">
    <button nz-button nzType="text" nzShape="round" (click)="handleCancel()">
      {{ 'btn.cancel' | translate }}
    </button>
    <button nz-button nzType="primary" [disableOnClick]="1000" nzShape="round" (click)="handleOk()">
      {{ 'btn.ok' | translate }}
    </button>
  </div>
</ng-template>
