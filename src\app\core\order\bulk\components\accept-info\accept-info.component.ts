import { Component, OnInit } from '@angular/core';
import { BulkService } from '../../bulk.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-accept-info',
  templateUrl: './accept-info.component.html',
  styleUrls: ['./accept-info.component.scss'],
})
export class AcceptInfoComponent implements OnInit {
  constructor(private _route: ActivatedRoute, private _service: BulkService) {}

  detailInfo!: any;

  baseConfig = [
    { label: '下单时间', type: 'datetime', key: 'order_date' },
    { label: '客户大货单号', type: 'text', key: 'io_code' },
    { label: '客户名称', type: 'text', key: 'receiver' },
    { label: '客户联系方式', type: 'text', key: 'contact' },
    { label: '客户款号', type: 'text', key: 'customer_style' },
    { label: '订单类型', type: 'text', key: 'task_type' },
    { label: '期望交期', type: 'date', key: 'due_time' },
    { label: '款式分类', type: 'text', key: 'material_name' },
    { label: '收货地址', type: 'text', key: 'address', span: 24 },
    { label: '备注', type: 'text', key: 'remark', span: 16 },
    { label: '订单件数', type: 'color-size', key: 'io_lines', span: 24 },
    { label: '附件资料', type: 'file', key: 'appendix_requirements', span: 24 },
  ];

  remarkConfig = [
    { label: '面料要求', key: 'material_require' },
    { label: '辅料要求', key: 'accessory_require' },
    { label: '版型要求', key: 'pattern_require' },
    { label: '工艺要求', key: 'technology_require' },
    { label: '包装及物流要求', key: 'packaging_require' },
  ];

  ngOnInit() {
    this.getDetail();
  }

  private getDetail(): void {
    const history_id = this._route.snapshot.queryParamMap.get('history_id')!;
    this._service.acceptDetail(history_id).subscribe((res) => {
      if (res.code === 200) {
        const { io_basic, io_lines, pos } = res.data;
        this.detailInfo = {
          io_lines,
          remark: io_basic.remark,
          io_code: io_basic.io_code,
          order_date: io_basic.order_date,
          task_type: io_basic.task_type,
          order_pictures: io_basic.order_pictures,
          customer_style: io_basic.customer_style,
          material_require: io_basic.material_require,
          accessory_require: io_basic.accessory_require,
          pattern_require: io_basic.pattern_require,
          technology_require: io_basic.technology_require,
          packaging_require: io_basic.packaging_require,
          material_name: `${io_basic.first_material_name ?? '/'}-${io_basic.second_material_name ?? '/'}-${
            io_basic.third_material_name ?? '/'
          }`,
          appendix_requirements: io_basic.appendix_requirements,

          due_time: pos.length ? pos[0]?.po_basic?.due_time : null,
          receiver: pos.length ? pos[0]?.po_basic?.receiver : null,
          contact: pos.length ? pos[0]?.po_basic?.contact : null,
          address: pos.length
            ? pos[0]?.po_basic?.country_name +
              pos[0]?.po_basic?.province_name +
              pos[0]?.po_basic?.city_name +
              pos[0]?.po_basic?.district_name +
              pos[0]?.po_basic?.address
            : null,
        };
      }
    });
  }
}
