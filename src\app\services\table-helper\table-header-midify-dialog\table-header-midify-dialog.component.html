<div class="wrap">
  <div class="background">
    <div class="headerTitle">{{ 'common.表头字段设置' | translate }}</div>
    <div class="Boundary" cdkDropListDisabled="true" cdkDropList (cdkDropListDropped)="drop($event)">
      <div cdkDrag cdkDragBoundary=".Boundary" class="selectLine" *ngFor="let item of _shadowList">
        <label nz-checkbox [(ngModel)]="item.visible" [name]="item.label" [nzDisabled]="item.disable || item.pinned">{{
          item.label
        }}</label>
        <a
          nz-button
          nzType="text"
          nzShape="circle"
          nzSize="small"
          [disabled]="false"
          class="pinBtn"
          [ngClass]="{ pinned: item.pinned, notvisible: !item.visible || item.disable }"
          [nzTooltipTitle]="(item.pinned ? 'common.点击取消置顶' : 'common.点击添加置顶') | translate"
          nzTooltipPlacement="top"
          nz-tooltip
          (click)="pin(item)">
          <i nz-icon nzType="pushpin" nzTheme="fill"></i>
        </a>
      </div>
    </div>
    <div class="bottomBar">
      <button nz-button nzType="text" nzShape="round" (click)="reset()">{{ 'btn.reset' | translate }}</button>
      <button nz-button nzType="primary" nzShape="round" (click)="submit()">{{ 'btn.ok' | translate }}</button>
    </div>
  </div>
  <div class="rightArrow">
    <!-- <div class="arrowContent"></div> -->
  </div>
</div>
