<div *ngIf="detail.io_basic.order_status !== 0" id="print" style="background: #fff; display: none">
  <div class="heard-box">
    {{ detail.io_basic.customer }} <span style="margin-left: 8px"> {{ translateName + '公司订单' | translate }} </span>
    <img [src]="logoUrl" alt="" class="img-box" />
    <div class="department">{{ translateName + '部门' | translate }}：{{ detail.io_basic.department }}</div>
  </div>
  <div class="basic-info noBreak" nz-row>
    <div nz-col nzSpan="2">PO#</div>
    <div nz-col nzSpan="4">
      {{ detail.io_basic.io_code || '-' }}
    </div>
    <div nz-col nzSpan="2">{{ translateName + '商店' | translate }}</div>
    <div nz-col nzSpan="4">
      {{ detail.io_basic.store || '-' }}
    </div>
    <div nz-col nzSpan="2">{{ translateName + '交期' | translate }}</div>
    <div nz-col nzSpan="4">
      {{ detail.io_basic.due_time | date: 'yyyy/MM/dd' }}
    </div>
    <div nz-col nzSpan="3">{{ translateName + '公司编号' | translate }}</div>
    <div nz-col nzSpan="3">
      {{ detail.io_basic.company_code || '-' }}
    </div>
    <div nz-col nzSpan="2">{{ translateName + '款号' | translate }}</div>
    <div nz-col nzSpan="4">
      {{ detail.io_basic.style_code || '-' }}
    </div>
    <div nz-col nzSpan="2">{{ translateName + '品名' | translate }}</div>
    <div nz-col nzSpan="4">
      {{ detail.io_basic.category || '-' }}
    </div>
    <div nz-col nzSpan="2">{{ translateName + '数量' | translate }}</div>
    <div nz-col nzSpan="4">
      {{ sizeTotal.get('total') || '-' }}
    </div>
    <div nz-col nzSpan="3">{{ translateName + '制单日期' | translate }}</div>
    <div nz-col nzSpan="3">
      {{ detail.io_basic.order_date | date: 'yyyy/MM/dd' }}
    </div>
    <div nz-col nzSpan="2">{{ translateName + '面料' | translate }}</div>
    <div nz-col nzSpan="22" class="lang-td">
      {{ detail.io_basic.fabric || '-' }}
    </div>
    <div nz-col nzSpan="2">{{ translateName + '配色' | translate }}</div>
    <div nz-col nzSpan="22" class="lang-td">
      {{ detail.io_basic.color || '-' }}
    </div>
    <div nz-col nzSpan="24" class="lang-td">
      {{ translateName + '备注' | translate }}：
      {{ detail.io_basic.remark || '-' }}
    </div>
  </div>
  <nz-table #basicTable [nzData]="dataList" nzBordered [nzFrontPagination]="false" class="bulk-print-0001 noBreak">
    <thead>
      <tr>
        <th nzWidth="80px" style="border-left: 1px solid #242b3b">PO#</th>
        <th nzWidth="80px">{{ translateName + '款式/国家' | translate }}</th>
        <th nzWidth="80px">{{ translateName + '订单颜色' | translate }}</th>
        <th nzWidth="80px" *ngFor="let part of summaryPartList">{{ part.displayPartName }}</th>
        <th nzWidth="80px">{{ translateName + '离厂时间' | translate }}</th>
        <th *ngFor="let size of sizeDataList">{{ size.spec_size }}</th>
        <th nzWidth="80px">{{ translateName + '合计' | translate }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of basicTable.data">
        <td style="border-left: 1px solid #242b3b">{{ data.po_code }}</td>
        <td>{{ detail.io_basic.style_code }}</td>
        <td>{{ data.color_name }}</td>
        <td *ngFor="let part of data.partList" style="word-break: break-all">
          <ng-container *ngIf="part.displayPartName; else emptyTpl">
            <ng-container *ngFor="let item of part.material_color_list; let last = last"
              >{{ item.material_code }}({{ item.color_name }})<ng-container *ngIf="!last">\</ng-container></ng-container
            >
          </ng-container>
          <ng-template #emptyTpl>-</ng-template>
        </td>
        <td>{{ data.due_time | date: 'yyyy/MM/dd' }}</td>
        <td *ngFor="let size of sizeDataList">
          {{ data[size.spec_id] || '-' }}
        </td>
        <td>{{ data.totalQty || '-' }}</td>
      </tr>
      <tr>
        <td style="border-left: 1px solid #242b3b">{{ translateName + '合计' | translate }}</td>
        <td></td>
        <td></td>
        <td *ngFor="let part of summaryPartList"></td>
        <td></td>
        <td *ngFor="let size of sizeDataList">
          {{ sizeTotal.get(size.spec_id) || '-' }}
        </td>
        <td>{{ sizeTotal.get('total') || '-' }}</td>
      </tr>
      <tr>
        <td style="border-left: 1px solid #242b3b">{{ translateName + '合同价' | translate }}</td>
        <td>
          {{ detail.io_basic.contract_price }}
        </td>
        <td [colSpan]="3 + sizeDataList.length + summaryPartList.length"></td>
      </tr>
    </tbody>
  </nz-table>
  <div nz-row class="bottom-basci-info noBreak">
    <div>
      <div nz-row style="height: 100%">
        <div class="info-label" style="letter-spacing: 18px">{{ translateName + '款式图' | translate }}</div>
        <div id="order_pictures-img" style="flex: 1; display: flex; align-items: center; padding-left: 8px; padding-top: 8px">
          <!-- <flc-image-gallery
            #imageGallery
            [ngModelOptions]="{ standalone: true }"
            [isEdit]="false"
            [numberLimit]="100"
            [(ngModel)]="detail.io_basic.order_pictures"></flc-image-gallery> -->

          <img [src]="detail.io_basic.order_pictures[0]?.url" class="img-box" alt="" />
        </div>
      </div>
    </div>
    <div style="flex: 1">
      <div nz-row style="align-items: center">
        <div class="info-label">{{ translateName + '样衣要求' | translate }}\{{ translateName + '印绣花' | translate }}</div>
        <div style="flex: 1; height: 100%">
          <div
            *ngIf="detail.io_basic.sample_requirement"
            style="border-bottom: 1px solid #d5d7dc; padding-top: 8px; padding-left: 8px; white-space: pre-wrap; min-height: 50%">
            {{ detail.io_basic.sample_requirement }}
          </div>
          <div
            *ngIf="detail.io_basic.printing_embroidery"
            style="padding-top: 8px; padding-left: 8px; white-space: pre-wrap; min-height: 50%">
            {{ detail.io_basic.printing_embroidery }}
          </div>
        </div>
        <img style="flex-shrink: 0" *ngFor="let pic of ioBasicPicList" [src]="pic.url" class="img-box" alt="" />
      </div>
    </div>
  </div>
  <div nz-row class="bottom-basci-info-2 noBreak">
    <div
      nz-col
      nzSpan="1"
      style="
        writing-mode: vertical-lr;
        display: flex;
        align-items: center;
        justify-content: center;
        letter-spacing: 8px;
        border-right: 1px solid;
      ">
      {{ translateName + '包装辅料信息' | translate }}
    </div>
    <div nz-col nzSpan="23">
      <div nz-row class="packaging-box">
        <div nz-col nzSpan="8">
          {{ detail.io_basic.sew_accessory }}
        </div>
        <div nz-col nzSpan="8">
          {{ detail.io_basic.consolidation_accessory }}
        </div>
        <div nz-col nzSpan="8">
          {{ detail.io_basic.accessory_remark }}
        </div>
      </div>
    </div>
  </div>
  <div class="noBreak" nz-row style="min-height: 120px">
    <div
      nz-col
      nzSpan="1"
      style="
        writing-mode: vertical-lr;
        display: flex;
        align-items: center;
        justify-content: center;
        letter-spacing: 8px;
        border: 1px solid;
        border-top: none;
      ">
      {{ translateName + '特殊要求' | translate }}
    </div>
    <div nz-col nzSpan="23" style="min-height: 100%">
      <div style="border-bottom: 1px solid; padding-left: 8px; min-height: 100%; padding-top: 8px; border-right: 1px solid">
        {{ detail.io_basic.special_requirement }}
      </div>
    </div>
  </div>
  <div class="noBreak" nz-row style="margin-top: 4px">
    <div nz-col nzSpan="6">{{ translateName + '签收栏' | translate }}</div>
    <div nz-col nzSpan="6">{{ translateName + '制单' | translate }}：{{ detail.io_basic.biz_user_name }}</div>
    <div nz-col nzSpan="6">{{ translateName + '审核' | translate }}：</div>
    <div nz-col nzSpan="6">{{ translateName + '质检' | translate }}：</div>
    <div nz-col nzSpan="6" style="margin-top: 4px">{{ translateName + '技术部' | translate }}：</div>
    <div nz-col nzSpan="6" style="margin-top: 4px">{{ translateName + '生产部' | translate }}：</div>
    <div nz-col nzSpan="6" style="margin-top: 4px">{{ translateName + '辅料' | translate }}：</div>
    <div nz-col nzSpan="6" style="margin-top: 4px">{{ translateName + '财务' | translate }}：</div>
  </div>
</div>
