import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NotpassReasonComponent } from './notpass-reason/notpass-reason.component';

@Injectable()
export class DischargeCommonService {
  constructor(private http: HttpClient, private modalService: NzModalService) {}

  // 分配算料员
  batchSubmitMaterialsEmployeeOption(payload: any) {
    return this.http.post<any>('/service/procurement-inventory/layoutcons/v1/dist', payload);
  }

  // 获取部门人员
  getDepartmentOption() {
    return this.http.get<any>('/service/archive/v1/api/organization/basic_option');
  }

  // 审核弹出框
  confirmDialogWithReason() {
    return this.modalService.create({
      nzContent: NotpassReasonComponent,
      nzWidth: 400,
      nzClosable: false,
      nzWrapClassName: 'flc-confirm-modal',
      nzFooter: null,
    });
  }

  // 审核
  audit(payload: any) {
    return this.http.post<any>('/service/procurement-inventory/layoutcons/v1/approve', payload);
  }
}
