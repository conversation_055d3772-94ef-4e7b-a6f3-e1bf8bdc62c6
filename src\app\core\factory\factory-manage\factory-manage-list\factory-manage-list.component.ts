import { <PERSON><PERSON>iew<PERSON>nit, Component, On<PERSON><PERSON>roy, OnInit, QueryList, TemplateRef, ViewChild, ViewChildren } from '@angular/core';
import { Router } from '@angular/router';
import { FactoryListData, SearchData } from '../interface';
import { NzResizeEvent } from 'ng-zorro-antd/resizable';
import { TableHeaderConfig, TableHelperService } from '../../../../services/table-helper/table-helper.service';
import { TranslateService } from '@ngx-translate/core';
import { FactoryManageService } from '../factory-manage.service';
import { FactoryManageEnum } from '../interface/factory-manage.enum';
import { CommonService } from '../../../../shared/common.service';
import { isNotNil } from 'ng-zorro-antd/core/util';
import { Resizable } from '../../../../decorator/resizable';
import { format } from 'date-fns';
import { SearchContainerComponent } from 'src/app/components/search-container/search-container.component';
import { finalize } from 'rxjs/operators';
import { AppStorageService } from 'src/app/shared/app-storage.service';
import { FlcRouterEventBusService } from 'fl-common-lib';

const version = '1.0.0';
type lineItem = TableHeaderConfig<FactoryListData>;
const defaultHeaders: lineItem[] = [
  {
    label: '公司编码',
    key: 'code',
    visible: true,
    width: '183px',
    type: 'text',
    disable: false,
    resizeble: true,
    pinned: false,
  },
  {
    label: '公司名称',
    key: 'name',
    visible: true,
    width: '182px',
    type: 'text',
    disable: false,
    resizeble: true,
    pinned: false,
  },
  {
    label: '公司缩写',
    key: 'abbr',
    visible: true,
    width: '183px',
    type: 'text',
    disable: false,
    resizeble: true,
    pinned: false,
  },
  {
    label: '地区',
    key: 'region',
    visible: true,
    width: '231px',
    type: 'template',
    disable: false,
    resizeble: true,
    template: 'regions',
    pinned: false,
  },
  {
    label: '状态',
    key: 'status',
    visible: true,
    width: '141px',
    type: 'template',
    disable: false,
    resizeble: true,
    template: 'statusTpl',
    pinned: false,
  },
  { label: '创建人', key: 'gen_user', visible: true, width: '143px', type: 'text', disable: false, resizeble: true, pinned: false },
  {
    label: '创建日期',
    key: 'gen_time',
    visible: true,
    width: '261px',
    type: 'datetime',
    disable: false,
    resizeble: true,
    pinned: false,
    sort: true,
  },
];

@Component({
  selector: 'app-factory-manage-list',
  templateUrl: './factory-manage-list.component.html',
  styleUrls: ['./factory-manage-list.component.scss'],
  providers: [FactoryManageService],
})
export class FactoryManageListComponent extends Resizable implements OnInit, OnDestroy, AfterViewInit {
  @ViewChildren('regions') regions!: QueryList<TemplateRef<HTMLElement>>;
  @ViewChildren('statusTpl') statusTpl!: QueryList<TemplateRef<HTMLElement>>;
  @ViewChild('searchHeader') hearderComponent?: SearchContainerComponent;
  tableHeight = 0;
  factoryStatusEnum = FactoryManageEnum;
  placeSelect!: string;
  searchData: SearchData = {
    name: null,
    abbr: null,
    region_id: null,
    status: null,
    gen_user_id: null,
    gen_time: null,
  };
  searchOptions = {
    regionOptions: [],
    statusOptions: this._service.statusLists,
  };
  loading = false;
  total = 0;
  pageIndex = 1;
  pageSize = 20;
  headers: lineItem[] = [];
  renderHeaders: lineItem[] = [];
  datalist: FactoryListData[] = [];
  header_title!: string;
  routeListerner?: any;
  orderBy: string[] = [];
  btnHighLight = false;
  id?: number;

  constructor(
    private _tableHelper: TableHelperService,
    private _router: Router,
    private _translate: TranslateService,
    public _service: FactoryManageService,
    private _commonService: CommonService,
    private _routerEventBus: FlcRouterEventBusService,
    public _storage: AppStorageService
  ) {
    super();
  }

  onSearch() {
    this.pageIndex = 1;
    this.getFactoryList();
  }

  reset() {
    this.searchData = {
      name: null,
      abbr: null,
      region_id: null,
      status: null,
      gen_user_id: null,
      gen_time: null,
    };
    this.getFactoryList(true);
  }

  handleWhere(reset = false): object {
    const where: { [key: string]: any } = {};
    if (!reset) {
      Object.entries(this.searchData).forEach((item) => {
        if (isNotNil(item[1])) {
          if (item[0] === 'region_id') {
            if (item[1].length) {
              where[item[0]] = {
                op: '=',
                value: item[1][1],
              };
            }
          } else if (item[0] === 'gen_time') {
            if (item[1].length) {
              const startTime = format(item[1][0], 'yyyy-MM-dd');
              const endTime = format(item[1][1], 'yyyy-MM-dd');
              where[item[0]] = {
                op: 'between',
                value: [startTime, endTime],
              };
            }
          } else {
            where[item[0]] = {
              op: '=',
              value: item[1],
            };
          }
        }
      });
    }
    return where;
  }

  getFactoryList(reset = false) {
    this.pageIndex = reset ? 1 : this.pageIndex;
    const where = this.handleWhere(reset) ?? {};
    this._service
      .getFactoryList({
        where: where,
        orderBy: this.orderBy,
        page: this.pageIndex,
        limit: this.pageSize,
      })
      .subscribe((res) => {
        if (res.code === 200) {
          this.datalist = res.data.factories;
          this.total = res.data.len;
        }
      });
  }

  onSort(e: any) {
    if (e === 'desc') {
      this.orderBy = ['gen_time desc'];
    } else if (e === 'asc') {
      this.orderBy = ['gen_time asc'];
    } else {
      this.orderBy = [];
    }
    this.getFactoryList();
  }

  onResize({ width }: NzResizeEvent, col: string): void {
    this.headers = this._tableHelper.tableResize<lineItem>(width, col, this.headers, version);
    this.getRenderHeaders();
  }

  ngOnInit(): void {
    super.ngOnInit();
    this.addRouterListener();
    this.headers = this._tableHelper.getTableHeaderConfig<lineItem>(defaultHeaders, version);
    this.getRenderHeaders();
    this.placeSelect = this._translate.instant('placeholder.select');
    this.header_title = this._translate.instant('工厂列表');
    this.getOptions();
    this.reset();
    if (!this._service.btnArr?.length) {
      this._service.btnArr = this._storage.getUserActions('factory/factoryManage');
    }
  }

  addRouterListener() {
    /*if (this.routeListerner) {
      this.routeListerner = null;
    }
    this.routeListerner = this._router.events
      .pipe(
        filter((event: any) => {
          return event instanceof ActivationEnd && event?.snapshot?.routeConfig?.path === 'factoryManage';
        })
      )
      .subscribe(() => {
        this.getOptions();
        this.getFactoryList();
      });*/
    this.routeListerner = this._routerEventBus.onRouterActivationEnd('/factory/factoryManage', () => {
      if (!this._service.btnArr?.length) {
        this._service.btnArr = this._storage.getUserActions('factory/factoryManage');
      }
      this.getOptions();
      this.getFactoryList();
    });
  }

  removeRouterListener() {
    this.routeListerner?.unsubscribe();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.resize();
    });
  }

  resize() {
    const headerHeight = this.hearderComponent?.headerBox?.nativeElement?.offsetHeight ?? 32;
    // 搜索行，面包屑，内容的内边距，表格与搜索行间距，表头高，页码高，页码外边距
    this.tableHeight = window.innerHeight - headerHeight - 48 - 24 - 8 - 34 - 24 - 32;

    //table最小高度
    if (this.tableHeight < 200) {
      this.tableHeight = 200;
    }
  }

  ngOnDestroy() {
    super.ngOnDestroy();
    this.removeRouterListener();
  }

  getOptions() {
    this._commonService.getRegion({}).subscribe((res) => {
      if (res.code === 200) {
        this.searchOptions.regionOptions = res.data;
      }
    });
  }

  changeHeader(event: MouseEvent): void {
    this.btnHighLight = true;
    const shadow = JSON.parse(JSON.stringify(this.headers));
    for (const item of shadow) {
      this._translate
        .get(item.label)
        .subscribe((res: string) => {
          item.label = res;
        })
        .unsubscribe();
    }
    this._tableHelper
      .openTableHeaderMidifyDialog<lineItem>(shadow, event.target as HTMLElement)
      .pipe(finalize(() => (this.btnHighLight = false)))
      .subscribe((res) => {
        if (res) {
          for (const item of res) {
            item.label = defaultHeaders.find((i) => i.key === item.key)?.label ?? '';
          }
          this.headers = res;
          this.getRenderHeaders();
          this._tableHelper.saveTableHeaderConfig(this.headers, version);
        }
      });
  }

  detail(id: number): void {
    this.id = id;
    this._router.navigate(['/factory/factoryManage', id]);
  }

  addFactory() {
    this._router.navigate(['/factory/factoryManage', 'new']);
  }

  getTemplate(template: string | undefined, index: number): TemplateRef<HTMLElement> | null {
    let returnTemplateRef: TemplateRef<HTMLElement> | null = null;
    switch (template) {
      case 'regions':
        returnTemplateRef = this.regions.toArray()[index];
        break;
      case 'statusTpl':
        returnTemplateRef = this.statusTpl.toArray()[index];
        break;
      default:
        break;
    }
    return returnTemplateRef;
  }

  getRenderHeaders() {
    this.renderHeaders = this._tableHelper.getRenderHeader<lineItem>(this.headers);
  }
}
