import { NzStepsModule } from 'ng-zorro-antd/steps';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ShowcaseComponent } from './showcase.component';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { RouterModule } from '@angular/router';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { SubcaseComponent } from './subcase/subcase.component';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { FlUiAngularModule } from 'fl-ui-angular';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzResizableModule } from 'ng-zorro-antd/resizable';
import { ComponentsModule } from 'src/app/components/components.module';

export function createTranslateFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/showcase/', '.json');
}

@NgModule({
  declarations: [ShowcaseComponent, SubcaseComponent],
  imports: [
    CommonModule,
    HttpClientModule,
    RouterModule.forChild([
      { path: '', component: ShowcaseComponent },
      { path: 'subcase', component: SubcaseComponent },
    ]),
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: createTranslateFactory,
        deps: [HttpClient],
      },
    }),
    NzButtonModule,
    NzIconModule,
    FlUiAngularModule,
    NzTableModule,
    NzResizableModule,
    ComponentsModule,
    NzSelectModule,
    NzStepsModule,
  ],
})
export class ShowcaseModule {
  constructor(public translateService: TranslateService) {
    // 切换lang必须刷新页面，模块级别i18n文件才能成功加载
    const dl = translateService.defaultLang;
    translateService.defaultLang = '';
    translateService.setDefaultLang(dl);
  }
}
