import { HttpError<PERSON><PERSON>po<PERSON>, <PERSON>tt<PERSON><PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { FlcSpUtilService } from 'fl-common-lib';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { catchError, filter, switchMap, take } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { AppStorageService } from './app-storage.service';
import { AuthService } from './auth.service';
import { NotifyService } from './notify.service';

const baseUrl = environment.baseUrl + '/' + environment.version;

const LANG_MAP: any = {
  en: 'en_XX',
  zh: 'zh_CN',
};

@Injectable()
export class HttpInterceptorService implements HttpInterceptor {
  isRefreshing = false;
  refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(null);

  constructor(
    private _auth: AuthService,
    private _storage: AppStorageService,
    private _router: Router,
    private _notify: NotifyService,
    private _notification: NzNotificationService,
    private _translate: TranslateService,
    @Inject('environment') env: any,
    private _spUtil: FlcSpUtilService
  ) {
    this.env = env;
  }

  env: any;

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<any> {
    if (req.url.indexOf('i18n') !== -1) {
      return next.handle(req);
    }

    let authReq: HttpRequest<any>;
    authReq = this.addTokenHeader(req, this._storage.getAccessToken());
    authReq = authReq.clone({
      url: this.isCommonServiceUrl(req.url) ? req.url : baseUrl.concat(req.url),
      setHeaders: {
        ResourceCode: this._storage.ResourceCode,
        lang: LANG_MAP[this._translate.currentLang],
      },
    });

    return next.handle(authReq).pipe(
      catchError((error) => {
        if (error instanceof HttpErrorResponse) {
          switch (error.status) {
            case 400: // 业务错误
              if (error.error.reason === 'INVALID_ACCESS') {
                this._notification.remove();
                this.logout();
                break;
              } else {
                this.handle400ErrorMsg(error.error);
                return throwError(error);
              }
              break;
            case 401: // access token失效,重新renew
              return this.handle401Error(authReq, next);
            case 403: // 禁止访问，退出系统
              this._notification.remove();
              this.logout();
              break;
            case 404: // 接口未找到
              break;
            case 500: //系统错误
              break;
            default:
              if (error.status > 400 && error.status < 600) {
                break;
              } else if (!navigator.onLine) {
                this._notify.notify('NOINTERNET', 0);
                return throwError(error);
              }
              break;
          }
          this._notify.notify(error.error.reason, error.error.code);
        }

        return throwError(error);
      })
    );
  }

  logout() {
    // 系统强制退出登录（400/403）时，设置forceLogout-true，leaveguard会根据forceLogout判断是否直接跳转
    this._spUtil.putBoolean('forceLogout', true);

    setTimeout(() => {
      this._router.navigate(['/login']).then(() => {
        window.location.reload();
        this._storage.signout();
      });
    }, 3000);
  }

  handle400Error(error: any) {
    this._notify.notify(error.reason, error.code);
  }

  handle400ErrorMsg(error: any) {
    if (error.message) {
      this._notification.error(this._translate.instant('bizErrorNotification.prompt'), error.message);
    }
  }

  handle401Error(req: HttpRequest<any>, next: HttpHandler) {
    if (!this.isRefreshing) {
      this.isRefreshing = true;
      this.refreshTokenSubject.next(null);

      return this._auth.refreshAccessToken().pipe(
        //first(),
        switchMap((res: any) => {
          this.isRefreshing = false;

          const jwt = res.data.jwt;

          // 处理jwt更新返回空
          if (jwt === null) {
            this.handle400ErrorMsg({ message: '登录失效，转跳登录页' });
            this.logout();
            return throwError({});
          }

          this._storage.saveAccessToken(jwt.access_token);
          this._storage.saveRefreshToken(jwt.refresh_token);

          this.refreshTokenSubject.next(jwt.access_token);

          return next.handle(this.addTokenHeader(req, jwt.access_token)).pipe(
            catchError((err) => {
              // refresh token 之后，请求返回有错误代码400的场景，给出提示
              if (err.status === 400) {
                this.handle400ErrorMsg(err.error);
              }
              return throwError(err);
            })
          );
        })
      );
    }

    return this.refreshTokenSubject.pipe(
      filter((token) => token !== null),
      take(1),
      switchMap((token) => {
        return next.handle(this.addTokenHeader(req, token));
      })
    );
  }

  addTokenHeader(req: HttpRequest<any>, token: string | null) {
    if (token) {
      return req.clone({
        setHeaders: { Authorization: 'Bearer ' + token },
      });
    }
    return req;
  }

  isCommonServiceUrl(reqUrl: string) {
    return ['service'].includes(reqUrl.split('/')[1]);
  }
}
