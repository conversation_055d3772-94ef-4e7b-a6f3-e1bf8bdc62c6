<div class="outer-container">
  <div #searchBar style="padding-bottom: 12px" *ngIf="isShowSearchContainer">
    <flc-search-container [headerTitle]="'secProcess.二次工艺报表列表' | translate" [showBtnContainer]="false">
      <ng-container *ngFor="let item of searchOptions">
        <div class="search-item-container" *ngIf="item.visible">
          <span class="search-name">{{ 'secProcess.' + item.label | translate }}</span>
          <ng-container *ngIf="item.type === 'select'">
            <flc-dynamic-search-select
              [dataUrl]="searchOptionUrl"
              [transData]="{ value: 'value', label: 'label' }"
              [(ngModel)]="searchData[item.key]"
              [column]="item.key"
              (ngModelChange)="onSearch(true)">
            </flc-dynamic-search-select>
          </ng-container>
          <ng-container *ngIf="item.type === 'local-select'">
            <nz-select
              [nzPlaceHolder]="'placeholder.select' | translate"
              nzAllowClear
              [(ngModel)]="searchData[item.key]"
              (ngModelChange)="onSearch(true)">
              <nz-option *ngFor="let item of item?.options" [nzLabel]="item.label" [nzValue]="item.value"></nz-option>
            </nz-select>
          </ng-container>
          <ng-container *ngIf="item.type === 'dateRange'">
            <nz-range-picker
              [nzSuffixIcon]="''"
              [nzPlaceHolder]="['secProcess.开始' | translate, 'secProcess.结束' | translate]"
              [(ngModel)]="searchData[item.key]"
              (ngModelChange)="onSearch(true)">
            </nz-range-picker>
          </ng-container>
        </div>
      </ng-container>
    </flc-search-container>
  </div>

  <app-production-report-tab
    [(selectedTab)]="selectedTab"
    [(currentDimension)]="currentDimension"
    (selectedTabChange)="onTabChange()"
    (currentDimensionChange)="onDimensionChange()"
    (handleReset)="onReset()"
    (handleToggle)="onToggle()">
  </app-production-report-tab>

  <div style="background-color: #fff; padding: 12px 4px 12px 12px">
    <nz-table
      #secTable
      [nzFrontPagination]="false"
      [(nzPageSize)]="tableConfig.pageSize"
      [(nzPageIndex)]="tableConfig.pageIndex"
      [nzShowTotal]="totalTemplate"
      [nzTotal]="tableConfig.count"
      [nzPageSizeOptions]="[20, 30, 40, 50]"
      (nzPageIndexChange)="onIndexChange($event)"
      (nzPageSizeChange)="onSizeChange($event)"
      nzShowSizeChanger
      [nzData]="tableConfig.dataList"
      [nzLoading]="tableConfig.loading"
      [nzScroll]="{ x: '100%', y: tableConfig.height + 'px' }"
      [nzPaginationType]="'small'"
      nzBordered>
      <thead>
        <tr>
          <th nzLeft="0px" nzWidth="46px">#</th>
          <th nzLeft="46px" nzWidth="88px" *ngIf="selectedTab === 1">{{ 'secProcess.日期' | translate | translate }}</th>
          <th [nzLeft]="selectedTab === 1 ? '134px' : '46px'" nzWidth="144px">{{ 'secProcess.大货单号' | translate | translate }}</th>
          <th [nzLeft]="selectedTab === 1 ? '278px' : '190px'" nzWidth="144px">{{ 'secProcess.款式编码' | translate }}</th>
          <th nzWidth="144px" *ngIf="currentDimension === 2">{{ 'secProcess.交付单' | translate }}</th>
          <th nzWidth="104px">
            {{ 'secProcess.交期' | translate }}
            <flc-sort-btn [(sortOrder)]="dueSort" (sortOrderChange)="onSortChange($event, 'due_time')"></flc-sort-btn>
          </th>
          <th nzWidth="144px">{{ 'secProcess.工艺' | translate }}</th>
          <th nzWidth="144px">{{ 'secProcess.部位' | translate }}</th>
          <th nzWidth="144px">{{ 'secProcess.外发厂' | translate }}</th>
          <th nzWidth="144px">{{ 'secProcess.分配订单数' | translate }}</th>
          <ng-container *ngIf="selectedTab === 1">
            <th nzWidth="144px">{{ 'secProcess.日收货（裁片）' | translate }}</th>
            <th nzWidth="144px">{{ 'secProcess.累计收货' | translate }}</th>
            <th nzWidth="144px">{{ 'secProcess.日完成' | translate }}</th>
            <th nzWidth="144px">{{ 'secProcess.累计完成' | translate }}</th>
            <th nzWidth="144px">{{ 'secProcess.日合格/不良' | translate }}</th>
            <th nzWidth="144px">{{ 'secProcess.累计合格/不良' | translate }}</th>
            <th nzWidth="144px">{{ 'secProcess.日发货' | translate }}</th>
            <th nzWidth="144px">{{ 'secProcess.累计发货' | translate }}</th>
          </ng-container>
          <ng-container *ngIf="selectedTab === 2">
            <th nzWidth="144px">{{ 'secProcess.已裁数（裁片）' | translate }}</th>
            <th nzWidth="144px">{{ 'secProcess.外发数' | translate }}</th>
            <th nzWidth="144px">{{ 'secProcess.收货数' | translate }}</th>
            <th nzWidth="144px">{{ 'secProcess.在途数' | translate }}</th>
            <th nzWidth="144px">{{ 'secProcess.完成数' | translate }}</th>
            <th nzWidth="144px">{{ 'secProcess.合格/不良' | translate }}</th>
            <th nzWidth="144px">{{ 'secProcess.已发货' | translate }}</th>
            <th nzWidth="144px">{{ 'secProcess.进度' | translate }}</th>
            <th nzWidth="104px">{{ 'secProcess.状态' | translate }}</th>
            <th nzRight nzWidth="132px">{{ 'secProcess.操作' | translate }}</th>
          </ng-container>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let item of secTable.data; index as i">
          <ng-container *ngFor="let po of item?.po_list; index as poIndex">
            <ng-container *ngFor="let extra of po?.extra_process_list; index as exIndex">
              <ng-container *ngFor="let sub of extra?.sub_list; index as subIndex">
                <tr [ngClass]="{ 'selected-row': setTdBackground(item, po, extra, sub) }">
                  <ng-container *ngIf="!poIndex && !exIndex && !subIndex">
                    <td nzLeft="0px" [rowSpan]="item?.len">
                      {{ i + 1 }}
                    </td>
                    <td nzLeft="46px" [rowSpan]="item?.len" *ngIf="selectedTab === 1">
                      <span *ngIf="item?.biz_date; else noValueTpl">{{ item?.biz_date | date: 'MM/dd' }}</span>
                    </td>
                    <td [nzLeft]="selectedTab === 1 ? '134px' : '46px'" [rowSpan]="item?.len">
                      <span *ngIf="item?.io_code; else noValueTpl">{{ item?.io_code }}</span>
                    </td>
                    <td [nzLeft]="selectedTab === 1 ? '278px' : '190px'" [rowSpan]="item?.len">
                      <span *ngIf="item?.customer_style; else noValueTpl">{{ item?.customer_style }}</span>
                    </td>
                  </ng-container>

                  <ng-container *ngIf="!exIndex && !subIndex">
                    <td [attr.rowspan]="po?.len" *ngIf="currentDimension === 2">
                      <span *ngIf="po?.po_code; else noValueTpl">{{ po?.po_code }}</span>
                    </td>
                    <td [attr.rowspan]="po?.len">
                      <div
                        *ngIf="po?.due_time_list?.length; else noValueTpl"
                        nz-tooltip
                        [nzTooltipTrigger]="po?.due_time_overflow ? 'hover' : null"
                        [nzTooltipTitle]="due_time_tpl"
                        class="due_times">
                        <span *ngFor="let due of po?.due_time_arr; let due_idx = index"
                          >{{ due }}{{ due_idx === po?.due_time_arr?.length - 1 && po?.due_time_overflow ? '...' : '' }}</span
                        >
                      </div>
                      <ng-template #due_time_tpl>
                        <div *ngFor="let due of po.due_time_list">{{ due }}</div>
                      </ng-template>
                    </td>
                  </ng-container>

                  <ng-container *ngIf="!subIndex">
                    <!-- 工艺 -->
                    <td [attr.rowspan]="extra?.len">
                      <flc-text-truncated [data]="extra?.extra_process_name"></flc-text-truncated>
                    </td>
                    <!-- 部位 -->
                    <td [attr.rowspan]="extra?.len">
                      <flc-text-truncated [data]="extra?.position_name"></flc-text-truncated>
                    </td>
                  </ng-container>
                  <!-- 外发厂 -->
                  <td>
                    <flc-text-truncated [data]="sub?.factory_name"></flc-text-truncated>
                  </td>
                  <!-- 分配订单数 -->
                  <td>{{ sub?.order_count ?? 0 | number }}</td>

                  <ng-container *ngIf="selectedTab === 1">
                    <!-- 日收货（裁片） -->
                    <td>
                      <ng-container
                        *ngTemplateOutlet="
                          cellTpl;
                          context: { $implicit: sub?.daily_received_qty, key: 'daily_received_qty' }
                        "></ng-container>
                    </td>
                    <!-- 累计收货 -->
                    <td>
                      <ng-container
                        *ngTemplateOutlet="cellTpl; context: { $implicit: sub?.received_qty, key: 'received_qty' }"></ng-container>
                    </td>
                    <!-- 日完成 -->
                    <td>
                      <ng-container
                        *ngTemplateOutlet="
                          cellTpl;
                          context: { $implicit: sub?.daily_finished_qty, key: 'daily_finished_qty' }
                        "></ng-container>
                    </td>
                    <!-- 累计完成 -->
                    <td>
                      <ng-container
                        *ngTemplateOutlet="cellTpl; context: { $implicit: sub?.finished_qty, key: 'finished_qty' }"></ng-container>
                    </td>
                    <!-- 日合格/不良 -->
                    <td>
                      <ng-container
                        *ngTemplateOutlet="
                          qualityTpl;
                          context: { qualified: sub?.daily_qualified_qty, defective: sub?.daily_defective_qty, isDaily: true }
                        "></ng-container>
                    </td>
                    <!-- 累计合格/不良 -->
                    <td>
                      <ng-container
                        *ngTemplateOutlet="
                          qualityTpl;
                          context: { qualified: sub?.qualified_qty, defective: sub?.defective_qty, isDaily: false }
                        "></ng-container>
                    </td>
                    <!-- 日发货 -->
                    <td>
                      <ng-container
                        *ngTemplateOutlet="
                          cellTpl;
                          context: { $implicit: sub?.daily_delivered_qty, key: 'daily_delivered_qty' }
                        "></ng-container>
                    </td>
                    <!-- 累计发货 -->
                    <td>
                      <ng-container
                        *ngTemplateOutlet="cellTpl; context: { $implicit: sub?.delivered_qty, key: 'delivered_qty' }"></ng-container>
                    </td>
                  </ng-container>

                  <ng-container *ngIf="selectedTab === 2">
                    <!-- 已裁数（裁片）-->
                    <td>
                      <ng-container
                        *ngTemplateOutlet="cellTpl; context: { $implicit: sub?.cutting_qty, key: 'cutting_qty' }"></ng-container>
                    </td>
                    <!-- 外发数 -->
                    <td>
                      <ng-container
                        *ngTemplateOutlet="cellTpl; context: { $implicit: sub?.outsource_qty, key: 'outsource_qty' }"></ng-container>
                    </td>
                    <!-- 收货数 -->
                    <td>
                      <ng-container
                        *ngTemplateOutlet="cellTpl; context: { $implicit: sub?.received_qty, key: 'received_qty' }"></ng-container>
                    </td>
                    <!-- 在途数 -->
                    <td>
                      <ng-container
                        *ngTemplateOutlet="cellTpl; context: { $implicit: sub?.transit_qty, key: 'transit_qty' }"></ng-container>
                    </td>
                    <!-- 完成数 -->
                    <td>
                      <ng-container
                        *ngTemplateOutlet="cellTpl; context: { $implicit: sub?.finished_qty, key: 'finished_qty' }"></ng-container>
                    </td>
                    <!-- 合格/不良 -->
                    <td>
                      <ng-container
                        *ngTemplateOutlet="
                          qualityTpl;
                          context: { qualified: sub?.qualified_qty, defective: sub?.defective_qty, isDaily: false }
                        "></ng-container>
                    </td>
                    <!-- 已发货 -->
                    <td>
                      <ng-container
                        *ngTemplateOutlet="cellTpl; context: { $implicit: sub?.delivered_qty, key: 'delivered_qty' }"></ng-container>
                    </td>
                    <!-- 进度 -->
                    <td>{{ sub?.progress ?? 0 | percent: '1.0-1' }}</td>
                    <!-- 状态 -->
                    <td>
                      <span [ngClass]="['status-text' + sub?.status, 'status']">{{ statusValue[sub?.status] }}</span>
                    </td>
                    <!-- 操作 -->
                    <td nzRight>
                      <a class="operate-btn" nz-button nzType="link" (click)="onViewDetail($event, item, po, extra, sub)">
                        {{ 'secProcess.外发生产监控' | translate }}
                      </a>
                    </td>
                  </ng-container>

                  <ng-template #cellTpl let-val let-key="key">
                    <span [ngClass]="{ 'blue-link-text': val }" (click)="val && onOpenModal(key, item, po, extra, sub)">{{
                      val ?? 0 | number
                    }}</span>
                  </ng-template>

                  <ng-template #qualityTpl let-qualified="qualified" let-defective="defective" let-isDaily="isDaily">
                    <span
                      [ngClass]="{ 'blue-link-text': qualified }"
                      (click)="qualified && onOpenModal(isDaily ? 'daily_qualified_qty' : 'qualified_qty', item, po, extra, sub)"
                      >{{ qualified ?? 0 | number }}</span
                    >
                    /
                    <span
                      [ngClass]="{ 'red-link-text': defective }"
                      (click)="defective && onOpenModal(isDaily ? 'daily_defective_qty' : 'defective_qty', item, po, extra, sub)"
                      >{{ defective ?? 0 | number }}</span
                    >
                  </ng-template>
                </tr>
              </ng-container>
            </ng-container>
          </ng-container>
        </ng-container>

        <!-- 合计 -->
        <tr *ngIf="tableConfig?.dataList?.length" class="total-row">
          <td nzLeft="0px">合计</td>
          <td nzLeft="46px" *ngIf="selectedTab === 1"></td>
          <td [nzLeft]="selectedTab === 1 ? '134px' : '46px'"></td>
          <td [nzLeft]="selectedTab === 1 ? '278px' : '190px'"></td>
          <td *ngIf="currentDimension === 2"></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td>{{ sumData?.order_count ?? 0 | number }}</td>
          <ng-container *ngIf="selectedTab === 1">
            <td>{{ sumData?.daily_received_qty ?? 0 | number }}</td>
            <td>{{ sumData?.received_qty ?? 0 | number }}</td>
            <td>{{ sumData?.daily_finished_qty ?? 0 | number }}</td>
            <td>{{ sumData?.finished_qty ?? 0 | number }}</td>
            <!-- 日合格/不良 -->
            <td>
              <span>{{ sumData?.daily_qualified_qty ?? 0 | number }}</span>
              /
              <span [ngClass]="{ red: sumData?.daily_defective_qty }">{{ sumData?.daily_defective_qty ?? 0 | number }}</span>
            </td>
            <!-- 累计合格/不良 -->
            <td>
              <span>{{ sumData?.qualified_qty ?? 0 | number }}</span>
              /
              <span [ngClass]="{ red: sumData?.defective_qty }">{{ sumData?.defective_qty ?? 0 | number }}</span>
            </td>
            <td>{{ sumData?.daily_delivered_qty ?? 0 | number }}</td>
            <td>{{ sumData?.delivered_qty ?? 0 | number }}</td>
          </ng-container>

          <ng-container *ngIf="selectedTab === 2">
            <td>{{ sumData?.cutting_qty ?? 0 | number }}</td>
            <td>{{ sumData?.outsource_qty ?? 0 | number }}</td>
            <td>{{ sumData?.received_qty ?? 0 | number }}</td>
            <td>{{ sumData?.transit_qty ?? 0 | number }}</td>
            <td>{{ sumData?.finished_qty ?? 0 | number }}</td>
            <!-- 合格/不良 -->
            <td>
              <span>{{ sumData?.qualified_qty ?? 0 | number }}</span>
              /
              <span [ngClass]="{ red: sumData?.defective_qty }">{{ sumData?.defective_qty ?? 0 | number }}</span>
            </td>
            <td>{{ sumData?.delivered_qty ?? 0 | number }}</td>
            <td>{{ sumData?.progress ?? 0 | percent: '1.0-1' }}</td>
            <td></td>
            <td nzRight></td>
          </ng-container>
        </tr>
      </tbody>
    </nz-table>
    <ng-template #totalTemplate>
      <div style="margin-right: 16px; color: #000000; font-size: 14px">
        共 {{ tableConfig?.count }} 条， 第 <span style="color: #0f86f8">{{ tableConfig?.pageIndex }}</span> /
        {{ tableConfig?.count / tableConfig?.pageSize | flcMathCeil }} 页
      </div>
    </ng-template>
  </div>
</div>

<ng-template #noValueTpl>
  <span style="color: #b5b8bf">-</span>
</ng-template>
