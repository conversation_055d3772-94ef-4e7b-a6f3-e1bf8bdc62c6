:host {
  .btn-box {
    button:not(:last-child) {
      margin-right: 8px;
    }
  }
}

// Tool class
// border bottom radius 12px, 设置下边框圆角
.border-bottom-radius-12 {
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}

.border-top-radius-12 {
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}

.go-container {
  // 退回原因
  .reason-container {
    display: flex;
    align-items: center;
    background-color: #fee4e4;
    padding: 0 12px 4px 12px;
    border-radius: 12px 12px 0px 0px;
    color: #f74949;

    & > span {
      flex-basis: 100px;
      margin-left: 4px;
      font-size: 16px;
      flex-shrink: 0;
    }
  }

  // 虚线
  .dotted-line {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      margin: 0 auto;
      display: block;
      width: calc(100% - 16px);
      border-bottom: 1px solid transparent;
      padding: 1px 0;
      background: linear-gradient(white, white) padding-box, repeating-linear-gradient(-45deg, #ccc 0, #ccc 0.25em, white 0, white 0.5em);
    }
  }

  .factory-dotted-line {
    margin: 12px 0 9px 0;
    width: 100%;
    height: 1px;

    &::after {
      width: 100%;
    }
  }

  // 表单 form
  &::ng-deep .ant-form-item-control {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  &::ng-deep .ant-form-item-label {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  &::ng-deep .form-item {
    padding-right: 0 !important;
  }

  &::ng-deep .ant-form-item {
    margin-bottom: 0;
  }

  // 选择大货单
  .io-container {
    background-color: #ffffff;
    padding: 8px 12px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    margin-top: -4px;
    @extend .border-bottom-radius-12;
    &::ng-deep .card-container {
      padding: 0 !important;
    }
  }

  ::ng-deep.table-outer-container {
    padding: 8px 0;
  }

  ::ng-deep .rest-color-size-table-padding .table-outer-container {
    padding: 8px;
  }
}

.head-title {
  display: flex;
  align-items: center;
}
.status {
  display: inline-block;
  height: 22px;
  font-size: 14px;
  padding: 0 8px;
  line-height: 22px;
  border-radius: 12px;
  margin-left: 6px;
}
.status1,
.status11 {
  // 待提交 审核通过 1 11
  background: #e7f3fe;
  color: #138aff;
}
.status2,
.status9 {
  // 待审核2
  background: #feefe5;
  color: #fc8334;
}
.status4,
.status10 {
  // 待修改 4
  background: #ffe8e4;
  color: #ff4a1d;
}
.status8 {
  // 已取消 8
  background: #fafbfd;
  color: #97999c;
}

// 没有数据展示
.not-data-container {
  background-color: #ffffff;
  height: calc(100vh - 160px);
  display: flex;
  padding-top: 13%;
  justify-content: center;
  @extend .border-bottom-radius-12;
  @extend .border-top-radius-12;

  span {
    color: #97999c;
    margin-left: 20px;
  }
}

.process-first {
  padding: 16px 0 14px 98px;
  background: #ffffff;
  border-radius: 12px 12px 4px 4px;
  position: relative;
  margin-bottom: 8px;
  overflow: hidden;

  .process-title {
    position: absolute;
    z-index: 6;
    top: 12px;
    left: 12px;
    width: 72px;
    height: 32px;
    color: #56607d;
    font-size: 14px;
    line-height: 32px;
    font-weight: 500;
    text-align: center;
  }

  .first-desc {
    margin-top: 10px;
    width: 148px;
    justify-content: center;
    color: #515665;
    font-size: 14px;
    font-weight: 500;
    overflow: hidden;
    display: flex;

    span {
      display: block;
      float: left;
      text-align: center;
      overflow: hidden;
    }

    span:first-child {
      width: 60px;
    }

    span:last-child {
      flex: 80px;
    }
  }

  .process-item-num {
    float: left;
    width: 200px;
  }

  .process-item {
    float: left;
    width: 200px;
    position: relative;
    flex-shrink: 1;

    &::before {
      content: '';
      position: absolute;
      // left: -48px;
      top: 15px;
      width: 28%;
      transform: translateX(-100%);
      height: 1px;
      background: #d4d7dc;
    }

    &:first-child::before {
      content: none;
    }

    .first-item {
      width: 148px;
      height: 31px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 16px;
      color: #54607c;
      font-size: 14px;
      line-height: 14px;
      background: #f8fafe;
      border-radius: 15px;
      border: 1px solid #eeeeee;
      font-weight: 500;
      position: relative;
      z-index: 1;

      span {
        text-align: center;
      }

      span:first-child {
        width: 60px;
      }

      span:last-child {
        flex: 80px;
      }

      .item-sum {
        flex: 6;
        font-size: 12px;
        line-height: 12px;
        color: #54607c;
      }
    }
  }

  .item-actived {
    .first-item {
      background: #e7f3fe;
      color: #138aff;
      border: 1px solid transparent;
    }

    .first-desc {
      .process-num {
        color: #138aff;
      }
    }

    &::before {
      background: #7fbcff;
    }
  }
}

.surplus-no-data {
  &::ng-deep .flc-no-data-container {
    margin-top: 24px;
    margin-bottom: 20px;
  }
}

.pre-order-tag {
  height: 16px;
  border: 1px solid #ef6c69;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  line-height: 14px;
  box-sizing: border-box;
  text-align: center;
  color: #ef6c69;
  margin-left: 5px;
  padding: 0 5px;
}
