import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ResponseData } from '../../common/interface/http';

const trackingListUrl = '/service/procurement-inventory/tracking_schedule/v1/list';
const trackingListOptionUrl = '/service/procurement-inventory/tracking_schedule/v1/list-option';
const trackingDetailSaveUrl = '/service/procurement-inventory/tracking_schedule/v1/save';

@Injectable({
  providedIn: 'root',
})
export class OrderTrackingService {
  get optionUrl(): string {
    return trackingListOptionUrl;
  }
  constructor(private http: HttpClient) {}
  getOrderTrackingList(data: { page: number; size: number; code?: string; customer?: string; style_code?: string; order_by?: string[] }) {
    return this.http.post<ResponseData<any>>(trackingListUrl, data);
  }
  saveOrderTrackingDetail(data: any) {
    return this.http.post<ResponseData<any>>(trackingDetailSaveUrl, data);
  }
}
