import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { OrderGarmentOutsourcingRoutingModule } from './order-garment-outsourcing-routing.module';

import { FlButtonModule } from 'fl-ui-angular/button';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzCascaderModule } from 'ng-zorro-antd/cascader';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzSpinModule } from 'ng-zorro-antd/spin';

import { FlcComponentsModule, FlcDirectivesModule } from 'fl-common-lib';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { OrderGarmentOutsourcingListComponent } from './order-garment-outsourcing-list/order-garment-outsourcing-list.component';
import { OrderGarmentOutsourcingDetailComponent } from './order-garment-outsourcing-detail/order-garment-outsourcing-detail.component';
import { OrderGarmentOutsourcingService } from './order-garment-outsourcing.service';
import { OrderOutsourcingComponentsModule } from '../components/order-outsourcing-components.module';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import { RecommendFactoryModule } from 'fl-sewsmart-lib/recommend-factory';
import { CommonComponentModule } from 'fl-sewsmart-lib/common-component';
import { AssignEmpDrawer } from './components/assign-emp-drawer/assign-emp-drawer.component';

const nzModules = [
  NzIconModule,
  NzButtonModule,
  NzTableModule,
  NzDatePickerModule,
  NzFormModule,
  NzSelectModule,
  NzInputModule,
  NzInputNumberModule,
  NzPopoverModule,
  NzCascaderModule,
  NzToolTipModule,
  NzTabsModule,
  NzDividerModule,
  NzModalModule,
  NzSwitchModule,
  NzSpinModule,
];
@NgModule({
  declarations: [OrderGarmentOutsourcingListComponent, OrderGarmentOutsourcingDetailComponent, AssignEmpDrawer],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    OrderOutsourcingComponentsModule,
    FormsModule,
    OrderGarmentOutsourcingRoutingModule,
    FlButtonModule,
    FlcComponentsModule,
    FlcDirectivesModule,
    RecommendFactoryModule,
    CommonComponentModule,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new MultiTranslateHttpLoader(http, [
            { prefix: './assets/i18n/outsourcing-manage/garm-sec-outsourcing/', suffix: '.json' },
            { prefix: './assets/i18n/table-data-view/', suffix: '.json' },
          ]);
        },
        deps: [HttpClient],
      },
    }),
    ...nzModules,
  ],
  providers: [OrderGarmentOutsourcingService],
})
export class OrderGarmentOutsourcingModule {
  constructor(public translateService: TranslateService, private _service: OrderGarmentOutsourcingService) {
    const cl = translateService.currentLang;
    translateService.currentLang = '';
    translateService.use(cl).subscribe(() => {
      this._service.translateEventEmitter.emit();
    });
  }
}
