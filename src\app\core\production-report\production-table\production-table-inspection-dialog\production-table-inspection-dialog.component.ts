import { Component, EventEmitter, Input, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { FlcValidatorService } from 'fl-common-lib';
import { intersection, round, uniq, uniqBy } from 'lodash';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import {
  ProductionTableColorSizeLine,
  ProductionTableNodeDetailItem,
  ProductionTableOrderLineInfo,
  ProductionTableOrderNode,
} from '../production-table.config';
import { ProductionTableService } from '../production-table.service';

@Component({
  selector: 'app-production-table-inspection-dialog',
  templateUrl: './production-table-inspection-dialog.component.html',
  styleUrls: ['./production-table-inspection-dialog.component.scss'],
})
export class ProductionTableInspectionDialogComponent implements OnInit {
  @Input() nodeInfo!: ProductionTableOrderNode;
  @Input() orderInfo!: ProductionTableOrderLineInfo;
  @Output() closeModal = new EventEmitter<boolean>();
  @ViewChild('inspectionHistoryTemplate') inspectionHistoryTemplate!: TemplateRef<HTMLElement>;
  rawLineList: ProductionTableColorSizeLine[] = [];
  colorList: ColorItem[] = [];
  displaySizeList: SizeItem[] = [];
  displayItem = { qualified_qty: 0, qualifiedPercent: 0, defectedPercent: 0 };
  inspectionForm = this._fb.group({
    color_list: [null, [Validators.required]],
    size_list: [null, [Validators.required]],
    remark: [null],
    inspected_status: [1, [Validators.required]],
    inspected_qty: [null, [Validators.required, Validators.min(1)]],
    defected_qty: [0],
    pictures: [[]],
  });
  inspectionHistoryList: HistoryLine[] = [];
  inspectionTotalInfo?: {
    inspected_qty: number;
    qualified_qty: number;
    qualifiedPercent: number;
    defected_qty: number;
    defectedPercent: number;
  };
  historyModal?: NzModalRef<unknown, any>;
  translateName = 'ProductionTableTemplateFiled.';
  lang = localStorage.getItem('lang') || 'zh';
  basicInfoConfig: any = [
    { label: '订单需求号', key: 'bulk_order_code' },
    { label: '款式编码', key: 'style_code' },
    { label: '加工厂', key: 'factory_name' },
  ];

  constructor(
    private _service: ProductionTableService,
    private _notice: NzNotificationService,
    private _modal: NzModalService,
    private _flcFormService: FlcValidatorService,
    private _fb: FormBuilder
  ) {}

  ngOnInit(): void {
    this.getNodeInfo();
  }
  getNodeInfo() {
    this._service
      .getNodeInfo({
        bulk_order_id: this.orderInfo.bulk_order_id,
        factory_out_sourcing_id: this.orderInfo.factory_out_sourcing_id,
        node_id: this.nodeInfo.id,
        node_type: this.nodeInfo.node_type,
      })
      .subscribe((result: { code: number; data: ProductionTableNodeDetailItem }) => {
        if (result.code === 200) {
          this.rawLineList = result.data.line_list;
          this.handleRawLineList();
        }
      });
  }
  handleRawLineList() {
    const colorMap: Map<number, ColorItem> = new Map();
    this.rawLineList.forEach((item) => {
      const targetColor = colorMap.get(item.color_id);
      if (!targetColor) {
        colorMap.set(item.color_id, {
          color_id: item.color_id,
          color_name: item.color_name,
          sizeList: [{ spec_id: item.spec_id, spec_size: item.spec_size }],
        });
      } else {
        targetColor.sizeList.push({ spec_id: item.spec_id, spec_size: item.spec_size });
      }
    });
    this.colorList = Array.from(colorMap.values());
    this.colorList.forEach((color) => {
      color.sizeList = uniqBy(color.sizeList, 'spec_id');
    });
  }
  handleChangeQty() {
    const { inspected_qty, defected_qty } = this.inspectionForm.value;
    if (inspected_qty > 0) {
      const { qualified_qty, qualifiedPercent, defectedPercent } = this.handleQtyAndPercentCalc({
        inspected_qty,
        defected_qty: defected_qty ?? 0,
      });
      this.displayItem = { qualified_qty, qualifiedPercent, defectedPercent };
    }
  }
  handleQtyAndPercentCalc(input: any) {
    const { inspected_qty, defected_qty } = input;
    let qualified_qty = inspected_qty - defected_qty;
    let qualifiedPercent = round((qualified_qty / inspected_qty) * 100, 2);
    let defectedPercent = round((defected_qty / inspected_qty) * 100, 2);
    return { qualified_qty, qualifiedPercent, defectedPercent };
  }
  handleChangeColor(event: number[]) {
    const displaySize: SizeItem[] = [];
    event.forEach((colorId) => {
      const targetColor = this.colorList.find((item) => item.color_id === colorId);
      if (targetColor) {
        displaySize.push(...targetColor.sizeList);
      }
    });
    const sizeCtrl = this.inspectionForm.get('size_list')!;
    this.displaySizeList = uniqBy(displaySize, 'spec_id');
    const displaySizeIds = this.displaySizeList.map((item) => item.spec_id);
    const targetSizeList = intersection(displaySizeIds, sizeCtrl.value);
    sizeCtrl.setValue(targetSizeList);
  }

  uploadImage(event: any) {
    this.inspectionForm.get('pictures')?.setValue(event);
  }
  changeInsecptionStatus(status: number) {
    this.inspectionForm.get('inspected_status')?.setValue(status);
  }
  showInspectionHistory() {
    const payload = {
      node_id: this.nodeInfo.id,
      node_type: this.nodeInfo.node_type,
    };
    this._service.getNodeHistory(payload).subscribe((result) => {
      if (result.code === 200) {
        this.inspectionHistoryList = result.data.line_list;
        this.inspectionTotalInfo = {
          inspected_qty: 0,
          defected_qty: 0,
          qualified_qty: 0,
          qualifiedPercent: 0,
          defectedPercent: 0,
        };
        this.inspectionHistoryList.forEach((line) => {
          this.inspectionTotalInfo!.inspected_qty += line.inspected_qty;
          this.inspectionTotalInfo!.defected_qty += line.defected_qty;
          line.displayColors = uniq(line.color_list.map((color) => color.color_name)).join('、');
          line.displaySizes = uniq(line.spec_list.map((spec) => spec.spec_size)).join('、');
          const { defectedPercent, qualifiedPercent, qualified_qty } = this.handleQtyAndPercentCalc(line);
          line.defectedPercent = defectedPercent;
          line.qualifiedPercent = qualifiedPercent;
          line.qualified_qty = qualified_qty;
        });
        const { defectedPercent, qualifiedPercent, qualified_qty } = this.handleQtyAndPercentCalc(this.inspectionTotalInfo);
        this.inspectionTotalInfo!.qualified_qty = qualified_qty;
        this.inspectionTotalInfo!.qualifiedPercent = qualifiedPercent;
        this.inspectionTotalInfo!.defectedPercent = defectedPercent;
        this.historyModal = this._modal.create({
          nzTitle: this._service.translateValue(this.translateName + '历史记录'),
          nzFooter: null,
          nzWidth: 850,
          nzContent: this.inspectionHistoryTemplate,
          nzBodyStyle: {
            padding: '0 12px',
          },
        });
      }
    });
  }
  save() {
    if (this._flcFormService.formIsInvalid(this.inspectionForm)) {
      return;
    }
    const line_list: {
      color_id: number;
      color_name: string;
      spec_id: number;
      spec_size: string;
    }[] = [];
    const selectedColorIds: number[] = this.inspectionForm.get('color_list')?.value;
    const selectedSizeIds: number[] = this.inspectionForm.get('size_list')?.value;
    const selectColorList = this.colorList.filter((color) => selectedColorIds.includes(color.color_id));
    selectColorList.forEach((color) => {
      const selectedSizeList = color.sizeList.filter((size) => selectedSizeIds.includes(size.spec_id));
      selectedSizeList.forEach((size) => {
        line_list.push({
          color_id: color.color_id,
          color_name: color.color_name,
          spec_id: size.spec_id,
          spec_size: size.spec_size,
        });
      });
    });
    const payload: { id: number; content_type: number; node_list: uploadNodeItem[] } = {
      id: this.nodeInfo.id,
      content_type: 3,
      node_list: [
        {
          finished_time: new Date().getTime(),
          node_type: this.nodeInfo.node_type,
          remark: this.inspectionForm.get('remark')?.value,
          pictures: this.inspectionForm.get('pictures')?.value,
          inspected_qty: this.inspectionForm.get('inspected_qty')?.value,
          defected_qty: this.inspectionForm.get('defected_qty')?.value,
          inspected_status: this.inspectionForm.get('inspected_status')?.value,
          line_list,
        },
      ],
    };
    this._service.updateNode(payload).subscribe((result) => {
      if (result.code === 200) {
        this._notice.success('', this._service.translateValue('flss.success.update'));
        this.closeModal.emit(true);
      }
    });
  }
  cancel() {
    this.closeModal.emit(false);
  }
}

interface ColorItem {
  color_id: number;
  color_name: string;
  sizeList: SizeItem[];
}
interface SizeItem {
  spec_id: number;
  spec_size: string;
}

interface uploadNodeItem {
  finished_time: number;
  pictures?: Picture[];
  node_type: number;
  inspected_qty?: number;
  defected_qty?: number;
  remark?: string;
  inspected_status?: number;
  process_desc?: string;
  finish_qty?: number;
  po_unique_code?: string;
  line_list?: {
    color_id: number;
    color_name: string;
    spec_id: number;
    spec_size: string;
    qty?: number;
    checked?: boolean;
  }[];
}

interface HistoryLine {
  color_list: { color_id: number; color_name: string }[];
  displayColors: string;
  finished_time: number;
  id: number;
  info_id: number;
  inspected_qty: number;
  qualified_qty?: number;
  qualifiedPercent?: number;
  defected_qty: number;
  defectedPercent?: number;
  inspected_status: number;
  pictures: Picture[];
  process_desc: string;
  qty: number;
  remark: string;
  spec_list: { spec_id: number; spec_size: string }[];
  displaySizes: string;
  user_name: string;
}

interface Picture {
  name: string;
  url: string;
  version: string;
}
