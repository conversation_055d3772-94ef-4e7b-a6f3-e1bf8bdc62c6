import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { FactoryDetail } from '../../interface/factory-config';
import { FactoryManageDetailService } from '../factory-manage-detail.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-factory-basic-information-readonly',
  templateUrl: './factory-basic-information-readonly.component.html',
  styleUrls: ['./factory-basic-information-readonly.component.scss'],
})
export class FactoryBasicInformationReadonlyComponent implements OnInit, OnChanges {
  @Input() changeStep = false;
  detailConfig: any;
  step = '1.';

  constructor(public _service: FactoryManageDetailService, public _translateService: TranslateService) {}

  ngOnInit(): void {
    this.detailConfig = FactoryDetail;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes && changes.changeStep && changes.changeStep.currentValue) {
      this.step = this._translateService.instant('第几步', { value: '1' }) + '：';
    } else {
      this.step = '1.';
    }
  }
}
