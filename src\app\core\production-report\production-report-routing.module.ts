import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
const routes: Routes = [
  {
    path: 'production-progress',
    loadChildren: () => import('./production-progress/production-progress.module').then((m) => m.ProductionProgressModule),
  },
  {
    path: 'sec-process',
    loadChildren: () => import('./sec-process/sec-process.module').then((m) => m.SecProcessModule),
  },
  {
    path: 'production-table',
    loadChildren: () => import('./production-table/production-table.module').then((m) => m.productionTableModule),
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ProductionReportRoutingModule {}
