import { Directive, ElementRef, EventEmitter, Input, NgZone, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { PlanGraphItem } from '../interface';
const width = 48;
@Directive({
  selector: '[ganttDragItem]',
})
export class DragItemDirective implements OnInit, OnDestroy, OnChanges {
  @Input('dragData') data!: PlanGraphItem;
  @Input('dragTitle') title = '';
  @Input('backgroundColor') backgroundColor = '#a0a0a0';
  @Input('dragDisabled') disabled = false;
  @Output('dragStarted') started = new EventEmitter<DragEvent>();
  @Output('dragMoved') moved = new EventEmitter<DragEvent>();
  @Output('drop') drop = new EventEmitter<DragEvent>();
  count = 0;
  constructor(public _element: ElementRef<HTMLElement>, private zone: NgZone) {}
  ngOnChanges(changes: SimpleChanges): void {
    if (changes.disabled && !changes.disabled.firstChange) {
      this._element.nativeElement.draggable = !this.disabled;
      if (!this.disabled) {
        this.handleEvent();
      }
    }
  }
  ngOnInit(): void {
    if (!this.disabled) {
      this.zone.runOutsideAngular(() => {
        this.handleEvent();
      });
    }
  }
  ngOnDestroy(): void {
    this._element.nativeElement.removeAllListeners?.();
  }

  handleEvent() {
    const element = this._element.nativeElement;
    element.draggable = true;
    element.addEventListener('dragstart', (event) => {
      const backgroundColor = window.getComputedStyle(this._element.nativeElement).backgroundColor;
      const clientHeight = this._element.nativeElement.clientHeight;
      const element = document.createElement('div');
      const style = element.style;
      element.id = 'dragGhost';
      style.position = 'absolute';
      style.zIndex = '-9999';
      style.top = -10000 + 'px';

      style.width = `${width}px`;
      style.height = `${clientHeight}px`;
      style.padding = '1px 2px';
      style.backgroundColor = backgroundColor;
      style.borderRadius = '2px';
      style.border = '1px solid #ccc';
      style.textOverflow = 'ellipsis';
      style.fontSize = '10px';
      style.fontWeight = '500';
      style.lineHeight = '14px';
      style.overflow = 'hidden';
      element.innerText = this.title;
      document.body.appendChild(element);
      event.dataTransfer?.setDragImage(element, 0, clientHeight);
      const { rawData, graphRef, ...transfromData } = { ...this.data };
      event.dataTransfer?.setData('text', JSON.stringify(transfromData));
      event.dataTransfer && (event.dataTransfer.effectAllowed = 'move');

      this._element.nativeElement.classList.add('disabled');
      this.zone.run(() => {
        this.started.emit(event);
      });
    });
    // element.addEventListener('drag', (event) => {
    //   // const element = document.getElementById('dragGhost');
    //   // element.style.cursor = 'move';
    //   this.zone.run(() => {
    //     this.moved.emit(event);
    //   });
    // });
    element.addEventListener('dragend', (event) => {
      document.body.removeChild(document.getElementById('dragGhost')!);
      this._element.nativeElement.classList.remove('disabled');
    });
    // dragover会阻止放入
    element.addEventListener('dragover', (event) => {
      event.preventDefault();
    });
    element.addEventListener(
      'drop',
      (event) => {
        event.stopPropagation();
        this.zone.run(() => {
          this.drop.emit(event);
        });
      },
      true
    );

    element.addEventListener(
      'dragenter',
      (event) => {
        this.count++;
        // 当可拖动的元素进入可放置的目标高亮目标节点
        // if ((event.target as HTMLElement).className?.includes('graph-item-text')) {
        const target = this.returnTargetElement(event.target as HTMLElement);
        target?.classList.add('target-item');
        // }
        // event.preventDefault();
      },
      false
    );

    element.addEventListener(
      'dragleave',
      (event) => {
        this.count--;
        // 当拖动元素离开可放置目标节点，重置其背景
        // if ((event.target as HTMLElement).className?.includes('graph-item-text')) {
        if (this.count === 0) {
          const target = this.returnTargetElement(event.target as HTMLElement);
          target?.classList.remove('target-item');
        }
        // }
      },
      false
    );
  }
  returnTargetElement(target: HTMLElement | null): HTMLElement | null {
    if (target == null) return null;
    if (target.classList.contains('graph-item')) {
      return target;
    } else {
      return this.returnTargetElement(target.parentElement);
    }
  }
}
