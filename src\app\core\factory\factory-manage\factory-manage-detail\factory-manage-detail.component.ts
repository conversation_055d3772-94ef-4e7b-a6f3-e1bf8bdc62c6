import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FactoryManageDetailService } from './factory-manage-detail.service';
import { ActivatedRoute, Router, RouteReuseStrategy } from '@angular/router';
import { FactoryDetailData, Lifecycle } from '../interface/factory-detail-data';
import { FormBuilder, Validators } from '@angular/forms';
import { UndoModalComponent } from './undo-modal/undo-modal.component';
import { CancellationModalComponent } from './cancellation-modal/cancellation-modal.component';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ReturnModalComponent } from './return-modal/return-modal.component';
import { TranslateService } from '@ngx-translate/core';
import { InitProcessSteps } from '../interface/factory-config';
import { format, addMonths } from 'date-fns';
import { isNil } from 'ng-zorro-antd/core/util';
import { finalize } from 'rxjs/operators';
import { UtilService } from 'src/app/shared/util.service';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { ModalService } from 'src/app/shared/modal.service';
import { FactoryManageService } from '../factory-manage.service';
import { AppStorageService } from 'src/app/shared/app-storage.service';
import { FlcRouteReuseStrategy } from 'fl-common-lib';

@Component({
  selector: 'app-factory-manage-detail',
  templateUrl: './factory-manage-detail.component.html',
  styleUrls: ['./factory-manage-detail.component.scss'],
  providers: [FactoryManageDetailService, FactoryManageService],
})
export class FactoryManageDetailComponent implements OnInit {
  @ViewChild(UndoModalComponent) undoModal: any;
  @ViewChild(CancellationModalComponent) cancellationModal: any;
  @ViewChild(ReturnModalComponent) returnModal: any;
  id: any;
  editable = false;
  processLines: Lifecycle[] = InitProcessSteps;
  deleteSureText!: string;
  loading = false;
  btnLoading = false;
  useModal = false;
  pwdPattern = '[A-Za-z0-9]*';
  urlPattern = '(https?)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]';

  constructor(
    private _route: ActivatedRoute,
    private _fb: FormBuilder,
    private _msg: NzMessageService,
    private _router: Router,
    public _service: FactoryManageDetailService,
    public _listService: FactoryManageService,
    private _translate: TranslateService,
    private _util: UtilService,
    private _notice: NzNotificationService,
    private _modal: NzModalService,
    private _routeReuseStrategy: RouteReuseStrategy,
    private _ownModal: ModalService,
    public _storage: AppStorageService
  ) {}

  deleteRouteSnapshot() {
    (this._routeReuseStrategy as FlcRouteReuseStrategy).clearCachedRoute('/factory/factoryManage');
  }

  ngOnInit(): void {
    this.getVersionLists();
    this.id = this._route.snapshot.paramMap.get('id');
    this.deleteSureText = this._translate.instant('sure-notice.delete');
    this.getFactoryDetail();
    if (!this._listService.btnArr?.length) {
      this._listService.btnArr = this._storage.getUserActions('factory/factoryManage');
    }
  }

  /**
   * 获取所有版本数据
   */
  getVersionLists() {
    this._service.getVersionLists({}).subscribe((res) => {
      if (res.code === 200) {
        this._service.versionLists = res.data?.products ?? [];
      }
    });
  }

  /**
   * 获取工厂详情数据
   */
  getFactoryDetail() {
    if (this.id !== 'new' && this.id !== null) {
      this.loading = true;
      this._service
        .getFactoryDetail(+this.id)
        .pipe(finalize(() => (this.loading = false)))
        .subscribe((res) => {
          if (res.code === 200) {
            const data = res.data;
            this._service.factoryStatus = data.status;
            this.processLines = data.metadata.lifecycle.map((item: any) => {
              return {
                ...item,
                gen_time: item?.gen_time ? format(new Date(item.gen_time), 'yyyy/MM/dd HH:mm:ss') : '',
              };
            });
            this.initData(data);
          }
        });
    } else {
      this.editable = true;
      this._service.factoryStatus = this._service.factoryStatusEnum.Create;
      this.initData();
      this.getFactoryCode();
    }
  }

  /**
   * 初始化详情数据
   * @param line
   */
  initData(line: FactoryDetailData | null = null) {
    let domain = null;
    const splits = window.location.host.split('.');
    if (line?.status === 3 && line?.factory_type === 2 && line?.domain == null && splits.length >= 3) {
      // 判断 'elan.flkj.pub'
      const isPro = window.location.host.includes('elan.flkj.pub');
      const suffixList = splits.slice(isPro ? -2 : -3);
      const gapStr = isPro ? '.sst.' : '.ss.';
      const str = 'https://' + line?.elan_code?.toLowerCase() + line?.code?.toLowerCase() + gapStr + suffixList.join('.');
      domain = str;
    }
    const region_model = line?.region ? [line.region.province.id, line.region.city.id] : null;
    this._service.factoryForm = this._fb.group({
      elan_code: [line?.elan_code, line?.factory_type === 2 ? [Validators.required] : []],
      id: [line?.id ?? null],
      code: [
        line?.code ?? null,
        [Validators.required, Validators.maxLength(20), Validators.pattern(this.pwdPattern)],
        [this._service.uniqueValidator(line?.code ?? null)],
      ],
      name: [line?.name ?? null, [Validators.required, Validators.maxLength(20)]],
      abbr: [line?.abbr ?? null, [Validators.required, Validators.maxLength(20)]],
      coop: [line?.coop ?? null],
      scale: [line?.scale ?? null, [Validators.min(1)]],
      sewing_workers: [line?.sewing_workers ?? null, [Validators.min(1)]],
      category: [line?.category ?? null, [Validators.maxLength(30)]],
      customer: [line?.customer ?? null, [Validators.maxLength(30)]],
      contacts: [line?.contacts ?? null, [Validators.maxLength(20)]],
      tel: [line?.tel ?? null, [Validators.maxLength(20)]],
      email: [line?.email ?? null, [Validators.maxLength(30)]],
      region: [line?.region ?? null],
      region_model: [region_model],
      address: [line?.address ?? null, [Validators.maxLength(30)]],
      factory_type: [line?.factory_type ?? 2],
      deep_flow_enable: [line?.deep_flow_enable ?? false],
      domain: [line?.domain ?? domain],
      initial_link: [line?.initial_link ?? null],
      product_id: [line?.product_id ?? 1, [Validators.required]],
      reason: [line?.reason ?? null],
      changelog: [line?.metadata?.changelog ?? []],
      modified_time: [line?.modified_time ?? null],
      retain_time: [null],
      deploy_region: [line?.deploy_region ?? null, [Validators.required]],
      deploy_region_name: [line?.deploy_region_name],
    });
    if (this._service.factoryStatus === this._service.factoryStatusEnum.DEPLOY_PENDING) {
      this._service.factoryForm.get('domain')?.setValidators([Validators.required, Validators.maxLength(100)]);
    } else {
      this._service.factoryForm.get('domain')?.clearValidators();
    }
    const cancel_time = this._service.factoryForm.get('modified_time')?.value;
    if (cancel_time && this._service.factoryStatus === this._service.factoryStatusEnum.CANCELLED) {
      const retain_time = format(addMonths(new Date(cancel_time), 3), 'yyyy/MM/dd HH:mm:ss');
      this._service.factoryForm.get('retain_time')?.setValue(retain_time);
    }
  }

  /**
   * 从系统获取公司编码，可修改
   */
  getFactoryCode() {
    this._service.getFactoryCode().subscribe((res) => {
      if (res.code === 200) {
        const code = res.data;
        this._service.factoryForm?.get('code')?.setValue(code);
      }
    });
  }

  /**
   * 取消,返回到上一次的页面状态
   */
  cancel(tplContent: TemplateRef<any>): void {
    if (this._service.factoryForm?.dirty) {
      this._modal.create({
        nzTitle: undefined,
        nzContent: tplContent,
        nzWidth: '300px',
        nzFooter: null,
        nzWrapClassName: 'modal-outer',
      });
    } else {
      this.handleCancel();
    }
  }

  /**
   * 离开时的路由守卫
   * @returns
   */
  canLeave() {
    return true;
  }

  cancelOk() {
    if (this.id !== 'new' && this.id !== null) {
      this.getFactoryDetail();
    }
    this.handleCancel();
    this._modal.closeAll();
  }

  cancelStop() {
    this._modal.closeAll();
  }

  handleCancel() {
    if (this.id !== 'new' && this.id !== null) {
      this.editable = false;
    } else {
      this._router.navigate(['/factory/factoryManage']);
    }
  }

  /**
   * 删除
   */
  delete() {
    const ref: NzModalRef = this._ownModal.confirm('confirm-delete');
    ref.afterClose.subscribe((val: boolean) => {
      if (val) {
        this.btnLoading = true;
        this._service
          .deleteFactory(+this.id)
          .pipe(finalize(() => (this.btnLoading = false)))
          .subscribe((res) => {
            if (res.code === 200) {
              this.deleteRouteSnapshot();
              const delete_msg = this._translate.instant('success.delete');
              this._msg.success(delete_msg);
              this.useModal = false;
              this._router.navigate(['/factory/factoryManage']);
            }
          });
      } else {
        return;
      }
    });
  }

  /**
   * 返回，直接返回列表页
   */
  back() {
    this.useModal = false;
    this._router.navigate(['/factory/factoryManage']);
  }

  /**
   * 编辑
   */
  edit() {
    this.editable = true;
  }

  /**
   * 撤销
   */
  undo() {
    this.undoModal.createUndoModel();
  }

  /**
   * 确认撤销
   */
  handleUndoOk() {
    this._service.withdrawFactory(+this.id).subscribe((res) => {
      if (res.code === 200) {
        const undo_msg = this._translate.instant('success.undo');
        this._msg.success(undo_msg);
        this.getFactoryDetail();
        this.undoModal.closeUndoModel();
      }
    });
  }

  /**
   * 退回修改
   */
  modify() {
    this.returnModal.createReturnModel();
  }

  /**
   * 确认退回修改
   * @param payload
   */
  handleReturnOk(payload: object) {
    this._service
      .disapproveFactory({
        id: +this.id,
        ...payload,
      })
      .subscribe((res) => {
        if (res.code === 200) {
          const return_msg = this._translate.instant('success.return');
          this._msg.success(return_msg);
          this.editable = false;
          this.getFactoryDetail();
          this.returnModal.closeReturnModel();
        }
      });
  }

  /**
   * 审核通过
   */
  pass() {
    this._service.approveFactory(+this.id).subscribe((res) => {
      if (res.code === 200) {
        const pass_msg = this._translate.instant('success.pass');
        this._msg.success(pass_msg);
        this.getFactoryDetail();
      }
    });
  }

  /**
   * 保存
   */
  save() {
    this.btnLoading = true;
    const data = this._service.factoryForm?.getRawValue();
    const payload = {
      ...data,
      deep_flow_enable: data.factory_type === 1,
    };
    if (this.id !== 'new' && this.id !== null) {
      if (this._service.factoryStatus === this._service.factoryStatusEnum.DEPLOY_PENDING) {
        this._service
          .saveFactoryDomain({ id: payload.id, domain: payload.domain })
          .pipe(finalize(() => (this.btnLoading = false)))
          .subscribe((res) => {
            if (res.code === 200) {
              const save_msg = this._translate.instant('success.save');
              this._msg.success(save_msg);
              this._service.factoryForm = null;
              setTimeout(() => {
                this.getFactoryDetail();
              }, 0);
            }
          });
      } else {
        this._service
          .saveFactory(payload)
          .pipe(finalize(() => (this.btnLoading = false)))
          .subscribe((res) => {
            if (res.code === 200) {
              const save_msg = this._translate.instant('success.save');
              this._msg.success(save_msg);
              this._service.factoryForm = null;
              setTimeout(() => {
                this.getFactoryDetail();
              }, 0);
            }
          });
      }
    } else {
      this._service
        .addFactory(payload)
        .pipe(finalize(() => (this.btnLoading = false)))
        .subscribe((res) => {
          if (res.code === 200) {
            this.id = res.data;
            this.deleteRouteSnapshot();
            const save_msg = this._translate.instant('success.save');
            this._msg.success(save_msg);
            this.useModal = false;
            this._router.navigate(['/factory/factoryManage', res.data]);
            this._service.factoryForm = null;
            setTimeout(() => {
              this.getFactoryDetail();
            }, 0);
          }
        });
    }
  }

  /**
   * 提交
   */
  commit() {
    this.btnLoading = true;
    const data = this._service.factoryForm?.get('domain')?.value;
    if (data && !this._service.checkUrl(data)) {
      const msg = this._translate.instant('form-error.input-right');
      this._notice.error('', msg);
      this.btnLoading = false;
      return;
    }
    const scale = this._service.factoryForm?.get('scale')?.value || 0;
    const sewing_workers = this._service.factoryForm?.get('sewing_workers')?.value || 0;
    if (scale < sewing_workers) {
      const msg = this._translate.instant('factory-commit-notice.worker-more-scale');
      this._notice.error('', msg);
      this.btnLoading = false;
      return;
    }
    this._util
      .checkIfFormPassesValidation(this._service.factoryForm as any)
      .then((valid) => {
        if (valid) {
          const data = this._service.factoryForm?.getRawValue();
          if (this._service.factoryStatus === this._service.factoryStatusEnum.DEPLOY_PENDING) {
            const payload = {
              id: data.id,
              domain: data.domain,
              deep_flow_enable: data.deep_flow_enable,
            };
            this._service
              .deployFactory(payload)
              .pipe(finalize(() => (this.btnLoading = false)))
              .subscribe((res) => {
                if (res.code === 200) {
                  this.editable = false;
                  this.getFactoryDetail();
                }
              });
          } else {
            const payload = {
              ...data,
              deep_flow_enable: data.factory_type === 1,
            };
            this._service
              .commitFactory(payload)
              .pipe(finalize(() => (this.btnLoading = false)))
              .subscribe((res) => {
                if (res.code === 200) {
                  if (this.id === 'new' || isNil(this.id)) {
                    this.deleteRouteSnapshot();
                    this.id = res.data;
                    const submit_msg = this._translate.instant('success.submit');
                    this._msg.success(submit_msg);
                    this.useModal = false;
                    this._router.navigate(['/factory/factoryManage', this.id]);
                  }
                  this.editable = false;
                  this.getFactoryDetail();
                }
              });
          }
        } else {
          const msg = this._translate.instant('form-error.input-right');
          this._notice.error('', msg);
          return;
        }
      })
      .catch((error) => {
        console.log('error', error);
        this.btnLoading = false;
        return;
      })
      .finally(() => {
        this.btnLoading = false;
        return;
      });
  }

  /**
   * 注销
   */
  cancellation() {
    this.cancellationModal.createCancellationModel();
  }

  /**
   * 注销确认
   */
  handleCancelOk() {
    this._service.cancelFactory(+this.id).subscribe((res) => {
      if (res.code === 200) {
        const cancellation_msg = this._translate.instant('success.cancellation');
        this._msg.success(cancellation_msg);
        this.getFactoryDetail();
        this.cancellationModal.closeCancellationModel();
      }
    });
  }

  /**
   * 激活
   */
  activate() {
    const msg = this._translate.instant('factory-modal.activate-modal');
    this._ownModal.cancelActionModal({
      content: msg,
      clickYes: () => {
        this._service.reActiveFactory(+this.id).subscribe((res) => {
          if (res.code === 200) {
            const activate_msg = this._translate.instant('success.activate');
            this._msg.success(activate_msg);
            this.getFactoryDetail();
          }
        });
      },
      clickNo: () => {
        this._modal.closeAll();
      },
    });
  }
}
