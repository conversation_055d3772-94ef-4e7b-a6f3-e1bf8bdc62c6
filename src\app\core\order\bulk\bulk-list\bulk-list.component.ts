import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { Router } from '@angular/router';
import { endOfDay, format, startOfDay } from 'date-fns';
import { isNotNil } from 'ng-zorro-antd/core/util';
import { resizable, FlcRouterEventBusService, FlcModalService, FlcOssUploadService, FlcFileGalleryComponent } from 'fl-common-lib';
import { BulkService } from '../bulk.service';
import { OrderStatus, OrderOperateBtnEnum, OrderOperateLabelEnum, SourceTypeEnum, EventTypeEnum } from '../models/bulk.enum';
import { AppStorageService } from 'src/app/shared/app-storage.service';
import { FlcDrawerHelperService } from 'fl-common-lib';
import { AcceptListComponent } from '../components/accept-list/accept-list.component';
import { NzUploadFile } from 'ng-zorro-antd/upload';
import { Observable, Observer, Subscription, finalize } from 'rxjs';
import { NzMessageService } from 'ng-zorro-antd/message';
import { UploadImagesComponent } from '../components/upload-images/upload-images.component';
import { TableDataViewComponent } from 'fl-sewsmart-lib/common-component';
import { SearchOptionsCustomContainerComponent } from 'src/app/components/search-options-custom-container/search-options-custom-container.component';
import { NzModalService } from 'ng-zorro-antd/modal';
import { ImportResultComponent } from '../components/import-result/import-result.component';
import { initSearchList, initTableHeader } from './config';
import { factoryCodes } from 'src/app/shared/common-config';
import { BatchUpdateLeadtimeComponent } from '../components/batch-update-leadtime/batch-update-leadtime.component';
import { BatchUpdateFobPriceComponent } from '../components/batch-update-fob-price/batch-update-fob-price.component';

@Component({
  selector: 'app-bulk-list',
  templateUrl: './bulk-list.component.html',
  styleUrls: ['./bulk-list.component.scss'],
})
@resizable()
export class BulkListComponent implements OnInit {
  translateName = 'bulk.';
  @ViewChild('fileGalleryRef') fileGalleryRef?: FlcFileGalleryComponent;
  searchList = initSearchList();

  searchData: any = {
    io_code: null,
    order_category: null,
    order_production_type: null,
    brand_id: null,
    style: null,
    customer: null,
    contract_number: null,
    order_status: null,
    order_date: null,
    due_time: null,
    biz_user: null,
    dist_user: null,
    customer_due_time: null,
    gen_user: null,
    style_code: null,
    create_time: null,
    keywords: null,
  };
  orderStatusList = [
    {
      label: '全部',
      value: 0,
      key: 'all',
      isTotalItem: true,
      amount: '0',
    },
    {
      label: '待提交',
      value: 1,
      key: 'to_submit',
      amount: '0',
    },
    {
      label: '待审核',
      key: 'to_audit',
      amount: '0',
      value: 2,
    },
    {
      label: '待修改',
      key: 'to_modify',
      amount: '0',
      value: 4,
    },
    {
      label: '已取消',
      key: 'cancelled',
      amount: '0',
      value: 8,
    },
    {
      label: '审核通过',
      key: 'audit_pass',
      amount: '0',
      value: 11,
    },
    {
      label: '修改待审核',
      key: 'to_modify_audit',
      amount: '0',
      value: 9,
    },
    {
      label: '修改未通过',
      key: 'modify_audit_return',
      amount: '0',
      value: 10,
    },
  ];
  tableConfig: any = {
    translateName: this.translateName,
    hasCheckbox: true,
    trCheck: true,
    detailBtn: true,
    dataList: [],
    count: null,
    height: 500,
    loading: false,
    pageSize: 20,
    actionWidth: '110px',
    pageIndex: 1,
    version: '1.0.4',
  };
  @ViewChild('header') hearderComponent?: ElementRef;
  @ViewChild('tableDataView') tableDataView?: TableDataViewComponent;
  @ViewChild('tableRef') tableRef!: any;

  tableHeader = initTableHeader();

  orderOperateBtnEnum = OrderOperateBtnEnum;
  sourceTypeEnum = SourceTypeEnum;
  styleList: any[] = []; // 下拉列表数据
  order_by: any = []; // 排序
  checkedInfo!: { count: number; list: any[] };
  routeListerner?: any;
  topTitle = this._service.translateValue('flss.btn.copy');
  isVisible = false;
  printTitle = '标签全打';
  printType = 1;
  selectedIds: number[] = [];
  bulk_order_card = [];
  permission: string[] = [];
  lang = this._service.lang;
  userInfo: any = {};
  factoryCodes = factoryCodes || [];
  _eventSubscription?: Subscription;

  bom_fill_status = {
    10: '未填写',
    20: '已填写',
  };

  bom_audit_status = {
    10: '待审核',
    20: '部分审核',
    30: '全部审核',
  };

  inquiry_status = {
    10: '未发起',
    20: '已发起',
  };

  constructor(
    private _router: Router,
    private _routerEventBus: FlcRouterEventBusService,
    private _drawerHelp: FlcDrawerHelperService,
    private _notice: NzNotificationService,
    private _flcModalService: FlcModalService,
    public _storage: AppStorageService,
    private ossUploadService: FlcOssUploadService,
    private _msg: NzMessageService,
    public _service: BulkService,
    private modalService: NzModalService
  ) {
    const userInfo: string | null = localStorage.getItem('userInfo');
    this.userInfo = userInfo ? JSON.parse(userInfo) : {};
  }
  hasPriceInquiryAuth = false;
  ngOnInit(): void {
    this.initBulkSearchListConfig();
    (this as any).addResizePageListener();
    this.initAuth();
    this._storage.getUserActions('order/bulk');
    // this.getList();
    this.getNumber();
    setTimeout(() => {
      this.addRouterListener();
    });

    this._eventSubscription = this._service.eventEmitter.subscribe((res) => {
      if (res === EventTypeEnum.refreshList) {
        this.getList();
      }
    });
  }

  initAuth() {
    this._service.btnArr = this._storage.getUserActions('order/bulk');
    this.hasPriceInquiryAuth = this._service.btnArr.includes('order:bulk-order-inquiry');
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.resizePage();
    });
  }
  ngOnDestroy() {
    (this as any).removeResizePageListener();
    this.routeListerner?.unsubscribe();
    this._eventSubscription?.unsubscribe();
  }

  initBulkSearchListConfig() {
    const bulkSearchListConfig = JSON.parse(localStorage.getItem(this.customSearchListConfig.localKey) || 'false');
    if (bulkSearchListConfig && bulkSearchListConfig.version === this.customSearchListConfig.version) {
      this.searchList = bulkSearchListConfig.configList;
    }
  }

  getCount(data: any) {
    this.checkedInfo = data;
  }
  resizePage() {
    const headerHeight = this.hearderComponent?.nativeElement?.offsetHeight ?? 32;
    const staticViewContentH = this.tableDataView?.contentHeight() ?? 0;
    this.tableConfig.height = window.innerHeight - headerHeight - 150 - staticViewContentH;
    this.tableConfig = { ...this.tableConfig };
  }
  addRouterListener() {
    this.routeListerner = this._routerEventBus.onRouterActivationEnd('/order/bulk/list', () => {
      if (this._service.isChange) {
        // 详情有新建或者改变
        setTimeout(() => {
          this.getList();
          this._service.isChange = false;
        }, 0);
      }
    });
  }
  /**
   * 页码改变
   * @param  {number} e
   */
  indexChanges(e: number) {
    this.tableConfig.pageIndex = e;
    this.getList();
  }
  /**
   * 页数改变
   * @param  {number} e
   */
  sizeChanges(e: number) {
    this.tableConfig.pageIndex = 1;
    this.tableConfig.pageSize = e;
    this.getList();
  }
  // 更改了排序方式
  sortDataLists({ value, key }: { value: 'desc' | 'asc' | null; key: string }) {
    this.order_by = value ? [key + ' ' + value] : [];
    this.getList();
  }
  /**
   * 打开款式下拉
   */
  openStyle(e: any) {
    if (e) {
      this._service.getMaterial({ cache: true }).subscribe((res: any) => {
        this.styleList = this._service.onTransOption(res.data.info);
      });
    }
  }
  /**
   * 打印方法
   */
  onTagPrint(type: number) {
    if (!this.selectedIds.length) {
      this._notice.error(this._service.translateValue('flss.message.select-least-one'), '');
      return;
    }
    this.bulk_order_card = [];
    this.getPrintOptions();
    this.printType = type;
    switch (type) {
      case 1:
        this.printTitle = '唯一码标签全打';
        break;
      case 2:
        this.printTitle = '唯一码标签补打';
        break;
    }
  }

  onSelectedCount(e: any) {
    this.selectedIds = e.list.map((item: any) => item.id);
  }

  getPrintOptions() {
    this._service.getLabelOptions({ order_id_list: this.selectedIds }).subscribe((res: any) => {
      if (res) {
        this.bulk_order_card = res.data.bulk_order_card_list;
        this.bulk_order_card.forEach((item: any) => {
          item.printOptionsOne = [];
          this.isVisible = true;
        });
      }
    });
  }
  /**
   *重置
   */
  reset() {
    this.getList(true);
  }
  /**
   * 下拉选择搜索
   */
  onSearch() {
    this.tableConfig.pageIndex = 1;
    this.getList();
  }
  onInputSearchValue(value: string) {
    this.searchData.keywords = value.length ? value : null;
    this.tableConfig.pageIndex = 1;
    this.getList();
  }
  onOrderStatusChanged(event: number[]) {
    if (event && event.length) {
      this.searchData.status = `${event}`;
    } else {
      this.searchData.status = null;
    }
    this.tableConfig.pageIndex = 1;
    this.getList();
  }
  getList(reset = false) {
    this.tableConfig.pageIndex = reset ? 1 : this.tableConfig.pageIndex;
    this.tableConfig = { ...this.tableConfig, loading: true };
    const data = {
      cache: true,
      where: this.handleWhere(reset),
      order_by: this.order_by,
      limit: this.tableConfig.pageSize,
      page: this.tableConfig.pageIndex,
    };
    this._service
      .bulkList(data)
      .pipe(
        finalize(() => {
          this.tableConfig = { ...this.tableConfig, loading: false };
        })
      )
      .subscribe((res) => {
        if (res.code === 200) {
          this.tableRef.clearAllSelected();
          this.tableConfig.dataList = res.data.data;
          this.tableConfig.count = res.data.total;
          this.orderStatusList.forEach((item) => {
            item.amount = res?.data?.statistics[item.key];
            if (!item.amount.length) {
              item.amount = '0';
            }
          });
          this.tableConfig = { ...this.tableConfig };
        }
      });
  }
  /**
   * 获取搜索项
   * @param  {} reset=false
   * @returns object
   */
  handleWhere(reset = false): object {
    let where: any = [];
    if (reset) {
      this.searchData = {
        io_code: null,
        order_category: null,
        order_production_type: null,
        brand_id: null,
        style: null,
        customer: null,
        contract_number: null,
        order_status: null,
        order_date: null,
        due_time: null,
        biz_user: null,
        dist_user: null,
        customer_due_time: null,
        gen_user: null,
        create_time: this.searchData.create_time,
        keywords: this.searchData.keywords,
        status: this.searchData.status,
      };
    }
    Object.entries(this.searchData).forEach((item: any) => {
      if (isNotNil(item[1])) {
        if (item[0] === 'due_time' || item[0] === 'create_time' || item[0] === 'order_date' || item[0] === 'customer_due_time') {
          if (item[1].length) {
            const startTime = format(startOfDay(item[1][0]), 'T');
            const endTime = format(endOfDay(item[1][1]), 'T');
            const value = startTime + ',' + endTime;
            where.push({ column: item[0], op: 'between', value: value });
          }
        } else if (item[0] === 'style') {
          if (item[1].length) {
            const materialList = [
              { column: 'first_material_name', op: '=', value: item[1][0] },
              { column: 'second_material_name', op: '=', value: item[1][1] },
              { column: 'third_material_name', op: '=', value: item[1][2] },
            ];
            where = [...where, ...materialList];
          }
        } else if (item[0] === 'order_label') {
          where.push({ column: item[0], op: '=', value: JSON.stringify([item[1]]) });
        } else {
          where.push({ column: item[0], op: '=', value: item[1] });
        }
      }
    });
    return where;
  }
  getDetail(item: any) {
    if (item === 'new') {
      this._router.navigate(['/order/bulk/list/new']);
    } else {
      const queryParams: any = {};
      if (item?.source === 4) {
        queryParams['history_id'] = item.history_id;
      }
      this._router.navigate(['/order/bulk/list', item.id], { queryParams });
    }
  }

  handlePrePayment() {
    console.log(this.selectedIds, this.tableConfig.dataList);

    // 筛选出选中的订单数据
    const selectedOrders = this.tableConfig.dataList.filter((item: any) => this.selectedIds.includes(item.id));

    // 检查是否所有选中订单都是审核通过状态
    const invalidStatusOrders = selectedOrders.filter((item: any) => item.order_status !== OrderStatus.auditPass);

    // 检查是否所有选中订单都是同一客户同一币种
    let customerId: any = null;
    let currencyCode: any = null;
    const invalidCustomerOrders: any[] = [];

    selectedOrders.forEach((item: any) => {
      if (customerId === null) {
        customerId = item.customer;
        currencyCode = item.currency;
      } else if (item.customer !== customerId || item.currency !== currencyCode) {
        invalidCustomerOrders.push(item);
      }
    });

    // 如果有不符合条件的订单，显示错误信息
    if (invalidStatusOrders.length > 0 || invalidCustomerOrders.length > 0) {
      const invalidOrderCodes = [...invalidStatusOrders, ...invalidCustomerOrders]
        .map((item: any) => item.io_code)
        .filter((value, index, self) => self.indexOf(value) === index); // 去重

      const errorMsg =
        invalidCustomerOrders.length > 0
          ? this._service.translateValue('请选择同客户同币种的订单')
          : this._service.translateValue('订单必须是审核通过状态');

      this._notice.error(`订单${invalidOrderCodes.join('、')}无法生成预收款单。${errorMsg}`, '');
      return;
    }

    // 所有检查通过，继续处理
    const order_uuids: string[] = selectedOrders.map((item: any) => item.io_uuid);

    // this._router.navigate(['/settlement/receipt-management/list', 17]);
    if (order_uuids.length > 0) {
      this._service.toReceipt({ order_uuid: order_uuids }).subscribe((res: any) => {
        if (res.code === 200) {
          this._router.navigate(['/settlement/receipt-management/list', 'new'], { queryParams: { id: res.data.id } });
        }
      });
    } else {
      this._notice.error('请至少选择一个订单', '');
    }
  }

  copy(id: number) {
    this.topTitle = '';
    setTimeout(() => {
      this._router.navigate(['/order/bulk/list', 'new'], { queryParams: { copyId: id } });
      this.topTitle = this._service.translateValue('flss.btn.copy');
    });
  }

  handleButtonAction(label: string) {
    if (!this.checkedInfo?.count) {
      this._notice.error(this._service.translateValue('flss.message.select-least-one'), '');
      return;
    }
    const isInvalidCodes: string[] = [];
    const ids: string[] = [];
    switch (label) {
      case OrderOperateBtnEnum.pass:
        this.checkedInfo?.list?.forEach((item) => {
          [OrderStatus.toAudit, OrderStatus.toModifyAudit].includes(item.order_status)
            ? ids.push(item.id)
            : isInvalidCodes.push(item.io_code);
        });
        this._onBatchPass(ids);
        break;
      case OrderOperateBtnEnum.modify:
        this.checkedInfo?.list.forEach((item) => {
          [OrderStatus.toAudit, OrderStatus.toModifyAudit].includes(item.order_status)
            ? ids.push(item.id)
            : isInvalidCodes.push(item.io_code);
        });
        this._onBatchModify(ids);
        break;
    }
    if (isInvalidCodes.length) {
      const str = this._service.translateValue(this.translateName + '大货单号不能操作', {
        code: isInvalidCodes.join('、'),
        operate: this._service.translateValue(this.translateName + OrderOperateLabelEnum[label]),
      });
      // this._notice.warning(`大货单号${isInvalidCodes.join('、')}不能操作${OrderOperateLabelEnum[label]}`, '');
      this._notice.warning(str, '');
    }
  }

  // 批量审核
  private _onBatchPass(ids: string[]) {
    if (!ids.length) return;
    const ref = this._flcModalService.confirmCancel({
      content: this._service.translateValue(this.translateName + '确定审核通过'),
    });
    ref.afterClose.subscribe((res: any) => {
      if (!res) {
        return;
      } else {
        this._service.batchPassList({ ids: ids }).subscribe((res: any) => {
          if (res.code === 200) {
            if (res.data?.msg?.length) {
              this._notice.error(res.data.msg, '');
            } else {
              this._notice.success(this._service.translateValue('flss.success.operate'), '');
              this._service.batchCreateProductionTable({ bulk_order_ids: ids, operation_type: 1 }).subscribe((res) => {});
            }
            this._service.isChange = true;
            this.getList();
          }
        });
      }
    });
  }

  /**
   * 退回修改
   */
  private _onBatchModify(ids: string[]) {
    if (!ids.length) return;
    this._service.confirmDialogWithReason().afterClose.subscribe((result) => {
      if (result?.success) {
        this._service.batchModifyList({ ids: ids, reason: result.reason }).subscribe((res) => {
          if (res.code === 200) {
            if (res.data?.msg?.length) {
              this._notice.error(res.data.msg, '');
            } else {
              this._notice.success(this._service.translateValue('flss.success.operate'), '');
            }
            this._service.isChange = true;
            this.getList();
          }
        });
      }
    });
  }

  // 获取待接单数量
  acceptNum = 0; // 待接单数量
  getNumber() {
    this._service.getAcceptNumber().subscribe((res: any) => {
      if (res.code === 200) {
        this.acceptNum = res.data.total;
      }
    });
  }

  // 打开接单池抽屉
  getAccept() {
    this._service.isAccept = false;
    this._drawerHelp
      .openDrawer({ title: this._service.translateValue(this.translateName + '待接单订单池'), content: AcceptListComponent })
      .subscribe((res: any) => {
        // if (this._service.isAccept) {
        this.getList();
        this.getNumber();
        // }
      });
  }

  // 下载模板
  onDownloadTemplate() {
    this._service.downloadTemplate().subscribe((res: any) => {
      if (res.data?.url?.length) {
        const a = document.createElement('a');
        a.href = res.data.url;
        a.click();
        document.body.removeChild(a);
      }
    });
  }

  interval: any;
  fileList: any[] = [];
  isImporting = false;

  onImport() {
    this.fileGalleryRef?.['uploadInputField'].nativeElement.click();
    let num = 0;
    // 检测是否开始上传
    this.interval = setInterval(() => {
      if (this.isImporting) {
        clearInterval(this.interval);
        return;
      }
      this.isImporting = this.fileGalleryRef?.status === 'uploading';
      // 最多检测3分钟
      num++ === 3 * 60 && clearInterval(this.interval);
    }, 1000);
  }

  importResList: any[] = []; // 导入结果
  onUploaded(val: any) {
    this.fileList = [];
    this.tableConfig = { ...this.tableConfig, loading: true };
    this._service
      .uploadTemplate({ url: val?.[0]?.url })
      .pipe(
        finalize(() => {
          this.tableConfig = { ...this.tableConfig, loading: false };
          this.fileList = [];
        })
      )
      .subscribe((res: any) => {
        if (res.code === 200) {
          this.getList();
          this._msg.success(this._service.translateValue('flss.success.import'));
          this.isImporting = false;
          clearInterval(this.interval);
          this.fileList = [];
          if (!this.factoryCodes.includes(this.userInfo?.factory_code)) {
            if (res.data.order_ids?.length) {
              this.fetchOrderInfo(res.data.order_ids);
            }
          } else {
            this.importResList = res?.data?.data_list || [];
            this.openImportResultModal();
          }
        }
      });
  }

  fetchOrderInfo(order_ids: string[]) {
    this._service.getOrderInfoByOrderIds({ order_ids }).subscribe((res) => {
      if (res.code === 200) {
        this.openUploadDraer(res.data.orders);
      }
    });
  }

  // 批量更新leadtime
  handleUpdateLeadtime() {
    if (!this.checkedInfo?.count) {
      this._notice.error(this._service.translateValue('flss.message.select-least-one'), '');
      return;
    }
    const order_ids =
      this.checkedInfo?.list?.map((item: any) => {
        return item?.id;
      }) || [];
    this.modalService
      .create({
        nzWidth: 450,
        nzTitle: this._service.translateValue(this.translateName + '批量更新'),
        nzMaskClosable: false,
        nzContent: BatchUpdateLeadtimeComponent,
        nzBodyStyle: {
          padding: '0',
        },
        nzComponentParams: {
          order_ids,
        },
        nzFooter: null,
      })
      .afterClose.subscribe(() => {});
  }

  // 批量更新FOB价格
  handleUpdateFOBPrice() {
    if (!this.checkedInfo?.count) {
      this._notice.error(this._service.translateValue('flss.message.select-least-one'), '');
      return;
    }
    const order_ids =
      this.checkedInfo?.list?.map((item: any) => {
        return item?.id;
      }) || [];
    this.modalService
      .create({
        nzWidth: 450,
        nzTitle: this._service.translateValue(this.translateName + '批量更新'),
        nzMaskClosable: false,
        nzContent: BatchUpdateFobPriceComponent,
        nzBodyStyle: {
          padding: '0',
        },
        nzComponentParams: {
          order_ids,
        },
        nzFooter: null,
      })
      .afterClose.subscribe(() => {});
  }

  handleUpdateImages() {
    if (!this.checkedInfo?.count) {
      this._notice.error(this._service.translateValue('flss.message.select-least-one'), '');
      return;
    }
    if (this.checkedInfo.list.some((item) => ![1, 2, 4, 9].includes(item.order_status))) {
      this._notice.error(
        this._service.translateValue(this.translateName + '只有待提交、待审核、待修改、修改待审核状态的订单可以批量传图'),
        ''
      );
      return;
    }
    this.openUploadDraer(this.checkedInfo.list);
  }

  handleERPAsyn() {
    this._service.jstSync().subscribe((res) => {
      if (res.code === 200) {
        this.reset();
      }
    });
  }

  openUploadDraer(list: any[]) {
    this._drawerHelp
      .openDrawer({
        title: this._service.translateValue(this.translateName + '批量上传图片'),
        height: 'calc(100% - 48px)',
        content: UploadImagesComponent,
        contentParams: {
          list,
        },
      })
      .subscribe((params: any) => {
        if (params) {
          this._service.batchImportOrderPicture(params).subscribe((res) => {
            if (res.code === 200) {
              this.getList();
            }
          });
        }
      });
  }

  // 去询价
  priceInquiry() {
    if (!this.checkedInfo?.count) {
      this._notice.error('请至少选择一条数据', '');
      return;
    }
    const isInvalidCodes: string[] = [];
    const ids: string[] = [];
    this.checkedInfo?.list.forEach((item) => {
      [OrderStatus.auditPass].includes(item.order_status) ? ids.push(item.id) : isInvalidCodes.push(item.io_code);
    });
    if (isInvalidCodes.length) {
      this._notice.warning(`订单${isInvalidCodes.join('、')}未审核通过，不可去询价`, '');
      return;
    }
    this._service.orderInquiry({ ids: ids }).subscribe((res: any) => {
      if (res.code == 200) {
        this._msg.success('操作成功');
        const resultIds = res.data.ids ?? [];
        const hasAuth = (this._storage.getUserActions('quote-price-manage/quote-price-inquiry') as string[]).includes(
          'dev-manage:quote-price-inquiry-list'
        );
        if (!hasAuth) return;
        // 多条数据跳转到列表页
        if (resultIds.length > 1) {
          this._router.navigate(['/quote-price-manage/quote-price-inquiry/list']);
          return;
        }
        // 单条数据跳转到详情
        if (this.checkedInfo?.list.length == 1) {
          this._router.navigate([`/quote-price-manage/quote-price-inquiry/list/inquiry/${resultIds[0]}`]);
          return;
        }
      }
    });
    // 前端询价
    // 多条数据跳转到列表页
    // if (this.checkedInfo?.list.length > 1) {
    //   this._router.navigate(['/quote-price-manage/quote-price-inquiry/list']);
    //   return;
    // }
    // // 单条数据跳转到详情
    // if (this.checkedInfo?.list.length == 1) {
    //   this._router.navigate(['/quote-price-manage/quote-price-inquiry/list/inquiry/new'], {
    //     queryParams: { order_uuid: this.checkedInfo?.list[0].io_uuid },
    //   });
    //   return;
    // }
  }

  handleTimeFilter(value: any) {
    this.searchData.create_time = value.length ? [Number(value[0]), Number(value[1])] : null;
    this.onSearch();
  }

  handlePanelSelect(value: any) {
    if (value == null) {
      this.searchData.status = null;
    } else {
      const statusMap: any = { 100: null, 200: 11, 300: 4 };
      this.searchData.status = statusMap[value] ? statusMap[value].toString() : null;
    }
    this.orderStatusList.forEach((e) => {
      (e as any).checked = e.value == this.searchData.status;
    });
    this.orderStatusList = [...this.orderStatusList];
    this.onSearch();
  }

  handleFolder() {
    this.resizePage();
  }
  // 导入结果弹框
  openImportResultModal() {
    this.modalService.create({
      nzContent: ImportResultComponent,
      nzTitle: this._service.translateValue(this.translateName + '导入结果'),
      nzWidth: '80%',
      nzClosable: true,
      nzWrapClassName: 'bulk-import-result-modal',
      nzFooter: [
        {
          label: this._service.translateValue('common.关闭'),
          type: 'primary',
          shape: 'round',
          onClick: () => this.modalService.closeAll(),
        },
      ],
      nzComponentParams: {
        dataList: this.importResList || [],
      },
    });
  }

  // 打开控制筛选项显示隐藏的功能
  customSearchListConfig = {
    localKey: 'bulkListSearchOptions',
    version: '1.2.0',
    translateName: this.translateName,
  };
  onOpenDrawerCustom() {
    this._drawerHelp.openDrawer({
      title: this._service.translateValue('flss.common.自定义/业务配置'),
      content: SearchOptionsCustomContainerComponent,
      placement: 'right',
      width: '480px',
      contentParams: {
        noticeText: this._service.translateValue(this.translateName + '筛选项字段'),
        configList: this.searchList,
        config: this.customSearchListConfig,
        saveConfig: (data: any) => {
          this.searchList = data;
          this._drawerHelp.closeDrawer();
        },
        cancelConfig: () => {
          this._drawerHelp.closeDrawer();
        },
      },
    });
  }
}
