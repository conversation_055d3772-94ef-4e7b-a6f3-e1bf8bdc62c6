import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FlcDrawerHelperService, FlcModalService, resizable } from 'fl-common-lib';
import { ProductionPlanShareService } from '../production-plan-share.service';
import { PlanOperateBtnEnum, ProductionPlanSubjectEventEnum } from '../model/production-plan.enum';
import { ProductionPlanV2Service } from '../production-plan-v2.service';
import { OrderAllocationListComponent } from '../components/order-allocation-list/order-allocation-list.component';
import { NzTreeNodeOptions } from 'ng-zorro-antd/tree';
import { PlanProductionLineViewComponent } from '../components/plan-production-line-view/plan-production-line-view.component';
import { NzMessageService } from 'ng-zorro-antd/message';
import { FactoryRequestPayload } from '../interface';
import { PlanListSearchComponent } from '../components/plan-list-search/plan-list-search.component';

@Component({
  selector: 'app-production-plan-v2-list',
  templateUrl: './production-plan-v2-list.component.html',
  styleUrls: ['./production-plan-v2-list.component.scss'],
})
@resizable()
export class ProductionPlanV2ListComponent implements OnInit {
  @ViewChild('ganttTable') ganttTable?: ElementRef;
  @ViewChild(PlanProductionLineViewComponent) planProductionLineViewComponent?: PlanProductionLineViewComponent;
  @ViewChild('appPlanListSearch') planListSearchComponent?: PlanListSearchComponent;
  isEdit = false;
  refreshLoading = false;
  allocateNum = 0; // 待分配订单数

  searchData: { order_uuids?: string[]; factory_codes?: string[]; style_codes?: string[]; is_pre_order?: boolean } = {};
  searchOptions: { [key: string]: any[] } = {};
  productionOptions: any = {};
  contentHeight = 0;
  constructor(
    private _flcDrawerHelperService: FlcDrawerHelperService,
    public _planShareService: ProductionPlanShareService,
    private _service: ProductionPlanV2Service,
    private _msg: NzMessageService,
    private _flcModalService: FlcModalService
  ) {}

  ngOnInit(): void {
    this.getAllocateOrderList();
    this.getPlanListOptions();
    (this as any).addResizePageListener();
  }

  ngOnDestroy(): void {
    (this as any).addResizePageListener();
  }

  ngAfterViewInit(): void {
    //Called after ngAfterContentInit when the component's view has been initialized. Applies to components only.
    //Add 'implements AfterViewInit' to the class.
    this.resizePage();
  }

  onButtonAction(action: PlanOperateBtnEnum) {
    switch (action) {
      case PlanOperateBtnEnum.edit:
        this.onEdit();
        break;
      case PlanOperateBtnEnum.exitEdit:
        if (!this._planShareService.undo_disabled) {
          const ref = this._flcModalService.confirmCancel({
            content: '修改的内容未保存或未发布，退出编辑后修改的内容将丢失',
            okText: '退出编辑',
            cancelText: '继续编辑',
          });
          ref.afterClose.subscribe((res) => {
            if (res) this.exitEdit();
          });
        } else {
          this.exitEdit();
        }
        break;
      case PlanOperateBtnEnum.allocateOrder:
        this.allocateOrder();
        break;
      case PlanOperateBtnEnum.revoke:
        this._planShareService.sendSubjectEvent(ProductionPlanSubjectEventEnum.revoke);
        break;
      case PlanOperateBtnEnum.publish:
        this.onPubilsh();
        break;
    }
  }

  resizePage() {
    setTimeout(() => {
      const offsetTop = this.ganttTable?.nativeElement.getBoundingClientRect().top || 0;
      this.contentHeight = window.innerHeight - offsetTop - 15;

      //content最小高度
      if (this.contentHeight < 200) {
        this.contentHeight = 200;
      }
    });
  }

  onSearch() {
    const _factory_codes: FactoryRequestPayload[] = [];
    this.searchData.factory_codes?.forEach((code: string) => {
      const [factory_code, line_no] = code.split('-');
      if (code.indexOf('-') > -1) {
        const _item = _factory_codes.find((item) => item.factory_code === factory_code);
        if (_item) {
          _item.production_line_nos.push(line_no);
        } else {
          _factory_codes.push({
            factory_code,
            production_line_nos: [line_no],
            is_all_lines: false,
          });
        }
      } else {
        const _factory_item = this.productionOptions.factory_codes?.find((item: any) => item.factory_code === factory_code);
        _factory_codes.push({
          factory_code: code,
          production_line_nos:
            _factory_item?.production_lines?.filter((item: any) => item.production_line_no).map((item: any) => item.production_line_no) ||
            [],
          is_all_lines: _factory_item.order_distribute_type === 1,
        });
      }
    });
    this._planShareService.searchData.factory_codes = _factory_codes || [];
    this._planShareService.searchData.order_uuids = this.searchData.order_uuids || [];
    this._planShareService.searchData.is_pre_order = this.searchData.is_pre_order;
    this._planShareService.searchData.style_codes = this.searchData.style_codes ?? [];
    if (this._planShareService.searchData.order_uuids.length) {
      const _option = this.searchOptions.order_uuids?.filter((item: any) =>
        this._planShareService.searchData.order_uuids.includes(item.value)
      );
      const _timeArrr: number[] = _option?.map((item: any) => item.start_time).filter((item) => item);
      const _min_time = _timeArrr?.length ? Math.min(..._timeArrr) : new Date().getTime();
      this._planShareService.setDefalutDate(_min_time);
      setTimeout(() => {
        this._planShareService.sendSubjectEvent(ProductionPlanSubjectEventEnum.scrollLeft, _min_time);
      });
    }
    this.planProductionLineViewComponent?.onSearch();
  }

  private onEdit() {
    // const _factory_codes: any[] = [];
    // selectedList.forEach((selectItem) => {
    //   const _factory_item = _factory_codes.find((item) => item.factory_code === selectItem.factory_code);
    //   const _factory_option = this.productionOptions.factory_codes?.find((item: any) => item.factory_code === selectItem.factory_code);
    //   if (!_factory_item) {
    //     _factory_codes.push({
    //       factory_code: selectItem.factory_code,
    //       production_line_nos: _factory_option?.order_distribute_type === 1 ? [] : [selectItem.production_line_no],
    //       is_all_lines: _factory_option?.order_distribute_type === 1,
    //     });
    //   } else {
    //     _factory_item.production_line_nos.push(selectItem.production_line_no);
    //   }
    // });
    this._planShareService.sendSubjectEvent(ProductionPlanSubjectEventEnum.onEdit);
  }

  private onPubilsh() {
    this._flcModalService.confirmCancel({ content: '是否对所有修改的订单进行发布？' }).afterClose.subscribe((res) => {
      if (!res) return;
      this._service.publishProductionPlan(this._planShareService.temp_session || '').subscribe((res) => {
        if (res.code === 200) {
          this._planShareService.temp_session = res.data.temp_session;
          this.exitEdit();
        }
      });
    });
  }

  private exitEdit() {
    this._service.exitEdit().subscribe((res) => {
      if (res.code === 200) {
        this.isEdit = false;
        if (this.planProductionLineViewComponent) {
          this.planProductionLineViewComponent.isEdit = false;
          this.planProductionLineViewComponent.selectedList = [];
          this.planProductionLineViewComponent.onSearch();
          this.planProductionLineViewComponent.getTempSessionList();
        }
        this.onRreash();
      }
    });
  }

  onReset() {
    if (!this.isEdit) {
      this.searchData = {};
      this._planShareService.resetSearDate();
      this.planProductionLineViewComponent!.selectedList = [];
    }
    this.planProductionLineViewComponent?.onSearch();
    this.onRreash();
  }

  isHold = true;
  onHold() {
    this.isHold = !this.isHold;
    this.resizePage();
  }

  getPlanListOptions() {
    this._service.getProductionOptions().subscribe((res: any) => {
      this.productionOptions = res.data;
      const _treeOption: NzTreeNodeOptions[] = (res.data.factory_codes as any[]).map((item: any) => {
        return {
          key: item.factory_code,
          title: item.factory_name,
          isLeaf: item.order_distribute_type === 1,
          children:
            item.production_lines
              ?.filter((child: any) => child.production_line_no)
              .map((line: any) => {
                return {
                  key: item.factory_code + '-' + line.production_line_no,
                  title: line.production_line_name,
                  isLeaf: true,
                };
              }) || [],
        };
      });
      this.searchOptions = {
        order_uuids: res.data.order_uuids,
        factory_codes: _treeOption,
        style_codes: res.data.style_codes,
      };
    });
  }

  // 待分配订单
  private allocateOrder() {
    const factory_items = this.planProductionLineViewComponent?.selectedList.map((item) => {
      return {
        factory_code: item.factory_code,
        production_line_no: item.production_line_no || null,
      };
    });
    this._flcDrawerHelperService
      .openDrawer({ title: '待分配订单', content: OrderAllocationListComponent, contentParams: { isEdit: this.isEdit, factory_items } })
      .subscribe(() => {
        this.planProductionLineViewComponent?.refresh();
        this.getAllocateOrderList();
      });
  }

  private getAllocateOrderList() {
    this._service.getAllocateOrderList({ size: 10, page: 1 }).subscribe((res) => {
      this.allocateNum = res.data.total;
    });
  }

  onRreash() {
    this.getAllocateOrderList();
    this.getPlanListOptions();
  }

  onSearchWithParams(params: any) {
    this.planListSearchComponent?.onSearchWithParams(params);
  }
}
