import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { endOfDay, format, startOfDay } from 'date-fns';
import { FlcOssUploadService, resizable } from 'fl-common-lib';
import { NzCascaderOption } from 'ng-zorro-antd/cascader';
import { Subscription } from 'rxjs';
import { MappedType, SearchModal } from '../modal/sample-outsourcing.interface';
import { SampleOutsourcingService } from '../sample-outsourcing.service';
import { searchOptions, initialTableHeader } from './smaple-outsourcing.config';

@Component({
  selector: 'app-sample-outsourcing-list',
  templateUrl: './sample-outsourcing-list.component.html',
  styleUrls: ['./sample-outsourcing-list.component.scss'],
})
@resizable()
export class SampleOutsourcingListComponent implements OnInit {
  translateSuffix = 'sampleOutsourcing.';
  constructor(
    private _router: Router,
    private _activeRoute: ActivatedRoute,
    private _translate: TranslateService,
    private ossUploadService: FlcOssUploadService,
    private _service: SampleOutsourcingService
  ) {}

  private _eventSubscription?: Subscription;
  translateSubject?: Subscription; // 国际化监听
  /***** 表单筛选项 ****/
  searchOptionUrl = this._service.searchOptionUrl;
  searchOptions = searchOptions;
  searchData: Partial<MappedType<SearchModal>> & { status?: number[] | null } = {};

  permission: string[] = [];
  ngOnInit() {
    this.onSearch();
    (this as any).addResizePageListener();
    this.permission = this._service.getUserActions();
    this._eventSubscription = this._service.refreshEvent.subscribe(() => {
      this.getList();
    });
    this.translateSubject = this._service.translateEventEmitter.subscribe(() => {
      setTimeout(() => {
        this.resizePage();
      });
    });
  }

  translate(key: string): string {
    return this._translate.instant(key);
  }

  /***** 页面自适应 ****/
  @ViewChild('header') headerComponent?: ElementRef;
  @ViewChild('quickStatus') quickStatusComponent?: ElementRef;
  resizePage() {
    setTimeout(() => {
      const header_height = this.headerComponent?.nativeElement?.clientHeight;
      const quickStatus_height = this.quickStatusComponent?.nativeElement?.clientHeight;
      let height = window.innerHeight - header_height - quickStatus_height - 144;
      if (height < 200) {
        height = 200;
      }
      this.tableConfig = { ...this.tableConfig, height: height };
    });
  }
  ngAfterViewInit(): void {
    this.resizePage();
  }
  ngOnDestroy() {
    (this as any).removeResizePageListener();
    this._eventSubscription?.unsubscribe();
    this.translateSubject?.unsubscribe();
  }

  // 获取款式下拉数据
  styleList: NzCascaderOption[] = [];
  openCascader(e: any) {
    if (e) {
      this._service.getStyleOptions().subscribe((res) => {
        this.styleList = res.data;
      });
    }
  }

  /***** 表格基础功能 ****/
  tableHeader = initialTableHeader();
  tableConfig = {
    dataList: <any>[],
    count: null,
    height: 500,
    loading: false,
    pageSize: 20,
    pageIndex: 1,
    translateName: this.translateSuffix + 'sampleOutsourcingTable.',
  };

  onSearch(): any {
    this.tableConfig.pageIndex = 1;
    this.getList();
  }

  onRest() {
    // 重置排序
    this.resetSortOrder();
    // 重置快捷筛选
    this.resetStatusCheck();
    // 重置筛选表单
    this.searchData = {};
    this.onSearch();
  }

  sizeChanges(size: number) {
    this.tableConfig = { ...this.tableConfig, pageSize: size };
    this.onSearch();
  }

  indexChanges(page: number) {
    this.tableConfig = { ...this.tableConfig, pageIndex: page };
    this.getList();
  }

  getList() {
    this.tableConfig = {
      ...this.tableConfig,
      loading: true,
    };
    const params = {
      where: this.handleWhere(),
      order_by: this.orderBy,
      limit: this.tableConfig.pageSize,
      page: this.tableConfig.pageIndex,
    };
    this._service.sampleList(params).subscribe(async ({ code, data }) => {
      if (code === 200) {
        this.setStatusAmount(data.status_statistics);
        this.tableConfig.count = data.count;
        // oss图片加签名
        for (const d of data.data_list) {
          for (const p of d.style_pic_list) {
            p.url = await this.ossUploadService.getSignatureUrl(p.url, p.version);
          }
        }
        this.tableConfig.dataList = data.data_list;
        this.tableConfig.loading = false;
        this.tableConfig = { ...this.tableConfig };
      }
    });
  }

  handleWhere() {
    const where: object[] = [];
    Object.entries(this.searchData).map(([key, value]) => {
      if (value) {
        switch (key) {
          case 'gen_time':
          case 'expected_delivery_date':
            if (Array.isArray(value) && value.length) {
              const startTime = format(startOfDay(value[0]), 'T');
              const endTime = format(endOfDay(value[1]), 'T');
              where.push({ column: key, op: 'between', value: [startTime, endTime] });
            }
            break;
          case 'style_id':
            if (Array.isArray(value) && value.length) {
              where.push({ column: 'first_style_id', op: '=', value: value[0] });
              where.push({ column: 'second_style_id', op: '=', value: value[1] });
              where.push({ column: 'third_style_id', op: '=', value: value[2] });
            }
            break;
          case 'status':
            where.push({ column: key, op: '=', value: (value as any)[0] });
            break;
          case 'brand_name':
            where.push({ column: key, op: 'like', value });
            break;
          default:
            where.push({ column: key, op: '=', value });
        }
      }
    });
    return where;
  }

  /***** 快捷筛选项 *****/
  statusOptions: any = [
    {
      label: this.translate(this.translateSuffix + 'sampleOutsourcingStatus.全部'),
      value: 0,
      key: 'total',
      isTotalItem: true,
      checked: true,
    },
    { label: this.translate(this.translateSuffix + 'sampleOutsourcingStatus.待提交'), value: 1, key: 'to_be_submitted' },
    { label: this.translate(this.translateSuffix + 'sampleOutsourcingStatus.待审核'), value: 2, key: 'to_be_reviewed' },
    { label: this.translate(this.translateSuffix + 'sampleOutsourcingStatus.待修改'), value: 3, key: 'to_be_modified' },
    { label: this.translate(this.translateSuffix + 'sampleOutsourcingStatus.待接单'), value: 4, key: 'wait_accept_order' },
    { label: this.translate(this.translateSuffix + 'sampleOutsourcingStatus.待收样'), value: 8, key: 'sample_to_be_received_total' },
    { label: this.translate(this.translateSuffix + 'sampleOutsourcingStatus.已收样'), value: 7, key: 'sample_received' },
    { label: this.translate(this.translateSuffix + 'sampleOutsourcingStatus.已取消'), value: -1, key: 'canceled' },
  ];

  setStatusAmount(data: any) {
    this.statusOptions.map((d: any) => {
      d.amount = data[d.key];
    });
  }

  onStatusCheck(checks: Array<number>) {
    this.searchData.status = checks.length ? checks : null;
    this.onSearch();
  }

  resetStatusCheck() {
    this.statusOptions.map((d: any) => {
      d.checked = d.isTotalItem;
    });
  }

  /***** 排序 *****/
  orderBy: Array<string> = [];
  sortOrderChange({ key, value }: { key: string; value: 'desc' | 'asc' | null }) {
    this.orderBy = value ? [key + ' ' + value] : [];
    this.getList();
  }

  resetSortOrder() {
    this.orderBy = [];
    this.tableHeader.map((item: any) => {
      item.sortOrderBy = null;
    });
    this.tableHeader = [...this.tableHeader];
  }

  /***** 表格数据处理 *****/
  categoryFormatter(item: any) {
    let text = '';
    if (item.first_style_name) {
      text += item.first_style_name;
    }
    if (item.second_style_name) {
      text += '-' + item.second_style_name;
    }
    if (item.third_style_name) {
      text += '-' + item.third_style_name;
    }
    return text;
  }

  sampleSortNumFormatter(item: any) {
    let text = '';
    if (item.sample_sort_num >= 0) {
      text = `${this.translate(this.translateSuffix + 'sampleOutsourcingPage.第')}${item.sample_sort_num}${this.translate(
        this.translateSuffix + 'sampleOutsourcingPage.轮'
      )}`;
    }
    return text;
  }

  /***** 列表操作按钮 *****/
  onCreate() {
    this._router.navigate(['../list/new'], { relativeTo: this._activeRoute });
  }
  jumpDetail(event: any) {
    this._router.navigate(['../list', event?.id], { relativeTo: this._activeRoute });
  }

  /* 折叠搜索框 */
  isFold = false;
  foldTooltip = 'btn.fold';
  onFold(val: any) {
    this.isFold = !val;
    this.foldTooltip = this.isFold ? 'btn.unfold' : 'btn.fold';
    this.resizePage();
  }
}
