.basic-info {
  background: #f7f8fa;
  border-radius: 0px 0px 8px 8px;
  padding: 8px 12px;
  color: #262d48;
  font-weight: 500;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: 8px;
  &-item {
    display: flex;
    align-items: center;
  }
  &-title {
    color: #54607c;
  }
}

:host ::ng-deep .ant-divider-vertical {
  margin: 4px 16px 0 16px;
  background: #d1d8e1;
}

:host ::ng-deep {
  nz-table {
    width: 100% !important;
  }
}
.template-cell {
  min-height: 42px;
  line-height: 42px;
  margin: 0 -8px;
  padding: 0 8px;
  position: relative;
  &:not(:last-child)::after {
    content: '';
    position: absolute;
    width: 100%;
    border-bottom: 1px solid #d4d7dc;
    left: 0;
    right: 0;
    bottom: 0;
  }
}

.table-wrapper {
  max-height: calc(80vh - 120px);
  overflow-y: auto;
  padding-right: 4px;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3px;
}

.no-data {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}
