import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { Component, OnInit, ViewChild, Input, Output, EventEmitter, OnDestroy } from '@angular/core';
import { subDays, addDays, format, endOfDay, startOfDay } from 'date-fns';
import { Observable, Subscription } from 'rxjs';
import {
  FactoryRequestPayload,
  ForwardConnectRequestPaylod,
  LineOrderInterface,
  PlanGraphActionTypeEnum,
  PlanGraphOperationTypeEnum,
  PlanListPayload,
  PlanMovedParam,
  ProductionLineResponse,
  ProductionPlanLineItem,
} from '../../interface';
import { ProductionLineViewGraphComponent } from './production-line-view-graph/production-line-view-graph.component';
import { ProductionPlanShareService } from '../../production-plan-share.service';
import { ProductionPlanV2Service } from '../../production-plan-v2.service';
import { ProductionPlanSubjectEventEnum } from '../../model/production-plan.enum';
import { BroadcastService, FlcModalService } from 'fl-common-lib';
import { MessageNotificationDrawerComponent } from './message-notification-drawer/message-notification-drawer.component';

@Component({
  selector: 'app-plan-production-line-view',
  templateUrl: './plan-production-line-view.component.html',
  styleUrls: ['./plan-production-line-view.component.scss'],
})
export class PlanProductionLineViewComponent implements OnInit, OnDestroy {
  @Input() isEdit = false;
  @Input() searchOptions: { [key: string]: any[] } = {};
  @ViewChild(ProductionLineViewGraphComponent) graphComponent!: ProductionLineViewGraphComponent;
  @ViewChild('appMessageNotificationDrawer') notiDrawerComp!: MessageNotificationDrawerComponent;
  @Output() changeEditValue = new EventEmitter();
  @Output() onSearchWithParams = new EventEmitter();
  isLoading = true;
  dates: any = {
    start_date: null,
    end_date: null,
  };
  graphOptions: { view: 'order' | 'productionLine'; item_dimension: 'io' | 'po' } = {
    view: 'productionLine',
    item_dimension: this._shareService.getProOrderDimension() || 'io',
  };
  pageConfig = {
    page: 1,
    size: 20,
    total: 0,
  };

  productionLineList: ProductionPlanLineItem[] = [];
  listObservable: Observable<any> | null = null;

  selectedList: ProductionPlanLineItem[] = [];

  constructor(
    public _shareService: ProductionPlanShareService,
    private _service: ProductionPlanV2Service,
    private _modal: NzModalService,
    private _msg: NzMessageService,
    private _broadcast: BroadcastService,
    private _flModalService: FlcModalService
  ) {}
  broadCastSubscription!: Subscription;
  ngOnInit(): void {
    this.dates = {
      start_date: this._shareService.searchData.start_date,
      end_date: this._shareService.searchData.end_date,
    };
    this.initProductionLineList();
    this.getNotificationCount();

    this.broadCastSubscription = this._broadcast.eavesdrop((event) => {
      if (event.channel === 'SwitchTabGlobalKey') {
        if (event.message.path === '/intellect-plan/production-plan') {
          this.getNotificationCount();
        }
      }
    });

    this._shareService.addSubjectListener(
      'list_line_view',
      [
        ProductionPlanSubjectEventEnum.onZoomChange,
        ProductionPlanSubjectEventEnum.onEdit,
        ProductionPlanSubjectEventEnum.move,
        ProductionPlanSubjectEventEnum.revoke,
      ],
      (res) => {
        // 缩放比例
        if (res.type === ProductionPlanSubjectEventEnum.onZoomChange) {
          this._shareService.rate = res.data ?? 1;
          this.graphComponent?.profileTable();
        } else if (res.type === ProductionPlanSubjectEventEnum.onEdit) {
          this.onEdit();
        } else if (res.type === ProductionPlanSubjectEventEnum.revoke) {
          this.onRevoke();
        } else if (res.type === ProductionPlanSubjectEventEnum.move) {
          this.onMove(res.data);
        }
      }
    );
  }

  ngOnDestroy(): void {
    this._shareService.removeSubjectListener('list_line_view');
    this.broadCastSubscription.unsubscribe();
  }

  async onSearch() {
    this.pageConfig.page = 1;
    this.pageConfig.size = 20;
    return await this.getProductionLineList();
  }

  private initProductionLineList() {
    this._service.getEditLockDetail().subscribe((res) => {
      if (res.code !== 200) {
        this.getProductionLineList();
        return;
      }
      const _list = res.data.locked_list.filter((item) => item.is_current_user_lock);
      if (_list.length === 0) {
        this.getProductionLineList();
        return;
      }
      const factory_codes: FactoryRequestPayload[] = [];
      _list.forEach((item) => {
        const _factory_item = factory_codes.find((_item) => _item.factory_code === item.factory_code);
        if (_factory_item) {
          _factory_item.production_line_nos.push(item.production_line_no);
        } else {
          factory_codes.push({
            factory_code: item.factory_code,
            production_line_nos: item.production_line_no ? [item.production_line_no] : [],
            is_all_lines: !item.production_line_no,
          });
        }
      });
      const params: PlanListPayload = {
        factory_codes: factory_codes,
        start_time: new Date(this._shareService.searchData.start_date).getTime(),
        end_time: new Date(this._shareService.searchData.end_date).getTime(),
        temp_session: this._shareService.temp_session ?? null,
        size: 100,
        page: 1,
      };
      this._service.getProductionLineList(params).subscribe((res) => {
        if (res.code === 200) {
          this.getList(res.data);
          this.selectedList = this.productionLineList;
          this.pageConfig.total = res.data.total;
          this.changeEditValue.emit();
        }
      });
      this.getTempSessionList();
    });
  }

  async getProductionLineList() {
    this.isLoading = true;

    const params: PlanListPayload = {
      factory_codes: this.isEdit ? this.getSelectFactoryCodePayload(this.selectedList) : this._shareService.searchData.factory_codes,
      order_uuids: this._shareService.searchData.order_uuids,
      style_codes: this._shareService.searchData.style_codes,
      start_time: Number(format(startOfDay(this._shareService.searchData.start_date), 'T')),
      end_time: Number(format(endOfDay(this._shareService.searchData.end_date), 'T')),
      temp_session: this._shareService.temp_session ?? null,
      size: this.pageConfig.size,
      page: this.pageConfig.page,
    };

    this.dates = {
      start_date: new Date(this._shareService.searchData.start_date),
      end_date: new Date(this._shareService.searchData.end_date),
    };

    const _str = `开始时间---${format(params.start_time, 'yyy-MM-dd')}\n结束时间---${format(params.end_time, 'yyy-MM-dd')}`;
    console.log(_str);

    return new Promise((resolve) => {
      this._service.getProductionLineList(params).subscribe((res) => {
        if (res.code === 200) {
          this.getList(res.data);
          this.pageConfig.total = res.data.total;
        } else if (String(res.code) === '40005') {
          this.planException();
        } else {
          this.isLoading = false;
        }
        resolve(res.code === 200);
        if (this.isEdit) this.getTempSessionList();
      });
    });
  }

  private getList(data: ProductionLineResponse) {
    // 是否可以撤销
    this.productionLineList = [];
    data?.list.forEach((item) => {
      const _item = new ProductionPlanLineItem(item, data?.order_info_map, this._shareService.searchData.order_uuids || []);
      if (!this.isEdit) {
        const _index = this.selectedList.findIndex(
          (select_item) => select_item.factory_code === _item.factory_code && select_item.production_line_no === _item.production_line_no
        );
        if (_index !== -1) {
          _item.isSelected = true;
          // 更新选中的item
          this.selectedList[_index] = _item;
        }
      }
      this.productionLineList.push(_item);
    });
    this._shareService.temp_session = data?.temp_session;
    this.isLoading = false;
    this.graphComponent.clearDetailBar();
  }

  onSelectLine(data: ProductionPlanLineItem | null) {
    const _list = data ? [data] : this.productionLineList;
    _list.forEach((item) => {
      if (item.isSelected) {
        this.selectedList.push(item);
      } else {
        this.selectedList = this.selectedList.filter(
          (select_item) => select_item.factory_code !== item.factory_code && item.production_line_no !== select_item.production_line_no
        );
      }
    });
  }

  // 存储选中项 取消恢复选中项
  private async onEdit() {
    if (!this.selectedList?.length) {
      this._msg.error('请勾选工厂后再编辑');
      return;
    }
    return new Promise((resolve) => {
      this._service.getEditLockDetail().subscribe((res) => {
        const _lockList: ProductionPlanLineItem[] = [];
        const _unLockList: ProductionPlanLineItem[] = [];
        this.selectedList.forEach((item) => {
          const _item = res.data.locked_list.find(
            (lockItem) => lockItem.factory_code === item.factory_code && lockItem.production_line_no == item.production_line_no
          );
          _item ? _lockList.push(item) : _unLockList.push(item);
        });

        if (_unLockList.length === 0) {
          this._msg.error('当前选中的工厂都已处于编辑中，请选择其他工厂!');
          return;
        }

        if (_lockList.length) {
          const _lockArr = _lockList.map((item) =>
            item.production_line_no ? `${item.factory_name} / ${item.production_line_name}` : item.factory_name
          );
          const _modal = this._flModalService.confirmCancel({
            title: '提示',
            content: `以下工厂/产线正被其他用户编辑中，是否自动选择当前可用的工厂/产线，直接进入编辑？<div> ${_lockArr.join('、')} </div>`,
          });
          _modal.afterClose.subscribe((res) => {
            if (res) {
              this.selectedList = _unLockList;
              this.productionLineList = this.selectedList;
              this.productionLineList.map((item) => (item.isSelected = false));
              this.onEditRequest();
            }
          });
        } else {
          this.productionLineList.map((item) => (item.isSelected = false));
          this.productionLineList = this.selectedList;
          this.onEditRequest();
        }
      });
      resolve(true);
    });
  }

  private onEditRequest() {
    const _payload = this.selectedList.map((item) => {
      return {
        factory_code: item.factory_code,
        production_line_no: item.production_line_no || '',
      };
    });
    this._service.startEdit(_payload).subscribe((res) => {
      if (res.code === 200) {
        this.isEdit = true;
        this.changeEditValue.emit();
      }
    });
  }

  // 右键操作
  onAction(event: { type: PlanGraphActionTypeEnum; data: any }) {
    switch (event.type) {
      case PlanGraphActionTypeEnum.split:
        this.splitOrder(event.data);
        break;
      case PlanGraphActionTypeEnum.forwardMerge:
        this.forwardMerge(event.data);
        break;
      case PlanGraphActionTypeEnum.forwardConnect:
        this.connectOrder(event.data);
        break;
      case PlanGraphActionTypeEnum.AllConnect:
        this.connectOrder(event.data);
        break;
      case PlanGraphActionTypeEnum.cancel:
        this.cancelOrder(event.data);
        break;
      case PlanGraphActionTypeEnum.replace:
        this.replaceOrder(event.data);
    }
  }

  private splitOrder(payload: { group_id: number; temp_session: string; split_date: number }) {
    this._service.splitOrder({ ...payload, temp_session: this._shareService.temp_session }).subscribe((res) => {
      if (res.code === 200) {
        this._shareService.temp_session = res.data.temp_session;
        this._shareService.undo_disabled = false;
        this.refresh();
      }
    });
  }

  private forwardMerge(payload: { temp_session: string; group_ids: number[] }) {
    this._service.forwardMerge({ ...payload, temp_session: this._shareService.temp_session }).subscribe((res) => {
      if (res.code === 200) {
        this._shareService.temp_session = res.data.temp_session;
        this._shareService.undo_disabled = false;
        this.refresh();
      }
    });
  }

  private connectOrder(payload: ForwardConnectRequestPaylod) {
    this._service.connectOrder({ ...payload, temp_session: this._shareService.temp_session }).subscribe((res) => {
      if (res.code === 200) {
        this._shareService.temp_session = res.data.temp_session;
        this._shareService.undo_disabled = false;
        this.refresh();
      }
    });
  }

  private cancelOrder(payload: { temp_session: string; group_id: number }) {
    this._service.cancelAllocated({ ...payload, temp_session: this._shareService.temp_session }).subscribe((res) => {
      if (res.code === 200) {
        this._shareService.temp_session = res.data.temp_session;
        this._shareService.undo_disabled = false;
        this._msg.success('取消分配成功');
        this.refresh();
      } else if (String(res.code) === '40005') {
        this.planException();
      }
    });
  }

  private replaceOrder(data: any) {
    this._service.replaceOrder(data.payload).subscribe((res) => {
      if (res.code === 200) {
        data.modal?.close();
        this._shareService.temp_session = res.data.temp_session;
        this._shareService.undo_disabled = false;
        this._msg.success('替换成功');
        this.refresh();
      } else if (String(res.code) === '40005') {
        this.planException();
      }
    });
  }

  // 撤销
  private onRevoke() {
    if (!this._shareService.temp_session) return;
    this._service.revoke(this._shareService.temp_session).subscribe((res) => {
      if (res.code === 200) {
        this._shareService.temp_session = res.data.temp_session;
        this.refresh();
      }
    });
  }

  onMove(params: PlanMovedParam) {
    // 校验订单计划开始时间超前物料齐套时间，不可分配！
    if (
      params.operation_type === PlanGraphOperationTypeEnum.movePosition &&
      params.pre_material_completed_time &&
      Number(format(startOfDay(params.pre_material_completed_time), 'T')) > params.target_time
    ) {
      this._msg.error('订单计划开始时间超前物料齐套时间，不可分配！');
      this.refresh();
      return;
    }

    // 大货单已外发，拖动到其它工厂或产线，这提示
    if (params.is_should_comfirm) {
      this._flModalService.confirmCancel({ content: '该订单已发单,确认调整?' }).afterClose.subscribe((res) => {
        res ? this.movePlan(params) : this.refresh();
      });
      return;
    }

    this.movePlan(params);
  }

  private movePlan(params: PlanMovedParam) {
    this.isLoading = true;
    this._service.movePlan({ ...params, temp_session: this._shareService.temp_session }).subscribe({
      next: (res) => {
        if (res.code === 200) {
          this._shareService.undo_disabled = false;
          this._shareService.temp_session = res.data.temp_session;
          this.refresh();
        }
      },
      error: () => {
        this.isLoading = false;
        this.refresh();
      },
    });
  }

  /**
   * 当操作甘特图发生异常时
   */
  planException() {
    console.error('操作产线视图发生异常');
    this._shareService.temp_session = '';
    this.getProductionLineList();
  }

  async resetEffortedData() {
    // this._cdr.detach();
    // try {
    //   const params = {
    //     production_line_nos: [],
    //     end_date: this._shareService.searchData.end_date ? format(new Date(this._shareService.searchData.end_date), 'yyyy-MM-dd') : null,
    //     start_date: this._shareService.searchData.start_date
    //       ? format(new Date(this._shareService.searchData.start_date), 'yyyy-MM-dd')
    //       : null,
    //     temp_session: this._shareService?.temp_session ?? null,
    //   };
    //   const listObserve = this._service.getProductionLineList(params).pipe(finalize(() => (this.isLoading = false)));
    //   const lineList = await lastValueFrom(listObserve);
    //   if (lineList?.code === 200) {
    //     return lineList?.data;
    //   } else if (String(lineList?.code) === '40005') {
    //     this.planException();
    //   }
    //   this._cdr.reattach();
    // } catch (error) {
    //   this._cdr.reattach();
    //   console.log(error);
    //   return null;
    // }
  }

  /**
   * 恢复勾选
   */
  recoverSelectedd() {
    this.productionLineList?.forEach((productionLine) => {
      productionLine.isSelected = false;
    });
  }

  refresh() {
    this.getProductionLineList();
  }

  getSelectedList(): LineOrderInterface[] {
    const selectedOrder: any = [];
    this.productionLineList.forEach((item) => {
      item.line_orders.forEach((order) => {
        if (order.isSelected) {
          selectedOrder.push(order);
        }
      });
    });
    return selectedOrder;
  }

  /**
   * 向前90天
   */
  forward() {
    this._shareService.searchData.start_date = subDays(new Date(this._shareService.searchData.start_date), 90);
    this.getProductionLineList();
  }

  /**
   * 向后90天
   */
  backwards() {
    this._shareService.searchData.end_date = addDays(new Date(this._shareService.searchData.end_date), 90);
    this.getProductionLineList();
    this.graphComponent?.moveDatePosition();
  }

  clearAll() {
    this.graphComponent?.clearAll();
  }

  private refreshList() {
    this._shareService.temp_session = '';
    this.graphComponent.clearDetailBar();
    this.getProductionLineList();
  }

  onPageIndexChange() {
    this.getProductionLineList();
  }

  onPageSizeChange(size: number) {
    this.pageConfig.size = size;
    this.getProductionLineList();
  }

  private getSelectFactoryCodePayload(selectedList: ProductionPlanLineItem[]): FactoryRequestPayload[] {
    const _factory_codes: FactoryRequestPayload[] = [];
    selectedList.forEach((selectItem) => {
      const _factory_item = _factory_codes.find((item) => item.factory_code === selectItem.factory_code);
      if (!_factory_item) {
        _factory_codes.push({
          factory_code: selectItem.factory_code,
          production_line_nos: selectItem.production_line_no ? [selectItem.production_line_no] : [],
          is_all_lines: !selectItem.production_line_no,
        });
      } else {
        _factory_item.production_line_nos.push(selectItem.production_line_no);
      }
    });
    return _factory_codes;
  }

  getTempSessionList() {
    return this._service.getTempSessionList().subscribe((res) => {
      if (res.code === 200) {
        if (res.data.temp_session_list?.length) {
          this._shareService.undo_disabled = false;
        } else {
          this._shareService.undo_disabled = true;
        }
      }
    });
  }

  /// 显示消息弹出框
  showNotiModal() {
    let params = null;
    if (this.isEdit) {
      const order_codes: any[] = [];
      this._shareService.searchData?.order_uuids?.forEach((e: any) => {
        const order = this.searchOptions.order_uuids?.find((order: any) => order.value === e);
        if (order) order_codes.push(order.label);
      });
      params = {
        ...this._shareService.searchData,
        order_codes: order_codes,
        factory_codes: this.selectedList.map((f: any) => {
          return { factory_code: f.rawData.factory_code, is_all_lines: false, production_line_nos: [f.rawData.production_line_no] };
        }),
      };
    }
    this.notiDrawerComp.onShow(params, { temp_session: this._shareService.temp_session });
  }

  /// 显示消息数量
  notificationMessageCount = 0;
  getNotificationCount() {
    this._service.getNotificationCount().subscribe((res: any) => {
      if (res.code === 200) {
        this.notificationMessageCount = res.data.total ?? 0;
      } else {
        this.notificationMessageCount = 0;
      }
    });
  }

  /// 点击消息弹出框-手动调整做数据筛选
  searchWithParams(params: any) {
    this.onSearchWithParams.emit(params);
  }

  /// 点击消息弹出框关闭 更细消息数量
  onHideDrawer() {
    this.getNotificationCount();
  }

  /// 点击消息弹出框-自动筛选需要强制到编辑状态
  async onAcceptAutoAdjustPlan(data: any) {
    if (!this.isEdit) {
      // 重置搜索框获取数据
      this._shareService.resetSearDate();
      this._shareService.searchData.factory_codes = [
        {
          factory_code: data.factory_code,
          production_line_nos: [data.production_line_no],
          is_all_lines: false,
        },
      ];
      const search = await this.onSearch();
      if (!search) return;
      const targerLine = this.productionLineList.find(
        (e) => e.factory_code === data.factory_code && e.production_line_no === data.production_line_no
      );
      if (!targerLine) return;
      this.selectedList = [targerLine];
      // 开始编辑 -- 后端会更新编辑状态
      this.isEdit = true;
      this.changeEditValue.emit();
      // const edit = await this.onEdit();
      // if (!edit) return;
    } else {
      // 如果已经处于编辑状态，需要刷新页面
      this.refresh();
    }
  }
}
