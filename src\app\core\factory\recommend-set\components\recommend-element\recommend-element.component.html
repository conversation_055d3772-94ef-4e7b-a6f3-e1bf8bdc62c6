<div class="element-wrap">
  <nz-input-group [nzPrefix]="iconSearch">
    <ng-template #iconSearch>
      <i nz-icon nzIconfont="icon-sousuoxiao"></i>
    </ng-template>
    <input nz-input flcInputTrim type="text" placeholder="搜索" [(ngModel)]="searchVal" (ngModelChange)="onSearch()" />
  </nz-input-group>

  <ul>
    <li *ngFor="let item of currentOptions" [ngClass]="{ checked: setOfChecked.has(item.key) }" (click)="onItemChecked(item.key)">
      <div class="label">
        <flc-text-truncated [data]="item.name"></flc-text-truncated>
      </div>
      <i *ngIf="setOfChecked.has(item.key)" nz-icon nzType="check" nzTheme="outline"></i>
    </li>
  </ul>

  <div class="action-btn-wrap">
    <label
      nz-checkbox
      [nzDisabled]="!!allChecked"
      [nzIndeterminate]="indeterminate"
      [(ngModel)]="allChecked"
      (ngModelChange)="onAllChecked($event)">
      全选
    </label>
  </div>
</div>
