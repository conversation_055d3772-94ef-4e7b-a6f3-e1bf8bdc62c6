export interface OptionItem {
  label: string;
  value: string;
}

// 大货单状态
export enum BulkStatus {
  toBeCommit = 1, // 待大货单提交
  toBeAudited = 2, // 待大货单审核
  toBeReceive = 3, // 待客户收货
  toBeModify = 4, // 待大货单修改
  finish = 5, // 大货完成
  toBeSchedule = 6, // 待大货排期
  toBeFinish = 7, // 待生产完成
  cancel = 8, // 已取消
}

export enum BULK_SUB_STATUS {
  toBeAudited = 1, // 待审核
  auditedPass = 3, // 审核通过(已取消)
  auditedNoPass = 2, // 审核未通过
}

export const searchData = [
  // 原料档案搜索初始数据
  {
    code: { value: null, searchType: 'like' },
    name: { value: null, searchType: 'like' },
    parent_id: { value: null, searchType: '=' },
    common_supplier_id: { value: null, searchType: '=' },
    composition: { value: null, searchType: 'like' },
    specifications: { value: null, searchType: 'like' },
    code_status: { value: null, searchType: '=' },
    color_info: { value: null, searchType: 'like' },
    weight: { value: [null, null], searchType: 'between' },
    price: { value: [null, null], searchType: 'between' },
    gen_userid: { value: null, searchType: '=' },
    sales_mode: { value: null, searchType: '=' },
    supplier_art_num: { value: null, searchType: 'like' },
  },
  // 辅料档案搜索初始数据
  {
    code: { value: null, searchType: 'like' },
    name: { value: null, searchType: 'like' },
    parent_id: { value: null, searchType: '=' },
    common_supplier_id: { value: null, searchType: '=' },
    composition: { value: null, searchType: 'like' },
    specifications: { value: null, searchType: 'like' },
    code_status: { value: null, searchType: '=' },
    color_info: { value: null, searchType: 'like' },
    price: { value: [null, null], searchType: 'between' },
    gen_userid: { value: null, searchType: '=' },
    sales_mode: { value: null, searchType: '=' },
    supplier_art_num: { value: null, searchType: 'like' },
  },
  // 洗唛搜索初始数据
  {
    code: { value: null, searchType: 'like' },
    name: { value: null, searchType: 'like' },
    parent_id: { value: null, searchType: '=' },
    composition: { value: null, searchType: 'like' },
    specifications: { value: null, searchType: 'like' },
    code_status: { value: null, searchType: '=' },
    gen_userid: { value: null, searchType: '=' },
    sales_mode: { value: null, searchType: '=' },
    series: { value: null, searchType: '=' },
    brand: { value: null, searchType: '=' },
  },
];

export const FabricFileTblConfig: any = {
  code: { key: 'code', label: '面料编码', width: 200, visible: true, required: true, nzLeft: true },
  name: { key: 'name', label: '面料名称', width: 200, visible: true, required: true, nzLeft: true },
  material_pictures: { key: 'material_pictures', label: '图片', width: 120, visible: true },
  parent_name: { key: 'parent_name', label: '分类', width: 120, visible: true },
  weight: { key: 'weight', label: '克重', width: 120, visible: true },
  width: { key: 'width', label: '门幅', width: 150, visible: true },
  composition: { key: 'composition', label: '成分', width: 130, visible: true },
  specifications: { key: 'specifications', label: '规格', width: 140, visible: true },
  supplier: { key: 'supplier', label: '供应商', width: 120, visible: true },
  supplier_art_num: { key: 'supplier_art_num', label: '供应商货号', width: 160, visible: true },
  price: { key: 'price', label: '价格(元/米)', width: 150, visible: true },
  unit: { key: 'unit', label: '单位', width: 120, visible: true },
  color_list: { key: 'color_list', label: '供应商色号', width: 120, visible: true },
  sales_mode_value: { key: 'sales_mode_value', label: '销售模式', width: 120, visible: true },
  modify_time: { key: 'modify_time', label: '修改时间', width: 120, visible: true, sortable: true },
  user_name: { key: 'user_name', label: '创建人', width: 120, visible: true },
  code_status: { key: 'code_status', label: '状态', width: 120, visible: true, sortable: true },
};

export const AccessoryFileTblConfig: any = {
  code: { key: 'code', label: '辅料编码', width: 120, visible: true, required: true, nzLeft: true },
  name: { key: 'name', label: '辅料名称', width: 120, visible: true, required: true, nzLeft: true },
  material_pictures: { key: 'material_pictures', label: '图片', width: 120, visible: true },
  parent_name: { key: 'parent_name', label: '分类', width: 120, visible: true },
  composition: { key: 'composition', label: '成分', width: 130, visible: true },
  specifications: { key: 'specifications', label: '规格', width: 140, visible: true },
  supplier: { key: 'supplier', label: '供应商', width: 120, visible: true },
  supplier_art_num: { key: 'supplier_art_num', label: '供应商货号', width: 150, visible: true },
  price: { key: 'price', label: '价格', width: 120, visible: true },
  unit: { key: 'unit', label: '单位', width: 120, visible: true },
  color_list: { key: 'color_list', label: '供应商色号', width: 120, visible: true },
  sales_mode_value: { key: 'sales_mode_value', label: '销售模式', width: 120, visible: true },
  modify_time: { key: 'modify_time', label: '修改时间', width: 120, visible: true, sortable: true },
  user_name: { key: 'user_name', label: '创建人', width: 120, visible: true },
  code_status: { key: 'code_status', label: '状态', width: 120, visible: true, sortable: true },
};
