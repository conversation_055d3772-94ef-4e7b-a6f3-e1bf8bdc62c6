import { Component, Input, OnInit } from '@angular/core';
import { NzModalRef } from 'ng-zorro-antd/modal';
import { OrderTrackingLineModel } from '../order-tracking-model';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { OrderTrackingService } from '../order-tracking.service';
import { firstValueFrom } from 'rxjs';
import { getTime, isDate, startOfDay } from 'date-fns';
import { NzNotificationService } from 'ng-zorro-antd/notification';

@Component({
  selector: 'app-order-tracking-edit-dialog',
  templateUrl: './order-tracking-edit-dialog.component.html',
  styleUrls: ['./order-tracking-edit-dialog.component.scss'],
})
export class OrderTrackingEditDialogComponent implements OnInit {
  @Input() line!: OrderTrackingLineModel;
  @Input() tabIndex = 0;
  formGroup!: FormGroup;
  dateFormatter = 'yyyy-MM-dd';
  constructor(
    private _modal: NzModalRef,
    private _fb: FormBuilder,
    private _service: OrderTrackingService,
    private _notice: NzNotificationService
  ) {}

  ngOnInit(): void {
    this.buildForm();
  }
  buildForm() {
    this.formGroup = this._fb.group({
      id: { value: this.line.id, disabled: true },
      io_uuid: { value: this.line.io_uuid, disabled: true },
      order_code: { value: this.line.order_code, disabled: true },
      customer: { value: this.line.customer, disabled: true },
      category: { value: this.line.category, disabled: true },
      style_code: { value: this.line.style_code, disabled: true },
      color_names: { value: this.line.color_names, disabled: true },
      total_qty: { value: this.line.total_qty, disabled: true },
      due_times: { value: this.line.due_times, disabled: true },
      order_date: { value: this.line.order_date, disabled: true },
      material_code: { value: this.line.material_code, disabled: true },
      material_name: { value: this.line.material_name, disabled: true },
      fabric_material_name: { value: this.line.fabric_material_name, disabled: true },
      customer_data_plan_time: { value: this.line.customer_data_plan_time, disabled: false },
      customer_data_real_time: { value: this.line.customer_data_real_time, disabled: false },
      fitting_plan_time: { value: this.line.fitting_plan_time, disabled: false },
      fitting_real_time: { value: this.line.fitting_real_time, disabled: false },
      fabric_quality_plan_time: { value: this.line.fabric_quality_plan_time, disabled: false },
      fabric_quality_real_time: { value: this.line.fabric_quality_real_time, disabled: false },
      accessory_plan_time: { value: this.line.accessory_plan_time, disabled: false },
      accessory_real_time: { value: this.line.accessory_real_time, disabled: false },
      printing_plan_time: { value: this.line.printing_plan_time, disabled: false },
      printing_real_time: { value: this.line.printing_real_time, disabled: false },
      fabric_cylinder_plan_time: { value: this.line.fabric_cylinder_plan_time, disabled: false },
      fabric_cylinder_real_time: { value: this.line.fabric_cylinder_real_time, disabled: false },
      preproduction_plan_time: { value: this.line.preproduction_plan_time, disabled: false },
      preproduction_real_time: { value: this.line.preproduction_real_time, disabled: false },
      bulk_samle_plan_time: { value: this.line.bulk_samle_plan_time, disabled: false },
      bulk_samle_real_time: { value: this.line.bulk_samle_real_time, disabled: false },
      first_spreading_plan_time: { value: this.line.first_spreading_plan_time, disabled: false },
      first_spreading_real_time: { value: this.line.first_spreading_real_time, disabled: true },
      fabric_instock_plan_time: { value: this.line.fabric_instock_plan_time, disabled: false },
      fabric_instock_real_time: { value: this.line.fabric_instock_real_time, disabled: true },
      thread_instock_plan_time: { value: this.line.thread_instock_plan_time, disabled: false },
      thread_instock_real_time: { value: this.line.thread_instock_real_time, disabled: true },
      zipper_instock_plan_time: { value: this.line.zipper_instock_plan_time, disabled: false },
      zipper_instock_real_time: { value: this.line.zipper_instock_real_time, disabled: true },
      packing_instock_plan_time: { value: this.line.packing_instock_plan_time, disabled: false },
      packing_instock_real_time: { value: this.line.packing_instock_real_time, disabled: true },
      fabric_sign_line: { value: this.line.fabric_sign_line, disabled: false },
      fabric_requirements: { value: this.line.fabric_requirements, disabled: false },
      production_line_names: { value: this.line.production_line_names, disabled: true },
      sewing_start_plan_time: { value: this.line.sewing_start_plan_time, disabled: true },
      sewing_start_real_time: { value: this.line.sewing_start_real_time, disabled: true },
      sewing_end_plan_time: { value: this.line.sewing_end_plan_time, disabled: true },
      sewing_end_real_time: { value: this.line.sewing_end_real_time, disabled: true },
      daily_target_qty: { value: this.line.daily_target_qty, disabled: false },
      type_names: { value: this.line.type_names, disabled: true },
      factory_names: { value: this.line.factory_names, disabled: true },
      return_plan_time: { value: this.line.return_plan_time, disabled: false },
      return_real_time: { value: this.line.return_real_time, disabled: true },
      daily_return_target_qty: { value: this.line.daily_return_target_qty, disabled: false },
      inspection_mid_plan_time: { value: this.line.inspection_mid_plan_time, disabled: false },
      inspection_mid_real_time: { value: this.line.inspection_mid_real_time, disabled: false },
      inspection_last_plan_time: { value: this.line.inspection_last_plan_time, disabled: false },
      inspection_last_real_time: { value: this.line.inspection_last_real_time, disabled: false },
      outstock_plan_time: { value: this.line.outstock_plan_time, disabled: false },
      outstock_real_time: { value: this.line.outstock_real_time, disabled: true },
      version: { value: this.line.version, disabled: true },
    });
  }
  async submit() {
    const rawData = this.formGroup.getRawValue();
    Object.keys(rawData).forEach((key) => {
      const value = rawData[key];
      if (isDate(value)) {
        rawData[key] = getTime(startOfDay(value));
      }
    });
    const result = await firstValueFrom(this._service.saveOrderTrackingDetail(rawData));
    if (result.code === 200) {
      this._notice.success('保存成功', '');
      this._modal.close(true);
    }
  }

  close() {
    this._modal.close();
  }
}
