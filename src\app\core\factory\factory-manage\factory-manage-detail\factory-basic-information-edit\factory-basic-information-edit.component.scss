.basic-container {
  padding-bottom: 16px;

  .title-label {
    font-size: 16px;
    font-weight: 500;
    color: #54607c;
  }

  nz-divider {
    margin: 8px 0px 16px 0px;
  }

  .text-label-required:before {
    content: '*';
    color: red;
  }

  .text-label {
    font-size: 14px;
    font-weight: 500;
    color: #515661;

    &:after {
      content: '：';
    }
  }
  nz-form-item {
    margin: 0px;
  }

  .info-line {
    display: flex;
    width: 33.33%;
    gap: 2px;
    align-items: flex-start;

    nz-form-label {
      min-width: 25%;
      text-align: right;
      white-space: nowrap;
    }

    nz-form-control {
      width: 70%;
      font-size: 14px;
      font-weight: 500;
      color: #222b3c;

      nz-input-group > nz-input-number {
        width: calc(100% - 32px);
      }

      .ant-input-group.ant-input-group-compact {
        display: flex;
      }

      .unit-box {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background: #f7f8fa;
        border-radius: 0px 2px 2px 0px;
        border: 1px solid #d4d7dc;
        width: 32px;
      }
    }
  }
}
