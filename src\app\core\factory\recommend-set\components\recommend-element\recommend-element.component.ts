import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { RecommendElementObj } from '../../models/recommend.config';
import { Subject, debounce, distinctUntilChanged, timer } from 'rxjs';
import { RecommendKeyEnum } from '../../models/recommend.enum';

@Component({
  selector: 'app-recommend-element',
  templateUrl: './recommend-element.component.html',
  styleUrls: ['./recommend-element.component.scss'],
})
export class RecommendElementComponent implements OnInit, OnDestroy {
  @Input() set alreadyAdd(val: RecommendKeyEnum[]) {
    this.setOfChecked = new Set(val);
    this.refreshCheckedStatus();
  }

  @Output() addRecommend = new EventEmitter();

  allChecked = false;
  indeterminate = true;
  setOfChecked = new Set<RecommendKeyEnum>();

  allOptions = Object.values(RecommendElementObj);
  currentOptions = [...this.allOptions];

  searchVal = '';
  searchChange$ = new Subject<string>();

  constructor() {}

  ngOnInit() {
    this.addSearchListen();
  }

  ngOnDestroy() {
    this.searchChange$.unsubscribe();
  }

  onSearch() {
    this.searchChange$.next(this.searchVal);
  }

  addSearchListen() {
    this.searchChange$
      .pipe(
        debounce(() => timer(500)), //  等待，直到用户停止输入(500ms)
        distinctUntilChanged() //  等待，直到搜索内容发生了变化
      )
      .subscribe(() => {
        this.currentOptions = this.allOptions.filter((item) => {
          return item.name.indexOf(this.searchVal) > -1;
        });
        this.refreshCheckedStatus();
      });
  }

  updateCheckedSet(key: RecommendKeyEnum, checked: boolean): void {
    if (checked) {
      this.setOfChecked.add(key);
    } else {
      this.setOfChecked.delete(key);
    }
  }

  refreshCheckedStatus(): void {
    this.allChecked = this.currentOptions.every(({ key }) => this.setOfChecked.has(key));
    this.indeterminate = this.currentOptions.some(({ key }) => this.setOfChecked.has(key)) && !this.allChecked;
  }

  onAllChecked(checked: boolean): void {
    if (!checked) return;
    this.currentOptions.forEach(({ key }) => this.updateCheckedSet(key, checked));
    this.refreshCheckedStatus();
    this.addRecommend.emit(this.setOfChecked);
  }

  onItemChecked(key: RecommendKeyEnum): void {
    if (this.setOfChecked.has(key)) return;
    this.updateCheckedSet(key, !this.setOfChecked.has(key));
    this.refreshCheckedStatus();
    this.addRecommend.emit(this.setOfChecked);
  }
}
