import { Component, Input, OnInit, SimpleChanges } from '@angular/core';

@Component({
  selector: 'app-text-ellipsis',
  templateUrl: './text-ellipsis.component.html',
  styleUrls: ['./text-ellipsis.component.scss'],
})
export class TextEllipsisComponent implements OnInit {
  @Input() totalString = '';
  @Input() qty = 15;
  showEllipsis = false;
  showString = '';

  constructor() {}

  private _render() {
    if (this.totalString && this.totalString.length > this.qty) {
      this.showEllipsis = true;
      this.showString = `${this.totalString.substring(0, this.qty)}...`;
    } else {
      this.showEllipsis = false;
      this.showString = this.totalString;
    }
  }

  ngOnInit() {
    this._render();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.totalString && !changes.totalString.firstChange) {
      this._render();
    }
  }
}
