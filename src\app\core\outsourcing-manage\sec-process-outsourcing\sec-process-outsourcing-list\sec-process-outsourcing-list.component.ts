import { DatePipe } from '@angular/common';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { TranslatePipe } from '@ngx-translate/core';
import { endOfDay, format, startOfDay } from 'date-fns';
import { resizable } from 'fl-common-lib';
import { isNotNil } from 'ng-zorro-antd/core/util';
import { Subscription } from 'rxjs';
import { AppStorageService } from 'src/app/shared/app-storage.service';
import { OrderSecProcessOutsourcingService } from '../sec-process-outsourcing.service';
import { initSearchList, initialSecProcessOutSourcingListHeader } from './sec-process-outsourcing-list.config';

@Component({
  selector: 'app-sec-process-outsourcing-list',
  templateUrl: './sec-process-outsourcing-list.component.html',
  styleUrls: ['./sec-process-outsourcing-list.component.scss'],
  providers: [TranslatePipe, DatePipe],
})
@resizable()
export class SecProcessOutsourcingListComponent implements OnInit {
  @ViewChild('searchContainer') searchContainer!: ElementRef<HTMLElement>;
  searchOptionFetchUrl = this._service.optionsUrl;
  searchList = initSearchList();
  tableHeaders = initialSecProcessOutSourcingListHeader();

  searchParams: any = {
    io_code: null,
    customer_code: null,
    style: null,
    customer: null,
    contract_number: null,
    order_status: null,
    order_date: null,
    due_time: null,
    geb_user: null,
    create_time: null,
  };
  tableConfig: any = {
    translateName: 'outsourcingTableHeaderAndLabel.',
    detailBtn: true,
    dataList: [],
    count: 40,
    height: 500,
    loading: false,
    pageSize: 20,
    actionWidth: '112px',
    pageIndex: 1,
    version: '1.0.1',
  };
  translateSubject?: Subscription; // 国际化监听
  orderBy: Array<string> = []; // 排序
  selectedId: any;
  _searchParams = {};
  detailBackSubject?: Subscription; // 订阅详情返回事件
  constructor(
    private _router: Router,
    public _service: OrderSecProcessOutsourcingService,
    private datepipe: DatePipe,
    private _storage: AppStorageService
  ) {}
  ngOnInit(): void {
    this._service.btnArr = [...this._storage.getUserActions('outsourcing-manage/sec-process-outsourcing')];
    this._searchParams = JSON.parse(JSON.stringify(this.searchParams));
    this.getDataList();
    this.translateSubject = this._service.translateEventEmitter.subscribe(() => {
      this.resizePage();
    });
    (this as any).addResizePageListener();
    /*
     * 订阅详情页返回事件
     */
    this.detailBackSubject = this._service.eventEmitter.subscribe((res) => {
      if (res === 'refresh') {
        this.getDataList();
      }
    });
  }
  resizePage() {
    setTimeout(() => {
      const height = window.innerHeight - this.searchContainer?.nativeElement?.clientHeight - 147;
      this.tableConfig = { ...this.tableConfig, height: height };
    });
  }
  ngAfterViewInit() {
    setTimeout(() => {
      this.resizePage();
    }, 0);
  }
  ngOnDestroy(): void {
    this.translateSubject?.unsubscribe();
    this.detailBackSubject?.unsubscribe();
    (this as any).removeResizePageListener();
  }
  /**
   *重置
   */
  reset() {
    this.searchParams = JSON.parse(JSON.stringify(this._searchParams));
    this.tableHeaders.forEach((item: any) => {
      item.sortOrderBy = null;
    });
    this.orderBy = [];
    this.tableHeaders = [...this.tableHeaders];
    this.getDataList(true);
  }
  /**
   * 下拉选择搜索
   */
  onSearch() {
    this.tableConfig.pageIndex = 1;
    this.getDataList();
  }
  getDataList(reset = false) {
    this.tableConfig = { ...this.tableConfig, loading: true, pageIndex: reset ? 1 : this.tableConfig.pageIndex };
    const params = {
      page: this.tableConfig.pageIndex,
      limit: this.tableConfig.pageSize,
      order_by: this.orderBy,
      where: this.handleWhere(),
      cache: true,
    };
    this._service.getOutsourcingList(params).subscribe((res) => {
      res.data.data.forEach((item: any) => {
        item.po_due_times?.forEach((po_time: string, index: number) => {
          item.po_due_times[index] = this.datepipe.transform(po_time, 'yyyy/MM/dd');
        });
        item._extra_process_info = '';
        item.extra_process_info?.forEach((extra: any) => {
          item._extra_process_info += `${extra.extra_process_name}、`;
        });
        item._extra_process_info = item._extra_process_info.replace(/[、]$/, '');
      });
      this.tableConfig = { ...this.tableConfig, loading: false, count: res.data.total, dataList: [...res.data.data] };
    });
  }

  // Tool
  //  分页
  onIndexChange(page: number) {
    this.tableConfig = { ...this.tableConfig, pageIndex: page };
    this.getDataList();
  }
  onSizeChange(size: number) {
    this.tableConfig = { ...this.tableConfig, pageSize: size };
    this.getDataList(true);
  }
  // 更改了排序方式
  sortOrderChange({ value, key }: { value: 'desc' | 'asc' | null; key: string }) {
    this.orderBy = value ? [key + ' ' + value] : [];
    this.getDataList();
  }
  // 跳转详情页面
  jumpDetail(event: any) {
    this.selectedId = event?.id;
    this._router.navigate(['/outsourcing-manage/sec-process-outsourcing/list', event?.id], { queryParams: { io_id: event?.io_id } });
  }
  // 查询参数打包
  handleWhere() {
    const where: object[] = [];
    Object.entries(this.searchParams).forEach((item) => {
      if (isNotNil(item[1])) {
        if (['gen_time'].includes(item[0])) {
          if (Array.isArray(item[1]) && item[1].length > 0) {
            const startTime = format(startOfDay(item[1][0]), 'T');
            const endTime = format(endOfDay(item[1][1]), 'T');
            where.push({ column: item[0], op: 'between', value: [startTime, endTime].toString() });
          }
        } else {
          if ('customer' === item[0]) {
            where.push({ column: item[0], op: 'like', value: item[1] });
          } else {
            where.push({ column: item[0], op: '=', value: item[1] });
          }
        }
      }
    });
    return where;
  }
}
