import { Component, Input, OnInit } from '@angular/core';
import { configItem } from './interface';
import { CdkDrag, CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { isEqual, cloneDeep } from 'lodash';
@Component({
  selector: 'flc-search-options-custom-container',
  templateUrl: './search-options-custom-container.component.html',
  styleUrls: ['./search-options-custom-container.component.scss'],
})
export class SearchOptionsCustomContainerComponent implements OnInit {
  @Input() noticeText!: string;
  @Input() configList!: configItem[];
  @Input() config!: {
    localKey: string;
    version: string;
    translateName: string;
  };
  @Input() cancelConfig!: () => void;
  @Input() saveConfig!: (res: any) => any;
  disabledSortIndexList: Array<number> = [];
  basicList: configItem[] = [];
  constructor() {}

  ngOnInit(): void {
    this.initSort();
  }

  initSort() {
    this.basicList = cloneDeep(this.configList);
    this.disabledSortIndexList = [];
    this.basicList.forEach((item, index) => {
      if (item.disabledSort) {
        this.disabledSortIndexList.push(index);
      }
    });
  }

  onDragDrop(event: CdkDragDrop<string[]>) {
    moveItemInArray(this.basicList, event.previousIndex, event.currentIndex);
  }

  sortPredicate = (i: number, item: CdkDrag<configItem>) => {
    const sortedIndexList: number[] = [];
    item.data.forEach((sortItem: configItem, index: number) => {
      if (sortItem.disabledSort) {
        sortedIndexList.push(index);
      }
    });
    return isEqual(sortedIndexList, this.disabledSortIndexList);
  };

  cancel() {
    this.cancelConfig();
  }

  save() {
    this.saveToLocal();
    this.saveConfig(this.basicList);
  }

  // 保存到本地
  saveToLocal() {
    localStorage.setItem(
      this.config.localKey,
      JSON.stringify({
        configList: this.basicList,
        version: this.config.version,
      })
    );
  }
}
