import { Component, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { SysParametersService } from '../sys-parameters.service';
import { forkJoin } from 'rxjs';
import { SysParametersFormComponent } from './parameter-form/sys-parameters-form.component';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { FlcModalService } from 'fl-common-lib';
import { needStringfiedKeys } from '../sys-parameters.config';
import { SysSettingsServices } from 'fl-sewsmart-lib/common-services';

@Component({
  selector: 'app-sys-parameters-detail',
  templateUrl: './sys-parameters-detail.component.html',
  styleUrls: ['./sys-parameters-detail.component.scss'],
})
export class SysParametersDetailComponent implements OnInit {
  @ViewChild('paramsForm') paramsFormRef!: SysParametersFormComponent;

  isEditMode = false;
  paramsData: Record<string, any> = {};

  translateSuffix = 'sysSetting.sysParameters.common.';

  canEdit = false;

  constructor(
    private _service: SysParametersService,
    private _messageService: NzMessageService,
    private _noticeService: NzNotificationService,
    private _translate: TranslateService,
    private _systemSettingService: SysSettingsServices,
    private _flcModalService: FlcModalService
  ) {}

  ngOnInit() {
    this.initUserAction();
    this.getParametersConfig();
  }

  initUserAction() {
    const actionList = this._service.getUserActions();
    if (actionList) {
      this.canEdit = actionList.includes('settings:sys-parameters-update');
    }
  }

  canLeave() {
    return !(this.isEditMode && this.paramsFormRef.dirty);
  }

  onEdit() {
    this.isEditMode = true;
  }

  onCancel() {
    const _cancelFunc = () => {
      this.getParametersConfig();
      this.isEditMode = false;
    };
    if (this.paramsFormRef.dirty) {
      this._flcModalService
        .confirmCancel({
          type: 'confirm-leave',
          okText: this.translateValue('ok', 'btn.'),
          cancelText: this.translateValue('cancel', 'btn.'),
        })
        .afterClose.subscribe((res) => {
          if (res) {
            _cancelFunc();
          }
        });
    } else {
      _cancelFunc();
    }
  }

  onSubmit() {
    if (!this.paramsFormRef.valid) {
      this._noticeService.error(this.translateValue('requiredPromptMsg'), '');
      return;
    }
    this.setParametersConfig(this.paramsFormRef.formData);
  }

  getParametersConfig() {
    const reqList: Record<string, any> = {
      procurementInvetory: this._service.getProcurementInventorySettings(),
    };
    forkJoin(reqList).subscribe((res: any) => {
      const paramsData = {};
      res.procurementInvetory.data.list.forEach((param: { key: string; value: string }) => {
        if (needStringfiedKeys.includes(param.key)) {
          paramsData[param.key] = param.value?.length ? JSON.parse(param.value) : null;
        } else {
          paramsData[param.key] = param.value;
        }
      });
      this.paramsData = paramsData;
      if (res.procurementInvetory.code == 200) this._systemSettingService.updateData(res.procurementInvetory.data.list);
    });
  }

  setParametersConfig(formData: Record<string, any>) {
    const procurementInvetoryParams = [];
    for (const key in formData) {
      if (needStringfiedKeys.includes(key)) {
        procurementInvetoryParams.push({ key: key, value: formData[key] ? JSON.stringify(formData[key]) : null });
      } else {
        procurementInvetoryParams.push({ key: key, value: `${formData[key]}` });
      }
    }
    const reqList: Record<string, any> = {
      procurementInvetory: this._service.setProcurementInventorySettings({ list: procurementInvetoryParams }),
    };
    forkJoin(reqList).subscribe((res: any) => {
      if (res.procurementInvetory.code === 200) {
        this._messageService.info(this.translateValue('submit', 'success.'));
        this.getParametersConfig();
        this.isEditMode = false;
      }
    });
  }

  translateValue(key: string, suffix: string = this.translateSuffix) {
    return this._translate.instant(suffix + key);
  }
}
