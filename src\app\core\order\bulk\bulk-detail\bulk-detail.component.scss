.btn-box {
  button {
    margin-left: 8px;
  }
}
input {
  width: 100%;
  border: none;
  outline: medium;
  &::placeholder {
    color: #bfbfbf;
  }
}
.tip-yellow-text {
  font-size: 12px;
  color: #fb6401;
}
.tag-list {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  & > span {
    cursor: pointer;
  }
  & > span + span {
    margin-top: 12px;
  }
}
/* 多项选择框限制三行高度*/
:host ::ng-deep .ant-select-selector {
  max-height: 90px;
  overflow-x: hidden;
  overflow-y: auto;
}
.no-data-text {
  width: 100%;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: #97999c;
}
.search-select-loading {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 操作列样式
.action-column {
  text-align: center;
  padding: 8px 4px !important;

  .action-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;

    .action-btn {
      padding: 0;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;
      border-radius: 4px;
      transition: all 0.3s ease;

      i {
        font-size: 16px;
      }

      &:hover {
        transform: scale(1.1);
      }

      // 删除按钮 - 红色
      &.delete-btn {
        i {
          color: #ff4d4f;
        }

        &:hover {
          background-color: #fff2f0;
        }
      }

      // 添加按钮 - 蓝色
      &.add-btn {
        i {
          color: #1890ff;
        }

        &:hover {
          background-color: #f0f8ff;
        }
      }

      // 复制按钮 - 灰色
      &.copy-btn {
        i {
          color: #8c8c8c;
        }

        &:hover {
          background-color: #f5f5f5;
        }
      }
    }
  }
}
.fontW500 {
  font-weight: 500;
}
.pass-process {
  margin-bottom: 8px;
  .process-first {
    padding: 16px 0 16px 98px;
    background: #ffffff;
    border-radius: 4px 4px 12px 12px;
    position: relative;
    overflow: hidden;
    .process-title {
      position: absolute;
      z-index: 6;
      top: 0;
      left: 0;
      width: 72px;
      height: 32px;
      background: #f7fafe;
      border-radius: 4px 0px 16px 0px;
      color: #54607c;
      font-size: 14px;
      line-height: 32px;
      font-weight: 500;
      text-align: center;
    }
    .first-desc {
      margin-top: 10px;
      width: 124px;
      justify-content: center;
      color: #515665;
      font-size: 14px;
      font-weight: 500;
      overflow: hidden;
      span {
        display: block;
        float: left;
        width: 50%;
        padding: 0 2px;
        box-sizing: border-box;
        text-align: center;
        overflow: hidden;
      }
    }
    .process-item-num {
      float: left;
      width: 172px;
    }
    .process-item {
      float: left;
      width: 172px;
      position: relative;
      &::before {
        content: '';
        position: absolute;
        left: -48px;
        top: 15px;
        width: 48px;
        height: 1px;
        background: #d4d7dc;
        z-index: 0;
      }
      &:first-child::before {
        content: none;
      }

      .first-item {
        width: 124px;
        height: 31px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 16px;
        color: #54607c;
        font-size: 14px;
        line-height: 14px;
        background: #f8fafe;
        border-radius: 15px;
        border: 1px solid #eeeeee;
        font-weight: 500;
        position: relative;
        z-index: 1;
        span {
          flex: 5;
          text-align: center;
        }
        .item-sum {
          margin-left: 4px;
          flex: 6;
          font-size: 12px;
          line-height: 12px;
          color: #54607c;
        }
      }
    }
    .item-actived {
      .first-item {
        background: #e7f3fe;
        color: #138aff;
        border: 1px solid transparent;
      }
      .first-desc {
        .process-num {
          color: #138aff;
        }
      }
      &::before {
        background: #7fbcff;
      }
    }
  }
  .second-top-box {
    padding-bottom: 12px;
    div:first-child {
      display: flex;
    }
  }
  .process-second {
    padding: 0;
    position: relative;
    background: #ffffff;
    border-radius: 12px 12px 4px 4px;
    .bulk-out-box {
      display: flex;
      align-items: center;
    }
    .sencond-title {
      margin-top: 10px;
      position: relative;
      z-index: 2;
      background: #fff;
      display: flex;
      flex-direction: column;
      width: 98px;
      font-size: 14px;
      line-height: 22px;
      height: 22px;
      color: #222b3c;
      text-align: center;
      font-weight: 500;
      span {
        margin-bottom: 10px;
      }
    }
    &::before {
      position: relative;
      z-index: 9;
      display: block;
      width: 100%;
      content: '';
      height: 16px;
      border-top: 1px dashed #d4d7dc;
    }
  }
}
.head-title {
  display: flex;
  align-items: center;
}
.status {
  display: inline-block;
  height: 22px;
  font-size: 14px;
  padding: 0 8px;
  line-height: 22px;
  border-radius: 12px;
  margin-left: 6px;
}
.status1,
.status11 {
  // 待提交 审核通过 1 11
  background: #e7f3fe;
  color: #138aff;
}
.status2,
.status9 {
  // 待审核2
  background: #feefe5;
  color: #fc8334;
}
.status4,
.status10 {
  // 待修改 4
  background: #ffe8e4;
  color: #ff4a1d;
}
.status8 {
  // 已取消 8
  background: #fafbfd;
  color: #97999c;
}
.basic-box {
  padding: 0 8px;
  border-radius: 4px;
  background: #fff;
  margin-bottom: 8px;
}
.reason-box {
  display: flex;
  align-items: center;
  height: 40px;
  padding: 0 10px 8px;
  background: #fee4e4;
  color: #f74949;
  border-radius: 6px 6px 0px 0px;
  .reason-wrap {
    margin-left: 4px;
    flex: 1;
    overflow: hidden;
  }
}
.block-title {
  display: flex;
  justify-content: space-between;
  height: 40px;
  align-items: center;
  font-size: 16px;
  color: #222b3c;
  border-bottom: 1px solid #d4d7dc;
}
.basic-desc {
  padding-top: 16px;
  .desc-left {
    .basic-item {
      font-size: 14px;
      .basic-label {
        display: inline-block;
        text-align: right;
        color: #515665;
      }
      .basic-value {
        color: #222b3c;
        font-weight: 500;
        flex: 1;
      }
    }
    .ant-form-item {
      margin-bottom: 8px;
    }
    nz-form-item {
      nz-form-label {
        text-align: right;
      }
    }
    // .remark-width {
    //   // flex: 0 0 100%;
    // }
    // .ant-picker {
    //   // width: 100%;
    // }
  }

  .desc-right {
    width: 100%;
    padding-right: 20px;
    display: flex;
    justify-content: right;
  }
}
.all-screen {
  &:hover {
    cursor: pointer;
    color: #138aff;
  }
}
.append-box {
  width: 100%;
  display: flex;
  align-items: flex-start;
  line-height: 32px;
  .append-title {
    width: 122px;
    font-size: 14px;
    text-align: right;
    flex-shrink: 0;
    color: #515665;
  }
  .append-desc {
    flex: 1;
    overflow: scroll hidden;
    font-size: 12px;
    color: #97999c;
  }
}
.po-title {
  margin-top: 8px;
  display: flex;
  height: 32px;
  justify-content: space-between;
}
.title-left {
  display: flex;
  height: 32px;
  align-items: center;
  font-size: 12px;
  color: #97999c;
  .po-name {
    font-size: 14px;
    color: #222b3c;
    display: flex;
    height: 32px;
    align-items: center;
    font-weight: 500;

    &::before {
      margin-right: 4px;
      content: '';
      display: block;
      width: 3px;
      height: 14px;
      background: #54607c;
      border-radius: 2px;
    }
  }
}
.po-input {
  width: 130px;
}
.time-style {
  font-size: 12px;
  line-height: 12px;
  height: 12px;
  font-weight: normal;
  color: #515665;
}
:host ::ng-deep .ant-tabs-nav-list {
  margin-top: 8px;
}
:host ::ng-deep .ant-tabs-tab {
  position: relative;
  padding: 4px 5px !important;
  border: 1px solid #d4d7dc !important;
  border-radius: 4px 4px 0px 0px !important;
  min-width: 114px;
  display: flex;

  justify-content: center;
  &::before {
    border-bottom: 1px solid #d4d7dc !important;
  }
  &:hover {
    color: #222b3c;
  }
}
:host ::ng-deep .ant-tabs-nav {
  &::before {
    border-bottom: 1px solid #d4d7dc !important;
  }
}

:host ::ng-deep .ant-tabs-tab .anticon {
  margin-right: 0;
}
:host ::ng-deep .ant-tabs-tab-remove {
  position: absolute;
  display: flex;
  justify-content: center;
  width: 12px;
  height: 12px;
  font-size: 12px;
  line-height: 12px;
  top: -6px;
  right: -4px;
  color: #b5b8bf;
  align-items: center;
  &:hover {
    color: #000000;
    opacity: 0.7;
  }
}
.po-title-box {
  max-width: 302px;
  font-size: 16px;
  line-height: 18px;
  font-weight: 500;
  i {
    font-size: 14px;
    color: #727683;
  }
}
:host ::ng-deep .ant-tabs-tab-active,
:host ::ng-deep .ant-tabs-tab:hover {
  background: #eef7ff !important;
  border: 1px solid #138aff !important;
  border-bottom-color: #fff !important;
  .po-title-box {
    color: #138aff;
    .time-style {
      color: #138aff;
    }
    i {
      color: #138aff;
    }
  }
}
::ng-deep .bulk-po-tab-box .ant-tabs-nav-operations {
  flex-shrink: 0;
}
.size {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  column-gap: 10px;
  .line {
    margin: 0 6px 0 16px;
    width: 1px;
    height: 16px;
    background: #d4d7dc;
  }
}
.color-size-tile {
  display: flex;
  min-height: 44px;
  justify-content: space-between;
  background: #f9fafb;
  padding: 8px 6px 0 6px;
  border-radius: 4px 4px 0px 0px;

  .auto-sort-btn {
    font-size: 12px;
    &:hover {
      color: #138aff;
      border-color: #138aff;
    }
  }
}
.address-box {
  display: flex;
  padding: 6px;
  .add-item {
    display: flex;
    flex-wrap: nowrap;
    min-height: 32px;
    align-items: center;
    min-width: 250px;
    margin-right: 24px;
    span {
      flex-shrink: 0;
      color: #54607c;
    }
  }
}
.po-tab-box-bg {
  margin-top: -16px;
  padding: 5px;
  background: #f0f3fa;
  border-radius: 0px 0 4px 4px;
  border: 1px solid #d4d7dc;
  border-top: none;
  .po-tab-box-wrap {
    background: #fff;
    box-shadow: 0px -1px 3px 0px rgba(214, 222, 227, 0.5);
    border-radius: 4px;
  }
}
.sum-box {
  border-radius: 12px 12px 4px 4px;
  margin-top: -8px;
  .block-title {
    border-top: 1px dashed #d4d7dc;
    border-bottom: none;
  }
}
::ng-deep .bulk-remark-tip {
  .ant-tooltip-inner {
    width: 400px !important;
  }
}
:host ::ng-deep .spec-group-item .ant-select-selector {
  border-radius: 20px !important;
}
:host ::ng-deep .size .ant-form-item-label > label {
  color: #54607c;
}
:host ::ng-deep .ant-input-suffix {
  margin-right: -3px;
  color: rgba($color: #000000, $alpha: 0.45);
  font-size: 12px;
}

.fold-btn {
  color: #138aff;
  cursor: pointer;
  font-size: 14px;
}

.source-tag {
  font-weight: 500;
  font-size: 14px;
  color: #007aff;
  line-height: 24px;
  padding: 0 4px;
  border-radius: 4px;
  border: 1px solid #138aff;
  margin: 0 10px 0 10px;
}

.ant-form-item {
  margin-bottom: 8px;
}

.sort-btn-box {
  display: flex;
  justify-content: space-between;
}

.img-box {
  position: relative;
  width: 128px;
  height: 128px;
  border-radius: 2px;
  overflow: hidden;
  background: #fafbfd;
  text-align: center;
  border: 1px solid #eeeeee;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

:host ::ng-deep .append-desc .add-item-container .add-item {
  transform: scale(1.3) !important;
}

.ratio-cell {
  display: flex;
  align-items: center;
  gap: 8px;

  nz-input-number {
    width: 80px;
  }

  i {
    cursor: pointer;
    font-size: 16px;

    &[nzType='close'] {
      color: #ff4d4f;
    }

    &[nzType='check'] {
      color: #52c41a;
    }
  }
}

// 合计行样式
nz-table {
  ::ng-deep {
    tfoot {
      tr {
        td {
          background-color: #fafafa;
          font-weight: 500;
        }
      }
    }
  }
}

// 输入框样式
nz-input-number {
  width: 100px;
}
