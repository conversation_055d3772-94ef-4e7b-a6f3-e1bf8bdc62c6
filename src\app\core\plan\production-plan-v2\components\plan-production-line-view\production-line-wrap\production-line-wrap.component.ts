import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ProductionLinePlanRenderItem } from '../../../interface';
import { ProductionPlanShareService } from '../../../production-plan-share.service';

@Component({
  selector: 'app-production-line-wrap',
  templateUrl: './production-line-wrap.component.html',
  styleUrls: ['./production-line-wrap.component.scss'],
})
export class ProductionLineWrapComponent implements OnInit {
  @Input() isEdit = false;
  @Input() data!: ProductionLinePlanRenderItem;
  @Output() onSelectLine = new EventEmitter();

  constructor(public _service: ProductionPlanShareService) {}

  ngOnInit(): void {}
  // switchExpandStatus(status: boolean) {
  //   this.data.rawData.changeExpanded(status);
  //   this.expanded.emit(this.data);
  // }
}
