import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ResponseData } from '../../common/interface/http';
import { assignRoleDepartmentModel, assignRoleEmployeeModel, searchItemModel, userAssignRoleModel } from './assign-role.model';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root',
})
export class AssignRoleService {
  constructor(private _http: HttpClient, private _translate: TranslateService) {}
  translateValue(key: string, params?: any) {
    return this._translate.instant(key, params);
  }
  getGlobalInfo() {
    return this._http.get<ResponseData<{ company: string }>>('/global');
  }
  getDepartmentRoot() {
    return this._http.get<ResponseData<(assignRoleDepartmentModel | assignRoleEmployeeModel)[]>>('/role/depts');
  }
  getDepartmentDetail(id: number) {
    return this._http.get<ResponseData<assignRoleDepartmentModel>>(`/role/depts/cascade?id=${id}`);
  }
  searchDeptNEmp(keyword: string) {
    return this._http.get<ResponseData<searchItemModel[]>>(`/role/depts/search?name=${keyword}`);
  }
  saveAllChange(data: userAssignRoleModel[]) {
    return this._http.put<ResponseData<boolean>>('/role/binds', data);
  }
  // handlePartent(partent: assignRoleDepartmentModel | null, list: (assignRoleDepartmentModel | assignRoleEmployeeModel)[]) {
  //   list.forEach((item) => {
  //     item.partent = partent;
  //     if (item.type === 'dept') {
  //       if (item.children?.length > 0) {
  //         this.handlePartent(item, item.children);
  //       }
  //       if (item.employees?.length > 0) {
  //         this.handlePartent(item, item.employees);
  //       }
  //     }
  //   });
  // }
}
