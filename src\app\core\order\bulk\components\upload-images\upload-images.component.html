<div class="container">
  <form nz-form [formGroup]="formData">
    <nz-table
      nzBordered
      [nzData]="formData.get('orders')?.controls"
      [nzFrontPagination]="false"
      [nzShowPagination]="false"
      [nzWidthConfig]="['150px', '250px', 'auto', 'auto']"
      [nzScroll]="{ x: '100%', y: tableHeight }">
      <thead>
        <tr>
          <th>{{ translateName + '款式编码' | translate }}</th>
          <th>{{ translateName + '订单需求号' | translate }}</th>
          <th>{{ translateName + '款式图' | translate }}</th>
          <th>{{ translateName + '附件' | translate }}</th>
        </tr>
      </thead>
      <tbody class="table-body" formArrayName="orders">
        <tr *ngFor="let control of formData.get('orders')?.controls; index as i" [formGroupName]="i">
          <td *ngIf="control.get('rowspan').value" [rowSpan]="control.get('rowspan').value">
            {{ control.get('style_code').value }}
          </td>
          <td>{{ control.get('io_code').value }}</td>
          <td class="uploader-container">
            <nz-form-item>
              <nz-form-control>
                <flc-file-uploader
                  #uploaderRef
                  class="uploader"
                  string
                  [formControlName]="'pictures'"
                  mode="image"
                  [ossCustomConfig]="ossCustomConfig"></flc-file-uploader>
              </nz-form-control>
            </nz-form-item>
          </td>
          <td class="uploader-container">
            <nz-form-item>
              <nz-form-control>
                <flc-file-uploader
                  #uploaderRef
                  class="uploader"
                  [formControlName]="'files'"
                  mode="attachment"
                  [ossCustomConfig]="ossCustomConfig"></flc-file-uploader>
              </nz-form-control>
            </nz-form-item>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </form>
</div>

<div class="footer">
  <button nz-button [nzShape]="'round'" (click)="onClose()">{{ 'btn.cancel' | translate }}</button>
  <button nz-button nzType="primary" [nzShape]="'round'" (click)="onConfirm()">{{ 'btn.ok' | translate }}</button>
</div>
