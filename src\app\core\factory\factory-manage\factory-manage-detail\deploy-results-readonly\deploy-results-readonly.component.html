<div class="deploy-container">
  <div class="title-label">{{ '3.' }}&nbsp;&nbsp;{{ '部署结果' | translate }}</div>
  <nz-divider></nz-divider>
  <form nz-form *ngIf="_service.factoryForm" [formGroup]="_service.factoryForm">
    <div style="display: flex; flex-direction: column; gap: 16px">
      <div class="deploy-content">
        <div class="label-required">{{ '工厂环境地址' | translate }}：</div>
        <div>
          <div class="link-container">
            <p
              nz-typography
              [nzCopyable]="
                _service.factoryForm?.get('domain')?.value &&
                ![_service.factoryStatusEnum.WITHDRAWED, _service.factoryStatusEnum.CANCELLED].includes(_service.factoryStatus)
              "
              [nzContent]="_service.factoryForm?.get('domain')?.value"></p>
            <button
              nz-button
              nzType="link"
              *ngIf="_service.factoryStatus === _service.factoryStatusEnum.INIT_PENDING"
              (click)="getInitLink()">
              <i nz-icon [nzIconfont]="'icon-lianjie'"></i>{{ 'factory-btn.init-link' | translate }}
            </button>
          </div>
          <div
            class="notice-container"
            *ngIf="_service.factoryStatus === _service.factoryStatusEnum.INIT_PENDING && _service.factoryForm?.get('initial_link')?.value">
            <i nz-icon [nzIconfont]="'icon-chenggong'"></i>
            {{ 'factory-message.init-notice' | translate }}
          </div>
        </div>
      </div>
      <div class="deploy-content" *ngIf="_service.factoryStatus === _service.factoryStatusEnum.INIT_PENDING">
        <div>{{ '初始化链接' | translate }}：</div>
        <div>
          <div class="link-container">
            <p
              nz-typography
              [nzCopyable]="_service.factoryForm?.get('initial_link')?.value ? true : false"
              [nzContent]="_service.factoryForm?.get('initial_link')?.value"></p>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>
