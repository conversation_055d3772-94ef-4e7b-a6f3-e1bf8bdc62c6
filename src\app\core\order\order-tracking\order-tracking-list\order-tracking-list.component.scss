/*
** 页面结构： header + app-content
** 设置app-content最小高度
*/
:host ::ng-deep .app-content {
  min-height: calc(100vh - 124px);
}
:host ::ng-deep .tableArea td {
  word-break: break-all;
}
.tableArea {
  margin-top: 10px;
}
:host .planAndRealTime {
  display: flex;
  flex-direction: column;
  * {
    height: 100%;
  }
  .red {
    color: red;
  }
  .edit {
    margin: 0;
    padding: 0;
    font-weight: 500;
  }
  .plan {
    &::before,
    ::ng-deep span::before {
      content: '计划:';
      color: #222b3c;
    }
  }
  .real {
    &::before,
    ::ng-deep span::before {
      content: '实际:';
      color: #222b3c;
    }
  }
}
:host .fakeButton {
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  cursor: pointer;
  color: #4d96ff;
  &:hover,
  &:active {
    color: #75b3ff;
  }
}
