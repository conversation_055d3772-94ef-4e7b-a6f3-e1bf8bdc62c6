import { Directive, HostListener, Input } from '@angular/core';

@Directive({
  selector: '[disableOnClick]',
})
export class DisableOnClickDirective {
  @Input() disableOnClick: number | undefined;

  @HostListener('click', ['$event'])
  clickEvent(event: MouseEvent) {
    if ((event.currentTarget as HTMLButtonElement).disabled) {
      return;
    }

    const target = event.currentTarget as HTMLButtonElement;
    target.disabled = true;

    if (this.disableOnClick) {
      setTimeout(() => {
        target.disabled = false;
      }, this.disableOnClick);
    }
  }

  constructor() {}
}
