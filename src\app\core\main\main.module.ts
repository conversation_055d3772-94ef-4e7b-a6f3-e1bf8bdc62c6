import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MainComponent } from './main.component';
import { MainRoutingModule } from './main-routing.module';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { TranslateModule } from '@ngx-translate/core';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { NzIconModule, NzIconService } from 'ng-zorro-antd/icon';
import { SideBarComponent } from './side-bar/side-bar.component';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzAlertModule } from 'ng-zorro-antd/alert';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { DashboardComponent } from './dashboard/dashboard.component';
import { MainResolverService } from './main.resolver';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { OverlayModule } from '@angular/cdk/overlay';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SubMenuBarOverlayComponentComponent } from './sub-menu-bar-overlay-component/sub-menu-bar-overlay-component.component';
import { FlcComponentsModule, FlcDirectivesModule, FlcDrawerHelperService, FlcPipesModule } from 'fl-common-lib';
import { NgxQRCodeModule } from '@techiediaries/ngx-qrcode';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzBadgeModule } from 'ng-zorro-antd/badge';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { SampleNoticeComponent } from './sample-notice/sample-notice.component';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzSelectModule } from 'ng-zorro-antd/select';

@NgModule({
  declarations: [MainComponent, SideBarComponent, DashboardComponent, SubMenuBarOverlayComponentComponent, SampleNoticeComponent],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MainRoutingModule,
    TranslateModule,
    NzLayoutModule,
    NzMenuModule,
    NzIconModule,
    NzDropDownModule,
    NzBreadCrumbModule,
    NzDrawerModule,
    NzToolTipModule,
    NzAlertModule,
    NzDividerModule,
    NzTabsModule,
    NzButtonModule,
    NzPopconfirmModule,
    NzAutocompleteModule,
    NzInputModule,
    FlcDirectivesModule,
    FlcComponentsModule,
    NzBadgeModule,
    NzTableModule,
    FlcPipesModule,
    NzCheckboxModule,
    FormsModule,
    ReactiveFormsModule,
    ScrollingModule,
    OverlayModule,
    NgxQRCodeModule,
    NzSelectModule,
  ],
  providers: [MainResolverService, FlcDrawerHelperService],
})
export class MainModule {
  constructor(private iconService: NzIconService) {
    this.iconService.fetchFromIconfont({ scriptUrl: 'https://at.alicdn.com/t/c/font_2782026_sg4de5az9ih.js' });
  }
}
