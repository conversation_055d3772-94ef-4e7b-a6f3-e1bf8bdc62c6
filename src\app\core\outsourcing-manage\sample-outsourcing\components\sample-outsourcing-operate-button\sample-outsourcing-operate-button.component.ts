import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { SampleOutsouringOperateBtnEnum, SampleOutsouringStatusEnum } from '../../modal/sample-outsourcing.enum';
import { SampleOutsourcingService } from '../../sample-outsourcing.service';

@Component({
  selector: 'app-sample-outsourcing-operate-button',
  templateUrl: './sample-outsourcing-operate-button.component.html',
  styleUrls: ['./sample-outsourcing-operate-button.component.scss'],
})
export class SampleOutsourcingOperateButtonComponent implements OnInit {
  @Input() isEdit = false; //是否编辑
  @Input() status = SampleOutsouringStatusEnum.new; // 打样单状态
  @Output() onButtonAction = new EventEmitter();
  sampleOperateBtnEnum = SampleOutsouringOperateBtnEnum;
  sampleStatusEnum = SampleOutsouringStatusEnum;
  translateName = 'sampleOutsourcing.OperateBtnField.';
  permission: Array<string> = [];
  permissionPrefix = 'dev-manage:sample-outsourcing-';

  constructor(private _service: SampleOutsourcingService) {}

  ngOnInit(): void {
    this.permission = this._service.getUserActions();
  }

  handleButtonAction(key: string) {
    this.onButtonAction.emit(key);
  }
}
