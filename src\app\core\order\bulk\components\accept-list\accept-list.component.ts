import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { endOfDay, format, startOfDay } from 'date-fns';
import { FlcDrawerHelperService, FlcModalService } from 'fl-common-lib';
import { isNotNil } from 'ng-zorro-antd/core/util';
import { NzMessageService } from 'ng-zorro-antd/message';
import { BulkService } from '../../bulk.service';

@Component({
  selector: 'app-accept-list',
  templateUrl: './accept-list.component.html',
  styleUrls: ['./accept-list.component.scss'],
})
export class AcceptListComponent implements OnInit {
  @ViewChild('searchBarWrap') searchBarWrap!: ElementRef<HTMLElement>;
  translateName = 'prolist.';
  searchList: any = [
    {
      label: '客户大货单号',
      labelKey: 'io_code',
      valueKey: 'io_code',
      type: 'select',
    },
    // {
    //   label: '客户款号',
    //   labelKey: 'customer_style',
    //   valueKey: 'customer_style',
    //   type: 'select',
    // },
    // {
    //   label: '款式分类',
    //   labelKey: 'style',
    //   valueKey: 'style',
    //   type: 'cascader',
    // },
    // {
    //   label: '客户名称',
    //   labelKey: 'customer',
    //   valueKey: 'customer',
    //   type: 'select',
    // },
    {
      label: '下单时间',
      labelKey: 'order_date',
      valueKey: 'order_date',
      type: 'date',
    },
  ];
  searchData = {
    io_code: null,
    customer_style: null,
    style: null,
    customer: null,
    order_date: null,
    due_time: null,
  };
  tableHeader = [
    { label: '客户大货单号', key: 'io_code', visible: true, width: '90px', type: 'text', isHidePin: true, pinned: true, disable: false },
    { label: '下单时间', key: 'order_date', visible: true, width: '180px', type: 'datetime', pinned: false, disable: false },
    { label: '客户名称', key: 'customer', visible: true, width: '99px', type: 'text', pinned: false, disable: false },
    { label: '地区', key: 'area', visible: true, width: '120px', type: 'text', pinned: false, disable: false },
    { label: '款式图片', key: 'order_pictures', visible: true, width: '100px', type: 'image', pinned: false, disable: false },
    {
      label: '款式分类',
      key: 'material_name',
      formatter: this.getMaterialCustomName.bind(this),
      visible: true,
      width: '205px',
      type: 'text',
      pinned: false,
      disable: false,
    },
    { label: '客户款号', key: 'customer_style', visible: true, width: '120px', type: 'text', pinned: false, disable: false },
    { label: '件数', key: 'qty', visible: true, width: '130px', type: 'quantity', pinned: false, disable: false },
    {
      label: '期望交期',
      key: 'po_due_times',
      visible: true,
      width: '200px',
      type: 'template',
      templateName: 'po_due_times',
      pinned: false,
      disable: false,
    },
    { label: '订单类型', key: 'task_type', visible: true, width: '100px', type: 'text', pinned: false, disable: false },
  ];
  tableConfig: any = {
    translateName: this.translateName,
    detailBtn: false,
    dataList: [],
    count: null,
    height: 800,
    loading: false,
    tableName: 'prolist',
    pageSize: 20,
    pageIndex: 1,
    actionWidth: '120px',
  };
  styleList: any[] = []; // 款式下拉列表数据
  order_by: any = []; // 排序

  constructor(
    private _msg: NzMessageService,
    private _router: Router,
    private _drawerHelp: FlcDrawerHelperService,
    private _service: BulkService
  ) {}

  ngOnInit(): void {
    this.getList();
  }
  getDetail(id: number | string): void {
    this._drawerHelp.closeAllDrawer();
    this._router.navigate(['/orderpro/bulk', id], { queryParams: { order: 'waiting' } });
  }
  getType(item: any) {
    return item.production_type === 2 ? '二次工艺加工' : '成衣加工';
  }
  getMaterialCustomName(item: any) {
    if (item.first_material_name) {
      return `${item.first_material_name}-${item.second_material_name}-${item.third_material_name}`;
    }
    return '';
  }
  calcTableHeight() {
    const bodyHeight = document.querySelector('.ant-drawer-body')!.clientHeight;
    const searchBarHeight = this.searchBarWrap.nativeElement.clientHeight;
    const renderY = bodyHeight - searchBarHeight;
    const renderScrollY = renderY - 90;
    this.tableConfig.height = renderScrollY;
    this.tableConfig = { ...this.tableConfig };
  }
  accept(id: number) {
    this._service.postAccept({ id: id }).subscribe((res: any) => {
      if (res.code === 200) {
        this._service.isAccept = true;
        this._msg.create('success', this._service.translateValue(this.translateName + '接单成功'));
        this.getList(true);
      }
    });
  }
  /**
   * 打开款式下拉
   */
  openStyle(e: any) {
    if (e) {
      this._service.getStyle().subscribe((res: any) => {
        this.styleList = this.onTransOption(res.data.info);
      });
    }
  }
  /**
   * 处理is_leaf为isLeaf
   */
  onTransOption(value: any) {
    if (value.length) {
      value.forEach((node: any) => {
        node['isLeaf'] = node.is_leaf;
        if (node?.children && node?.children?.length && !node?.is_leaf) {
          this.onTransOption(node?.children);
        }
      });
      return value;
    }
  }
  // 更改了排序方式
  sortDataLists({ value, key }: { value: 'desc' | 'asc' | null; key: string }) {
    this.order_by = value ? [key + ' ' + value] : [];
    this.getList();
  }
  /**
   * 页码改变
   * @param  {number} e
   */
  indexChanges(e: number) {
    this.tableConfig.pageIndex = e;
    this.getList();
  }
  /**
   * 页数改变
   * @param  {number} e
   */
  sizeChanges(e: number) {
    this.tableConfig.pageIndex = 1;
    this.tableConfig.pageSize = e;
    this.getList();
  }
  handleWhere(reset = false) {
    let where: any = [];
    if (reset) {
      this.searchData = {
        io_code: null,
        customer_style: null,
        style: null,
        customer: null,
        order_date: null,
        due_time: null,
      };
    } else {
      Object.entries(this.searchData).forEach((item: any) => {
        if (isNotNil(item[1])) {
          if (item[0] === 'order_date') {
            if (item[1].length) {
              const startTime = format(startOfDay(item[1][0]), 'T');
              const endTime = format(endOfDay(item[1][1]), 'T');
              const value = startTime + ',' + endTime;
              where.push({ column: item[0], op: 'between', value: value });
            }
          } else if (item[0] === 'style') {
            if (item[1].length) {
              const materialList = [
                { column: 'first_material_name', op: '=', value: item[1][0] },
                { column: 'second_material_name', op: '=', value: item[1][1] },
                { column: 'third_material_name', op: '=', value: item[1][2] },
              ];
              where = [...where, ...materialList];
            }
          } else {
            where.push({ column: item[0], op: '=', value: item[1] });
          }
        }
      });
    }
    return where;
  }
  getList(reset = false) {
    if (this.tableConfig.loading) {
      this.tableConfig.pageIndex = 1;
      return;
    }
    this.tableConfig.pageIndex = reset ? 1 : this.tableConfig.pageIndex;
    this.tableConfig.loading = true;
    const data = {
      where: this.handleWhere(reset),
      order_by: this.order_by,
      limit: this.tableConfig.pageSize,
      page: this.tableConfig.pageIndex,
    };
    this.tableConfig = { ...this.tableConfig };
    this._service.postAcceptList(data).subscribe((res: any) => {
      if (res.code === 200) {
        this.tableConfig.dataList = res.data.data;
        this.tableConfig.count = res.data.total;
        this.tableConfig.loading = false;
        this.tableConfig = { ...this.tableConfig };
      }
    });
  }
}
