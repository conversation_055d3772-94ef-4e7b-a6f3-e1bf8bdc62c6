import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { OrganizationService } from '../organization.service';
import { FrequencyTypeEnum } from '../work-frequencies/work-frequencies.model';
import { format } from 'date-fns';
import { FlcValidatorService } from 'fl-common-lib';

@Component({
  selector: 'app-shift-setting',
  templateUrl: './shift-setting.component.html',
  styleUrls: ['./shift-setting.component.scss'],
})
export class ShiftSettingComponent implements OnInit {
  data!: any;

  id = 0;
  shiftSettingForm!: FormGroup;
  frequencyTypeEnum = FrequencyTypeEnum;
  detailData: any = {};

  constructor(private _fb: FormBuilder, private _service: OrganizationService, private _flcValidatorService: FlcValidatorService) {}

  ngOnInit() {
    this.getShiftSettingDetail();
  }

  getShiftSettingDetail() {
    const params = {
      department_code: this.data.department_code ?? '',
    };
    this._service.getShiftSettingDetail(params).subscribe((res: any) => {
      if (res.code === 200) {
        (this.id = res.data?.id ?? 0), this.createForm(res.data);
      }
    });
  }

  createForm(data?: any) {
    this.shiftSettingForm = this._fb.group({
      work_start_time: [this.handleTime(data?.work_start_time), [Validators.required]],
      work_end_time: [this.handleTime(data?.work_end_time), [Validators.required]],
      first_rest_start_time: [this.handleTime(data?.first_rest_start_time)],
      first_rest_end_time: [this.handleTime(data?.first_rest_end_time)],
      second_rest_start_time: [this.handleTime(data?.second_rest_start_time)],
      second_rest_end_time: [this.handleTime(data?.second_rest_end_time)],
      holiday_type: [data?.holiday_type ?? null], // 1.每周 2.每月
      work_frequencies: [this.handleFrequencies(data?.holiday_type, data?.holiday_weekday, data?.holiday_month)],
    });
  }

  handleTime(timeStr: string): Date | null {
    if (!timeStr) return null;
    const [hours, minutes] = timeStr.split(':').map(Number);
    const dateObject = new Date();
    dateObject.setHours(hours);
    dateObject.setMinutes(minutes);
    return dateObject;
  }

  handleFrequencies(holiday_type: any, holiday_weekday: Array<number>, holiday_month: Array<number>) {
    if (!holiday_type) return [];
    if (holiday_type === this.frequencyTypeEnum.week) {
      return holiday_weekday ?? [];
    } else {
      return holiday_month ?? [];
    }
  }

  /** 切换频次，重置频次内容选择 */
  frequencyChange() {
    this.shiftSettingForm.get('work_frequencies')?.setValue([]);
  }

  // 校验表单
  formIsInvalid(): boolean {
    return this._flcValidatorService.formIsInvalid(this.shiftSettingForm);
  }

  handleFormPayload() {
    const { department_id, department_name, department_code } = this.data;
    const value = this.shiftSettingForm.getRawValue();
    const work_start_time = this.handleFormatTime(value.work_start_time);
    const work_end_time = this.handleFormatTime(value.work_end_time);
    const first_rest_start_time = this.handleFormatTime(value.first_rest_start_time);
    const first_rest_end_time = this.handleFormatTime(value.first_rest_end_time);
    const second_rest_start_time = this.handleFormatTime(value.second_rest_start_time);
    const second_rest_end_time = this.handleFormatTime(value.second_rest_end_time);
    const payload = {
      id: this.id,
      department_code,
      department_id,
      department_name,
      work_start_time,
      work_end_time,
      first_rest_start_time,
      first_rest_end_time,
      second_rest_start_time,
      second_rest_end_time,
      holiday_type: value.holiday_type,
    };
    if (payload.holiday_type === this.frequencyTypeEnum.week) {
      payload['holiday_weekday'] = value?.work_frequencies ?? [];
    } else {
      payload['holiday_month'] = value?.work_frequencies ?? [];
    }
    return payload;
  }

  handleFormatTime(time: Date | null) {
    return time ? format(time, 'HH:mm') : '';
  }
}
