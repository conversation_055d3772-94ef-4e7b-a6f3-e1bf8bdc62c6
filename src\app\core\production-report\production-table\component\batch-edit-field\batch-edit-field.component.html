<div style="padding: 15px 10px">
  <form nz-form [formGroup]="validateForm" style="margin-top: 24px">
    <ng-container *ngFor="let item of formConfig">
      <nz-form-item>
        <nz-form-label [nzRequired]="item.required" class="info-label" [nzSpan]="8">
          {{ translateName + item.name | translate }}
        </nz-form-label>
        <nz-form-control [nzSpan]="14" [flcErrorTip]="translateName + item.name | translate">
          <ng-container *ngIf="item.type === 'input'">
            <nz-input-group [nzSuffix]="(validateForm.get(item.key)?.value?.length ?? 0) + '/' + item?.maxLength">
              <input
                nz-input
                flcInputTrim
                [formControlName]="item.key"
                [placeholder]="'placeholder.input' | translate"
                [maxlength]="item?.maxLength" />
            </nz-input-group>
          </ng-container>

          <ng-container *ngIf="item.type === 'select'">
            <!-- 下拉， 数据取自字典 -->
            <flc-dynamic-search-select
              [dataUrl]="item?.dataUrl || ''"
              [transData]="{ value: item?.transDataValue || 'value', label: item?.transDataLabel || 'label' }"
              [column]="item?.column || item?.key"
              [formControlName]="item.key">
            </flc-dynamic-search-select>
          </ng-container>

          <ng-container *ngIf="item.type === 'textarea'">
            <nz-textarea-count [nzMaxCharacterCount]="item?.maxLength" class="inline-count" style="width: 100%">
              <textarea
                [nzAutosize]="{ minRows: 1, maxRows: 4 }"
                nz-input
                [formControlName]="item.key"
                [placeholder]="'placeholder.input' | translate"
                [maxLength]="item?.maxLength"
                nzAutosize
                flcInputTrim></textarea>
            </nz-textarea-count>
          </ng-container>

          <ng-container *ngIf="['input-number', FieldTypeEnum.Number].includes(item.type)">
            <nz-input-number
              style="width: 100%"
              [formControlName]="item.key"
              [nzPlaceHolder]="'placeholder.input' | translate"
              [nzMin]="item?.min"
              [nzPrecision]="item?.precision"
              [nzMax]="item?.max">
            </nz-input-number>
          </ng-container>

          <ng-container *ngIf="[FieldTypeEnum.SingleChoice, FieldTypeEnum.MultipleChoice].includes(item.type)">
            <nz-select
              [nzAllowClear]="true"
              [nzShowSearch]="true"
              [formControlName]="item.key"
              [nzMode]="item?.type === FieldTypeEnum.MultipleChoice ? 'multiple' : 'default'"
              [nzPlaceHolder]="'placeholder.select' | translate">
              <nz-option *ngFor="let op of item?.options || []" [nzLabel]="op?.label" [nzValue]="op.value"></nz-option>
            </nz-select>
          </ng-container>

          <ng-container *ngIf="item.type === FieldTypeEnum.Attachment">
            <flc-file-gallery
              #fileGalleryRef
              [wrap]="false"
              [isEditMode]="true"
              [addBtnText]="translateName + '上传附件' | translate"
              [fileList]="validateForm.get(item.key)?.value"
              [fileMaxSize]="100"
              [fileMaxCount]="100"
              [galleryType]="'any'"
              (onUploaded)="onUploaded($event, item)"
              (onDeleted)="onDeleted($event, item)"></flc-file-gallery>
          </ng-container>

          <ng-container *ngIf="item.type === FieldTypeEnum.Date">
            <nz-date-picker
              [nzFormat]="'yyyy/MM/dd'"
              [nzPlaceHolder]="'placeholder.select' | translate"
              [formControlName]="item.key"
              (ngModelChange)="changeDate($event, item)"></nz-date-picker>
          </ng-container>

          <ng-container *ngIf="item.type === FieldTypeEnum.Percentage">
            <span> {{ getPercent(item) || '-' }}% </span>
          </ng-container>
        </nz-form-control>
      </nz-form-item>
    </ng-container>
  </form>

  <div class="action-btn-container">
    <button nz-button flButton="default" [nzShape]="'round'" (click)="onCancel()">{{ 'btn.cancel' | translate }}</button>
    <button nz-button nzType="primary" [nzShape]="'round'" (click)="onSave()">{{ 'btn.save' | translate }}</button>
  </div>
</div>
