<div class="tab-wrapper">
  <div>
    <span
      *ngFor="let tab of tabs; index as i"
      class="tab-item"
      [ngClass]="{ active: selectedTab === tab.value }"
      (click)="onTabChange(tab.value)">
      {{ 'productionReportTab.' + tab.name | translate }}
    </span>
  </div>
  <div class="btn-container">
    <span style="color: #54607c">{{ 'productionReportTab.展示维度' | translate }}：</span>
    <nz-radio-group [(ngModel)]="currentDimension" (ngModelChange)="onDimensionChange($event)">
      <label *ngFor="let dimension of dimensions" nz-radio [nzValue]="dimension.value">
        {{ 'productionReportTab.' + dimension.name | translate }}
      </label>
    </nz-radio-group>
    <nz-divider nzType="vertical" style="margin: 0 0 0 -16px"></nz-divider>
    <button nz-button flButton="border-reset" class="btn-radius-8px" nz-tooltip [nzTooltipTitle]="resetTip" (click)="onReset()">
      <i nz-icon [nzIconfont]="'icon-zhongzhi1'"></i>
    </button>
    <button
      nz-button
      flToggleButton
      class="btn-radius-8px"
      nz-tooltip
      (toggleActiveChange)="onToggle($event)"
      [toggleActive]="!isFold"
      [nzTooltipTitle]="toggleTip">
      <i nz-icon [nzIconfont]="'icon-shaixuan-xuanzhong'"></i>
    </button>
  </div>
</div>
