import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { finalize, Observable } from 'rxjs';

@Injectable()
export class AppResolverService implements Resolve<any> {
  env: any;
  constructor(private http: HttpClient, @Inject('environment') env: any) {
    this.env = env;
  }

  resolve(_route: ActivatedRouteSnapshot, _state: RouterStateSnapshot) {
    return new Observable((observer) => {
      if (!this.env.brand) {
        this.http
          .post('/common/source_type', null)
          .pipe(
            finalize(() => {
              observer.next();
              observer.complete();

              console.log(
                `%c brand %c ${this.env.brand}`,
                'background:#00af7f; color: #FFF; padding: 4px; border-radius: 2px 0 0 2px; font-size: 12px',
                'background:#000000; color: #FFF; padding: 4px; border-radius: 0 2px 2px 0; font-size: 12px'
              );
            })
          )
          .subscribe((res: any) => {
            this.env.brand = res.data;
            localStorage.setItem('brand', res.data);
          });
      } else {
        observer.next();
        observer.complete();
      }
    });
  }
}
