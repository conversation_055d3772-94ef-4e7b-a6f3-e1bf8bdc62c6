$highlightColor: #138aff;
$highlightIconColor: #0f86f8;
$highlightHoverColor: #4d96ff;
$highlightColor2: #007aff;

// 虚线
.dotted-line {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    margin: 0 auto;
    display: block;
    width: calc(100% - 16px);
    border-bottom: 1px solid transparent;
    padding: 1px 0;
    background: linear-gradient(white, white) padding-box, repeating-linear-gradient(-45deg, #ccc 0, #ccc 0.25em, white 0, white 0.5em);
  }
}

.plant-item {
  background: #f2f9ff;
  border-radius: 4px;
  padding: 8px;
  margin-top: 8px;

  .plant-item-header-line {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      width: calc(100% + 16px);
      display: block;
      height: 0.7px;
      background-color: #d4d7dc;
      margin: 0 -8px 0 -8px;
    }
  }

  .plant-item-header {
    padding-bottom: 8px;
    translate: all 1s;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .plant-item-header-left {
      display: inline-block;

      .plant-item-header-serial {
        width: 19px;
        height: 19px;
        background: #7fbcff;
        margin-top: 6px;
        border-radius: 11px;
        color: #fff;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-right: 4px;
      }

      .form-item-plant {
        display: inline-flex;
        margin-right: 32px;
        flc-text-truncated {
          font-size: 16px;
          font-weight: 500;
          color: #36393e;
          display: inline;
          align-self: center;
        }
      }

      .plant-select-width ::ng-deep {
        nz-select,
        nz-input-number-group {
          max-width: 300px;
          min-width: 160px;
        }
      }
    }

    .plant-item-header-right {
      display: inline-flex;
      align-items: center;
      float: right;

      .status-area {
        display: flex;
        align-items: center;
      }

      .icon-delete {
        color: #54607c;

        &:hover {
          color: #f74949;
          cursor: pointer;
        }
      }

      span {
        margin-right: 4px;
        color: $highlightColor;
      }

      .icon-toggle {
        color: $highlightIconColor;
        font-size: 12px;
      }

      .icon-xinjian1 {
        margin-right: 4px;
        font-size: 12px;
        color: $highlightColor;
      }

      .surplus {
        display: flex;
        align-items: center;
        margin-right: 0;

        &::after {
          content: '';
          margin: 0 12px;
          display: inline-block;
          height: 12px;
          width: 1px;
          background-color: #d4d7dc;
        }

        &:hover span {
          cursor: pointer;
          color: $highlightHoverColor;
        }
      }

      &::ng-deep .ant-divider-vertical {
        border-left: 1px solid #d3d3d3;
        margin: 0 13px;
        height: 12px;
      }

      .toggle,
      .xinjian-box {
        &:hover {
          cursor: pointer;

          span {
            color: $highlightHoverColor;
          }

          i {
            color: $highlightHoverColor;
          }
        }

        i,
        span {
          color: $highlightColor;
        }
      }
      .toggle {
        i,
        span {
          color: #54607c;
        }
      }
      .disabled-element {
        color: #b5b8bf !important;

        &:hover {
          cursor: no-drop;
          opacity: 0.8;

          span {
            cursor: no-drop !important;
            text-decoration: none !important;
            color: #b5b8bf !important;
          }

          .icon-xinjian1 {
            color: #b5b8bf;
          }
        }

        .icon-xinjian1 {
          color: #b5b8bf;
        }

        span {
          color: #b5b8bf;
        }
      }
    }
  }

  .reset-padding {
    padding-bottom: 0;
  }

  .toggle-zhankai {
    margin-top: 8px;
    max-height: 666px;
    transition: all 0.4s ease-in-out;
    overflow: hidden;
  }

  .toggle-shouqi {
    max-height: 0;
    margin-top: 0;
    transition: all 0.4s;
  }

  // 收货地址
  .personal-info {
    background-color: #fff;
    padding: 0 8px 8px 8px;
    display: flex;

    & > div {
      display: flex;
      flex-wrap: nowrap;
      height: 32px;
      align-items: center;
      min-width: 250px;
      margin-right: 24px;

      & > span {
        flex-shrink: 0;
        color: #54607c;
      }
    }
  }
}
