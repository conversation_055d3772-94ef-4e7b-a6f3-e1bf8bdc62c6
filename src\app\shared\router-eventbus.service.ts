import { Injectable } from '@angular/core';
import { Observable, Subject, Subscription } from 'rxjs';
import { filter, map } from 'rxjs/operators';

export interface EventData {
  name: string;
  type: string;
  value: any;
}

/**
 * @deprecated Replaced with FlcRouterEventBusService.
 */
@Injectable({
  providedIn: 'root',
})
export class RouterEventBusService {
  private subject$ = new Subject();

  constructor() {}

  // emit(event: EventData) {
  //   this.subject$.next(event);
  // }

  // on(eventName: string, action: any): Subscription {
  //   return this.subject$.pipe(filter((e: any) => e.name === eventName)).subscribe(action);
  // }

  emitRouterEvent(event: EventData) {
    this.subject$.next(event);
  }

  // 注册 ActivationEnd 通知，用于路由复用
  onRouterActivationEnd(pathname: string, action: any): Subscription {
    return this.subject$.pipe(filter((e: any) => e.name === pathname && e.type === 'ActivationEnd')).subscribe(action);
  }

  // 注册 NavigationEnd 通知
  onRouterNavigationEnd(action: any): Subscription {
    return this.subject$.pipe(filter((e: any) => e.type === 'NavigationEnd')).subscribe(action);
  }

  // 注册 NavigationCancel 通知
  onRouterNavigationCanceled(action: any): Subscription {
    return this.subject$.pipe(filter((e: any) => e.type === 'NavigationCanceled')).subscribe(action);
  }
}
