import { NzMessageService } from 'ng-zorro-antd/message';
import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnInit,
  ChangeDetectorRef,
  ViewChild,
  OnChanges,
  SimpleChanges,
  ElementRef,
  OnDestroy,
  AfterViewInit,
} from '@angular/core';
import { addDays, endOfDay, format, isAfter, startOfDay, subDays } from 'date-fns';
import { cloneDeep, isNumber, pickBy } from 'lodash';
import { combineLatest, finalize, lastValueFrom, Observable, Subscription } from 'rxjs';
import { DayRange, WeekRange, GanttCellWidth } from 'src/app/components/graph-table/graph-table';
import { GraphTableComponent } from 'src/app/components/graph-table/graph-table.component';
import { FactoryItem, PlanItem, SchedulePlan } from '../production-plan';
import { ProductionPlanService } from '../production-plan.service';
import { resizable } from 'fl-common-lib';
import { TranslateService } from '@ngx-translate/core';

const isNotEmpty = (input: any) => {
  if (Array.isArray(input)) {
    return input.length > 0;
  }
  return input !== undefined && input !== null && input !== '';
};

@Component({
  selector: 'app-plan-gantt',
  templateUrl: './plan-gantt.component.html',
  styleUrls: ['./plan-gantt.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
@resizable()
export class PlanGanttComponent implements OnInit, OnChanges, OnDestroy, AfterViewInit {
  @ViewChild('detailBar') detailBar!: ElementRef;
  @ViewChild(GraphTableComponent) productionGraphTable!: GraphTableComponent;
  @Input() dates!: {
    start_date: Date;
    end_date: Date;
  };
  @Input() graphOptions: { item_dimension: 'io' | 'po' } = {
    item_dimension: 'io',
  };
  data: any;
  days: DayRange[] = []; // 用于显示有多少个天（表头）
  weeks: WeekRange[] = []; // 用于显示多少个周
  options = {
    signalWidth: GanttCellWidth.day,
    dayWidth: GanttCellWidth.day, // 日维度一天48，小时维度一天24*12
    wrapBodyWidth: 0,
    dimension: 'day',
    per1Px: 1,
  };
  factoryList: FactoryItem[] = [];
  selectedItemList!: PlanItem | null;
  private _hoverItem!: PlanItem | null;
  get detailBarItem(): PlanItem | null {
    if (this.selectedItemList) {
      return this.selectedItemList ?? null;
    } else {
      return this._hoverItem ?? null;
    }
  }
  listObservable!: Observable<any>;
  listSubscription!: Subscription;
  itemObservable!: Observable<any>;
  itemSubscription!: Subscription;
  isLoading = false;
  maxWidth!: number;
  exitClickable = false;

  constructor(
    private _service: ProductionPlanService,
    private _cdr: ChangeDetectorRef,
    private _msg: NzMessageService,
    private _translateService: TranslateService
  ) {}

  ngOnInit(): void {
    this.getGanttData();
    (this as any).addResizePageListener();
  }

  ngAfterViewInit(): void {
    this.resizePage();
  }

  ngOnDestroy(): void {
    (this as any).removeResizePageListener();
    this.destroyList();
    this.destroyItem();
  }

  destroyList(): void {
    this.listObservable = null as any;
    this.listSubscription?.unsubscribe();
  }

  destroyItem(): void {
    this.itemObservable = null as any;
    this.itemSubscription?.unsubscribe();
  }

  resizePage(): void {
    setTimeout(() => {
      const detailBox = this.detailBar?.nativeElement?.offsetWidth ?? 1048;
      this.maxWidth = (detailBox - 16 - 40) / 3;
      this._cdr.detectChanges();
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes && changes?.graphOptions?.currentValue) {
      this.selectedItemList = null;
      this.exitClickable = false;
      this.factoryList?.forEach((f) => {
        f.order_plan_items?.forEach((o) => (o.is_selected = false));
        f.po_plan_items?.forEach((p) => (p.is_selected = false));
      });
      this._cdr.detectChanges();
    }
  }

  getGanttData(recoverScroll = false): Promise<any> {
    this.destroyList();
    this.isLoading = true;
    const payload = {
      start_date: Number(format(startOfDay(new Date(this._service.searchData.start_date)), 'T')),
      end_date: Number(format(endOfDay(new Date(this._service.searchData.end_date)), 'T')),
    };
    this.dates = {
      start_date: new Date(this._service.searchData.start_date),
      end_date: new Date(this._service.searchData.end_date),
    };
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const self = this;
    return new Promise(function (resolve) {
      self.listObservable = self._service.getScheduleList(payload);
      const colorObservable = self._service.getSettingColor({ set_status: 1 });
      self.listSubscription = combineLatest(
        [lastValueFrom(colorObservable), lastValueFrom(self.listObservable)],
        (colorObserve, listObserve) => {
          if (colorObserve?.code === 200 && listObserve?.code === 200) {
            const graph_color_data = colorObserve?.data?.datalist ?? [];
            const datalist = listObserve?.data?.allocation_list ?? [];
            self.selectedItemList = null;
            self.factoryList = [];
            datalist?.forEach((line: SchedulePlan) => {
              self.factoryList?.push(new FactoryItem(line, graph_color_data));
            });
            self.factoryList = [...self.factoryList];
            self._cdr.detectChanges();
            console.log('this.factoryList', self.factoryList);
          }
          resolve(listObserve);
        }
      )
        ?.pipe(
          finalize(() => {
            self.isLoading = false;
            self._cdr.detectChanges();
          })
        )
        ?.subscribe((res) => {});
    });
  }

  heightLightList(recoverScroll = true) {
    this.isLoading = true;
    const search: any = { ...this._service.searchData };
    delete search.date, delete search.start_date, delete search.end_date;
    // 过滤出有值的搜索项
    const heightLightParams = pickBy(search, isNotEmpty);
    const heightLightParamsList = Object.entries(heightLightParams);
    const shadowList = [...this.factoryList].sort((a, b) => b.factory_id - a.factory_id);
    shadowList.forEach((line) => {
      line.isHighLight = false;
      line.order_plan_items.forEach((order) => (order.isHighLight = false));
      line.po_plan_items.forEach((order) => (order.isHighLight = false));
    });
    if (heightLightParamsList?.length) {
      this._cdr.detach();
      let matchList = cloneDeep(shadowList);
      heightLightParamsList.forEach((item) => {
        if (item[0] === 'factory_id') {
          matchList = matchList.filter((pro) => pro.factory_id === item[1]);
        } else {
          matchList = matchList.filter((pro) => {
            pro.order_plan_items = pro.order_plan_items.filter((line: any) => line[item[0]] === item[1]);
            pro.po_plan_items = pro.po_plan_items.filter((line: any) => line[item[0]] === item[1]);
            return pro?.order_plan_items?.length > 0 || pro?.po_plan_items?.length > 0;
          });
        }
      });
      matchList.forEach((item: any) => {
        const lineIndex = shadowList.findIndex((shadow) => shadow.factory_id === item.factory_id);
        const shadow = shadowList.splice(lineIndex, 1)[0];
        shadow.isHighLight = true;
        item.order_plan_items.forEach((resOrder: any) => {
          shadow.order_plan_items.forEach((shadowOrder) => {
            if (shadowOrder.order_code === resOrder.order_code) {
              shadowOrder.isHighLight = true;
            }
          });
        });
        item.po_plan_items.forEach((resOrder: any) => {
          shadow.po_plan_items.forEach((shadowOrder) => {
            if (shadowOrder.po_code === resOrder.po_code) {
              shadowOrder.isHighLight = true;
            }
          });
        });
        shadowList.push(shadow);
      });
      this.factoryList = shadowList.reverse();
      if (matchList?.length === 0) {
        this._msg.error(this._translateService.instant('plan.productionPlan.暂无搜索结果'));
        this.productionGraphTable?.toToday();
      } else {
        let firstData: any;
        if (this.graphOptions.item_dimension === 'io') {
          firstData = this.factoryList?.[0]?.order_plan_items?.find((item) => item.isHighLight);
        } else {
          firstData = this.factoryList?.[0]?.po_plan_items?.find((item) => item.isHighLight);
        }
        this.productionGraphTable?.toScrollDate(firstData?.start_time ? new Date(firstData?.start_time) : new Date());
      }
      this.isLoading = false;
    } else {
      this.factoryList = shadowList.reverse();
      this.isLoading = false;
      this.productionGraphTable?.toToday();
    }
    this._cdr.markForCheck();
    this._cdr.detectChanges();
  }

  daysChange(e: DayRange[]) {
    this.days = [...e];
    this._cdr.detectChanges();
  }

  weeksChange(e: WeekRange[]) {
    this.weeks = [...e];
    this._cdr.detectChanges();
  }

  optionsChange(e: any) {
    this.options = { ...e };
    this._cdr.detectChanges();
  }

  /**
   * 相同交付单的allocated_qty，output_qty是一致的
   * @param data
   */
  tapGraphItem(data: any, i: number) {
    this.factoryList?.forEach((l: FactoryItem, index: number) => {
      if (l.exitSelected && i !== index) {
        l.po_plan_items?.forEach((po) => po.setSelected(false));
        l.order_plan_items?.forEach((order) => order.setSelected(false));
        l.exitSelected = false;
      }
    });
    this.selectedItemList = null;
    // 调用接口获取整合计划起止时间
    if (data?.is_selected) {
      this.exitClickable = true;
      this.destroyItem();
      this.itemObservable = this._service?.getLineDetail({ order_codes: [data?.order_code] });
      this.itemSubscription = this.itemObservable?.pipe(finalize(() => this._cdr.detectChanges()))?.subscribe((res) => {
        if (res?.code === 200) {
          const factoryData = res?.data?.factory_bulkOrder?.find((f: any) => f?.factory_id === this.factoryList[i]?.factory_id);
          const matchData = factoryData?.bulk_orders?.find((item: any) => item?.order_code === data?.order_code);
          data.allocated_qty = 0;
          data.scheduled_qty = 0;
          data.output_qty = 0;
          const po_uuid_lists: string[] = [];
          if (matchData) {
            if (this.graphOptions?.item_dimension === 'io') {
              data.plan_dates = this.mergeTime(matchData?.po_plan ?? []);

              (matchData?.po_plan ?? [])?.forEach((item: any) => {
                if (!po_uuid_lists?.includes(item?.po_uuid)) {
                  po_uuid_lists?.push(item?.po_uuid);
                  data.allocated_qty = data.allocated_qty + (item?.allocated_qty ?? 0);
                  data.output_qty = data.output_qty + (item?.output_qty ?? 0);
                }

                data.scheduled_qty = data.scheduled_qty + (item?.scheduled_qty ?? 0);
              });
            } else {
              const po_lists = (matchData?.po_plan ?? [])?.filter((po: any) => po?.po_code === data?.po_code);
              data.plan_dates = this.mergeTime(po_lists ?? []);
              (po_lists ?? [])?.forEach((item: any) => {
                data.allocated_qty = item?.allocated_qty ?? 0;
                data.output_qty = item?.output_qty ?? 0;
                data.scheduled_qty = data.scheduled_qty + (item?.scheduled_qty ?? 0);
              });
            }
          }
          this.selectedItemList = { ...data };
        }
      });
    } else {
      this.exitClickable = false;
    }
    this._cdr.detectChanges();
  }

  /**
   * 相同交付单的allocated_qty，output_qty是一致的
   * @param data
   */
  hoverGraphItem(data: any, i: number) {
    this.selectedItemList = null;
    this.destroyItem();
    // 调用接口获取整合计划起止时间
    this.itemObservable = this._service?.getLineDetail({ order_codes: [data?.order_code] });
    this.itemSubscription = this.itemObservable?.pipe(finalize(() => this._cdr.detectChanges()))?.subscribe((res) => {
      if (res?.code === 200) {
        const factoryData = res?.data?.factory_bulkOrder?.find((f: any) => f?.factory_id === this.factoryList[i]?.factory_id);
        const matchData = factoryData?.bulk_orders?.find((item: any) => item?.order_code === data?.order_code);
        data.allocated_qty = 0;
        data.scheduled_qty = 0;
        data.output_qty = 0;
        const po_uuid_lists: string[] = [];
        if (matchData) {
          if (this.graphOptions?.item_dimension === 'io') {
            data.plan_dates = this.mergeTime(matchData?.po_plan ?? []);

            (matchData?.po_plan ?? [])?.forEach((item: any) => {
              if (!po_uuid_lists?.includes(item?.po_uuid)) {
                po_uuid_lists?.push(item?.po_uuid);
                data.allocated_qty = data.allocated_qty + (item?.allocated_qty ?? 0);
                data.output_qty = data.output_qty + (item?.output_qty ?? 0);
              }

              data.scheduled_qty = data.scheduled_qty + (item?.scheduled_qty ?? 0);
            });
          } else {
            const po_lists = (matchData?.po_plan ?? [])?.filter((po: any) => po?.po_code === data?.po_code);
            data.plan_dates = this.mergeTime(po_lists ?? []);
            (po_lists ?? [])?.forEach((item: any) => {
              data.allocated_qty = item?.allocated_qty ?? 0;
              data.output_qty = item?.output_qty ?? 0;
              data.scheduled_qty = data.scheduled_qty + (item?.scheduled_qty ?? 0);
            });
          }
        }
        this.selectedItemList = { ...data };
      }
    });
    this._cdr.detectChanges();
  }

  leaveGraphItem() {
    this.selectedItemList = null;
    this._cdr.detectChanges();
  }

  mergeTime(plan_times: []): any[] {
    /** 整合订单进度条，将能首尾相连的进度条进行整合 */
    const merged_order_item: ({ start_time: any; end_time: any } | undefined)[] = [];
    let startTime: any;
    let endTime: any;
    plan_times?.forEach((order: any, index: number) => {
      if (index === 0) {
        startTime = new Date(order.start_time);
        endTime = new Date(order.end_time);
      } else {
        if (isAfter(new Date(order?.start_time), new Date(endTime))) {
          merged_order_item.push({
            start_time: startTime,
            end_time: endTime,
          });
          startTime = new Date(order.start_time);
          endTime = new Date(order.end_time);
        } else if (isAfter(new Date(order?.end_time), new Date(endTime))) {
          endTime = new Date(order.end_time);
        }
      }
    });
    if (plan_times?.length) {
      merged_order_item.push({
        start_time: startTime,
        end_time: endTime,
      });
    }
    return merged_order_item;
  }

  /**
   * 向前10天
   */
  forward() {
    this._service.searchData.start_date = subDays(new Date(this._service.searchData.start_date), 10);
    this.getGanttData().then(() => {
      this.heightLightList();
    });
  }

  /**
   * 向后10天
   */
  backwards() {
    this._service.searchData.end_date = addDays(new Date(this._service.searchData.end_date), 10);
    this.getGanttData().then(() => {
      this.heightLightList();
    });
    setTimeout(() => {
      const searchData = this._service.searchData;
      const showEnd = searchData.end_date ?? new Date();
      this.productionGraphTable?.toScrollDate(showEnd);
    }, 10);
  }

  getNewColor(data: { is_refresh_list: boolean }) {
    if (data?.is_refresh_list) {
      this.isLoading = true;
      this._service
        .getSettingColor({ set_status: 1 })
        ?.pipe(
          finalize(() => {
            this.isLoading = false;
            this._cdr.detectChanges();
          })
        )
        ?.subscribe((res) => {
          if (res?.code === 200) {
            const graph_color_data = res?.data?.datalist ?? [];
            this.factoryList?.forEach((f) => {
              f?.order_plan_items?.forEach((order) => {
                order.backgroundColor = this.getColor(order, graph_color_data) ?? order?.originBackgroundColor;
              });
              f.po_plan_items?.forEach((po) => {
                po.backgroundColor = this.getColor(po, graph_color_data) ?? po?.originBackgroundColor;
              });
            });
            this.factoryList = [...this.factoryList];
          }
        });
    }
  }

  getColor(order: any, graph_color_data: any) {
    const colorData = graph_color_data?.find(
      (item: any) => item?.first_style_id === order?.first_style_id && item?.second_style_id === order?.second_style_id
    );
    if (colorData && isNumber(colorData?.simple)) {
      const sam = order?.order_sam ?? 0;
      if (colorData?.simple && sam <= colorData?.simple) {
        return colorData?.simple_color;
      } else if (sam > colorData?.simple && sam <= colorData?.general) {
        return colorData?.general_color;
      } else if (sam > colorData?.general && sam <= colorData?.difficult) {
        return colorData?.difficult_color;
      } else if (sam > colorData?.extremely_difficult) {
        return colorData?.extremely_difficult_color;
      }
    }
    return null;
  }
}
