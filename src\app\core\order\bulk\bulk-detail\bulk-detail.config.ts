import { factoryCodes } from 'src/app/shared/common-config';

const userInfo: string | null = localStorage.getItem('userInfo');
const user = userInfo ? JSON.parse(userInfo) : {};

/**
 * 基本信息字段配置
 */
export function initBasicList() {
  const Standard = [
    {
      label: '款式编码',
      code: 'style_code',
      type: 'select',
      maxLength: 20,
      required: true,
      visible: true,
      autoCom: true,
      colSpan: 8,
    },
    { label: '订单需求号', code: 'io_code', type: 'textarea', maxLength: 30, required: true, visible: true, colSpan: 8 },
    {
      label: '客户名称',
      code: 'customer',
      labelKey: 'customer',
      type: 'select',
      maxLength: 100,
      required: true,
      visible: true,
      colSpan: 8,
    },

    { label: '品名', code: 'category', type: 'textarea', maxLength: 50, required: false, visible: true, colSpan: 8 },
    { label: '款式分类', code: 'style', type: 'cascader', maxLength: 16, required: true, visible: true, colSpan: 8 },
    { label: '下单日期', code: 'order_date', type: 'date', maxLength: 16, required: true, visible: true, colSpan: 8 },
    { label: '品牌', code: 'brand_id', type: 'select', maxLength: 16, required: true, visible: true, colSpan: 8 },
    { label: '批次', code: 'order_category', type: 'select', maxLength: 16, required: true, visible: true, colSpan: 8 }, // 原 订单类型
    { label: '生产类型', code: 'order_production_type', type: 'select', maxLength: 16, required: true, visible: true, colSpan: 8 },
    { label: '订单类型', code: 'order_classification', type: 'select', maxLength: 16, required: false, visible: true, colSpan: 8 },
    { label: '二次工艺', code: 'extra_process_info', type: 'multipleSelect', maxLength: 16, required: false, visible: true, colSpan: 8 },
    { label: '销售单号', code: 'contract_number', type: 'textarea', maxLength: 16, required: false, visible: true, colSpan: 8 },
    { label: '品质', code: 'quality_level', type: 'select', maxLength: 16, required: false, visible: true, colSpan: 8 },
    {
      label: '产品类型',
      code: 'production_category_id',
      type: 'dynamic-select',
      maxLength: 16,
      required: false,
      visible: true,
      colSpan: 8,
    },
    // { label: '付款条件', code: 'payment_condition', type: 'select', maxLength: 16, required: false, visible: true, colSpan: 8 },
    { label: '付款方式', code: 'payment_id', type: 'select', maxLength: 16, required: false, visible: true, colSpan: 8 },
    { label: '币种', code: 'currency_id', type: 'select', maxLength: 16, required: true, visible: true, colSpan: 8 },
    {
      label: '预收比例',
      code: 'upfront_payment_rate',
      type: 'number-input',
      precision: 2,
      min: 0,
      required: false,
      visible: true,
      colSpan: 8,
      suffix: '%',
    },
    {
      label: '预收款',
      code: 'upfront_payment_amount',
      type: 'number-input',
      precision: 2,
      min: 0,
      required: false,
      visible: true,
      colSpan: 8,
    },
    { label: '是否快反', code: 'fast_reply', type: 'select_radio', maxLength: 16, required: false, visible: false, colSpan: 8 },
    {
      label: '是否急单',
      code: 'urgent_status',
      type: 'select_radio',
      trueValue: 20,
      falseValue: 10,
      maxLength: 16,
      required: false,
      visible: false,
      colSpan: 8,
    },
    {
      label: '是否需要第三方质检',
      code: 'third_quality_check',
      type: 'select_radio',
      maxLength: 16,
      required: false,
      visible: true,
      colSpan: 8,
    },
    { label: '是否需要报关', code: 'customs_clearance', type: 'select_radio', maxLength: 16, required: false, visible: false, colSpan: 8 },
    { label: '外发加工厂', code: 'process_factory_code', type: 'select', required: false, visible: false, colSpan: 8 },
    { label: '跟单员', code: 'merchandiser_user_id', type: 'select', required: false, visible: true, colSpan: 8 },
    { label: '预计物料齐套日期', code: 'pre_material_completed_time', type: 'date', required: false, visible: false, colSpan: 8 },
    { label: '业务员', code: 'biz_user_emp_id', type: 'treeSelect', required: false, visible: false, colSpan: 8 },
    { label: '是否预排单', code: 'is_pre_order', type: 'select_radio', required: true, visible: true, colSpan: 8 },
    {
      label: '备注',
      code: 'remark',
      type: 'textarea',
      tipName: 'bulk-remark-tip',
      maxLength: 500,
      required: false,
      visible: true,
      colSpan: 24,
    },

    // { label: '销售渠道', code: 'sale_channel_id', type: 'select', required: false, visible: true },
    { label: '是否使用生产计划', code: 'is_use_plan', type: 'select', required: true, visible: true, colSpan: 8 },
    { label: '商店', code: 'store', type: 'input', maxLength: 30, required: false, visible: true, colSpan: 8 },
    { label: '公司编号', code: 'company_code', type: 'input', maxLength: 30, required: false, visible: true, colSpan: 8 },
    { label: '成分', code: 'composition', type: 'input', maxLength: 30, required: false, visible: false, colSpan: 8 },
    { label: '部门', code: 'department', type: 'input', maxLength: 30, required: false, visible: false, colSpan: 8 },
    { label: '合同价', code: 'contract_price', type: 'number-input', required: false, visible: false, colSpan: 8 },
    { label: '面料', code: 'fabric', type: 'textarea', maxLength: 255, required: false, visible: false, colSpan: 24 },
    { label: '配色', code: 'color', type: 'textarea', maxLength: 255, required: false, visible: false, colSpan: 24 },
    { label: '样衣要求', code: 'sample_requirement', type: 'textarea', maxLength: 255, required: false, visible: false, colSpan: 24 },
    { label: '印绣花', code: 'printing_embroidery', type: 'textarea', maxLength: 255, required: false, visible: false, colSpan: 24 },
    { label: '车缝辅料', code: 'sew_accessory', type: 'textarea', maxLength: 255, required: false, visible: false, colSpan: 24 },
    { label: '后整辅料', code: 'consolidation_accessory', type: 'textarea', maxLength: 255, required: false, visible: false, colSpan: 24 },
    { label: '辅料备注', code: 'accessory_remark', type: 'textarea', maxLength: 255, required: false, visible: false, colSpan: 24 },
    { label: '特殊要求', code: 'special_requirement', type: 'textarea', maxLength: 255, required: false, visible: false, colSpan: 24 },
    { label: '订单标签', code: 'order_labels', type: 'select', required: false, visible: false, colSpan: 24 },
    {
      label: '附件',
      code: 'appendix_requirements',
      type: 'upload',
      maxLength: 255,
      required: false,
      visible: false,
      disabledSort: true,
      colSpan: 12,
    },
    {
      label: '印绣花图片',
      code: 'pictures',
      type: 'upload',
      maxLength: 255,
      required: false,
      visible: false,
      disabledSort: true,
      colSpan: 12,
    },
    {
      label: '所属办事处',
      code: 'pro_office',
      type: 'select',
      required: false,
      visible: false,
      colSpan: 12,
      labelKey: 'pro_office',
    },
    {
      label: '品类',
      code: 'category_type',
      type: 'select',
      required: false,
      visible: false,
      colSpan: 12,
      labelKey: 'category_type',
    },
    {
      label: '品类名称',
      code: 'cat_name',
      type: 'textarea',
      maxLength: 100,
      required: false,
      visible: false,
      colSpan: 12,
    },
    {
      label: '品类编码',
      code: 'cat_no',
      type: 'textarea',
      maxLength: 100,
      required: false,
      visible: false,
      colSpan: 12,
    },
    {
      label: '生产周期',
      code: 'lead_time',
      type: 'select',
      required: false,
      visible: false,
      colSpan: 12,
      labelKey: 'lead_time',
    },
    {
      label: '季节',
      code: 'season',
      type: 'select',
      required: false,
      visible: false,
      colSpan: 12,
      labelKey: 'season',
    },
    {
      label: '生产国',
      code: 'origin_country',
      type: 'select',
      required: false,
      visible: false,
      colSpan: 12,
      labelKey: 'origin_country',
    },
    // 用于在配置中显示
    {
      label: '款式图',
      code: 'order_pictures',
      type: 'upload',
      required: true,
      visible: true,
      disabledSort: true,
      canSwitch: true,
    },
  ];

  const CTC = [
    { label: '订单需求号', code: 'io_code', type: 'textarea', maxLength: 100, required: true, visible: true, colSpan: 8 },
    { label: '业务员', code: 'biz_user_emp_id', type: 'treeSelect', required: false, visible: false, colSpan: 8 },
    {
      label: '客户名称',
      code: 'customer',
      labelKey: 'customer',
      type: 'select',
      maxLength: 100,
      required: false,
      visible: true,
      colSpan: 8,
    },
    { label: '品牌', code: 'brand_id', type: 'select', maxLength: 16, required: false, visible: true, colSpan: 8 },
    {
      label: '所属办事处',
      code: 'pro_office',
      type: 'select',
      required: false,
      visible: true,
      colSpan: 8,
      labelKey: 'pro_office',
    },
    {
      label: '品类',
      code: 'category_type',
      type: 'select',
      required: false,
      visible: true,
      colSpan: 8,
      labelKey: 'category_type',
    },
    {
      label: '品类名称',
      code: 'cat_name',
      type: 'textarea',
      maxLength: 100,
      required: false,
      visible: true,
      colSpan: 8,
    },
    { label: '品类编码', code: 'cat_no', type: 'textarea', maxLength: 100, required: false, visible: true, colSpan: 8 },
    {
      label: '生产周期',
      code: 'lead_time',
      type: 'select',
      required: false,
      visible: true,
      colSpan: 8,
      labelKey: 'lead_time',
    },
    {
      label: '季节',
      code: 'season',
      type: 'select',
      required: false,
      visible: true,
      colSpan: 8,
      labelKey: 'season',
    },
    { label: '款式编码', code: 'style_code', type: 'select', maxLength: 20, required: true, visible: true, autoCom: true, colSpan: 8 },
    { label: '品名', code: 'category', type: 'textarea', maxLength: 100, required: false, visible: true, colSpan: 8 },
    {
      label: '生产国',
      code: 'origin_country',
      type: 'select',
      required: false,
      visible: true,
      colSpan: 8,
      labelKey: 'origin_country',
    },
    { label: '跟单员', code: 'merchandiser_user_id', type: 'select', required: false, visible: true, colSpan: 8 },
    { label: '外发加工厂', code: 'process_factory_code', type: 'select', required: false, visible: false, colSpan: 8 },
    { label: '下单日期', code: 'order_date', type: 'date', maxLength: 16, required: true, visible: true, colSpan: 8, canSwitch: true },
    {
      label: '备注',
      code: 'remark',
      type: 'textarea',
      tipName: 'bulk-remark-tip',
      maxLength: 500,
      required: false,
      visible: true,
      colSpan: 16,
    },

    { label: '款式分类', code: 'style', type: 'cascader', maxLength: 16, required: true, visible: false, colSpan: 8 },
    { label: '批次', code: 'order_category', type: 'select', maxLength: 16, required: true, visible: false, colSpan: 8, canSwitch: true }, // 原 订单类型
    {
      label: '生产类型',
      code: 'order_production_type',
      type: 'select',
      maxLength: 16,
      required: true,
      visible: false,
      colSpan: 8,
      canSwitch: true,
    },
    { label: '订单类型', code: 'order_classification', type: 'select', maxLength: 16, required: false, visible: false, colSpan: 8 },
    { label: '二次工艺', code: 'extra_process_info', type: 'multipleSelect', maxLength: 16, required: false, visible: false, colSpan: 8 },
    { label: '销售单号', code: 'contract_number', type: 'textarea', maxLength: 100, required: false, visible: false, colSpan: 8 },
    { label: '品质', code: 'quality_level', type: 'select', maxLength: 16, required: false, visible: false, colSpan: 8 },
    {
      label: '产品类型',
      code: 'production_category_id',
      type: 'dynamic-select',
      maxLength: 16,
      required: false,
      visible: false,
      colSpan: 8,
    },
    // { label: '付款条件', code: 'payment_condition', type: 'select', maxLength: 16, required: false, visible: false, colSpan: 8 },
    { label: '付款方式', code: 'payment_id', type: 'select', maxLength: 16, required: false, visible: true, colSpan: 8 },
    { label: '币种', code: 'currency_id', type: 'select', maxLength: 16, required: true, visible: true, colSpan: 8 },
    {
      label: '预缩比例',
      code: 'upfront_payment_rate',
      type: 'number-input',
      precision: 2,
      min: 0,
      required: false,
      visible: true,
      colSpan: 8,
      suffix: '%',
    },
    {
      label: '预收款',
      code: 'upfront_payment_amount',
      type: 'number-input',
      precision: 2,
      min: 0,
      required: false,
      visible: true,
      colSpan: 8,
    },
    { label: '是否快反', code: 'fast_reply', type: 'select_radio', maxLength: 16, required: false, visible: false, colSpan: 8 },
    {
      label: '是否需要第三方质检',
      code: 'third_quality_check',
      type: 'select_radio',
      maxLength: 16,
      required: false,
      visible: false,
      colSpan: 8,
    },
    { label: '是否需要报关', code: 'customs_clearance', type: 'select_radio', maxLength: 16, required: false, visible: false, colSpan: 8 },
    {
      label: '预计物料齐套日期',
      code: 'pre_material_completed_time',
      type: 'date',
      required: false,
      visible: false,
      colSpan: 8,
      flexWidth: '250px',
    },
    { label: '是否预排单', code: 'is_pre_order', type: 'select_radio', required: true, visible: false, colSpan: 8, canSwitch: true },
    // { label: '销售渠道', code: 'sale_channel_id', type: 'select', required: false, visible: false },
    { label: '是否使用生产计划', code: 'is_use_plan', type: 'select', required: true, visible: false, colSpan: 8, canSwitch: true },
    { label: '商店', code: 'store', type: 'input', maxLength: 100, required: false, visible: false, colSpan: 8 },
    { label: '公司编号', code: 'company_code', type: 'input', maxLength: 100, required: false, visible: false, colSpan: 8 },
    { label: '成分', code: 'composition', type: 'input', maxLength: 100, required: false, visible: false, colSpan: 8 },
    { label: '归属部门', code: 'gen_dept_id', type: 'select', maxLength: 30, required: false, visible: false, colSpan: 8 },
    { label: '合同价', code: 'contract_price', type: 'number-input', required: false, visible: false, colSpan: 8 },
    { label: '面料', code: 'fabric', type: 'textarea', maxLength: 255, required: false, visible: false, colSpan: 24 },
    { label: '配色', code: 'color', type: 'textarea', maxLength: 255, required: false, visible: false, colSpan: 24 },
    { label: '样衣要求', code: 'sample_requirement', type: 'textarea', maxLength: 255, required: false, visible: false, colSpan: 24 },
    { label: '印绣花', code: 'printing_embroidery', type: 'textarea', maxLength: 255, required: false, visible: false, colSpan: 24 },
    { label: '车缝辅料', code: 'sew_accessory', type: 'textarea', maxLength: 255, required: false, visible: false, colSpan: 24 },
    {
      label: '后整辅料',
      code: 'consolidation_accessory',
      type: 'textarea',
      maxLength: 255,
      required: false,
      visible: false,
      colSpan: 24,
      flexWidth: '190px',
    },
    { label: '辅料备注', code: 'accessory_remark', type: 'textarea', maxLength: 255, required: false, visible: false, colSpan: 24 },
    { label: '特殊要求', code: 'special_requirement', type: 'textarea', maxLength: 255, required: false, visible: false, colSpan: 24 },
    {
      label: '附件',
      code: 'appendix_requirements',
      type: 'upload',
      maxLength: 255,
      required: false,
      visible: false,
      disabledSort: true,
      colSpan: 12,
    },
    {
      label: '印绣花图片',
      code: 'pictures',
      type: 'upload',
      maxLength: 255,
      required: false,
      visible: false,
      disabledSort: true,
      colSpan: 12,
      flexWidth: '255px',
    },
    // 用于在配置中显示
    {
      label: '款式图',
      code: 'order_pictures',
      type: 'upload',
      required: true,
      visible: false,
      disabledSort: true,
      canSwitch: true,
    },
  ];

  return factoryCodes.includes(user?.factory_code) ? CTC : Standard;
}

/**
 * 交付单字段配置
 */
export function initDFWOConfigList() {
  const Standard = [
    {
      label: '尺码组',
      code: 'spec_group_id',
      required: true,
      visible: true,
    },
    {
      label: '交付日期',
      code: 'due_time',
      required: true,
      visible: true,
    },
    {
      label: '客户交期',
      code: 'customer_due_time',
      required: false,
      visible: true,
    },
    {
      label: '客单号',
      code: 'cust_po',
      required: false,
      visible: false,
      type: 'textarea',
      maxLength: 100,
    },
    {
      label: '单价',
      code: 'fob_price',
      required: false,
      visible: false,
      suffix: factoryCodes.includes(user?.factory_code) ? '($)' : null,
      type: 'input-number',
      precision: 2,
      min: 0,
    },
    {
      label: '总金额',
      code: 'total_fob',
      required: false,
      visible: false,
      suffix: factoryCodes.includes(user?.factory_code) ? '($)' : null,
      // suffix: '($)',
      type: 'input-number',
      precision: 2,
      min: 0,
    },
    {
      label: '总销售金额',
      code: 'total_sales_cad',
      required: false,
      visible: false,
      suffix: factoryCodes.includes(user?.factory_code) ? '($)' : null,
      // suffix: '($)',
      type: 'input-number',
      precision: 2,
      min: 0,
    },
    {
      label: '出货港',
      code: 'shipment_port',
      required: false,
      visible: false,
      type: 'select',
    },
    {
      label: '收货人',
      code: 'receiver',
      required: false,
      visible: true,
    },
    {
      label: '联系方式',
      code: 'contact',
      required: false,
      visible: true,
    },
    {
      label: '收货地址',
      code: 'address',
      required: false,
      visible: true,
    },
  ];
  const CTC = [
    {
      label: '尺码组',
      code: 'spec_group_id',
      required: true,
      visible: true,
    },
    {
      label: '交付日期',
      code: 'due_time',
      required: true,
      visible: true,
    },
    {
      label: '客户交期',
      code: 'customer_due_time',
      required: false,
      visible: true,
    },
    {
      label: '客单号',
      code: 'cust_po',
      required: false,
      visible: true,
      type: 'textarea',
      maxLength: 100,
    },
    {
      label: '单价',
      code: 'fob_price',
      required: false,
      visible: true,
      suffix: factoryCodes.includes(user?.factory_code) ? '($)' : null,
      type: 'input-number',
      precision: 2,
      min: 0,
    },
    {
      label: '总金额',
      code: 'total_fob',
      required: false,
      visible: true,
      suffix: factoryCodes.includes(user?.factory_code) ? '($)' : null,
      // suffix: '($)',
      type: 'input-number',
      precision: 2,
      min: 0,
    },
    {
      label: '总销售金额',
      code: 'total_sales_cad',
      required: false,
      visible: true,
      suffix: factoryCodes.includes(user?.factory_code) ? '($)' : null,
      // suffix: '($)',
      type: 'input-number',
      precision: 2,
      min: 0,
    },
    {
      label: '出货港',
      code: 'shipment_port',
      required: false,
      visible: true,
      type: 'select',
    },
    {
      label: '收货人',
      code: 'receiver',
      required: false,
      visible: false,
    },
    {
      label: '联系方式',
      code: 'contact',
      required: false,
      visible: false,
    },
    {
      label: '收货地址',
      code: 'address',
      required: false,
      visible: false,
    },
  ];

  return factoryCodes.includes(user?.factory_code) ? CTC : Standard;
}
