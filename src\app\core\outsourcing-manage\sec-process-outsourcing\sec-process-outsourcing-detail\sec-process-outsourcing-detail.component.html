<flc-app-header [showTemplate]="true" [headerTopTitle]="headerTopTitleTpl" [headerBtn]="headerBtnTmpl"></flc-app-header>
<ng-template #headerTopTitleTpl>
  <div class="head-title">
    {{ topTitle }}
  </div>
</ng-template>
<!-- 头部按钮 -->
<ng-template #headerBtnTmpl>
  <div class="btn-box">
    <button *ngIf="['read'].includes(editMode)" nz-button flButton="default" [nzShape]="'round'" (click)="back()">
      {{ 'btn.back' | translate }}
    </button>

    <button *ngIf="['add', 'edit'].includes(editMode)" nz-button flButton="default" [nzShape]="'round'" (click)="cancel()">
      {{ 'btn.cancel' | translate }}
    </button>
    <!-- 更新使用 -->
    <button
      *ngIf="
        ['edit'].includes(editMode) &&
        [OrderStatus.toSubmit].includes(orderStatus) &&
        _service.btnArr.includes('bulk:sec-process-outsourcing-update')
      "
      nz-button
      flButton="pretty-minor"
      [nzShape]="'round'"
      [flcDisableOnClick]="1000"
      (click)="save()">
      <i nz-icon [nzIconfont]="'icon-baocun'"></i>{{ 'btn.save' | translate }}
    </button>
    <button
      *ngIf="['edit'].includes(editMode) && _service.btnArr.includes('bulk:sec-process-outsourcing-update')"
      nz-button
      flButton="pretty-primary"
      [nzShape]="'round'"
      (click)="commit()"
      [flcDisableOnClick]="1000">
      <i nz-icon [nzIconfont]="'icon-tijiao'"></i>
      {{ 'btn.commit' | translate }}
    </button>
    <!-- 创建使用 -->
    <button
      *ngIf="
        ['add'].includes(editMode) &&
        [OrderStatus.toSubmit].includes(orderStatus) &&
        _service.btnArr.includes('bulk:sec-process-outsourcing-create')
      "
      nz-button
      flButton="pretty-minor"
      [nzShape]="'round'"
      [flcDisableOnClick]="1000"
      (click)="save()">
      <i nz-icon [nzIconfont]="'icon-baocun'"></i>{{ 'btn.save' | translate }}
    </button>
    <!-- 提交 -->
    <button
      *ngIf="['add'].includes(editMode) && _service.btnArr.includes('bulk:sec-process-outsourcing-create')"
      nz-button
      flButton="pretty-primary"
      [nzShape]="'round'"
      (click)="commit()"
      [flcDisableOnClick]="1000">
      <i nz-icon [nzIconfont]="'icon-tijiao'"></i>
      {{ 'btn.commit' | translate }}
    </button>
    <!-- 退回修改 -->
    <button
      *ngIf="
        ['read'].includes(editMode) &&
        [OrderStatus.toAudit, OrderStatus.toModifyAudit].includes(orderStatus) &&
        _service.btnArr.includes('bulk:sec-process-outsourcing-approval')
      "
      nz-button
      flButton="fault-minor"
      [nzShape]="'round'"
      (click)="modify()"
      [flcDisableOnClick]="1000">
      {{ 'btn.modify' | translate }}
    </button>
    <!-- 审核通过 -->
    <button
      *ngIf="
        ['read'].includes(editMode) &&
        [OrderStatus.toAudit, OrderStatus.toModifyAudit].includes(orderStatus) &&
        _service.btnArr.includes('bulk:sec-process-outsourcing-approval')
      "
      nz-button
      flButton="pretty-primary"
      [nzShape]="'round'"
      (click)="pass()">
      {{ 'btn.pass' | translate }}
    </button>
    <!-- 取消订单 -->
    <button
      *ngIf="
        ['read'].includes(editMode) &&
        [OrderStatus.auditPass, OrderStatus.toModify, OrderStatus.modifyAuditReturn].includes(orderStatus) &&
        _service.btnArr.includes('bulk:sec-process-outsourcing-cancel')
      "
      nz-button
      flButton="default-positive-danger"
      [nzShape]="'round'"
      nzDanger
      (click)="cancelOrder()">
      {{ 'btn.cancelOrder' | translate }}
    </button>
    <!-- 编辑 -->
    <button
      *ngIf="
        ['read'].includes(editMode) &&
        ![OrderStatus.toAudit, OrderStatus.toModifyAudit, OrderStatus.cancelled].includes(orderStatus) &&
        _service.btnArr.includes('bulk:sec-process-outsourcing-update')
      "
      nz-button
      flButton="pretty-primary"
      [nzShape]="'round'"
      (click)="edit()"
      [flcDisableOnClick]="1000">
      <i nz-icon [nzIconfont]="'icon-caozuolan_bianji1'"></i>{{ 'btn.edit' | translate }}
    </button>
  </div>
</ng-template>

<!-- 主体内容 -->
<form nz-form [formGroup]="detailForm">
  <div class="go-container">
    <!-- 退回修改 -->
    <div
      *ngIf="[OrderStatus.toModify, OrderStatus.modifyAuditReturn].includes(orderStatus) && orderDetail?.reason"
      class="reason-container">
      <i nz-icon [nzIconfont]="'icon-cuowu'" class="icon-cuowu"></i>
      <span>{{ 'outsourcingDetail.审核未通过。' | translate }}</span>
      <flc-text-truncated [data]="('outsourcingDetail.原因：' | translate) + orderDetail?.reason"></flc-text-truncated>
    </div>
    <!-- 大货单号 -->
    <div class="io-container dotted-line">
      <div class="form-bg">
        <div nz-row>
          <div nz-col [nzSpan]="6" *ngFor="let item of detailFormConfig">
            <nz-form-item *ngIf="item.type === 'select'" class="form-item form-item-{{ item.type }}">
              <nz-form-label [nzRequired]="item.required">{{ item.label }}</nz-form-label>
              <nz-form-control [nzSpan]="18" [flcErrorTip]="'outsourcingDetail.' + item.label | translate">
                <ng-container
                  *ngIf="
                    editMode !== 'read' &&
                      (orderStatus === OrderStatus.toSubmit || orderStatus === OrderStatus.toAudit || orderStatus === OrderStatus.toModify);
                    else readOnly
                  ">
                  <flc-dynamic-search-select
                    class="reset-select-width"
                    [canClear]="false"
                    [extraOptions]="extraOptions"
                    [optAlwaysReload]="true"
                    [dataUrl]="searchOptionFetchUrl"
                    [transData]="{ value: 'value', label: 'label' }"
                    [defaultValue]="ioCodeDefaultValue"
                    [formControlName]="item.valueKey"
                    [column]="item.labelKey"
                    [payLoad]="{ cache: false, production_type: 2 }"
                    (handleSearch)="handleChangeValueIo($event)">
                  </flc-dynamic-search-select>
                </ng-container>
                <ng-template #readOnly>
                  <flc-text-truncated [data]="detailForm.value[item.labelKey]"></flc-text-truncated>
                </ng-template>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item *ngIf="item.type === 'text' && editMode !== editModeEnum.add" class="form-item form-item-{{ item.type }}">
              <nz-form-label [nzSpan]="6" class="info-label" [nzRequired]="item.required">{{
                'outsourcingDetail.' + item.label | translate
              }}</nz-form-label>
              <nz-form-control [nzSpan]="10" [flcErrorTip]="'outsourcingDetail.' + item.label | translate">
                <span [style]="item.formater({ order_status: orderDetail?.status })">{{ orderDetail?.status_value }}</span>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>
      </div>
    </div>

    <div
      class="process-first process-second"
      *ngIf="[OrderStatus.auditPass, OrderStatus.modifyAuditReturn, OrderStatus.toModifyAudit].includes(orderStatus)">
      <div class="second-top-box">
        <div style="overflow: hidden; margin-left: 98px">
          <div class="process-item" [ngClass]="outData?.cutCount ? 'item-actived' : ''">
            <div class="first-item">
              <span>{{ 'outsourcingDetail.裁剪' | translate }}</span
              ><span class="item-sum">{{ 'outsourcingDetail.总裁数(裁片)' | translate }}</span>
            </div>
          </div>
          <div class="process-item" [ngClass]="outData?.outsourceQty ? 'item-actived' : ''">
            <div class="first-item">
              <span>{{ 'outsourcingDetail.外发' | translate }}</span
              ><span class="item-sum">{{ 'outsourcingDetail.外发数' | translate }}</span>
            </div>
          </div>
          <div class="process-item" [ngClass]="outData?.receivedQty ? 'item-actived' : ''">
            <div class="first-item">
              <span>{{ 'outsourcingDetail.收货' | translate }}</span
              ><span class="item-sum">{{ 'outsourcingDetail.收货数' | translate }}</span>
            </div>
          </div>
          <div class="process-item" [ngClass]="outData?.finishedQty ? 'item-actived' : ''">
            <div class="first-item">
              <span>{{ 'outsourcingDetail.加工' | translate }}</span
              ><span class="item-sum">{{ 'outsourcingDetail.完成数' | translate }}</span>
            </div>
          </div>
          <div class="process-item" [ngClass]="outData?.qualifiedQty ? 'item-actived' : ''">
            <div class="first-item">
              <span>{{ 'outsourcingDetail.质检' | translate }}</span
              ><span class="item-sum">{{ 'outsourcingDetail.合格数' | translate }}</span>
            </div>
          </div>
          <div class="process-item" [ngClass]="outData?.sendQty ? 'item-actived' : ''">
            <div class="first-item">
              <span>{{ 'outsourcingDetail.走货' | translate }}</span
              ><span class="item-sum">{{ 'outsourcingDetail.已发货' | translate }}</span>
            </div>
          </div>
        </div>
        <div *ngFor="let item of outData" class="bulk-out-box">
          <div class="sencond-titlte">
            <flc-text-truncated *ngIf="item.extra_process_name" [data]="item.extra_process_name"></flc-text-truncated>
          </div>
          <div class="process-item-num" [ngClass]="item.cut_count > 0 ? 'item-actived' : ''">
            <div class="first-desc">
              <span class="process-num"><flc-text-truncated [data]="'(' + item?.cut_progress + ')'"></flc-text-truncated></span
              ><span><flc-text-truncated [data]="item.cut_count | number"></flc-text-truncated></span>
            </div>
          </div>
          <div class="process-item-num" [ngClass]="item.outsource_qty > 0 ? 'item-actived' : ''">
            <div class="first-desc">
              <span class="process-num"><flc-text-truncated [data]="'(' + item?.outsource_progress + ')'"></flc-text-truncated></span
              ><span><flc-text-truncated [data]="item.outsource_qty | number"></flc-text-truncated></span>
            </div>
          </div>
          <div class="process-item-num" [ngClass]="item.received_qty > 0 ? 'item-actived' : ''">
            <div class="first-desc">
              <span class="process-num"><flc-text-truncated [data]="'(' + item?.received_progress + ')'"></flc-text-truncated></span
              ><span><flc-text-truncated [data]="item.received_qty | number"></flc-text-truncated></span>
            </div>
          </div>
          <div class="process-item-num" [ngClass]="item.finished_qty > 0 ? 'item-actived' : ''">
            <div class="first-desc">
              <span class="process-num"><flc-text-truncated [data]="'(' + item?.finished_progress + ')'"></flc-text-truncated></span
              ><span><flc-text-truncated [data]="item.finished_qty | number"></flc-text-truncated></span>
            </div>
          </div>
          <div class="process-item-num" [ngClass]="item.qualified_qty > 0 ? 'item-actived' : ''">
            <div class="first-desc">
              <span class="process-num"><flc-text-truncated [data]="'(' + item?.qualified_progress + ')'"></flc-text-truncated></span
              ><span><flc-text-truncated [data]="item.qualified_qty | number"></flc-text-truncated></span>
            </div>
          </div>
          <div class="process-item-num" [ngClass]="item.sent_qty > 0 ? 'item-actived' : ''">
            <div class="first-desc">
              <span class="process-num"><flc-text-truncated [data]="'(' + item.sent_progress + ')'"></flc-text-truncated></span
              ><span><flc-text-truncated [data]="item.sent_qty | number"></flc-text-truncated></span>
            </div>
          </div>
        </div>
        <div class="process-title">{{ 'outsourcingDetail.生产进度' | translate }}：</div>
      </div>
    </div>

    <div class="io-content" *ngIf="editMode !== 'add' || showMainPanel">
      <!-- 加工厂 -->
      <app-order-outsourcing-factory-container
        [editMode]="editMode"
        [leftContainerTpl]="leftContainerTpl"
        [parentGroup]="detailForm"
        (addFactoryItem)="addFactoryItem()">
        <div formArrayName="factorys">
          <ng-container *ngFor="let plant of factorys.controls; let i = index" [formGroupName]="i">
            <app-order-outsourcing-factory-item
              #factoryItem
              [labelName]="'outsourcingDetail.外发厂' | translate"
              [editMode]="editMode"
              [detailForm]="detailForm"
              [parentGroup]="detailForm.get('factorys').controls[i]"
              [tabs]="pos"
              [surplusPos]="surplusPos"
              [factoryIndex]="i"
              [orderStatus]="orderStatus"
              [_toggle]="plant.get('is_open')?.value">
            </app-order-outsourcing-factory-item>
          </ng-container>
        </div>
      </app-order-outsourcing-factory-container>
      <ng-template #leftContainerTpl>
        <sec-process-banner
          #secProcessRef
          [detailForm]="detailForm"
          [editMode]="editMode"
          [extra_process_info]="extra_process_info"></sec-process-banner>
      </ng-template>

      <!-- 剩余未分配 -->
      <app-order-outsourcing-card-container [title]="'outsourcingDetail.剩余未分配' | translate" [borderRadiusSize]="'12px 12px 4px 4px'">
        <app-order-outsourcing-tabs-container
          [tabsBodyTpl]="surplusColorSizeTpl"
          [modelType]="false"
          [showPersonalInfo]="true"
          [tabs]="surplusPos"
          [colorSizeRefs]="colorSizeTableSurplusRefs">
          <ng-template #surplusColorSizeTpl let-data="data">
            <flc-color-size-table
              #colorSizeSurplusTable
              class="rest-color-size-table-padding"
              [dataList]="data?.po_lines"></flc-color-size-table>
          </ng-template>
        </app-order-outsourcing-tabs-container>
        <!-- <合计> -->
        <ng-container *ngIf="surplusLines.length; else surplusNotData">
          <div class="dotted-line factory-dotted-line"></div>
          <app-order-outsourcing-card-container
            [title]="'outsourcingDetail.合计' | translate"
            [showCorner]="true"
            [titleLine]="false"
            [cardPadding]="'0'"
            [borderRadiusSize]="'12px 12px 4px 4px'">
            <div>
              <flc-color-size-table [dataList]="surplusLines"></flc-color-size-table>
            </div>
          </app-order-outsourcing-card-container>
        </ng-container>
        <!-- 暂无数据 -->
        <ng-template #surplusNotData>
          <flc-no-data [flcDisableOnClick]="1000" [noDataTextTpl]="noDataTextTpl" class="surplus-no-data"></flc-no-data>
          <ng-template #noDataTextTpl>
            <span>{{ 'outsourcingMessage.暂无数据，已全部分配完成' | translate }}</span>
          </ng-template>
        </ng-template>
      </app-order-outsourcing-card-container>

      <div style="margin: 8px 0">
        <app-order-outsourcing-card-container [title]="'outsourcingDetail.基本信息' | translate" [borderRadiusSize]="'4px 4px 4px 4px'">
          <app-order-outsourcing-basic-info *ngIf="io_basic" [basicInfo]="io_basic"></app-order-outsourcing-basic-info>
        </app-order-outsourcing-card-container>
      </div>

      <app-order-outsourcing-card-container [title]="'outsourcingDetail.颜色尺码' | translate">
        <app-order-outsourcing-tabs-container
          [tabsBodyTpl]="colorSizeTpl"
          [tabs]="pos"
          [showPersonalInfo]="true"
          [colorSizeRefs]="colorSizeTableRefs">
          <ng-template #colorSizeTpl let-data="data">
            <flc-color-size-table class="rest-color-size-table-padding" #colorSizeTable [dataList]="data?.po_lines"></flc-color-size-table>
          </ng-template>
        </app-order-outsourcing-tabs-container>
      </app-order-outsourcing-card-container>
      <!-- <合计> -->
      <div class="dotted-line"></div>
      <app-order-outsourcing-card-container
        [title]="'outsourcingDetail.合计' | translate"
        [showCorner]="true"
        [titleLine]="false"
        [borderRadiusSize]="'12px 12px 4px 4px'">
        <div>
          <ng-container *ngIf="io_lines?.length; else io_linesNotData">
            <flc-color-size-table [dataList]="io_lines"></flc-color-size-table>
          </ng-container>
          <ng-template #io_linesNotData>
            <flc-no-data [flcDisableOnClick]="1000" [noDataTextTpl]="noDataTextTpl"></flc-no-data>
            <ng-template #noDataTextTpl>
              <span>{{ 'outsourcingMessage.' + '暂无数据' | translate }}</span>
            </ng-template>
          </ng-template>
        </div>
      </app-order-outsourcing-card-container>
    </div>
  </div>
</form>
<!-- 暂无数据 -->
<div *ngIf="editMode === 'add' && !detailForm.get('io_id').value" class="not-data-container">
  <flc-no-data [flcDisableOnClick]="1000" [noDataTextTpl]="noDataTextTpl"></flc-no-data>
  <ng-template #noDataTextTpl>
    <span>{{ 'outsourcingMessage.暂无数据，请先选择大货单号～' | translate }}</span>
  </ng-template>
</div>
