import { ElementRef } from '@angular/core';
import { LineOrderInterface, PlanAlertStatusEnum, PlanStatus } from '.';

export interface DayRange {
  start: string;
  end: string;
  day: number;
  date: Date;
  isWeekday: boolean;
  isRestDay: boolean;
  month: number;
  week: number;
  year: number;
  isToday: boolean;
}

export interface WeekRange {
  week: number;
  year: number;
  children: DayRange[];
}

export interface PlanGraph {
  items: PlanGraphItem[];
  height: number;
  status: PlanStatus;
  overdue: boolean;
  alert: boolean; // 预警
  rehearsal: boolean; // 预排单
  selected: boolean; // 表示一行被选中高亮
  checked: boolean; // 表示io列的复选框被选中
  // actual_qty: number;
  // qty: number;
  order_code: string;
  production_line_name: string;
  production_line_no: string;
  bulk_order_id: number;
  rehearsalHasPublish: boolean; // 该预排单是否发布了大货单
  samHasChanged: boolean; // 该io的sam工时发生变化
  due_date: string;
  warning: boolean;
  cancel_status?: number;
  order_qty?: number;
  output_qty?: number;
}
export interface PlanGraphItem {
  startTime: number;
  endTime: number;
  uniqueId?: string;
  width: number;
  left: number;
  top: number;
  backgroundColor: string;
  title: string;
  order_code: string;
  graphRef?: HTMLElement;
  /** 1-待发布2-已发布 */
  publish_status?: number;
  publish_status_value?: string;
  rawData?: LineOrderInterface;
  disabled: boolean;
  due_times: string;
  tags: PlanAlertStatusEnum[];
  /**
   * 左右拖拽区域
   */
  draggable: boolean;
  clickable: boolean;
  event?: MouseEvent;

  show?: boolean;
  group_id?: number;
  factory_code: string;
  production_line_no?: string;

  // 物料齐套时间
  pre_material_completed_time?: number | null;

  is_pre_order: boolean; // 是否预排单

  is_outsourced: boolean; // 是否外发

  last_publish_user: string;
  last_publish_time: number;
}

export interface PlanGraphEvent {
  ref?: HTMLElement | ElementRef;
  x?: number;
  y?: number;
}
