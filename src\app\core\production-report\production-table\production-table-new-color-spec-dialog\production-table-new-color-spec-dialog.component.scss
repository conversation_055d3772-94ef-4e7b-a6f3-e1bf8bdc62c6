.dashboard {
  padding: 0 12px;
}
.orderInfo {
  background: #f7f8fa;
  border-radius: 0px 0px 8px 8px;
  display: flex;
  padding: 4px 12px;
  align-items: center;
  margin-bottom: 6px;
  column-gap: 4px;
  flex-wrap: wrap;
  .orderItem {
    font-size: 14px;
    color: #262d48;
    font-weight: 500;
    span {
      color: #54607c;
    }
  }
}
.selectLine {
  display: flex;
  padding: 4px 0;
  align-items: center;
  margin-bottom: 6px;
  column-gap: 4px;
  .selectItem {
    flex: 1;
    font-size: 14px;
    color: #262d48;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    span {
      color: #54607c;
      flex-shrink: 0;
      margin-right: 4px;
    }
    :not(span) {
      flex: 1;
    }
  }
}
.inspacetionBoard {
  display: inline-flex;
  width: 100%;
  margin-top: 8px;
  .leftArea {
    flex: 1;
    .ant-form-item {
      margin-bottom: 12px;
    }
    .ant-input-number {
      width: 100%;
    }
  }
  .rightArea {
    flex: 1;
  }
}

.footer {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 56px;
  column-gap: 8px;
}
:host ::ng-deep {
  .ant-divider-horizontal {
    margin: 12px 0 0 0;
  }
  .topTabset nz-tabs-nav {
    margin-bottom: 0;
  }
}
