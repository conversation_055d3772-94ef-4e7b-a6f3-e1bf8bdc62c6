<div class="wrapper-block">
  <div class="block-title">
    <span>{{ 'sampleOutsourcing.detailField.打样资料包' | translate }}</span>
    <div>
      <button nz-button type="default" [nzSize]="'small'" [flButton]="'pretty-default'" [nzShape]="'round'" (click)="onViewVersions()">
        {{ '历史版本' }}
      </button>
      <i
        nz-icon
        [nzIconfont]="isFullScreenMode ? 'icon-shouqi-weixuanzhong' : 'icon-zhankai1'"
        nz-tooltip
        [nzTooltipTitle]="isTooltipVisible ? (isFullScreenMode ? '收起全屏' : '展开全屏') : null"
        class="zhankai-icon"
        (click)="onToggleFullScreen()"></i>
    </div>
  </div>
  <div class="block-content">
    <flss-material-package
      #materialPackageRef
      [entry]="'sampleOutsource'"
      [isOrderEdit]="false"
      [editable]="false"
      [contentHeight]="materialPackageHeight"
      [orderInfo]="orderInfo"
      [canSyncDataToStyleLib]="false">
    </flss-material-package>
  </div>
</div>
