<div class="mode-setting-container">
  <div class="title-label">{{ step }}&nbsp;&nbsp;{{ '工厂模式配置' | translate }}</div>
  <nz-divider></nz-divider>
  <div>
    <div class="mode-content">
      <div class="label-required">{{ '使用版本' | translate }}：</div>
      <form nz-form *ngIf="_service.factoryForm" [formGroup]="_service.factoryForm">
        <nz-radio-group formControlName="product_id">
          <ng-container *ngFor="let item of _service.versionLists || []">
            <label nz-radio-button [nzValue]="item.id">{{ item.name | translate }}</label>
          </ng-container>
        </nz-radio-group>
      </form>
    </div>
  </div>
</div>
