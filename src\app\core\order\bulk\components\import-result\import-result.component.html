<nz-table
  [nzBordered]="true"
  [nzVirtualItemSize]="15"
  [nzData]="dataList"
  [nzFrontPagination]="false"
  [nzShowPagination]="false"
  [nzScroll]="{ y: '500px' }">
  <thead>
    <tr>
      <th [nzWidth]="'46px'">#</th>
      <th *ngFor="let item of tableHeader" [nzWidth]="item?.width">{{ translateName + item.label | translate }}</th>
    </tr>
  </thead>
  <tbody>
    <tr *ngFor="let data of dataList; index as i">
      <td>{{ i + 1 }}</td>
      <td *ngFor="let item of tableHeader">
        <ng-container *ngIf="['handle_type', 'result_type'].includes(item.key); else otherTpl">
          <flc-text-truncated
            *ngIf="item.key === 'handle_type'"
            [data]="translateName + (data?.handle_type === 1 ? '新建' : '更新') | translate"></flc-text-truncated>

          <flc-text-truncated
            *ngIf="item.key === 'result_type'"
            [ngStyle]="{ color: data?.result_type === 1 ? '' : '#ff5c33' }"
            [data]="translateName + (data?.result_type === 1 ? '通过' : '失败') | translate"></flc-text-truncated>
        </ng-container>

        <ng-template #otherTpl>
          <flc-text-truncated [data]="data?.[item.key]"></flc-text-truncated>
        </ng-template>
      </td>
    </tr>
  </tbody>
</nz-table>
