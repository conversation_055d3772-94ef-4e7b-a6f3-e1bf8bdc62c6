import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { TemplatePortal } from '@angular/cdk/portal';
import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  QueryList,
  SimpleChanges,
  TemplateRef,
  ViewChild,
  ViewChildren,
  ViewContainerRef,
} from '@angular/core';
import { format, startOfDay } from 'date-fns';
import { NzDrawerService } from 'ng-zorro-antd/drawer';
import { NzModalService } from 'ng-zorro-antd/modal';
import {
  DayRange,
  GanttCellWidth,
  LineOrderInterface,
  PlanGraphActionTypeEnum,
  PlanGraphEvent,
  PlanGraphItem,
  PlanRightClickParam,
  ProductionLinePlanRenderItem,
  ProductionPlanLineItem,
  WeekRange,
} from '../../../interface';
import { ProductionPlanShareService } from '../../../production-plan-share.service';
import { GraphTableComponent } from '../../graph-table/graph-table.component';
import { SewingItemDrawerComponent } from '../../sewing-item-drawer/sewing-item-drawer.component';
import { ProductionLineGraphWrapComponent } from '../production-line-graph-wrap/production-line-graph-wrap.component';
import { NzMessageService } from 'ng-zorro-antd/message';
import { FlcModalService } from 'fl-common-lib';
import { ProductionReplaceModalComponent } from '../production-replace-modal/production-replace-modal.component';

@Component({
  selector: 'app-production-line-view-graph',
  templateUrl: './production-line-view-graph.component.html',
  styleUrls: ['./production-line-view-graph.component.scss'],
})
export class ProductionLineViewGraphComponent implements OnInit, OnChanges {
  @Input() isEdit = false;
  @ViewChild(GraphTableComponent) productionGraphTable!: GraphTableComponent;
  @ViewChildren(ProductionLineGraphWrapComponent) productionLineGraphWrap!: QueryList<ProductionLineGraphWrapComponent>;
  @ViewChild('blankRightClicktpl') blankRightClickTemplate!: TemplateRef<{ $implicit: string }>;
  @ViewChild('graphItemRightClicktpl') graphItemRightClickTemplate!: TemplateRef<{
    $implicit: LineOrderInterface;
    ref: HTMLElement;
  }>;
  @Input() graphOptions: { view: 'order' | 'productionLine'; item_dimension: 'io' | 'po' } = {
    view: 'productionLine',
    item_dimension: 'io',
  };
  @Input() productionLineList: ProductionPlanLineItem[] = [];
  @Input() dates!: {
    start_date: Date;
    end_date: Date;
  };
  @Output() actionRefresh = new EventEmitter<boolean>();
  @Output() forwardDate = new EventEmitter(); // 向前5天
  @Output() backwardsDate = new EventEmitter(); // 向后5天
  @Output() actionLoading = new EventEmitter<boolean>(); // 控制产线视图loading
  @Output() onAction = new EventEmitter<{
    type: PlanGraphActionTypeEnum;
    data: any;
  }>();
  @Output() onSelectLineEmit = new EventEmitter<ProductionPlanLineItem | null>();

  planGraphActionTypeEnum = PlanGraphActionTypeEnum;

  days: DayRange[] = []; // 用于显示有多少个天（表头）
  weeks: WeekRange[] = []; // 用于显示多少个周
  renderData: ProductionLinePlanRenderItem[] = [];
  options = {
    signalWidth: GanttCellWidth.day,
    dayWidth: GanttCellWidth.day, // 日维度一天48，小时维度一天24*12
    wrapBodyWidth: 0,
    dimension: 'day',
    per1Px: 1,
  };
  colors = ['#FFE3CB', '#C6EDFF', '#C1E8E0'];
  rightMenuRef?: OverlayRef;

  checkboxConfig = {
    indeterminate: false,
    allChecked: false,
  };

  selectedItemList: { rawData: LineOrderInterface; ref: HTMLElement }[] = [];

  op_index: number | null = 0;

  graphItemList: PlanGraphItem[] = [];
  private _hoverItem?: LineOrderInterface | null;
  get detailBarItem(): LineOrderInterface | null {
    if (this.selectedItemList.length > 0) {
      return [...this.selectedItemList].pop()?.rawData || null;
    } else {
      return this._hoverItem || null;
    }
  }

  constructor(
    public _shareService: ProductionPlanShareService,
    private _overlay: Overlay,
    private _viewContainerRef: ViewContainerRef,
    private _modal: NzModalService,
    private _drawerService: NzDrawerService,
    private _msg: NzMessageService,
    private _flcModal: FlcModalService
  ) {}

  ngOnInit(): void {
    this.transformData();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.productionLineList && !changes.productionLineList.firstChange && changes.productionLineList.currentValue) {
      this.transformData();
    }
  }

  clearAll() {
    this.selectedItemList = [];
    this._hoverItem = null;
  }

  // 合并订单后清空向前信息
  clearDetailBar() {
    this.selectedItemList = [];
    this._hoverItem = null;
  }

  /** 转换父组件传来的数据 */
  transformData() {
    this.renderData = [];
    this.graphItemList = [];
    this.productionLineList.forEach((item) => {
      const shadow = new ProductionLinePlanRenderItem({
        factory_name: item.factory_name,
        OrderRenderList: [],
        rawData: item,
        rest_days: item?.rest_days ?? [],
        production_line_no: item.production_line_no,
        production_line_name: item.production_line_name,
        height: 52,
      });
      this.renderData.push(shadow);
      item.line_orders.forEach((order, index) => {
        const graph: PlanGraphItem = {
          title: order.order_code,
          factory_code: item.factory_code,
          production_line_no: item.production_line_no,
          startTime: order.start_time,
          endTime: order.end_time,
          group_id: order.group_id,
          clickable: true,
          draggable: true,
          left: 0,
          top: 0,
          width: 0,
          tags: order.tags,
          backgroundColor: this.colors[index % 3],
          order_code: order.order_code,
          publish_status: order.publish_status,
          publish_status_value: ['', '待发布', '已发布'][order.publish_status],
          rawData: order,
          disabled: !order.can_edit,
          due_times: order.due_times.map((item) => format(item, 'yyyy/MM/dd')).join(','),
          pre_material_completed_time: order.pre_material_completed_time,
          is_pre_order: order.is_pre_order,
          is_outsourced: order.is_outsourced,
          last_publish_time: order.last_publish_time,
          last_publish_user: order.last_publish_user,
        };
        this.graphItemList.push(graph);
        order.graphItem = graph;
        shadow.OrderRenderList.push(graph);
      });
    });
    this.checkSelected();
  }

  /** 点击进度条 */
  tapGraphItem(item: PlanGraphItem & PlanGraphEvent, op_index: number) {
    this.selectedItemList.forEach((item) => (item.rawData.isSelected = false));
    // 左点击事件
    if (this.selectedItemList.findIndex((_item) => _item.rawData.group_id === item.rawData?.group_id) !== -1) {
      this.selectedItemList = [];
      return;
    }
    item.rawData && (item.rawData.isSelected = true);
    this.selectedItemList = [{ rawData: item.rawData as LineOrderInterface, ref: item.ref as HTMLElement }];
  }

  /**
   * 鼠标hover进度条
   * hover实际条，对应亮计划条
   * hover计划条，对应亮实际条
   * @param item
   */
  hoverGraphItem(item: PlanGraphItem) {
    // 对应高亮hover条
    this.renderData.forEach((renderData) => {
      if (renderData.production_line_no === item.production_line_no) {
        renderData.OrderRenderList?.filter((order) => order?.rawData?.group_id === item?.rawData?.group_id)?.forEach((order) => {
          order.rawData!.isHover = true;
        });
      }
    });
    this._hoverItem = item.rawData;
  }

  /**
   * 鼠标移出进度条，去除所有hover样式
   */
  leaveGraphItem() {
    this.renderData.forEach((renderData) => {
      renderData.OrderRenderList?.forEach((order: any) => {
        order.rawData.isHover = false;
      });
    });
  }

  rightClick(e: PlanRightClickParam, op_index: number) {
    this.op_index = op_index;
    const beforeClickStatus = e?.rawData?.isSelected ?? true;
    let ref: OverlayRef | undefined;
    if (e.postion === 'graphItem') {
      e.rawData && (e.rawData.isSelected = true);
      ref = this._overlay.create({
        disposeOnNavigation: true,
        hasBackdrop: true,
        width: '120px',
        positionStrategy: this._overlay
          .position()
          .flexibleConnectedTo({
            x: e.x,
            y: e.y,
          })
          .withPositions([{ originX: 'start', originY: 'top', overlayX: 'center', overlayY: 'top', offsetY: 2 }]),
      });
      ref.attach(
        new TemplatePortal(this.graphItemRightClickTemplate, this._viewContainerRef, {
          $implicit: e.rawData,
          ref: e.ref as HTMLElement,
        })
      );
    }
    ref?.backdropClick().subscribe(() => {
      ref?.dispose();
    });
    ref?.detachments().subscribe(() => {
      if (!beforeClickStatus && e.rawData) {
        e.rawData.isSelected = false;
      }
    });
    this.rightMenuRef = ref;
    ref?.detachments().subscribe(() => {});
  }

  onActionType(type: PlanGraphActionTypeEnum, item: LineOrderInterface) {
    switch (type) {
      case PlanGraphActionTypeEnum.detail:
        this.orderDetail(item);
        break;
      case PlanGraphActionTypeEnum.split:
        this.splitOrder(item);
        break;
      case PlanGraphActionTypeEnum.forwardMerge:
        this.forwardMerge(item);
        break;
      case PlanGraphActionTypeEnum.forwardConnect:
        this.forwardConnect(item);
        break;
      case PlanGraphActionTypeEnum.AllConnect:
        this.allConnect(item);
        break;
      case PlanGraphActionTypeEnum.cancel:
        this.cancelOrder(item);
        break;
      case PlanGraphActionTypeEnum.replace:
        this.onRelace(item);
        break;
    }
    this.rightMenuRef?.dispose();
  }

  // 拆分订单
  private splitOrder(data: LineOrderInterface) {
    const productionLineGraph = this.productionLineGraphWrap.toArray()?.[this.op_index!];
    const _time = productionLineGraph?.getClickTime() ?? 0;
    if (!_time) return;
    // 如果拆分日期为计划起始日期，则报错
    if (startOfDay(_time) === startOfDay(data.plan_start_time)) {
      this._msg.error('当前点击处无法快速拆分');
      return;
    }

    this.onAction.emit({
      type: PlanGraphActionTypeEnum.split,
      data: { group_id: data.group_id, temp_session: this._shareService.temp_session, split_date: Number(format(_time, 'T')) },
    });
  }

  // 向前合并
  private forwardMerge(item: LineOrderInterface) {
    // 排序
    const _sort_list: PlanGraphItem[] = [...this.graphItemList]
      .sort((a, b) => a.left - b.left)
      .filter((_item) => _item.factory_code === item.factory_code && _item.production_line_no == item.production_line_no);
    // 判断当前衔接的订单是否是当前订单的同一个io
    const _index = _sort_list.findIndex((_item) => _item.rawData?.group_id === item.group_id);
    // if (_index === 0) {
    //   this._msg.error('当前订单是第一个，无法向前合并');
    //   return;
    // }
    const beforeItem = _sort_list[_index - 1];
    if (beforeItem.rawData?.order_uuid !== item.order_uuid) {
      this._msg.error('当前订单与上一个订单不是同一个IO，无法向前合并');
      return;
    }
    this.onAction.emit({
      type: PlanGraphActionTypeEnum.forwardMerge,
      data: { temp_session: this._shareService.temp_session, group_ids: [beforeItem.group_id, item.group_id] },
    });
  }

  // 向前衔接
  private forwardConnect(item: LineOrderInterface) {
    // // 排序
    // const _sort_list: PlanGraphItem[] = [...this.graphItemList].sort((a, b) => a.left - b.left);
    // // 判断当前衔接的是否第一个
    // const _index = _sort_list.findIndex((_item) => _item.rawData?.group_id === item.group_id);
    // if (_index === 0) {
    //   this._msg.error('当前订单是第一个，无法向前衔接');
    //   return;
    // }
    this.onAction.emit({
      type: PlanGraphActionTypeEnum.forwardConnect,
      data: {
        temp_session: this._shareService.temp_session,
        group_id: item.group_id,
        is_all: false,
        factory_code: item.factory_code,
        production_line_no: item.production_line_no,
      },
    });
  }
  // 整体向前衔接
  private allConnect(item: LineOrderInterface) {
    this.onAction.emit({
      type: PlanGraphActionTypeEnum.AllConnect,
      data: {
        temp_session: this._shareService.temp_session,
        is_all: true,
        factory_code: item.factory_code,
        production_line_no: item.production_line_no,
        group_id: item.group_id,
      },
    });
  }

  private orderDetail(data: LineOrderInterface) {
    this.rightMenuRef?.dispose();
    const drawerRef = this._drawerService.create<SewingItemDrawerComponent>({
      nzTitle: '进度条详情',
      nzPlacement: 'right',
      nzWidth: '520px',
      nzMaskClosable: false,
      nzContent: SewingItemDrawerComponent,
      nzContentParams: {
        group_id: data?.group_id,
        isEdit: this.isEdit,
      },
      nzWrapClassName: 'sewing-line-drawer',
      nzOnCancel: () => {
        return new Promise((resolve) => {
          const instance = drawerRef?.getContentComponent();
          instance?.close();
          resolve(true);
        });
      },
    });
    drawerRef.afterClose.subscribe((data: { is_refresh_list: boolean; is_refresh_session: boolean }) => {
      if (data?.is_refresh_list) {
        this._shareService.undo_disabled = false;
        this.actionRefresh.emit();
      }
    });
  }

  // 取消分配
  cancelOrder(data: LineOrderInterface) {
    this.rightMenuRef?.dispose();
    this._flcModal
      .confirmCancel({
        content: '确认取消分配',
      })
      .afterClose.subscribe((res) => {
        if (!res) return;
        this.onAction.emit({
          type: PlanGraphActionTypeEnum.cancel,
          data: {
            temp_session: this._shareService.temp_session,
            group_id: data.group_id,
          },
        });
      });
  }

  // 替换正式订单
  onRelace(data: LineOrderInterface) {
    const _modal = this._modal.create({
      nzTitle: '替换订单',
      nzContent: ProductionReplaceModalComponent,
      nzFooter: null,
      nzWidth: '650px',
      nzWrapClassName: 'flc-confirm-modal',
      nzMaskClosable: false,
      nzComponentParams: {
        data,
      },
      nzAutofocus: null,
      nzOnOk: (com) => {
        this.onAction.emit({
          type: PlanGraphActionTypeEnum.replace,
          data: {
            modal: _modal,
            payload: {
              temp_session: this._shareService.temp_session,
              group_id: data.group_id,
              list: com.getPayload(),
            },
          },
        });
        return false;
      },
    });
  }

  forward() {
    this.forwardDate.emit();
  }

  backwards() {
    this.backwardsDate.emit();
  }

  daysChange(e: any) {
    this.days = [...e];
  }

  weeksChange(e: any) {
    this.weeks = [...e];
  }

  optionsChange(e: any) {
    this.options = {
      ...e,
    };
  }

  selectAll(status: boolean) {
    this.productionLineList.forEach((item) => {
      item.isSelected = status;
    });
    this.onSelectLineEmit.emit(null);
  }

  onSelectLine(data: ProductionLinePlanRenderItem) {
    const _data = this.productionLineList.find(
      (item) => item.factory_code === data.rawData.factory_code && item.production_line_no === data.production_line_no
    );
    this.onSelectLineEmit.emit(_data);
    this.checkSelected();
  }

  checkSelected() {
    if (!this.productionLineList.length) {
      this.checkboxConfig = {
        allChecked: false,
        indeterminate: false,
      };
      return;
    }
    const allChecked = this.productionLineList.every((item) => item.isSelected);
    const indeterminate =
      !allChecked && this.productionLineList.some((item) => item.isSelected || item.line_orders?.some((order) => order.isSelected));
    this.checkboxConfig = {
      allChecked,
      indeterminate,
    };
  }

  moveDatePosition() {
    setTimeout(() => {
      const searchData = this._shareService.searchData;
      const showEnd = searchData.end_date ?? new Date();
      this.productionGraphTable?.toScrollDate(showEnd);
    }, 10);
  }

  profileTable() {
    this.productionGraphTable?.calCellWidth();
  }
}
