# compiled output
/dist
/tmp
/out-tsc

# dependencies
/node_modules
package-lock.json

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# misc
/.angular/cache
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings

# System Files
.DS_Store
Thumbs.db

# CI/CD
/.info
container/.cicd
container/scripts
container/charts
container/package
container/helm/values.yaml
container/helm/Chart.yaml
!container/**/*.yaml

/dist
package-lock.json

# ignore softlinks
src/fl-common-lib
src/fl-sewsmart-lib

scripts/lib.json