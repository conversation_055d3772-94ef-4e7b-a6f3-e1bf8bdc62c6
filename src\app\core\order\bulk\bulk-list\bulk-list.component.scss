.bulk-table-box {
  margin-top: 10px;

  .update-tip {
    font-size: 9px;
    position: absolute;
    top: 0px;
    background: #4d96ff;
    color: white;
    margin-left: -8px;
    padding: 0 3px;
    border-radius: 0 0 5px 0;
  }

  .urgent-tip {
    font-size: 10px;
    position: absolute;
    top: 0px;
    right: 0px;
    background: #f74949;
    color: white;
    padding: 0 3px;
    border-radius: 0 0 0 5px;
  }
}
.search-select-loading {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tag-list {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  & > span {
    cursor: pointer;
  }
  & > span + span {
    margin-top: 12px;
  }
}

::ng-deep {
  .batch-action-item {
    color: #54607c;
    cursor: pointer;
    margin-bottom: 8px;
    &:last-child {
      margin-bottom: 0;
    }
    &:hover {
      color: #00c790;
    }
  }
  .ant-popover-inner {
    border-radius: 4px;
  }
  .ant-popover-inner-content {
    padding: 8px 16px;
  }
  .danger:hover {
    color: #f74949;
  }
  .bulk-import-result-modal .ant-modal-footer {
    justify-content: center;
  }
}
