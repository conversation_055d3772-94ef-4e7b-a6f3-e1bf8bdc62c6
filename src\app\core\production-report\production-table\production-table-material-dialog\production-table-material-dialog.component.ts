import { Component, EventEmitter, Input, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import {
  ProductionTableMaterialLine,
  ProductionTableNodeDetailItem,
  ProductionTableOrderLineInfo,
  ProductionTableOrderNode,
} from '../production-table.config';
import { ProductionTableService } from '../production-table.service';
import { isNil, isNumber, round } from 'lodash';

@Component({
  selector: 'app-production-table-material-dialog',
  templateUrl: './production-table-material-dialog.component.html',
  styleUrls: ['./production-table-material-dialog.component.scss'],
})
export class ProductionTableMaterialDialogComponent implements OnInit {
  @Input() nodeInfo!: ProductionTableOrderNode;
  @Input() orderInfo!: ProductionTableOrderLineInfo;
  @Output() closeModal = new EventEmitter<boolean>();
  @ViewChild('historyTemplate') historyTemplate!: TemplateRef<HTMLElement>;
  translateName = 'ProductionTableTemplateFiled.';
  rawLineList: MaterialLine[] = [];
  colorList: ColorItem[] = [];
  currentColorIndex = 0;
  materialSearchValue: string | null = null;
  historyLine: MaterialLine | null = null;
  historyModal?: NzModalRef<any, any>;
  get currentColor(): ColorItem {
    return this.colorList[this.currentColorIndex];
  }
  get fabricList(): MaterialLine[] {
    return this.currentColor.fabricList;
  }
  get accessoryList(): MaterialLine[] {
    return this.currentColor.accessoryList;
  }
  constructor(private _service: ProductionTableService, private _notice: NzNotificationService, private _modal: NzModalService) {}

  ngOnInit(): void {
    this.getNodeInfo();
  }
  getNodeInfo() {
    this._service
      .getNodeInfo({
        bulk_order_id: this.orderInfo.bulk_order_id,
        factory_out_sourcing_id: this.orderInfo.factory_out_sourcing_id,
        node_id: this.nodeInfo.id,
        node_type: this.nodeInfo.node_type,
      })
      .subscribe((result: { code: number; data: ProductionTableNodeDetailItem }) => {
        if (result.code === 200) {
          this.rawLineList = result.data.material_list.map((item) => new MaterialLine(item));
          this.handleRawLineList();
        }
      });
  }
  handleRawLineList() {
    const colorMap: Map<number, ColorItem> = new Map();
    this.rawLineList.forEach((item) => {
      const targetColor = colorMap.get(item.order_color_id);
      if (targetColor) {
        if (item.material_type === 1) {
          targetColor.fabricList.push(item);
        } else {
          targetColor.accessoryList.push(item);
        }
      } else {
        const newOne: ColorItem = {
          color_id: item.order_color_id,
          color_name: item.order_color_name,
          fabricList: [],
          accessoryList: [],
        };
        if (item.material_type === 1) {
          newOne.fabricList.push(item);
        } else {
          newOne.accessoryList.push(item);
        }
        colorMap.set(item.order_color_id, newOne);
      }
    });
    this.colorList = Array.from(colorMap.values());
  }
  showDetail(line: MaterialLine) {
    this.historyLine = line;
    const payload = {
      node_id: this.nodeInfo.id,
      node_type: this.nodeInfo.node_type,
      sku_id: line.sku_id,
      order_color_id: line.order_color_id,
    };
    this._service.getNodeHistory(payload).subscribe((result) => {
      if (result.code === 200) {
        this.historyLine!.historyList = result.data.line_list;
        this.historyModal = this._modal.create({
          nzTitle: this._service.translateValue(this.translateName + '历史记录'),
          nzContent: this.historyTemplate,
          nzFooter: null,
          nzWidth: 800,
          nzBodyStyle: {
            padding: '0 12px',
          },
        });
      }
    });
  }
  saveHistoryChange() {
    const payload = {
      update_list: this.historyLine?.historyList.map((line) => ({
        node_id: this.nodeInfo.id,
        info_id: line.info_id,
        id: line.id,
        qty: line.qty.toString(),
        node_type: this.nodeInfo.node_type,
        // pictures: line.pictures,
      })),
    };
    this._service.updateNodeHistory(payload).subscribe((result) => {
      if (result.code === 200) {
        this._notice.success('', this._service.translateValue('flss.success.update'));
        this.getNodeInfo();
        this.closeHistoryModal();
      }
    });
  }
  closeHistoryModal() {
    this.historyLine = null;
    this.historyModal?.close();
  }
  save() {
    const payload: { id: number; content_type: number; node_list: MaterialReturnData[] } = {
      id: this.nodeInfo.id,
      content_type: 3,
      node_list: this.rawLineList.filter((item) => item.hasQty).map((item) => item.getReturnData()),
    };
    this._service.updateNode(payload).subscribe((result) => {
      if (result.code === 200) {
        this._notice.success('', this._service.translateValue('flss.success.update'));
        this.closeModal.emit(true);
      }
    });
  }
  cancel() {
    this.closeModal.emit(false);
  }
}
class MaterialLine {
  get hasQty(): boolean {
    return this.qty > 0;
  }
  getReturnData(): MaterialReturnData {
    return {
      node_type: 8,
      sku_id: this.sku_id,
      order_color_id: this.order_color_id,
      qty: this._qty,
      finished_time: this.finished_time!.getTime(),
    };
  }
  material_name: string;
  material_color_name: string;
  supplier_name: string;
  specification_name: string;
  material_type: number;
  _qty!: string;
  unit_name: string;
  historyList: HistoryLine[] = [];
  get qty(): number {
    return Number(this._qty);
  }
  set qty(value: number | string) {
    if (isNumber(value) && isNil(this.finished_time)) {
      this.finished_time = new Date();
    }
    this._qty = String(value);
  }
  _total_qty!: string;
  get total_qty(): number {
    return Number(this._total_qty);
  }
  set total_qty(value: number | string) {
    this._total_qty = String(value);
  }
  _material_qty!: string;
  get material_qty(): number {
    return Number(this._material_qty);
  }
  set material_qty(value: number | string) {
    this._material_qty = String(value);
  }
  sku_id: number;
  order_color_id: number;
  order_color_name: string;

  finished_time?: Date;
  get status(): 1 | 2 | 3 {
    if (this.total_qty >= this.material_qty) {
      return 3;
    } else if (this.total_qty === 0) {
      return 1;
    } else {
      return 2;
    }
  }
  get statusName(): string {
    switch (this.status) {
      case 1:
      case 2:
        return '未完成';
      case 3:
        return '已完成';
    }
  }
  get statusTextColor(): string {
    switch (this.status) {
      case 1:
      case 2:
        return '#FB6401';
      case 3:
        return '#007AFF';
    }
  }
  get statusBgColor(): string {
    switch (this.status) {
      case 1:
      case 2:
        return '#FFF3ED';
      case 3:
        return '#DEEFFF';
    }
  }
  get finishedPercent(): number {
    if (this.total_qty >= this.material_qty) {
      return 100;
    } else if (this.material_qty === 0) {
      return 0;
    } else {
      return round((this.total_qty / this.material_qty) * 100, 2);
    }
  }

  constructor(input: ProductionTableMaterialLine) {
    this.material_name = input.material_name;
    this.material_color_name = input.material_color_name;
    this.supplier_name = input.supplier_name;
    this.specification_name = input.specifications;
    this.material_type = input.material_type;
    this.qty = input.qty;
    this.unit_name = input.unit_name;
    this.total_qty = input.total_qty;
    this.material_qty = input.material_qty;
    this.sku_id = input.sku_id;
    this.order_color_id = input.order_color_id;
    this.order_color_name = input.order_color_name;
  }
}

interface ColorItem {
  color_id: number;
  color_name: string;
  fabricList: MaterialLine[];
  accessoryList: MaterialLine[];
}

interface MaterialReturnData {
  sku_id: number;
  qty: string;
  finished_time: number;
  node_type: number;
  order_color_id: number | null;
}

interface HistoryLine {
  color_list: any[];
  finished_time: number;
  id: number;
  info_id: number;
  inspected_qty: number;
  qualified_qty?: number;
  qualifiedPercent?: number;
  defected_qty: number;
  defectedPercent?: number;
  inspected_status: number;
  process_desc: string;
  qty: number;
  remark: string;
  spec_list: any[];
  user_name: string;
}
