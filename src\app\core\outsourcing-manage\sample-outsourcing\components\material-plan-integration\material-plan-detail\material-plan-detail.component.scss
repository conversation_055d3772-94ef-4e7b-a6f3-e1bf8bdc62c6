:host ::ng-deep {
  .material-plan-detail-wrap {
    overflow-x: hidden;
    .title {
      font-size: 16px;
      font-weight: 500;
      color: #54607c;
      margin: 12px 0;
    }

    .label-wrapper {
      width: 90px;
    }
    .label-text {
      color: #515661;
      font-weight: 500;
    }

    .controls-text {
      font-weight: 500;
      color: #222b3c;
      flex: 1;
    }
  }
}

:host ::ng-deep {
  .transition-icon {
    &:hover {
      color: #138aff;
    }
  }
  .transform90.transition-icon {
    transform: rotate(90deg);
    color: #138aff;
    &:hover {
      opacity: 0.8;
    }
  }

  .transition-icon {
    color: #b3b9c9;
    cursor: pointer;
    transition: transform 0.3s ease-out;
    &:hover {
      opacity: 0.8;
    }
  }

  .ant-table-row-expand-icon {
    display: none;
  }
}

:host ::ng-deep .ant-table {
  .ant-table-tbody {
    .ant-table-expanded-row td {
      border-right: none !important;
    }
    .inner-table.ant-table-wrapper:only-child .ant-table {
      border: 1px solid #d4d7dc;
      border-radius: 4px 4px 0px 0px;
      margin: 6px 0 8px;
      .ant-table-thead > tr > th {
        color: #5e6577;
        background: #f7f8fa;
        border-bottom: 1px solid #d4d7dc;
        &::before {
          display: none;
        }
      }
      .ant-table-tbody > tr > td {
        color: #5e6577;
      }
    }
  }
}

:host ::ng-deep .outter-table .ant-table-expanded-row .ant-table-expanded-row-fixed {
  background-color: #f5faff;
}

:host ::ng-deep {
  .inspiration-source {
    .image-title-pannel {
      display: flex;
      font-weight: 500;
      margin-bottom: 8px;
      .image-title {
        flex: 1;
        padding: 0;
        color: #515661;
      }
    }

    .image-describe {
      margin: 12px 0;
    }
  }

  .drop-content {
    display: flex;
    justify-content: center;
    .drop-container {
      position: relative;
      min-width: 510px;
      max-height: 390px;
      overflow-y: scroll;
      overflow-x: hidden;
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      flex-direction: row;
      padding: 10px;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #ccc;
      display: flex;
      gap: 10px;

      &:nth-child(1) {
        margin-right: 10px;
      }
      &::-webkit-scrollbar {
        width: 0;
      }
      &:first-child {
        margin-right: 12px;
      }
    }

    .drop-box {
      width: 140px;
      height: 140px;
      border: solid 1px #ccc;
      font-size: 14px;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      background: #fff;
      border-radius: 4px;
      position: relative;
      z-index: 1;
      transition: box-shadow 200ms cubic-bezier(0, 0, 0.2, 1);
      box-shadow: inset 0px 0px 3px 0px rgba(0, 0, 0, 0.15);

      img {
        width: 100%;
        height: 100%;
        -webkit-user-drag: none;
        border-radius: 4px;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        cursor: pointer;
        object-fit: contain;
      }
      &:hover {
        .action-line {
          display: flex;
        }
      }
    }

    .action-line {
      position: absolute;
      display: flex;
      width: 100%;
      height: 18px;
      padding-bottom: 2px;
      bottom: 0;
      left: 0;
      align-items: center;
      color: #fff;
      font-size: 12px;
      background-color: rgba(34, 43, 60);
      opacity: 0.4;
      border-radius: 0 0 3px 3px;
      cursor: pointer;
      display: none;
      span:hover {
        color: #0f86f8;
      }
    }

    .no-data {
      position: absolute;
      display: flex;
      width: calc(100% - 10px);
      height: calc(100% - 10px);
      justify-content: center;
      align-items: center;
    }
  }
}
