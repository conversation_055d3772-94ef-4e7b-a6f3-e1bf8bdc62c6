/*
 * File: order.module.ts
 * Project: elan-web
 * File Created: Tuesday, 30th August 2022 2:38:37 pm
 * Author: liucp
 * Description:
 * -----
 * Last Modified: Thursday, 22nd September 2022 5:46:48 pm
 * Modified By: liucp
 */
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { FlcLeaveGuard } from 'fl-common-lib';
import { BulkDetailComponent } from './bulk-detail/bulk-detail.component';
import { BulkListComponent } from './bulk-list/bulk-list.component';
const routes: Routes = [
  {
    path: 'list',
    component: BulkListComponent,
    data: {
      keepAlive: true,
    },
  },
  {
    // 大货订单详情
    path: 'list/:id',
    component: BulkDetailComponent,
    canDeactivate: [FlcLeaveGuard],
  },
  { path: '', redirectTo: 'list' },
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class BulkRoutingModule {}
