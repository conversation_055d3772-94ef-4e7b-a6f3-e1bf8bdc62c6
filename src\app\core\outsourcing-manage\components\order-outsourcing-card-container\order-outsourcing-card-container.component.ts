import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-order-outsourcing-card-container',
  templateUrl: './order-outsourcing-card-container.component.html',
  styleUrls: ['./order-outsourcing-card-container.component.scss'],
})
export class OrderOutsourcingCardContainerComponent implements OnInit {
  @Input() title: any;
  @Input() titleLine = true; // 是否需要title下方的直线
  @Input() borderType: 'top' | 'bottom' | 'all' = 'all';
  @Input() borderRadiusSize = ''; //'4px 4px 12px 12px'
  @Input() showCorner = false; //文字开头的角标，默认没有 开启后会有字号变成14px
  @Input() cardPadding = '12px 8px'; // 剩余未分配的情况下 padding要为0 默认 12px 8px
  constructor() {}

  ngOnInit() {}
}
