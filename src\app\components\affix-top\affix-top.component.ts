/*
 * File: affix-top.component.ts
 * Project: sewsmart-web
 * File Created: Thursday, 18th November 2021 2:08:18 pm
 * Author: liucp
 * Description: 头部操作栏固定
 * -----
 * Last Modified: Wednesday, 29th December 2021 12:27:18 pm
 * Modified By: liucp
 */

import { Component, Input, OnInit } from '@angular/core';
import { fromEvent } from 'rxjs';
import { debounceTime } from 'rxjs/operators';

@Component({
  selector: 'app-affix-top',
  templateUrl: './affix-top.component.html',
  styleUrls: ['./affix-top.component.scss'],
})
export class AffixTopComponent implements OnInit {
  @Input() title: string | undefined;
  showTitleBackgroud = false;
  constructor() {}

  ngOnInit(): void {
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const element = document.querySelector('.ant-layout-content')!;
    fromEvent(element, 'scroll')
      .pipe(debounceTime(50))
      .subscribe((e) => {
        this.showTitleBackgroud = element.scrollTop > 0;
      });
  }
}
