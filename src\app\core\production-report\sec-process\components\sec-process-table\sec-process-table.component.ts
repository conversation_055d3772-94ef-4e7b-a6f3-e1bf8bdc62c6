import { Component, Input, OnInit } from '@angular/core';
import { finalize } from 'rxjs';
import { DimensionRange, ReportRange } from '../../../production-report.enum';
import { DetailModalMode } from '../../models/sec-process.enum';
import { SecProcessService } from '../../sec-process.service';

interface TableConfigType {
  key: string;
  label: string;
  visible: boolean;
  type: string;
  width: string;
  sort: boolean;
  sortOrderBy: any;
  pinned: boolean;
  templateName?: string;
}
@Component({
  selector: 'app-sec-process-table',
  templateUrl: './sec-process-table.component.html',
  styleUrls: ['./sec-process-table.component.scss'],
})
export class SecProcessTableComponent implements OnInit {
  @Input() mode = DetailModalMode.PartColorSizeMode;
  @Input() tab!: number;
  @Input() dimension!: number;
  @Input() detailData: any;
  @Input() payload: any;
  @Input() version = 0;
  @Input() key = '';

  HeaderBaseConfig = { visible: true, type: 'text', width: '100px', sort: false, sortOrderBy: null, pinned: false };
  headerConfig: Array<TableConfigType> = [
    { key: 'origin_send_time', label: '外发日期', ...this.HeaderBaseConfig, type: 'dateMonth' },
    { key: 'receive_time', label: '外发厂实际收货', ...this.HeaderBaseConfig, width: '120px', type: 'dateMonth' },
    { key: 'outsource_qty', label: '外发数量', ...this.HeaderBaseConfig, type: 'quantity' },
    { key: 'send_time', label: '发货日期', ...this.HeaderBaseConfig, type: 'dateMonth' },
    { key: 'send_qty', label: '发货数量', ...this.HeaderBaseConfig, type: 'quantity' },
    { key: 'target_factory_name', label: '收货方', ...this.HeaderBaseConfig },
    { key: 'position_name', label: '部位', ...this.HeaderBaseConfig, type: 'template', templateName: 'position_name' },
    { key: 'qualified_qty', label: '合格/不良', ...this.HeaderBaseConfig, type: 'template', templateName: 'qualified_qty', width: '144px' },
    { key: 'color', label: '颜色/尺码', ...this.HeaderBaseConfig, type: 'template', templateName: 'color' },
    { key: 'total', label: '合计', ...this.HeaderBaseConfig, type: 'template', templateName: 'total' },
    { key: 'expected_back_time', label: '期望回厂', ...this.HeaderBaseConfig, type: 'template', templateName: 'expected_back_time' },
    { key: 'actual_back_time', label: '实际回厂', ...this.HeaderBaseConfig, type: 'template', templateName: 'actual_back_time' },
    { key: 'status', label: '状态', ...this.HeaderBaseConfig, type: 'template', templateName: 'status' },
  ];
  renderHeader!: Array<TableConfigType>;
  baseInfoConfig: Array<{ key: string; label: string; visible: boolean }> = [];
  tableConfig = {
    hasAction: false,
    dataList: [],
    loading: false,
    width: 800,
  };
  sizeList: Array<string> = [];
  detailInfo: any = null; // 详情数据
  detailList: Array<any> = [];
  statusValue: any = {
    1: '未逾期',
    2: '已逾期',
  };
  detailModalMode = DetailModalMode;
  loading = false;
  // 日报表下累计字段明细不需要展示日期(累计完成、累计收货、累计发货、累计合格和累计不合格)
  hideBizDateField = ['received_qty', 'finished_qty', 'delivered_qty', 'qualified_qty', 'defective_qty'];

  constructor(private _service: SecProcessService) {}

  ngOnInit(): void {
    this.getDetail();
    // 日报表下非累计字段明细 需要展示日期
    this.baseInfoConfig = [
      { key: 'biz_date', label: '日期', visible: this.tab === ReportRange.daily && !this.hideBizDateField.includes(this.key) },
      { key: 'io_code', label: '大货单号', visible: true },
      { key: 'po_code', label: '交付单', visible: this.dimension === DimensionRange.po },
      { key: 'extra_process_name', label: '工艺', visible: true },
      { key: 'factory_name', label: '外发厂', visible: true },
    ];
  }

  getDetail() {
    this.loading = true;
    this._service
      .getSecProcessDetail(this.payload)
      .pipe(
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe((res: any) => {
        if (res.code == 200) {
          this.loading = false;
          setTimeout(() => {
            this.initDetailData(res.data);
          }, 300);
        }
      });
  }

  initDetailData(data: any) {
    this.detailInfo = data;
    const list = data?.list?.length ? data?.list : [];
    if (this.mode === DetailModalMode.ColorSizeMode) {
      // 展示flc-color-size-table组件
      list.forEach((item: any) => {
        item.color_size_info = item.list[0]?.positions[0]?.po_lines;
      });
    } else {
      list?.forEach((item: any) => {
        item.tableConfig = {
          ...this.tableConfig,
          loading: false,
          dataList: item.list,
          version: this.version,
        };
      });
    }
    this.detailList = [...list];

    if (this.mode !== DetailModalMode.ColorSizeMode) {
      this.initHeader();
    }
  }

  initHeader() {
    let columns: string[] = [];
    if (this.mode === DetailModalMode.PartColorSizeMode) {
      columns = ['position_name', 'color', 'total'];
    }
    if (this.mode === DetailModalMode.OutsourceMode) {
      columns = [
        'origin_send_time',
        'receive_time',
        'outsource_qty',
        'position_name',
        'color',
        'total',
        'expected_back_time',
        'actual_back_time',
        'status',
      ];
    }
    if (this.mode === DetailModalMode.DeliveredMode) {
      columns = ['send_time', 'send_qty', 'target_factory_name', 'position_name', 'qualified_qty', 'color', 'total'];
    }
    this.getSizes();

    const tmpHeader = this.headerConfig.filter((item) => columns.includes(item.key));
    const index = tmpHeader.findIndex((item) => item.key === 'color');
    const sizeHeader = this.sizeList.map((size: any) => {
      return {
        key: size,
        label: size,
        ...this.HeaderBaseConfig,
        type: 'template',
        templateName: size,
      };
    });
    tmpHeader.splice(index + 1, 0, ...sizeHeader);
    this.renderHeader = tmpHeader;
    this.handleDatalist();
  }

  getSizes() {
    this.sizeList = [];
    this.detailList?.forEach((table: any) => {
      table?.tableConfig?.dataList?.forEach((item: any) => {
        item.positions.forEach((pos: any) => {
          pos.po_lines.forEach((line: any) => {
            this.sizeList.push(line.size_info.spec_size);
          });
        });
      });
    });
    this.sizeList = Array.from(new Set(this.sizeList));
  }

  handleDatalist() {
    this.detailList?.forEach((table: any) => {
      table?.tableConfig?.dataList?.forEach((item: any) => {
        item.positions.forEach((pos: any) => {
          const color_size_info: any = [];
          const total_info: any = {};
          pos.po_lines.forEach((line: any) => {
            const index = color_size_info.findIndex((val: any) => val.color_name === line.color_info.color_name);
            if (index > -1) {
              color_size_info[index][line.size_info.spec_size] = line.qty;
              color_size_info[index].total += line.qty;
            } else {
              color_size_info.push({
                color_name: line.color_info.color_name,
                [line.size_info.spec_size]: line.qty,
                total: line.qty,
              });
            }
            this.sizeList.forEach((size: any) => {
              if (line.size_info.spec_size === size) {
                total_info[size] = total_info[size] ? total_info[size] + line.qty : line.qty;
              }
            });
            total_info.total = total_info.total ? total_info.total + line.qty : line.qty;
          });
          pos.color_size_info = [...color_size_info, total_info];
        });
      });
    });
  }
}
