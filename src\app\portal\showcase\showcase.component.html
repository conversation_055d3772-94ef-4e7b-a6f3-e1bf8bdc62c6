<div>
  <p>showcase {{ 'showcase' | translate }}</p>

  <div>
    <b>Localization 示例</b>
  </div>
  <div>多层级 subGroup.error => {{ 'subGroup.error' | translate }}</div>
  <div>带参数 withParam => {{ 'withParam' | translate: { value: 'world' } }}</div>
  <div>
    使用directive方式 withParam, using directive way => <span [translate]="'withParam'" [translateParams]="{ value: 'world' }"></span>
  </div>

  <br />

  <br />
  <p><b>按钮示例</b></p>
  <p>nz-zorro-buttons:</p>
  <p>
    <button nz-button nzType="primary">NZ Primary Button</button>
    <button nz-button nzType="default">NZ Default Button</button>
    <button nz-button nzType="dashed">NZ Dashed Button</button>
    <button nz-button nzType="text">NZ Text Button</button>
    <a nz-button nzType="link">NZ Link Button</a>
  </p>
  <p>
    <button nz-button nzType="primary" nzShape="round">NZ Primary Button</button>
    <button nz-button nzType="default" nzShape="round" class="ant-btn-default">NZ Default Button</button>
    <button nz-button nzType="dashed" nzShape="round">NZ Dashed Button</button>
  </p>
  <p>
    <button nz-button nzType="primary" disabled>NZ Primary Button</button>
    <button nz-button nzType="default" disabled>NZ Default Button</button>
    <button nz-button nzType="dashed" disabled>NZ Dashed Button</button>
    <button nz-button nzType="text" disabled>NZ Text Button</button>
    <a nz-button nzType="link" disabled>NZ Link Button</a>
  </p>
  <p>
    <button nz-button nzType="primary" nzDanger>NZ Primary Danger</button>
    <button nz-button nzType="default" nzDanger>NZDefault</button>
    <button nz-button nzType="dashed" nzDanger>NZ Dashed</button>
    <button nz-button nzType="text" nzDanger>NZ Text</button>
    <a nz-button nzType="link" nzDanger>NZ Link</a>
  </p>
  <p>fl-ui buttons:</p>
  <p>
    <button nz-button flButton="minor">FL minor</button>
    <button nz-button flButton="default">FL default</button>
    <button nz-button flButton="dashed">FL dashed</button>
    <button nz-button flButton="fault">FL fault</button>
  </p>
  <p>
    <button nz-button flButton="minor" nzShape="round">FL minor</button>
    <button nz-button flButton="default" nzShape="round">FL default</button>
    <button nz-button flButton="dashed" nzShape="round">FL dashed</button>
    <button nz-button flButton="fault" nzShape="round">FL fault</button>
  </p>
  <p>
    <button nz-button flButton="minor" disabled>FL minor</button>
    <button nz-button flButton="default" disabled>FL default</button>
    <button nz-button flButton="dashed" disabled>FL dashed</button>
    <button nz-button flButton="fault" disabled>FL fault</button>
  </p>

  <button nz-button flButton="pretty-primary">FL pretty-primary</button>
  <button nz-button flButton="pretty-minor" nzShape="round">FL pretty-minor</button>

  <br />
  <br />

  <nz-table #basicTable [nzData]="listOfData" [nzScroll]="{ x: '1000px', y: '200px' }">
    <thead>
      <tr>
        <th nzLeft nzWidth="100px">pre1</th>
        <th nzLeft nzWidth="100px">pre2</th>
        <ng-container *ngFor="let col of cols">
          <th
            *ngIf="col.width"
            [nzWidth]="col.width"
            nz-resizable
            nzBounds="window"
            nzPreview
            [nzMaxWidth]="400"
            [nzMinWidth]="60"
            (nzResizeEnd)="onResize($event, col.title)">
            {{ col.title }}
            <nz-resize-handle nzDirection="right">
              <div class="resize-trigger"></div>
            </nz-resize-handle>
          </th>
          <th *ngIf="!col.width">
            {{ col.title }}
          </th>
        </ng-container>
        <th nzRight nzWidth="100px">extra</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of basicTable.data">
        <td nzLeft>- pre1 -</td>
        <td nzLeft>- pre2 -</td>
        <td>{{ data.name }}</td>
        <td>{{ data.age }}</td>
        <td>{{ data.address0 }}</td>
        <td>{{ data.address1 }}</td>
        <td>{{ data.address2 }}</td>
        <td>{{ data.address3 }}</td>
        <td>-</td>
        <td nzRight>-extra-</td>
      </tr>
    </tbody>
  </nz-table>

  <br />
  <br />
  <app-search-container [headerTitle]="'工厂列表'" [btnTpl]="btnTpl">
    <div>工厂名称：<input /></div>
    <div>工厂编码：<input /></div>
    <div>工厂状态：<input /></div>
    <div>工厂状态：<input /></div>
    <div>
      工厂模式：<nz-select ngModel="jack">
        <nz-option nzValue="jack" nzLabel="Jack"></nz-option>
        <nz-option nzValue="lucy" nzLabel="Lucy11111111111111111111111111111111111111"></nz-option>
      </nz-select>
    </div>
  </app-search-container>
  <ng-template #btnTpl>
    <button nz-button flButton="pretty-primary" nzShape="round">
      <i nz-icon [nzIconfont]="'icon-xinjian1'"></i>
      {{ 'btn.add' | translate }}
    </button>
  </ng-template>

  <br />
  <br />
  <nz-steps nzType="navigation">
    <nz-step nzTitle="Step 1" nzSubtitle="00:00:05" nzStatus="finish" nzDescription="This is a description."></nz-step>
    <nz-step nzTitle="Step 2" nzStatus="process" [nzIcon]="iconTemplate"></nz-step>
    <ng-template #iconTemplate>
      <i nz-icon [nzIconfont]="'icon-daibushu'" style="font-size: 32px"></i>
    </ng-template>
    <nz-step nzTitle="Step 3" nzStatus="wait"></nz-step>
  </nz-steps>
</div>
