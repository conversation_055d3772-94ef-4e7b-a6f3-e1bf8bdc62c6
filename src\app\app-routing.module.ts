import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AppResolverService } from './app.resolver';
import { NotFoundPageComponent } from './portal/not-found-page/not-found-page.component';
import { ShowcaseComponent } from './portal/showcase/showcase.component';
import { AuthGuard } from './shared/auth.guard';

const routes: Routes = [
  {
    path: '',
    resolve: {
      initData: AppResolverService,
    },
    canActivate: [AuthGuard],
    loadChildren: () => import('./core/main/main.module').then((m) => m.MainModule),
  },
  {
    path: 'login',
    resolve: {
      initData: AppResolverService,
    },
    loadChildren: () => import('./portal/login/login.module').then((m) => m.LoginModule),
  },
  {
    //供应商申请入驻
    path: 'supplier-sourcing',
    data: {
      noKeepAlive: true,
    },
    resolve: {
      initData: AppResolverService,
    },
    loadChildren: () => import('fl-sewsmart-lib/supplier-admission').then((m) => m.SupplierAdmissionModule),
  },
  {
    path: 'showcase',
    data: {
      noKeepAlive: true,
    },
    loadChildren: () => import('./portal/showcase/showcase.module').then((m) => m.ShowcaseModule),
  },
  {
    path: 'showcase2',
    data: {
      noKeepAlive: true,
    },
    component: ShowcaseComponent,
  },
  {
    path: 'not-found',
    component: NotFoundPageComponent,
    data: {
      noKeepAlive: true,
    },
  },
  {
    path: '**',
    component: NotFoundPageComponent,
    data: {
      noKeepAlive: true,
    },
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule {}
