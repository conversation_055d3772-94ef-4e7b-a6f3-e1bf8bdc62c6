import { Component, ElementRef, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { PlanGraphItem, PlanItem } from '../production-plan';

@Component({
  selector: 'app-plan-graph-item',
  templateUrl: './plan-graph-item.component.html',
  styleUrls: ['./plan-graph-item.component.scss'],
})
export class PlanGraphItemComponent implements OnInit {
  @Input() data!: PlanGraphItem;
  @Input() options!: {
    signalWidth: number;
    dayWidth: number; // 日维度一天48，小时维度一天24*12
    wrapBodyWidth: number;
    dimension: string;
    per1Px: number;
  };
  @Input() lineIndex!: number;
  @Input() exitClickable!: boolean;
  @Output() tapGraphItem = new EventEmitter<PlanItem>();
  @Output() hoverGraphItem = new EventEmitter<PlanItem>();
  @Output() leaveGraphItem = new EventEmitter();

  get backGroundColor() {
    if (this.data?.isSelected) {
      return '#EEF7FF';
    }
    return this.data?.backgroundColor ? this.data.backgroundColor : '#ffe3cb';
  }

  get isClickable(): boolean {
    return this.exitClickable ?? false;
  }

  constructor() {}

  ngOnInit(): void {}

  /** 鼠标移出进度条 */
  leaveGraph(e: Event) {
    e.preventDefault();
    e.stopPropagation();
    if (!this.exitClickable && this.data.plan_type !== 3) {
      this.leaveGraphItem.emit();
    }
  }

  onMouseEnter(e: MouseEvent) {
    e.preventDefault();
    e.stopPropagation();
    if (!this.exitClickable && this.data.plan_type !== 3) {
      this.hoverGraphItem.emit({ ...this.data, lineIndex: this.lineIndex });
    }
  }

  tapGraph(e: MouseEvent, ref?: HTMLElement | ElementRef) {
    if (e.button !== 0) {
      return;
    }
    e.preventDefault();
    e.stopPropagation();
    const is_selected = this.data.is_selected;
    this.data.clickable = !is_selected;
    this.data.setSelected(!is_selected);
    this.tapGraphItem.emit({ ...this.data, lineIndex: this.lineIndex });
  }

  /**
   * 将输入色与灰色（128，128，128）进行对比
   * 当color值大于128时,color值偏向255,即#ffffff,此时字体颜色应为#000000
   * 当color值小于128时,color值偏向0,即#000000,此时字体颜色应为#ffffff
   * @param colorStr 输入颜色
   * @returns 返回的反差色（黑或白）
   */
  getContrastColor(colorStr: string) {
    if (!colorStr) {
      return '#000000';
    }
    // 将十六进制颜色码转为rgb颜色值
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(colorStr);
    const rgbArr = result ? [parseInt(result[1], 16), parseInt(result[2], 16), parseInt(result[3], 16)] : null;

    const color: boolean = rgbArr ? 0.213 * rgbArr[0] + 0.715 * rgbArr[1] + 0.072 * rgbArr[2] > 255 / 2 : true;
    return color ? '#000000' : '#ffffff';
  }
}
