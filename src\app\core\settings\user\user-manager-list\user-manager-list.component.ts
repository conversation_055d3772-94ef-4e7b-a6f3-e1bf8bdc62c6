import {
  ChangeDetector<PERSON><PERSON>,
  Component,
  <PERSON>ement<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  QueryList,
  TemplateRef,
  ViewChild,
  ViewChildren,
} from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { format } from 'date-fns';
import { FlcRouterEventBusService, resizable } from 'fl-common-lib';
import { isNotNil } from 'ng-zorro-antd/core/util';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzResizeEvent } from 'ng-zorro-antd/resizable';
import { Subject, Subscription } from 'rxjs';
import { debounceTime, finalize } from 'rxjs/operators';
import { Resizable } from 'src/app/decorator/resizable';
import { TableHeaderConfig, TableHelperService } from 'src/app/services/table-helper/table-helper.service';
import { AppStorageService } from 'src/app/shared/app-storage.service';
import { UserService } from '../use.service';
import { SearchContainerComponent } from 'src/app/components/search-container/search-container.component';

const version = '1.0.1';
type lineItem = TableHeaderConfig<any>;
const defaultHeaders: lineItem[] = [
  { label: '用户编码', key: 'login_name', visible: true, width: '183px', type: 'text', disable: false, resizeble: true, pinned: false },
  { label: '用户名称', key: 'name', visible: true, width: '182px', type: 'text', disable: false, resizeble: true, pinned: false },
  {
    label: '权限名称',
    key: 'roles',
    visible: true,
    width: 'auto',
    type: 'template',
    disable: false,
    resizeble: true,
    template: 'roles',
    pinned: false,
  },
  { label: '状态', key: 'status', visible: true, width: '100px', type: 'text', disable: false, resizeble: true, pinned: false },
  { label: '创建人', key: 'gen_user', visible: true, width: '141px', type: 'text', disable: false, resizeble: true, pinned: false },
  {
    label: '创建日期',
    key: 'gen_time',
    visible: true,
    width: '164px',
    type: 'datetime',
    disable: false,
    resizeble: true,
    pinned: false,
  },
  {
    label: '最新修改时间',
    key: 'modified_time',
    visible: true,
    width: '164px',
    type: 'datetime',
    disable: false,
    resizeble: true,
    pinned: false,
  },
  {
    label: '最近登录时间',
    key: 'last_login_time',
    visible: true,
    width: '164px',
    type: 'datetime',
    disable: false,
    resizeble: true,
    pinned: false,
  },
];
@Component({
  selector: 'app-user-manager-list',
  templateUrl: './user-manager-list.component.html',
  styleUrls: ['./user-manager-list.component.scss'],
})
@resizable()
export class UserManagerListComponent extends Resizable implements OnInit, OnDestroy {
  routeListerner?: any;
  @ViewChild('header') hearderComponent?: SearchContainerComponent;
  tableHeight = 0;
  tableData: any; // 表格数据
  openOption = false; // 下拉框是否打开
  changeSelect = false; // 输入框的纸是否改变
  searchList: any = [
    {
      label: '用户编码',
      code: 'login_name',
    },
    {
      label: '用户名称',
      code: 'name',
    },
    {
      label: '权限名称',
      code: 'roles',
    },
    {
      label: '状态',
      code: 'status',
      options: [
        { label: '启用', value: 1 },
        { label: '停用', value: 0 },
      ],
    },
    {
      label: '创建人',
      code: 'gen_user',
    },
  ];
  gen_time: 'desc' | 'asc' | null = null; // 创建日期排序
  modified_time: 'desc' | 'asc' | null = null; // 最近修改日期排序
  last_login_time: 'desc' | 'asc' | null = null; // 最近登录日期排序
  searchData: any = {
    login_name: null,
    name: null,
    roles: null,
    status: null,
    gen_user: null,
    gen_time: [],
  };
  orderBy: any = [];
  @ViewChildren('roles') roles!: QueryList<TemplateRef<HTMLElement>>; // 权限名称
  id: number | undefined; // 点击去详情的id
  loading = false;
  total = 0;
  pageIndex = 1;
  pageSize = 20;
  headers: lineItem[] = [];
  renderHeaders: lineItem[] = [];
  btnHighLight = false;
  btnArr: string[] = [];
  subject$ = new Subject<boolean>();
  subscription?: Subscription;

  translateName = 'user.';

  constructor(
    private _tableHelper: TableHelperService,
    private _service: UserService,
    private _router: Router,
    private cdr: ChangeDetectorRef,
    private _translate: TranslateService,
    private _routerEventBus: FlcRouterEventBusService,
    public _storage: AppStorageService,
    private _notice: NzNotificationService
  ) {
    super();
  }

  getRenderHeaders() {
    this.renderHeaders = this._tableHelper.getRenderHeader<lineItem>(this.headers);
    this.resize();
  }
  ngOnInit(): void {
    super.ngOnInit();
    this.addRouterListener();
    this.headers = this._tableHelper.getTableHeaderConfig<lineItem>(defaultHeaders, version);
    this.getRenderHeaders();
    this.btnArr = this._storage.getUserActions('settings/user');
    this.subscription = this.subject$.pipe(debounceTime(500)).subscribe((res) => {
      this.getUserList(res);
    });
    this.subject$.next(false);
    (this as any).addResizePageListener();
  }

  ngAfterViewInit() {
    this.resize();
    this.cdr.detectChanges();
  }

  ngOnDestroy() {
    (this as any).removeResizePageListener();
    this.subscription?.unsubscribe();
  }

  addRouterListener() {
    this.routeListerner = this._routerEventBus.onRouterActivationEnd('/settings/user/list', () => {
      if (this._service.detailUpdate) {
        this.btnArr = this._storage.getUserActions('settings/user');
        console.log('addRouterListeneraddRouterListener');
        this.subject$.next(false);
      }
    });
  }
  /**
   * 下拉选择搜索
   */
  onSearch() {
    this.pageIndex = 1;
    this.subject$.next(false);
  }
  resize() {
    setTimeout(() => {
      const headerHeight = this.hearderComponent?.headerBox?.nativeElement?.offsetHeight ?? 32;
      this.tableHeight = window.innerHeight - headerHeight - 158;
      //table最小高度
      if (this.tableHeight < 200) {
        this.tableHeight = 200;
      }
    });
  }
  /**
   * 日期选择
   * @param  {any} e
   */
  onChange() {
    this.pageIndex = 1;
    this.subject$.next(false);
  }
  /**
   * 重置
   */
  reset() {
    this.searchData = {
      login_name: null,
      name: null,
      roles: null,
      status: null,
      gen_user: null,
      gen_time: null,
    };
    this.subject$.next(true);
  }

  /**
   * 切页码
   */
  onChangePageIndex() {
    this.subject$.next(false);
  }

  /**
   * 排序
   * @param  {any} e
   */
  sortOrderChange(e: any, item: any) {
    this.gen_time = item === 'gen_time' ? e : null;
    this.modified_time = item === 'modified_time' ? e : null;
    this.last_login_time = item === 'last_login_time' ? e : null;
    this.orderBy = [];
    if (e) {
      this.orderBy.push(item + ' ' + e);
    }
    this.subject$.next(false);
  }
  /**
   * 获取列表数据
   * @param  {} reset=false
   */
  getUserList(reset = false) {
    this.loading = true;
    this.pageIndex = reset ? 1 : this.pageIndex;
    this.orderBy = reset ? [] : this.orderBy;
    const where = this.handleWhere(reset) ?? {};
    this._service
      .getUserList({
        where: where,
        orderBy: this.orderBy,
        page: this.pageIndex,
        limit: this.pageSize,
      })
      .subscribe((res) => {
        if (res.code === 200) {
          this.loading = false;
          this.tableData = res.data.data;
          this.total = res.data.len;
        }
      });
  }
  /**
   * 获取搜索项
   * @param  {} reset=false
   * @returns object
   */
  handleWhere(reset = false): object {
    const where: { [key: string]: any } = {};
    if (!reset) {
      Object.entries(this.searchData).forEach((item: any) => {
        if (isNotNil(item[1])) {
          if (item[0] === 'roles') {
            if (item[1].length) {
              where[item[0]] = {
                op: 'in',
                value: item[1],
              };
            }
          } else if (item[0] === 'gen_time') {
            if (item[1].length) {
              const startTime = format(item[1][0], 'yyyy-MM-dd');
              const endTime = format(item[1][1], 'yyyy-MM-dd');
              where[item[0]] = {
                op: 'between',
                value: [startTime, endTime],
              };
            }
          } else {
            item[0] = item[0] === 'gen_user' ? 'gen_user_id' : item[0];
            where[item[0]] = {
              op: '=',
              value: item[1],
            };
          }
        }
      });
    }
    return where;
  }

  onResize({ width }: NzResizeEvent, col: string): void {
    this.headers = this._tableHelper.tableResize<lineItem>(width, col, this.headers, version);
    this.getRenderHeaders();
  }
  /**
   * 自定义表头
   * @param  {MouseEvent} event
   * @returns void
   */
  changeHeader(event: MouseEvent): void {
    this.btnHighLight = true;
    const shadow = JSON.parse(JSON.stringify(this.headers));
    for (const item of shadow) {
      this._translate
        .get(this.translateName + item.label)
        .subscribe((res: string) => {
          item.label = res;
        })
        .unsubscribe();
    }
    this._tableHelper
      .openTableHeaderMidifyDialog<lineItem>(shadow, event.target as HTMLElement)
      .pipe(finalize(() => (this.btnHighLight = false)))
      .subscribe((res) => {
        if (res) {
          for (const item of res) {
            item.label = defaultHeaders.find((i) => i.key === item.key)?.label ?? '';
          }
          this.headers = res;
          this.getRenderHeaders();
          this._tableHelper.saveTableHeaderConfig(this.headers, version);
        }
      });
  }
  getTemplate(template: string | undefined, index: number): TemplateRef<HTMLElement> | null {
    let returnTemplateRef: TemplateRef<HTMLElement> | null = null;
    switch (template) {
      case 'roles':
        returnTemplateRef = this.roles.toArray()[index];
        break;
      default:
        break;
    }
    return returnTemplateRef;
  }
  /**
   * 详情跳转
   */
  detail(id: any): void {
    this.id = id;
    this._router.navigate(['/settings/user/list', id]);
  }
}
