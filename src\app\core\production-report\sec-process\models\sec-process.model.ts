import { ReportRange } from '../../production-report.enum';

export function renderModalTitle(tab: number, key: string): string {
  const transferKeyName: { [key: string]: string } = {
    order_count: '分配订单数',
    daily_cutting_qty: '日裁数',
    cutting_qty: '已裁数（裁片）',
    received_qty: tab === ReportRange.daily ? '累计收货' : '收货数',
    transit_qty: '在途数',
    finished_qty: tab === ReportRange.daily ? '累计完成' : '完成数',
    delivered_qty: tab === ReportRange.daily ? '累计发货' : '已发货',
    outsource_qty: '外发数',
    daily_delivered_qty: '日发货',
    daily_finished_qty: '日完成',
    daily_received_qty: '日收货（裁片）',
    daily_qualified_qty: '日合格数',
    qualified_qty: tab === ReportRange.daily ? '累计合格数' : '合格数',
    daily_defective_qty: '日不良数',
    defective_qty: tab === ReportRange.daily ? '累计不良数' : '不良数',
  };
  return transferKeyName[key];
}
