<div class="work-frequency-container">
  <div class="work-frequency-header">
    <div>{{ 'work-fre.请从下方选择' | translate }}</div>
    <div>
      <label nz-checkbox [(ngModel)]="allChecked" (ngModelChange)="updateAllChecked()" [nzIndeterminate]="indeterminate">
        {{ 'work-fre.全选' | translate }}
      </label>
    </div>
  </div>
  <nz-divider nzType="horizontal" nzDashed></nz-divider>
  <div class="work-frequency-body">
    <nz-checkbox-wrapper (nzOnChange)="onCheckChange($event)" (ngModelChange)="onCheckChange($event)">
      <ng-container *ngFor="let item of checkList">
        <label nz-checkbox [nzValue]="item.value" [(ngModel)]="item.checked" (nzBlur)="onTouch()">
          <div class="checkbox-btn">{{ type === FrequencyTypeEnum.week ? ('work-fre.' + item.label | translate) : item.label }}</div>
        </label>
      </ng-container>
    </nz-checkbox-wrapper>
  </div>
</div>
