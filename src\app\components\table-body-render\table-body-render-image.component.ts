import { Component, Input, OnInit } from '@angular/core';
import { NzImageService } from 'ng-zorro-antd/image';
@Component({
  selector: 'component-table-body-render-image',
  template: ` <div class="wrap">
    <div class="cornersign">
      <img [width]="48" [src]="url" (click)="previewImage()" alt="" srcset="" />
      <span *ngIf="itemCount > 1">{{ itemCount }}</span>
    </div>
  </div>`,
  styles: [
    `
      .wrap {
        display: flex;
        justify-content: center;
      }
      .cornersign {
        position: relative;
        overflow: hidden;
        width: 48px;
        img {
          border-radius: 4px;
        }
        span {
          padding: 0 2px;
          position: absolute;
          color: #fff;
          right: 0px;
          bottom: -2px;
          background: rgba(38, 46, 71, 0.5);
          min-width: 15px;
          font-size: 12px;
        }
      }
    `,
  ],
})
export class TableBodyRenderImageComponent implements OnInit {
  @Input('data') inputData?: any;
  @Input() width = 100;
  data: {
    name: string;
    url: string;
  }[] = [];
  url = '';
  itemCount = 0;
  constructor(private nzImageService: NzImageService) {}

  ngOnInit(): void {
    if (this.inputData && Array.isArray(this.inputData) && this.inputData.length > 0) {
      this.data = this.inputData;
      this.url = this.data[0].url;
      this.itemCount = this.data.length;
    }
  }
  previewImage() {
    const list = (this.data ?? []).map((item) => ({
      src: item.url,
    }));
    this.nzImageService.preview(list);
  }
}
