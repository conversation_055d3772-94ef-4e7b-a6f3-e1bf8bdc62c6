import { Component, OnInit, Input } from '@angular/core';
import { groupBy } from 'lodash';
import { FlcDrawerHelperService } from 'fl-common-lib';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzDrawerRef } from 'ng-zorro-antd/drawer';

@Component({
  selector: 'app-permission-setting',
  templateUrl: './permission-setting.component.html',
  styleUrls: ['./permission-setting.component.scss'],
})
export class PermissionSettingComponent implements OnInit {
  @Input() set moduleList(list: any[]) {
    this.initValue(list);
    this._cache_str = JSON.stringify(list);
    this._moduleList = list;
  }
  _cache_str = '';
  _moduleList: any[] = [];
  isSelectedAll = false;
  indeterminate = false;
  constructor(private _drawer: NzDrawerRef, private modal: NzModalService) {}

  ngOnInit() {}

  get validModuleList() {
    return this._moduleList.filter((item: any) => item.value);
  }

  initValue(moduleList: any[]) {
    moduleList.forEach((item: any) => {
      item.value = item.permission !== 3;
      item.module_action_list.forEach((item: any) => {
        item.value = item.permission !== 3;
      });
      // 模块字段类型排序, 依次面料,辅料, 无, 以及后续其他新增类型
      // field_type 模块字段类型： 1=面料，2,=辅料，3=无
      item.fields_list = this._jsonToSortedArray(groupBy(item.module_field_list, 'field_type')).map((list) => {
        const labelMaps = { 1: '面料全选', 2: '辅料全选', 3: '全选' };
        list.forEach((item) => {
          item.value = item.permission !== 3;
        });
        const fields = {
          allLable: labelMaps?.[list?.[0]?.field_type as keyof typeof labelMaps] || '全选',
          isSelectAll: true,
          indeterminate: true,
          list,
        };
        this.fieldChecked(fields);
        return fields;
      });
    });
    // 对模块排序, 依次是编辑,只读, 未勾选
    // 权限： 1=编辑，2=只读，3=未勾选
    moduleList.sort((a, b) => a.permission - b.permission);
    this.singleChecked(moduleList);
  }

  _jsonToSortedArray(json: Record<string, any[]>) {
    return Object.keys(json)
      .map(Number) // 将字符串键转为数字
      .sort((a, b) => a - b) // 按数字大小排序
      .map((key) => json[key]); // 按排序后的键提取值
  }

  onUpdateAllChecked() {
    this.indeterminate = false;
    this._moduleList.forEach((item: any) => {
      item.value = this.isSelectedAll;
      item.permission = this.isSelectedAll ? 1 : 3;
    });
  }

  onActionChanged(event: boolean, btn: any) {
    btn.permission = event ? 1 : 3;
    btn.value = btn.permission !== 3;
  }

  onCheckboxChanged(event: boolean, btn: any) {
    btn.permission = event ? 1 : 3;
    btn.value = btn.permission !== 3;

    this.singleChecked(this._moduleList);
  }

  singleChecked(moduleList: any[]): void {
    if (moduleList.every((item: any) => !item.value)) {
      this.isSelectedAll = false;
      this.indeterminate = false;
    } else if (moduleList.every((item: any) => item.value)) {
      this.isSelectedAll = true;
      this.indeterminate = false;
    } else {
      this.isSelectedAll = false;
      this.indeterminate = true;
    }
  }

  onToggleModule(btn: any, parent: any) {
    if (parent) {
      // 只有父级是可编辑状态时, 才可以切换只读
      if (parent.permission === 1) {
        btn.permission = btn.permission === 1 ? 2 : 1;
      } else {
        btn.permission = 2;
      }
      return;
    }

    btn.permission = btn.permission === 1 ? 2 : 1;
    this.updateChildren(btn);
  }

  updateChildren(btn: any) {
    if (btn.permission === 1) {
      // 已选中字段, 都切换为编辑权限
      btn.module_field_list.forEach((item: any) => {
        if (item.permission !== 3) {
          item.permission = 1;
        }
      });

      // 操作权限都切换为选中
      btn.module_action_list.forEach((item: any) => {
        item.permission = 1;
        item.value = true;
      });
    } else {
      // 已选中字段, 都切换为只读权限
      btn.module_field_list.forEach((item: any) => {
        if (item.permission !== 3) {
          item.permission = 2;
        }
      });

      // 操作权限都切换为不选中
      btn.module_action_list.forEach((item: any) => {
        item.permission = 3;
        item.value = false;
      });
    }
  }

  updateFieldAllChecked(fileds: any, module: any) {
    fileds.indeterminate = false;
    fileds.list.forEach((item: any) => {
      // 模块只读, 则字段切换为选中并只读. 若模块可编辑, 则字段切换为选中且可编辑
      item.value = fileds.isSelectAll;
      const permission = module.permission === 1 ? 1 : 2;
      item.permission = fileds.isSelectAll ? permission : 3;
    });
  }

  fieldCheckboxChanged(event: boolean, btn: any, fileds: any, parent: any) {
    const permission = parent.permission === 1 ? 1 : 2;
    btn.permission = event ? permission : 3;
    btn.value = btn.permission !== 3;
    this.fieldChecked(fileds);
  }

  fieldChecked(fileds: any): void {
    if (fileds.list.every((it: any) => !it.value)) {
      fileds.isSelectAll = false;
      fileds.indeterminate = false;
    } else if (fileds.list.every((it: any) => it.value)) {
      fileds.isSelectAll = true;
      fileds.indeterminate = false;
    } else {
      fileds.isSelectAll = false;
      fileds.indeterminate = true;
    }
  }

  hasChanged() {
    const current_str = JSON.stringify(this._moduleList);
    return current_str !== this._cache_str;
  }
  onClose() {
    if (this.hasChanged()) {
      this.modal.confirm({
        nzTitle: '当前编辑未保存,确定离开吗？',
        nzOnOk: () => {
          this._drawer.close();
        },
      });
    } else {
      this._drawer.close();
    }
  }

  onConfirm() {
    this._drawer.close(this._moduleList);
  }
}
