import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SettingsRoutingModule } from './settings-routing.module';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { NzIconModule, NzIconService } from 'ng-zorro-antd/icon';
import { PipesModule } from 'src/app/pipes/pipes.module';
import { FlUiAngularModule } from 'fl-ui-angular';
import { ComponentsModule } from 'src/app/components/components.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzImageModule } from 'ng-zorro-antd/image';
import { NzResizableModule } from 'ng-zorro-antd/resizable';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzFormModule } from 'ng-zorro-antd/form';
import { UserModule } from './user/user.module';
import { UserRoutingModule } from './user/user-routing.module';
import { RoleManagerComponent } from './role-manager/role-manager.component';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzMessageModule } from 'ng-zorro-antd/message';
import { NzNotificationServiceModule } from 'ng-zorro-antd/notification';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzSpaceModule } from 'ng-zorro-antd/space';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';
// import { DragDropModule } from '@angular/cdk/drag-drop';

import { RoleLineComponent } from './role-manager/role-line/role-line.component';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { RoleLineLastMenuLineComponent } from './role-manager/role-line/role-line-last-menu-line/role-line-last-menu-line.component';
import { RoleLineLastMenuRangeLineComponent } from './role-manager/role-line/role-line-last-menu-range-line/role-line-last-menu-range-line.component';
import { DirectivesModule } from 'src/app/directive/directives.module';
import { AssignRoleComponent } from './assign-role/assign-role.component';
import { AssignRoleDetailComponent } from './assign-role/assign-role-detail/assign-role-detail.component';
import { AssignRoleDetailLineComponent } from './assign-role/assign-role-detail/assign-role-detail-line/assign-role-detail-line.component';
import { SortablejsModule } from 'ngx-sortablejs';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import { PermissionSettingComponent } from './role-manager/role-line/permission-setting/permission-setting.component';
import { NzDrawerService } from 'ng-zorro-antd/drawer';
import { FlcDrawerHelperService } from 'fl-common-lib';

const nzModules = [
  NzFormModule,
  NzButtonModule,
  NzSelectModule,
  NzIconModule,
  NzTableModule,
  NzResizableModule,
  NzImageModule,
  NzToolTipModule,
  NzDatePickerModule,
  NzFormModule,
  NzInputModule,
  NzSwitchModule,
  NzMessageModule,
  NzNotificationServiceModule,
  NzSpinModule,
  NzSpaceModule,
  NzDividerModule,
  NzRadioModule,
  NzCheckboxModule,
  NzAutocompleteModule,
  NzPopconfirmModule,
];
@NgModule({
  declarations: [
    RoleManagerComponent,
    RoleLineComponent,
    RoleLineLastMenuLineComponent,
    RoleLineLastMenuRangeLineComponent,
    AssignRoleComponent,
    AssignRoleDetailComponent,
    AssignRoleDetailLineComponent,
    PermissionSettingComponent,
  ],
  imports: [
    CommonModule,
    SettingsRoutingModule,
    ReactiveFormsModule,
    FormsModule,
    ComponentsModule,
    FlUiAngularModule,
    PipesModule,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new MultiTranslateHttpLoader(http, [
            { prefix: './assets/i18n/settings/', suffix: '.json' },
            { prefix: './assets/i18n/settings/role-manager/', suffix: '.json' },
            { prefix: './assets/i18n/settings/assign-role/', suffix: '.json' },
          ]);
        },
        deps: [HttpClient],
      },
    }),
    DirectivesModule,
    // DragDropModule,
    SortablejsModule,
    ...nzModules,
  ],
  providers: [NzDrawerService, FlcDrawerHelperService],
})
export class SettingsModule {
  constructor(public translateService: TranslateService, private iconService: NzIconService) {
    // 切换lang必须刷新页面，模块级别i18n文件才能成功加载
    const dl = translateService.defaultLang;
    translateService.defaultLang = '';
    translateService.setDefaultLang(dl);

    this.iconService.fetchFromIconfont({
      scriptUrl: 'https://at.alicdn.com/t/font_2782026_gkrcufahyqc.js',
    });
  }
}
