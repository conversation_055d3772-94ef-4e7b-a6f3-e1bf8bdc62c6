import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { FlcLeaveGuard } from 'fl-common-lib';
import { EmployeeListComponent } from './employee/list/employee-list.component';
import { OrganizationComponent } from './organization/organization.component'; // 员工档案列表
const routes: Routes = [
  {
    path: 'org',
    component: OrganizationComponent,
    canDeactivate: [FlcLeaveGuard],
  },
  {
    path: 'employee',
    component: EmployeeListComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class HrRoutingModule {}
