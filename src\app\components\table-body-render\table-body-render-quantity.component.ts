import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'component-table-body-render-quantity',
  template: ' {{ (data | number: "1.0-0")|noValue }} ',
  styles: [],
})
export class TableBodyRenderQuantityComponent implements OnInit {
  @Input('data') inputData?: any;
  data?: number;
  constructor() {}

  ngOnInit(): void {
    if (this.inputData && typeof this.inputData === 'number') {
      this.data = this.inputData;
    }
  }
}
