import { FlcTableHeaderConfig } from 'fl-common-lib';
import { AllocationListItem } from '../../model/production-plan.interface';

export const allocationListHeader: Partial<FlcTableHeaderConfig<AllocationListItem>>[] = [
  {
    label: '大货单号',
    key: 'order_code',
    visible: true,
    width: '150px',
    type: 'template',
    templateName: 'order_code',
    isHidePin: true,
    pinned: true,
    disable: false,
  },
  { label: '款式编码', key: 'style_code', visible: true, width: '120px', type: 'text', pinned: false, disable: false },
  { label: '品名', key: 'category', visible: true, width: '100', type: 'text', pinned: false, disable: false },
  {
    label: '款式分类',
    key: 'first_style_class',
    visible: true,
    width: '205px',
    type: 'text',
    pinned: false,
    disable: false,
    formatter: (item: AllocationListItem) => {
      if (!item.first_style_class) return '';
      return item.first_style_class + '-' + item.second_style_class + '-' + item.third_style_class;
    },
  },
  { label: '款式图片', key: 'style_pic_list', visible: true, width: '100px', type: 'image', pinned: false, disable: false },
  { label: '客户名称', key: 'customer', visible: true, width: '120px', type: 'text', pinned: false, disable: false },
  {
    label: '交期/件数',
    key: 'due_time_qty_list',
    templateName: 'due_time_qty_list',
    visible: true,
    width: '150px',
    type: 'template',
    pinned: false,
    disable: false,
  },
  {
    label: '预计物料齐套日期',
    key: 'pre_material_completed_time',
    visible: true,
    width: '150px',
    type: 'date',
    pinned: false,
    disable: false,
  },
  { label: '待分配', key: 'to_be_allocated', visible: true, width: '100px', type: 'quantity', pinned: false, disable: false },
  { label: '外发加工厂', key: 'factory_name', visible: true, width: '120px', type: 'text', pinned: false, disable: false },
  {
    label: 'SAM(分钟)/人均日台产(件)',
    key: 'sam',
    visible: true,
    width: '200px',
    type: 'template',
    templateName: 'sam',
    pinned: false,
    disable: false,
    pinRight: true,
  },
];

export const allocationListSearchConfig = [
  {
    label: '大货单号',
    valueKey: 'order_uuid',
    type: 'select',
    optionKey: 'order_uuids',
  },
  {
    label: '客户名称',
    valueKey: 'customer',
    type: 'select',
    optionKey: 'customers',
  },
  {
    label: '外发加工厂',
    valueKey: 'factory_name',
    type: 'select',
    optionKey: 'factory_names',
  },
  {
    label: '交期',
    valueKey: 'due_time',
    type: 'date',
  },
  {
    label: '预计物料齐套日期',
    valueKey: 'pre_material_completed_time',
    type: 'date',
  },
];
