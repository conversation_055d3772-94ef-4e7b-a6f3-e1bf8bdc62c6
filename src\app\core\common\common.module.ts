import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { TranslateModule } from '@ngx-translate/core';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { FormsModule } from '@angular/forms';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { CancelActionModalComponent } from './popup-modal/cancel-action-modal.component';

@NgModule({
  declarations: [CancelActionModalComponent],
  imports: [CommonModule, TranslateModule, NzModalModule, NzButtonModule, NzTableModule, NzRadioModule, FormsModule, NzIconModule],
})
export class AppCommonModule {}
