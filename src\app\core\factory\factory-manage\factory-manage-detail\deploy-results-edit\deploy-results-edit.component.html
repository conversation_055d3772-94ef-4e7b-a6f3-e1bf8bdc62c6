<div class="deploy-container">
  <div class="title-label">{{ ('第几步' | translate: { value: '3' }) + '：' }}&nbsp;&nbsp;{{ '维护部署结果' | translate }}</div>
  <nz-divider></nz-divider>
  <form nz-form *ngIf="_service.factoryForm" [formGroup]="_service.factoryForm">
    <div style="display: flex; flex-direction: column; gap: 16px">
      <div class="deploy-content">
        <div class="label-required">{{ '工厂环境地址' | translate }}：</div>
        <div>
          <div class="link-container">
            <nz-form-item style="margin-bottom: 0px">
              <nz-form-control [nzErrorTip]="domainTpl">
                <input nz-input formControlName="domain" [placeholder]="placeInput" [maxLength]="100" (blur)="checkUrl()" inputTrim />
              </nz-form-control>
              <ng-template #domainTpl let-control>
                <ng-container *ngIf="control?.hasError('required')">
                  {{ 'placeholder.input' | translate }}{{ '工厂环境地址' | translate }}
                </ng-container>
                <ng-container *ngIf="control?.hasError('pattern')">
                  {{ 'form-error.input-number' | translate }}{{ '工厂环境地址' | translate }}
                </ng-container>
              </ng-template>
            </nz-form-item>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>
