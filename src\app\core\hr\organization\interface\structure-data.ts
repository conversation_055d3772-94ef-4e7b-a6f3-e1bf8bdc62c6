export interface EmployeeListData {
  id?: number;
  name: string;
  code: string;
  user: UserData;
  user_id: number;
  user_login_name: string;
  user_name: string;
  roles: any[];
  status: boolean;
}

export interface UserData {
  id?: number;
  name: string;
  login_name: string;
}

export interface DepartmentData {
  id: number;
  name: string;
  is_leaf: boolean;
  status: boolean;
  parent_id: number | null;
  employee: Employee[];
  emp_count: number;
  child: DepartmentData[];
}

export interface Employee {
  id: number;
  name: string;
}

export interface Department {
  id: number;
  code: string;
  name: string;
  status: boolean;
  parent_id: number | null;
  gen_time: string;
  modified_time: string;
  gen_user: string;
  modify_user: string;
  logos: string[];
}

export interface TreeOptions {
  label: any;
  value: any;
  expanded_list: any[];
  is_employee: boolean;
  depart_id?: number;
  parent_id?: number | null;
  id: any;
  name: any;
}
