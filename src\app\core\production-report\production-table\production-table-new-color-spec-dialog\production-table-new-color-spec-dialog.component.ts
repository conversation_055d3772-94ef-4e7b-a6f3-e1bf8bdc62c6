import { Component, EventEmitter, Input, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { startOfDay } from 'date-fns';
import { flattenDeep, isNumber, round, uniqBy } from 'lodash';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import {
  ProductionTableColorSizeLine,
  ProductionTableNodeDetailItem,
  ProductionTableNodePoItem,
  ProductionTableOrderLineInfo,
  ProductionTableOrderNode,
} from '../production-table.config';
import { ProductionTableService } from '../production-table.service';

@Component({
  selector: 'app-production-table-new-color-spec-dialog',
  templateUrl: './production-table-new-color-spec-dialog.component.html',
  styleUrls: ['./production-table-new-color-spec-dialog.component.scss'],
})
export class ProductionTableNewColorSpecDialogComponent implements OnInit {
  @Input() nodeInfo!: ProductionTableOrderNode;
  @Input() orderInfo!: ProductionTableOrderLineInfo;
  @Output() closeModal = new EventEmitter<boolean>();
  @ViewChild('qtyHistoryTemplate') qtyHistoryTemplate!: TemplateRef<HTMLElement>;
  @ViewChild('inspectionHistoryTemplate') inspectionHistoryTemplate!: TemplateRef<HTMLElement>;
  translateName = 'ProductionTableTemplateFiled.';
  lang = localStorage.getItem('lang') || 'zh';
  rawLineList: ProductionTableColorSizeLineClass[] = [];
  currentColorIndex = 0;
  currentColorLineList: ProductionTableColorSizeLineClass[] = [];
  poList: PoWithColorListClass[] = [];
  currentPo?: PoWithColorListClass;
  historyLine?: ProductionTableColorSizeLineClass;
  historyList: HistoryLine[] = [];
  inspectionHistoryList: HistoryLine[] = [];
  inspectionTotalInfo?: {
    inspected_qty: number;
    qualified_qty: number;
    qualifiedPercent: number;
    defected_qty: number;
    defectedPercent: number;
  };
  historyModal?: NzModalRef;
  topTabsetIndex = 0;
  inspectionForm: {
    process_desc: string;
    inspected_qty: number;
    defected_qty: number;
    qualified_qty: number;
    qualifiedPercent: number;
    defectedPercent: number;
    pictures: Picture[];
    remark: string;
  } = {
    process_desc: '',
    inspected_qty: 0,
    defected_qty: 0,
    qualified_qty: 0,
    qualifiedPercent: 0,
    defectedPercent: 0,
    pictures: [],
    remark: '',
  };

  pictures: Picture[] = [];
  get qtyTitle(): string {
    switch (this.nodeInfo.node_type) {
      case 1:
        return '裁数';
      case 2:
      case 4:
        return '产量';
      default:
        return '';
    }
  }
  get currentColorList(): ColorItem[] {
    return this.currentPo?.colorList ?? [];
  }
  get currentColor(): ColorItem {
    return this.currentColorList[this.currentColorIndex];
  }
  selectedDate = new Date();
  get isCheckAll(): boolean | null {
    if (this.currentColorLineList.length === 0) {
      return false;
    }
    let some: boolean = false;
    let every: boolean = true;
    this.currentColorLineList.forEach((item) => {
      if (item.checked) {
        some = true;
      }
      if (!item.checked) {
        every = false;
      }
    });
    return every ? true : some ? null : false;
  }
  set isCheckAll(value: boolean | null) {
    this.currentColorLineList.forEach((item) => (item.checked = value!));
  }

  constructor(private _service: ProductionTableService, private _notice: NzNotificationService, private _modal: NzModalService) {}

  ngOnInit(): void {
    this.getNodeInfo();
    if ([1, 9].includes(this.nodeInfo.node_type)) {
      this.topTabsetIndex = 1;
    }
  }
  getNodeInfo() {
    this._service
      .getNodeInfo({
        bulk_order_id: this.orderInfo.bulk_order_id,
        factory_out_sourcing_id: this.orderInfo.factory_out_sourcing_id,
        node_id: this.nodeInfo.id,
        node_type: this.nodeInfo.node_type,
      })
      .subscribe((result: { code: number; data: ProductionTableNodeDetailItem }) => {
        if (result.code === 200) {
          this.rawLineList = result.data.line_list.map((item) => new ProductionTableColorSizeLineClass(item));
          this.handleRawLineList(result.data.po_finish_list);
          this.currentPo = this.poList[0];
          if (this.currentColorIndex >= 0) this.changeColor(this.currentColorIndex);
        }
      });
  }
  uploadImage(event: any) {
    this.inspectionForm.pictures = event;
  }

  uploadImageCutting(event: any) {
    this.pictures = event;
  }
  handleRawLineList(poFinishList: ProductionTableNodePoItem[]) {
    const poMap: Map<string, PoWithColorListClass> = new Map();
    this.rawLineList.forEach((item) => {
      const targetPo = poMap.get(item.po_unique_code);
      if (targetPo) {
        const targetColor = targetPo.colorList.find((color) => color.color_id === item.color_id);
        if (targetColor) {
          targetColor.sizeList.push(item);
        } else {
          targetPo.colorList.push({
            color_id: item.color_id,
            color_name: item.color_name,
            sizeList: [item],
          });
        }
      } else {
        const newOne = new PoWithColorListClass(item);
        const targetColor = newOne.colorList.find((color) => color.color_id === item.color_id);
        if (targetColor) {
          targetColor.sizeList.push(item);
        } else {
          newOne.colorList.push({
            color_id: item.color_id,
            color_name: item.color_name,
            sizeList: [item],
          });
        }
        poMap.set(item.po_unique_code, newOne);
      }
    });
    poFinishList.forEach((po) => {
      const target = poMap.get(po.po_unique_code);
      if (target) {
        target.total_qty = po.total_qty;
      }
    });
    this.poList = Array.from(poMap.values());
    this.poList.forEach((po) => {
      po.colorList = uniqBy(po.colorList, 'color_id');
    });
  }
  handleChangeQty() {
    if (this.inspectionForm.inspected_qty) {
      const { qualified_qty, qualifiedPercent, defectedPercent } = this.handleQtyAndPercentCalc({
        inspected_qty: this.inspectionForm.inspected_qty,
        defected_qty: this.inspectionForm.defected_qty ?? 0,
      });
      this.inspectionForm.qualified_qty = qualified_qty;
      this.inspectionForm.qualifiedPercent = qualifiedPercent;
      this.inspectionForm.defectedPercent = defectedPercent;
    }
  }
  handleQtyAndPercentCalc(input: any) {
    const { inspected_qty, defected_qty } = input;
    let qualified_qty = inspected_qty - defected_qty;
    let qualifiedPercent = round((qualified_qty / inspected_qty) * 100, 2);
    let defectedPercent = round((defected_qty / inspected_qty) * 100, 2);
    return { qualified_qty, qualifiedPercent, defectedPercent };
  }
  changePo($event: any) {
    this.currentColorIndex = 0;
    this.changeColor(0);
  }
  changeColor(index: number) {
    const colorId = this.currentColorList[index].color_id;
    const poUniqueCode = this.currentPo!.po_unique_code;
    this.currentColorLineList = this.rawLineList.filter((line) => line.po_unique_code === poUniqueCode && line.color_id === colorId);
  }
  showDetail(line: ProductionTableColorSizeLineClass) {
    this.historyLine = line;
    const payload = {
      color_id: line.color_id,
      node_id: this.nodeInfo.id,
      node_type: this.nodeInfo.node_type,
      spec_id: line.spec_id,
      po_unique_code: this.currentPo!.po_unique_code,
    };
    this._service.getNodeHistory(payload).subscribe((result) => {
      if (result.code === 200) {
        this.historyList = result.data.line_list;
        this.historyModal = this._modal.create({
          nzTitle: this._service.translateValue(this.translateName + '历史记录'),
          nzFooter: null,
          nzWidth: 600,
          nzContent: this.qtyHistoryTemplate,
          nzBodyStyle: {
            padding: '0 12px',
          },
        });
        this.historyModal?.afterClose.subscribe(() => {
          this.historyLine = undefined;
        });
      }
    });
  }
  showPoHistory() {
    const payload = {
      node_id: this.nodeInfo.id,
      node_type: this.nodeInfo.node_type,
      po_unique_code: this.currentPo!.po_unique_code,
    };
    this._service.getNodeHistory(payload).subscribe((result) => {
      if (result.code === 200) {
        this.historyList = result.data.line_list;
        this.historyModal = this._modal.create({
          nzTitle: this._service.translateValue(this.translateName + '历史记录'),
          nzFooter: null,
          nzWidth: 600,
          nzContent: this.qtyHistoryTemplate,
          nzBodyStyle: {
            padding: '0 12px',
          },
        });
      }
    });
  }
  showInspectionHistory() {
    const payload = {
      node_id: this.nodeInfo.id,
      node_type: 3,
      // node_type: this.nodeInfo.node_type,
    };
    this._service.getNodeHistory(payload).subscribe((result) => {
      if (result.code === 200) {
        this.inspectionHistoryList = result.data.line_list;
        this.inspectionTotalInfo = {
          inspected_qty: 0,
          defected_qty: 0,
          qualified_qty: 0,
          qualifiedPercent: 0,
          defectedPercent: 0,
        };
        this.inspectionHistoryList.forEach((line) => {
          this.inspectionTotalInfo!.inspected_qty += line.inspected_qty;
          this.inspectionTotalInfo!.defected_qty += line.defected_qty;
          const { defectedPercent, qualifiedPercent, qualified_qty } = this.handleQtyAndPercentCalc(line);
          line.defectedPercent = defectedPercent;
          line.qualifiedPercent = qualifiedPercent;
          line.qualified_qty = qualified_qty;
        });
        const { defectedPercent, qualifiedPercent, qualified_qty } = this.handleQtyAndPercentCalc(this.inspectionTotalInfo);
        this.inspectionTotalInfo!.qualified_qty = qualified_qty;
        this.inspectionTotalInfo!.qualifiedPercent = qualifiedPercent;
        this.inspectionTotalInfo!.defectedPercent = defectedPercent;
        this.historyModal = this._modal.create({
          nzTitle: this._service.translateValue(this.translateName + '历史记录'),
          nzFooter: null,
          nzWidth: 850,
          nzContent: this.inspectionHistoryTemplate,
          nzBodyStyle: {
            padding: '0 12px',
          },
        });
      }
    });
  }
  closeHistoryModal() {
    this.historyModal?.close();
  }
  saveHistoryChange() {
    const payload = {
      update_list: this.historyList.map((line) => ({
        node_id: this.nodeInfo.id,
        info_id: line.info_id,
        id: line.id,
        qty: line.qty.toString(),
        node_type: this.nodeInfo.node_type,
        // pictures: line.pictures,
      })),
    };
    this._service.updateNodeHistory(payload).subscribe((result) => {
      if (result.code === 200) {
        this._notice.success('', this._service.translateValue('flss.success.update'));
        this.getNodeInfo();
        this.historyModal?.close();
      }
    });
  }
  getQtyNode() {
    const node: uploadNodeItem = {
      finished_time: startOfDay(this.selectedDate).getTime(),
      node_type: this.nodeInfo.node_type,
      po_unique_code: this.currentPo!.po_unique_code,
      pictures: this.pictures,
      line_list: [],
    };
    const po = this.currentPo!;
    if (po.disableAllchildrenQty) {
      node.finish_qty = po.input_qty;
    } else {
      po.colorList.forEach((color) => {
        color.sizeList.forEach((size) => {
          if (size.qty) {
            node.line_list?.push({
              checked: size.checked,
              color_id: color.color_id,
              color_name: color.color_name,
              spec_id: size.spec_id,
              spec_size: size.spec_size,
              qty: size.qty,
            });
          }
        });
      });
    }
    return node;
  }
  cancel() {
    this.closeModal.emit(false);
  }
  save() {
    const payload: { id: number; content_type: number; node_list: uploadNodeItem[] } = {
      id: this.nodeInfo.id,
      content_type: 3,
      node_list: [],
    };
    switch (this.nodeInfo.node_type) {
      case 1:
      case 9:
        const node: uploadNodeItem = this.getQtyNode();
        if (node.line_list?.length === 0 && !this.currentPo?.disableAllchildrenQty) {
          this._notice.error('', this._service.translateValue(this.translateName + '请至少填写一条数据'));
          return;
        }
        payload.node_list.push(node);
        break;
      case 2:
      case 4:
        if (this.topTabsetIndex === 0) {
          if (this.inspectionForm.process_desc.trim().length === 0) {
            this._notice.error('', this._service.translateValue(this.translateName + '请先填写工序信息'));
            return;
          }
          if (this.inspectionForm.inspected_qty === 0) {
            this._notice.error('', this._service.translateValue(this.translateName + '检验数不能为0'));
            return;
          }
          const node: uploadNodeItem = {
            finished_time: new Date().getTime(),
            pictures: this.inspectionForm.pictures,
            node_type: 3,
            inspected_qty: this.inspectionForm.inspected_qty,
            defected_qty: this.inspectionForm.defected_qty,
            remark: this.inspectionForm.remark,
            process_desc: this.inspectionForm.process_desc,
          };
          payload.node_list.push(node);
        } else {
          const node: uploadNodeItem = this.getQtyNode();
          if (node.line_list?.length === 0 && !this.currentPo?.disableAllchildrenQty) {
            this._notice.error('', this._service.translateValue(this.translateName + '请至少填写一条数据'));
            return;
          }
          payload.node_list.push(node);
        }
        break;
      default:
        break;
    }
    this._service.updateNode(payload).subscribe((result) => {
      if (result.code === 200) {
        this._notice.success('', this._service.translateValue('flss.success.update'));
        this.closeModal.emit(true);
      }
    });
  }
}

interface ColorItem {
  color_id: number;
  color_name: string;
  sizeList: ProductionTableColorSizeLineClass[];
}
class PoWithColorListClass implements ProductionTableNodePoItem {
  po_unique_code: string;
  po_code: string;
  total_qty: number;
  input_qty?: number;
  colorList: ColorItem[] = [];
  get disableInputQty(): boolean {
    return this.colorList.some((color) => color.sizeList.some((size) => isNumber(size.qty) && size.qty > 0));
  }
  get disableAllchildrenQty(): boolean {
    return isNumber(this.input_qty);
  }
  constructor(input: ProductionTableNodePoItem) {
    this.po_code = input.po_code!;
    this.po_unique_code = input.po_unique_code!;
    this.total_qty = input.total_qty ?? 0;
  }
}
class ProductionTableColorSizeLineClass implements ProductionTableColorSizeLine {
  color_id: number;
  color_name: string;
  spec_id: number;
  spec_size: string;
  qty: number;
  order_qty: number;
  po_code: string;
  po_unique_code: string;
  gen_user_name: string;
  finished_time: number;
  total_qty: number;
  _checked: boolean;
  historyList: HistoryLine[] = [];
  get checked() {
    return this._checked;
  }
  set checked(value: boolean) {
    this._checked = value;
    if (value && this.difference_qty > 0) {
      this.qty = this.order_qty - this.total_qty;
    }
  }
  get difference_qty(): number {
    const diffQty = this.order_qty - this.qty - this.total_qty;
    if (diffQty < 0) {
      return 0;
    }
    return diffQty;
  }
  constructor(input: ProductionTableColorSizeLine) {
    this.color_id = input.color_id;
    this.color_name = input.color_name;
    this.spec_id = input.spec_id;
    this.spec_size = input.spec_size;
    this.qty = 0;
    this.order_qty = input.order_qty;
    this.po_code = input.po_code;
    this.po_unique_code = input.po_unique_code;
    this.gen_user_name = input.gen_user_name;
    this.finished_time = input.finished_time;
    this.total_qty = input.total_qty;
    this._checked = input.checked;
  }
}
interface uploadNodeItem {
  finished_time: number;
  pictures?: Picture[];
  node_type: number;
  inspected_qty?: number;
  defected_qty?: number;
  remark?: string;
  inspected_status?: number;
  process_desc?: string;
  finish_qty?: number;
  po_unique_code?: string;
  line_list?: {
    color_id: number;
    color_name: string;
    spec_id: number;
    spec_size: string;
    qty: number;
    checked: boolean;
  }[];
}

interface HistoryLine {
  color_list: any[];
  finished_time: number;
  id: number;
  info_id: number;
  inspected_qty: number;
  qualified_qty?: number;
  qualifiedPercent?: number;
  defected_qty: number;
  defectedPercent?: number;
  inspected_status: number;
  pictures: Picture[];
  process_desc: string;
  qty: number;
  remark: string;
  spec_list: any[];
  user_name: string;
}

interface Picture {
  name: string;
  url: string;
  version: string;
}
