<div class="plant-item">
  <app-order-outsourcing-factory-item-header
    #factoryItemHeader
    [toggle]="toggle"
    [parentGroup]="parentGroup"
    [editMode]="editMode"
    [_pos]="_pos"
    [orderStatus]="orderStatus"
    [detailForm]="detailForm"
    [orderDetail]="orderDetail"
    [showOrderFinish]="showOrderFinish"
    [showOneClickInbound]="showOneClickInbound"
    [showAdvancePayment]="showAdvancePayment"
    [io_basic]="io_basic"
    (handleChangeToggle)="handleChangeToggle()"
    (handleChangeValue)="handleChangeValue($event)"
    (handleChangeLineValue)="handleChangeLineValue()"
    (handleRemoveFactory)="handleRemoveFactory()"
    (orderFinish)="orderFinish()"
    (showInferencePrice)="showInferencePrice()"
    (oneClickInbound)="oneClickInbound()"
    (onAdvancePayment)="onAdvancePayment()"></app-order-outsourcing-factory-item-header>

  <div [ngClass]="{ 'toggle-shouqi': !toggle }" class="toggle-zhankai">
    <div class="plant-item-header" *ngIf="editMode !== 'read'">
      <div class="plant-item-header-left" *ngIf="parentGroup.get('factory_short_name')?.value">
        <span class="">{{ 'outsourcingComponents.交付单' | translate }}</span>
      </div>
      <div class="plant-item-header-right" *ngIf="parentGroup.get('factory_short_name')?.value">
        <!-- 注释掉添加剩余未分配标签 -->
        <!-- <div
          class="surplus"
          [ngClass]="{ 'disabled-element': parentGroup.get('finish')?.value || !surplusPos.length || io_basic?.is_use_plan }">
          <i nz-icon [nzIconfont]="'icon-xinjian1'" class="icon-xinjian1"></i>
          <span (click)="handleAddSurplus()">{{ 'outsourcingComponents.添加剩余未分配' | translate }}</span>
        </div> -->
        <div
          class="xinjian-box"
          (click)="handleAddPo()"
          [ngClass]="{ 'disabled-element': parentGroup.get('finish')?.value || !getAvailablePos().length || io_basic?.is_use_plan }">
          <i nz-icon [nzIconfont]="'icon-xinjian1'" class="icon-xinjian1"></i>
          <span>{{ 'outsourcingComponents.添加交付单' | translate }}</span>
        </div>
      </div>
    </div>
    <div class="plant-item-body">
      <!-- pos没有数据则暂时暂无数据 放在factoryTpl会无法展示 -->
      <ng-container *ngIf="_pos && !_pos.length">
        <div style="padding-bottom: 10px; margin-bottom: 60px; margin-top: 14px">
          <flc-no-data [flcDisableOnClick]="1000" [noDataTextTpl]="noDataTextTpl"></flc-no-data>
          <ng-template #noDataTextTpl>
            <span>{{ 'outsourcingComponents.暂无数据' | translate }}</span>
          </ng-template>
        </div>
      </ng-container>
      <app-order-outsourcing-tabs-container
        #tabContainer
        [parentGroup]="parentGroup"
        [selectableList]="getAvailablePos()"
        [tabsBodyTpl]="factoryTpl"
        [tabs]="_pos"
        [surplusPos]="surplusPos"
        [modelType]="editMode !== 'read'"
        [is_use_plan]="io_basic?.is_use_plan"
        [factoryIndex]="factoryIndex"
        [getPoId]="getPoId"
        [colorSizeRefs]="colorSizePosTableRefs"
        [colorSizeSurplusRefs]="colorSizeSurplusRefs"
        [showLossCount]="true"
        [orderStatus]="orderStatus">
        <ng-template #factoryTpl let-data="data">
          <ng-container *ngIf="data?.po_lines?.length; else po_linesNotData">
            <flc-color-size-table
              [mode]="editMode !== 'read' && !io_basic?.is_use_plan ? 'toggleCell' : 'readOnly'"
              [isEditToggle]="tableCanEdit"
              class="rest-color-size-table-padding"
              [dataList]="data?.po_lines"
              [fetchCellData]="onFetchCellData"
              #colorSizePosTable
              (onChange)="sizeColorChange($event)"></flc-color-size-table>
            <div class="personal-info">
              <div>
                <span>{{ 'outsourcingComponents.收货人' | translate }}：</span>
                <flc-text-truncated [data]="data?.po_basic?.receiver"></flc-text-truncated>
              </div>
              <div>
                <span>{{ 'outsourcingComponents.联系方式' | translate }}：</span>
                <flc-text-truncated [data]="data?.po_basic?.contact"></flc-text-truncated>
              </div>
              <div>
                <span>{{ 'outsourcingComponents.收货地址' | translate }}：</span>
                <flc-text-truncated
                  [data]="
                    data?.po_basic?.country_name +
                    data?.po_basic?.province_name +
                    data?.po_basic?.city_name +
                    data?.po_basic?.district_name +
                    data?.po_basic?.address
                  "></flc-text-truncated>
              </div>
            </div>
            <div class="dotted-line" *ngIf="_po_lines.length"></div>
            <app-order-outsourcing-card-container
              *ngIf="_po_lines.length"
              [title]="'outsourcingComponents.合计' | translate"
              [showCorner]="true"
              [titleLine]="false"
              [borderRadiusSize]="'0 0 4px 4px'">
              <flc-color-size-table #colorSizeSurplusRefs [dataList]="_po_lines"></flc-color-size-table>
            </app-order-outsourcing-card-container>
          </ng-container>
          <!-- 暂无数据 -->
          <ng-template #po_linesNotData>
            <div style="background-color: #fff">
              <flc-no-data [flcDisableOnClick]="1000" [noDataTextTpl]="noDataTextTpl"></flc-no-data>
              <ng-template #noDataTextTpl>
                <span>{{ 'outsourcingComponents.暂无数据' | translate }}</span>
              </ng-template>
            </div>
          </ng-template>
        </ng-template>
      </app-order-outsourcing-tabs-container>
    </div>
  </div>
</div>
<!-- 暂无数据 -->
<ng-template #noData>
  <div class="not-data-container">
    <flc-no-data [flcDisableOnClick]="1000" [noDataTextTpl]="noDataTextTpl"></flc-no-data>
    <ng-template #noDataTextTpl>
      <span>{{
        (editMode === 'read' ? 'outsourcingComponents.暂无数据' : 'outsourcingComponents.暂无数据，请先选择' + labelName) | translate
      }}</span>
    </ng-template>
  </div>
</ng-template>
