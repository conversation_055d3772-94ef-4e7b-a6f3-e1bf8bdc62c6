<div class="container">
  <div #searchBar *ngIf="isShowSearchContainer" style="margin-bottom: 12px">
    <flc-search-container [headerTitle]="'productionProgressTitle.生产进度报表' | translate" [showBtnContainer]="false">
      <ng-container *ngFor="let item of searchList">
        <div class="search-item-container" *ngIf="item.visible">
          <span class="search-name">{{ 'productionProgress.' + item?.label | translate }}：</span>
          <flc-dynamic-search-select
            *ngIf="item.type === 'select'"
            [dataUrl]="searchOptionFetchUrl"
            [transData]="{ value: 'value', label: 'label' }"
            [(ngModel)]="searchData[item?.key]"
            [column]="item?.key"
            [optAlwaysReload]="true"
            (handleSearch)="onSearch(true)">
          </flc-dynamic-search-select>
          <nz-select
            *ngIf="item.type === 'local-select'"
            [(ngModel)]="searchData[item?.key]"
            nzAllowClear
            [nzPlaceHolder]="'placeholder.select' | translate"
            (ngModelChange)="onSearch(true)">
            <nz-option
              *ngFor="let item of item?.options"
              [nzValue]="item?.value"
              [nzLabel]="'productionProgressOption.' + item?.label | translate"></nz-option>
          </nz-select>
          <nz-range-picker
            *ngIf="item.type === 'dateRange'"
            [(ngModel)]="searchData[item?.key]"
            (ngModelChange)="onSearch(true)"
            [nzPlaceHolder]="[
              'productionProgressPlaceholder.开始' | translate,
              'productionProgressPlaceholder.结束' | translate
            ]"></nz-range-picker>
        </div>
      </ng-container>
    </flc-search-container>
  </div>

  <app-production-report-tab
    [(selectedTab)]="selectedTab"
    [(currentDimension)]="currentDimension"
    (handleReset)="onReset()"
    (selectedTabChange)="onTabChange()"
    (currentDimensionChange)="onDimensionChange()"
    (handleToggle)="onToggle()">
  </app-production-report-tab>

  <div style="padding: 12px; background-color: #fff">
    <nz-table
      #progressTable
      [nzFrontPagination]="false"
      [(nzPageSize)]="tableConfig.pageSize"
      [(nzPageIndex)]="tableConfig.pageIndex"
      [nzShowTotal]="totalTemplate"
      [nzTotal]="tableConfig.count"
      [nzPageSizeOptions]="[20, 30, 40, 50]"
      (nzPageIndexChange)="onIndexChanges($event)"
      (nzPageSizeChange)="onSizeChanges($event)"
      nzShowSizeChanger
      [nzData]="tableConfig.dataList"
      [nzLoading]="tableConfig.loading"
      [nzScroll]="{ x: '100%', y: tableConfig?.height + 'px' }"
      [nzPaginationType]="'small'"
      nzBordered>
      <thead>
        <tr>
          <th [nzLeft]="true" nzWidth="46px">
            <a class="setting-btn" (click)="onChangeHeader($event)">
              <i nz-icon nzType="setting" nzTheme="fill"></i>
            </a>
          </th>
          <ng-container *ngFor="let item of renderHeaders">
            <th [nzLeft]="item.pinned || false" [nzWidth]="item.width" *ngIf="item.visible">
              {{ 'productionProgress.' + item.label | translate }}
            </th>
          </ng-container>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let item of progressTable.data; index as i">
          <ng-container *ngFor="let po of item?.po_list; index as poIndex">
            <tr *ngFor="let sub of po?.sub_list; index as subIndex">
              <!-- 序号 -->
              <td [rowSpan]="item?.len" [nzLeft]="true" *ngIf="!poIndex && !subIndex">{{ i + 1 }}</td>
              <ng-container *ngFor="let header of renderHeaders">
                <!-- 日期 大货单号 customer_style -->
                <td
                  [rowSpan]="item?.len"
                  [nzLeft]="header.pinned || false"
                  *ngIf="header.isPoIndex && !poIndex && !subIndex && header.visible">
                  <ng-container *ngIf="header.key === 'biz_date'">
                    <flc-text-truncated data="{{ item?.biz_date | date: 'MM/dd' }}"></flc-text-truncated>
                  </ng-container>
                  <ng-container *ngIf="header.type === 'text'">
                    <flc-text-truncated [data]="item[header.key]"></flc-text-truncated>
                  </ng-container>
                </td>

                <!-- 交付单 交期 订单数-->
                <td [attr.rowspan]="po?.len" [nzLeft]="header.pinned || false" *ngIf="header.subIndex && !subIndex && header.visible">
                  <ng-container *ngIf="header.key === 'po_code' || header.key === 'customer'">
                    <flc-text-truncated [data]="po[header.key]"></flc-text-truncated>
                  </ng-container>
                  <ng-container *ngIf="header.key === 'due_time_list'">
                    <div
                      *ngIf="po?.due_time_list?.length; else noValueTpl"
                      nz-tooltip
                      [nzTooltipTrigger]="po?.due_time_overflow ? 'hover' : null"
                      [nzTooltipTitle]="due_time_tpl"
                      class="due_times">
                      <span *ngFor="let due of po?.due_time_arr; let due_idx = index"
                        >{{ due }}{{ due_idx === po?.due_time_arr.length - 1 && po?.due_time_overflow ? '...' : '' }}</span
                      >
                    </div>
                    <ng-template #due_time_tpl>
                      <div *ngFor="let due of po?.due_time_list">{{ due }}</div>
                    </ng-template>
                  </ng-container>
                  <ng-container *ngIf="header.key === 'order_total'">
                    {{ po?.order_total ?? 0 | number }}
                  </ng-container>
                </td>

                <!-- other -->
                <td *ngIf="!header.isPoIndex && !header.subIndex && header.visible" [nzLeft]="header.pinned || false">
                  <ng-container *ngIf="header.type === 'text'">
                    <flc-text-truncated data="{{ sub[header.key] }}"></flc-text-truncated>
                  </ng-container>

                  <ng-container *ngIf="header.type === 'cellTpl'">
                    <ng-container *ngTemplateOutlet="cellTpl; context: { $implicit: sub?.[header.key], key: header.key }"></ng-container>
                  </ng-container>

                  <ng-container *ngIf="header.type === 'qualityTpl'">
                    <span
                      [ngClass]="{ 'blue-link-text': sub?.[header.key] }"
                      (click)="sub?.[header.key]  && onOpenModal(header.key, item, po, sub)"
                      >{{ sub?.[header.key]  ?? 0 | number }}</span
                    >
                    /
                    <span
                      [ngClass]="{ 'red-link-text': sub?.[header.subKey!]  }"
                      (click)="sub?.[header.subKey!] && onOpenDefectiveModal(header.subKey!, item, po, sub)"
                      >{{ sub?.[header.subKey!] ?? 0 | number }}</span
                    >
                  </ng-container>

                  <!-- 生产计划起止日期 -->
                  <ng-container *ngIf="header.key === 'plan_times'">
                    <ng-container *ngIf="sub?.plan_times?.length; else noValueTpl">
                      <div *ngFor="let plan_time of sub?.plan_times">
                        <flc-text-truncated
                          data="{{
                            plan_time.plan_start_time | date: 'yyyy/MM/dd' + ' ~ ' + (plan_time.plan_end_time | date: 'yyyy/MM/dd')
                          }}"></flc-text-truncated>
                      </div>
                    </ng-container>
                  </ng-container>
                  <!-- 状态 -->
                  <ng-container *ngIf="header.key === 'status'">
                    <span
                      *ngIf="sub?.status; else noValueTpl"
                      [ngClass]="[(currentDimension === dimensionRange.io ? 'io-status-' : 'po-status-') + sub?.status, 'status']">
                      {{ sub.status_name }}
                    </span>
                  </ng-container>

                  <ng-container *ngIf="header.key === 'progress'">
                    <span>{{ sub?.progress ?? 0 | percent: '1.0-1' }}</span>
                  </ng-container>
                </td>
              </ng-container>

              <ng-template #cellTpl let-val let-key="key" let-color="color">
                <span [ngClass]="{ 'blue-link-text': val }" (click)="val && onOpenModal(key, item, po, sub)">{{ val ?? 0 | number }}</span>
              </ng-template>
            </tr>
          </ng-container>
        </ng-container>

        <tr *ngIf="tableConfig.dataList?.length" class="total-row">
          <td [nzLeft]="true">合计</td>
          <ng-container *ngFor="let header of renderHeaders">
            <td [nzLeft]="header.pinned || false" *ngIf="header.visible">
              <ng-container *ngIf="header.hasTotal">
                <ng-container *ngIf="header.type === 'cellTpl' || header.type === 'number'">
                  {{ sumData[header.key] ?? 0 | number }}
                </ng-container>
                <ng-container *ngIf="header.type === 'qualityTpl'">
                  <span>{{ sumData?.[header.key] ?? 0 | number }}</span>
                  /
                  <span [ngClass]="{ red: sumData?.[header.subKey!] }">{{ sumData?.[header.subKey!] ?? 0 | number }}</span>
                </ng-container>
              </ng-container>
            </td>
          </ng-container>
        </tr>
      </tbody>
    </nz-table>
    <ng-template #totalTemplate>
      <div style="margin-right: 16px; color: #000000; font-size: 14px">
        共 {{ tableConfig?.count }} 条， 第 <span style="color: #0f86f8">{{ tableConfig?.pageIndex }}</span> /
        {{ tableConfig?.count / tableConfig?.pageSize | flcMathCeil }} 页
      </div>
    </ng-template>
  </div>
</div>

<ng-template #noValueTpl>
  <span style="color: #b5b8bf">-</span>
</ng-template>
