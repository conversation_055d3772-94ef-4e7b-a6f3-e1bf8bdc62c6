<div class="full-screen" [ngClass]="{ mobile: mobileMode }">
  <img src="../../../assets/image/logo_fl.png" class="logo-corner" />

  <div class="main">
    <!-- 国际化切换选择，默认隐藏 -->
    <div style="position: absolute; top: 20px; right: 20px" *ngIf="showSwitchLang">
      <nz-select [(ngModel)]="lang" (ngModelChange)="switchLanguage($event)" [nzDropdownMatchSelectWidth]="false">
        <nz-option nzValue="en" nzLabel="En"></nz-option>
        <nz-option nzValue="zh" nzLabel="中文"></nz-option>
      </nz-select>
    </div>

    <div [ngSwitch]="brand">
      <div *ngSwitchCase="'FLKJ_ELAN'" class="title" [innerHTML]="title"></div>
      <div *ngSwitchDefault class="title-image-wrap">
        <div class="title" [innerHTML]="title"></div>
        <div class="image"></div>
      </div>
    </div>

    <!-- 用户登录 -->
    <div *ngIf="state == 'login'">
      <div class="sub-title">{{ 'login.userLogin' | translate }}</div>

      <form nz-form nzlayout="vertical" class="login-form">
        <nz-form-item>
          <nz-form-control>
            <nz-input-group [nzPrefix]="prefixUser">
              <input
                #userNameInput
                type="text"
                nz-input
                name="userName"
                [(ngModel)]="userName"
                placeholder="{{ 'login.userPlaceholder' | translate }}"
                inputTrim />
            </nz-input-group>
          </nz-form-control>
          <ng-template #prefixUser><i class="iconfont icon-yonghuming"></i></ng-template>
        </nz-form-item>
        <nz-form-item>
          <nz-form-control>
            <nz-input-group [nzPrefix]="prefixLock" [nzSuffix]="suffixEye">
              <input
                #passwordInput
                [type]="isPwdVisible ? 'text' : 'password'"
                nz-input
                name="password"
                [(ngModel)]="password"
                placeholder="{{ 'login.pwdPlaceholder' | translate }}"
                inputTrim />
            </nz-input-group>
          </nz-form-control>
          <ng-template #prefixLock><i class="iconfont icon-mima"></i></ng-template>
          <ng-template #suffixEye>
            <i nz-icon [nzType]="isPwdVisible ? 'eye' : 'eye-invisible'" (click)="isPwdVisible = !isPwdVisible"></i>
          </ng-template>
        </nz-form-item>

        <button
          nz-button
          class="login-form-button"
          flButton="pretty-primary"
          nzShape="round"
          nzSize="large"
          [disableOnClick]="1000"
          (click)="goCaptcha()"
          [disabled]="userName.length === 0 || password.length === 0">
          {{ 'login.login' | translate }}
        </button>
      </form>
    </div>

    <div *ngIf="state === 'captcha'">
      <div class="sub-title">{{ 'login.captchaTitle' | translate }}</div>
      <div class="captcha-form">
        <flc-captcha-puzzle [fetchPuzzleImage]="onFetchPuzzleImage" [verify]="onCaptchaVerify"></flc-captcha-puzzle>
        <button
          nz-button
          class="login-form-button"
          flButton="pretty-primary"
          nzShape="round"
          nzSize="large"
          [disableOnClick]="1000"
          (click)="goLogin()">
          {{ 'login.back' | translate }}
        </button>
      </div>
    </div>

    <!-- 设置密码 -->
    <div class="resetPwd" *ngIf="state == 'resetPwd'">
      <div class="sub-title">{{ 'login.pwdSetup' | translate }}</div>

      <form nz-form [formGroup]="pwdForm" nzLayout="vertical" class="login-form">
        <nz-form-item>
          <nz-form-control>
            <nz-input-group [nzPrefix]="prefixLock" [nzSuffix]="suffixEye1">
              <input
                [type]="isPwd1Visible ? 'text' : 'password'"
                nz-input
                maxlength="16"
                formControlName="password1"
                placeholder="{{ 'login.pwdPlaceholder' | translate }}"
                (click)="onPwd1Click()"
                inputTrim />
            </nz-input-group>
          </nz-form-control>
          <ng-template #suffixEye1>
            <i nz-icon [nzType]="isPwd1Visible ? 'eye' : 'eye-invisible'" (click)="isPwd1Visible = !isPwd1Visible"></i>
          </ng-template>
          <nz-form-label class="pwd-hint">{{ pwdHint }}</nz-form-label>
        </nz-form-item>

        <nz-form-item>
          <nz-form-control>
            <nz-input-group [nzPrefix]="prefixLock" [nzSuffix]="suffixEye2">
              <input
                [type]="isPwd2Visible ? 'text' : 'password'"
                nz-input
                maxlength="16"
                formControlName="password2"
                placeholder="{{ 'login.pwdConfirmPlaceholder' | translate }}"
                inputTrim />
            </nz-input-group>
          </nz-form-control>
          <ng-template #suffixEye2>
            <i nz-icon [nzType]="isPwd2Visible ? 'eye' : 'eye-invisible'" (click)="isPwd2Visible = !isPwd2Visible"></i>
          </ng-template>
          <ng-template #prefixLock><i class="iconfont icon-mima"></i></ng-template>
          <nz-form-label class="pwd-hint">
            <div *ngIf="pwdForm.errors?.['notMatching'] && (pwdForm.touched || pwdForm.dirty)" class="error">
              {{ 'login.pwdNotMatching' | translate }}
            </div>
          </nz-form-label>
        </nz-form-item>

        <button
          nz-button
          class="login-form-button"
          flButton="pretty-primary"
          nzShape="round"
          nzSize="large"
          [disabled]="!(pwdForm.touched && pwdForm.valid)"
          [disableOnClick]="1000"
          (click)="submitPwdForm()">
          {{ 'login.nextStep' | translate }}
        </button>
      </form>
    </div>

    <!-- 设置密码 -->
    <div *ngIf="state == 'resetDone'">
      <div class="sub-title">{{ 'login.pwdSetup' | translate }}</div>
      <div class="login-form">
        <div class="msg"><i nz-icon nzType="check-circle" nzTheme="fill"></i> {{ 'login.pwdResetDone' | translate }}</div>
        <button nz-button class="login-form-button" flButton="pretty-primary" nzShape="round" nzSize="large" (click)="goLogin()">
          {{ 'login.loginNow' | translate }}
        </button>
      </div>
    </div>
  </div>
</div>
