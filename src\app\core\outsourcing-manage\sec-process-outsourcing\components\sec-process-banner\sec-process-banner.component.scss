:host::ng-deep {
  .sec-process-tab-container {
    display: flex;
    align-items: flex-start;
    width: calc(100% - 140px);
    flc-text-truncated {
      color: #007aff;
    }
    .sec-process-tab-btn-area {
      display: flex;
      flex-wrap: wrap;
      row-gap: 8px;
      .btn-margin-right {
        margin-right: 8px;
        background-color: #f7f8fa;
      }
      .sec-process-tab-btn-active {
        background-color: #e5f9f3;
        color: #00af7f;
        border-color: #e5f9f3;
      }
      .tab-btn {
        height: 30px;
        border: 1px solid #d9d9d9;
        color: #515665;
        background: #fff;
        border-radius: 16px;
        text-align: center;
        padding: 4px 16px;
      }
    }
    .sec-process-select-area {
      .ant-form-item {
        flex-wrap: nowrap;
        .ant-select-selector {
          border-radius: 16px;
          width: 210px;
          flex-wrap: nowrap;
        }
      }
    }
    .vertical-divider {
      display: flex;
      padding: 10px 0;
      .ant-divider-vertical {
        margin: 0 12px 0 4px;
        border-left: 1px solid #d3d3d3;
      }
    }
    .ant-select-multiple .ant-select-selector {
      padding: 1px 8px;
      .ant-select-selection-item {
        height: 20px;
        line-height: 16px;
        font-size: 12px;
      }
      .ant-select-selection-item-remove {
        line-height: 15px;
      }
      .ant-select-clear {
        margin-top: -7px;
      }
    }
  }
}
