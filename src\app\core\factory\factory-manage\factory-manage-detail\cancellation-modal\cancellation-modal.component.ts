import { Component, EventEmitter, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { NzModalService } from 'ng-zorro-antd/modal';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-cancellation-modal',
  templateUrl: './cancellation-modal.component.html',
  styleUrls: ['./cancellation-modal.component.scss'],
})
export class CancellationModalComponent implements OnInit {
  @ViewChild('titleTpl') titleTpl: TemplateRef<any> | undefined;
  @ViewChild('contentTpl') contentTpl: TemplateRef<any> | undefined;
  @ViewChild('footerTpl') footerTpl: TemplateRef<any> | undefined;
  @Output() handleCancelOk = new EventEmitter<any>();
  cancelNotice?: string;
  cancelContent?: string;

  constructor(private _modal: NzModalService, public _translateService: TranslateService) {}

  ngOnInit(): void {}

  createCancellationModel() {
    this.cancelNotice = this._translateService.instant('factory-modal.cancellation-notice');
    this.cancelContent = this._translateService.instant('factory-modal.cancellation-content');
    this._modal.create({
      nzWidth: '350px',
      nzTitle: this.titleTpl,
      nzContent: this.contentTpl,
      nzFooter: this.footerTpl,
    });
  }

  closeCancellationModel() {
    this._modal.closeAll();
  }

  handleCancel() {
    this.closeCancellationModel();
  }

  handleOk() {
    this.handleCancelOk.emit();
  }
}
