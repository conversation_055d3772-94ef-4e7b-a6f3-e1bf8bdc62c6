import { Injectable } from '@angular/core';
import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

@Injectable({
  providedIn: 'root',
})
export class ValidationService {
  constructor() {}

  /**
   * 比对两个field的内容是否一样，不一样返回error: "notMatching: true"
   * @param field1
   * @param field2
   * @returns
   */
  notMatchingValidator = (field1: string, field2: string): ValidatorFn => {
    return (control: AbstractControl): ValidationErrors | null => {
      const c1 = control.get(field1);
      const c2 = control.get(field2);

      return c1 && c1.value !== '' && c2 && c2.value !== '' && c1.value !== c2.value ? { notMatching: true } : null;
    };
  };
}
