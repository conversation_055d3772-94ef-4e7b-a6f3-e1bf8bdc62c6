<div class="wrap">
  <div class="ganttTable">
    <div
      *ngIf="isLoading"
      style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; position: absolute; z-index: 900">
      <nz-spin [nzSpinning]="true"></nz-spin>
    </div>
    <div
      style="height: 100%"
      [ngStyle]="{
        opacity: isLoading ? '0.5' : '1'
      }">
      <app-production-line-view-graph
        [isEdit]="isEdit"
        [graphOptions]="graphOptions"
        [dates]="dates"
        [productionLineList]="productionLineList"
        (actionRefresh)="refresh()"
        (onAction)="onAction($event)"
        (forwardDate)="forward()"
        (backwardsDate)="backwards()"
        (actionLoading)="isLoading = $event"
        (onSelectLineEmit)="onSelectLine($event)">
      </app-production-line-view-graph>
    </div>
  </div>

  <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px">
    <nz-badge [nzCount]="notificationMessageCount" [nzOverflowCount]="999" nzSize="small" [nzOffset]="[0, 8]">
      <button nz-button [nzLoading]="false" class="badge-box" (click)="showNotiModal()">
        <i nz-icon [nzIconfont]="'icon-xiaoxitongzhi'" style="font-size: 20px"></i>
      </button>
    </nz-badge>
    <nz-pagination
      [(nzPageIndex)]="pageConfig.page"
      [nzTotal]="pageConfig.total"
      [nzSize]="'small'"
      [nzPageSize]="20"
      [nzPageSizeOptions]="[20, 30, 40, 50]"
      nzShowSizeChanger
      (nzPageIndexChange)="onPageIndexChange()"
      (nzPageSizeChange)="onPageSizeChange($event)"
      [nzShowTotal]="totalTemplate"></nz-pagination>
    <ng-template #totalTemplate let-total
      >共{{ pageConfig.total }}条,第<span style="color: #0f86f8">{{ pageConfig.page }}</span> /
      {{ pageConfig.total / pageConfig.size | flcMathCeil }} 页</ng-template
    >
  </div>
</div>

<app-message-notification-drawer
  #appMessageNotificationDrawer
  (onHideDrawer)="onHideDrawer()"
  (onAcceptAutoAdjustPlan)="onAcceptAutoAdjustPlan($event)"
  (onSearchWithParams)="searchWithParams($event)"></app-message-notification-drawer>
