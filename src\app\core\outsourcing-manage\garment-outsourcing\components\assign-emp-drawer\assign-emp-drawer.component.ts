import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';

import { FlcDrawerHelperService, FlcValidatorService, resizable } from 'fl-common-lib';
import { NzMessageService } from 'ng-zorro-antd/message';

import { AssignTypeEnum } from '../../models/order-garment-outsourcing.enum';
import { OrderGarmentOutsourcingService } from '../../order-garment-outsourcing.service';
import { NzNotificationService } from 'ng-zorro-antd/notification';

@resizable()
@Component({
  selector: 'flss-assign-emp-drawer',
  templateUrl: './assign-emp-drawer.component.html',
  styleUrls: ['./assign-emp-drawer.component.scss'],
  providers: [OrderGarmentOutsourcingService],
})
export class AssignEmpDrawer implements OnInit {
  @Input() assignType!: AssignTypeEnum;
  @Input() list: any[] = [];
  @Input() batchConfig: any[] = []; // 上方批量赋值的字段
  @Input() tableHeaders: any[] = [];
  @Input() departmentOptions: any[] = [];

  flcTableConfig = {
    dataList: <any[]>[],
    tableName: '',
    count: 0,
    height: 400,
    loading: false,
    pageSize: 20,
    pageIndex: 1,
    translateName: '',
    detailBtn: false,
    actionWidth: '80px',
    hasCheckbox: false,
    hasAction: true,
    canSetHeader: false,
  };

  assignForm: FormGroup = this._fb.group({
    lines: this._fb.array([]),
  });

  get lines() {
    return this.assignForm.get('lines') as FormArray;
  }

  constructor(
    private _fb: FormBuilder,
    private _msg: NzMessageService,
    private _service: OrderGarmentOutsourcingService,
    private _cdr: ChangeDetectorRef,
    private _drawer: FlcDrawerHelperService,
    private _flcValidator: FlcValidatorService,
    private _notify: NzNotificationService
  ) {}

  ngOnInit() {
    this.flcTableConfig.dataList = this.list;
    this.initFormLines();
    (this as any).addResizePageListener();
  }

  ngAfterViewInit(): void {
    this.resizePage();
  }

  ngOnDestroy() {
    (this as any).addResizePageListener();
  }

  resizePage() {
    setTimeout(() => {
      const drawerHeight = document.querySelector('.ant-drawer-body')?.clientHeight || 0;
      let _height = drawerHeight - 150;
      if (_height < 200) {
        _height = 200;
      }
      this.flcTableConfig = { ...this.flcTableConfig, height: _height };
    }, 10);
  }

  // 找出对应的项
  findItemByUserId(arr: any[] = [], userId = 0) {
    for (let i = 0; i < arr.length; i++) {
      if (arr[i].user_id === userId) {
        return arr[i];
      }
      if (arr[i].children && arr[i].children.length > 0) {
        const result: any = this.findItemByUserId(arr[i].children, userId);
        if (result) {
          return result;
        }
      }
    }
    return null;
  }

  getTreeKey(user_id: number) {
    const ss = this.findItemByUserId(this.departmentOptions, user_id);
    return ss?.key ?? null;
  }

  // 渲染表单行
  // 负责人存user_id(原逻辑)， 跟单员/QC是key
  private initFormLines() {
    if (this.assignType === AssignTypeEnum.AssignEmployee) {
      this.list.forEach((item, index) => {
        const _group = this._fb.group({
          id: [item?.id],

          employee_id: [item.employee_id ? `employ-${this.getTreeKey(item.employee_id)}` : null, [Validators.required]], // 用于回显
          employee_user_id: [item.employee_id ?? null],
          employee_name: [item.employee_name], // 负责人
        });
        this.lines.insert(index, _group);
      });
    } else {
      this.list.forEach((item, index) => {
        const _group = this._fb.group({
          id: [item?.id],

          factory_out_sourcing_id: [item?.factory_out_sourcing_id],
          factory_id: [item?.factory_id],

          merchandiser_id: [item.merchandiser_id ? `employ-${item.merchandiser_id}` : null],
          merchandiser_name: [item.merchandiser_name], // 跟单员
          merchandiser_user_id: [item.merchandiser_user_id ?? null],

          qc_id: [item.qc_id ? `employ-${item.qc_id}` : null],
          qc: [item.qc], // QC
          qc_user_id: [item.qc_user_id ?? null],
        });
        this.lines.insert(index, _group);
      });
    }
  }

  // 更改负责人/跟单员/QC
  onEmployeeChange(data: any, index: number | null, item: any) {
    if (index != null) {
      const group = this.lines.controls[index];
      group.get(item.key)?.setValue(data?.key ?? null, { emitEvent: false });
      group.get(item.labelKey)?.setValue(data?.title ?? null, { emitEvent: false });
      group.get(item.valueKey)?.setValue(data?.user_id ?? null, { emitEvent: false });
      group.get(item.key)?.updateValueAndValidity();
      return;
    } else {
      if (!data) return;
      // 批量更换
      this.lines.controls.forEach((group: AbstractControl) => {
        group.get(item.key)?.setValue(data?.key ?? null, { emitEvent: false });
        group.get(item.labelKey)?.setValue(data?.title ?? null, { emitEvent: false });
        group.get(item.valueKey)?.setValue(data?.user_id ?? null, { emitEvent: false });
        if (this.assignType === AssignTypeEnum.AssignEmployee) {
          group.get(item.key)?.updateValueAndValidity();
        }
      });
    }
  }

  // 删除行
  handleDeleteRow(i: number) {
    this.lines?.removeAt(i);
    this.flcTableConfig?.dataList?.splice(i, 1);
    this._cdr.detectChanges();
  }

  // 提交参数
  getPayload() {
    const value = this.lines.getRawValue();
    return value.map((e: any) => {
      return {
        ...e,
        employee_id: e.employee_user_id ?? null, // 负责人传user_id

        merchandiser_id: e.merchandiser_id?.length ? Number(e.merchandiser_id.split('-').pop()) : null, // 跟单员传key
        qc_id: e.qc_id?.length ? Number(e.qc_id.split('-').pop()) : null, // QC传key
      };
    });
  }

  // 确定
  async onOk() {
    const list = this.getPayload();
    // 指派负责人
    if (this.assignType === AssignTypeEnum.AssignEmployee) {
      const invalid = await this._flcValidator.formIsAsyncInvalid(this.assignForm);
      if (invalid) {
        this._notify.error('请完善负责人', '');
        return;
      }
      const employeeParams = list?.map((item: any) => {
        return {
          io_id: item?.id,
          employee_id: item?.employee_id ?? null,
        };
      });
      this._service.assigneeEmployee({ data: employeeParams }).subscribe((res: any) => {
        if (res?.code === 200) {
          if (res.data?.msg?.length) {
            this._msg.error(res.data.msg);
          } else {
            this._msg.success('指派成功');
          }
          this._drawer.closeDrawer({ isSave: true });
        }
      });
    } else {
      // 指派跟单员/QC传参
      const params = list?.map((item: any) => {
        return {
          factory_out_sourcing_id: item?.factory_out_sourcing_id,
          merchandiser_id: item?.merchandiser_id,
          merchandiser_name: item?.merchandiser_name,
          qc_id: item?.qc_id,
          qc: item?.qc,
          merchandiser_user_id: item?.merchandiser_user_id,
          qc_user_id: item?.qc_user_id,
          factory_id: item?.factory_id,
        };
      });
      this._service.assigneeMerchandiser_QC({ data: params }).subscribe((res: any) => {
        if (res.data?.msg?.length) {
          this._msg.error(res.data.msg);
        } else {
          this._msg.success('指派成功');
        }
        this._drawer.closeDrawer({ isSave: true });
      });
    }
  }

  // 取消
  onCancel() {
    this._drawer.closeAllDrawer();
  }
}
