<ng-container>
  <div class="search-container" #headerBox>
    <div class="search-content">
      <ng-container *ngIf="isFold">
        <ng-container *ngIf="isHeaderTitleString">
          <span class="search-title">{{ _headerTitle }}</span>
        </ng-container>
        <ng-container *ngIf="!isHeaderTitleString">
          <ng-template *ngTemplateOutlet="_headerTplTitle"></ng-template>
        </ng-container>
      </ng-container>
      <ng-container *ngIf="!isFold">
        <ng-content></ng-content>
      </ng-container>
    </div>
    <div class="btn-container">
      <button nz-button class="reset-btn" nz-tooltip [nzTooltipTitle]="resetTooltip" (click)="reset.emit()">
        <i nz-icon [nzIconfont]="'icon-zhongzhi1'"></i>
      </button>
      <button
        *ngIf="showFoldBtn"
        nz-button
        [ngClass]="{ 'filter-btn': isFold, 'filter-btn-active': !isFold }"
        nz-tooltip
        [nzTooltipPlacement]="toolPlace"
        [nzTooltipTitle]="foldTooltip"
        (click)="fold()">
        <i nz-icon [nzIconfont]="'icon-shaixuan-xuanzhong'"></i>
      </button>
      <ng-template *ngTemplateOutlet="btnTpl"></ng-template>
    </div>
  </div>
</ng-container>
