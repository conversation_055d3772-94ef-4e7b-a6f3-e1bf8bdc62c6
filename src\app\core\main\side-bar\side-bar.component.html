<!-- <ul style="padding-bottom: 40px" nz-menu #menuDom nzMode="inline" [nzInlineCollapsed]="isCollapsed" [nzInlineIndent]="16">
  <ng-container *ngFor="let sub of menu">
    <li
      nz-submenu
      *ngIf="sub['value']"
      [nzOpen]="sub.isOpen"
      [ngClass]="{ CollapsedSpan: isCollapsed }"
      (nzOpenChange)="onOpenChange($event, sub)">
      <span title>
        <span>{{ sub['name'] }}</span>
        <span class="sub-num" [ngClass]="{ 'double-num': getSubMenuCount(sub) > 9 }">{{ getSubMenuCount(sub) }}</span>
      </span>
      <ul>
        <ng-container *ngFor="let leaf of sub['children']">
          <li nz-menu-item *ngIf="leaf.value" [routerLink]="leaf.path" [nzMatchRouter]="true">
            {{ leaf.name }}
          </li>
        </ng-container>
      </ul>
    </li>
  </ng-container>
</ul> -->
<ul id="newMenu" #menuDom>
  <ng-container *ngFor="let first of menu">
    <li
      *ngIf="first.value"
      class="firstLevelMenu"
      [ngClass]="{ selected: first.isCurrent }"
      (mouseenter)="enterMenu(first, $event)"
      (mouseleave)="leaveMenu()">
      <div class="menuWrapper">
        <i class="menuIcon" nz-icon [nzIconfont]="first.icon"></i>
        <div [ngClass]="lang === 'en' ? 'menuNameEn' : ''">{{ first.name }}</div>
      </div>
    </li>
  </ng-container>
</ul>
