import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { RoleNameChildModel } from '../../role-manager.model';

@Component({
  selector: 'app-role-line-last-menu-range-line',
  templateUrl: './role-line-last-menu-range-line.component.html',
  styleUrls: ['./role-line-last-menu-range-line.component.scss'],
})
export class RoleLineLastMenuRangeLineComponent implements OnInit {
  @Input() child!: RoleNameChildModel;
  @Input() isSaveError!: boolean;
  @Input() isEdit!: boolean;
  @Input() showALLDetail!: boolean;
  @Input() allDepartmentList: { id: number; name: string }[] = [];
  @Output() statusChange = new EventEmitter<boolean>();
  @Output() checkedChange = new EventEmitter<void>();

  translateName = 'roleMgt.';
  constructor() {}

  canConfigRange = true;
  nonRangeConfigKey: string[] = [
    'bulk-material-preparation',
    'material-preparation',
    'material-preparation-report',
    'prototyping',
    // 'bulkorder', // 物料采购计划
    'other',
    'material-procurement:order',
    'autocheck-price',
    'sys-parameters',
    'in-stock',
    'out-stock',
    'stock-on-hand',
    'quote-price-upstream',
    'delivery-plan:packing-list',
    'material-settlement',
    'manufacture-settlement',
    'material-payment-request',
    'manufacture-payment-request',
    'payable-report',
    'settlement-adjustment',
    'material_manage',
    'garment_check',
    'coop:material-delivery-plan',
    'coop:material-tag',
    'coop:sending-material',
    'coop:returning-material',
    'material:material-delivery-plan',
    'material:receiving-material',
    'basic-archive:supplier-access', // 供应商准入
    'basic-archive:supplier-evaluation-scm', // 供应商考核
    'basic-archive:supplier-style', // 供应商类型
    'basic-archive:supplier-evaluation-criteria', // 供应商评估标准
    'coop:supplier-evaluation-collab', // 供应商绩效
    'basic-archive:dict', //字典
    'product-plan-manage:design-plan', // 设计企划
    'invoice-management', // 发票管理
    'material-procurement:fabric-analysis-report', // 面料分析报表，
    'dashboard-supplychain', // 供应链协同数据大屏
  ];
  ngOnInit(): void {
    for (const key of this.nonRangeConfigKey) {
      if (this.child.code.endsWith(key)) {
        this.canConfigRange = false;
        break;
      }
    }
  }
  getDepartmentName(id: number): string | undefined {
    return this.allDepartmentList.find((x) => x.id === id)?.name;
  }
  typeChanged(type: number) {
    if (type !== 4) {
      this.child.payload.data_range = [];
    }
  }
}
