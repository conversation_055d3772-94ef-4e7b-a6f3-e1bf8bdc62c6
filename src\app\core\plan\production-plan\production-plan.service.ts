import { Observable } from 'rxjs';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { subDays, addMonths, addDays, differenceInMinutes, startOfMinute } from 'date-fns';
import { DimensionType, PlanGraphItem } from './production-plan';
import { isNil } from 'lodash';

const graphLineHeight = 40;
const graphItemHeight = 20;

@Injectable()
export class ProductionPlanService {
  btnArr: string[] = [];
  searchData = {
    factory_id: null,
    order_code: null,
    po_code: null,
    style_code: null,
    date: [new Date(), addMonths(new Date(), 1)],
    start_date: subDays(new Date(), 15),
    end_date: addDays(addMonths(new Date(), 1), 15),
  };
  private dimension: DimensionType = 'io';

  constructor(private _http: HttpClient) {}

  // 获取显示维度
  getProOrderDimension(): DimensionType {
    return this.dimension;
  }

  // 设置显示维度
  setProOrderDimension(dimension: DimensionType) {
    this.dimension = dimension;
  }

  getOptions(): Observable<any> {
    return this._http.get<any>('/service/plan/v1/search_date');
  }

  getAllFactory(): Observable<any> {
    return this._http.get<any>('/service/plan/v1/factory/list');
  }

  getRangeTime(payload: { order_code: string | null; style_code: string | null }): Observable<any> {
    return this._http.post<any>('/service/plan/v1/search/time', payload);
  }

  getScheduleList(payload: { start_date: number; end_date: number }): Observable<any> {
    return this._http.post<any>('/service/plan/v1/factory/display', payload);
  }

  getLineDetail(payload: { order_codes: string[] }): Observable<any> {
    return this._http.post<any>('/service/plan/v1/order/plan/list', payload);
  }

  /**
   * 获取颜色设置列表
   * @param  {} payload
   * @returns Observable
   */
  getSettingColor(payload: any): Observable<any> {
    return this._http.post('/service/archive/v1/style/sam_color_list', payload);
  }

  createHttpParams(payload: any): HttpParams {
    let params = new HttpParams();
    Object.keys(payload).forEach((key: string) => {
      if (!isNil(payload[key]) && String(payload[key]).trim() !== '') {
        params = params.append(key, payload[key]);
      }
    });
    return params;
  }

  /** 计算每个graph位置 */
  calcGraphPosition(item: PlanGraphItem, signalWidth: number, startDate: Date, isHourMode: boolean): void {
    item.top = (graphLineHeight - graphItemHeight) / 2;
    const startTime = startOfMinute(new Date(item.start_time));
    const endTime = startOfMinute(new Date(item.end_time));
    const offset = differenceInMinutes(startTime, startDate);
    const width = differenceInMinutes(endTime, startTime);
    item.left = (offset / 60 / 24) * signalWidth * (isHourMode ? 12 : 1);
    item.width = (width / 60 / 24) * signalWidth * (isHourMode ? 12 : 1);
  }

  /**
   * 将每个甘特的顺序排一遍
   * 排完是个二维数组，分多行展示
   * @param arr
   */
  sort(arr: any[]) {
    const resArr: any[] = [];
    arr.forEach((element) => {
      this.pushFF(resArr, 0, element);
    });
    return resArr;
  }

  /**
   * 将一条甘特图放到二维数组中
   * @param sqArr 存甘特图的二维数组
   * @param index 在二维数组的index处插入甘特图数据
   * @param dict 要插入的甘特图数据
   */
  pushFF(sqArr: any[], index: number, dict: any) {
    if (!(sqArr.length > index)) {
      sqArr.push([]);
    }
    if (!sqArr[index].length) {
      sqArr[index].push(dict);
      return;
    }
    for (let i = 0; i < sqArr[index].length; i++) {
      if (i === 0) {
        if (dict.left + dict.width <= sqArr[index][i].left) {
          sqArr[index].unshift(dict);
          return;
        }
      }
      if (dict.left >= sqArr[index][i].left + sqArr[index][i].width) {
        if (i + 1 < sqArr[index].length) {
          if (dict.left + dict.width <= sqArr[index][i + 1].left) {
            sqArr[index].splice(i + 1, 0, dict);
            return;
          }
        } else {
          sqArr[index].push(dict);
          return;
        }
      }
    }
    this.pushFF(sqArr, index + 1, dict);
  }
}
