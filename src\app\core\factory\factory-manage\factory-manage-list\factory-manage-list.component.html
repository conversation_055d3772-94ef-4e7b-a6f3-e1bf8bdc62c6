<app-search-container #searchHeader [headerTitle]="header_title" [btnTpl]="btnTpl" (reset)="reset()" (handleFold)="resize()">
  <div>
    {{ '公司名称' | translate }}:
    <app-dynamic-search-select [(ngModel)]="searchData.name" [column]="'name'" [dataUrl]="'/factory/search'" (ngModelChange)="onSearch()">
    </app-dynamic-search-select>
  </div>
  <div>
    {{ '公司缩写' | translate }}:
    <app-dynamic-search-select [(ngModel)]="searchData.abbr" [column]="'abbr'" [dataUrl]="'/factory/search'" (ngModelChange)="onSearch()">
    </app-dynamic-search-select>
  </div>
  <div>
    {{ '地区' | translate }}:
    <nz-cascader
      [nzPlaceHolder]="placeSelect"
      [(ngModel)]="searchData.region_id"
      [nzOptions]="searchOptions.regionOptions"
      [nzShowSearch]="true"
      nzAllowClear
      (ngModelChange)="onSearch()">
    </nz-cascader>
  </div>
  <div>
    {{ '状态' | translate }}:
    <nz-select
      [(ngModel)]="searchData.status"
      [nzPlaceHolder]="placeSelect"
      [nzDropdownMatchSelectWidth]="false"
      (ngModelChange)="onSearch()"
      nzShowSearch
      nzAllowClear
      style="min-width: 96px">
      <ng-container *ngFor="let item of searchOptions.statusOptions">
        <nz-option [nzValue]="item.value" [nzLabel]="item.label"></nz-option>
      </ng-container>
    </nz-select>
  </div>
  <div>
    {{ '创建人' | translate }}:
    <app-dynamic-search-select
      [(ngModel)]="searchData.gen_user_id"
      [column]="'gen_user'"
      [dataUrl]="'/factory/search'"
      [transData]="{ value: 'id', label: 'name' }"
      (ngModelChange)="onSearch()">
    </app-dynamic-search-select>
  </div>
  <div>
    {{ '创建日期' | translate }}:
    <nz-range-picker [(ngModel)]="searchData.gen_time" (ngModelChange)="onSearch()"> </nz-range-picker>
  </div>
</app-search-container>
<ng-template #btnTpl>
  <button
    nz-button
    flButton="pretty-primary"
    nzShape="round"
    (click)="addFactory()"
    *ngIf="_service.btnArr.includes('settings:factoryManage-create')">
    <i nz-icon [nzIconfont]="'icon-xinjian1'"></i>
    {{ 'btn.add' | translate }}
  </button>
</ng-template>

<div style="margin-top: 8px">
  <nz-table
    class="factory-table zebra-striped-table"
    [nzFrontPagination]="false"
    [nzLoading]="loading"
    [(nzPageIndex)]="pageIndex"
    [(nzPageSize)]="pageSize"
    [nzShowTotal]="totalTemplate"
    [nzTotal]="total"
    (nzPageIndexChange)="getFactoryList()"
    (nzPageSizeChange)="onSearch()"
    #nzTable
    [nzScroll]="{ x: '1000px', y: tableHeight + 'px' }"
    [nzData]="datalist"
    [nzPageSizeOptions]="[20, 30, 40, 50]"
    nzShowSizeChanger
    nzSize="middle"
    nzBordered="ture">
    <thead>
      <tr>
        <th nzLeft nzWidth="32px">#</th>
        <ng-container *ngFor="let item of renderHeaders">
          <th
            [nzLeft]="item.disable || item.pinned"
            nz-resizable
            *ngIf="item.visible"
            nzPreview
            [nzWidth]="item.width"
            [nzMaxWidth]="360"
            [nzMinWidth]="56"
            (nzResizeEnd)="onResize($event, item.label)">
            <div style="display: flex; align-items: center; justify-content: center; gap: 10px">
              {{ item.label | translate }}
              <app-sort-btn *ngIf="item.sort" style="padding-bottom: 5px" (sortOrderChange)="onSort($event)"></app-sort-btn>
            </div>

            <nz-resize-handle nzDirection="right">
              <div class="resize-trigger"></div>
            </nz-resize-handle>
          </th>
        </ng-container>
        <th nzWidth="90px" nzRight>
          {{ '操作' | translate }}
          <a class="setting-btn" [ngStyle]="{ color: btnHighLight ? '#4D96FF' : '' }" (click)="changeHeader($event)">
            <i nz-icon nzType="setting" nzTheme="fill"></i>
          </a>
        </th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let item of nzTable.data; let i = index">
        <td [ngStyle]="{ background: item.id === id ? '#EEF6FF' : '' }" nzLeft>{{ i + 1 }}</td>
        <ng-container *ngFor="let col of renderHeaders">
          <td [nzLeft]="col.disable || col.pinned" [ngStyle]="{ background: item.id === id ? '#EEF6FF' : '' }" *ngIf="col.visible">
            <component-table-body-render
              [width]="col.width"
              [data]="item[col.key]"
              [type]="col.type"
              [template]="getTemplate(col.template, i)">
            </component-table-body-render>
          </td>
        </ng-container>
        <td [ngStyle]="{ background: item.id === id ? '#EEF6FF' : '' }" nzRight>
          <a nz-button nzType="link" (click)="detail(item.id)" *ngIf="_service.btnArr.includes('settings:factoryManage-detail')">
            {{ '详情' | translate }}
          </a>
        </td>

        <ng-template #regions>
          <ng-container *ngIf="item?.region?.province?.name; else noRegion">
            {{ item?.region?.province?.name }}{{ item?.region?.city?.name }}
          </ng-container>
          <ng-template #noRegion>
            {{ '-' }}
          </ng-template>
        </ng-template>

        <ng-template #statusTpl>
          <div
            [ngStyle]="{
              color: [factoryStatusEnum.APPROVE_PENDING, factoryStatusEnum.DEPLOY_PENDING, factoryStatusEnum.INIT_PENDING].includes(
                item.status
              )
                ? '#FF912A'
                : '#222B3C'
            }">
            {{ 'factory-status.' + item.status | translate | noValue }}
          </div>
        </ng-template>
      </tr>
    </tbody>
  </nz-table>
</div>

<ng-template #totalTemplate>
  <div style="margin-right: 16px; color: #000000; font-size: 14px">
    共 {{ total }} 条， 第 <span style="color: #0f86f8">{{ pageIndex }}</span> / {{ total / pageSize | mathCeil }} 页
  </div>
</ng-template>
