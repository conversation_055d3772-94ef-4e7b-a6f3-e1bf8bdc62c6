import { NgModule } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import {
  FlcComponentsModule,
  FlcDirectivesModule,
  FlcDrawerHelperService,
  FlcOssUploadService,
  FlcPipesModule,
  FlcTableHelperModule,
} from 'fl-common-lib';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';

import { ProductionReportComponentsModule } from '../components/components.module';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { ProductionTableComponent } from './list/production-table.component';
import { ProductionTableService } from './production-table.service';
import { ProductionTableRoutingModule } from './production-table-routing.module';
import { NzMessageModule, NzMessageService } from 'ng-zorro-antd/message';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzCascaderModule } from 'ng-zorro-antd/cascader';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { ProductionTableColorSpecDialogComponent } from './production-table-color-spec-dialog/production-table-color-spec-dialog.component';
import { FlButtonModule } from 'fl-ui-angular';
import { FlUiAngularModule } from 'fl-ui-angular';
import { MaterialDialogComponent } from './component/material-dialog/material-dialog.component';
import { ProductionTableNewColorSpecDialogComponent } from './production-table-new-color-spec-dialog/production-table-new-color-spec-dialog.component';
import { ProductionTableMaterialDialogComponent } from './production-table-material-dialog/production-table-material-dialog.component';
import { ProductionTableInspectionDialogComponent } from './production-table-inspection-dialog/production-table-inspection-dialog.component';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { BatchEditFieldComponent } from './component/batch-edit-field/batch-edit-field.component';
import { EditNodeFormComponent } from './component/edit-node-form/edit-node-form.component';
import { NzBadgeModule } from 'ng-zorro-antd/badge';
import { MessageNotificationComponent } from './component/message-notification/message-notification.component';
import { NzPopoverModule } from 'ng-zorro-antd/popover';

const nzModules = [
  NzTableModule,
  NzIconModule,
  NzButtonModule,
  NzDatePickerModule,
  NzModalModule,
  NzCascaderModule,
  NzInputModule,
  NzMessageModule,
  NzDrawerModule,
  NzFormModule,
  NzInputNumberModule,
  NzSwitchModule,
  NzDividerModule,
  NzSelectModule,
  NzSpinModule,
  NzToolTipModule,
  NzCheckboxModule,
  NzDividerModule,
  NzTabsModule,
  NzBadgeModule,
  NzPopoverModule,
];

@NgModule({
  declarations: [
    ProductionTableComponent,
    ProductionTableColorSpecDialogComponent,
    MaterialDialogComponent,
    ProductionTableNewColorSpecDialogComponent,
    ProductionTableMaterialDialogComponent,
    ProductionTableInspectionDialogComponent,
    BatchEditFieldComponent,
    EditNodeFormComponent,
    MessageNotificationComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    FlcDirectivesModule,
    FlcComponentsModule,
    FlcPipesModule,
    FlcTableHelperModule,
    FlButtonModule,
    FlUiAngularModule,
    ProductionTableRoutingModule,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new MultiTranslateHttpLoader(http, [{ prefix: './assets/i18n/production-report/production-table/', suffix: '.json' }]);
        },
        deps: [HttpClient],
      },
    }),
    ProductionReportComponentsModule,
    ...nzModules,
  ],
  providers: [ProductionTableService, NzMessageService, FlcDrawerHelperService, FlcOssUploadService, DatePipe],
})
export class productionTableModule {
  constructor(
    public translateService: TranslateService,
    private _service: ProductionTableService,
    public ossUploadService: FlcOssUploadService
  ) {
    const cl = translateService.currentLang;
    translateService.currentLang = '';
    translateService.use(cl).subscribe(() => {
      this._service.translateEventEmitter.next();
    });
    // oss upload service模块级别配置信息
    ossUploadService.setOssNamespace('production-report');
    ossUploadService.setOssAuthUrl('/service/frontapi/v1/oss');
    ossUploadService.setOssAuthParams({
      biz_type: 'order',
    });
    const userInfo: string | null = localStorage.getItem('userInfo');
    if (userInfo) {
      const user = JSON.parse(userInfo);
      ossUploadService.setFilePath(`scm/production-report/${user?.factory_code}`);
    }
  }
}
