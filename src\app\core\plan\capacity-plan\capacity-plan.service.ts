import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ResponseData, ResponseDataAny, ResponseDataList } from '../../common/interface/http';
import {
  CapacityFilterList,
  CapacityGraph,
  CapacityGraphParam,
  CapacityList,
  CapacityListParam,
  EditSamParam,
} from './capacity-plan.interface';

@Injectable({
  providedIn: 'root',
})
export class CapacityPlanService {
  factories: { factory_id: number; factory_name: string }[] = [];

  constructor(private _http: HttpClient) {}

  /**
   * 待分配订单IO列表
   * @param payload
   * @returns
   */
  ioList(payload: CapacityListParam) {
    return this._http.post<ResponseData<{ order_infos?: CapacityList[]; po_infos?: CapacityList[]; total: number }>>(
      '/service/plan/v1/not-out/io/list',
      payload
    );
  }

  poList(payload: CapacityListParam) {
    return this._http.post<ResponseData<{ order_infos?: CapacityList[]; po_infos?: CapacityList[]; total: number }>>(
      '/service/plan/v1/not-out/po/list',
      payload
    );
  }

  filterList() {
    return this._http.get<ResponseData<CapacityFilterList>>('/service/plan/v1/order-code/list');
  }

  capacityList(payload: CapacityGraphParam) {
    return this._http.post<ResponseData<{ factories: CapacityGraph[] }>>('/service/plan/v1/factory/capacity/list', payload);
  }

  editSam(payload: EditSamParam) {
    return this._http.put<ResponseDataAny>('/service/scm/style_lib/update_sam', payload);
  }
}
