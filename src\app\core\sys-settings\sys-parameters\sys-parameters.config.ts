type FieldType =
  | 'input'
  | 'input-number'
  | 'simple-radio-group'
  | 'valueLabel-radio-group'
  | 'template'
  | 'local-multiple-select'
  | 'text'
  | 'input-only';
const _template = {
  // 后端保存的对应字段
  // SettingKeyBomPurchaseDefaultType bom资料包采购类型配置, 1:自购 2:客供 3:厂供
  SettingKeyBomPurchaseDefaultType: 'bom_purchase_default_type',
  // SettingKeyPriceEvaluationAutoGenTime 打样核价自动生成时间, 1:打样审核通过后 2:样衣评审通过后
  SettingKeyPriceEvaluationAutoGenTime: 'price_evaluation_auto_gen_time',
  // SettingKeyPriceEvaluationAutoGenRule 打样核价自动生成规则, 1:一个款式只自动生成一次 2:每个打样单均自动生成 3:根据样板类型生成
  SettingKeyPriceEvaluationAutoGenRule: 'price_evaluation_auto_gen_rule',
  // SettingKeySampleTypeAutoGenRule 打样核价自动生成规则, 根据样板类型生成时的样板类型id, sample_type表的id
  SettingKeySampleTypeAutoGenRule: 'sample_type_auto_gen_rule',
  // SettingKeyMaterialProcurementProcedure 物料采购流程, 1:物料采购计划->物料采购需求->采购单 2:物料采购计划->采购单 3 物料采购需求 -> 采购单
  SettingKeyMaterialProcurementProcedure: 'material_procurement_procedure',
  // SettingKeyArrivalWarnDays 物料入库预警时间, 在预计入库时间前n天
  SettingKeyArrivalWarnDays: 'arrival_warn_days',
  // SettingKeyInventoryIsCylinderVolume 物料库存面料是否维护缸号卷号
  SettingKeyInventoryIsCylinderVolume: 'inventory_is_cylinder_volume',
};
interface BaseConfigType {
  type: FieldType;
  titleKey: string;
  fieldKey: string;
  display: boolean;
}

interface BaseInputConfigType {
  inputLabelKey?: string;
  inputSuffixKey?: string;
}

interface InputConfigType extends BaseConfigType, BaseInputConfigType {
  type: 'input' | 'input-only';
  maxLength?: number;
}

interface InputNumberConfigType extends BaseConfigType, BaseInputConfigType {
  type: 'input-number';
  precision: number;
  max: number;
  min: number;
}

interface SimpleRadioGroupConfigType extends BaseConfigType {
  type: 'simple-radio-group';
  oneItem: string;
  twoItem: string;
}
interface ValueLabelRadioGroupConfigType extends BaseConfigType {
  type: 'valueLabel-radio-group';
  options: { value: any; label: string }[];
}
interface TemplateConfigType extends BaseConfigType {
  type: 'template';
  template: string;
}

interface LocalMultiSelectConfigType extends BaseConfigType {
  type: 'local-multiple-select';
  options: { value: any; label: string }[];
}

interface TextConfigType extends Partial<BaseConfigType>, BaseInputConfigType {
  type: 'text';
  titleKey: string;
}
type SysParamsCategoryKey =
  | 'materialInventory'
  | 'dataPackage'
  | 'checkPrice'
  | 'materialPurchase'
  | 'materialInspection'
  | 'sampleTask'
  | 'sampleAdjustment'
  | 'styleCenter'
  | 'designDraft'
  | 'merchandiseValuation'
  | 'order'
  | 'materialProcurement'
  | 'materialProcurement2'
  | 'elanDashboard';

interface SysParamsConfig {
  titleKey: SysParamsCategoryKey | string;
  subKey?: string;
  children: Array<
    | InputConfigType
    | InputNumberConfigType
    | SimpleRadioGroupConfigType
    | ValueLabelRadioGroupConfigType
    | TemplateConfigType
    | LocalMultiSelectConfigType
    | TextConfigType
  >;
}

const _sysParamsFieldConfig: { [key in SysParamsCategoryKey]: string[] } = {
  // 物料库存
  materialInventory: ['arrival_warn_days', 'inventory_is_cylinder_volume'],
  // bom
  dataPackage: ['bom_purchase_default_type', 'bom_create_quickly', 'bom_required_property', 'bom_accessory_required_property'],
  // 核价
  checkPrice: ['price_evaluation_auto_gen_time', 'price_evaluation_auto_gen_rule', 'sample_type_auto_gen_rule'],
  // 物料采购
  materialPurchase: ['material_procurement_procedure', 'gen_procurement_by_layout'],
  // 物料质检
  materialInspection: ['material_inspection_procedure'],
  // 打样任务
  sampleTask: [
    'overdue_hours',
    'complete_time_calculate_type',
    'sample_task_create_quickly',
    'sample_auto_inventory_node',
    'sample_order_cancel_rule',
  ],
  // 调样需求
  sampleAdjustment: ['sample_adjustment_order_status'],
  // 款式库
  styleCenter: ['style_center_create_quickly', 'generating_proofing_sheets_from_style_files_rules'],
  // 设计图稿
  designDraft: ['design_draft_create_quickly'],
  // 商品定价
  merchandiseValuation: ['merchandise_valuation'],
  // 订单
  order: ['order-distribution-force-factory', 'plan-distribution-force-factory'],
  // 物料采购单
  materialProcurement: ['delivery_dimension', 'delivery_is_required', 'delivery_qty_is_required'],
  materialProcurement2: [
    'material-procurement-type-delivery-dimension',
    'material-procurement-type-delivery-is-required',
    'material-procurement-type-num-is-required',
  ],
  elanDashboard: [],
};

export const getSysParamsFieldConfig = (): { [key in SysParamsCategoryKey]: string[] } => {
  return JSON.parse(JSON.stringify(_sysParamsFieldConfig));
};

const _sysParamsConfig: Array<SysParamsConfig> = [
  {
    // 资料包
    titleKey: 'dataPackage',
    children: [
      {
        type: 'valueLabel-radio-group',
        options: [
          { value: '1', label: '自购' },
          { value: '2', label: '客供' },
          { value: '3', label: '厂供' },
        ],
        titleKey: 'datePackageTitle',
        fieldKey: _sysParamsFieldConfig.dataPackage[0],
        display: true,
      },
      // {
      //   type: 'template',
      //   template: 'placeholder',
      //   titleKey: '',
      //   fieldKey: '',
      //   display: true,
      // },
      {
        type: 'simple-radio-group',
        titleKey: '快捷创建',
        oneItem: '启用',
        twoItem: '禁用',
        fieldKey: _sysParamsFieldConfig.dataPackage[1],
        display: true,
      },
      {
        type: 'local-multiple-select',
        titleKey: '面料必填字段',
        fieldKey: _sysParamsFieldConfig.dataPackage[2],
        display: true,
        options: [
          { value: 'part', label: '部位' },
          { value: 'material_type', label: '面料类型' },
          { value: 'material_color', label: '物料颜色' },
        ],
      },
      {
        type: 'local-multiple-select',
        titleKey: '辅料必填字段',
        fieldKey: _sysParamsFieldConfig.dataPackage[3],
        display: true,
        options: [
          { value: 'part', label: '部位' },
          { value: 'material_color', label: '物料颜色' },
        ],
      },
    ],
  },
  {
    // 款式库
    titleKey: 'styleCenter',
    children: [
      {
        type: 'simple-radio-group',
        titleKey: '快捷创建',
        oneItem: '启用',
        twoItem: '禁用',
        fieldKey: _sysParamsFieldConfig.styleCenter[0],
        display: true,
      },
      {
        type: 'valueLabel-radio-group',
        titleKey: '款式档案生成打样单规则',
        options: [
          { value: '1', label: '允许换款' },
          { value: '2', label: '锁定款式(不可修改)' },
        ],
        fieldKey: _sysParamsFieldConfig.styleCenter[1],
        display: true,
      },
    ],
  },
  {
    // 商品定价
    titleKey: 'merchandiseValuation',
    children: [
      {
        type: 'simple-radio-group',
        titleKey: 'merchandiseValuation',
        oneItem: '是',
        twoItem: '否',
        fieldKey: _sysParamsFieldConfig.merchandiseValuation[0],
        display: true,
      },
    ],
  },

  {
    // 设计图稿
    titleKey: 'designDraft',
    children: [
      {
        type: 'simple-radio-group',
        titleKey: '快捷创建',
        oneItem: '启用',
        twoItem: '禁用',
        fieldKey: _sysParamsFieldConfig.designDraft[0],
        display: true,
      },
    ],
  },
  {
    // 核价
    titleKey: 'checkPrice',
    children: [
      {
        type: 'valueLabel-radio-group',
        options: [
          { value: '1', label: '打样审核通过后' },
          { value: '2', label: '样衣评审通过后' },
        ],
        titleKey: 'checkPriceTitle1',
        fieldKey: _sysParamsFieldConfig.checkPrice[0],
        display: true,
      },
      {
        type: 'template',
        template: 'checkPriceTemplate',
        titleKey: 'checkPriceTitle2',
        fieldKey: _sysParamsFieldConfig.checkPrice[1],
        display: true,
      },
      {
        type: 'template',
        template: 'checkPriceTemplate',
        titleKey: 'checkPriceTitle2',
        fieldKey: _sysParamsFieldConfig.checkPrice[2],
        display: false,
      },
    ],
  },
  {
    // 物料采购
    titleKey: 'materialPurchase',
    children: [
      {
        type: 'valueLabel-radio-group',
        options: [
          { value: '1', label: '物料采购计划->物料采购需求->采购单' },
          // { value: '2', label: '物料采购计划->采购单' },
          // { value: '3', label: '物料采购需求->采购单' },
        ],
        titleKey: 'materialPurchaseTitle',
        fieldKey: _sysParamsFieldConfig.materialPurchase[0],
        display: true,
      },
      {
        type: 'simple-radio-group',
        titleKey: 'materialRulesByLayoutTitle',
        oneItem: 'item1',
        twoItem: 'item2',
        fieldKey: _sysParamsFieldConfig.materialPurchase[1],
        display: true,
      },
    ],
  },
  {
    // 物料质检
    titleKey: 'materialInspection',
    children: [
      {
        type: 'valueLabel-radio-group',
        options: [
          { value: '1', label: '物料入库后检验' },
          { value: '2', label: '物料入库前检验' },
        ],
        titleKey: 'materialInspectionTitle',
        fieldKey: _sysParamsFieldConfig.materialInspection[0],
        display: true,
      },
    ],
  },
  {
    // 物料库存
    titleKey: 'materialInventory',
    children: [
      // 物料入库预警时间
      {
        type: 'input-number',
        titleKey: 'title1',
        inputLabelKey: 'label1',
        inputSuffixKey: 'suffix1',
        fieldKey: _sysParamsFieldConfig.materialInventory[0],
        precision: 0,
        max: 999999999,
        min: 1,
        display: true,
      },
      // 是否维护缸号卷号
      {
        type: 'simple-radio-group',
        titleKey: 'title2',
        oneItem: 'item1',
        twoItem: 'item2',
        fieldKey: _sysParamsFieldConfig.materialInventory[1],
        display: true,
      },
    ],
  },
  {
    // 打样任务
    titleKey: 'sampleTask',
    children: [
      // 可能超时
      {
        type: 'input-number',
        titleKey: 'title3',
        inputLabelKey: 'label2',
        inputSuffixKey: 'suffix2',
        fieldKey: _sysParamsFieldConfig.sampleTask[0],
        precision: 0,
        max: 999999999,
        min: 1,
        display: true,
      },
      // 计划完成时间
      {
        type: 'simple-radio-group',
        titleKey: 'title4',
        oneItem: 'item3',
        twoItem: 'item4',
        fieldKey: _sysParamsFieldConfig.sampleTask[1],
        display: true,
      },
      {
        type: 'simple-radio-group',
        titleKey: '快捷创建',
        oneItem: '启用',
        twoItem: '禁用',
        fieldKey: _sysParamsFieldConfig.sampleTask[2],
        display: true,
      },
      // 打样自动入库节点
      {
        type: 'valueLabel-radio-group',
        options: [
          { value: '1', label: '样衣评审通过' },
          { value: '3', label: '样衣制作完成' },
          { value: '2', label: '流程结束' },
        ],
        titleKey: '打样自动入库节点',
        fieldKey: _sysParamsFieldConfig.sampleTask[3],
        display: true,
      },
      {
        type: 'valueLabel-radio-group',
        titleKey: '打样单取消规则',
        options: [{ value: '1', label: '完成件数为0时，支持取消打样单' }],
        fieldKey: _sysParamsFieldConfig.sampleTask[4],
        display: true,
      },
    ],
  },
  {
    // 调样需求
    titleKey: 'sampleAdjustment',
    children: [
      {
        type: 'simple-radio-group',
        titleKey: '是否启用调样单',
        oneItem: '是',
        twoItem: '否',
        fieldKey: _sysParamsFieldConfig.sampleAdjustment[0],
        display: true,
      },
    ],
  },
  {
    // 订单
    titleKey: 'order',
    children: [
      {
        type: 'simple-radio-group',
        titleKey: 'title5',
        oneItem: '启用',
        twoItem: '禁用',
        fieldKey: _sysParamsFieldConfig.order[0],
        display: true,
      },
      {
        type: 'simple-radio-group',
        titleKey: 'title6',
        oneItem: '启用',
        twoItem: '禁用',
        fieldKey: _sysParamsFieldConfig.order[1],
        display: true,
      },
    ],
  },
  {
    // 物料采购单
    titleKey: 'materialProcurement',
    subKey: '1. 采购类型: 正单',
    children: [
      {
        type: 'text',
        titleKey: '1. 采购类型: 正单',
        display: true,
      },
      {
        type: 'valueLabel-radio-group',
        options: [
          { value: '1', label: '采购单' },
          { value: '2', label: '物料' },
          { value: '3', label: '订单' },
        ],
        titleKey: '交期维度',
        fieldKey: _sysParamsFieldConfig.materialProcurement[0],
        display: true,
      },
      {
        type: 'simple-radio-group',
        titleKey: '交期是否必填',
        oneItem: '是',
        twoItem: '否',
        fieldKey: _sysParamsFieldConfig.materialProcurement[1],
        display: true,
      },
      {
        type: 'simple-radio-group',
        titleKey: '交付数量是否必填',
        oneItem: '是',
        twoItem: '否',
        fieldKey: _sysParamsFieldConfig.materialProcurement[2],
        display: true,
      },
      {
        type: 'text',
        titleKey: '2. 采购类型: 备料&采购',
        display: true,
      },
      {
        type: 'valueLabel-radio-group',
        options: [
          { value: '1', label: '采购单' },
          { value: '2', label: '物料' },
        ],
        titleKey: '交期维度',
        fieldKey: _sysParamsFieldConfig.materialProcurement2[0],
        display: true,
      },
      {
        type: 'simple-radio-group',
        titleKey: '交期是否必填',
        oneItem: '是',
        twoItem: '否',
        fieldKey: _sysParamsFieldConfig.materialProcurement2[1],
        display: true,
      },
      {
        type: 'simple-radio-group',
        titleKey: '交付数量是否必填',
        oneItem: '是',
        twoItem: '否',
        fieldKey: _sysParamsFieldConfig.materialProcurement2[2],
        display: true,
      },
    ],
  },
  {
    // 供应链协同数据大屏
    titleKey: 'elanDashboard',
    children: [
      {
        type: 'input-only',
        titleKey: 'elanDashboardTitle',
        inputLabelKey: 'elanDashboardLabel',
        fieldKey: 'dashboard_factory_name',
        display: true,
        maxLength: 10,
      },
    ],
  },
];

export const getSysParamsConfig = (): SysParamsConfig[] => {
  return JSON.parse(JSON.stringify(_sysParamsConfig));
};

export const needStringfiedKeys = ['bom_required_property', 'bom_accessory_required_property'];
