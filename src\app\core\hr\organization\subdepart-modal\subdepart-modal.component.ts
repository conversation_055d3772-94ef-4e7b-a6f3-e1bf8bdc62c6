import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzModalService } from 'ng-zorro-antd/modal';
import { Component, EventEmitter, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { OrganizationService } from '../organization.service';
import { finalize } from 'rxjs/operators';
import { UtilService } from 'src/app/shared/util.service';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzMessageService } from 'ng-zorro-antd/message';
import { TreeOptions } from '../interface/structure-data';

@Component({
  selector: 'app-subdepart-modal',
  templateUrl: './subdepart-modal.component.html',
  styleUrls: ['./subdepart-modal.component.scss'],
})
export class SubdepartModalComponent implements OnInit {
  @ViewChild('titleTpl') titleTpl: TemplateRef<any> | undefined;
  @ViewChild('contentTpl') contentTpl: TemplateRef<any> | undefined;
  @ViewChild('footerTpl') footerTpl: TemplateRef<any> | undefined;
  @Output() handleCreateSubDepart = new EventEmitter<any>();
  subDepartForm?: FormGroup;
  placeInput!: string;
  createNotice!: string;
  data!: any;
  depart_id!: number;
  constructor(
    private _modal: NzModalService,
    private _translateService: TranslateService,
    private _service: OrganizationService,
    private _util: UtilService,
    private _notice: NzNotificationService,
    private _fb: FormBuilder,
    private _msg: NzMessageService
  ) {}

  ngOnInit(): void {}

  createSubDepartModal() {
    this.placeInput = this._translateService.instant('placeholder.input');
    this.createNotice = this._translateService.instant('subdepart-modal.create-notice', { name: this.data?.name });
    this.initSubDepartForm();
    this._modal.create({
      nzWrapClassName: 'modal-outer-middle',
      nzTitle: this.titleTpl,
      nzContent: this.contentTpl,
      nzFooter: this.footerTpl,
    });
  }

  initSubDepartForm() {
    const codePattern = '[A-Za-z0-9]*';
    this.subDepartForm = this._fb.group({
      name: [null, [Validators.required, Validators.maxLength(16)]],
      code: [
        null,
        [Validators.required, Validators.maxLength(20), Validators.pattern(codePattern)],
        [this._service.uniqueDepartValidator(null)],
      ],
      status: [1, [Validators.required]],
      parent_id: [this.data.id],
    });
  }

  handleCancel() {
    this._modal.closeAll();
  }

  handleOk() {
    this._util
      .checkIfFormPassesValidation(this.subDepartForm as any)
      .then((valid) => {
        if (valid) {
          const data = this.subDepartForm?.getRawValue();
          data.status = data.status ? 1 : 0;
          this._service
            .addDepartment(data)
            .pipe(finalize(() => console.log('aa')))
            .subscribe((res) => {
              if (res.code === 200) {
                const create_msg = this._translateService.instant('success.create');
                this._msg.success(create_msg);
                this.depart_id = res?.data;
                const expandedNode = [...this._service.expandedTree] ?? [];
                this._service.getTree().then((datas: TreeOptions[]) => {
                  this._service.selectTree = [this.depart_id];
                  this._service.getEmployeeList(this.depart_id, true);
                  const line = datas.find((item) => item.id === this.depart_id && !item.is_employee);
                  const expand_set = new Set([...expandedNode, ...(line?.expanded_list ?? [])]);
                  this._service.expandedTree = [...expand_set];
                  this._service.selectNode(this.depart_id, { ...data, id: this.depart_id ?? null }, true);
                  setTimeout(() => {
                    this._service.treeComponent?.scrollTree();
                  }, 500);
                });
                this._modal.closeAll();
              }
            });
        } else {
          const msg = this._translateService.instant('form-error.input-right');
          this._notice.error('', msg);
          return;
        }
      })
      .catch((error) => {
        console.log('error', error);
        // this.btnLoading = false;
        return;
      })
      .finally(() => {
        // this.btnLoading = false;
        return;
      });
  }
}
