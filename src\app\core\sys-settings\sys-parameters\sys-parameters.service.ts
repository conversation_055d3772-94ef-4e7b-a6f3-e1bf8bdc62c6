import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { FlcSpKeyConstant, FlcSpUtilService } from 'fl-common-lib';

@Injectable()
export class SysParametersService {
  private readonly procurementInventoryServiceUrl = '/service/procurement-inventory/common/v1';

  constructor(private http: HttpClient, private _spUtil: FlcSpUtilService) {}

  // 获取采购库存相关系统设置
  getProcurementInventorySettings() {
    return this.http.get<any>(`${this.procurementInventoryServiceUrl}/get-settings`);
  }

  // 修改采购库存相关系统设置
  setProcurementInventorySettings(payload: any) {
    return this.http.post<any>(`${this.procurementInventoryServiceUrl}/set-settings`, payload);
  }

  // 权限相关
  private userActions?: Array<string>;
  getUserActions() {
    if (!this.userActions && this._spUtil.containsObjectKey(FlcSpKeyConstant.UserActions)) {
      const actionMap = this._spUtil.getObject(FlcSpKeyConstant.UserActions) as Map<string, []>;
      this.userActions = actionMap.get('sys-settings/sys-parameters');
    }
    return this.userActions;
  }
  getCheckPriceTemplateOption() {
    return this.http.post<any>('/service/archive/v1/sample_style/basic_option', {
      limit: 999,
      page: 1,
      column: 'name',
      value: '',
    });
  }
}
