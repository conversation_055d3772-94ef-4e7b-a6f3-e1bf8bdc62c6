import { factoryCodes } from 'src/app/shared/common-config';
import { SourceTypeEnum } from '../models/bulk.enum';

const dictUrl = '/service/scm/dict_category/dict_option';

const userInfo: string | null = localStorage.getItem('userInfo');
const user = userInfo ? JSON.parse(userInfo) : {};

function getStatusColorStyle(item: any) {
  const styleObj: any = {};
  switch (item.order_status) {
    case 1: // 待提交
      styleObj.color = '#138AFF';
      break;
    case 2: // 待审核
    case 9: // 修改待审核
      styleObj.color = '#FB6401';
      break;
    case 4: // 待修改
    case 10: // 修改未通过
      styleObj.color = '#FF4A1D';
      break;
    case 8: // 已取消
      styleObj.color = '#97999C';
      break;
  }
  return styleObj;
}

function getMaterialCustomName(item: any) {
  if (item.first_material_name) {
    return `${item.first_material_name}-${item.second_material_name}-${item.third_material_name}`;
  }
  return '';
}

/**
 * 列表筛选项配置
 */

export function initSearchList() {
  // 标准版配置
  const StandardOptions = [
    {
      label: '订单需求号',
      labelKey: 'io_code',
      valueKey: 'io_code',
      type: 'select',
      visible: true,
    },
    {
      label: '批次',
      labelKey: 'order_category',
      valueKey: 'order_category',
      type: 'select',
      visible: true,
    },
    {
      label: '生产类型',
      labelKey: 'order_production_type',
      valueKey: 'order_production_type',
      type: 'select',
      visible: true,
    },
    {
      label: '品牌',
      labelKey: 'brand_id',
      valueKey: 'brand_id',
      type: 'select',
      visible: true,
    },
    {
      label: '款式分类',
      labelKey: 'style',
      valueKey: 'style',
      type: 'cascader',
      visible: true,
    },
    {
      label: '客户名称',
      labelKey: 'customer',
      valueKey: 'customer',
      type: 'select',
      visible: true,
    },
    {
      label: '销售单号',
      labelKey: 'contract_number',
      valueKey: 'contract_number',
      type: 'select',
      visible: true,
    },
    // {
    //   label: '状态',
    //   labelKey: 'status',
    //   valueKey: 'status',
    //   type: 'select',
    //   canSearch: false,
    //   alwaysReload: true,
    // },
    {
      label: '下单日期',
      labelKey: 'order_date',
      valueKey: 'order_date',
      type: 'date',
      visible: true,
    },
    {
      label: '订单类型',
      labelKey: 'order_classification',
      valueKey: 'order_classification',
      type: 'select',
      visible: true,
    },
    {
      label: '订单标签',
      labelKey: 'order_label',
      valueKey: 'order_label',
      type: 'select',
      visible: true,
    },
    {
      label: '交付日期',
      labelKey: 'due_time',
      valueKey: 'due_time',
      type: 'date',
      visible: true,
    },
    {
      label: '客户交期',
      labelKey: 'customer_due_time',
      valueKey: 'customer_due_time',
      type: 'date',
      visible: true,
    },
    {
      label: '业务员',
      labelKey: 'biz_user',
      valueKey: 'biz_user',
      type: 'select',
      visible: true,
    },
    {
      label: '创建人',
      labelKey: 'gen_user',
      valueKey: 'gen_user',
      type: 'select',
      visible: true,
    },
    {
      label: '算料员',
      labelKey: 'dist_user',
      valueKey: 'dist_user',
      type: 'select',
      visible: true,
    },
    {
      label: '款式编码',
      labelKey: 'style_code',
      valueKey: 'style_code',
      type: 'select',
      visible: true,
    },
    {
      label: '是否预排单',
      labelKey: 'is_pre_order',
      valueKey: 'is_pre_order',
      type: 'local-select',
      options: [
        { label: '是', value: '1' },
        { label: '否', value: '2' },
      ],
      visible: true,
    },
    {
      label: '是否使用生产计划',
      labelKey: 'is_use_plan',
      valueKey: 'is_use_plan',
      type: 'local-select',
      options: [
        { label: '是', value: 'true' },
        { label: '否', value: 'false' },
      ],
      visible: true,
    },
    {
      label: 'BOM状态',
      labelKey: 'bom_fill_status',
      valueKey: 'bom_fill_status',
      type: 'select',
      visible: true,
    },
    {
      label: 'BOM审核状态',
      labelKey: 'bom_audit_status',
      valueKey: 'bom_audit_status',
      type: 'select',
      visible: true,
    },
    {
      label: '来源',
      labelKey: 'source',
      valueKey: 'source',
      type: 'select',
      visible: true,
    },
    // 2025年01月09日15:09:29  去掉
    // {
    //   label: '创建时间',
    //   labelKey: 'create_time',
    //   valueKey: 'create_time',
    //   type: 'date',
    //   visible: true,
    // },
    {
      label: '跟单员',
      labelKey: 'merchandiser_user_id',
      valueKey: 'merchandiser_user_id',
      type: 'select',
      visible: false,
    },
    {
      label: '季节',
      labelKey: 'jj',
      valueKey: 'season',
      type: 'select',
      visible: false,
      dataUrl: dictUrl,
      transDataValue: 'label',
    },
    {
      label: '交付单号',
      labelKey: 'po_code',
      valueKey: 'po_code',
      type: 'select',
      visible: false,
    },
    {
      label: '生产国',
      labelKey: 'origincountry',
      valueKey: 'origin_country',
      type: 'select',
      visible: false,
      dataUrl: dictUrl,
      transDataValue: 'label',
    },
    {
      label: '出货港',
      labelKey: 'incoterms',
      valueKey: 'shipment_port',
      type: 'select',
      visible: false,
      dataUrl: dictUrl,
      transDataValue: 'label',
    },
  ];

  // CTC配置
  const CTCOptions = [
    {
      label: '订单需求号',
      labelKey: 'io_code',
      valueKey: 'io_code',
      type: 'select',
      visible: false,
    },
    {
      label: '批次',
      labelKey: 'order_category',
      valueKey: 'order_category',
      type: 'select',
      visible: false,
    },
    {
      label: '生产类型',
      labelKey: 'order_production_type',
      valueKey: 'order_production_type',
      type: 'select',
      visible: false,
    },

    {
      label: '款式分类',
      labelKey: 'style',
      valueKey: 'style',
      type: 'cascader',
      visible: false,
    },
    {
      label: '款式编码',
      labelKey: 'style_code',
      valueKey: 'style_code',
      type: 'select',
      visible: true,
    },
    {
      label: '是否预排单',
      labelKey: 'is_pre_order',
      valueKey: 'is_pre_order',
      type: 'local-select',
      options: [
        { label: '是', value: '1' },
        { label: '否', value: '2' },
      ],
      visible: false,
    },
    {
      label: '是否使用生产计划',
      labelKey: 'is_use_plan',
      valueKey: 'is_use_plan',
      type: 'local-select',
      options: [
        { label: '是', value: 'true' },
        { label: '否', value: 'false' },
      ],
      visible: false,
    },
    {
      label: '客户名称',
      labelKey: 'customer',
      valueKey: 'customer',
      type: 'select',
      visible: true,
    },
    {
      label: '品牌',
      labelKey: 'brand_id',
      valueKey: 'brand_id',
      type: 'select',
      visible: true,
    },
    {
      label: '销售单号',
      labelKey: 'contract_number',
      valueKey: 'contract_number',
      type: 'select',
      visible: false,
    },
    // {
    //   label: '状态',
    //   labelKey: 'status',
    //   valueKey: 'status',
    //   type: 'select',
    //   canSearch: false,
    //   alwaysReload: true,
    // },
    {
      label: '下单日期',
      labelKey: 'order_date',
      valueKey: 'order_date',
      type: 'date',
      visible: false,
    },

    {
      label: '客户交期',
      labelKey: 'customer_due_time',
      valueKey: 'customer_due_time',
      type: 'date',
      visible: false,
    },
    {
      label: '业务员',
      labelKey: 'biz_user',
      valueKey: 'biz_user',
      type: 'select',
      visible: true,
    },
    {
      label: '创建人',
      labelKey: 'gen_user',
      valueKey: 'gen_user',
      type: 'select',
      visible: false,
    },
    {
      label: '算料员',
      labelKey: 'dist_user',
      valueKey: 'dist_user',
      type: 'select',
      visible: false,
    },

    {
      label: '创建时间',
      labelKey: 'create_time',
      valueKey: 'create_time',
      type: 'date',
      visible: false,
    },

    {
      label: '跟单员',
      labelKey: 'merchandiser_user_id',
      valueKey: 'merchandiser_user_id',
      type: 'select',
      visible: true,
    },
    {
      label: '季节',
      labelKey: 'jj',
      valueKey: 'season',
      type: 'select',
      visible: true,
      dataUrl: dictUrl,
      transDataValue: 'label',
    },
    {
      label: '交付单号',
      labelKey: 'po_code',
      valueKey: 'po_code',
      type: 'select',
      visible: true,
    },
    {
      label: '生产国',
      labelKey: 'origincountry',
      valueKey: 'origin_country',
      type: 'select',
      visible: true,
      dataUrl: dictUrl,
      transDataValue: 'label',
    },
    {
      label: '出货港',
      labelKey: 'incoterms',
      valueKey: 'shipment_port',
      type: 'select',
      visible: true,
      dataUrl: dictUrl,
      transDataValue: 'label',
    },
    {
      label: '交付日期',
      labelKey: 'due_time',
      valueKey: 'due_time',
      type: 'date',
      visible: true,
    },
  ];
  return factoryCodes.includes(user?.factory_code) ? CTCOptions : StandardOptions;
}

/**
 * 列表表头配置
 */
const StandardTableHeader = [
  {
    label: '订单需求号',
    key: 'io_code',
    visible: true,
    width: '130px',
    type: 'template',
    templateName: 'io_code',
    isHidePin: true,
    pinned: true,
    disable: false,
  },
  { label: '批次', key: 'order_category_label', visible: true, width: '82px', type: 'text', pinned: false, disable: false },
  { label: '生产类型', key: 'order_production_type_label', visible: true, width: '90px', type: 'text', pinned: false, disable: false },
  {
    label: '订单类型',
    key: 'order_classification',
    visible: true,
    width: '90px',
    type: 'text',
    pinned: false,
    disable: false,
  },
  {
    label: '订单标签',
    key: 'order_labels',
    visible: true,
    width: '90px',
    type: 'template',
    templateName: 'order_labels',
    pinned: false,
    disable: false,
  },
  { label: '品牌', key: 'brand_name', visible: true, width: '82px', type: 'text', pinned: false, disable: false },
  { label: '生产周期', key: 'lead_time', visible: false, width: '82px', type: 'text', pinned: false, disable: false },
  { label: '款式编码', key: 'style_code', visible: true, width: '100px', type: 'text', pinned: false, disable: false },
  { label: '品名', key: 'category', visible: true, width: '88px', type: 'text', pinned: false, disable: false },
  {
    label: '款式分类',
    key: 'material_name',
    formatter: getMaterialCustomName.bind(this),
    visible: true,
    width: '150px',
    type: 'text',
    pinned: false,
    disable: false,
  },
  { label: '款式图', key: 'order_pictures', visible: true, width: '78px', type: 'image', pinned: false, disable: false },
  { label: '客户名称', key: 'customer', visible: true, width: '98px', type: 'text', pinned: false, disable: false },
  { label: '总件数', key: 'qty', visible: true, width: '62px', type: 'quantity', pinned: false, disable: false },
  { label: '预收金额', key: 'pre_payment', visible: true, width: '100px', type: 'text', pinned: false, disable: false },
  { label: '总金额', key: 'total_fob', visible: false, width: '100px', type: 'text', pinned: false, disable: false },
  { label: '总销售金额', key: 'total_sales_cad', visible: false, width: '130px', type: 'text', pinned: false, disable: false },
  { label: '销售单号', key: 'contract_number', visible: true, width: '100px', type: 'text', pinned: false, disable: false },
  { label: '下单日期', key: 'order_date', sort: true, visible: true, width: '100px', type: 'date', pinned: false, disable: false },
  {
    label: '交付日期',
    key: 'po_due_times',
    sort: true,
    visible: true,
    width: '130px',
    type: 'template',
    templateName: 'po_due_times',
    pinned: false,
    disable: false,
  },
  {
    label: '客户交期',
    key: 'po_customer_due_times',
    visible: true,
    width: '150px',
    type: 'template',
    templateName: 'po_customer_due_times',
    pinned: false,
    disable: false,
  },
  {
    label: '状态',
    key: 'order_status_value',
    style: getStatusColorStyle.bind(this),
    visible: true,
    width: '90px',
    type: 'text',
    pinned: false,
    disable: false,
  },
  {
    label: 'BOM状态',
    key: 'bom_fill_status',
    visible: true,
    width: '90px',
    type: 'template',
    templateName: 'bom_fill_status',
    pinned: false,
    disable: false,
  },
  {
    label: 'BOM审核状态',
    key: 'bom_audit_status',
    visible: true,
    width: '120px',
    type: 'template',
    templateName: 'bom_audit_status',
    pinned: false,
    disable: false,
  },
  {
    label: '询价状态',
    key: 'inquiry_status',
    visible: true,
    width: '90px',
    type: 'template',
    templateName: 'inquiry_status',
    pinned: false,
    disable: false,
  },
  {
    label: '业务员',
    key: 'biz_user_name',
    visible: true,
    width: '90px',
    type: 'text',
    pinned: false,
    disable: false,
  },
  {
    label: '来源',
    key: 'source',
    visible: true,
    width: '70px',
    type: 'template',
    templateName: 'source',
    pinned: false,
    disable: false,
  },
  { label: '创建时间', key: 'gen_time', visible: true, width: '180px', type: 'datetime', sort: true, pinned: false, disable: false },
  { label: '创建人', key: 'gen_user', visible: true, width: '80px', type: 'text', pinned: false, disable: false },
  {
    label: '是否预排单',
    key: 'is_pre_order',
    visible: true,
    width: '120px',
    type: 'template',
    templateName: 'is_pre_order',
    pinned: false,
    disable: false,
  },
  {
    label: '是否使用生产计划',
    key: 'is_use_plan',
    visible: true,
    width: '130px',
    type: 'template',
    templateName: 'is_use_plan',
    pinned: false,
    disable: false,
  },
  { label: '算料员', key: 'dist_user_label', visible: true, width: '80px', type: 'text', pinned: false, disable: false },
  { label: '备注', key: 'remark', visible: true, width: '130px', type: 'text', pinned: false, disable: false },
];

const CTCTableHeader = [
  {
    label: '订单需求号',
    key: 'io_code',
    visible: true,
    width: '130px',
    type: 'template',
    templateName: 'io_code',
    isHidePin: true,
    pinned: true,
    disable: false,
  },
  { label: '批次', key: 'order_category_label', visible: false, width: '82px', type: 'text', pinned: false, disable: false },
  { label: '生产类型', key: 'order_production_type_label', visible: false, width: '90px', type: 'text', pinned: false, disable: false },

  { label: '款式编码', key: 'style_code', visible: true, width: '100px', type: 'text', pinned: false, disable: false },
  { label: '客户名称', key: 'customer', visible: true, width: '98px', type: 'text', pinned: false, disable: false },
  { label: '品名', key: 'category', visible: true, width: '88px', type: 'text', pinned: false, disable: false },
  {
    label: '款式分类',
    key: 'material_name',
    formatter: getMaterialCustomName.bind(this),
    visible: true,
    width: '150px',
    type: 'text',
    pinned: false,
    disable: false,
  },
  { label: '品牌', key: 'brand_name', visible: true, width: '82px', type: 'text', pinned: false, disable: false },
  { label: '生产周期', key: 'lead_time', visible: true, width: '82px', type: 'text', pinned: false, disable: false },
  { label: '款式图', key: 'order_pictures', visible: false, width: '78px', type: 'image', pinned: false, disable: false },
  { label: '总件数', key: 'qty', visible: true, width: '62px', type: 'quantity', pinned: false, disable: false },
  { label: '预收金额', key: 'pre_payment', visible: true, width: '100px', type: 'text', pinned: false, disable: false },
  { label: '总金额', key: 'total_fob', visible: true, width: '100px', type: 'text', pinned: false, disable: false },
  { label: '总销售金额', key: 'total_sales_cad', visible: true, width: '130px', type: 'text', pinned: false, disable: false },
  { label: '销售单号', key: 'contract_number', visible: false, width: '100px', type: 'text', pinned: false, disable: false },
  { label: '下单日期', key: 'order_date', sort: true, visible: true, width: '100px', type: 'date', pinned: false, disable: false },
  {
    label: '交付日期',
    key: 'po_due_times',
    sort: true,
    visible: true,
    width: '130px',
    type: 'template',
    templateName: 'po_due_times',
    pinned: false,
    disable: false,
  },
  {
    label: '客户交期',
    key: 'po_customer_due_times',
    visible: true,
    width: '150px',
    type: 'template',
    templateName: 'po_customer_due_times',
    pinned: false,
    disable: false,
  },
  {
    label: '业务员',
    key: 'biz_user_name',
    visible: true,
    width: '90px',
    type: 'text',
    pinned: false,
    disable: false,
  },
  {
    label: '状态',
    key: 'order_status_value',
    style: getStatusColorStyle.bind(this),
    visible: true,
    width: '90px',
    type: 'text',
    pinned: false,
    disable: false,
  },
  {
    label: '来源',
    key: 'source',
    visible: false,
    width: '70px',
    type: 'template',
    templateName: 'source',
    pinned: false,
    disable: false,
  },
  { label: '创建时间', key: 'gen_time', visible: true, width: '180px', type: 'datetime', sort: true, pinned: false, disable: false },
  { label: '创建人', key: 'gen_user', visible: true, width: '80px', type: 'text', pinned: false, disable: false },
  {
    label: '是否预排单',
    key: 'is_pre_order',
    visible: false,
    width: '120px',
    type: 'template',
    templateName: 'is_pre_order',
    pinned: false,
    disable: false,
  },
  {
    label: '是否使用生产计划',
    key: 'is_use_plan',
    visible: false,
    width: '130px',
    type: 'template',
    templateName: 'is_use_plan',
    pinned: false,
    disable: false,
  },
  { label: '算料员', key: 'dist_user_label', visible: false, width: '80px', type: 'text', pinned: false, disable: false },
];

export function initTableHeader() {
  return factoryCodes.includes(user?.factory_code) ? CTCTableHeader : StandardTableHeader;
}
