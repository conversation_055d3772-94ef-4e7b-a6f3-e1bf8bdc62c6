import { catchError } from 'rxjs/operators';
import { AfterViewInit, Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { FlcSpUtilService } from 'fl-common-lib';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzSelectComponent } from 'ng-zorro-antd/select';
import { NzFormatEmitEvent, NzTreeNode } from 'ng-zorro-antd/tree';
import { Department } from '../interface/structure-data';
import { OrganizationService } from '../organization.service';
import { SubdepartModalComponent } from '../subdepart-modal/subdepart-modal.component';
import { of } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-structure-tree',
  templateUrl: './structure-tree.component.html',
  styleUrls: ['./structure-tree.component.scss'],
})
export class StructureTreeComponent implements OnInit, AfterViewInit {
  @ViewChild(SubdepartModalComponent) subDepartModal: any;
  @ViewChild('treeWrapTpl') treeWrapTpl!: ElementRef<HTMLElement>;
  @ViewChild('selectRef') selectRef!: NzSelectComponent;
  @Input() treeMaxHeight: any;
  searchValue: any;
  isSelectLoading = false;
  orgParams: any;
  id = null; // 部门id或公司id

  constructor(
    public _service: OrganizationService,
    private _fb: FormBuilder,
    private _msg: NzMessageService,
    private _spUtil: FlcSpUtilService,
    private _translateService: TranslateService
  ) {}

  ngOnInit(): void {
    this._service.getTree().then(() => {
      this._service.expandedTree = [];
      if (this._service.nodes.length) {
        this._service.selectTree = [this._service.nodes[0]?.id];
        this._service.expandedTree = [this._service.nodes[0]?.id];
        this.getDepartDetail(this._service.nodes[0].id);
        this._service.getEmployeeList(this._service.nodes[0]?.id, true);
        this._getNodeInfo(this._service.nodes[0]);
      }

      // 由权限配置跳转，自动选中当前员工
      this.orgParams = this._spUtil.getObject('orgKey');
      if (this.orgParams) {
        this.searchTree({
          value: 'employee_' + this.orgParams.id,
        });
      }
    });
  }

  ngAfterViewInit(): void {}

  ngOnDestroy() {
    this._spUtil?.removeObject('orgKey');
  }

  /**
   * 搜索架构树
   * @param e
   */
  searchTree(e: any) {
    this._service.addedEmployee = null;
    if (e) {
      const data = this._service.selectOptions.find((item) => item.value === e.value);
      this._service.selectTree = [data?.value];
      const expand_set = new Set([...this._service.expandedTree, ...(data?.expanded_list as any[])]);
      this._service.expandedTree = [...expand_set] ?? [];
      const value = data?.is_employee ? data?.expanded_list[data?.expanded_list?.length - 1] : data?.id;
      this.getDepartDetail(value);
      const dept_id = data?.is_employee ? data?.depart_id : data?.id;
      const emp_id = data?.is_employee ? data?.id : null;
      this._service.getEmployeeList(dept_id, true, emp_id);
      this.selectRef.onClearSelection();

      setTimeout(() => {
        this.scrollTree();
      }, 500);
    }
  }

  /**
   * 架构树滚动至选中处
   */
  scrollTree() {
    const parentElement = this.treeWrapTpl.nativeElement;
    const child = parentElement.querySelector('.ant-tree-treenode-selected') as HTMLElement;
    const h = child.parentElement?.offsetTop ?? 0;
    console.log(child, child.offsetTop, h);
    parentElement.scrollTo(0, child.offsetTop - h);
  }

  /**
   * 1.在编辑状态中，左边的树只可以展开，不可以被选择，如果用户单击树中的其他部门 toast提示：请先保存您的操作再切换哟～
   * 2.点击保存后，左边的树更新为保存后结果，并且自动选中刚刚新增的部门。
   * @param event
   */
  nzEvent(event: NzFormatEmitEvent): void {
    console.log('click tree');
    this._service.isEnterprise = !event?.keys?.length || event.keys.every((item: any) => item === -1);

    this._service.addedEmployee = null;
    // 编辑状态下，不可切换树节点
    if (this._service.editable) {
      this._service.selectTree = [this._service.departForm.get('id')?.value];
      this._msg.error(this._translateService.instant('tips.请先保存您的操作再切换哟'));
      return;
    }
    // 选中树节点，更新部门信息与直属员工列表
    if (event.selectedKeys?.length) {
      const origin: any = event.node?.origin;
      this._service.selectTree = [...(event.keys as any[])];
      this._service.expandedTree = [...this._service.expandedTree, origin?.key];
      const depart_id = origin?.is_employee ? origin?.depart_id : origin?.id;
      const employee_id = origin?.is_employee ? origin?.id : null;
      this.getDepartDetail(depart_id);
      this._service.getEmployeeList(depart_id, true, employee_id);
      this._getNodeInfo(origin);
    } else {
      // 连续点击依旧高亮
      this._service.selectTree = [...this._service.selectTree];
    }
  }

  // 根据当前节点获取右侧显示部门还是公司信息
  _getNodeInfo(origin: any) {
    const depart_id = origin?.is_employee ? origin?.depart_id : origin?.id;
    this.id = depart_id;
    this._service.showDept = depart_id !== 1;
  }

  /**
   * 获取选中节点的所有父节点
   * @returns
   */
  getSelectTreeParent(): any[] {
    if (this._service.selectTree?.length) {
      const line = this._service.selectOptions?.find((item) => item.value === this._service.selectTree[0]);
      return line?.expanded_list ?? [];
    }
    return [];
  }

  /**
   * 点击展开箭头时的change
   * @param e
   */
  expandedChange(e: NzFormatEmitEvent) {
    if (!e?.node?.isExpanded) {
      const ex_index = this._service.expandedTree.findIndex((i) => i === e?.node?.key);
      if (ex_index !== -1) {
        this._service.expandedTree.splice(ex_index, 1);
      }
      const expandeds = this.getSelectTreeParent();
      this.foldChild(e?.node?.children, expandeds);
    } else {
      this._service.expandedTree.push(e?.node?.key);
    }
    this._service.expandedTree = [...this._service.expandedTree];
  }

  /**
   * 递归当前节点的子节点
   * 选中节点的父节点不处理，其余节点均折叠
   * @param data：子节点数据
   * @param expandeds：选中节点的父节点
   */
  foldChild(data?: NzTreeNode[], expandeds?: any[]) {
    data?.forEach((item: any) => {
      if (!expandeds?.includes(item.key)) {
        item._isExpanded = false;
        const ex_index = this._service.expandedTree.findIndex((i) => i === item.key);
        if (ex_index !== -1) {
          this._service.expandedTree.splice(ex_index, 1);
        }
      }
      this.foldChild(item?.children, expandeds);
    });
  }

  getDepartDetail(id: number) {
    this._service
      .getDepartmentDetail({ id: id })
      .pipe(
        catchError((error: any) => {
          if (error.error.code === 400) {
            this._service.hasDeptDataAuth = false;
            this._service.deptDataAuthError = error.error;
            return of(this.initDepartForm(null));
          }
          return of(null);
        })
      )
      .subscribe((res) => {
        if (res?.code === 200) {
          this._service.hasDeptDataAuth = true;
          this.initDepartForm({ ...res?.data });
          this._service.departStatus = res.data.status;
        }
      });
  }

  /**
   * 公司名称（不超过25个字符）公司编码（不超过15个字符，唯一性，不可重复）
   * 部门名称：最多不超过16个字符；必填; 部门编码：最多不超过20个字符；必填；唯一，不可重复
   */
  initDepartForm(line: Department | null = null) {
    const codePattern = '[A-Za-z0-9_]*';
    const status = !this._service.hasDeptDataAuth ? null : line?.status ?? 1;
    this._service.departForm = this._fb.group({
      id: [{ value: line?.id ?? null, disabled: false }],
      name: [{ value: line?.name ?? null, disabled: false }, [Validators.required, Validators.maxLength(line?.parent_id ? 16 : 25)]],
      code: [
        { value: line?.code ?? null, disabled: !line?.parent_id },
        [Validators.required, Validators.maxLength(line?.parent_id ? 20 : 15), Validators.pattern(codePattern)],
        [this._service.uniqueDepartValidator(line?.code ?? null)],
      ],
      status: [{ value: status, disabled: false }, [Validators.required]],
      parent_id: [{ value: line?.parent_id ?? null, disabled: false }],
      logos: [{ value: line?.logos ?? [], disabled: false }],
    });

    if (!this._service.hasDeptDataAuth) {
      // 通过parent_id知道是展示部门还是公司
      this._service.departForm.get('parent_id')?.setValue(this._service.showDept ? 1 : null);
      this._service.departForm.get('id')?.setValue(this.id);
    }
  }

  /**
   * 新建子部门
   */
  toAddSubDepart() {
    this.subDepartModal.data = this._service.departForm.getRawValue();
    this.subDepartModal.createSubDepartModal();
  }
}
