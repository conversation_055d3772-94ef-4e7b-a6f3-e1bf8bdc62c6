.roleLineWrap {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;

  > span {
    margin: 0 4px;
    padding: 8px 4px;
    background-color: #f1f3f6;
    border-radius: 2px;
  }
}
::ng-deep app-sort-btn {
  margin-top: -4px;
  display: inline-block;
}
.stop-use {
  color: #ff912a;
}
tr td:nth-last-child(2) {
  font-size: 12px;
}
tr td:nth-last-child(3) {
  font-size: 12px;
}
tr td:nth-last-child(4) {
  font-size: 12px;
}
.roles {
  display: inline-block;
  font-size: 12px;
  line-height: 24px;
  color: #515661;
  padding: 0 8px;
  background: #f1f3f6;
  border-radius: 2px;
  margin: 6px 12px 6px 0;
}
// 弹窗按钮全圆角重置
.ant-modal-footer button {
  border-radius: 40px;
}
