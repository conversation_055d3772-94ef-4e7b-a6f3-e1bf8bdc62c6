<div class="wrap">
  <app-search-container toolPlace="topRight" [headerTitle]="translateName + '权限名称' | translate" (reset)="reset()">
    <div class="searchLine" *ngIf="btnArr.includes('settings:role-search')">
      {{ translateName + '权限名称' | translate }}：<nz-select
        [nzDisabled]="isEdit"
        [ngModel]="searchOptions.name"
        (ngModelChange)="setOptions('name', $event)"
        (nzOpenChange)="getOptions('name')"
        [nzShowArrow]="true"
        [nzServerSearch]="false"
        nzShowSearch
        nzAllowClear
        [nzPlaceHolder]="'placeholder.select' | translate">
        <nz-option *ngFor="let item of nameOptions" [nzLabel]="item" [nzValue]="item"></nz-option>
      </nz-select>
    </div>
    <div class="searchLine" *ngIf="btnArr.includes('settings:role-search')">
      {{ translateName + '权限编码' | translate }}：<nz-select
        [nzDisabled]="isEdit"
        [ngModel]="searchOptions.code"
        (ngModelChange)="setOptions('code', $event)"
        (nzOpenChange)="getOptions('code')"
        [nzShowArrow]="true"
        [nzServerSearch]="false"
        nzShowSearch
        nzAllowClear
        [nzPlaceHolder]="'placeholder.select' | translate">
        <nz-option *ngFor="let item of codeOptions" [nzLabel]="item" [nzValue]="item"></nz-option>
      </nz-select>
    </div>
    <div class="searchLine" *ngIf="btnArr.includes('settings:role-search')">
      {{ translateName + '状态' | translate }}：<nz-select
        [nzDisabled]="isEdit"
        [(ngModel)]="searchOptions.status"
        (ngModelChange)="setOptions('status', $event)"
        [nzServerSearch]="false"
        nzShowSearch
        nzAllowClear
        [nzPlaceHolder]="'placeholder.select' | translate">
        <nz-option
          *ngFor="let item of statusOptions"
          [nzLabel]="translateName + (item === 1 ? '启用' : '禁用') | translate"
          [nzValue]="item"></nz-option>
      </nz-select>
    </div>
    <div class="searchLine" *ngIf="btnArr.includes('settings:role-search')">
      {{ translateName + '创建日期' | translate }}：<nz-range-picker
        [nzDisabled]="isEdit"
        [(ngModel)]="genTimeSearchArray"
        (ngModelChange)="setOptions('gen_time', $event)"
        [nzPlaceHolder]="['placeholder.start' | translate, 'placeholder.end' | translate]"></nz-range-picker>
    </div>
    <div class="searchLine" *ngIf="btnArr.includes('settings:role-search')">
      {{ translateName + '创建人员' | translate }}：<nz-select
        [nzDisabled]="isEdit"
        [(ngModel)]="searchOptions.gen_user"
        (ngModelChange)="setOptions('gen_user', $event)"
        (nzOpenChange)="getOptions('gen_user')"
        [nzServerSearch]="false"
        nzShowSearch
        nzAllowClear
        [nzPlaceHolder]="'placeholder.select' | translate">
        <nz-option *ngFor="let item of genUserOptions" [nzLabel]="item" [nzValue]="item"></nz-option>
      </nz-select>
    </div>
  </app-search-container>

  <div class="board">
    <div class="titleBar">
      <div class="title">{{ translateName + '权限名称分配及设置' | translate }}</div>
      <div class="bar">
        <button
          *ngIf="btnArr.includes('settings:role-logList')"
          nz-button
          nzType="link"
          nzShape="round"
          flButton="link-primary"
          [disableOnClick]="1000"
          (click)="showLogModal()">
          {{ translateName + '操作记录' | translate }}
        </button>
        <button *ngIf="btnArr.includes('settings:role-create')" nz-button nzType="primary" nzShape="round" (click)="addNewRoleNameBtn()">
          <i nz-icon [nzIconfont]="'icon-xinjian1'"></i>
          {{ 'roleName.btn.addRole' | translate }}
        </button>
      </div>
    </div>
    <div class="content">
      <div #RoleNameListWrap class="left">
        <nz-spin *ngIf="roleListIsLoading" style="min-height: 200px; width: 100%" [nzSpinning]="true"></nz-spin>
        <ng-container *ngIf="!roleListIsLoading">
          <div class="roleListEmpty" *ngIf="RoleNameList.length === 0">
            <span>{{ translateName + '暂无权限名称哦~' | translate }}</span>
          </div>
          <ng-container *ngIf="RoleNameList.length > 0">
            <div class="topLeftButton">
              <app-sort-btn [(sortOrder)]="roleNameOrderBy" (sortOrderChange)="toggleSortBy($event)"></app-sort-btn>
            </div>
            <div
              class="roleLine"
              [ngClass]="{ selected: index === selectedRoleNameIndex }"
              *ngFor="let item of RoleNameList; let index = index"
              (click)="selectRoleName(index)">
              <app-text-truncated class="menu-item" [template]="roleLineName">
                <ng-template #roleLineName>
                  <span>{{ getIndex(index) }}.{{ item.name }}</span>
                </ng-template>
              </app-text-truncated>

              <button
                type="button"
                class="copy"
                nz-tooltip
                [nzTooltipTitle]="'flss.btn.copy' | translate"
                nz-button
                nzSize="small"
                nzShape="circle"
                (click)="onCopyAuth($event, item)">
                <i nz-icon nzType="copy" nzTheme="outline"></i>
              </button>
            </div>
          </ng-container>
        </ng-container>
      </div>
      <div class="right">
        <nz-spin *ngIf="roleDetailIsLoading" style="min-height: 200px" [nzSpinning]="true"> </nz-spin>
        <ng-container *ngIf="!roleDetailIsLoading && selectedRoleNameItem == undefined">
          <div class="detailEmpty">
            <span><img style="width: 200px" src="../../../../assets/image/role_empty.png" alt="RoleDetailEmpty" /></span>
            <span>{{ translateName + '暂无权限可配置哟～' | translate }}</span>
          </div>
        </ng-container>
        <ng-container *ngIf="!roleDetailIsLoading && selectedRoleNameItem != undefined">
          <div class="contentTitle">
            <div class="detailInfo">
              <span class="roleTitle">{{ selectedRoleNameItem!.name }}</span>
              <div class="roleSubtitle">
                <nz-space [nzSplit]="spaceSplit" nzAlign="center">
                  <ng-template #spaceSplit>
                    <nz-divider nzType="vertical"></nz-divider>
                  </ng-template>
                  <span *nzSpaceItem>{{ translateName + '权限编码' | translate }}：{{ selectedRoleNameItem!.code }}</span>
                  <span *nzSpaceItem>{{ translateName + '创建人' | translate }}：{{ selectedRoleNameItem!.gen_user || '-' }}</span>
                  <span *nzSpaceItem
                    >{{ translateName + '创建时间' | translate }}：{{ selectedRoleNameItem!.gen_time | date: 'yyyy/MM/dd HH:mm:ss' }}</span
                  >
                  <span *nzSpaceItem
                    >{{ translateName + '最近修改' | translate }}：{{
                      selectedRoleNameItem!.modified_time | date: 'yyyy/MM/dd HH:mm:ss'
                    }}</span
                  >
                </nz-space>
              </div>
            </div>
            <div class="controlBar">
              <nz-switch
                *ngIf="btnArr.includes('settings:role-update')"
                [nzCheckedChildren]="translateName + '启用' | translate"
                [nzUnCheckedChildren]="translateName + '禁用' | translate"
                [(ngModel)]="selectedRoleNameItem!.active"
                (ngModelChange)="activeSwitcher($event)"></nz-switch>
              <nz-divider nzType="vertical"></nz-divider>
              <button
                *ngIf="btnArr.includes('settings:role-update')"
                class="formIcon"
                nz-button
                nzType="text"
                nzSize="small"
                nzShape="circle"
                (click)="editRoleNameBtn()">
                <i nz-icon [nzIconfont]="'icon-caozuolan_bianji1'"></i>
              </button>
              <button
                *ngIf="btnArr.includes('settings:role-delete')"
                class="deleteIcon"
                nz-button
                nzType="text"
                nzSize="small"
                nzShape="circle"
                (click)="deleteRoleNameBtn()">
                <i nz-icon [nzIconfont]="'icon-caozuolan_shanchu1'"></i>
              </button>
            </div>
          </div>
          <div class="tabbar">
            <div class="controlBar">
              <div class="flagBar">
                <div class="leftArrow" (click)="switchToRangeMode(false)" [ngClass]="{ active: !isRangeMode }">
                  <div>
                    <span>{{ translateName + '功能权限' | translate }}</span>
                    ({{ translateName + '配置可访问的菜单及可操作的按钮' | translate }})
                  </div>
                </div>

                <ng-container *ngIf="rangeList.length === 0">
                  <div
                    nz-tooltip
                    [nzTooltipTitle]="translateName + '请先配置功能权限' | translate"
                    class="rightArrow"
                    (click)="switchToRangeMode(true)"
                    [ngClass]="{ active: isRangeMode }">
                    <div>
                      <span>{{ translateName + '管理范围' | translate }}</span>
                      ({{ translateName + '配置可见的数据范围' | translate }})
                    </div>
                  </div>
                </ng-container>
                <ng-container *ngIf="rangeList.length > 0">
                  <div class="rightArrow" (click)="switchToRangeMode(true)" [ngClass]="{ active: isRangeMode }">
                    <div>
                      <span>{{ translateName + '管理范围' | translate }}</span>
                      ({{ translateName + '配置可见的数据范围' | translate }})
                    </div>
                  </div>
                </ng-container>
              </div>
              <div class="buttonGroup">
                <ng-container *ngIf="!isEdit">
                  <button
                    *ngIf="btnArr.includes('settings:role-update')"
                    nz-button
                    nzShape="round"
                    nzType="primary"
                    (click)="isEdit = true">
                    {{ 'btn.edit' | translate }}
                  </button>
                </ng-container>
                <ng-container *ngIf="isEdit">
                  <button nz-button nzShape="round" (click)="cancel()">{{ 'btn.cancel' | translate }}</button>
                  <button
                    *ngIf="btnArr.includes('settings:role-update')"
                    nz-button
                    nzShape="round"
                    nzType="primary"
                    [nzLoading]="saving"
                    [disabled]="rangeList.length === 0"
                    (click)="beforeSaveCheck()">
                    {{ 'btn.save' | translate }}
                  </button>
                </ng-container>
              </div>
            </div>
            <div class="menuList">
              <nz-radio-group [(ngModel)]="selectedFirstMenu" (ngModelChange)="FirstMenuChange($event)">
                <ng-container *ngFor="let item of firstMenuList">
                  <ng-container *ngIf="!(isRangeMode && item.isHideForRangeMode)">
                    <label nz-radio-button [nzValue]="item" *ngIf="!(isRangeMode && !item.hasSelectedMenu)">
                      {{ item.name }}
                    </label>
                    <label
                      *ngIf="isRangeMode && !item.hasSelectedMenu"
                      nz-radio-button
                      [nzValue]="item"
                      nz-tooltip
                      [nzTooltipTitle]="translateName + '请先配置功能权限' | translate"
                      [nzDisabled]="true">
                      {{ item.name }}
                    </label>
                  </ng-container>
                </ng-container>
              </nz-radio-group>

              <div class="menu-right">
                <label
                  *ngIf="isEdit && !isRangeMode"
                  nz-checkbox
                  [(ngModel)]="allChecked"
                  [nzIndeterminate]="allCheckedIndeterminate"
                  (ngModelChange)="onAllCheckedChange($event)"
                  >{{ translateName + '全选' | translate }}</label
                >
                <button *ngIf="!isRangeMode" nz-button flButton="default-positive" (click)="toggleShowALLDetail()">
                  {{ translateName + (showALLDetail ? '收起细化' : '展开细化') | translate }}
                </button>
              </div>
            </div>
          </div>
          <div id="listview" #listview (scroll)="listviewOnScroll($event)">
            <ng-container *ngIf="selectedFirstMenu!.code === 'ALLMenu'">
              <app-role-line
                #roleLineRefs
                [isSaveError]="isSaveError"
                [showALLDetail]="showALLDetail"
                [isRangeMode]="isRangeMode"
                (statusChange)="statusChanged()"
                (checkedChange)="updateAllChecked()"
                *ngFor="let item of selectedRoleNameItem!.children"
                [isEdit]="isEdit"
                [allDepartmentList]="allDepartmentList"
                [roleItem]="item"></app-role-line>
            </ng-container>
            <ng-container *ngIf="selectedFirstMenu!.code !== 'ALLMenu'">
              <app-role-line
                #roleLineRef
                [isSaveError]="isSaveError"
                [showALLDetail]="showALLDetail"
                [isRangeMode]="isRangeMode"
                (statusChange)="statusChanged()"
                (checkedChange)="updateAllChecked()"
                [isEdit]="isEdit"
                [allDepartmentList]="allDepartmentList"
                [roleItem]="selectedRoleNameItem!.children[realSelectedIndex]"></app-role-line>
            </ng-container>
          </div>
        </ng-container>
      </div>
    </div>
  </div>
</div>
<ng-template #RoleNameFormTpl>
  <form *ngIf="RoleNameForm" nz-form [formGroup]="RoleNameForm">
    <nz-form-item>
      <nz-form-label nzFor="code" nzSpan="7" nzRequired>
        {{ translateName + '权限编码' | translate }}
      </nz-form-label>
      <nz-form-control nzSpan="17"> <input nz-input formControlName="code" inputTrim /> </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label nzFor="code" nzSpan="7" nzRequired>
        {{ translateName + '权限名称' | translate }}
      </nz-form-label>
      <nz-form-control nzSpan="17" [nzErrorTip]="nameErrorTip">
        <ng-template #nameErrorTip let-control>
          <ng-container *ngIf="control.hasError('required')">{{ translateName + '请输入权限名称' | translate }}</ng-container>
        </ng-template>
        <nz-textarea-count [nzMaxCharacterCount]="12" class="inline-count">
          <textarea [nzAutosize]="{ minRows: 1, maxRows: 1 }" [maxLength]="12" nz-input formControlName="name" rows="1" inputTrim>
          </textarea>
        </nz-textarea-count>
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label nzFor="code" nzSpan="7" nzRequired>
        {{ translateName + '状态' | translate }}
      </nz-form-label>
      <nz-form-control nzSpan="17">
        <nz-switch formControlName="status"></nz-switch>
      </nz-form-control>
    </nz-form-item>
  </form>
</ng-template>
<ng-template #RoleLog>
  <div style="margin-bottom: 12px; padding: 9px 24px; background: #f9faff; border-radius: 0px 0px 4px 4px">
    <app-search-container (reset)="resetLogSearch()" [showFoldBtn]="false">
      <div class="searchLine" *ngIf="btnArr.includes('settings:role-logSearch')">
        {{ translateName + '操作时间' | translate }}：<nz-range-picker
          [nzDisabled]="isEdit"
          [(ngModel)]="RoleLogSearchOptions.gen_time"
          (ngModelChange)="setLogOptions('gen_time', $event)"
          [nzPlaceHolder]="['placeholder.start' | translate, 'placeholder.end' | translate]"></nz-range-picker>
      </div>
      <div class="searchLine" *ngIf="btnArr.includes('settings:role-logSearch')">
        {{ translateName + '操作人' | translate }}：<nz-select
          [nzDisabled]="isEdit"
          [ngModel]="RoleLogSearchOptions.gen_user"
          (ngModelChange)="setLogOptions('gen_user', $event)"
          (nzOnSearch)="getLogOptions('gen_user', $event)"
          [nzShowArrow]="true"
          [nzServerSearch]="false"
          nzShowSearch
          nzAllowClear
          [nzPlaceHolder]="'placeholder.select' | translate">
          <nz-option *ngFor="let item of RoleLogGenUserOptions" [nzLabel]="item" [nzValue]="item"></nz-option>
        </nz-select>
      </div>
      <div class="searchLine" *ngIf="btnArr.includes('settings:role-logSearch')">
        {{ translateName + '操作类型' | translate }}：<nz-select
          [nzDisabled]="isEdit"
          [ngModel]="RoleLogSearchOptions.operation"
          (ngModelChange)="setLogOptions('operation', $event)"
          (nzOnSearch)="getLogOptions('operation', $event)"
          [nzShowArrow]="true"
          [nzServerSearch]="false"
          nzShowSearch
          nzAllowClear
          [nzPlaceHolder]="'placeholder.select' | translate">
          <nz-option *ngFor="let item of RoleLogOperationOptions" [nzLabel]="item" [nzValue]="item"></nz-option>
        </nz-select>
      </div>
      <div class="searchLine" *ngIf="btnArr.includes('settings:role-logSearch')">
        {{ translateName + '权限名称' | translate }}：<nz-select
          [nzDisabled]="isEdit"
          [ngModel]="RoleLogSearchOptions.role_name"
          (ngModelChange)="setLogOptions('role_name', $event)"
          (nzOnSearch)="getLogOptions('role_name', $event)"
          [nzShowArrow]="true"
          [nzServerSearch]="false"
          nzShowSearch
          nzAllowClear
          [nzPlaceHolder]="'placeholder.select' | translate">
          <nz-option *ngFor="let item of RoleLogRoleNameOptions" [nzLabel]="item" [nzValue]="item"></nz-option>
        </nz-select>
      </div>
    </app-search-container>
  </div>
  <nz-table
    [nzScroll]="{ y: '400px' }"
    #basicTable
    [nzFrontPagination]="false"
    [nzShowSizeChanger]="true"
    [(nzPageIndex)]="RoleLogIndex"
    [(nzPageSize)]="RoleLogLimit"
    [nzPageSizeOptions]="[10, 20, 30, 40, 50]"
    [nzShowTotal]="totalTemplate"
    [nzTotal]="RoleLogCount"
    (nzPageIndexChange)="getLogList()"
    (nzPageSizeChange)="getLogList(true)"
    [nzData]="RoleLogList"
    [nzLoading]="RoleLogLoading"
    nzBordered="ture"
    nzSize="middle">
    <thead>
      <tr>
        <th>#</th>
        <th>
          {{ translateName + '操作时间' | translate }}
          <app-sort-btn style="display: inline-block" [(sortOrder)]="RoleLogGenTime" (sortOrderChange)="getLogList(true)"></app-sort-btn>
        </th>
        <th>{{ translateName + '操作人' | translate }}</th>
        <th>{{ translateName + '操作类型' | translate }}</th>
        <th>{{ translateName + '权限名称' | translate }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of RoleLogList; let index = index">
        <td>{{ index + 1 }}</td>
        <td>{{ data.gen_time | date: 'yyyy/MM/dd HH:mm:ss' }}</td>
        <td>{{ data.gen_user }}</td>
        <td
          [ngClass]="{
            addOperation: data.operation === '新增',
            deleteOperation: data.operation === '删除',
            editOperation: data.operation === '修改'
          }">
          {{ data.operation }}
        </td>
        <td>{{ data.role_name }}</td>
      </tr>
    </tbody>
  </nz-table>
</ng-template>

<ng-template #totalTemplate>
  <div
    style="margin-right: 16px; color: #000000; font-size: 14px"
    [innerHTML]="
      'common.共x条,第x页'
        | translate: { total: RoleLogCount, currentPage: RoleLogIndex, totalPage: RoleLogCount / RoleLogLimit | mathCeil }
    "></div>
</ng-template>
<ng-template #deleteHeadTemplate>
  <div
    style="
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 18px;
      font-weight: 500;
      color: #54607c;
      line-height: 24px;
    ">
    {{ 'btn.delete' | translate }}
  </div>
</ng-template>
<ng-template #deleteTemplate>
  <div>
    <div>
      <div
        *ngIf="deleteTplHasUsers"
        style="border-radius: 0 0 8px 8px; background-color: #f4f7f9; display: flex; justify-content: center; align-items: center">
        {{ translateName + '该权限名称已配置操作权限，删除后不可使用' | translate }}
      </div>
      <div>
        <div style="height: 72px; display: flex; justify-content: center; align-items: center">
          <i nz-icon [nzIconfont]="'icon-jinggao'" style="color: #ff5c33; margin-right: 9px"></i>
          <div class="modalContent">
            {{ translateName + '确定删除' | translate }}
            <span style="color: #138aff">{{ selectedRoleNameItem?.name }}</span>
            ？
          </div>
          <!-- <div class="modalContent">{{ '确定删除权限名称？' | translate }}</div> -->
        </div>
        <div style="display: flex; justify-content: center; align-items: center; column-gap: 8px">
          <button nz-button nzShape="round" nzType="default" (click)="deleteModal!.close()">{{ 'btn.cancel' | translate }}</button>
          <button nz-button nzShape="round" nzType="default" (click)="deleteRoleName()">{{ 'btn.ok' | translate }}</button>
        </div>
      </div>
    </div>
  </div>
</ng-template>
<ng-template #emptyBtnHeadTemplate>
  <div
    style="
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 18px;
      font-weight: 500;
      color: #54607c;
      line-height: 24px;
    ">
    {{ translateName + '提示' | translate }}
  </div>
</ng-template>
<ng-template #emptyBtnTemplate>
  <div>
    <div
      style="
        border-radius: 0 0 8px 8px;
        background-color: #f4f7f9;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 30px;
      ">
      {{ translateName + '功能权限内有菜单为编辑者，尚未配置操作按钮' | translate }}
    </div>
    <div style="height: 70px; display: flex; justify-content: center; align-items: center">
      <i nz-icon [nzIconfont]="'icon-jinggao'" style="color: #ff5c33"></i>
      <div class="modalContent">{{ translateName + '是否继续保存？' | translate }}</div>
    </div>
    <div style="display: flex; justify-content: center; align-items: center; column-gap: 8px">
      <button nz-button nzShape="round" nzType="default" (click)="emptyBtnModal!.close()">{{ 'btn.cancel' | translate }}</button>
      <button nz-button nzShape="round" nzType="default" (click)="save()">{{ 'btn.ok' | translate }}</button>
    </div>
  </div>
</ng-template>
