.wrap {
  display: grid;
  grid-template-columns: 1fr 11px;
}

.background {
  border-radius: 5px;
  background: #ffffff;
  box-shadow: 0px 0px 6px 0px #e4e4e4;
  border: 1px solid #d6dae2;
}

.headerTitle {
  padding: 5px 10px;
  background: #f7f9fa;
  font-size: 16px;
  font-family: PingFangHK-Medium, PingFangHK;
  font-weight: 500;
  color: #222b3c;
  line-height: 22px;
}

.selectLine {
  width: 100%;
  padding: 6px 12px;
  font-size: 14px;
  font-family: PingFangHK-Medium, PingFangHK;
  font-weight: 500;
  color: #222b3c;
  line-height: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  ::ng-deep span:last-child {
    overflow: hidden;
    text-overflow: ellipsis;
    // display: -webkit-box;
    // -webkit-line-clamp: 1;
    // -webkit-box-orient: vertical;
  }

  .pinBtn {
    color: #54607c36;

    &.pinned {
      color: #54607c;
    }

    &.notvisible {
      visibility: hidden;
    }

    &:hover {
      color: #54607cbd;
    }
  }
}

.bottomBar {
  padding: 6px 10px;
  display: flex;
  justify-content: space-evenly;
}

.rightArrow {
  width: 11px;
  display: flex;
  justify-content: center;
  align-items: center;

  > .arrowContent {
    position: relative;
    left: 4px;
    width: 0;
    height: 0;
    border-bottom: 15px solid transparent;
    display: inline-flex;
    align-items: flex-end;
    justify-content: center;
    color: #ffffff;
    border-top: 15px solid transparent;
    border-left: 10px solid #d6dae2;
    border-right: 10px solid transparent;
  }
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}
