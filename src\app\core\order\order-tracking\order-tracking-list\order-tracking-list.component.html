<div #header>
  <flc-search-container [showFoldBtn]="false" class="plan-search-box" [headerTitle]="'排料计算列表'" (reset)="resetAllSelect()">
    <div>
      <span class="search-name">大货单号：</span>
      <flc-dynamic-search-select
        [payLoad]="{ cache: false }"
        [dataUrl]="_service.optionUrl"
        [(ngModel)]="searchData.code"
        [column]="'code'"
        (handleSearch)="onSearch()"
        [optAlwaysReload]="false">
      </flc-dynamic-search-select>
    </div>
    <div>
      <span class="search-name">客户：</span>
      <flc-dynamic-search-select
        [payLoad]="{ cache: false }"
        [dataUrl]="_service.optionUrl"
        [(ngModel)]="searchData.customer"
        [column]="'customer'"
        (handleSearch)="onSearch()"
        [optAlwaysReload]="false">
      </flc-dynamic-search-select>
    </div>
    <div>
      <span class="search-name">款号：</span>
      <flc-dynamic-search-select
        [payLoad]="{ cache: false }"
        [dataUrl]="_service.optionUrl"
        [(ngModel)]="searchData.style_code"
        [column]="'style_code'"
        (handleSearch)="onSearch()"
        [optAlwaysReload]="false">
      </flc-dynamic-search-select>
    </div>
    <div>
      <span class="search-name">下单日期：</span>
      <nz-range-picker [(ngModel)]="searchData.date_range" (ngModelChange)="onSearch()"> </nz-range-picker>
    </div>
    <div>
      <span class="search-name">是否逾期：</span>
      <nz-select
        [nzOptions]="[
          { label: '全部', value: 0 },
          { label: '是', value: 1 },
          { label: '否', value: 2 }
        ]"
        nzPlaceHolder="请选择"
        [(ngModel)]="searchData.overdue"
        (ngModelChange)="onSearch()"></nz-select>
    </div>
  </flc-search-container>
</div>

<div class="tableArea">
  <nz-table
    class="zebra-striped-table"
    #trackingTable
    nzBordered
    [nzScroll]="{ x: '5200px', y: '750px' }"
    [nzData]="dataList"
    [(nzPageIndex)]="currentIndex"
    [(nzPageSize)]="currentSize"
    (nzPageIndexChange)="getTableList()"
    (nzPageSizeChange)="getTableList(true)"
    [nzTotal]="totalCount"
    [nzShowTotal]="totalTemplate"
    [nzPageSizeOptions]="[20, 30, 40, 50]"
    nzShowSizeChanger
    nzSize="middle"
    [nzFrontPagination]="false">
    <thead>
      <tr>
        <th nzWidth="160px" rowspan="2" nzLeft>大货单号</th>
        <th nzWidth="100px" rowspan="2">客户</th>
        <th nzWidth="100px" rowspan="2">品名</th>
        <th nzWidth="100px" rowspan="2">款号</th>
        <th nzWidth="100px" rowspan="2">颜色</th>
        <th nzWidth="100px" rowspan="2">订单数</th>
        <th nzWidth="100px" rowspan="2">最终交期</th>
        <th nzWidth="100px" rowspan="2">面料名称</th>
        <th nzWidth="100px" rowspan="2">辅料名称</th>
        <th colspan="10">业务</th>
        <th colspan="10">采购计划</th>
        <th colspan="4">生产计划</th>
        <th colspan="4">二次工艺</th>
        <th colspan="2">质量管控</th>
        <th colspan="1">成品仓库</th>
        <th rowspan="2" nzRight>
          操作
          <!-- <a
            class="setting-btn"
            [ngStyle]="{ color: '#4D96FF'}"
            (click)="changeHeader($event)">
            <i nz-icon nzType="setting" nzTheme="fill"></i>
          </a> -->
        </th>
      </tr>
      <tr>
        <th nzWidth="100px">下单日期</th>
        <th nzWidth="140px">客人工艺资料<br />齐备日期</th>
        <th nzWidth="140px">FIT试穿样<br />提供日期</th>
        <th nzWidth="140px">面料色卡品质样<br />确认日期</th>
        <th nzWidth="140px">辅料样确认日期</th>
        <th nzWidth="140px">印绣花样<br />确认日期</th>
        <th nzWidth="140px">面料头缸<br />确认日期</th>
        <th nzWidth="140px">产前样确认日期</th>
        <th nzWidth="140px">大货样板日期</th>
        <th nzWidth="140px">客户确认<br />开裁日期</th>
        <th nzWidth="120px">面料划头</th>
        <th nzWidth="120px">供布要求</th>
        <th nzWidth="140px">面料要求<br />到仓日期</th>
        <th nzWidth="120px">面料剩余天数</th>
        <th nzWidth="140px">线材要求<br />到仓日期</th>
        <th nzWidth="120px">线材剩余天数</th>
        <th nzWidth="140px">标牌拉链<br />到仓日期</th>
        <th nzWidth="120px">标牌剩余天数</th>
        <th nzWidth="140px">包装材料要求<br />到仓日期</th>
        <th nzWidth="120px">包装剩余天数</th>
        <th nzWidth="120px">组别</th>
        <th nzWidth="140px">车间上线日期</th>
        <th nzWidth="120px">日产量目标</th>
        <th nzWidth="140px">车间下线日期</th>
        <th nzWidth="120px">二次工艺类型</th>
        <th nzWidth="120px">配套工厂</th>
        <th nzWidth="140px">返厂日期</th>
        <th nzWidth="120px">日返目标</th>
        <th nzWidth="140px">客人中期日期</th>
        <th nzWidth="140px">客人尾期日期</th>
        <th nzWidth="140px">出货日期</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let line of trackingTable.data">
        <td nzLeft>{{ line.order_code | noValue }}</td>
        <td>{{ line.customer | noValue }}</td>
        <td>{{ line.category | noValue }}</td>
        <td>{{ line.style_code | noValue }}</td>
        <td>{{ line.color_names | noValue }}</td>
        <td>{{ line.total_qty | noValue }}</td>
        <td>{{ line.due_times | noValue }}</td>
        <td><flc-text-truncated [data]="line.fabric_material_name" [showRow]="3"></flc-text-truncated></td>
        <td><flc-text-truncated [data]="line.material_name" [showRow]="3"></flc-text-truncated></td>
        <td>{{ line.order_date | dateEmpty: dateFormatter }}</td>
        <td>
          <div class="planAndRealTime">
            <button nz-button nzType="link" class="plan edit" (click)="editDetailModal(line, 0)">
              {{ line.customer_data_plan_time | dateEmpty: dateFormatter }}
            </button>
            <button
              nz-button
              nzType="link"
              class="real edit"
              (click)="editDetailModal(line, 0)"
              [ngClass]="{ red: isRed(line.customer_data_plan_time, line.customer_data_real_time) }">
              {{ line.customer_data_real_time | dateEmpty: dateFormatter }}
            </button>
          </div>
        </td>
        <td>
          <div class="planAndRealTime">
            <button nz-button nzType="link" class="plan edit" (click)="editDetailModal(line, 0)">
              {{ line.fitting_plan_time | dateEmpty: dateFormatter }}
            </button>
            <button
              nz-button
              nzType="link"
              class="real edit"
              (click)="editDetailModal(line, 0)"
              [ngClass]="{ red: isRed(line.fitting_plan_time, line.fitting_real_time) }">
              {{ line.fitting_real_time | dateEmpty: dateFormatter }}
            </button>
          </div>
        </td>
        <td>
          <div class="planAndRealTime">
            <button nz-button nzType="link" class="plan edit" (click)="editDetailModal(line, 0)">
              {{ line.fabric_quality_plan_time | dateEmpty: dateFormatter }}
            </button>
            <button
              nz-button
              nzType="link"
              class="real edit"
              (click)="editDetailModal(line, 0)"
              [ngClass]="{ red: isRed(line.fabric_quality_plan_time, line.fabric_quality_real_time) }">
              {{ line.fabric_quality_real_time | dateEmpty: dateFormatter }}
            </button>
          </div>
        </td>
        <td>
          <div class="planAndRealTime">
            <button nz-button nzType="link" class="plan edit" (click)="editDetailModal(line, 0)">
              {{ line.accessory_plan_time | dateEmpty: dateFormatter }}
            </button>
            <button
              nz-button
              nzType="link"
              class="real edit"
              (click)="editDetailModal(line, 0)"
              [ngClass]="{ red: isRed(line.accessory_plan_time, line.accessory_real_time) }">
              {{ line.accessory_real_time | dateEmpty: dateFormatter }}
            </button>
          </div>
        </td>
        <td>
          <div class="planAndRealTime">
            <button nz-button nzType="link" class="plan edit" (click)="editDetailModal(line, 0)">
              {{ line.printing_plan_time | dateEmpty: dateFormatter }}
            </button>
            <button
              nz-button
              nzType="link"
              class="real edit"
              (click)="editDetailModal(line, 0)"
              [ngClass]="{ red: isRed(line.printing_plan_time, line.printing_real_time) }">
              {{ line.printing_real_time | dateEmpty: dateFormatter }}
            </button>
          </div>
        </td>
        <td>
          <div class="planAndRealTime">
            <button nz-button nzType="link" class="plan edit" (click)="editDetailModal(line, 0)">
              {{ line.fabric_cylinder_plan_time | dateEmpty: dateFormatter }}
            </button>
            <button
              nz-button
              nzType="link"
              class="real edit"
              (click)="editDetailModal(line, 0)"
              [ngClass]="{ red: isRed(line.fabric_cylinder_plan_time, line.fabric_cylinder_real_time) }">
              {{ line.fabric_cylinder_real_time | dateEmpty: dateFormatter }}
            </button>
          </div>
        </td>
        <td>
          <div class="planAndRealTime">
            <button nz-button nzType="link" class="plan edit" (click)="editDetailModal(line, 0)">
              {{ line.preproduction_plan_time | dateEmpty: dateFormatter }}
            </button>
            <button
              nz-button
              nzType="link"
              class="real edit"
              (click)="editDetailModal(line, 0)"
              [ngClass]="{ red: isRed(line.preproduction_plan_time, line.preproduction_real_time) }">
              {{ line.preproduction_real_time | dateEmpty: dateFormatter }}
            </button>
          </div>
        </td>
        <td>
          <div class="planAndRealTime">
            <button nz-button nzType="link" class="plan edit" (click)="editDetailModal(line, 0)">
              {{ line.bulk_samle_plan_time | dateEmpty: dateFormatter }}
            </button>
            <button
              nz-button
              nzType="link"
              class="real edit"
              (click)="editDetailModal(line, 0)"
              [ngClass]="{ red: isRed(line.bulk_samle_plan_time, line.bulk_samle_real_time) }">
              {{ line.bulk_samle_real_time | dateEmpty: dateFormatter }}
            </button>
          </div>
        </td>
        <td>
          <div class="planAndRealTime">
            <button nz-button nzType="link" class="plan edit" (click)="editDetailModal(line, 0)">
              {{ line.first_spreading_plan_time | dateEmpty: dateFormatter }}
            </button>
            <div class="real" [ngClass]="{ red: isRed(line.first_spreading_plan_time, line.first_spreading_real_time) }">
              {{ line.first_spreading_real_time | dateEmpty: dateFormatter }}
            </div>
          </div>
        </td>
        <td>
          <flc-text-truncated class="fakeButton" [data]="line.fabric_sign_line" [showRow]="3" (click)="editDetailModal(line, 1)">
          </flc-text-truncated>
        </td>
        <td>
          <flc-text-truncated class="fakeButton" [data]="line.fabric_requirements" [showRow]="3" (click)="editDetailModal(line, 1)">
          </flc-text-truncated>
        </td>
        <td>
          <div class="planAndRealTime">
            <button nz-button nzType="link" class="plan edit" (click)="editDetailModal(line, 1)">
              {{ line.fabric_instock_plan_time | dateEmpty: dateFormatter }}
            </button>
            <div class="real" [ngClass]="{ red: isRed(line.fabric_instock_plan_time, line.fabric_instock_real_time) }">
              {{ line.fabric_instock_real_time | dateEmpty: dateFormatter }}
            </div>
          </div>
        </td>
        <td>{{ differenceByToday(line.fabric_instock_plan_time) }}</td>
        <td>
          <div class="planAndRealTime">
            <button nz-button nzType="link" class="plan edit" (click)="editDetailModal(line, 1)">
              {{ line.thread_instock_plan_time | dateEmpty: dateFormatter }}
            </button>
            <div class="real" [ngClass]="{ red: isRed(line.thread_instock_plan_time, line.thread_instock_real_time) }">
              {{ line.thread_instock_real_time | dateEmpty: dateFormatter }}
            </div>
          </div>
        </td>
        <td>{{ differenceByToday(line.thread_instock_plan_time) }}</td>
        <td>
          <div class="planAndRealTime">
            <button nz-button nzType="link" class="plan edit" (click)="editDetailModal(line, 1)">
              {{ line.zipper_instock_plan_time | dateEmpty: dateFormatter }}
            </button>
            <div class="real" [ngClass]="{ red: isRed(line.zipper_instock_plan_time, line.zipper_instock_real_time) }">
              {{ line.zipper_instock_real_time | dateEmpty: dateFormatter }}
            </div>
          </div>
        </td>
        <td>{{ differenceByToday(line.zipper_instock_plan_time) }}</td>
        <td>
          <div class="planAndRealTime">
            <button nz-button nzType="link" class="plan edit" (click)="editDetailModal(line, 1)">
              {{ line.packing_instock_plan_time | dateEmpty: dateFormatter }}
            </button>
            <div class="real" [ngClass]="{ red: isRed(line.packing_instock_plan_time, line.packing_instock_real_time) }">
              {{ line.packing_instock_real_time | dateEmpty: dateFormatter }}
            </div>
          </div>
        </td>
        <td>{{ differenceByToday(line.packing_instock_plan_time) }}</td>
        <td>{{ line.production_line_names | noValue }}</td>
        <td>
          <div class="planAndRealTime">
            <div class="plan">
              {{ line.sewing_start_plan_time | dateEmpty: dateFormatter }}
            </div>
            <div class="real" [ngClass]="{ red: isRed(line.sewing_start_plan_time, line.sewing_start_real_time) }">
              {{ line.sewing_start_real_time | dateEmpty: dateFormatter }}
            </div>
          </div>
        </td>
        <td>
          <button nz-button nzType="link" (click)="editDetailModal(line, 2)">{{ line.daily_target_qty }}</button>
        </td>
        <td>
          <div class="planAndRealTime">
            <div class="plan">
              {{ line.sewing_end_plan_time | dateEmpty: dateFormatter }}
            </div>
            <div class="real" [ngClass]="{ red: isRed(line.sewing_end_plan_time, line.sewing_end_real_time) }">
              {{ line.sewing_end_real_time | dateEmpty: dateFormatter }}
            </div>
          </div>
        </td>
        <td>{{ line.type_names | noValue }}</td>
        <td>{{ line.factory_names | noValue }}</td>
        <td>
          <div class="planAndRealTime">
            <button nz-button nzType="link" class="plan edit" (click)="editDetailModal(line, 3)">
              {{ line.return_plan_time | dateEmpty: dateFormatter }}
            </button>
            <div class="real" [ngClass]="{ red: isRed(line.return_plan_time, line.return_real_time) }">
              {{ line.return_real_time | dateEmpty: dateFormatter }}
            </div>
          </div>
        </td>
        <td>
          <button nz-button nzType="link" (click)="editDetailModal(line, 3)">{{ line.daily_return_target_qty | noValue }}</button>
        </td>
        <td>
          <div class="planAndRealTime">
            <button nz-button nzType="link" class="plan edit" (click)="editDetailModal(line, 4)">
              {{ line.inspection_mid_plan_time | dateEmpty: dateFormatter }}
            </button>
            <button
              nz-button
              nzType="link"
              class="real edit"
              (click)="editDetailModal(line, 4)"
              [ngClass]="{ red: isRed(line.inspection_mid_plan_time, line.inspection_mid_real_time) }">
              {{ line.inspection_mid_real_time | dateEmpty: dateFormatter }}
            </button>
          </div>
        </td>
        <td>
          <div class="planAndRealTime">
            <button nz-button nzType="link" class="plan edit" (click)="editDetailModal(line, 4)">
              {{ line.inspection_last_plan_time | dateEmpty: dateFormatter }}
            </button>
            <button
              nz-button
              nzType="link"
              class="real edit"
              (click)="editDetailModal(line, 4)"
              [ngClass]="{ red: isRed(line.inspection_last_plan_time, line.inspection_last_real_time) }">
              {{ line.inspection_last_real_time | dateEmpty: dateFormatter }}
            </button>
          </div>
        </td>
        <td>
          <div class="planAndRealTime">
            <button nz-button nzType="link" class="plan edit" (click)="editDetailModal(line, 5)">
              {{ line.outstock_plan_time | dateEmpty: dateFormatter }}
            </button>
            <div class="real" [ngClass]="{ red: isRed(line.outstock_plan_time, line.outstock_real_time) }">
              {{ line.outstock_real_time | dateEmpty: dateFormatter }}
            </div>
          </div>
        </td>
        <td nzRight>
          <a nz-button (click)="editDetailModal(line)" nzType="link">编辑</a>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <ng-template #totalTemplate>
    <div style="margin-right: 16px; color: #000000; font-size: 14px">
      共 {{ totalCount }} 条， 第 <span style="color: #0f86f8">{{ currentIndex }}</span> / {{ totalCount / currentSize | mathCeil }} 页
    </div>
  </ng-template>
</div>
