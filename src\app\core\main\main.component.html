<nz-layout style="min-height: 400px" *ngIf="menu">
  <nz-sider [nzWidth]="isCollapsed ? 0 : 80">
    <div [ngSwitch]="brand">
      <div *ngSwitchCase="'FLKJ_ELAN'">
        <ng-container *ngIf="!isCollapsed">
          <div class="logo text" [innerHTML]="logoText"></div>
        </ng-container>
      </div>
      <div *ngSwitchDefault>
        <ng-container *ngIf="!isCollapsed">
          <div class="logo image" (click)="toDashboard()"></div>
        </ng-container>
      </div>
    </div>
    <div class="side-menu">
      <app-side-bar #sidebar [menu]="menu" (refreshCollapsedStatus)="onRefreshCollapsedStatus()"></app-side-bar>
    </div>
    <!-- <div
      *ngIf="!isCollapsed"
      class="g-toggle"
      (click)="toggleAllMenu()"
      nz-tooltip
      [nzTooltipTitle]="(isAllExpand ? '全部展开' : '全部收起') | translate"
      [nzTooltipPlacement]="['topLeft', 'leftTop']">
      <i nz-icon [nzIconfont]="isAllExpand ? 'icon-zhankai2' : 'icon-shouqi1'"></i>
    </div> -->
    <div
      class="g-fold-icon"
      [ngClass]="{ collapsed: isCollapsed, 'not-collapsed': !isCollapsed }"
      nz-tooltip
      [nzTooltipTitle]="(isCollapsed ? 'main.filed.展开菜单栏' : 'main.filed.折叠菜单栏') | translate"
      [nzTooltipPlacement]="isCollapsed ? ['topLeft', 'leftTop'] : 'top'"
      (click)="toggleCollapsed()">
      <i nz-icon *ngIf="!isCollapsed" [nzType]="'menu-fold'"></i>
      <i nz-icon *ngIf="isCollapsed" [nzType]="'menu-unfold'"></i>
    </div>
  </nz-sider>
  <nz-layout style="height: 100vh; min-width: 1080px">
    <nz-header>
      <!-- <nz-breadcrumb>
        <nz-breadcrumb-item *ngFor="let pathname of breadcrum"> {{ pathname }} </nz-breadcrumb-item>
      </nz-breadcrumb> -->
      <button
        nz-button
        nzType="text"
        nzSize="large"
        style="flex-shrink: 0; align-self: end"
        nz-tooltip
        nzTooltipPlacement="bottomLeft"
        nzTooltipColor="#00000077"
        nzTooltipTrigger="click"
        (nzTooltipVisibleChange)="clickSearchMenu($event)"
        [(nzTooltipVisible)]="searchMenuVisible"
        [nzTooltipTitle]="searchMenuBar">
        <i nz-icon nzType="search" nzTheme="outline"></i>
      </button>
      <ng-template #searchMenuBar>
        <input
          id="searchMenuInput"
          nz-input
          [nzAutocomplete]="autoInput"
          [(ngModel)]="searchMenuInput"
          style="color: #223c4b"
          (ngModelChange)="searchMenu($event)" />
      </ng-template>
      <nz-autocomplete #autoInput [nzDataSource]="searchedMenu"> </nz-autocomplete>
      <!-- <nz-autocomplete #autoInput [compareWith]="compareFun">
        <nz-auto-optgroup *ngFor="let group of groupedSearchMenu" [nzLabel]="group.name">
          <nz-auto-option *ngFor="let leaf of group.children" [nzLabel]="leaf.name" [nzValue]="leaf">{{ leaf.name }}</nz-auto-option>
        </nz-auto-optgroup>
      </nz-autocomplete> -->
      <nz-tabset
        id="switchBar"
        [(nzSelectedIndex)]="currentOpeningMenuIndexing"
        [nzHideAdd]="true"
        nzType="editable-card"
        (nzClose)="closeTab($event)">
        <nz-tab *ngFor="let menu of openMenu; let index = index" [nzClosable]="index !== 0">
          <a *nzTabLink nz-tab-link [routerLink]="menu.url" [queryParams]="menu.params" (click)="switchMenu(index)"
            >{{ menu.name === '首页' ? ('main.filed.首页' | translate) : menu.name }}
          </a>
        </nz-tab>
      </nz-tabset>
      <div style="flex-shrink: 0">
        <!-- 国际化切换选择 -->
        <nz-select
          *ngIf="showSwitchLang || factoryCode === 'JHSCM'"
          [(ngModel)]="lang"
          (ngModelChange)="switchLanguage($event)"
          [nzDropdownMatchSelectWidth]="false">
          <nz-option nzValue="en" nzLabel="En"></nz-option>
          <nz-option nzValue="zh" nzLabel="中文"></nz-option>
        </nz-select>

        <i
          nz-icon
          nzType="clear"
          class="closeAllTabIcon"
          nz-popconfirm
          [nzPopconfirmTitle]="'main.filed.确认清除所有标签页吗' | translate"
          (nzOnConfirm)="closeAllTab()"></i>
        <i
          nz-icon
          [nzIconfont]="'icon-shouji'"
          class="icon-shouji1-class"
          nz-dropdown
          nzTrigger="click"
          [nzDropdownMenu]="qrCodeDropdownMenu"
          nzPlacement="bottom"></i>
        <nz-dropdown-menu #qrCodeDropdownMenu="nzDropdownMenu">
          <div class="qrcode-box">
            <ngx-qrcode [width]="80" [margin]="0" [value]="h5QrCodeUrl" cssClass="mobile-ngx-qrcode-class"> </ngx-qrcode>
            <div class="qrcode-tip-info">{{ 'main.filed.扫码体验手机端' | translate }}</div>
          </div>
        </nz-dropdown-menu>

        <nz-divider nzType="vertical" style="margin: 0 15px"></nz-divider>
        <!-- <nz-badge nzDot [nzShowDot]="isShowBadge" style="vertical-align: text-bottom; margin-right: 16px">
          <i nz-icon [nzIconfont]="'icon-tongzhi'" style="font-size: 18px; cursor: pointer" (click)="showNotice()"></i>
        </nz-badge> -->

        <span class="user-name">{{ user?.login_name }}</span>
        <nz-divider nzType="vertical"></nz-divider>
        <span class="user-name">{{ user?.employee_name }}</span>
        <span nz-tooltip [nzTooltipTitle]="roleName" nzTooltipPlacement="bottomRight" *ngIf="user?.roles">
          <ng-container *ngFor="let role of user.roles; let i = index">
            <span class="user-role" *ngIf="i < 3"> {{ role.role_name }} </span>
          </ng-container>
          <span class="user-role" *ngIf="user.roles.length > 3">···</span>
        </span>
        <a nz-dropdown nzTrigger="click" [nzDropdownMenu]="dropdownMenu" nzPlacement="bottomRight" class="logout">
          <i nz-icon nzType="caret-down"></i>
        </a>
        <nz-dropdown-menu #dropdownMenu="nzDropdownMenu">
          <ul nz-menu>
            <li nz-menu-item class="logout-btn" (click)="logout()">{{ 'login.logout' | translate }}</li>
          </ul>
        </nz-dropdown-menu>
      </div>
    </nz-header>
    <nz-content id="nz-content-container" cdkScrollable>
      <div class="content-container">
        <router-outlet></router-outlet>
      </div>
    </nz-content>
  </nz-layout>
</nz-layout>

<ng-template #noticeTitle>
  <span>{{ 'main.filed.待办事项' | translate }}</span>

  <nz-divider nzType="vertical"></nz-divider>

  <i nz-icon style="margin: 0px 4px 0px 8px">
    <svg t="1709092848952" class="icon" viewBox="0 0 1024 1024" version="1.1" width="14" height="14">
      <path
        d="M0 0m292.571429 0l438.857142 0q292.571429 0 292.571429 292.571429l0 438.857142q0 292.571429-292.571429 292.571429l-438.857142 0q-292.571429 0-292.571429-292.571429l0-438.857142q0-292.571429 292.571429-292.571429Z"
        fill="#FC8334"
        p-id="5844"></path>
      <path
        d="M512 219.428571a292.571429 292.571429 0 1 1 0 585.142858 292.571429 292.571429 0 0 1 0-585.142858z m-36.571429 146.285715a36.571429 36.571429 0 0 0-36.571428 36.571428v146.285715a36.571429 36.571429 0 0 0 37.302857 36.571428H621.714286a36.571429 36.571429 0 0 0 0-73.142857L512 511.926857V402.285714a36.571429 36.571429 0 0 0-36.571429-36.571428z"
        fill="#FFFFFF"
        p-id="5845"></path>
    </svg>
  </i>
  <span style="font-size: 14px">{{ 'main.filed.可能超时' | translate }}</span>

  <i nz-icon style="margin: 0px 4px 0px 8px">
    <svg width="16" height="16">
      <path
        d="M32 32m360 0l240 0q360 0 360 360l0 240q0 360-360 360l-240 0q-360 0-360-360l0-240q0-360 360-360Z"
        fill="#FFFFFF"
        p-id="8186"></path>
      <path
        d="M92 92m300 0l240 0q300 0 300 300l0 240q0 300-300 300l-240 0q-300 0-300-300l0-240q0-300 300-300Z"
        fill="#FF4141"
        p-id="8187"></path>
      <path
        d="M563.6 262.28l-266.16 276.6c-7.44 7.98-9.3 15.6-5.58 22.74 5.58 10.74 17.58 12.72 24.66 12.72h125.58l-68.94 174.72c-2.76 9.24-1.14 16.32 4.92 21.24 9 7.32 25.02 13.86 38.16 2.46 8.76-7.56 114.84-106.02 318.18-295.38 6.48-10.26 7.32-18.72 2.4-25.26-4.8-6.6-14.4-9.48-28.56-8.76L576.92 442.4l36-156c0.84-15.6-3.9-25.62-14.4-30.12-10.44-4.44-22.08-2.4-34.86 6z"
        fill="#FFFFFF"
        p-id="8188"></path>
    </svg>
  </i>
  <span style="font-size: 14px">{{ 'main.filed.已超时' | translate }}</span>
</ng-template>
