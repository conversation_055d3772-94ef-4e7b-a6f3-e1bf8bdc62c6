export enum OrderStatus {
  toSubmit = 1, // 待提交
  toAudit = 2, // 待审核
  toModify = 4, // 待修改
  toModifyAudit = 9, // 修改待审核
  modifyAuditReturn = 10, //修改审核未通过
  cancelled = 8, // 已取消
  auditPass = 11, // 审核通过
}

// 大货订单操作按钮
export enum OrderOperateBtnEnum {
  modify = 'modify', // 退回修改
  pass = 'pass', // 审核通过
}

export enum OrderOperateLabelEnum {
  pass = '审核通过',
  modify = '退回修改',
}

export enum SourceTypeEnum {
  '深链' = 4,
  '外部系统' = 5,
  '自建' = 0,
  '导入' = 6,
  'ERP同步' = 7,
}

export enum PageModeEnum {
  new,
  detail,
  copy,
}

// 是不是跟单员
export enum IsMerchandiserEnum {
  yes = 1,
  no = 2,
}

export enum EventTypeEnum {
  refreshList,
}
