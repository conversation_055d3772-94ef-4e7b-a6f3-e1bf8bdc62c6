.search-name {
  font-size: 14px;
  color: #54607c;
}

.quit td {
  color: #97999c;
}

.employee-table-box {
  margin-top: 14px;
}

.employee-table-box tr td.to-detail {
  cursor: pointer;
  color: #0066ca;
}

.fuceng {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
::ng-deep app-sort-btn {
  margin-top: -4px;
  display: inline-block;
}
.roles {
  display: inline-block;
  font-size: 12px;
  line-height: 24px;
  color: #515661;
  padding: 0 8px;
  background: #f1f3f6;
  border-radius: 2px;
  margin: 6px 12px 6px 0;
}
.quit td .roles {
  color: #97999c;
}

.head-box {
  display: flex;
}

.head-left {
  position: relative;
  width: 182px;
  height: 244px;
  background: #f7f8fa;
  border-radius: 6px;
  overflow: hidden;
  .person-flag {
    position: absolute;
    right: 8px;
    bottom: 8px;
    display: block;
    width: 64px;
    height: 64px;
    padding: 4px;
    box-sizing: border-box;
    text-align: center;
    border-radius: 40px;
    color: rgba($color: #4d96ff, $alpha: 0.6);
    border: 2px solid rgba($color: #4d96ff, $alpha: 0.6);
    span {
      display: block;
      width: 100%;
      height: 100%;
      font-size: 18px;
      line-height: 56px;
      border-radius: 40px;
      border: 1px dotted rgba($color: #4d96ff, $alpha: 0.6);
    }
  }
  .quit {
    color: rgba($color: #ff7070, $alpha: 0.6);
    border: 2px solid rgba($color: #ff7070, $alpha: 0.6);
    span {
      border: 1px dotted rgba($color: #ff7070, $alpha: 0.6);
    }
  }
  img {
    width: 100%;
    height: 100%;
  }
  .iconfont {
    font-size: 180px;
    color: #edf0f7;
    line-height: 244px;
    text-align: center;
  }
}

.head-right {
  margin-left: 16px;
  font-size: 18px;
  color: #222b3c;
  line-height: 24px;
  span {
    margin-top: 3px;
    font-size: 14px;
    color: #54607c;
    line-height: 20px;
  }
  .roles-name {
    margin-top: 10px;
    font-size: 14px;
    color: #54607c;
    line-height: 20px;
  }
  .null-role {
    margin-top: 10px;
    font-size: 14px;
    color: #54607c;
    line-height: 20px;
  }
  .roles-list {
    width: 228px;
    height: 176px;
    border-radius: 4px;
    overflow-y: auto;
    border: 1px solid #d4d7dc;
    .item {
      overflow: hidden;
    }
    .index {
      float: left;
      margin: 8px 4px 0 12px;
      width: 24px;
      height: 24px;
      font-size: 14px;
      line-height: 24px;
      text-align: center;
      color: #fff;
      background: #4d96ff;
      border-radius: 6px;
    }
    li {
      float: left;
      margin: 8px 16px 0 0;
      padding-left: 8px;
      width: 150px;
      height: 24px;
      font-size: 12px;
      color: #222b3c;
      background: #f4f7f9;
      .san {
        float: right;
        border-width: 12px;
        border-color: transparent #fff transparent transparent;
        border-style: dotted dotted dotted solid;
      }
      &:last-child {
        margin: 8px 16px 8px 0;
      }
    }
  }
}
