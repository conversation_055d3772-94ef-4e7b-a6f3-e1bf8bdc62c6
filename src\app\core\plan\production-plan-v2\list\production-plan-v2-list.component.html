<app-plan-list-search
  *ngIf="isHold"
  #appPlanListSearch
  [searchData]="searchData"
  [searchOptions]="searchOptions"
  (onSearch)="onSearch()"
  [isDisabled]="isEdit"></app-plan-list-search>
<div class="production-plan-back">
  <app-plan-list-header (reset)="onReset()" (hold)="onHold()">
    <app-operate-button
      [isEdit]="isEdit"
      [isHold]="isHold"
      [allocateNum]="allocateNum"
      (onButtonAction)="onButtonAction($event)"></app-operate-button>
  </app-plan-list-header>
  <div class="plan-gantt-table" [ngStyle]="{ height: contentHeight + 'px' }" #ganttTable>
    <app-plan-production-line-view
      [isEdit]="isEdit"
      [searchOptions]="searchOptions"
      (changeEditValue)="isEdit = true"
      (onSearchWithParams)="onSearchWithParams($event)"></app-plan-production-line-view>
  </div>
</div>
