<div
  class="graph-wrap"
  [ngClass]="{ isHeightLight: data.rawData.isHeightLight, isSelected: data.isSelected }"
  style="height: '52px'"
  row-no-start
  (contextmenu)="isEdit ? rightClick($event) : null"
  #graphWrap>
  <ng-container [ngSwitch]="options.dimension">
    <ng-template [ngSwitchCase]="'day'">
      <ng-container *ngFor="let week of weeks; index as i">
        <div
          class="day-cell"
          style="flex-shrink: 0"
          [ngStyle]="{ width: options.signalWidth + 'px' }"
          [ngClass]="{
            isRestDay: isRestDay(day),
            isHeightLight: data.rawData.isHeightLight,
            isLastDay: last
          }"
          *ngFor="let day of week?.children; index as index; last as last"
          ganttDropContainer
          (dropData)="dropData($event, i, index, 'day')"
          row-no-center>
          <div class="day-today" *ngIf="day.isToday"></div>
        </div>
      </ng-container>
    </ng-template>
    <ng-template [ngSwitchCase]="'hour'">
      <ng-container *ngFor="let day of days; index as i">
        <div
          class="day-cell"
          style="flex-shrink: 0"
          [ngStyle]="{ width: options.signalWidth + 'px' }"
          [ngClass]="{
            isRestDay: isRestDay(day),
            isHeightLight: data.rawData.isHeightLight,
            'day-today': day.isToday && hour === 12,
            isLastHour: hour === 24
          }"
          *ngFor="let hour of hours; index as hoursIndex"
          ganttDropContainer
          (dropData)="dropData($event, i, hoursIndex, 'hour')"
          row-no-center></div>
      </ng-container>
    </ng-template>
  </ng-container>
  <ng-container *ngFor="let item of data.OrderRenderList">
    <app-material-graph-item
      [isEdit]="isEdit"
      [data]="item"
      [options]="options"
      [rest_list]="data.rest_days"
      [graphOptions]="graphOptions"
      [showTag]="showTag"
      (hasMove)="graphItemMove($event)"
      (tapGraphItem)="graphItemClick($event)"
      (rightClickGraph)="graphItemRightClick($event)"
      (hoverGraphItem)="graphItemHover($event)"
      (leaveGraphItem)="graphItemLeave()"
      (dropGraphItem)="graphItemDrop($event)">
    </app-material-graph-item>
  </ng-container>
</div>
