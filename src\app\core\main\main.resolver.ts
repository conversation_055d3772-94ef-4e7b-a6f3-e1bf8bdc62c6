import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { forkJoin, Observable } from 'rxjs';
import { MainService } from './main.service';

@Injectable()
export class MainResolverService implements Resolve<any> {
  constructor(private _mainService: MainService) {}

  resolve(_route: ActivatedRouteSnapshot, _state: RouterStateSnapshot) {
    return new Observable((observer) => {
      forkJoin([this._mainService.getMenu(), this._mainService.getUser()]).subscribe((res) => {
        if (res[0].code === 200 && res[1].code === 200) {
          observer.next({
            menu: res[0].data.resources,
            user: res[1].data,
          });
        }
        observer.complete();
      });
    });
  }
}
