<div class="dashboard">
  <div class="inspacetionBoard">
    <nz-form [formGroup]="inspectionForm">
      <nz-form-item *ngFor="let cf of basicInfoConfig">
        <nz-form-label [nzSpan]="lang === 'en' ? 8 : 6">{{ translateName + cf.label | translate }}</nz-form-label>
        <nz-form-control [nzSpan]="lang === 'en' ? 16 : 18">
          <flc-text-truncated [data]="orderInfo?.[cf.key]"></flc-text-truncated>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label nzRequired [nzSpan]="lang === 'en' ? 8 : 6">{{ translateName + '颜色' | translate }}</nz-form-label>
        <nz-form-control [nzSpan]="lang === 'en' ? 16 : 18" [flcErrorTip]="translateName + '颜色' | translate">
          <nz-select
            formControlName="color_list"
            nzShowSearch
            nzAllowClear
            nzMode="multiple"
            (ngModelChange)="handleChangeColor($event)"
            [nzPlaceHolder]="'placeholder.select' | translate">
            <nz-option *ngFor="let item of colorList" [nzValue]="item.color_id" [nzLabel]="item.color_name"> </nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label nzRequired [nzSpan]="lang === 'en' ? 8 : 6">{{ translateName + '尺码' | translate }}</nz-form-label>
        <nz-form-control [nzSpan]="lang === 'en' ? 16 : 18" [flcErrorTip]="translateName + '尺码' | translate">
          <nz-select
            formControlName="size_list"
            nzShowSearch
            nzAllowClear
            nzMode="multiple"
            [nzPlaceHolder]="'placeholder.select' | translate">
            <nz-option *ngFor="let item of displaySizeList" [nzValue]="item.spec_id" [nzLabel]="item.spec_size"> </nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label nzRequired [nzSpan]="lang === 'en' ? 8 : 6">{{ translateName + '检验数' | translate }}</nz-form-label>
        <nz-form-control [nzSpan]="lang === 'en' ? 16 : 18" [flcErrorTip]="translateName + '检验数' | translate">
          <nz-input-number
            [nzPlaceHolder]="'placeholder.input' | translate"
            formControlName="inspected_qty"
            [nzMin]="0"
            [nzStep]="1"
            (ngModelChange)="handleChangeQty()">
          </nz-input-number>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="lang === 'en' ? 8 : 6">{{ translateName + '疵品数' | translate }}</nz-form-label>
        <nz-form-control [nzSpan]="lang === 'en' ? 16 : 18">
          <nz-input-number
            [nzPlaceHolder]="'placeholder.input' | translate"
            formControlName="defected_qty"
            [nzMin]="0"
            [nzMax]="inspectionForm.value.inspected_qty"
            [nzStep]="1"
            (ngModelChange)="handleChangeQty()">
          </nz-input-number>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="lang === 'en' ? 8 : 6">{{ translateName + '合格数' | translate }}</nz-form-label>
        <nz-form-control [nzSpan]="lang === 'en' ? 16 : 18">
          <nz-input-number
            [nzPlaceHolder]="translateName + '自动计算' | translate"
            nzDisabled
            [ngModel]="displayItem.qualified_qty"
            [ngModelOptions]="{ standalone: true }"
            [nzMin]="0"
            [nzStep]="1">
          </nz-input-number>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="lang === 'en' ? 8 : 6">{{ translateName + '合格率' | translate }}</nz-form-label>
        <nz-form-control [nzSpan]="lang === 'en' ? 16 : 18">
          <nz-input-number-group nzAddOnAfter="%" style="width: 100%">
            <nz-input-number
              [nzPlaceHolder]="translateName + '自动计算' | translate"
              nzDisabled
              [ngModel]="displayItem.qualifiedPercent"
              [ngModelOptions]="{ standalone: true }"
              [nzMin]="0"
              [nzStep]="1">
            </nz-input-number>
          </nz-input-number-group>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="lang === 'en' ? 8 : 6">{{ translateName + '疵品率' | translate }}</nz-form-label>
        <nz-form-control [nzSpan]="lang === 'en' ? 16 : 18">
          <nz-input-number-group nzAddOnAfter="%" style="width: 100%">
            <nz-input-number
              [nzPlaceHolder]="translateName + '自动计算' | translate"
              nzDisabled
              [ngModel]="displayItem.defectedPercent"
              [ngModelOptions]="{ standalone: true }"
              [nzMin]="0"
              [nzStep]="1">
            </nz-input-number>
          </nz-input-number-group>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="lang === 'en' ? 8 : 6">{{ translateName + '结果' | translate }}</nz-form-label>
        <nz-form-control [nzSpan]="lang === 'en' ? 16 : 18">
          <button
            style="margin-right: 12px"
            nz-button
            (click)="changeInsecptionStatus(2)"
            [nzType]="inspectionForm.value.inspected_status === 2 ? 'primary' : null"
            nzDanger>
            {{ translateName + '不合格' | translate }}
          </button>
          <button
            nz-button
            (click)="changeInsecptionStatus(1)"
            [flButton]="inspectionForm.value.inspected_status === 1 ? 'pretty-primary' : 'pretty-minor'">
            {{ translateName + '合格' | translate }}
          </button>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="lang === 'en' ? 8 : 6">{{ translateName + '备注' | translate }}</nz-form-label>
        <nz-form-control [nzSpan]="lang === 'en' ? 16 : 18">
          <nz-textarea-count [nzMaxCharacterCount]="50">
            <textarea
              style="min-height: 80px"
              rows="3"
              nz-input
              [maxlength]="50"
              formControlName="remark"
              [placeholder]="'placeholder.input' | translate"
              nzAutosize
              inputTrim></textarea>
          </nz-textarea-count>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="lang === 'en' ? 8 : 6">{{ translateName + '图片' | translate }}</nz-form-label>
        <nz-form-control [nzSpan]="lang === 'en' ? 16 : 18">
          <flc-file-gallery
            [fileMaxCount]="10"
            [fileList]="inspectionForm.value.pictures"
            [isEditMode]="true"
            galleryType="image"
            [addBtnText]="'common.添加图片' | translate"
            [wrap]="true"
            [needWatermark]="true"
            (onDeleted)="uploadImage($event)"
            (onUploaded)="uploadImage($event)">
          </flc-file-gallery>
        </nz-form-control>
      </nz-form-item>
    </nz-form>
  </div>
  <nz-divider style="margin: 12px 0 0 0" nzType="horizontal"></nz-divider>
  <div class="footer">
    <button nz-button nzType="default" (click)="showInspectionHistory()">{{ translateName + '历史记录' | translate }}</button>
    <button nz-button nzType="default" (click)="cancel()">{{ 'btn.cancel' | translate }}</button>
    <button nz-button nzType="primary" (flcClickStop)="save()">{{ 'btn.save' | translate }}</button>
  </div>
</div>

<ng-template #inspectionHistoryTemplate>
  <div class="orderInfo">
    <div class="orderItem">
      <span>{{ translateName + '节点名称' | translate }}:</span>{{ nodeInfo.template_node_name }}
    </div>
    <nz-divider nzType="vertical"></nz-divider>
    <div class="orderItem"><span>IO:</span>{{ orderInfo.bulk_order_code }}</div>
    <nz-divider nzType="vertical"></nz-divider>
    <div class="orderItem">
      <span>{{ translateName + '交期' | translate }}:</span>{{ orderInfo.max_due_time | date: 'yyyy-MM-dd' }}
    </div>
    <nz-divider nzType="vertical"></nz-divider>
    <div class="orderItem">
      <span>{{ translateName + '订单总数' | translate }}:</span>{{ orderInfo.order_quantity }}
    </div>
  </div>
  <nz-table
    nzBordered
    class="zebra-striped-table"
    #inspectionHistoryTable
    nzSize="small"
    [nzData]="inspectionHistoryList"
    [nzFrontPagination]="false">
    <thead>
      <tr>
        <th>{{ translateName + '提交日期' | translate }}</th>
        <th>{{ translateName + '颜色' | translate }}</th>
        <th>{{ translateName + '尺码' | translate }}</th>
        <th>{{ translateName + '检验数' | translate }}</th>
        <th>{{ translateName + '合格数' | translate }}</th>
        <th>{{ translateName + '合格率' | translate }}</th>
        <th>{{ translateName + '疵品数' | translate }}</th>
        <th>{{ translateName + '疵品率' | translate }}</th>
        <th>{{ translateName + '结果' | translate }}</th>
        <th>{{ translateName + '图片' | translate }}</th>
        <th>{{ translateName + '备注' | translate }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let line of inspectionHistoryTable.data">
        <td>{{ line.finished_time | date: 'yyyy-MM-dd' }}</td>
        <td><flc-table-body-render [data]="line.displayColors" type="text"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="line.displaySizes" type="text"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="line.inspected_qty" type="quantity"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="line.qualified_qty" type="quantity"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="line.qualifiedPercent + '%'" type="text"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="line.defected_qty" type="quantity"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="line.defectedPercent + '%'" type="text"></flc-table-body-render></td>
        <td>
          <span [ngClass]="line.inspected_status === 1 ? 'success' : 'danger'">{{
            translateName + (line.inspected_status === 1 ? '合格' : '不合格') | translate
          }}</span>
        </td>
        <td><flc-table-body-render [data]="line.pictures" type="image"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="line.remark" type="text"></flc-table-body-render></td>
      </tr>
      <tr *ngIf="inspectionHistoryTable.data.length > 0">
        <td colspan="3">{{ translateName + '合计' | translate }}</td>
        <td><flc-table-body-render [data]="inspectionTotalInfo?.inspected_qty" type="quantity"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="inspectionTotalInfo?.qualified_qty" type="quantity"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="inspectionTotalInfo?.qualifiedPercent + '%'" type="text"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="inspectionTotalInfo?.defected_qty" type="quantity"></flc-table-body-render></td>
        <td><flc-table-body-render [data]="inspectionTotalInfo?.defectedPercent + '%'" type="text"></flc-table-body-render></td>
        <td colspan="3"></td>
      </tr>
    </tbody>
  </nz-table>
</ng-template>
