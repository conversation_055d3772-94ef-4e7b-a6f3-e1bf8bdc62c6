.graph-item {
  position: absolute;
  height: 20px;
  border-radius: 2px;
  border: none;
  overflow: hidden;

  &.can-operate-item {
    border: 1px solid #a2abbe;
    cursor: pointer;
    &:hover {
      border-color: #f77615;
    }
  }

  &.isSelected {
    border-width: 1px;
    background-color: #2996ff;
    border-color: #2996ff;
    .operator-title {
      color: #fff;
    }
  }

  &.is-heightlight {
    border-color: #007dff;
    border-width: 2px;
    .operator-title {
      color: #fff;
    }
  }

  .operator-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
  }

  .operator-title {
    flex-grow: 1;
    padding: 0 6px;
    font-size: 10px;
    font-weight: 500;
    color: #272e45;
    line-height: 20px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    overflow: hidden;
    white-space: nowrap;
  }

  .alter-status-tag {
    display: flex;
    align-items: center;
    .sample-circle {
      z-index: 100;
    }
  }

  .graph-item-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
    overflow: hidden;
    flex: 100%;
  }

  &:hover {
    z-index: 5;
    min-width: 16px;
  }
}
