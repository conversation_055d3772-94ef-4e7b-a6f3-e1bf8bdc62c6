/**
 * 产能管理筛选数据返回
 */
export interface CapacityFilterList {
  /** 大货单号 */
  io_codes: string[];
  /** 交付单 */
  po_codes: string[];
  /** 款式编码 */
  style_codes: string[];
}

export interface CapacityListParam {
  /** 交付单 */
  po_code?: string | null;
  /** 大货单号 */
  io_code: string | null;
  /** 款式编码 */
  style_code: string | null;
  size?: number;
  page?: number;
}

export interface CapacityList {
  po_id: number;
  /** 交付单 */
  po_code: string;
  io_id: number;
  /** 大货单号 */
  io_code: string;
  /** 款式编码 */
  style_code: string;
  style_code_uuid: string;
  /** 款式名称 */
  style_name: string;
  /** 款式图 */
  order_picture: string;
  sam: number;
  /** 品名 */
  category: string;
  /** 二次工艺 */
  secondary_process: string;
  /** 交期/件数 */
  do_lines: { due_date: string; qty: number }[];
  /** 已分配 */
  sourced_line: { factory_id: number; factory_name: string; factory_code: string; sourced_qty: number }[];
  /** 是否开启sam编辑 */
  edit: boolean;
}

export interface CapacityGraphParam {
  /** 时间戳 */
  start_date: number;
  /** 时间戳 */
  end_date: number;
}

export enum FactoryType {
  /** 基础版 */
  'base' = 0,
  /** 专业版 */
  'profession' = 1,
}

export interface CapacityGraph {
  factory_id: number;
  /** 工厂编码 */
  factory_code: string;
  /** 工厂名称 */
  factory_name: string;
  /** 擅长品类 */
  main_category: string;
  /** 产能(分钟/天) */
  capacity: string;
  /** 0 基础版 1 专业版 */
  factory_type: FactoryType;
  /** 已排程 */
  factory_schedules: FactorySchedule[];
}

/**
 * 工厂排程
 */
export type FactorySchedule = {
  start_time: string;
  end_time: string;
} & {
  // 自定义计算数据

  /** 左边距离(以分钟记) */
  left: number;
  /** 柱子宽度(以分钟记) */
  width: number;
};

export interface EditSamParam {
  /** 款式编码UUID */
  uuid: string;
  sam: number;
}

export type SearchData = {
  // 工厂产能筛选

  date: [Date, Date];
  factory: null;
} & CapacityListParam;
