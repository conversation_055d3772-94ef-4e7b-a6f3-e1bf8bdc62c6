<div class="main">
  <flc-search-container #searchContainer class="capacity-search-box" [showBtnContainer]="false" *ngIf="!collapse">
    <div>
      {{ 'plan.capacityPlan.款式编码' | translate }}：
      <nz-select
        [(ngModel)]="searchData.style_code"
        (ngModelChange)="filterList()"
        [nzPlaceHolder]="'placeholder.select' | translate"
        [nzDropdownMatchSelectWidth]="false"
        nzDropdownClassName="plan-select-option"
        nzAllowClear
        nzShowSearch
        [nzBackdrop]="true">
        <ng-container *ngFor="let item of searchOptions?.style_codes">
          <nz-option [nzValue]="item" [nzLabel]="item"></nz-option>
        </ng-container>
      </nz-select>
    </div>
    <div>
      {{ 'plan.capacityPlan.大货单号' | translate }}：
      <nz-select
        [nzPlaceHolder]="'placeholder.select' | translate"
        [nzDropdownMatchSelectWidth]="false"
        nzDropdownClassName="plan-select-option"
        [(ngModel)]="searchData.io_code"
        (ngModelChange)="filterList()"
        nzAllowClear
        nzShowSearch
        [nzBackdrop]="true">
        <ng-container *ngFor="let item of searchOptions?.io_codes">
          <nz-option [nzValue]="item" [nzLabel]="item"></nz-option>
        </ng-container>
      </nz-select>
    </div>

    <div *ngIf="isPo">
      {{ '交付单号' | translate }}：
      <nz-select
        [nzPlaceHolder]="'placeholder.select' | translate"
        [nzDropdownMatchSelectWidth]="false"
        nzDropdownClassName="plan-select-option"
        [(ngModel)]="searchData.po_code"
        (ngModelChange)="filterList()"
        nzAllowClear
        nzShowSearch
        [nzBackdrop]="true">
        <ng-container *ngFor="let item of searchOptions?.po_codes">
          <nz-option [nzValue]="item" [nzLabel]="item"></nz-option>
        </ng-container>
      </nz-select>
    </div>

    <div>
      {{ 'plan.capacityPlan.加工厂' | translate }}：
      <nz-select
        [nzPlaceHolder]="'placeholder.select' | translate"
        [nzDropdownMatchSelectWidth]="false"
        nzDropdownClassName="plan-select-option"
        [(ngModel)]="searchData.factory"
        nzAllowClear
        nzShowSearch
        [nzBackdrop]="true">
        <ng-container *ngFor="let item of _service.factories">
          <nz-option [nzValue]="item.factory_id" [nzLabel]="item.factory_name"></nz-option>
        </ng-container>
      </nz-select>
    </div>
    <div>
      {{ 'plan.capacityPlan.时间' | translate }}：

      <nz-range-picker [nzRanges]="ranges" [nzAllowClear]="false" (ngModelChange)="dateChange($event)" [(ngModel)]="searchData.date">
      </nz-range-picker>
    </div>
  </flc-search-container>

  <div class="capacity-panel order-panel" *ngIf="!showFullCapacity">
    <flc-title-bar [title]="title" [action]="action" (reset)="reset()" (expandChange)="expandChange()">
      <ng-template #title> {{ 'plan.capacityPlan.待分配订单' | translate }} </ng-template>

      <ng-template #action>
        <button *ngIf="isPo" nz-button flToggleButton (click)="changeDimension()" nzShape="round" class="io-button">
          <i nz-icon nzType="swap" nzTheme="outline"></i>
          {{ 'plan.capacityPlan.订单维度' | translate }}
        </button>

        <button *ngIf="!isPo" nz-button flToggleButton (click)="changeDimension()" nzShape="round" class="po-button">
          <i nz-icon nzType="swap" nzTheme="outline"></i>
          {{ 'plan.capacityPlan.交付单维度' | translate }}
        </button>

        <nz-divider nzType="vertical"></nz-divider>

        <i
          nz-icon
          [nzIconfont]="showFullOrder ? 'icon-shouqi-weixuanzhong' : 'icon-zhankai1'"
          nz-button
          nz-tooltip
          [nzTooltipTitle]="(showFullOrder ? 'plan.capacityPlan.收起全屏' : 'plan.capacityPlan.展开全屏') | translate"
          nzTooltipPlacement="top"
          class="expand-icon"
          (click)="showFullOrder = !showFullOrder"></i>
      </ng-template>
    </flc-title-bar>

    <flc-table
      style="flex: 1; overflow: hidden"
      #tableRef
      [tableHeader]="renderHeader"
      [tableConfig]="tableConfig"
      [template]="tableTemplate"
      (sizeChanges)="onSizeChanges($event)"
      (indexChanges)="onIndexChange($event)">
    </flc-table>

    <ng-template #tableTemplate let-data="data">
      <ng-container [ngSwitch]="data.key">
        <ng-container *ngSwitchCase="'do_lines'">
          <div *ngFor="let item of data.item.do_lines" nz-row [nzAlign]="'middle'" style="line-height: 18px; padding: 2px 0px">
            <div nz-col nzSpan="14" style="text-align: right">
              <flc-table-body-render [data]="item.due_date | date: 'yyyy/MM/dd'" type="text"></flc-table-body-render>
            </div>
            <div nz-col nzSpan="10">
              <span class="number-span">
                <flc-table-body-render data="{{ item.qty | number }}" type="text"></flc-table-body-render>
              </span>
            </div>
          </div>
        </ng-container>
        <ng-container *ngSwitchCase="'sourced_lines'">
          <div *ngFor="let item of data.item.sourced_lines" nz-row [nzAlign]="'middle'" style="line-height: 18px; padding: 2px 0px">
            <div nz-col nzSpan="13" style="text-align: right">
              <flc-table-body-render [data]="item.factory_name" type="text"></flc-table-body-render>
            </div>
            <div nz-col nzSpan="11">
              <span class="number-span">
                <flc-table-body-render data="{{ item.sourced_qty | number }}" type="text"></flc-table-body-render>
              </span>
            </div>
          </div>
        </ng-container>

        <ng-container *ngSwitchCase="'order_picture'">
          <flc-table-body-render [data]="[{ url: data.item.order_picture }]" type="image"></flc-table-body-render>
        </ng-container>

        <ng-container *ngSwitchCase="'sam'">
          <div
            *ngIf="btnArr.includes('bulk:capacity-plan-update'); else samTmp"
            nz-row
            nzAlign="middle"
            nzJustify="center"
            style="color: #138aff">
            <ng-container *ngIf="!data.item.edit">
              <flc-table-body-render [data]="data.item.sam" type="text"></flc-table-body-render>
              <i nz-icon [nzIconfont]="'icon-xiugai'" style="margin-left: 6px; cursor: pointer" (click)="editSam(data.item)"></i>
            </ng-container>

            <ng-container *ngIf="data.item.edit">
              <input nz-input *ngIf="data.item.edit" [(ngModel)]="data.item.sam" type="text" style="height: 30px; width: 60px" />
              <i
                nz-icon
                [nzIconfont]="'icon-cuohao'"
                style="margin-left: 6px; color: #f96d6d; cursor: pointer"
                (click)="cancelChange(data.item)"></i>

              <i nz-icon [nzIconfont]="'icon-dui1'" style="margin-left: 6px; cursor: pointer" (click)="saveSam(data.item)"></i>
            </ng-container>
          </div>
          <ng-template #samTmp>
            <flc-table-body-render [data]="data.item.sam" type="text"></flc-table-body-render>
          </ng-template>
        </ng-container>

        <ng-container *ngSwitchDefault> <flc-table-body-render [data]="data.item" type="text"></flc-table-body-render></ng-container>
      </ng-container>
    </ng-template>
  </div>

  <div class="capacity-panel" style="flex: 4; overflow: hidden" *ngIf="!showFullOrder">
    <div style="padding: 8px 12px 0px">
      <flc-title-bar [title]="title" [showSearchCtrl]="false" [action]="action">
        <ng-template #title>
          <span>{{ 'plan.capacityPlan.工厂产能' | translate }}</span>

          <nz-divider nzType="vertical"></nz-divider>
          <span style="font-size: 12px">
            {{ 'plan.capacityPlan.示例' | translate }}:
            <span class="plan-icon"></span>
            {{ 'plan.capacityPlan.已排程' | translate }}
          </span>
        </ng-template>

        <ng-template #action>
          <i
            nz-icon
            [nzIconfont]="showFullCapacity ? 'icon-shouqi-weixuanzhong' : 'icon-zhankai1'"
            nz-button
            [(nzTooltipVisible)]="tooltipVisible"
            [nzTooltipTitle]="(showFullCapacity ? 'plan.capacityPlan.收起全屏' : 'plan.capacityPlan.展开全屏') | translate"
            nz-tooltip
            class="expand-icon"
            (click)="showFullCapacity = !showFullCapacity; tooltipVisible = false"></i>
        </ng-template>
      </flc-title-bar>
    </div>

    <app-distribute-order-graph [dates]="dates" [factoryId]="searchData.factory"></app-distribute-order-graph>
  </div>
</div>
