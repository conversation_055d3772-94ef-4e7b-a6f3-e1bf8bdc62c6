<div class="user-detail">
  <span class="title">{{ translateName + '操作用户详情' | translate }}</span>
  <div class="right-btn">
    <!-- 新建按钮 -->
    <ng-container *ngIf="isNew || isEdit; else other">
      <button (click)="cancel()" nz-button flButton="default" nzShape="round">
        {{ 'btn.cancel' | translate }}
      </button>
      <button (click)="submit()" nz-button flButton="pretty-primary" nzShape="round">
        <i nz-icon [nzIconfont]="'icon-tijiao'"></i>{{ 'btn.commit' | translate }}
      </button>
    </ng-container>
    <!-- 详情按钮 -->
    <ng-template #other>
      <button (click)="back()" nz-button flButton="default" nzShape="round">
        {{ 'btn.back' | translate }}
      </button>
      <button
        *ngIf="btnArr.includes('settings:user-delete')"
        (click)="deleUserInfo()"
        nz-button
        nzDanger
        flButton="default-negative-danger"
        nzShape="round"
        [disableOnClick]="1000">
        {{ 'btn.delete' | translate }}
      </button>
      <button *ngIf="btnArr.includes('settings:user-reset')" (click)="password()" nz-button flButton="pretty-minor" nzShape="round">
        {{ translateName + '重置密码' | translate }}
      </button>
      <button *ngIf="btnArr.includes('settings:user-update')" (click)="edit()" nz-button flButton="pretty-primary" nzShape="round">
        <i nz-icon [nzIconfont]="'icon-caozuolan_bianji1'"></i>{{ 'btn.edit' | translate }}
      </button>
    </ng-template>
  </div>
</div>
<div class="user-body" [ngStyle]="{ 'min-height': bodyHeight + 'px' }">
  <div class="user-left">
    <form nz-form [formGroup]="accountForm">
      <ng-container *ngFor="let item of detailFrom">
        <nz-form-item *ngIf="item.visible" class="info-line">
          <nz-form-label class="info-label" [nzRequired]="item.required">
            {{ translateName + item.label | translate }}
          </nz-form-label>
          <ng-container *ngIf="isNew || isEdit; else readOnly">
            <nz-form-control *ngIf="item.type === 'input'" [nzErrorTip]="nameTpl">
              <nz-textarea-count [nzMaxCharacterCount]="item.maxLength" class="inline-count">
                <textarea
                  rows="1"
                  nz-input
                  [ngModel]="detail[item.code]"
                  [formControlName]="item.code"
                  [placeholder]="'placeholder.input' | translate"
                  [maxLength]="item.maxLength"
                  nzAutosize
                  inputTrim></textarea>
              </nz-textarea-count>
            </nz-form-control>
            <nz-form-control *ngIf="item.type === 'password'" [nzErrorTip]="statusTpl">
              <nz-input-group [nzSuffix]="suffixTemplate">
                <input
                  [type]="passwordVisible ? 'text' : 'password'"
                  nz-input
                  [placeholder]="'placeholder.input' | translate"
                  formControlName="password"
                  [maxLength]="16"
                  inputTrim />
              </nz-input-group>
              <ng-template #suffixTemplate>
                <i nz-icon [nzType]="passwordVisible ? 'eye' : 'eye-invisible'" (click)="passwordVisible = !passwordVisible"></i>
              </ng-template>
            </nz-form-control>
            <ng-template #statusTpl>
              <ng-container *ngIf="accountForm?.get('password')?.dirty && accountForm?.get('password')?.hasError('required')">
                {{ 'placeholder.input' | translate }} {{ translateName + '初始密码' | translate }}
              </ng-container>
            </ng-template>
            <nz-form-control *ngIf="item.type === 'switch'">
              <nz-switch [ngModel]="detail[item.code] === '启用' ? true : false" [formControlName]="item.code"></nz-switch>
            </nz-form-control>
            <ng-template #nameTpl>
              <ng-container *ngIf="accountForm?.get(item.code)?.dirty && accountForm?.get(item.code)?.hasError('required')">
                {{ 'placeholder.input' | translate }} {{ translateName + item.label | translate }}
              </ng-container>
              <ng-container *ngIf="item.code === 'name' && accountForm.get('name')?.dirty && accountForm.get('name')?.hasError('pattern')">
                {{ 'form-error.special-characters' | translate }}
              </ng-container>
              <ng-container *ngIf="item.code === 'login_name' && accountForm?.get('login_name')?.pending">
                {{ 'account-drawer.用户编码' | translate }}{{ 'form-error.check-pending' | translate }}
              </ng-container>
              <ng-container
                *ngIf="
                  item.code === 'login_name' && accountForm.get('login_name')?.dirty && accountForm.get('login_name')?.hasError('pattern')
                ">
                {{ 'form-error.special-chinese-characters' | translate }}
              </ng-container>
              <ng-container
                *ngIf="
                  item.code === 'login_name' &&
                  accountForm?.get('login_name')?.dirty &&
                  accountForm?.get('login_name')?.hasError('duplicated')
                ">
                {{ translateName + '用户编码重复' | translate }}
              </ng-container>
            </ng-template>
          </ng-container>
          <!-- 只读状态 -->
          <ng-template #readOnly>
            <ng-container *ngIf="detail">
              <span *ngIf="item?.code === 'status'" class="detail-value">{{
                detail?.status ? (translateName + detail?.status | translate) : '-'
              }}</span>
              <flc-text-truncated
                style="flex: 1"
                *ngIf="item?.code !== 'status'"
                class="detail-value"
                [data]="detail[item.code]"></flc-text-truncated>
            </ng-container>
          </ng-template>
        </nz-form-item>
      </ng-container>
    </form>
  </div>
  <div class="user-right" [ngStyle]="{ height: bodyHeight + 'px', 'overflow-y': 'scroll' }">
    <ng-container *ngIf="isNew || isEdit || detail?.roles.length > 0; else noRoles">
      <div class="roles-head">
        <div class="roles-left">
          <span>{{ translateName + '优先级' | translate }}</span>
          <span *ngIf="isNew || isEdit; else readRole">{{ translateName + '权限分配' | translate }}</span>
          <ng-template #readRole>{{ translateName + '权限名称' | translate }}</ng-template>
        </div>
        <div *ngIf="isNew || isEdit" class="roles-right">{{ translateName + 'drag-notice' | translate }}</div>
      </div>
      <div cdkDropList class="roles-body" (cdkDropListDropped)="drop($event)">
        <div
          class="roles-box"
          *ngFor="let item of rolesList; index as i"
          cdkDrag
          [cdkDragDisabled]="!isEdit && !isNew"
          [cdkDragPreviewContainer]="'parent'"
          cdkDragPreviewClass="drag-item-preview">
          <div class="roles-num">{{ i + 1 }}</div>
          <div class="roles-content">
            <ng-container *ngIf="isNew || isEdit; else readOnly">
              <div class="roles-select">
                <app-dynamic-search-select
                  [dataUrl]="'/user/role/list'"
                  [(ngModel)]="item.role_id"
                  [defaultValue]="{ label: item.role_name, value: item.role_id }"
                  [transData]="{ value: 'role_id', label: 'role_name' }"
                  [column]="'roles'">
                </app-dynamic-search-select>
              </div>
              <i
                *ngIf="rolesList.length > 1"
                class="roles-icon"
                nz-icon
                [nzIconfont]="'icon-jianshao1'"
                style="color: #fe5d56"
                (click)="deleteRoles(i)"></i>
              <i *ngIf="rolesList.length == 1" class="roles-icon" nz-icon [nzIconfont]="'icon-jianshao1'" style="color: #b3b9c9"></i>
              <i class="roles-icon" nz-icon [nzIconfont]="'icon-zengjia1'" style="color: #4d96ff" (click)="addRoles(i)"></i>
            </ng-container>
            <ng-template #readOnly>
              <span class="read-only">{{ item.role_name }}</span>
            </ng-template>
          </div>
          <div *ngIf="isNew || isEdit" class="roles-handle" cdkDragHandle>
            <i class="role-drag" nz-icon nzType="drag" nzTheme="outline"></i>
          </div>
        </div>
      </div>
    </ng-container>
    <!-- 没有权限列表 -->
    <ng-template #noRoles>
      <img class="no-rules" src="../../../../../assets/image/no_roles.png" />
      <p class="no-desc">{{ translateName + '暂无分配权限哟' | translate }}</p>
    </ng-template>
  </div>
</div>
<!-- 重置密码弹窗 -->
<nz-modal [(nzVisible)]="resetPassword" [nzTitle]="translateName + '重置密码' | translate" (nzOnCancel)="resetPassword = false">
  <ng-container *nzModalContent>
    <form nz-form [formGroup]="passwordForm">
      <nz-form-item>
        <nz-form-label [nzSpan]="lang === 'en' ? 10 : 7" nzRequired>{{ translateName + '新密码' | translate }}</nz-form-label>
        <nz-form-control [nzSpan]="12" nzDisableAutoTips [nzErrorTip]="translateName + '请输入新密码' | translate">
          <nz-input-group [nzSuffix]="suffixTemplate1">
            <input
              [type]="passwordNewVisible ? 'text' : 'password'"
              nz-input
              [placeholder]="translateName + '请输入新密码' | translate"
              formControlName="password"
              [maxLength]="16"
              inputTrim
              (ngModelChange)="validateConfirmPassword()" />
          </nz-input-group>
          <ng-template #suffixTemplate1>
            <i nz-icon [nzType]="passwordNewVisible ? 'eye' : 'eye-invisible'" (click)="passwordNewVisible = !passwordNewVisible"></i>
          </ng-template>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="lang === 'en' ? 10 : 7" nzRequired>{{ translateName + '确认密码' | translate }}</nz-form-label>
        <nz-form-control [nzSpan]="12" nzDisableAutoTips [nzErrorTip]="passwordErrorTpl">
          <nz-input-group [nzSuffix]="suffixTemplate2">
            <input
              [type]="passwordComVisible ? 'text' : 'password'"
              nz-input
              [placeholder]="translateName + '请确认密码' | translate"
              formControlName="confirm"
              [maxLength]="16"
              inputTrim
              (ngModelChange)="validateConfirmPassword()" />
          </nz-input-group>
          <ng-template #suffixTemplate2>
            <i nz-icon [nzType]="passwordComVisible ? 'eye' : 'eye-invisible'" (click)="passwordComVisible = !passwordComVisible"></i>
          </ng-template>
          <ng-template #passwordErrorTpl let-control>
            <ng-container *ngIf="control.hasError('required')">{{ translateName + '请输入确认的密码' | translate }}</ng-container>
            <ng-container *ngIf="control.hasError('confirm')">{{ translateName + '密码输入不一致' | translate }}</ng-container>
          </ng-template>
        </nz-form-control>
      </nz-form-item>
    </form>
  </ng-container>
  <div *nzModalFooter>
    <button nz-button flButton="default" nzShape="round" (click)="resetPassword = false">
      {{ 'btn.cancel' | translate }}
    </button>
    <button nz-button flButton="pretty-primary" nzShape="round" (click)="passwordOk()">
      {{ 'btn.ok' | translate }}
    </button>
  </div>
</nz-modal>
<!-- 删除弹窗 -->
<nz-modal
  nzWidth="300px"
  [nzFooter]="null"
  nzWrapClassName="modal-outer"
  [(nzVisible)]="deleUser"
  nzTitle=""
  (nzOnCancel)="deleUser = false"
  (nzOnOk)="deleUserOk()">
  <ng-container *nzModalContent>
    <div style="display: flex; align-items: center; gap: 8px">
      <i nz-icon [nzIconfont]="'icon-jinggao'" style="color: #ff5c33"></i>
      <div
        style="font-size: 16px; font-weight: 500; color: #222b3c"
        [innerHTML]="translateName + '确认删除x吗？' | translate: { name: detail?.name }"></div>
    </div>
    <div style="display: flex; align-items: center; justify-content: flex-end; margin-top: 30px; gap: 8px">
      <button nz-button flButton="default" nzShape="round" (click)="deleUser = false">
        {{ 'btn.cancel' | translate }}
      </button>
      <button nz-button flButton="default" nzShape="round" (click)="deleUserOk()">
        {{ 'btn.ok' | translate }}
      </button>
    </div>
  </ng-container>
</nz-modal>
<!-- 取消当前操作 -->
<!-- <nz-modal
  nzWidth="300px"
  nzWrapClassName="modal-outer"
  [(nzVisible)]="isCancel"
  nzTitle=""
  [nzFooter]="null"
  (nzOnCancel)="isCancel = false"
  (nzOnOk)="cancelOk()">
  <ng-container *nzModalContent>
    <div style="display: flex; align-items: center; gap: 8px">
      <i nz-icon [nzIconfont]="'icon-jinggao'" style="color: #ff5c33"></i>
      <div style="font-size: 16px; font-weight: 500; color: #222b3c">{{ 'sure-notice.cancel' | translate }}</div>
    </div>
    <div style="display: flex; align-items: center; justify-content: flex-end; margin-top: 24px; gap: 8px">
      <button nz-button nzType="text" nzShape="round" (click)="isCancel = false">{{ 'btn.cancel' | translate }}</button>
      <button nz-button nzType="primary" nzShape="round" (click)="cancelOk()">{{ 'btn.ok' | translate }}</button>
    </div>
  </ng-container>
</nz-modal> -->
