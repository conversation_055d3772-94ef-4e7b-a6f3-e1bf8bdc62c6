<flc-app-header [showTemplate]="true" [headerTopTitle]="headerTopTitleTpl" [headerBtn]="headerBtnTmpl"></flc-app-header>
<ng-template #headerTopTitleTpl>
  <div class="head-title">
    {{ 'outsourcingDetail.' + topTitle | translate }}
    <span *ngIf="orderStatus" class="status" [ngClass]="'status' + orderStatus">{{ orderDetail?.status_value }}</span>
  </div>
</ng-template>
<!-- 头部按钮 -->
<ng-template #headerBtnTmpl>
  <div class="btn-box">
    <button *ngIf="['read'].includes(editMode)" nz-button flButton="default" [nzShape]="'round'" (click)="back()">
      {{ 'btn.back' | translate }}
    </button>

    <button *ngIf="['add', 'edit'].includes(editMode)" nz-button flButton="default" [nzShape]="'round'" (click)="cancel()">
      {{ 'btn.cancel' | translate }}
    </button>
    <!-- 更新使用 -->
    <button
      *ngIf="
        ['edit'].includes(editMode) &&
        [OrderStatus.toSubmit].includes(orderStatus) &&
        _service.btnArr.includes('bulk:garment-outsourcing-update')
      "
      nz-button
      flButton="pretty-minor"
      [nzShape]="'round'"
      [flcDisableOnClick]="1000"
      (click)="save()">
      <i nz-icon [nzIconfont]="'icon-baocun'"></i>{{ 'btn.save' | translate }}
    </button>

    <button
      *ngIf="['edit'].includes(editMode) && _service.btnArr.includes('bulk:garment-outsourcing-update')"
      nz-button
      flButton="pretty-primary"
      [nzShape]="'round'"
      (click)="commit()"
      [flcDisableOnClick]="1000">
      <i nz-icon [nzIconfont]="'icon-tijiao'"></i>
      {{ 'btn.commit' | translate }}
    </button>
    <!-- 新建使用 -->
    <button
      *ngIf="
        ['add'].includes(editMode) &&
        [OrderStatus.toSubmit].includes(orderStatus) &&
        _service.btnArr.includes('bulk:garment-outsourcing-create')
      "
      nz-button
      flButton="pretty-minor"
      [nzShape]="'round'"
      [flcDisableOnClick]="1000"
      (click)="save()">
      <i nz-icon [nzIconfont]="'icon-baocun'"></i>{{ 'btn.save' | translate }}
    </button>

    <button
      *ngIf="['add'].includes(editMode) && _service.btnArr.includes('bulk:garment-outsourcing-create')"
      nz-button
      flButton="pretty-primary"
      [nzShape]="'round'"
      (click)="commit()"
      [flcDisableOnClick]="1000">
      <i nz-icon [nzIconfont]="'icon-tijiao'"></i>
      {{ 'btn.commit' | translate }}
    </button>
    <!-- 退回修改 -->
    <button
      *ngIf="
        ['read'].includes(editMode) &&
        [OrderStatus.toAudit, OrderStatus.toModifyAudit].includes(orderStatus) &&
        _service.btnArr.includes('bulk:garment-outsourcing-approval')
      "
      nz-button
      flButton="fault-minor"
      [nzShape]="'round'"
      (click)="modify()"
      [flcDisableOnClick]="1000">
      {{ 'btn.modify' | translate }}
    </button>

    <button
      *ngIf="
        ['read'].includes(editMode) &&
        [OrderStatus.toAudit, OrderStatus.toModifyAudit].includes(orderStatus) &&
        _service.btnArr.includes('bulk:garment-outsourcing-approval')
      "
      nz-button
      flButton="pretty-primary"
      [nzShape]="'round'"
      (click)="pass()">
      {{ 'btn.pass' | translate }}
    </button>
    <!-- 取消订单 -->
    <button
      *ngIf="
        ['read'].includes(editMode) &&
        [OrderStatus.auditPass, OrderStatus.toModify, OrderStatus.modifyAuditReturn].includes(orderStatus) &&
        _service.btnArr.includes('bulk:garment-outsourcing-cancel')
      "
      nz-button
      flButton="default-positive-danger"
      [nzShape]="'round'"
      nzDanger
      (click)="cancelOrder()">
      {{ 'btn.cancelOrder' | translate }}
    </button>

    <button
      *ngIf="
        ['read'].includes(editMode) &&
        ![OrderStatus.toAudit, OrderStatus.toModifyAudit, OrderStatus.cancelled].includes(orderStatus) &&
        _service.btnArr.includes('bulk:garment-outsourcing-update')
      "
      nz-button
      flButton="pretty-primary"
      [nzShape]="'round'"
      (click)="edit()"
      [flcDisableOnClick]="1000">
      <i nz-icon [nzIconfont]="'icon-caozuolan_bianji1'"></i>{{ 'btn.edit' | translate }}
    </button>
  </div>
</ng-template>

<!-- 主体内容 -->
<form nz-form [formGroup]="detailForm">
  <div class="go-container">
    <!-- 退回修改 -->
    <div
      *ngIf="[OrderStatus.toModify, OrderStatus.modifyAuditReturn].includes(orderStatus) && orderDetail?.reason"
      class="reason-container">
      <i nz-icon [nzIconfont]="'icon-cuowu'" class="icon-cuowu"></i>
      <span>{{ 'outsourcingDetail.审核未通过。' | translate }}</span>
      <flc-text-truncated [data]="('outsourcingDetail.原因：' | translate) + orderDetail?.reason"></flc-text-truncated>
    </div>
    <!-- 大货单号 -->
    <div class="io-container dotted-line">
      <div class="form-bg">
        <div nz-row>
          <div nz-col [nzSpan]="16">
            <div nz-row>
              <div *ngFor="let item of detailFormConfig" nz-col [nzSpan]="item?.nzSpan">
                <nz-form-item *ngIf="item.type === 'select'" nz-col [nzGutter]="24" class="form-item form-item-{{ item.type }}">
                  <nz-form-label [nzRequired]="item.required">{{ 'outsourcingDetail.' + item.label | translate }}</nz-form-label>
                  <nz-form-control
                    *ngIf="
                      editMode !== 'read' &&
                      ![OrderStatus.auditPass, OrderStatus.toModifyAudit, OrderStatus.modifyAuditReturn].includes(orderStatus)
                    "
                    [nzSpan]="18"
                    [flcErrorTip]="'outsourcingDetail.' + item.label | translate">
                    <flc-dynamic-search-select
                      class="reset-select-width"
                      [optAlwaysReload]="true"
                      [dataUrl]="searchOptionFetchUrl"
                      [extraOptions]="extraOptions"
                      [defaultValue]="ioDefaultValue"
                      [transData]="{ value: 'value', label: 'label' }"
                      [column]="item.labelKey"
                      [formControlName]="item.valueKey"
                      [canClear]="false"
                      [payLoad]="{ cache: false, production_type: 1 }"
                      (handleSearch)="handleChangeValueIo($event)">
                    </flc-dynamic-search-select>
                  </nz-form-control>
                  <nz-form-control
                    *ngIf="
                      editMode === 'read' ||
                      [OrderStatus.auditPass, OrderStatus.toModifyAudit, OrderStatus.modifyAuditReturn].includes(orderStatus)
                    ">
                    <div style="display: flex; align-items: center">
                      <flc-text-truncated [data]="detailForm.value[item.labelKey]"></flc-text-truncated>
                      <span class="pre-order-tag" *ngIf="io_basic?.is_pre_order === 1">预排单</span>
                    </div>
                  </nz-form-control>
                </nz-form-item>
                <nz-form-item *ngIf="item.type === 'text'" nz-col [nzGutter]="24" class="form-item form-item-{{ item.type }}">
                  <nz-form-label>{{ 'outsourcingDetail.' + item.label | translate }}</nz-form-label>
                  <nz-form-control>
                    <flc-text-truncated [data]="io_basic?.[item.code]"></flc-text-truncated>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>
          </div>
          <div nz-col [nzSpan]="8">
            <app-order-outsourcing-factory-container
              [editMode]="editMode"
              [parentGroup]="detailForm"
              (addFactoryItem)="addFactoryItem()"
              *ngIf="!io_basic?.is_use_plan">
            </app-order-outsourcing-factory-container>
          </div>
        </div>
      </div>
    </div>
    <!-- 生产进度 -->
    <div
      class="process-first"
      *ngIf="[OrderStatus.auditPass, OrderStatus.modifyAuditReturn, OrderStatus.toModifyAudit].includes(orderStatus)">
      <div class="process-item" [ngClass]="prodData?.cutting_qty > 0 ? 'item-actived' : ''">
        <div class="first-item">
          <span>{{ 'outsourcingDetail.裁剪' | translate }}</span
          ><span class="item-sum" style="flex: 8">{{ 'outsourcingDetail.总裁数' | translate }}</span>
        </div>
        <div class="first-desc">
          <span class="process-num">
            <flc-text-truncated
              [data]="
                '(' + ((prodData?.order_count !== 0 ? prodData?.cutting_qty / prodData?.order_count : 0) | percent: '1.0-1') + ')'
              "></flc-text-truncated>
          </span>
          <span>
            <flc-text-truncated [data]="prodData?.cutting_qty | number"></flc-text-truncated>
          </span>
        </div>
      </div>
      <div class="process-item" [ngClass]="prodData?.sewing_qty > 0 ? 'item-actived' : ''">
        <div class="first-item">
          <span>{{ 'outsourcingDetail.车缝' | translate }}</span
          ><span class="item-sum">{{ 'outsourcingDetail.完成数' | translate }}</span>
        </div>
        <div class="first-desc">
          <span class="process-num">
            <flc-text-truncated
              [data]="
                '(' + ((prodData?.order_count !== 0 ? prodData?.sewing_qty / prodData?.order_count : 0) | percent: '1.0-1') + ')'
              "></flc-text-truncated>
          </span>
          <span>
            <flc-text-truncated [data]="prodData?.sewing_qty | number"></flc-text-truncated>
          </span>
        </div>
      </div>
      <div class="process-item" [ngClass]="prodData?.qualified_qty > 0 ? 'item-actived' : ''">
        <div class="first-item">
          <span>{{ 'outsourcingDetail.质检' | translate }}</span
          ><span class="item-sum">{{ 'outsourcingDetail.合格数' | translate }}</span>
        </div>
        <div class="first-desc">
          <span class="process-num">
            <flc-text-truncated
              [data]="
                '(' + ((prodData?.order_count !== 0 ? prodData?.qualified_qty / prodData?.order_count : 0) | percent: '1.0-1') + ')'
              "></flc-text-truncated>
          </span>
          <span>
            <flc-text-truncated [data]="prodData?.qualified_qty | number"></flc-text-truncated>
          </span>
        </div>
      </div>
      <div class="process-item" [ngClass]="prodData?.transport_qty > 0 ? 'item-actived' : ''">
        <div class="first-item">
          <span>{{ 'outsourcingDetail.走货' | translate }}</span
          ><span class="item-sum">{{ 'outsourcingDetail.走货数' | translate }}</span>
        </div>
        <div class="first-desc">
          <span class="process-num">
            <flc-text-truncated
              [data]="
                '(' + ((prodData?.order_count !== 0 ? prodData?.transport_qty / prodData?.order_count : 0) | percent: '1.0-1') + ')'
              "></flc-text-truncated>
          </span>
          <span>
            <flc-text-truncated [data]="prodData?.transport_qty | number"></flc-text-truncated>
          </span>
        </div>
      </div>
      <div class="process-title">{{ 'outsourcingDetail.生产进度' | translate }}：</div>
    </div>
    <div class="io-content" *ngIf="io_id !== 'add' || showMainPanel">
      <!-- 加工厂 -->
      <app-order-outsourcing-card-container [cardPadding]="'4px 8px 12px'" [borderRadiusSize]="'12px'">
        <div formArrayName="factorys">
          <ng-container *ngFor="let factory of factorys.controls; let i = index" [formGroupName]="i">
            <app-order-outsourcing-factory-item
              #factoryItem
              [labelName]="'outsourcingDetail.加工厂' | translate"
              [editMode]="editMode"
              [tableCanEdit]="true"
              [detailForm]="detailForm"
              [parentGroup]="detailForm.get('factorys').controls[i]"
              [orderDetail]="orderDetail"
              [tabs]="pos"
              [surplusPos]="surplusPos"
              [factoryIndex]="i"
              [orderStatus]="orderStatus"
              [io_basic]="io_basic"
              [showOrderFinish]="_service.btnArr.includes('bulk:garment-outsourcing-finished')"
              [showOneClickInbound]="_service.btnArr.includes('bulk:garment-outsourcing-inbound')"
              [showAdvancePayment]="_service.btnArr.includes('bulk:garment-outsourcing-upfront-payment')">
            </app-order-outsourcing-factory-item>
          </ng-container>
        </div>
      </app-order-outsourcing-card-container>

      <div class="dotted-line"></div>

      <!-- 注释掉剩余未分配标签 -->
      <!--
      <app-order-outsourcing-card-container [title]="'outsourcingDetail.剩余未分配' | translate" [borderRadiusSize]="'12px 12px 4px 4px'">
        <app-order-outsourcing-tabs-container
          [tabsBodyTpl]="surplusColorSizeTpl"
          [modelType]="false"
          [showPersonalInfo]="true"
          [tabs]="surplusPos"
          [colorSizeRefs]="colorSizeTableSurplusRefs">
          <ng-template #surplusColorSizeTpl let-data="data">
            <flc-color-size-table
              #colorSizeSurplusTable
              class="rest-color-size-table-padding"
              [dataList]="data?.po_lines"></flc-color-size-table>
          </ng-template>
        </app-order-outsourcing-tabs-container>
        <ng-container *ngIf="surplusLines.length; else surplusNotData">
          <div class="dotted-line factory-dotted-line"></div>
          <app-order-outsourcing-card-container
            [title]="'outsourcingDetail.合计' | translate"
            [showCorner]="true"
            [titleLine]="false"
            [cardPadding]="'0'"
            [borderRadiusSize]="'12px 12px 4px 4px'">
            <div>
              <flc-color-size-table [dataList]="surplusLines"></flc-color-size-table>
            </div>
          </app-order-outsourcing-card-container>
        </ng-container>
        <ng-template #surplusNotData>
          <flc-no-data [flcDisableOnClick]="1000" [noDataTextTpl]="noDataTextTpl" class="surplus-no-data"></flc-no-data>
          <ng-template #noDataTextTpl>
            <span>{{ 'outsourcingMessage.暂无数据，已全部分配完成' | translate }}</span>
          </ng-template>
        </ng-template>
      </app-order-outsourcing-card-container>
      -->

      <div style="margin: 8px 0">
        <app-order-outsourcing-card-container [title]="'outsourcingDetail.基本信息' | translate" [borderRadiusSize]="'4px 4px 4px 4px'">
          <app-order-outsourcing-basic-info *ngIf="io_basic" [basicInfo]="io_basic"></app-order-outsourcing-basic-info>
        </app-order-outsourcing-card-container>
      </div>

      <app-order-outsourcing-card-container [title]="'outsourcingDetail.颜色尺码' | translate">
        <app-order-outsourcing-tabs-container
          [tabsBodyTpl]="colorSizeTpl"
          [tabs]="pos"
          [showPersonalInfo]="true"
          [colorSizeRefs]="colorSizeTableRefs">
          <ng-template #colorSizeTpl let-data="data">
            <flc-color-size-table class="rest-color-size-table-padding" #colorSizeTable [dataList]="data?.po_lines"></flc-color-size-table>
          </ng-template>
        </app-order-outsourcing-tabs-container>
      </app-order-outsourcing-card-container>
      <!-- <合计> -->
      <div class="dotted-line"></div>
      <app-order-outsourcing-card-container
        [title]="'outsourcingDetail.合计' | translate"
        [showCorner]="true"
        [titleLine]="false"
        [borderRadiusSize]="'12px 12px 4px 4px'">
        <div>
          <ng-container *ngIf="io_lines?.length; else io_linesNotData">
            <flc-color-size-table [dataList]="io_lines"></flc-color-size-table>
          </ng-container>
          <ng-template #io_linesNotData>
            <flc-no-data [flcDisableOnClick]="1000" [noDataTextTpl]="noDataTextTpl"></flc-no-data>
            <ng-template #noDataTextTpl>
              <span>{{ 'outsourcingMessage.' + '暂无数据' | translate }}</span>
            </ng-template>
          </ng-template>
        </div>
      </app-order-outsourcing-card-container>
    </div>
  </div>
</form>
<!-- 暂无数据 -->
<div *ngIf="io_id === 'add' && !detailForm.get('io_id').value" class="not-data-container">
  <flc-no-data [flcDisableOnClick]="1000" [noDataTextTpl]="noDataTextTpl"></flc-no-data>
  <ng-template #noDataTextTpl>
    <span>{{ 'outsourcingMessage.暂无数据，请先选择大货单号～' | translate }}</span>
  </ng-template>
</div>
