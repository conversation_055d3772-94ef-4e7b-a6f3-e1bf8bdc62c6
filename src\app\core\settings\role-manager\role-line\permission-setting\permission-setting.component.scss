.container {
  display: flex;
  padding: 12px 0 60px;
  height: 100%;
  gap: 12px;
  .left {
    height: 100%;
    overflow: auto;
    width: 184px;
    background: #f0f3fa;
    border-radius: 4px;
    padding: 0 12px 12px;
    .title {
      position: sticky;
      top: 0;
      z-index: 100;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #f0f3fa;
      padding: 12px 0;
      > span {
        font-weight: 500;
        font-size: 16px;
        color: #222b3c;
      }
    }
    .module-list {
      display: flex;
      flex-direction: column;
      gap: 14px;
      .ant-checkbox-wrapper {
        margin-left: 0;
      }
    }
  }
  .right {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12px;
    overflow: auto;
    .module-item {
      background: #f0f3fa;
      border-radius: 4px;
      display: flex;
      padding: 12px;
      .module {
        width: 162px;
        display: flex;
        align-items: center;
        padding-right: 4px;
        .name {
          margin-right: 4px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .children {
        flex: 1;
        background: #ffffff;
        border-radius: 4px;
        padding: 12px;
        .operation {
          display: flex;
          padding-bottom: 12px;
          .title {
            display: flex;
            align-items: center;
            width: 100px;
            font-weight: 500;
            font-size: 14px;
            color: #222b3c;

            &::before {
              content: '';
              display: block;
              width: 4px;
              height: 12px;
              background: #138aff;
              border-radius: 4px;
              margin-right: 4px;
            }
          }
        }
        .fields-content {
          padding: 12px 0 0;
          display: flex;
          flex-direction: column;
          gap: 8px;
        }
        .fields-group {
          display: flex;
          align-items: flex-start;
          .fields-select-all {
            width: 100px;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: flex-end;
          }
          .fileds-wrap {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
          }
        }
      }

      .toggle-tag {
        white-space: nowrap;
        padding: 2px 5px;
        background: #e7f3fe;
        border-radius: 4px;
        font-weight: 500;
        font-size: 12px;
        color: #138aff;
        cursor: pointer;
        user-select: none;
        &.bordered {
          border: 1px solid #138aff;
          padding: 0 4px;
        }
        &.read {
          background: #d9f7ef;
          color: #00c790;
          &.bordered {
            border: 1px solid #00c790;
            padding: 0 4px;
          }
        }
      }
    }
  }
}

.footer {
  position: absolute;
  width: 100%;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  height: 48px;
  background: #ffffff;
  box-shadow: 0 -1px 3px #ced5de80;
  z-index: 10;
  > button {
    width: 80px;
  }
}
