<nz-modal
  [(nzVisible)]="adjustResultIsVisible"
  [nzTitle]="translateName + '自动调整方案确认' | translate"
  [nzWidth]="'1600px'"
  [nzMaskClosable]="false"
  [nzCloseIcon]="null"
  [nzFooter]="null">
  <div *nzModalContent>
    <nz-table nzBordered #basicTable [nzData]="adjustResult" [nzShowPagination]="false">
      <thead>
        <tr>
          <th>{{ translateName + '大货单号' | translate }}</th>
          <th>{{ translateName + '款式编码' | translate }}</th>
          <th>{{ translateName + 'PO' | translate }}</th>
          <th>{{ translateName + '颜色' | translate }}</th>
          <th>{{ translateName + '开始/结束日期' | translate }}</th>
          <th>{{ translateName + '计划天数' | translate }}</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of basicTable.data">
          <td>{{ data.bulk_code }}</td>
          <td>{{ data.style_code }}</td>
          <td>{{ data.po }}</td>
          <td>{{ data.colour }}</td>
          <td>
            <div class="start-end-date-label">
              <div class="adjust-label">{{ translateName + '调整前' | translate }}</div>
              <div>
                {{ data.before_adjustment?.start_time | date: 'yyyy/MM/dd' }} - {{ data.before_adjustment?.end_time | date: 'yyyy/MM/dd' }}
              </div>
              <nz-divider nzType="vertical"></nz-divider>
              <span class="day-label">{{ data.before_adjustment?.days ?? '' }}{{ translateName + '天' | translate }}</span>
            </div>
            <div class="start-end-date-label start-end-date-label-highlight">
              <div class="adjust-label adjust-label-highlight">{{ translateName + '调整后' | translate }}</div>
              <div>
                {{ data.after_adjustment?.start_time | date: 'yyyy/MM/dd' }} - {{ data.after_adjustment?.end_time | date: 'yyyy/MM/dd' }}
              </div>
              <nz-divider nzType="vertical"></nz-divider>
              <span class="day-label">{{ data.after_adjustment?.days ?? '' }}{{ translateName + '天' | translate }}</span>
            </div>
          </td>
          <td>
            <span class="plan-day-label" [ngClass]="{ select: data.adjust_days <= 0 }">
              {{ translateName + (data.adjust_days < 0 ? '增加' : '减少') | translate }}
              {{ getAdjustDays(data.adjust_days) + '天' | translate }}
            </span>
          </td>
        </tr>
      </tbody>
    </nz-table>
    <div class="modal-footer-notification">
      <button nz-button nzShape="round" flButton="default" (click)="manulAdjust()">
        {{ translateName + '手动调整' | translate }}
      </button>
      <button nz-button nzShape="round" flButton="minor" (click)="confirmed()">
        {{ translateName + '确定' | translate }}
      </button>
    </div>
  </div>
</nz-modal>

<nz-modal
  [(nzVisible)]="tipIsVisible"
  [nzTitle]="translateName + '提示' | translate"
  [nzMaskClosable]="false"
  [nzCloseIcon]="null"
  [nzFooter]="null">
  <div *nzModalContent>
    <span> {{ tip }}</span>
    <div class="modal-footer-notification-left">
      <button nz-button nzShape="round" flButton="default" (click)="handleCancel()">
        {{ translateName + '手动调整' | translate }}
      </button>
    </div>
  </div>
</nz-modal>
