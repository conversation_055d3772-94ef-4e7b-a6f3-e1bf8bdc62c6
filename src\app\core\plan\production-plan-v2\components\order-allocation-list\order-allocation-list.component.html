<div class="searchBarWrap" #searchBarWrap>
  <flc-screen-container (reset)="onReset()" (handleFold)="calcTableHeight()">
    <div class="select-box">
      <div *ngFor="let item of searchList" class="search-box">
        <span class="search-label">{{ item.label }}：</span>
        <nz-select
          *ngIf="item.type === 'select'"
          [(ngModel)]="searchData[item.valueKey]"
          [nzPlaceHolder]="'请选择'"
          nzAllowClear
          [nzDropdownMatchSelectWidth]="false"
          [nzShowSearch]="true"
          (ngModelChange)="getList()">
          <nz-option *ngFor="let option of optionList[item.optionKey] || []" [nzValue]="option.value" [nzLabel]="option.label"> </nz-option>
        </nz-select>
        <nz-range-picker
          *ngIf="item.type === 'date'"
          [nzPlaceHolder]="['开始', '结束']"
          [(ngModel)]="searchData[item.valueKey]"
          (ngModelChange)="onChangeDatePicker()"></nz-range-picker>
      </div>
    </div>
  </flc-screen-container>
</div>
<div style="margin-top: 10px">
  <div class="table-title">
    <div>待分配订单</div>
    <span>
      <i nz-icon [nzIconfont]="'icon-jinggao'"></i>
      <span>进入编辑状态，才能分配订单哦</span>
    </span>
  </div>
  <flc-table
    [tableHeader]="tableHeader"
    [tableConfig]="tableConfig"
    [template]="btnTpl"
    (indexChanges)="indexChanges($event)"
    (sizeChanges)="sizeChanges($event)">
  </flc-table>
  <ng-template let-data="data" #btnTpl>
    <ng-container *ngIf="!data.isAction">
      <ng-container *ngIf="data.key === 'order_code'">
        <div>
          <flc-text-truncated [data]="data.item.order_code"></flc-text-truncated>
        </div>
        <span class="pre-order-tag" *ngIf="data.item.is_pre_order">预</span>
      </ng-container>

      <ng-container *ngIf="data.key === 'due_time_qty_list'">
        <ng-container *ngIf="data.item.due_time_qty_list?.length; else noValue">
          <div *ngFor="let item of data.item.due_time_qty_list">
            {{ item.due_time | date: 'yyyy/MM/dd' }}
            {{ item.qty }}
          </div>
        </ng-container>
      </ng-container>
      <ng-container *ngIf="data.key === 'sam'">
        <div class="sam-box">
          <div>
            <span [ngClass]="{ grey: data.item.selected === 1 }"> {{ data.item.sam || '-' }} </span>
            /
            <span [ngClass]="{ grey: data.item.selected === 2 }">{{ data.item.average_daily_production || '-' }}</span>
          </div>

          <a
            *ngIf="hasEditSam"
            nz-button
            nzType="link"
            nz-popconfirm
            [nzPopconfirmTitle]="samPopConfirmTpl"
            [nzPopconfirmOverlayStyle]="{ width: '400px' }"
            (nzPopconfirmVisibleChange)="onChangeVisible($event, data.item)"
            (nzOnConfirm)="onComrimSam(data.item)"
            nzPopconfirmPlacement="top">
            <i nz-icon nzIconfont="icon-bianjisekuai"></i>
          </a>
        </div>
      </ng-container>
    </ng-container>
    <ng-container *ngIf="data.isAction">
      <div class="order-btn-box">
        <button class="accept-btn" (flcClickStop)="onAllocate(data.item)" nz-button nzType="link" [disabled]="!isEdit">分配</button>
      </div>
    </ng-container>
  </ng-template>
</div>

<ng-template #samPopConfirmTpl>
  <form nz-form [formGroup]="samEditForm">
    <nz-radio-group class="sam-modal" formControlName="selected">
      <nz-form-item>
        <nz-form-label [nzSm]="10">
          <label nz-radio [nzValue]="1">SAM(分钟)</label>
        </nz-form-label>
        <nz-form-control flcErrorTip="SAM">
          <nz-input-number formControlName="sam" placeholder="请输入" [nzMin]="0.01" [nzMax]="99999999" [nzPrecision]="2"></nz-input-number>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSm]="10">
          <label nz-radio [nzValue]="2">人均日台产(件)</label>
        </nz-form-label>
        <nz-form-control flcErrorTip="人均日台产">
          <nz-input-number
            formControlName="average_daily_production"
            placeholder="请输入"
            [nzMin]="1"
            [nzMax]="99999999"
            [nzPrecision]="0"></nz-input-number>
        </nz-form-control>
      </nz-form-item>
    </nz-radio-group>
  </form>
</ng-template>

<ng-template>
  <flc-text-truncated data=""></flc-text-truncated>
</ng-template>
