$departmentLineWidth: 180px;

.wrap {
  height: calc(100vh - 48px - 24px);
  display: flex;
  flex-direction: column;
  row-gap: 12px;
  width: 100%;
  overflow-x: hidden;
}

.departmentListEmpty {
  height: 600px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 400;
  color: #84879a;
  line-height: 20px;
  flex-direction: column;
}

.employeeListEmpty {
  height: 600px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 400;
  color: #84879a;
  line-height: 20px;
  flex-direction: column;
  margin-left: 4px;
  box-shadow: -3px 0px 5px 0px rgb(0 0 0 / 10%);
}

.roleNameDetailFlexWrap {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

.roleNameTitleBar {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  font-size: 16px;
  font-weight: 500;
  color: #222b3c;
  line-height: 24px;
  border-bottom: 1px solid #f1f2f3;
}

:host .roleNameSearchBar {
  ::ng-deep nz-select {
    background-color: #f7f8fa;
    width: 170px;
    border-radius: 30px;

    nz-select-top-control {
      border-radius: 30px;
    }
  }
}

.searchBar {
  > nz-input-group {
    border-radius: 30px;
    background-color: #f7f8fa;
    width: 170px;

    > input {
      background-color: #f7f8fa;
    }
  }
}

.roleNameListWrap {
  height: 100%;
  overflow-y: auto;
  padding: 8px;
  gap: 8px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  grid-template-rows: min-content;
  // display: flex;
  // flex-wrap: wrap;
  // align-content: flex-start;
}

.roleNameItem {
  // width: 100px;
  height: 40px;
  padding: 10px 8px;
  background-color: #f8f8f8;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  color: #222b3c;
  line-height: 20px;
  cursor: grab;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid transparent;
  flex-grow: 1;

  span {
    overflow: hidden;
    text-overflow: ellipsis;
    // display: -webkit-box;
    // -webkit-line-clamp: 1;
    // -webkit-box-orient: vertical;
  }

  &:active {
    cursor: grabbing;
  }

  &.selected {
    background-color: #edf4fb;
    color: #138aff;
  }

  &.edit {
    border: 1px dashed #979797;

    &.selected {
      border-color: #138aff;
    }
  }
}

.roleWrap {
  height: 264px;
  display: flex;
  flex-shrink: 0;

  .roleNameArea {
    width: 50%;
    background-color: #fff;
    box-shadow: 2px 0px 4px 0px rgba(221, 228, 234, 0.5);
    overflow-y: hidden;
    display: flex;
    flex-direction: column;
  }

  .roleNameDetail {
    width: 50%;
    background-color: #fafbfe;
  }
}

.spinning {
  display: flex;
  justify-content: center;
  align-items: center;
  align-content: center;
}

.empty {
  display: flex;
  justify-content: center;
  align-items: center;
  align-content: center;
  font-size: 14px;
  font-weight: 400;
  color: #84879a;
  line-height: 20px;
}

.companyContent {
  flex-grow: 1;
  overflow: hidden;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.companyInfo {
  padding: 12px;
  font-size: 18px;
  font-weight: 500;
  color: #222b3c;
  line-height: 20px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  border-bottom: 1px solid #f1f2f3;

  > .searchBar {
    display: flex;
    align-items: center;

    > nz-input-group {
      margin-left: 20px;
      min-width: 300px;
    }
  }

  > .btnBar {
    display: flex;

    > button {
      width: 80px;
      margin-left: 8px;
    }

    > a {
      background: #f7f8fa;
      border-radius: 4px;
      margin-left: 8px;
    }
  }
}

.departmentWrap {
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  overflow-x: auto;
  overflow-y: hidden;
  height: 100%;
  max-width: 50%;
}

.departmentBoard {
  height: 100%;
  flex-shrink: 0;
  overflow-y: auto;
  overflow-x: hidden;
  box-shadow: 3px 0px 5px 0px rgba(0, 0, 0, 0.1);

  &:not(:first-child) {
    margin-left: 4px;
  }
}

.directlyEmployeeWrap {
  width: $departmentLineWidth;
  padding: 0 4px 4px 4px;

  &.edit {
    padding: 0;
  }
}

.directlyEmployee {
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 30px;
  background: #f7f7f7;
  padding: 0 7px 0 13px;
  border-radius: 0px 0px 4px 4px;
  cursor: pointer;

  > .titleLine {
    font-size: 14px;
    font-weight: 400;
    color: #b3b9c9;
    line-height: 20px;
    display: flex;
    align-items: center;
    margin-bottom: 4px;
  }

  &.edit {
    margin: 0;
    border-radius: 0;
  }

  &.selected {
    > .titleLine {
      color: #138aff;
    }
  }
}

.departmentItem {
  width: $departmentLineWidth;
  cursor: pointer;
  border: 1px dashed transparent;
  padding: 0 7px 0 13px;

  > .titleLine {
    height: 48px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    font-weight: 400;
    color: #222b3c;
    line-height: 22px;

    .title {
      display: flex;
      overflow: hidden;

      div {
        white-space: nowrap;
      }

      span {
        // display: -webkit-box;
        // -webkit-box-orient: vertical;
        // -webkit-line-clamp: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    ::ng-deep svg {
      width: 12px;
      height: 12px;
      color: #9b9fab;
    }
  }

  &.selected {
    background-color: #f7f7f7;

    > .titleLine {
      color: #138aff;

      ::ng-deep svg {
        color: #138aff;
      }
    }
  }

  &.edit {
    border-bottom-color: #d4d7dc;
  }

  &.edit:first-child {
    border-top-color: #d4d7dc;
  }
}

.departmentRoleBoard {
  max-height: 160px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  .RoleLine {
    height: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    padding: 0 8px;
    margin: 3px 0;
    flex-shrink: 0;

    span {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 14px;
      font-weight: 400;
      color: #54607c;
      line-height: 18px;
    }

    i {
      color: #c1c4cc;
    }
  }
}

.employeeBoard {
  background-color: #f7f7f7;
  flex-grow: 1;
  margin-left: 4px;
  min-width: 620px;
  overflow-y: auto;
  box-shadow: -3px 0px 5px 0px rgb(0 0 0 / 10%);

  .employeeItem {
    border-bottom: 1px dashed #d4d7dc;

    .employeeName {
      padding: 12px 15px;
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 500;
      color: #222b3c;
      line-height: 22px;
    }

    .employeeRole {
      padding: 7px 12px;
      display: flex;
      // flex-wrap: nowrap;
      // flex-direction: column;
      flex-wrap: wrap;
      flex-direction: row;
      align-content: flex-start;
      row-gap: 6px;
      column-gap: 12px;

      &.cdk-drop-list-dragging .employeeRoleItem:not(.cdk-drag-placeholder) {
        transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
      }
    }
  }
}

.employeeRoleItem {
  display: flex;
  align-items: center;
  padding: 0 4px;
  background-color: #f7f7f7;

  &.cdk-drag-placeholder {
    opacity: 0;
  }

  .rolePriority {
    width: 16px;
    height: 16px;
    border-radius: 2px;
    background: #138aff;
    font-size: 12px;
    font-weight: 500;
    color: #ffffff;
    text-align: center;
  }

  .roleName {
    margin: 0 8px;
    width: 120px;
    height: 30px;
    padding: 0 8px;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 400;
    color: #54607c;
    line-height: 18px;

    > span {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    > i {
      color: #c1c4cc;
      cursor: pointer;
    }

    &.alone {
      width: 140px;
    }
  }

  .dragHandle {
    color: #138aff;
    cursor: grab;

    &:active {
      cursor: grabbing;
    }
  }
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.tooltip-text {
  font-size: 12px;
  .link {
    text-decoration: underline;
    text-underline-offset: 3px;
  }
}
