/*
 * File: text-truncated.component.ts
 * Project: elan-web
 * File Created: Wednesday, 24th November 2021 5:47:08 pm
 * Author: liucp
 * Description: 一行数组的溢出
 * -----
 * Last Modified: Monday, 29th November 2021 6:40:44 pm
 * Modified By: liucp
 */

import { Component, ElementRef, Input, OnInit } from '@angular/core';
@Component({
  selector: 'app-text-truncated',
  template: `
    <div id="contentDiv" nz-tooltip [nzTooltipTitle]="tooltip">
      <ng-container *ngIf="data">{{ data || '-' }}</ng-container>
      <ng-container *ngTemplateOutlet="template"></ng-container>
    </div>
  `,
  styles: [
    `
      div {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    `,
  ],
})
export class TextTruncatedComponent implements OnInit {
  @Input() template: any;
  @Input() data: any;
  tooltip: any;
  constructor(private ele: ElementRef) {}
  ngOnInit() {}

  ngAfterViewInit() {
    setTimeout(() => {
      const el = this.ele.nativeElement.querySelector('#contentDiv');
      if (el.scrollWidth > el.offsetWidth) {
        this.tooltip = this.template;
      }
    });
  }
}
