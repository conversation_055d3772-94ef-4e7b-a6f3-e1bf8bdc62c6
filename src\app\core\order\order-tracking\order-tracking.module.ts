import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CommonModule, DatePipe } from '@angular/common';
import { OrderTrackingListComponent } from './order-tracking-list/order-tracking-list.component';
import { HttpClient } from '@angular/common/http';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { FlcComponentsModule } from 'fl-common-lib';
import { MaterialPackageModule } from 'fl-sewsmart-lib/material-package';
import { FlButtonModule } from 'fl-ui-angular';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCascaderModule } from 'ng-zorro-antd/cascader';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzResizableModule } from 'ng-zorro-antd/resizable';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import { PipesModule } from 'src/app/pipes/pipes.module';
import { OrderTrackingEditDialogComponent } from './order-tracking-edit-dialog/order-tracking-edit-dialog.component';
import { OrderTrackingRoutingModule } from './order-tracking-routing.module';
import { DateEmptyPipe } from './date-empty.pipe';

const nzModules = [
  FlcComponentsModule,
  NzTableModule,
  NzSelectModule,
  NzCascaderModule,
  NzDatePickerModule,
  NzDrawerModule,
  NzToolTipModule,
  NzButtonModule,
  NzTabsModule,
  NzResizableModule,
  NzModalModule,
  NzFormModule,
  NzIconModule,
  NzInputModule,
  NzSwitchModule,
  NzInputNumberModule,
  FlButtonModule,
  NzPopconfirmModule,
  NzDividerModule,
];

@NgModule({
  providers: [DatePipe],
  declarations: [OrderTrackingListComponent, OrderTrackingEditDialogComponent, DateEmptyPipe],
  imports: [
    CommonModule,
    OrderTrackingRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    ...nzModules,
    PipesModule,
    MaterialPackageModule,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new MultiTranslateHttpLoader(http, [
            { prefix: './assets/i18n/material-selector/', suffix: '.json' },
            { prefix: './assets/i18n/flss-component/', suffix: '.json' },
            { prefix: './assets/i18n/data-pkg/', suffix: '.json' },
          ]);
        },
        deps: [HttpClient],
      },
    }),
  ],
})
export class OrderTrackingModule {}
