import { Component, OnInit, Input, OnChanges, SimpleChanges, forwardRef, ChangeDetectorRef, AfterViewInit } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { FrequencyTypeEnum, WeekChinese } from './work-frequencies.model';

@Component({
  selector: 'app-work-frequencies',
  templateUrl: './work-frequencies.component.html',
  styleUrls: ['./work-frequencies.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => WorkFrequenciesComponent),
      multi: true,
    },
  ],
})
export class WorkFrequenciesComponent implements ControlValueAccessor, OnInit, OnChanges, AfterViewInit {
  @Input() type = 1; // 1：周，2：月
  @Input() disabled = false; // 是否可选
  checkList: { label: any; value: any; checked: boolean }[] = [];
  allChecked = false;
  indeterminate = false;
  FrequencyTypeEnum = FrequencyTypeEnum;

  public selectedValue!: any[];
  onChange: any = () => {};
  onTouch: any = () => {};

  writeValue(obj: any[]): void {
    this.selectedValue = obj;
    this.checkList.forEach((item) => {
      if (this.selectedValue.includes(item.value)) {
        item.checked = true;
      } else {
        item.checked = false;
      }
    });
    this.handleCheckChange();
    this._cdr.detectChanges();
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouch = fn;
  }

  setDisabledState?(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  constructor(private _cdr: ChangeDetectorRef) {}

  ngOnInit(): void {}

  ngAfterViewInit(): void {
    this.checkList.forEach((item) => {
      if (this.selectedValue.includes(item.value)) {
        item.checked = true;
      } else {
        item.checked = false;
      }
    });
    this.handleCheckChange();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes && changes?.type && changes?.type?.currentValue) {
      this.checkList = [];
      if (this.type === FrequencyTypeEnum.week) {
        this.checkList = Object.entries(WeekChinese)?.map((item) => ({ label: item[1], value: Number(item[0]), checked: false }));
        this.checkList.sort((a, b) => [1, 2, 3, 4, 5, 6, 0].indexOf(a.value) - [1, 2, 3, 4, 5, 6, 0].indexOf(b.value));
      } else {
        for (let i = 0; i < 31; i++) {
          this.checkList.push({
            label: i + 1,
            value: i + 1,
            checked: false,
          });
        }
      }
    }
  }

  updateAllChecked(): void {
    this.indeterminate = false;
    if (this.allChecked) {
      this.checkList = this.checkList.map((item) => ({
        ...item,
        checked: true,
      }));
    } else {
      this.checkList = this.checkList.map((item) => ({
        ...item,
        checked: false,
      }));
    }
    const selectedList = this.checkList.map((item) => {
      if (item?.checked) {
        return item.value;
      } else {
        return;
      }
    });
    this.onChange(selectedList);
  }

  onCheckChange(selectedList: any) {
    this.handleCheckChange();
    this.onChange(selectedList);
  }

  handleCheckChange() {
    this.allChecked = this.checkList.every((item) => item?.checked);
    this.indeterminate = this.checkList.some((item) => item?.checked) && this.checkList.some((item) => !item?.checked);
  }
}
