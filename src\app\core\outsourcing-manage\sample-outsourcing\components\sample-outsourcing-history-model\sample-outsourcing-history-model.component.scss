.his-box {
  display: flex;
  margin-right: -12px;
  .hismodel-left {
    margin-left: -12px;
    width: 220px;
  }
}
.his-right {
  width: calc(100% - 208px);
  padding-left: 8px;
  box-shadow: 0px 2px 4px 0px #ced6de;
  overflow: hidden;
}
.content-title {
  position: relative;
  width: 100%;
  position: sticky;
  top: 0;
  z-index: 99;
  padding: 0 16px 0 8px;
  display: flex;
  height: 48px;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  color: #545f7c;
  font-weight: 500;
  background: #fff;
  &::after {
    position: absolute;
    content: '';
    display: block;
    left: -8px;
    right: 0;
    bottom: 0;
    height: 1px;
    background: #d4d7dc;
  }
}
.right-btn {
  i {
    cursor: pointer;
    margin-left: 18px;
    font-size: 16px;
    color: #545f7c;
    font-weight: 500;
  }
}

/*
 * File: history-version.component.scss
 * Project: sewsmart-web
 * File Created: Thursday, 11th November 2021 3:59:06 pm
 * Author: liucp
 * Description: 历史版本组件
 * -----
 * Last Modified: Tuesday, 1st November 2022 6:39:33 pm
 * Modified By: liucp
 */

ul,
li {
  list-style: none;
  margin: 0;
  padding: 0;
}

.his-time {
  position: relative;
  padding-right: 8px;
  &::-webkit-scrollbar {
    display: none;
  }
}
.his-title {
  padding-left: 12px;
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 500;
  color: #222b3c;
  height: 48px;
}
.dot {
  position: absolute;
  left: 3px;
  top: 50%;
  transform: translateY(-50%);
  display: block;
  width: 5px;
  height: 5px;
  background: #4d96ff;
  border-radius: 6px;
}
.wd-bottom {
  display: flex;
  align-items: center;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  font-size: 14px;
  line-height: 36px;
  background: #fff;
  border-top: 1px solid #e3e6e9;
  justify-content: center;
}
.time:nth-of-type(odd) {
  background: #f9fafb;
}
.his-left {
  padding: 8px 0 38px 8px;
  width: 220px;
  position: relative;
  box-sizing: border-box;
  box-shadow: 0px 2px 4px 0px #ced6de;
  border-right: 1px solid #e3e6e9;
  .title {
    font-size: 16px;
    line-height: 48px;
    color: #222b3c;
  }
  .time {
    position: relative;
    padding-left: 12px;
    font-size: 14px;
    line-height: 40px;
    border-radius: 2px;
    display: flex;
    height: 40px;
    align-items: center;
    cursor: pointer;
    color: #222b3c;
    margin: 2px 0;
    &:hover {
      background: #deefff;
    }
  }

  .now-top {
    position: absolute;
    top: 0;
    right: 0;
    padding: 0 4px;
    display: inline-block;
    font-size: 12px;
    line-height: 20px;
    border-radius: 0px 2px 0px 8px;
    color: #fff;
    background: #4d96ff;
  }
  .actived {
    color: #138aff;
    background: #deefff;
  }

  .change-veision {
    display: none;
    position: absolute;
    right: 6px;
    top: 10px;
    padding: 0 4px;
    display: none;
    font-size: 12px;
    line-height: 20px;
    color: #fff;
    background: #00c790;
    border-radius: 10px;
  }
  .time:hover .change-veision {
    display: block;
  }
  .now {
    color: #138aff;
    &:hover {
      .change-veision {
        display: none;
      }
    }
  }
}
