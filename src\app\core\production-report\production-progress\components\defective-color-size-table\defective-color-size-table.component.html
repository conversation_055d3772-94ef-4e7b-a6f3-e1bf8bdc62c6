<div #tableOuterContainer class="table-outer-container">
  <nz-table
    #colorSizeTable
    *ngIf="isShowTable"
    [ngStyle]="{ width: tableDef.width + 'px' }"
    [ngClass]="tableDef.tableCls"
    [nzData]="tableDef.data"
    [nzBordered]="true"
    [nzShowPagination]="false"
    [nzScroll]="tableDef.scroll">
    <thead>
      <tr class="drop-boundary">
        <!-- read-only(-v2) & edit-cell thead -->
        <ng-container *ngIf="tableDef.mode === 'readOnly'">
          <th [nzWidth]="defaultDescCellWidth + 'px'" nzLeft>
            <div
              class="desc-cell"
              [attr.before-content]="translateName + '颜色' | translate"
              [attr.after-content]="translateName + '尺码' | translate"></div>
          </th>
          <th [nzWidth]="defaultColWidth + 'px'" nzAlign="center" *ngFor="let header of tableDef.header">
            <flc-text-truncated [data]="header.columnName"></flc-text-truncated>
          </th>
          <th *ngIf="isShowSum" [nzWidth]="defaultSumColWidth + 'px'" nzRight>{{ translateName + '总数量' | translate }}</th>
        </ng-container>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of tableDef.data; index as ri">
        <td nzLeft>
          <ng-container *ngIf="tableDef.mode !== 'editTable'">
            <div [ngClass]="{ 'no-cell-value': tableDef.mode === 'toggleCell' && !tableDef.rowSum[ri] }">
              <flc-text-truncated [data]="data.rowName"></flc-text-truncated>
            </div>
          </ng-container>
        </td>
        <td *ngFor="let header of tableDef.header; index as ci">
          <ng-container *ngIf="tableDef.mode === 'readOnly'">
            <div
              nz-tooltip
              [nzTooltipTitle]="tooltipTpl"
              [ngClass]="{
                'sum-hint': data.columns[ci]?.over,
                'frozen-cell': data.columns[ci]?.frozen,
                'null-cell': data.columns[ci]?.qty === null
              }">
              {{ data.columns[ci]?.qty ?? '-' }}
            </div>
            <ng-template #tooltipTpl>
              {{ data.columns[ci]?.inspectionItems ?? '-' }}
            </ng-template>
          </ng-container>
        </td>
        <td *ngIf="isShowSum" class="sum-col" nzRight>
          <flc-text-truncated [data]="tableDef.rowSum[ri] + ''"></flc-text-truncated>
        </td>
      </tr>
      <tr *ngIf="isShowSum" class="sum-row">
        <td nzLeft>{{ translateName + '合计' | translate }}</td>
        <td *ngFor="let header of tableDef.header; index as ci">
          <flc-text-truncated [data]="tableDef.colSum[ci] + ''"></flc-text-truncated>
        </td>
        <td nzRight>
          <flc-text-truncated [data]="tableDef.totalSum + ''"></flc-text-truncated>
        </td>
      </tr>
    </tbody>
  </nz-table>
</div>
