<table-data-view
  #tableDataView
  type="requirement_order"
  (handleTimeFilter)="handleTimeFilter($event)"
  (handlePanelSelect)="handlePanelSelect($event)"
  (handleFolder)="handleFolder()"></table-data-view>
<div #header>
  <flc-search-combination
    [btnTpl]="btnTpl"
    [isFold]="true"
    [checkedCount]="checkedInfo?.count || 0"
    [showAddBtn]="false"
    [checkData]="orderStatusList"
    [translateName]="translateName"
    [searchPlaceholder]="translateName + '搜索内容' | translate"
    [isMultiCheck]="false"
    [showSearchSelect]="true"
    (reset)="reset()"
    (refresh)="getList()"
    (handleFold)="resizePage()"
    (onInputSearchValue)="onInputSearchValue($event)"
    (onCheckChange)="onOrderStatusChanged($event)">
    <ng-container *ngFor="let item of searchList">
      <div *ngIf="item?.visible">
        <span class="search-name">{{ translateName + item.label | translate }}：</span>
        <flc-dynamic-search-select
          *ngIf="item.type === 'select'"
          [payLoad]="{ cache: true }"
          [dataUrl]="item?.dataUrl || '/service/order/v1/list_option'"
          [transData]="{ value: item?.transDataValue || 'value', label: 'label' }"
          [(ngModel)]="searchData[item.valueKey]"
          [column]="item.labelKey"
          (handleSearch)="onSearch()"
          [canSearch]="item.canSearch ?? true"
          [optAlwaysReload]="item.alwaysReload ?? false">
        </flc-dynamic-search-select>
        <ng-container *ngIf="item.type === 'cascader'">
          <nz-cascader
            [nzPlaceHolder]="'flss.placeholder.select' | translate"
            nzAllowClear
            [nzValueProperty]="'label'"
            [nzShowSearch]="true"
            [nzOptions]="styleList"
            [(ngModel)]="searchData[item.valueKey]"
            (nzVisibleChange)="openStyle($event)"
            (nzSelectionChange)="onSearch()">
          </nz-cascader>
        </ng-container>
        <nz-range-picker *ngIf="item.type === 'date'" [(ngModel)]="searchData[item.valueKey]" (ngModelChange)="getList()"></nz-range-picker>

        <ng-container *ngIf="item.type === 'local-select'">
          <nz-select
            [nzPlaceHolder]="'flss.placeholder.select' | translate"
            nzAllowClear
            [nzShowSearch]="true"
            [(ngModel)]="searchData[item.valueKey]"
            (ngModelChange)="onSearch()">
            <nz-option *ngFor="let op of item?.options" [nzLabel]="translateName + op?.label | translate" [nzValue]="op?.value">
            </nz-option>
          </nz-select>
        </ng-container>
      </div>
    </ng-container>

    <button nz-button flButton="pretty-minor" (click)="onOpenDrawerCustom()" nz-tooltip [nzTooltipTitle]="''">
      <i nz-icon [nzIconfont]="'icon-xitongpeizhi'"></i>
    </button>
  </flc-search-combination>

  <ng-template #btnTpl>
    <button
      *ngIf="hasPriceInquiryAuth"
      nz-button
      [nzShape]="'round'"
      flButton="pretty-default"
      [flcDisableOnClick]="1000"
      (click)="priceInquiry()">
      {{ 'bulkListTableHeader.去询价' | translate }}
    </button>

    <button nz-button [nzShape]="'round'" flButton="pretty-default" [flcDisableOnClick]="1000" (click)="handleUpdateImages()">
      {{ 'bulkListTableHeader.批量传图' | translate }}
    </button>

    <!-- 审核通过 -->
    <button
      nz-button
      flButton="primary"
      [nzShape]="'round'"
      nz-popover
      [nzPopoverContent]="operatePopoverContentTemplate"
      nzPopoverTrigger="hover"
      nzPopoverPlacement="bottom">
      {{ translateName + '操作' | translate }}
    </button>

    <button
      *ngIf="_service.btnArr.includes('order:bulk-print')"
      nz-button
      flButton="primary"
      [nzShape]="'round'"
      nz-popover
      [nzPopoverContent]="popoverContentTemplate"
      nzPopoverTrigger="hover"
      nzPopoverPlacement="bottom">
      {{ translateName + '打印唯一码' | translate }}
    </button>

    <button nz-button [nzShape]="'round'" flButton="pretty-default" [flcDisableOnClick]="1000" (click)="handleERPAsyn()">
      {{ 'bulkListTableHeader.ERP同步' | translate }}
    </button>

    <button
      *ngIf="_service.btnArr.includes('order:bulk-order-import')"
      nz-button
      flButton="primary"
      [nzShape]="'round'"
      nz-popover
      [nzPopoverContent]="importPopoverContentTemplate"
      nzPopoverTrigger="hover"
      nzPopoverPlacement="bottom">
      {{ 'common.导入' | translate }}
    </button>

    <button nz-button flButton="pretty-primary" [nzShape]="'round'" (click)="handlePrePayment()">
      {{ 'bulk.预收款' | translate }}
    </button>

    <!-- 新建 -->
    <button
      *ngIf="_service.btnArr.includes('order:bulk-create')"
      nz-button
      flButton="pretty-primary"
      [nzShape]="'round'"
      (click)="getDetail('new')">
      <i nz-icon [nzIconfont]="'icon-xinjian1'"></i>
      {{ 'common.新建' | translate }}
    </button>
    <!-- 待接单 -->
    <nz-divider nzType="vertical" style="background: #d4d7dc"></nz-divider>
    <button nz-button [nzShape]="'round'" flButton="pretty-minor" (click)="getAccept()">
      {{ (translateName + '待接单' | translate) + '·' + acceptNum }}
    </button>
  </ng-template>
  <ng-template #popoverContentTemplate>
    <div class="tag-list">
      <span (click)="onTagPrint(1)">{{ translateName + '标签全打' | translate }}</span>
      <span (click)="onTagPrint(2)">{{ translateName + '标签补打' | translate }}</span>
    </div>
  </ng-template>

  <ng-template #operatePopoverContentTemplate>
    <div class="tag-list">
      <span (click)="handleUpdateLeadtime()"> {{ 'bulkListTableHeader.批量更新生产周期' | translate }}</span>
      <span (click)="handleUpdateFOBPrice()"> {{ 'bulkListTableHeader.批量更新FOB价格' | translate }}</span>
      <span (click)="handleUpdateImages()"> {{ 'bulkListTableHeader.批量传图' | translate }}</span>
      <span (click)="handleButtonAction(orderOperateBtnEnum.pass)" *ngIf="_service.btnArr.includes('order:bulk-approval')">
        {{ 'flss.btn.pass' | translate }}</span
      >
      <span (click)="handleButtonAction(orderOperateBtnEnum.modify)" *ngIf="_service.btnArr.includes('order:bulk-approval')">
        {{ 'common.退回修改' | translate }}</span
      >
    </div>
  </ng-template>

  <ng-template #importPopoverContentTemplate>
    <div class="tag-list">
      <span
        (click)="onDownloadTemplate()"
        *ngIf="_service.btnArr.includes('order:bulk-order-import') && !factoryCodes.includes(userInfo?.factory_code)">
        {{ translateName + '下载导入模板' | translate }}</span
      >
      <span (click)="onImport()" *ngIf="_service.btnArr.includes('order:bulk-order-import')"> {{ 'common.导入' | translate }}</span>
    </div>
  </ng-template>
</div>
<div class="bulk-table-box">
  <flc-table
    #tableRef
    [tableHeader]="tableHeader"
    [template]="outTpl"
    [tableConfig]="tableConfig"
    (indexChanges)="indexChanges($event)"
    (getCount)="onSelectedCount($event)"
    (sizeChanges)="sizeChanges($event)"
    (getDetails)="getDetail($event)"
    (sortDataLists)="sortDataLists($event)"
    (getCount)="getCount($event)">
  </flc-table>
</div>
<ng-template let-data="data" #outTpl>
  <ng-container *ngIf="data.isTd">
    <ng-container *ngIf="data.key === 'po_due_times'">
      <flc-text-truncated [template]="poDueTime"></flc-text-truncated>
      <ng-template #poDueTime>
        <ng-container *ngIf="data.item?.po_due_times.length > 0; else noTime">
          <span *ngFor="let value of data.item?.po_due_times; index as i">
            {{ value | date: 'yyyy/MM/dd' }}<ng-container *ngIf="i < data.item?.po_due_times.length - 1">、</ng-container>
          </span>
        </ng-container>
        <ng-template #noTime>
          <span style="color: #b5b8bf">-</span>
        </ng-template>
      </ng-template>
    </ng-container>
    <ng-container *ngIf="data.key === 'po_customer_due_times'">
      <flc-text-truncated [template]="poDueTime"></flc-text-truncated>
      <ng-template #poDueTime>
        <ng-container *ngIf="data.item?.po_customer_due_times.length > 0; else noTime">
          <span *ngFor="let value of data.item?.po_customer_due_times; index as i">
            {{ value | date: 'yyyy/MM/dd' }}<ng-container *ngIf="i < data.item?.po_customer_due_times.length - 1">、</ng-container>
          </span>
        </ng-container>
        <ng-template #noTime>
          <span style="color: #b5b8bf">-</span>
        </ng-template>
      </ng-template>
    </ng-container>
    <ng-container *ngIf="data.key === 'bom_fill_status'">
      <flc-text-truncated
        [data]="
          !bom_fill_status[data.item?.bom_fill_status] ? null : (translateName + bom_fill_status[data.item?.bom_fill_status] | translate)
        "></flc-text-truncated>
    </ng-container>
    <ng-container *ngIf="data.key === 'order_labels'">
      <flc-text-truncated [data]="(data.item?.order_labels ?? []).join('、')"></flc-text-truncated>
    </ng-container>
    <ng-container *ngIf="data.key === 'bom_audit_status'">
      <flc-text-truncated
        [data]="
          !bom_audit_status[data.item?.bom_audit_status]
            ? null
            : (translateName + bom_audit_status[data.item?.bom_audit_status] | translate)
        "></flc-text-truncated>
    </ng-container>
    <ng-container *ngIf="data.key === 'inquiry_status'">
      <flc-text-truncated
        [data]="
          !inquiry_status[data.item?.inquiry_status] ? null : (translateName + inquiry_status[data.item?.inquiry_status] | translate)
        "></flc-text-truncated>
    </ng-container>
    <ng-container *ngIf="data.key === 'io_code'">
      <flc-text-truncated [data]="data.item.io_code"></flc-text-truncated>
      <div class="update-tip" *ngIf="data.item.bom_update_times">
        {{ translateName + 'BOM更新' | translate }}+{{ data.item.bom_update_times }}
      </div>
      <div class="urgent-tip" *ngIf="data.item.urgent_status === 20">急</div>
    </ng-container>

    <ng-container *ngIf="data.key === 'source'">
      <span>{{ translateName + sourceTypeEnum[data.item.source] | translate }}</span>
    </ng-container>

    <ng-container *ngIf="data.key === 'is_pre_order'">
      {{ translateName + (data.item?.is_pre_order === 1 ? '是' : '否') | translate }}
    </ng-container>

    <ng-container *ngIf="data.key === 'is_use_plan'">
      {{ translateName + (data.item?.is_use_plan === 1 ? '是' : '否') | translate }}
    </ng-container>
  </ng-container>
  <ng-container *ngIf="data.isAction">
    <button
      nz-button
      style="margin-left: -15px; color: #54607c"
      nzType="link"
      flButton="link"
      *ngIf="_service.btnArr.includes('order:bulk-create')"
      nz-tooltip
      (click)="copy(data?.item?.id)"
      [nzTooltipTitle]="topTitle">
      <i nz-icon [nzIconfont]="'icon-caozuolan_fuzhi'"></i>
    </button>
  </ng-container>
</ng-template>

<print-label-component
  [isVisible]="isVisible"
  [printTitle]="translateName + printTitle | translate"
  [printType]="printType"
  (visibleCahnge)="isVisible = false"
  [bulk_order_card]="bulk_order_card"
  [selectedIds]="selectedIds"></print-label-component>

<flc-file-gallery
  style="display: none"
  #fileGalleryRef
  [isEditMode]="true"
  galleryType="file"
  addBtnType="link"
  addBtnText=""
  [fileList]="fileList"
  [fileAcceptType]="['xls', 'xlsx', 'xlsm']"
  [fileMaxSize]="5"
  [isSignatureMode]="true"
  [showSuccessTip]="false"
  (onUploaded)="onUploaded($event)">
</flc-file-gallery>
