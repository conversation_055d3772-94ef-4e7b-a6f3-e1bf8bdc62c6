import { FlcColorSizeTableCell } from 'fl-common-lib';
import { ProgressDataType } from './production-progress.enum';
import { DimensionRange, ReportRange } from '../../production-report.enum';

// 列表请求参数
export interface IListParams {
  where: Array<{
    column: string;
    op: string;
    value: string;
  }>;
  order_by: Array<string>;
  page: number;
  limit: number;
  dimension: number; // 订单维度
  daily: number; // 日维度
}

// 颜色尺码明细请求参数
export interface IDetailParams {
  io_code: string; // 大货单号
  po_code: string; // PO交付单
  factory_code: string; // 加工厂编码
  data_type: ProgressDataType;
  biz_date?: string;
  factory_name?: string; // 加工厂
  dimension: number; // 维度
}

// 颜色尺码明细数据
export interface IDetailData {
  io_code: string; // 大货单
  po_code: string; // 交付单
  factory_name: string; // 加工厂
  biz_date: string; // 日期
  type_name: string; // 类型
  po_lines: Array<FlcColorSizeTableCell>; // 颜色尺码明细
  production_line_info?: {
    line_name: string;
    po_lines: Array<FlcColorSizeTableCell>;
  }[];
}

export interface ProductionHeaderType {
  key: string;
  subKey?: string;
  label: string;
  width: string;
  type: string;
  pinned?: boolean;
  disabled?: boolean;
  tab?: ReportRange;
  isPoIndex?: boolean;
  subIndex?: boolean;
  dimension?: DimensionRange;
  hasTotal?: boolean; // 是否有合计
}
