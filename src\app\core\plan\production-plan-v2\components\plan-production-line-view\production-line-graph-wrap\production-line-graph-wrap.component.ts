import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ChangeDetectorRef, ElementRef } from '@angular/core';
import { addMinutes, endOfDay, format, startOfDay } from 'date-fns';
import {
  DayRange,
  GanttCellWidth,
  PlanGraphEvent,
  PlanGraphItem,
  PlanGraphOperationTypeEnum,
  PlanMovedParam,
  PlanRightClickParam,
  ProductionLinePlanRenderItem,
  ProductionPlanLineItem,
  WeekRange,
} from '../../../interface';
import { ProductionPlanShareService } from '../../../production-plan-share.service';
import { ProductionPlanSubjectEventEnum } from '../../../model/production-plan.enum';

const formatDate = (date: Date) => Number(format(date, 'T'));

@Component({
  selector: 'app-production-line-graph-wrap',
  templateUrl: './production-line-graph-wrap.component.html',
  styleUrls: ['./production-line-graph-wrap.component.scss'],
})
export class ProductionLineGraphWrapComponent implements OnInit, OnChanges {
  @Input() isEdit = false;
  @Input() graphOptions: { view: 'order' | 'productionLine'; item_dimension: 'io' | 'po' } = {
    view: 'order',
    item_dimension: 'io',
  };
  @Input() data!: ProductionLinePlanRenderItem;
  @Input() options!: {
    signalWidth: GanttCellWidth;
    dayWidth: GanttCellWidth; // 日维度一天48，小时维度一天24*12
    wrapBodyWidth: number;
    // dimension: 'day' | 'hour';
    dimension: string;
    per1Px: number;
  };
  @Input() days: DayRange[] = [];
  @Input() weeks: WeekRange[] = [];
  @Output() tapGraphItem = new EventEmitter<PlanGraphItem & PlanGraphEvent>();
  @Output() rightClickGraph = new EventEmitter<PlanRightClickParam>();
  @Output() hoverGraphItem = new EventEmitter<PlanGraphItem>();
  @Output() leaveGraphItem = new EventEmitter();
  hours = [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24];
  rawHeight = 52;
  clickItemEvent?: PlanRightClickParam | PlanGraphItem;
  get showTag() {
    return this._shareService.showTag;
  }

  constructor(public _shareService: ProductionPlanShareService, private _cdr: ChangeDetectorRef) {}
  ngOnChanges(changes: SimpleChanges): void {
    if (changes.data && !changes.data.firstChange && changes.data.currentValue) {
      this.transformData();
    }
    if (changes.options && !changes.options.firstChange && changes.options.currentValue) {
      this.transformData();
    }
  }

  ngOnInit(): void {
    this.transformData();
  }

  isRestDay(day: DayRange): boolean {
    if ((this.data?.rest_days as number[])?.indexOf(Number(day.start.split(' ')[0])) >= 0) {
      return true;
    } else {
      return false;
    }
  }

  transformData() {
    this.data.OrderRenderList.forEach((item) => {
      this._shareService.calcGraphPosition(
        item,
        this.options.signalWidth,
        startOfDay(this._shareService.searchData.start_date as Date),
        this.options.dimension === 'hour'
      );
    });
  }

  getSelectTime(event: DragEvent | MouseEvent, left: number): Date {
    const timeX = event.offsetX + left;
    const per1Px = (24 * 60) / this.options.dayWidth;
    const offsetTime = per1Px * timeX;
    const selectTime = addMinutes(startOfDay(this._shareService.searchData.start_date as Date), offsetTime);
    return selectTime;
  }

  rightClick(event: MouseEvent) {
    event.stopPropagation();
    event.preventDefault();
    this.rightClickGraph.emit({
      postion: 'blank',
      x: event.x,
      y: event.y,
      productionLineNo: (this.data.rawData as ProductionPlanLineItem).production_line_no,
    });
  }

  graphItemClick(item: PlanGraphItem & PlanGraphEvent) {
    this.clickItemEvent = item;
    this.tapGraphItem.emit(item);
  }

  /** 鼠标右击 */
  graphItemRightClick(item: PlanRightClickParam) {
    this.clickItemEvent = item;
    this.rightClickGraph.emit(item);
  }

  /** 获取点击处的时间 */
  getClickTime() {
    const left = this.getLeft(this.clickItemEvent?.event?.target as HTMLElement);
    const pointTime = startOfDay(this.getSelectTime(this.clickItemEvent?.event as MouseEvent, left));
    return pointTime;
  }

  getClickItemWidth() {
    return this.clickItemEvent?.width;
  }

  /** 鼠标hover进度条 */
  graphItemHover(item: PlanGraphItem) {
    this.hoverGraphItem.emit(item);
  }

  /** 鼠标移出进度条 */
  graphItemLeave() {
    this.leaveGraphItem.emit();
  }

  getLeft(element: HTMLElement): number {
    const inputElement = element;
    const correctTarget = this.returnTargetElement(element);
    const leftString = window.getComputedStyle(correctTarget).left;
    const targetLeft = Number(leftString.substring(0, leftString.length - 2));
    if (correctTarget === inputElement) {
      return targetLeft;
    } else {
      const inputDomLeft = inputElement.getBoundingClientRect().left;
      const targetDomLeft = correctTarget.getBoundingClientRect().left;
      const deviation = inputDomLeft - targetDomLeft;
      return targetLeft + deviation;
    }
  }

  returnTargetElement(target: HTMLElement): any {
    if (target == null) return null;
    if (target.classList.contains('graph-item')) {
      return target;
    } else {
      return this.returnTargetElement(target.parentElement!);
    }
  }

  /**
   * 拖动到某个网格上
   * @param event
   * @param index
   * @param nextIndex
   * @param dimension
   */
  dropData(event: DragEvent, index: number, nextIndex: number, dimension: string) {
    const data = JSON.parse((event as DragEvent).dataTransfer!.getData('text')) as PlanGraphItem;
    let indexResult = 0;
    if (dimension === 'day') {
      const weekList = this.weeks.slice(0, index);
      indexResult =
        weekList?.reduce((cur, item) => {
          return cur + item.children.length;
        }, 0) + nextIndex;
    } else if (dimension === 'hour') {
      indexResult = index * 12 + nextIndex;
    }
    const left = this.options.signalWidth * indexResult;
    const selectTime = this.getSelectTime(event as DragEvent, left);
    this._shareService.sendSubjectEvent(ProductionPlanSubjectEventEnum.move, {
      target_time: formatDate(startOfDay(selectTime)),
      factory_code: this.data.rawData.factory_code,
      production_line_no: this.data.rawData.production_line_no,
      group_id: data.group_id,
      operation_type: PlanGraphOperationTypeEnum.movePosition,
      pre_material_completed_time: data.pre_material_completed_time,
      is_should_comfirm: data.is_outsourced && data.factory_code !== this.data.rawData.factory_code,
    });
  }

  /**
   * 拖动到某个进度条上
   * @param event
   */
  graphItemDrop(event: DragEvent) {
    const data = JSON.parse(event.dataTransfer!.getData('text')) as PlanGraphItem;
    const left = this.getLeft(event.target as HTMLElement);
    const selectTime = this.getSelectTime(event, left);
    this._shareService.sendSubjectEvent(ProductionPlanSubjectEventEnum.move, {
      target_time: formatDate(startOfDay(selectTime)),
      factory_code: data.factory_code,
      production_line_no: data.production_line_no,
      group_id: data.group_id,
      operation_type: PlanGraphOperationTypeEnum.movePosition,
      pre_material_completed_time: data.pre_material_completed_time,
      is_should_comfirm: data.is_outsourced && data.factory_code !== this.data.rawData.factory_code,
    });
  }

  graphItemMove(item: PlanMovedParam) {
    this._shareService.sendSubjectEvent(ProductionPlanSubjectEventEnum.move, {
      ...item,
      target_time: formatDate(endOfDay(item.target_time)),
    });
  }
}
