import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { FactoryManageListComponent } from './factory-manage/factory-manage-list/factory-manage-list.component';
import { FactoryManageDetailComponent } from './factory-manage/factory-manage-detail/factory-manage-detail.component';
import { FlcLeaveGuard } from 'fl-common-lib';
import { RecommendSetListComponent } from './recommend-set/recommend-set-list/recommend-set-list.component';

const routes: Routes = [
  {
    path: 'factoryManage',
    component: FactoryManageListComponent,
    data: {
      keepAlive: true,
    },
  },
  {
    path: 'factoryManage/:id',
    component: FactoryManageDetailComponent,
    canDeactivate: [FlcLeaveGuard],
  },
  {
    path: 'factoryRecommendSet',
    component: RecommendSetListComponent,
    data: {
      keepAlive: true,
    },
  },
  { path: '', redirectTo: '/factory/factoryManage' },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class FactoryRoutingModule {}
