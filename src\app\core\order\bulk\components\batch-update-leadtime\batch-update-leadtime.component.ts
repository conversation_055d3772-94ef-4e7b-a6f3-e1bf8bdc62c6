import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormControl, Validators } from '@angular/forms';

import { FlcValidatorService } from 'fl-common-lib';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';

import { BulkService } from '../../bulk.service';
import { EventTypeEnum } from '../../models/bulk.enum';
import { AppStorageService } from 'src/app/shared/app-storage.service';

@Component({
  selector: 'app-batch-update-leadtime',
  templateUrl: './batch-update-leadtime.component.html',
  styleUrls: ['./batch-update-leadtime.component.scss'],
})
export class BatchUpdateLeadtimeComponent implements OnInit {
  @Input() order_ids: number[] = [];
  validateForm = this._fb.group({});
  formConfig: any = [
    {
      label: '生产周期',
      key: 'lead_time',
      type: 'remote-select',
      dataUrl: '/service/scm/dict_category/dict_option',
      column: 'leadtime',
      transDataValue: 'label',
      required: true,
    },
  ];
  translateName = 'bulk.';
  can_edit_leadtime = true;

  constructor(
    private _service: BulkService,
    private _fb: FormBuilder,
    private _validator: FlcValidatorService,
    private _modal: NzModalService,
    private _message: NzMessageService,
    public _storage: AppStorageService
  ) {
    this._service.fieldArr = this._storage.getFieldActions('order/bulk');
    this.can_edit_leadtime = this._service.fieldArr?.find((field: any) => field?.fieldName === '生产周期' && field?.editPermission === 2);
  }

  ngOnInit(): void {
    this.initForm();
  }

  initForm() {
    this.formConfig?.forEach((item: any) => {
      const _control = new FormControl();
      item.required && _control.addValidators(Validators.required);
      this.validateForm?.addControl(item.key, _control);
    });
  }

  async onSave() {
    const isInvalid = await this._validator.formIsAsyncInvalid(this.validateForm);
    if (isInvalid) {
      return;
    }

    const rawValue = this.validateForm?.getRawValue();

    this._service
      .batchUpdateLeadtime({
        lead_time: rawValue?.lead_time ?? null,
        order_ids: this.order_ids,
      })
      .subscribe((res: any) => {
        if (res?.code === 200) {
          this._message.success(this._service.translateValue('success.update'));
          this._service.eventEmitter.next(EventTypeEnum.refreshList);
          this._modal.closeAll();
        }
      });
  }

  onCancel() {
    this._modal.closeAll();
  }
}
