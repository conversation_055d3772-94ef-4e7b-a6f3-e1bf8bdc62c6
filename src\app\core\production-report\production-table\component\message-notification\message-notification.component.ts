import { Component, EventEmitter, OnInit, Output } from '@angular/core';

import { finalize } from 'rxjs';

import { ProductionTableService } from '../../production-table.service';
import { IMessageListItem } from '../../production-table.interface';

@Component({
  selector: 'app-message-notification',
  templateUrl: './message-notification.component.html',
  styleUrls: ['./message-notification.component.scss'],
})
export class MessageNotificationComponent implements OnInit {
  visible = false;
  translateName = 'ProductionTableTemplateFiled.';
  @Output() handleDeal = new EventEmitter();

  warningLevel = {
    1: { color: '#f74949' },
    2: { color: '#fb6401' },
    3: { color: '#138aff' },
  };

  constructor(private _service: ProductionTableService) {}

  ngOnInit(): void {
    this.getMessageCount();
    this.getMessageList();
  }

  // 获取
  messageCount = 0;
  getMessageCount() {
    this._service.getMessageCount({}).subscribe((res: any) => {
      if (res?.code === 200) {
        this.messageCount = res?.data?.count || 0;
      }
    });
  }

  visibleChange(visible: boolean) {
    if (visible) {
      this.getMessageList();
    }
  }

  messageList: IMessageListItem[] = [];
  pageLoading = false;
  getMessageList() {
    this.pageLoading = true;
    this._service
      .getMessageList({})
      .pipe(
        finalize(() => {
          this.pageLoading = false;
        })
      )
      .subscribe((res: any) => {
        if (res?.code === 200) {
          this.messageList = res?.data?.data_list || [];
        }
      });
  }

  // 消息通知
  dealMessage(item: IMessageListItem) {
    // if (item?.message_type === 1) {
    //   this.handleDeal.emit(item);
    //   return;
    // }
    this._service.setMessageRead({ warning_message_id: item.warning_message_id }).subscribe((res: any) => {
      if (res?.code === 200) {
        this.messageCount = res?.data?.count;
        this.getMessageCount();
        this.getMessageList();
      }
    });
  }
}
