import { Component, Input, OnInit } from '@angular/core';
import { NzModalRef } from 'ng-zorro-antd/modal';
import { AbstractControl, FormArray, FormBuilder, FormControl, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import {
  AllocationDetail,
  AllocationDetailFactoryItem,
  AllocationDetailLine,
  AllocationInitPlanPaylod,
  AllocationListItem,
  AllocationGetPlanEndTimePayload,
} from '../../model/production-plan.interface';
import { FactoryAllocationTypeEnum, OrderAllocationTypeEnum } from '../../model/production-plan.enum';
import { getTableHeadersMap } from './order-allocation-modal.config';
import { ProductionPlanV2Service } from '../../production-plan-v2.service';
import { FlcUtilService, FlcValidatorService } from 'fl-common-lib';
import { debounceTime, Subject, Subscription } from 'rxjs';
import { format, startOfDay } from 'date-fns';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-order-allocation-modal',
  templateUrl: './order-allocation-modal.component.html',
  styleUrls: ['./order-allocation-modal.component.scss'],
})
export class OrderAllocationModalComponent implements OnInit {
  @Input() data!: AllocationListItem;
  @Input() factory_items: { factory_code: string; production_line_no: string }[] = [];

  orderAllocationTypeEnum = OrderAllocationTypeEnum;
  orderAllocationType: OrderAllocationTypeEnum = OrderAllocationTypeEnum.order;
  tableHeadersMap = getTableHeadersMap(this.data?.selected);
  tableHeaders = this.tableHeadersMap[this.orderAllocationType];

  modalForm = this._fb.group({
    list: this._fb.array([]),
  });
  detailData?: AllocationDetail;

  opitonsMap: { [key: string]: any } = {};

  private requestSubscription?: Subscription;
  private subject$ = new Subject<FormGroup>();

  private qtySubscription?: Subscription;
  private qtySubject$ = new Subject<{ value: number | string; formGroup: FormGroup }>();

  get dataList() {
    return this.modalForm.get('list') as FormArray;
  }

  constructor(
    private modelRef: NzModalRef,
    private _fb: FormBuilder,
    private _msgService: NzMessageService,
    private _service: ProductionPlanV2Service,
    private _flcUntilService: FlcUtilService,
    private _flcValidatorService: FlcValidatorService
  ) {}

  ngOnInit() {
    this.tableHeadersMap = getTableHeadersMap(this.data?.selected);
    this.tableHeaders = this.tableHeadersMap[this.orderAllocationType];
    this.getAllocationDetail();

    this.requestSubscription = this.subject$.pipe(debounceTime(500)).subscribe((res) => {
      this.gePlanEndTime(res);
      this.validatorFactoryAndLines();
    });
    this.qtySubscription = this.qtySubject$.pipe(debounceTime(300)).subscribe((res) => {
      this.checkOrderQty(res.value, res.formGroup);
    });
  }

  ngOnDestroy(): void {
    this.requestSubscription?.unsubscribe();
    this.qtySubscription?.unsubscribe();
  }

  onChangeSelect(e: number | string, header: any, control: AbstractControl) {
    if (header.key === 'factory_code') {
      this.onResetFormGroupByFactory(control as FormGroup);
      if (this.dataList.controls.length === 1 && this.orderAllocationType === OrderAllocationTypeEnum.order) {
        this.autoAllocate();
        return;
      }
      if (e) {
        const _option = this.opitonsMap.factory_code?.find((item: any) => item.code === e);
        this.setLinesDisabled(_option, control as FormGroup);
      }
    } else if (header.key === 'production_line_no') {
      const _option = this.opitonsMap.production_line_no?.find((item: any) => item.production_line_no === e);
      control.get('production_line_name')?.setValue(_option?.production_line_name || null);
      control.get('work_days')?.setValue(null, { emitViewToModelChange: false });
      control.get('break_days')?.setValue(null);
      control.get('plan_start_time')?.setValue(null, { emitViewToModelChange: false });
      control.get('plan_end_time')?.setValue(null, { emitViewToModelChange: false });
    } else if (header.key === 'color_code') {
      this.onResetFormGroup(control as FormGroup);
      const _option = this.opitonsMap.color_code?.find((item: any) => item.color_code === e);
      const _sum_qty = this.dataList
        .getRawValue()
        .filter((item) => item.color_code === e)
        .reduce((pre: number, cur: AllocationDetailLine) => pre + Number(cur.qty || 0), 0);
      const _qty = Number(this._flcUntilService.accSubPlus(_option?.to_be_allocated || 0, _sum_qty));
      control.get('qty')?.setValue(_qty <= 0 ? null : _qty);
    } else if (header.key === 'po_unique_code') {
      this.onResetFormGroup(control as FormGroup);
      const _option = this.opitonsMap.po_unique_code?.find((item: any) => item.po_unique_code === e);
      const _sum_qty = this.dataList
        .getRawValue()
        .filter((item) => item.po_unique_code === e)
        .reduce((pre: number, cur: AllocationDetailLine) => pre + Number(cur.qty || 0), 0);
      const _qty = Number(this._flcUntilService.accSubPlus(_option?.to_be_allocated || 0, _sum_qty));
      control.get('qty')?.setValue(_qty <= 0 ? null : _qty);
    }
    this.checkTimeOfPreMaterialCompletedTime(control as FormGroup);

    this.subject$.next(control as FormGroup);
  }

  onChangeValues(e: number | string, header: any, control: AbstractControl) {
    if (header.key === 'qty') {
      this.qtySubject$.next({ value: e, formGroup: control as FormGroup });
      return;
    } else if (header.key === 'plan_start_time') {
      this.checkTimeOfPreMaterialCompletedTime(control as FormGroup);
    }
    this.subject$.next(control as FormGroup);
  }

  // 检测数量是否已经超过最大分配数，超过则重置回最大分配数
  private checkOrderQty(e: number | string, control: AbstractControl) {
    if (e) {
      const _value = this.dataList.value as AllocationGetPlanEndTimePayload[];
      if (this.orderAllocationType === OrderAllocationTypeEnum.order) {
        // 订单分配检测件数不可以大于订单数
        this.resetQtyValue(e, control, _value, this.detailData?.to_be_allocated || 0);
      } else if (this.orderAllocationType === OrderAllocationTypeEnum.color) {
        const _color_code = control.get('color_code')?.value;
        if (!_color_code) return;
        // 颜色分配件数不可以大于颜色订单数
        const _option = this.opitonsMap.color_code?.find((item: any) => item.color_code === _color_code);
        const _list = _value.filter((item) => item.color_code === _color_code);
        this.resetQtyValue(e, control, _list, _option?.to_be_allocated || 0);
      } else if (this.orderAllocationType === OrderAllocationTypeEnum.po) {
        const _po_unique_code = control.get('po_unique_code')?.value;
        if (!_po_unique_code) return;
        // po分配件数不可以大于颜色订单数
        const _list = _value.filter((item) => item.po_unique_code === _po_unique_code);
        const _option = this.opitonsMap.po_unique_code?.find((item: any) => item.po_unique_code === _po_unique_code);
        this.resetQtyValue(e, control, _list, _option.to_be_allocated || 0);
      }
    }
    this.calculateSurplusQty();
    this.subject$.next(control as FormGroup);
  }

  private resetQtyValue(
    value: number | string,
    control: AbstractControl,
    list: AllocationGetPlanEndTimePayload[],
    to_be_allocated: number
  ) {
    const _sum_qty = list.reduce((pre: number, cur) => pre + Number(cur.qty || 0), 0);
    if (_sum_qty > to_be_allocated) {
      this._msgService.error('分配件数超出可分配数，自动改回最大可分配数');
      const _diffQty = this._flcUntilService.accSubPlus(_sum_qty, to_be_allocated || 0);
      Number(this._flcUntilService.accSubPlus(value, _diffQty));
      control.get('qty')?.setValue(Number(this._flcUntilService.accSubPlus(value, _diffQty)), { emitViewToModelChange: false });
    }
  }

  onOpenChange(e: boolean, header: any, control: AbstractControl) {
    if (!e || header.key !== 'production_line_no') return;
    const _factory_code = control.get('factory_code')?.value;
    if (_factory_code) {
      const _option = this.opitonsMap.factory_code?.find((item: any) => item.code === _factory_code);
      this.opitonsMap.production_line_no = _option?.production_lines || [];
    }
  }

  // 校验开始时间是否大于物料齐套时间。是则重新社会物料齐套时间
  private checkTimeOfPreMaterialCompletedTime(group: FormGroup) {
    const _plan_start_time = group.get('plan_start_time')?.value;
    if (!_plan_start_time || !this.detailData?.pre_material_completed_time) return;
    const _pre_material_completed_time = Number(format(startOfDay(this.detailData.pre_material_completed_time), 'T'));
    const _plan_start_time_date = format(startOfDay(_plan_start_time), 'T');
    if (Number(_plan_start_time_date) < Number(_pre_material_completed_time)) {
      this._msgService.error('开始日期不能小于物料齐套日期');
      group.get('plan_start_time')?.setValue(_pre_material_completed_time);
    }
  }

  // 自动调整为最近空闲日，且最近空闲日不得小于物料齐套日期
  private resetPlanStartTime(group: FormGroup) {
    this._msgService.error('当前生产计划日期范围内，已有其他订单排期，已自动调整开始日期到最近的空闲日或物料齐套时间');
    const _value = group.getRawValue();
    const _factory_code = _value.factory_code;
    const _line_no = _value.production_line_no;

    const _factory_option = this.opitonsMap.factory_code?.find((item: any) => item.code === _factory_code);

    let _min_time = 0;

    if (_factory_code && _factory_option?.order_distribute_type === FactoryAllocationTypeEnum.factory) {
      _min_time = _factory_option?.recent_idle_date ?? 0;
    }

    if (_line_no && _factory_option?.order_distribute_type === FactoryAllocationTypeEnum.line) {
      // 产线最近的空闲日期
      const _line_option = _factory_option?.production_lines?.find((item: any) => item.production_line_no === _line_no);
      _min_time = _line_option?.recent_idle_date ?? 0;
    }

    if (this.detailData?.pre_material_completed_time) {
      const _pre_material_completed_time = this.detailData?.pre_material_completed_time
        ? Number(format(startOfDay(this.detailData.pre_material_completed_time), 'T'))
        : 0;
      _min_time = _pre_material_completed_time > _min_time ? _pre_material_completed_time : _min_time;
    }

    _min_time && group.get('plan_start_time')?.setValue(_min_time);
  }

  // 切换工厂 清除产线、生产天数、休息日、计划生产截止
  onResetFormGroupByFactory(group: FormGroup) {
    group.get('production_line_no')?.setValue(null, { emitViewToModelChange: false });
    group.get('production_line_no')?.enable();
    group.get('production_line_name')?.setValue(null, { emitViewToModelChange: false });
    group.get('work_days')?.setValue(null, { emitViewToModelChange: false });
    group.get('break_days')?.setValue(null);
    group.get('plan_start_time')?.setValue(null, { emitViewToModelChange: false });
    group.get('plan_end_time')?.setValue(null, { emitViewToModelChange: false });
  }

  onResetFormGroup(group: FormGroup) {
    group.get('qty')?.setValue(null, { emitViewToModelChange: false });
    // group.get('work_days')?.setValue(null, { emitViewToModelChange: false });
    // group.get('break_days')?.setValue(null);
    // group.get('plan_start_time')?.setValue(null, { emitViewToModelChange: false });
    // group.get('plan_end_time')?.setValue(null, { emitViewToModelChange: false });
  }

  // 加工厂是否有产线
  private setLinesDisabled(factory_item: AllocationDetailFactoryItem, formGroup: FormGroup) {
    if (factory_item.order_distribute_type === FactoryAllocationTypeEnum.factory) {
      formGroup.get('production_line_no')?.disable();
    } else {
      formGroup.get('production_line_no')?.enable();
    }
  }

  // 添加行
  onAddRow(index: number, data?: Partial<AllocationDetailLine>) {
    const _group: FormGroup = this._fb.group({});
    this.tableHeaders.forEach((item) => {
      const _contorl = new FormControl(data?.[item.key as keyof AllocationDetailLine] ?? null);
      item.required && _contorl.addValidators(Validators.required);
      _group.addControl(item.key, _contorl);
      item.labelKey && _group.addControl(item.labelKey, new FormControl(data?.[item.labelKey as keyof AllocationDetailLine] ?? null));
    });

    _group.get('qty')?.addValidators(this.validatoryQty());

    _group.get('factory_code')?.addValidators(this.validatorFactory(this.dataList));
    _group.get('production_line_no')?.addValidators(this.validatoryProductionLine(this.dataList));

    if (!data) {
      _group.get('sam')?.setValue(this.data.sam);
      _group.get('average_daily_production')?.setValue(this.data.average_daily_production);
    }

    this.dataList.insert(index + 1, _group);
    if (data?.factory_code) {
      const _option = this.opitonsMap.factory_code?.find((item: any) => item.code === data?.factory_code);
      _option && this.setLinesDisabled(_option, _group);
    }
    this.calculateSurplusQty();
  }

  onRemoveRow(index: number) {
    this.dataList.removeAt(index);
    this.calculateSurplusQty();
  }

  private calculateSurplusQty(): void {
    const _value = this.dataList.getRawValue();
    if (this.orderAllocationType === OrderAllocationTypeEnum.order) {
      const _sum_qty = _value.reduce((pre: number, cur: AllocationDetailLine) => pre + Number(cur.qty || 0), 0);
      const surplus_qty = this._flcUntilService.accSubPlus(this.detailData?.to_be_allocated || 0, _sum_qty);
      this.dataList.controls.forEach((_group) => _group.get('surplus_qty')?.setValue(surplus_qty));
    } else if (this.orderAllocationType === OrderAllocationTypeEnum.color) {
      const color_qty_map = new Map<string, { qty: number; to_be_allocated: number }>();
      this.detailData?.color_list.forEach((item) => {
        const _sum_qty = _value.reduce((pre: number, cur: any) => {
          if (cur.color_code === item.color_code) {
            return pre + Number(cur.qty || 0);
          } else {
            return pre;
          }
        }, 0);
        color_qty_map.set(item.color_code, {
          qty: _sum_qty,
          to_be_allocated: item.to_be_allocated,
        });
      });
      this.dataList.controls.forEach((_group) => {
        const _color_code = _group.get('color_code')?.value;
        if (!_color_code) return;
        _group
          .get('surplus_qty')
          ?.setValue(
            this._flcUntilService.accSub(color_qty_map.get(_color_code)?.to_be_allocated || 0, color_qty_map.get(_color_code)?.qty || 0)
          );
      });
    } else if (this.orderAllocationType === OrderAllocationTypeEnum.po) {
      const po_qty_map = new Map<string, { qty: number; to_be_allocated: number }>();
      this.detailData?.po_list.forEach((item) => {
        const _sum_qty = _value.reduce((pre: number, cur: any) => {
          if (cur.po_unique_code === item.po_unique_code) {
            return pre + Number(cur.qty || 0);
          } else {
            return pre;
          }
        }, 0);
        po_qty_map.set(item.po_unique_code, {
          qty: _sum_qty,
          to_be_allocated: item.to_be_allocated,
        });
      });
      this.dataList.controls.forEach((_group) => {
        const po_unique_code = _group.get('po_unique_code')?.value;
        if (!po_unique_code) return;
        _group
          .get('surplus_qty')
          ?.setValue(
            this._flcUntilService.accSub(po_qty_map.get(po_unique_code)?.to_be_allocated || 0, po_qty_map.get(po_unique_code)?.qty || 0)
          );
      });
    }
  }

  /**
   * 切换分配维度
   */
  onCheckTab() {
    this.tableHeaders = this.tableHeadersMap[this.orderAllocationType];
    this.dataList.clear();
    this.onAddRow(0, {
      sam: this.data.sam,
      average_daily_production: this.data.average_daily_production,
      qty: this.orderAllocationType === OrderAllocationTypeEnum.order ? this.detailData?.to_be_allocated : undefined,
    });
    if (this.orderAllocationType === OrderAllocationTypeEnum.order && this.detailData?.factory_list.length === 1) {
      this.getAllocationInitPlan({
        factory_code: this.detailData.factory_list[0].code,
        allocate_method: this.orderAllocationType,
        order_uuid: this.data.order_uuid,
        qty: this.detailData.to_be_allocated,
        production_nos: this.detailData.factory_list[0].production_lines?.map((_item) => _item.production_line_no) || [],
      });
    }
  }

  private gePlanEndTime(_formGroup: FormGroup) {
    const _value = _formGroup.getRawValue();
    if (!_formGroup.valid) return;
    delete _value.plan_end_time;
    _value.plan_start_time = format(startOfDay(_value.plan_start_time), 'T');
    const payload: AllocationGetPlanEndTimePayload = {
      ..._value,
      plan_start_time: _value.plan_start_time ? Number(_value.plan_start_time) : null,
      allocate_method: this.orderAllocationType,
      order_uuid: this.data.order_uuid,
      selected: this.data.selected,
    };
    this._service.gePlanEndTime(payload).subscribe((res) => {
      if (res.code !== 200) return;

      if (res.data.is_occupied) {
        this.resetPlanStartTime(_formGroup);
        return;
      }
      if (res.data.plan_start_time) {
        _formGroup.get('plan_start_time')?.setValue(res.data.plan_start_time, { emitViewToModelChange: false });
      }
      _formGroup.get('plan_end_time')?.setValue(res.data.plan_end_time);
      _formGroup.get('work_days')?.setValue(res.data.work_days);
      _formGroup.get('break_days')?.setValue(res.data.break_days);
    });
  }

  /**
   * 自动分配, 当前只有一条数据，且切换工厂时触发
   */
  private autoAllocate() {
    if (this.dataList.controls.length !== 1) return;
    const _value = this.dataList.getRawValue();
    if (!_value[0].factory_code) return;
    const factory_list = this.detailData?.factory_list.find((item) => item.code === _value[0].factory_code);
    this.getAllocationInitPlan({
      factory_code: _value[0].factory_code,
      allocate_method: this.orderAllocationType,
      order_uuid: this.data.order_uuid,
      qty: this.detailData?.to_be_allocated || 0,
      production_nos: factory_list?.production_lines?.map((_line) => _line.production_line_no) || [],
    });
  }

  // 获取分配详情 => 包括加工厂下下拉、产线下拉、颜色、po
  private getAllocationDetail() {
    this._service.getOrderAllocationDetail(this.data.order_uuid).subscribe((res) => {
      if (res.code !== 200) return;
      const factory_list = res.data.factory_list.filter((item) => this.factory_items.find((item2) => item2.factory_code === item.code));
      factory_list.forEach((_factory_item) => {
        _factory_item.production_lines = _factory_item.production_lines.filter((item) => {
          const factory_item = this.factory_items.find(
            (item2) => item2.factory_code === _factory_item.code && item2.production_line_no === item.production_line_no
          );
          return factory_item;
        });
      });
      res.data.factory_list = factory_list;
      this.detailData = res.data;
      this.detailData.due_times = this.detailData.po_list.map((item) => format(item.due_time, 'yyyy-MM-dd')).join(',');
      this.opitonsMap = {
        factory_code: factory_list,
        color_code: this.detailData.color_list,
        po_unique_code: this.detailData.po_list,
      };
      if (factory_list?.length > 1 || factory_list.length === 0) {
        this.onAddRow(0, {
          sam: this.data.sam,
          average_daily_production: this.data.average_daily_production,
          qty: this.detailData.to_be_allocated,
        });
      }
      if (factory_list?.length !== 1) return;
      this.getAllocationInitPlan({
        factory_code: factory_list[0].code,
        production_nos: factory_list[0].production_lines?.map((_line) => _line.production_line_no) || [],
        allocate_method: this.orderAllocationType,
        order_uuid: this.data.order_uuid,
        qty: this.detailData.to_be_allocated,
      });
    });
  }
  // 自动分配逻辑
  private getAllocationInitPlan(payload: AllocationInitPlanPaylod) {
    this._service.getAllocationInitPlan(payload).subscribe({
      next: (res) => {
        if (res.code === 200 && res.data.list?.length) {
          this.dataList.clear();
          res.data.list.forEach((item: any, index: number) => {
            item.average_daily_production = this.data.average_daily_production;
            item.sam = this.data.sam;
            const _factory_option = this.detailData?.factory_list.find((factory: any) => factory.code === item.factory_code);
            const _production_line_option = _factory_option?.production_lines?.find(
              (line: any) => line.production_line_no === item.production_line_no
            );
            item.production_line_name = _production_line_option?.production_line_name;
            this.onAddRow(index, item);
          });
        } else {
          this.dataList.controls.length === 0 &&
            this.onAddRow(0, {
              sam: this.data.sam,
              average_daily_production: this.data.average_daily_production,
              qty: this.detailData?.to_be_allocated,
            });
        }
      },
      error: () => {},
    });
  }

  onCloseModal(isClosed: boolean) {
    if (!isClosed) {
      this.modelRef.close();
      return;
    }
    const _invalid = this._flcValidatorService.formIsInvalid(this.modalForm);
    if (_invalid) return;
    const _value = this.dataList.getRawValue();
    if (_value.some((item) => !item.plan_end_time)) return;
    this.modelRef.triggerOk();
  }

  getPayload() {
    const _value = this.dataList.getRawValue();
    _value.forEach((item: any) => {
      item.plan_start_time = format(startOfDay(item.plan_start_time), 'T');
      item.plan_end_time = format(startOfDay(item.plan_end_time), 'T');
      item.selected = this.data.selected;
      // 后端需要color_name
      if (item.color_code) {
        const _option = this.opitonsMap.color_code?.find((e: any) => e.color_code === item.color_code);
        item.color_name = _option.color_name;
      }
    });

    return {
      order_uuid: this.data.order_uuid,
      allocate_method: this.orderAllocationType,
      is_publish: true,
      list: _value,
    };
  }

  validatorFactoryAndLines() {
    this.dataList.controls.forEach((control) => {
      const _factory_code = control.get('factory_code');
      if (_factory_code?.value) {
        _factory_code.markAsDirty();
        _factory_code.updateValueAndValidity();
      }

      const _production_line_no = control.get('production_line_no');
      if (_production_line_no?.value) {
        _production_line_no.markAsDirty();
        _production_line_no.updateValueAndValidity();
      }
    });
  }

  validatorFactory(arg: FormArray): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value && control.value !== 0) {
        return null;
      } else {
        const _repeatArr = [];
        const _values = arg.getRawValue();
        const _option = this.opitonsMap.factory_code?.find((option: any) => option.code === control.value);
        if (_option.order_distribute_type !== FactoryAllocationTypeEnum.factory) {
          return null;
        }
        const _pranentValue = control.parent?.getRawValue();
        _values.forEach((item) => {
          if (this.orderAllocationType === OrderAllocationTypeEnum.order) {
            item.factory_code === control.value && _repeatArr.push(item.factory_code);
          } else if (this.orderAllocationType === OrderAllocationTypeEnum.color) {
            item.factory_code === control.value && _pranentValue?.color_code == item.color_code && _repeatArr.push(item.factory_code);
          } else if (this.orderAllocationType === OrderAllocationTypeEnum.po) {
            item.factory_code === control.value &&
              _pranentValue?.po_unique_code == item.po_unique_code &&
              _repeatArr.push(item.factory_code);
          }
        });
        return _repeatArr.length >= 2 ? { duplicated: { message: '加工厂不可重复' } } : null;
      }
    };
  }

  validatoryProductionLine(arg: FormArray): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value && control.value !== 0) {
        return null;
      } else {
        const _repeatArr = [];
        const _values = arg.getRawValue();
        const _factory_code = control.parent?.value.factory_code;
        const _pranentValue = control.parent?.getRawValue();
        _values.forEach((item) => {
          if (this.orderAllocationType === OrderAllocationTypeEnum.order) {
            item.factory_code === _factory_code && item.production_line_no === control.value && _repeatArr.push(item.factory_code);
          } else if (this.orderAllocationType === OrderAllocationTypeEnum.color) {
            item.factory_code === _factory_code &&
              _pranentValue?.color_code == item.color_code &&
              item.production_line_no === control.value &&
              _repeatArr.push(item.factory_code);
          } else if (this.orderAllocationType === OrderAllocationTypeEnum.po) {
            item.factory_code === _factory_code &&
              _pranentValue?.po_unique_code == item.po_unique_code &&
              item.production_line_no === control.value &&
              _repeatArr.push(item.factory_code);
          }
        });
        return _repeatArr.length >= 2 ? { duplicated: { message: '加工厂产线不可重复' } } : null;
      }
    };
  }

  validatoryQty(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (control.value === 0 || control.value === '0') {
        return { customError: { message: '分配件数不能为0' } };
      }
      return null;
    };
  }
}
