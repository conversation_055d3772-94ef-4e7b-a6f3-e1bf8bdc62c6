.searchBarWrap {
  flex-shrink: 0;
  background-color: #f4f7f9;
  border-radius: 0 0 8px 8px;
}
.select-box {
  display: flex;
  font-size: 14px;
  color: #54607c;
  padding-left: 16px;
  padding-bottom: 8px;
  align-items: center;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 8px 24px;
  .search-box {
    display: flex;
    align-items: center;
    height: 32px;
  }
  .search-label {
    flex-shrink: 0;
    text-align: right;
  }
  ::ng-deep nz-select {
    min-width: 115px;
    width: 100%;
  }
}
.order-btn-box {
  display: flex;
  align-items: center;
  justify-content: center;

  button {
    margin: 0 -15px;
  }
  .accept-btn {
    margin-right: -5px;
  }
}

.sam-box {
  display: flex;
  align-items: center;
  text-align: center;
  .grey {
    color: #4d96ff;
  }
  > div {
    flex: 1;
  }
}

.pre-order-tag {
  font-size: 10px;
  position: absolute;
  right: 2px;
  top: 2px;
  width: 16px;
  height: 16px;
  text-align: center;
  line-height: 14px;
  color: #ef6c69;
  border: 1px solid #ef6c69;
  border-radius: 50%;
}

.table-title {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 10px;

  div {
    font-size: 16px;
    font-weight: 500;
    margin-right: 15px;
  }

  span {
    color: #007aff;
    i {
      margin-right: 5px;
    }
  }
}

::ng-deep {
  .sam-modal {
    .ant-radio-wrapper {
      margin-right: 0;
      span {
        padding-right: 0 !important;
      }
    }

    nz-input-number {
      width: 100%;
    }

    .ant-form-item-label {
      text-align: left;
    }

    .ant-form-item:last-child {
      margin-bottom: 0;
    }
  }
}
