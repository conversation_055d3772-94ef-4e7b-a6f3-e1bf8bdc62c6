.drawer-title {
  font-size: 18px;
  font-weight: 500;
  color: #222b3c;
}

.content-form {
  display: flex;
  flex-direction: column;

  nz-form-label {
    width: 25%;
    text-align: right;
    font-size: 14px;
    font-weight: 500;
    color: #515661;
  }

  nz-form-control {
    max-width: 70%;
  }
}
.content-form-en {
  display: flex;
  flex-direction: column;

  nz-form-label {
    width: 36%;
    text-align: right;
    font-size: 14px;
    font-weight: 500;
    color: #515661;
  }

  nz-form-control {
    max-width: 70%;
  }
}
.priority-content {
  .priority-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    font-weight: 500;
    color: #515661;
    .priority-head,
    .priority-subcontent-head {
      display: flex;
      justify-content: center;
    }
    .drag-notice {
      width: 148px;
      text-align: right;
    }
  }
  .priority-body {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 8px;
    // max-height: 225px;
    // overflow: auto;
    .priority-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .priority-num {
        width: 32px;
        height: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #4d96ff;
        border-radius: 8px;
        border: 1px solid #4d96ff;
        font-size: 16px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #ffffff;
      }
      .priority-subcontent {
        width: 70%;
        padding: 8px 16px;
        display: flex;
        align-items: center;
        gap: 5px;
        background: #f5f9ff;
        border-radius: 4px;
        font-size: 13px;

        ::ng-deep app-dynamic-search-select {
          .dy-select {
            width: 100%;
          }
        }

        i {
          cursor: pointer;
        }
      }
      .priority-handle {
        width: 32px;
        height: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        background: #edf5ff;
        border-radius: 8px;
        cursor: move;
        color: #a8cdff;
      }
    }
  }

  .cdk-drag-preview {
    display: flex;
    box-sizing: border-box;
    border-radius: 4px;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
  }

  .cdk-drag-placeholder {
    opacity: 0;
  }

  .cdk-drag-animating {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  }

  .example-box:last-child {
    border: none;
  }

  .example-list.cdk-drop-list-dragging .example-box:not(.cdk-drag-placeholder) {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  }
}

.footer-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 60px;
}
