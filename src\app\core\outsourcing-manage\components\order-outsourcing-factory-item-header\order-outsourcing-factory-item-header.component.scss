$highlightColor: #138aff;
$highlightIconColor: #0f86f8;
$highlightHoverColor: #4d96ff;
$highlightColor2: #007aff;
.plant-item-header-line {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    width: calc(100% + 16px);
    display: block;
    height: 0.7px;
    background-color: #d4d7dc;
    margin: 0 -8px 0 -8px;
  }
}

.plant-item-header {
  translate: all 1s;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .status-area-1 {
    display: flex;
    align-items: center;
    font-size: 12px;
    margin-left: 4px;
    .plant-status-box {
      display: flex;
      padding: 0 4px;
      align-items: center;
      height: 24px;
      border-radius: 4px;
      background-color: #feefe5;
      color: #fb6401;
      .plant-status-text {
        white-space: nowrap;
      }
    }
    .plant-status-box.is-active {
      background-color: #deefff;
      color: #007aff;
    }

    .plant-status-box.is-finish {
      background-color: #eaecef;
      color: #54607c;
    }
  }

  .status-area-2 {
    display: flex;
    align-items: center;
    .plant-status-box {
      border-radius: 13px 13px 13px 0px;
      background-color: #dee8f7;
      display: flex;
      padding: 2px 8px;
      align-items: center;

      i {
        margin-right: 2px;
        font-size: 15px;
        position: relative;
        top: 1px;
      }

      .plant-status-icon {
        color: #b8c4d9;
        opacity: 0.95;
      }

      .icon-chenggong-box {
        background-color: #b8c4d9;
        opacity: 0.95;
        height: 15px;
        width: 15px;
        padding: 1px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 2px;
        position: relative;
        top: 1px;

        i {
          margin-right: 0;
          color: #fff;
          font-size: 12px;
          position: relative;
          font-weight: 600;
        }
      }

      .plant-status-text {
        color: #54607c;
        line-height: 20px;
      }
    }

    .plant-status-box.is-active {
      background-color: #138aff;

      .icon-chenggong-box {
        background-color: #7fbcff;

        i {
          color: #fff;
          top: 0;
        }

        opacity: 0.85;
      }

      .plant-status-text {
        color: #ffffff;
      }
    }
  }
  .plant-item-header-left {
    flex: 1;
    align-items: center;

    .remark-input {
      width: 300px;
    }
    .plant-item-header-serial {
      width: 19px;
      height: 19px;
      background: #7fbcff;
      margin-top: 6px;
      border-radius: 11px;
      color: #fff;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      margin-right: 4px;
    }
    &::ng-deep {
      nz-select,
      nz-input-number-group {
        width: 160px;
      }
      .select-round {
        width: 82px;
        .ant-select-selector {
          box-shadow: none;
          border-color: #feefe5;
          background-color: #feefe5;
          border-radius: 16px;
        }
      }
      .check-type-read {
        height: 30px;
        border-radius: 15px;
        line-height: 30px;
        padding: 0 8px;
        min-width: 60px;
        text-align: center;
      }
      .select-round {
        border-radius: 16px;
        .ant-input-number {
          border-radius: 16px;
        }
        .ant-input-number-group > .ant-input-number:first-child,
        .ant-input-number-group-addon:first-child {
          border-top-right-radius: 0;
          border-bottom-right-radius: 0;
        }
        .ant-input-number-group > .ant-input-number-group-addon:last-child {
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
        }
        .ant-input-number-group-addon {
          padding: 0 8px;
          border-radius: 16px;
        }
      }
      .select-round-color-1 {
        background-color: #deefff;
        color: #138aff;
        .ant-select-selection-item,
        .ant-select-arrow {
          color: #138aff;
        }
        .ant-select-selector {
          background-color: #deefff;
          border-color: #deefff;
        }
      }
      .select-round-color-2 {
        background-color: #feefe5;
        color: #fb6401;
        .ant-select-selector {
          border-color: #feefe5;
        }
        .ant-select-selection-item,
        .ant-select-arrow {
          color: #fb6401;
        }
      }
      .select-round-color-3 {
        background-color: #e8eef4;
        color: inherit;
        .ant-select-selection-item,
        .ant-select-arrow {
          color: inherit;
        }
        .ant-select-selector {
          background-color: #e8eef4;
          border-color: #e8eef4;
        }
      }
    }
    .form-item-plant {
      margin-right: 24px;
      flex-wrap: nowrap;
      margin-bottom: 8px;
      flc-text-truncated {
        font-weight: 500;
        color: #36393e;
        display: inline;
        align-self: center;
      }
    }
    .price-button {
      color: #138aff;
      font-size: 12px;
      cursor: pointer;
      span {
        text-decoration-line: underline;
      }
    }
    .price-button:hover {
      color: #4d96ff;
    }

    .price-button-disabled,
    .price-button-disabled:hover {
      cursor: not-allowed;
      color: #00000040;
    }

    .plant-select-width ::ng-deep {
      nz-select,
      nz-input-number-group {
        max-width: 300px;
        min-width: 160px;
      }
    }
  }

  .plant-item-header-right {
    display: inline-flex;
    align-items: center;
    float: right;

    .fl-button-pretty-primary {
      background-color: #138aff;
      border-color: #138aff;
    }

    .ant-btn[disabled],
    .ant-btn[disabled]:hover,
    .ant-btn[disabled]:focus,
    .ant-btn[disabled]:active {
      color: rgba(0, 0, 0, 0.25);
      border-color: #d9d9d9;
      background: #eff0f4;
      text-shadow: none;
      box-shadow: none;
    }

    .icon-delete {
      color: #54607c;

      &:hover {
        color: #f74949;
        cursor: pointer;
      }
    }

    span {
      margin-right: 4px;
      color: $highlightColor;
    }

    .icon-toggle {
      color: $highlightIconColor;
      font-size: 12px;
    }

    .icon-xinjian1 {
      margin-right: 4px;
      font-size: 12px;
      color: $highlightColor;
    }

    .surplus {
      display: flex;
      align-items: center;
      margin-right: 0;

      &::after {
        content: '';
        margin: 0 12px;
        display: inline-block;
        height: 12px;
        width: 1px;
        background-color: #d4d7dc;
      }

      &:hover span {
        cursor: pointer;
        color: $highlightHoverColor;
      }
    }

    &::ng-deep .ant-divider-vertical {
      border-left: 1px solid #d3d3d3;
      margin: 0 13px;
      height: 12px;
    }
    .toggle,
    .xinjian-box {
      &:hover {
        cursor: pointer;

        span {
          color: $highlightHoverColor;
        }

        i {
          color: $highlightHoverColor;
        }
      }

      i,
      span {
        color: $highlightColor;
      }
    }
    .toggle {
      i,
      span {
        color: #54607c;
      }
    }
    .disabled-element {
      color: #b5b8bf !important;

      &:hover {
        cursor: no-drop;
        opacity: 0.8;

        span {
          cursor: no-drop !important;
          text-decoration: none !important;
          color: #b5b8bf !important;
        }

        .icon-xinjian1 {
          color: #b5b8bf;
        }
      }

      .icon-xinjian1 {
        color: #b5b8bf;
      }

      span {
        color: #b5b8bf;
      }
    }
  }
}

.reset-padding {
  padding-bottom: 0;
}

.recommend-btn {
  background: linear-gradient(#2becb6, #00c790) !important;
  border-width: 0;
}

:host ::ng-deep {
  .order-garment-outsourcing-select-area {
    .ant-select-selection-item {
      height: 20px;
      line-height: 16px;
      font-size: 12px;
    }
  }
}
