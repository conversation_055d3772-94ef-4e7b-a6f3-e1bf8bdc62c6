<app-order-outsourcing-card-container [titleLine]="false" [borderRadiusSize]="'12px 12px 12px 12px'" class="plant-container">
  <div class="plant-header" [formGroup]="parentGroup">
    <ng-container *ngIf="leftContainerTpl">
      <ng-template [ngTemplateOutlet]="leftContainerTpl"></ng-template>
    </ng-container>
    <div class="plant-add" (click)="handleAddPlant()" *ngIf="editMode !== 'read'">
      <button nz-button flButton="pretty-minor" nzShape="round">
        <i nz-icon [nzIconfont]="'icon-xinjian1'"></i>{{ 'outsourcingComponents.新增加工厂' | translate }}
      </button>
    </div>
  </div>
  <!-- 加工厂 item -->
  <div class="plant-body">
    <ng-content></ng-content>
  </div>
</app-order-outsourcing-card-container>
