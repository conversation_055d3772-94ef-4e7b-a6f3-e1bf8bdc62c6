import { Injectable } from '@angular/core';
import { CanDeactivate, Router } from '@angular/router';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { AuthService } from './auth.service';
import { ModalService } from './modal.service';
import { RouterEventBusService } from './router-eventbus.service';

/**
 * @deprecated Replaced with FlcLeaveGuard.
 */
@Injectable({
  providedIn: 'root',
})
export class LeaveGuard implements CanDeactivate<any> {
  constructor(
    private _auth: AuthService,
    private _router: Router,
    private _modal: NzModalService,
    private _appModal: ModalService,
    private _routerEventBus: RouterEventBusService
  ) {}

  canDeactivate(component: any) {
    console.log(component);
    let canleave = true;

    if (typeof component.canLeave === 'function') {
      canleave = component.canLeave();
    }

    if (!canleave) {
      const ref: NzModalRef = this._appModal.confirm('confirm-leave');
      ref.afterClose.subscribe((val: boolean) => {
        if (!val) {
          this._routerEventBus.emitRouterEvent({ type: 'NavigationCanceled', name: '', value: null });
        }
      });
      return ref.afterClose;
    }

    return true;
  }
}
