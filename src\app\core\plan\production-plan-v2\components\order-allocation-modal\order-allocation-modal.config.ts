import { OrderAllocationTypeEnum, OrderCalculationTypeEnum } from '../../model/production-plan.enum';

export const modalOrderHeaders = [
  {
    name: '分配件数',
    key: 'qty',
    type: 'input-number',
    width: '100px',
    required: true,
    max: 99999999,
    precision: 0,
    min: 0,
  },
  {
    name: '加工厂',
    key: 'factory_code',
    type: 'select',
    width: '120px',
    optionKey: {
      value: 'code',
      label: 'name',
    },
    required: true,
  },
  {
    name: '产线',
    key: 'production_line_no',
    labelKey: 'production_line_name',
    type: 'select',
    width: '120px',
    optionKey: {
      value: 'production_line_no',
      label: 'production_line_name',
    },
    required: true,
  },
  {
    name: '人均日台产',
    key: 'average_daily_production',
    type: 'input-number',
    width: '100px',
    required: true,
    precision: 0,
    max: 99999999,
    min: 1,
  },
  {
    name: 'SAM',
    key: 'sam',
    type: 'input-number',
    width: '100px',
    required: true,
    precision: 2,
    max: 99999999.99,
    min: 0.01,
  },
  {
    name: '生产天数',
    key: 'work_days',
    type: 'text',
    width: '70px',
  },
  {
    name: '休息日',
    key: 'break_days',
    type: 'text',
    width: '70px',
  },
  {
    name: '计划生产起止',
    key: 'plan_start_time',
    type: 'dateTpl',
    labelKey: 'plan_end_time',
    width: '200px',
    required: true,
  },
  {
    name: '剩余件数',
    key: 'surplus_qty',
    type: 'global-text',
    width: '70px',
  },
];

export function getTableHeadersMap(selected?: OrderCalculationTypeEnum) {
  const _modalOrderHeaders = modalOrderHeaders.filter(
    (item) => item.key !== (selected === OrderCalculationTypeEnum.sam ? 'average_daily_production' : 'sam')
  );
  const modalColorHeaders = [
    {
      name: '颜色',
      key: 'color_code',
      labelKey: 'color_name',
      type: 'select',
      width: '120px',
      optionKey: {
        value: 'color_code',
        label: 'color_name',
      },
      required: true,
    },
    ..._modalOrderHeaders,
  ];
  const modalPoHeaders = [
    {
      name: '交付单',
      key: 'po_unique_code',
      type: 'select',
      width: '120px',
      optionKey: {
        value: 'po_unique_code',
        label: 'po_code',
      },
      required: true,
    },
    ..._modalOrderHeaders,
  ];
  return {
    [OrderAllocationTypeEnum.order]: _modalOrderHeaders,
    [OrderAllocationTypeEnum.color]: modalColorHeaders,
    [OrderAllocationTypeEnum.po]: modalPoHeaders,
  };
}
