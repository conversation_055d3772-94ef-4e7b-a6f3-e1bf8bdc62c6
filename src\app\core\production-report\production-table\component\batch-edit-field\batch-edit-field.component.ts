import { Component, Input, OnInit } from '@angular/core';
import { ProductionTableService } from '../../production-table.service';
import { IBatchEditFieldOption, ITemplateFields } from '../../production-table.interface';
import { BatchEditTypeEnum, EventTypeEnum, FieldTypeEnum } from '../../production-table.enum';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import { FlcValidatorService } from 'fl-common-lib';
import { NzModalService } from 'ng-zorro-antd/modal';
import { initDefaultTableHeaders } from '../../production-table.config';
import { NzMessageService } from 'ng-zorro-antd/message';
@Component({
  selector: 'app-batch-edit-field',
  templateUrl: './batch-edit-field.component.html',
  styleUrls: ['./batch-edit-field.component.scss'],
})
export class BatchEditFieldComponent implements OnInit {
  @Input() selectEditItem: IBatchEditFieldOption | null = null;
  @Input() param: any;
  validateForm = this._fb.group({});
  formConfig: any = [];
  template: any = null;
  translateName = 'ProductionTableTemplateFiled.';
  tableHeader = initDefaultTableHeaders();
  templateFields: ITemplateFields[] = [];
  FieldTypeEnum = FieldTypeEnum;

  constructor(
    private _service: ProductionTableService,
    private _fb: FormBuilder,
    private _validator: FlcValidatorService,
    private _modal: NzModalService,
    private _message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.createForm();
  }

  createForm() {
    this.formConfig = [];
    // 字段
    if (this.selectEditItem?.type === BatchEditTypeEnum.field) {
      let config = {};
      if (
        ['shipment_status', 'delay_range', 'ship_performance', 'delay_responsibility']?.includes(this.selectEditItem?.prod_status_field)
      ) {
        // 下拉
        const op = this.tableHeader?.find((item: any) => item.key === this.selectEditItem?.prod_status_field);
        config = {
          type: 'select',
          column: op?.column || op?.key,
          dataUrl: '/service/scm/dict_category/dict_option',
          transDataValue: 'label',
          transDataLabel: 'label',
        };
      } else if (['delay_reason']?.includes(this.selectEditItem?.prod_status_field)) {
        config = { type: 'textarea', maxLength: 100 };
      } else if (['transit_time']?.includes(this.selectEditItem?.prod_status_field)) {
        config = { type: 'input-number', min: 0, precision: 0 };
      }

      this.formConfig.push({
        required: false,
        key: this.selectEditItem?.prod_status_field,
        name: this.selectEditItem?.prod_status_field_name,
        ...config,
      });
      this.initFieldForm();
    } else if (this.selectEditItem?.type === BatchEditTypeEnum.node) {
      // 节点
      const node_template_id = this.selectEditItem?.node_template_id;
      if (node_template_id) {
        this._service
          .getNodeTemplateFieldList({
            id: this.selectEditItem?.template_node_id,
          })
          .subscribe((res: any) => {
            if (res?.code === 200) {
              this.templateFields = res?.data?.template_fields;
              console.log(res?.data);
              // this.initNodeEditForm();
            }
          });
      }
    }
  }

  // 字段编辑表单
  initFieldForm() {
    this.formConfig?.forEach((item: any) => {
      const _control = new FormControl();
      item.required && _control.addValidators(Validators.required);
      item.maxLength && _control.addValidators(Validators.maxLength(item.maxLength));
      this.validateForm?.addControl(item.key, _control);
    });
  }

  getOptions(item: any) {
    if ([FieldTypeEnum.SingleChoice, FieldTypeEnum.MultipleChoice].includes(item.field_type)) {
      const attr = JSON.parse(item?.field_data);
      return attr?.option_name?.map((op: any) => {
        return {
          label: op?.label,
          value: op?.label,
        };
      });
    } else {
      return [];
    }
  }

  // 节点编辑表单
  initNodeEditForm() {
    this.formConfig = this.templateFields?.map((item: any) => {
      const cf: any = {
        label: item.field_name,
        key: item.field_name,
        tag: item.label,
        required: item.require === 1,
        type: item.field_type,
        data: item?.data,
        options: this.getOptions(item),
      };
      if (item.field_type === FieldTypeEnum.TextBox) {
        cf['maxLength'] = 50;
      }
      if (item.field_type === FieldTypeEnum.Number) {
        cf['max'] = 999999999.99999;
        cf['precision'] = 5;
      }
      return cf;
    });

    this.formConfig?.forEach((item: any) => {
      const _control = new FormControl();
      item.required && _control.addValidators(Validators.required);
      this.validateForm?.addControl(item.key, _control);

      const val = item?.data ? JSON.parse(item?.data) : null;
      this.validateForm?.get(item.key)?.setValue(val);
    });
  }

  async onSave() {
    const isInvalid = await this._validator.formIsAsyncInvalid(this.validateForm);
    if (isInvalid) {
      return;
    }
    const value = this.validateForm?.getRawValue();
    const key = this.selectEditItem?.prod_status_field;
    const payload = {
      prod_status_field: key,
      data: key && value?.[key] ? (typeof value?.[key] === 'string' ? value?.[key] : JSON.stringify(value?.[key])) : null,
    };
    this._service
      .updateBatchFieldOrNode({
        ids: this.param?.ids || [],
        type: BatchEditTypeEnum.field,
        ...payload,
      })
      .subscribe((res: any) => {
        if (res?.code === 200) {
          this._message.success(this._service.translateValue('success.save'));
          this._service.eventEmitter.next(EventTypeEnum.refreshList);
          this._modal.closeAll();
        }
      });
  }

  onCancel() {
    this._modal.closeAll();
  }
}
