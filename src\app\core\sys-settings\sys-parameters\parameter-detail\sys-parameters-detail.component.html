<flc-app-header [headerTopTitle]="'sysSetting.sysParameters.common.pageTitle' | translate" [headerBtn]="headerBtnTpl"></flc-app-header>
<ng-template #headerBtnTpl>
  <div class="header-btn-container">
    <ng-container *ngIf="!isEditMode && canEdit">
      <button nz-button flButton="pretty-primary" [nzShape]="env_project === 'deepflow' ? null : 'round'" (click)="onEdit()">
        <i nz-icon [nzIconfont]="'icon-caozuolan_bianji1'"></i>{{ 'btn.edit' | translate }}
      </button>
    </ng-container>
    <ng-container *ngIf="isEditMode">
      <button nz-button flButton="pretty-default" [nzShape]="env_project === 'deepflow' ? null : 'round'" (click)="onCancel()">
        {{ 'btn.cancel' | translate }}
      </button>
      <button nz-button flButton="pretty-primary" [nzShape]="env_project === 'deepflow' ? null : 'round'" (click)="onSubmit()">
        <i nz-icon [nzIconfont]="'icon-baocun'"></i>{{ 'btn.save' | translate }}
      </button>
    </ng-container>
  </div>
</ng-template>

<div class="form-container">
  <app-sys-parameters-form #paramsForm [isEditMode]="isEditMode" [paramsData]="paramsData"></app-sys-parameters-form>
</div>
