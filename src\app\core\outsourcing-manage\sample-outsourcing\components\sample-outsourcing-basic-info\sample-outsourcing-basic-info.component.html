<div class="wrapper-block" style="padding: 0 8px">
  <div class="block-content">
    <app-sample-oursourcing-basic-form
      [formInfo]="staticData"
      [isEdit]="isEdit"
      [simpleData]="simpleData"></app-sample-oursourcing-basic-form>
  </div>
</div>
<!-- 基本信息  -->
<div class="wrapper-block">
  <div class="block-title">{{ translateName + '基本信息' | translate }}</div>
  <div class="block-content">
    <div nz-row nzType="flex">
      <div nz-col [nzSpan]="item.code === 'extra_process_name' ? 16 : 8" class="info-item" *ngFor="let item of basicInfoConfig">
        <span>{{ translateName + item.label | translate }}：</span>
        <span><flc-text-truncated data="{{ simpleData[item.code] }}"></flc-text-truncated></span>
      </div>
      <div nz-col [nzSpan]="24" class="info-item">
        <span>{{ translateName + '图片' | translate }}：</span>
        <span>
          <flc-file-gallery
            #fileGallery
            [galleryType]="'image'"
            [wrap]="false"
            [fileList]="simpleData.style_pic_list || []"
            [fileMaxSize]="20"
            [fileMaxCount]="100"
            [isEditMode]="false"></flc-file-gallery>
        </span>
      </div>
    </div>
  </div>
</div>
<!-- 颜色尺码 -->
<div class="wrapper-block">
  <div class="block-title">{{ translateName + '颜色尺码' | translate }}</div>
  <div class="block-content">
    <flc-color-size-table
      *ngIf="simpleData && simpleData.color_size && simpleData.color_size.length > 0"
      [scrollHeight]="240"
      [mode]="'readOnly'"
      dataVersion="v1"
      [dataList]="simpleData.color_size"></flc-color-size-table>
    <flc-no-data *ngIf="!simpleData.color_size || simpleData.color_size.length === 0"></flc-no-data>
  </div>
</div>
<!-- 打样信息 -->
<div class="wrapper-block">
  <div class="block-title">{{ translateName + '打样信息' | translate }}</div>
  <div class="block-content">
    <div nz-row nzType="flex">
      <div nz-col [nzSpan]="item.colSpan" class="info-item" *ngFor="let item of sampleInfoConfig">
        <span>{{ translateName + item.label | translate }}：</span>
        <span
          style="overflow-x: hidden; display: inline-block; width: 100%"
          [class]="item.code === 'material_requirements' && simpleData?.material_rel?.length ? 'plan-wrap' : ''">
          <div class="textarea-box" [ngStyle]="{ color: simpleData[item.code] ? '' : '#b5b8bf' }">{{ simpleData[item.code] || '-' }}</div>
          <flss-material-plan-integration-form
            *ngIf="item.code === 'material_requirements'"
            [data]="simpleData?.material_rel ?? []"></flss-material-plan-integration-form>
        </span>
      </div>
    </div>
  </div>
</div>
