:host ::ng-deep {
  .accept-info {
    padding: 10px 15px;
  }

  .base-info {
    display: flex;
  }

  .style-picture-wrap {
    width: 275px;
    flex-shrink: 0;
  }

  .labelStyle {
    width: 112px;
    text-align: right;
  }

  .ant-form-item {
    margin-bottom: 10px;
  }

  .remark-info {
    max-height: 100px;
    overflow: auto;
    word-break: break-all;
    white-space: pre-line;
  }

  nz-form-control {
    font-weight: 500;
  }

  .ant-divider-horizontal.ant-divider-with-text {
    font-weight: 500;
    font-size: 14px;
    color: #222b3c;
  }

  .reqInfo-info {
    max-height: 100px;
    overflow: auto;
    word-break: break-all;
    white-space: pre-line;

    &:empty::after {
      content: '-';
      color: #b5b8bf;
    }
  }

  .flc-image-gallery {
    scale: 1.5;
    transform: translate(45px, 20px);
  }
}
