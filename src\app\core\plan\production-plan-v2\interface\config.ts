import { ElementRef } from '@angular/core';
import { LineOrderInterface } from './plan-production';
import { PlanGraphOperationTypeEnum } from './plan.enum';

export interface PlanMovedParam {
  temp_session?: string;
  target_time: number;
  group_id?: number;
  operation_type: PlanGraphOperationTypeEnum;
  factory_code?: string; // 目标工厂
  production_line_no?: string; // 目标产线
  pre_material_completed_time?: number;

  is_should_comfirm?: boolean; // 提示用户
}
export interface PlanRightClickParam {
  /** 抛出的位置, 产线空白处还是具体某一个绘图元素 */
  postion: 'graphItem' | 'blank';
  /** 在定位弹窗时的位置来源 */
  ref?: HTMLElement | ElementRef;
  /** 空白地区点击时需要传递 */
  x: number;
  /** 空白地区点击时需要传递 */
  y: number;
  productionLineNo?: string;
  bulkOrderId?: number;
  rawData?: LineOrderInterface;
  event?: MouseEvent;
  width?: number;
}
