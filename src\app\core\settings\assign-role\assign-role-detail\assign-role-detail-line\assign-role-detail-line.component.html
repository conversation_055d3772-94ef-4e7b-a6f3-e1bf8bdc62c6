<div class="wrap">
  <div class="title">
    <label [nzDisabled]="true" nz-checkbox [ngModel]="item.value"> </label>
    <span>{{ item.name }}</span>
  </div>
  <nz-divider nzType="vertical" *ngIf="item.value"></nz-divider>
  <div class="type" *ngIf="item.value">
    <label nz-radio [nzDisabled]="true" [ngModel]="true"></label>
    <ng-container *ngIf="isRangeMode">
      <span *ngIf="item.action_type === 'read'">{{ translateName + '只读权限' | translate }}</span>
      <span *ngIf="item.action_type === 'write'">{{ translateName + '编辑权限' | translate }}</span>
    </ng-container>
    <ng-container *ngIf="!isRangeMode">
      <span *ngIf="item.action_type === 'read'">{{ translateName + '查看者' | translate }}</span>
      <span *ngIf="item.action_type === 'write'">{{ translateName + '编辑者' | translate }}</span>
    </ng-container>
  </div>
  <div class="btnBoard" *ngIf="!isRangeMode && item.action_type === 'write' && showBtnArea">
    <ng-container *ngFor="let child of item.children ?? []">
      <div class="btn" *ngIf="child.visible">
        <label [nzDisabled]="true" nz-checkbox [ngModel]="child.value"></label>
        {{ child.name }}
      </div>
    </ng-container>
  </div>
  <div class="rangeBoard" *ngIf="isRangeMode && item.value && showRangeArea">
    <nz-radio-group style="display: flex; flex-wrap: wrap" [nzDisabled]="true" [(ngModel)]="item.payload.data_type">
      <label class="radioText" nz-radio [nzValue]="1">{{ translateName + '全部数据' | translate }}</label>
      <label class="radioText" nz-radio [nzValue]="2">{{ translateName + '仅本人' | translate }}</label>
      <label class="radioText" nz-radio [nzValue]="3">{{ translateName + '仅本部门及下级部门' | translate }}</label>
      <label class="radioText" nz-radio [nzValue]="4">
        <div>
          {{ translateName + '自定义部门' | translate }}
          <nz-select
            [nzDisabled]="true"
            style="min-width: 150px; max-width: max(15vw, 150px)"
            [(ngModel)]="item.payload.data_range"
            nzMode="multiple"
            nzPlaceHolder=""
            [nzMaxTagCount]="3"
            nzTooltipPlacement="topRight"
            [nzTooltipColor]="'rgba(15, 20, 35, 0.711)'"
            [nz-tooltip]="item.payload.data_range.length > 0 ? departmentList : null"
            nzAllowClear>
            <nz-option *ngFor="let item of allDepartmentList" [nzLabel]="item.name" [nzValue]="item.id"> </nz-option>
          </nz-select>
        </div>
      </label>
    </nz-radio-group>
  </div>
</div>
<ng-template #departmentList>
  <!-- <ng-container *ngFor="let department of allDepartmentList">
    <span style="margin: 4px 8px" *ngIf="item.payload.data_range.includes(department.id)">{{ department.name }}</span>
  </ng-container> -->
  <ng-container *ngFor="let deptId of item.payload.data_range">
    <span> {{ getDepartmentName(deptId) }} </span>
    <span *ngIf="deptId !== item.payload.data_range[item.payload.data_range.length - 1]" style="margin: 0 8px">|</span>
  </ng-container>
</ng-template>
