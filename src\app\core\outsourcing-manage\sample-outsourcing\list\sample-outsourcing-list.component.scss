.proofing-task-list {
  height: calc(100vh - 70px);
  display: flex;
  flex-direction: column;
  .table-box {
    flex-grow: 1;
    background-color: #fff;
    padding: 0 12px 8px 12px;
    border-radius: 4px;
    margin-top: 4px;
    .check-box-search {
      display: flex;
      align-items: center;
      padding: 11px 0;
      flc-order-status-checkbox {
        flex: 1;
      }
      .btn-box {
        text-align: right;
        .select-num {
          margin-right: 8px;
          font-size: 12px;
        }
      }
      ::ng-deep .flc-outer-container {
        .ant-btn {
          height: 26px;
          margin-right: 8px;
        }
      }
    }
    button {
      margin-right: 8px;
    }
  }
  nz-input-group {
    width: 135px;
  }
  nz-select {
    width: 90px;
  }
  ::ng-deep nz-range-picker {
    width: 250px;
  }
}
