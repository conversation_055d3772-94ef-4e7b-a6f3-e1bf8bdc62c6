import { NzModalService } from 'ng-zorro-antd/modal';
import { Component, EventEmitter, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';

@Component({
  selector: 'app-delete-modal',
  templateUrl: './delete-modal.component.html',
  styleUrls: ['./delete-modal.component.scss'],
})
export class DeleteModalComponent implements OnInit {
  @ViewChild('titleTpl') titleTpl: TemplateRef<any> | undefined;
  @ViewChild('contentTpl') contentTpl: TemplateRef<any> | undefined;
  @Output() handleDelete = new EventEmitter<any>();
  is_employee = false;
  data!: any;
  constructor(private _modal: NzModalService) {}

  ngOnInit(): void {}

  /**
   * 创建删除二次确认弹窗
   */
  createDeleteModal() {
    this._modal.create({
      nzWrapClassName: 'modal-outer-middle',
      nzTitle: this.titleTpl,
      nzContent: this.contentTpl,
      nzFooter: null,
      nzWidth: '320px',
    });
  }

  /**
   * 关闭弹窗
   */
  closeDeleteModal() {
    this._modal.closeAll();
  }

  /**
   * 点击取消时的回调
   */
  handleCancel() {
    this.closeDeleteModal();
  }

  /**
   * 点击确认时回调
   * 由父组件去处理确认删除的逻辑
   */
  handleOk() {
    this.handleDelete.emit(this.data);
  }
}
