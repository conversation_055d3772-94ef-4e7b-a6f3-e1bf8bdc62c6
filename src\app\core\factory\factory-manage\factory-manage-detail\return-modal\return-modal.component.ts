import { Form<PERSON><PERSON>er, Validators } from '@angular/forms';
import { FormGroup } from '@angular/forms';
import { Component, EventEmitter, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { NzModalService } from 'ng-zorro-antd/modal';

@Component({
  selector: 'app-return-modal',
  templateUrl: './return-modal.component.html',
  styleUrls: ['./return-modal.component.scss'],
})
export class ReturnModalComponent implements OnInit {
  @ViewChild('titleTpl') titleTpl: TemplateRef<any> | undefined;
  @ViewChild('contentTpl') contentTpl: TemplateRef<any> | undefined;
  @ViewChild('footerTpl') footerTpl: TemplateRef<any> | undefined;
  @Output() handleReturnOk = new EventEmitter<any>();
  placeInput!: string;
  returnForm!: FormGroup;

  constructor(private _modal: NzModalService, private _fb: FormBuilder, private _translateService: TranslateService) {}

  ngOnInit(): void {}

  createReturnModel() {
    this.placeInput = this._translateService.instant('placeholder.input');
    this.returnForm = this._fb.group({
      reason: [null, [Validators.required, Validators.maxLength(50)]],
    });
    this._modal.create({
      nzWidth: '400px',
      nzTitle: this.titleTpl,
      nzContent: this.contentTpl,
      nzFooter: this.footerTpl,
    });
  }

  closeReturnModel() {
    this._modal.closeAll();
  }

  handleCancel() {
    this.closeReturnModel();
  }

  handleOk() {
    for (const item in this.returnForm.controls) {
      this.returnForm.controls[item].markAsDirty();
      this.returnForm.controls[item].updateValueAndValidity();
    }

    if (this.getFormStatus() === 'PENDING') {
      const sub = this.returnForm.statusChanges.subscribe(() => {
        const status = this.getFormStatus();
        if (status !== 'PENDING') {
          sub.unsubscribe();
          this.onOk();
        }
      });
    } else {
      this.onOk();
    }
  }

  onOk() {
    if (this.getFormStatus() === 'VALID') {
      this.handleReturnOk.emit({ reason: this.returnForm.getRawValue().reason });
    }
  }

  getFormStatus() {
    let status = 'VALID';

    for (const item in this.returnForm.controls) {
      if (this.returnForm.controls[item].status === 'PENDING') {
        return 'PENDING';
      } else if (this.returnForm.controls[item].status === 'INVALID') {
        status = 'INVALID';
      }
    }

    return status;
  }
}
