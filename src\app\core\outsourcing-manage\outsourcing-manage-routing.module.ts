import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
const routes: Routes = [
  {
    path: 'sample-outsourcing',
    loadChildren: () => import('./sample-outsourcing/sample-outsourcing.module').then((m) => m.SampleOutsourcingModule),
    // loadChildren: () => import('./../plan/sample-adjustment/sample-adjustment.module').then((m) => m.SampleAdjustmentModule),
  },
  {
    path: 'garment-outsourcing',
    loadChildren: () => import('./garment-outsourcing/order-garment-outsourcing.module').then((m) => m.OrderGarmentOutsourcingModule),
  },
  {
    path: 'sec-process-outsourcing',
    loadChildren: () => import('./sec-process-outsourcing/sec-process-outsourcing.module').then((m) => m.SecProcessOutsourcingModule),
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class OutsourcingManageRoutingModule {}
