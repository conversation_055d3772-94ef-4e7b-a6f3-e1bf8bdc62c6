:host ::ng-deep {
  .container {
    .search-item-container {
      display: flex;
      align-items: center;
    }

    .blue-link-text {
      text-decoration: underline;
      text-underline-offset: 3px;
      color: #138aff;
      cursor: pointer;
      &:hover {
        opacity: 0.8;
      }
    }

    .red-link-text {
      text-decoration: underline;
      text-underline-offset: 3px;
      color: #f96d6d;
      cursor: pointer;
      &:hover {
        opacity: 0.8;
      }
    }

    .red {
      color: #f96d6d;
    }

    // 未开始
    .io-status-1 {
      color: #54607c;
      background-color: #e4ebef;
    }
    // 生产中
    .io-status-2 {
      color: #138aff;
      background-color: #e7f3fe;
    }
    // 已完成
    .io-status-3 {
      color: #54607c;
      background-color: #e4ebef;
    }
    // 未完成
    .po-status-1 {
      color: #fb6401;
      background-color: #feefe5;
    }
    // 已完成
    .po-status-2 {
      color: #54607c;
      background-color: #e4ebef;
    }
    // 已取消
    .io-status-4, .po-status-3 {
      color: #97999C;
      background-color: #E6E6E6;
    }
    .status {
      border-radius: 12px;
      text-align: center;
      padding: 4px 7px;
      font-size: 12px;
      font-weight: 500;
    }
  }

  .total-row.ant-table-row {
    position: sticky;
    bottom: 0;
    z-index: 2;
    td {
      background-color: #f3faff;
      border-top: 1px solid #d4d7dc;
    }
  }

  .due_times {
    display: flex;
    flex-direction: column;
    span {
      height: 20px;
      line-height: 20px;
    }
  }
}
