<form nz-form *ngIf="shiftSettingForm" [formGroup]="shiftSettingForm" class="shift-setting-form">
  <nz-form-item>
    <nz-form-label nzRequired>{{ 'shift-setting.上班时间' | translate }}</nz-form-label>
    <nz-form-control>
      <nz-time-picker formControlName="work_start_time" nzFormat="HH:mm" style="width: 320px"></nz-time-picker>
    </nz-form-control>
  </nz-form-item>
  <nz-form-item>
    <nz-form-label nzRequired>{{ 'shift-setting.下班时间' | translate }}</nz-form-label>
    <nz-form-control>
      <nz-time-picker formControlName="work_end_time" nzFormat="HH:mm" style="width: 320px"></nz-time-picker>
    </nz-form-control>
  </nz-form-item>
  <div class="gap"></div>
  <nz-form-item>
    <nz-form-label>{{ 'shift-setting.第一次休息时间' | translate }}</nz-form-label>
    <nz-form-control>
      <nz-time-picker formControlName="first_rest_start_time" nzFormat="HH:mm"></nz-time-picker>
    </nz-form-control>
    <div class="start">{{ 'shift-setting.开始' | translate }}</div>
  </nz-form-item>
  <nz-form-item>
    <nz-form-control style="margin-left: 140px">
      <nz-time-picker formControlName="first_rest_end_time" nzFormat="HH:mm"></nz-time-picker>
    </nz-form-control>
    <div class="end">{{ 'shift-setting.结束' | translate }}</div>
  </nz-form-item>
  <nz-form-item>
    <nz-form-label>{{ 'shift-setting.第二次休息时间' | translate }}</nz-form-label>
    <nz-form-control>
      <nz-time-picker formControlName="second_rest_start_time" nzFormat="HH:mm"></nz-time-picker>
    </nz-form-control>
    <div class="start">{{ 'shift-setting.开始' | translate }}</div>
  </nz-form-item>
  <nz-form-item>
    <nz-form-control style="margin-left: 140px">
      <nz-time-picker formControlName="second_rest_end_time" nzFormat="HH:mm"></nz-time-picker>
    </nz-form-control>
    <div class="end">{{ 'shift-setting.结束' | translate }}</div>
  </nz-form-item>
  <nz-form-item>
    <nz-form-label>{{ 'shift-setting.放假时间' | translate }}</nz-form-label>
    <nz-form-control>
      <nz-radio-group
        formControlName="holiday_type"
        style="height: 32px; display: flex; align-items: center"
        (ngModelChange)="frequencyChange()">
        <label nz-radio [nzValue]="frequencyTypeEnum.week">{{ 'shift-setting.每周重复' | translate }}</label>
        <label nz-radio [nzValue]="frequencyTypeEnum.month">{{ 'shift-setting.每月重复' | translate }}</label>
      </nz-radio-group>
    </nz-form-control>
    <ng-container *ngIf="shiftSettingForm?.get('holiday_type')?.value">
      <app-work-frequencies [type]="shiftSettingForm?.get('holiday_type')?.value" formControlName="work_frequencies"></app-work-frequencies>
    </ng-container>
  </nz-form-item>
</form>
