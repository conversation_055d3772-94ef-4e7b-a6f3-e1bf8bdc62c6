import { PlanGraphItem, PlanOrderInfo, PlanOrderViewItem, ProductionLineListItem } from '.';
// 产线视图/表格视图详情传参
export interface ProductionPlanParams {
  order_code?: string;
  po_code?: string;
  production_line_nos?: string | number[];
  date?: Date[];
  start_date?: Date | string;
  end_date?: Date | string;
  style_code?: string;
  publish_status?: number[];
  alert_status?: number[];
  production_status?: number[];
  temp_session?: string;
}

export class ProductionLinePlanRenderItem implements ProductionLinePlanRenderItemInterface {
  factory_name: string;
  production_line_name: string;
  production_line_no: string;
  OrderRenderList: PlanGraphItem[];
  rawData: ProductionPlanLineItem;
  rest_days: Date[] | number[];
  height = 52;
  unPublish: boolean;

  constructor(data: ProductionLinePlanRenderItemInterface) {
    this.factory_name = data.factory_name;
    this.production_line_name = data.production_line_name;
    this.production_line_no = data.production_line_no;
    this.rawData = data.rawData;
    this.OrderRenderList = data.OrderRenderList;
    this.rest_days = data?.rest_days ?? [];
    this.production_line_name = data?.production_line_name ?? null;
    this.height = data?.height || 52;
    this.unPublish = data.rawData.line_orders.some((item) => item.publish_status === 1);
  }
  get isSelected() {
    return this.rawData.isSelected;
  }
  set isSelected(value: boolean) {
    this.rawData.isSelected = value;
  }
}
export interface ProductionLinePlanRenderItemInterface {
  factory_name: string;
  production_line_no: string;
  production_line_name: string;
  OrderRenderList: PlanGraphItem[];
  rawData: ProductionPlanLineItem;
  rest_days: Date[] | number[];
  height: number;
}

export class ProductionPlanLineItem {
  factory_code: string;
  factory_name: string; // 加工厂
  production_line_no: string; // 产线no
  production_line_name: string; // 产线名称
  lock_user_id: number;
  lock_user_name: string;
  isDisabled: boolean; // 是否禁用
  isHeightLight: boolean; // 是否高亮
  line_orders: LineOrderInterface[]; // 甘特图条块
  rest_days: Date[] | number[]; //产线休息天数
  rawData: ProductionLineListItem; //存储当前行原始数据
  isHover: boolean;
  isSelected: boolean;
  order_info_map: { [key: string]: PlanOrderInfo }; //
  constructor(rawData: ProductionLineListItem, planOrderInfo: { [key: string]: PlanOrderInfo }, order_uuids: string[] = []) {
    this.rawData = rawData;
    this.order_info_map = planOrderInfo;
    this.factory_code = rawData.factory_code;
    this.factory_name = rawData.factory_name;
    this.production_line_no = rawData.production_line_no;
    this.production_line_name = rawData.production_line_name;
    this.lock_user_id = rawData.lock_user_id;
    this.lock_user_name = rawData.lock_user_name;
    this.rest_days = rawData.rest_days ?? [];
    this.isHover = false;
    this.isSelected = false;
    this.line_orders = [];
    rawData.view_list.forEach((item) => {
      const _order_info = planOrderInfo[item.order_uuid];
      this.line_orders.push({
        ...item,
        ..._order_info,
        isSelected: false,
        isDisabled: !_order_info.can_edit,
        isHover: false,
        isHeightLight: order_uuids.includes(item.order_uuid),
        factory_code: rawData.factory_code,
        production_line_no: rawData.production_line_no,
        parentProductionLine: this,
        production_line_name: rawData.production_line_name,
      });
    });
    this.isHeightLight = false;
    this.isDisabled = false;
  }
  // get isSelected(): boolean {
  //   return !!this.line_orders.length && this.line_orders.every((item) => item.isSelected);
  // }
  // set isSelected(value: boolean) {
  //   this.line_orders.forEach((item) => (item.isSelected = value));
  // }
  toJSON() {
    return {
      rawData: this.rawData,
      order_info_map: this.order_info_map,
    };
  }
}

export interface LineOrderInterface extends PlanOrderViewItem, PlanOrderInfo {
  parentProductionLine: ProductionPlanLineItem;
  production_line_no: string;
  production_line_name: string;
  factory_code: string;
  isHover: boolean;
  isHeightLight: boolean;
  isSelected: boolean;
  isDisabled: boolean;
  graphItem?: PlanGraphItem;
  can_edit: boolean;
}

// 保存发布传参
export interface PublishParams {
  order_codes: string[];
  production_line_nos: string[];
  group_ids: string[];
  temp_session: string;
}

export type Nullable<T> = {
  [P in keyof T]: T[P] | null;
};
