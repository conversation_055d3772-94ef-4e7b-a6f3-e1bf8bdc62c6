/*
 * File: employee.service.ts
 * Project: elan-web
 * File Created: Wednesday, 24th November 2021 5:47:08 pm
 * Author: liucp
 * Description:
 * -----
 * Last Modified: Wednesday, 8th December 2021 11:54:27 am
 * Modified By: liucp
 */

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class EmployeeService {
  constructor(private http: HttpClient) {}

  /**
   * 获取所有员工
   * @param payload
   */
  getAllemployee(payload: any): Observable<any> {
    return this.http.post<any>('/employee/list', payload);
  }

  /**
   * 获取搜索下拉数据
   */
  getOptionLists(payload: any): Observable<any> {
    return this.http.post<any>('/employee/search', payload);
  }
}
