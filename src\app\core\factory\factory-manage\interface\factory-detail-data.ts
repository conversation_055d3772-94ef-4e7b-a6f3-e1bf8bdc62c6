import { Region } from './factory-list-data';

export interface FactoryDetailData {
  id?: number;
  code: string;
  name: string;
  abbr: string;
  status: number;
  domain: string;
  initial_link: string;
  coop: string;
  scale: number;
  sewing_workers: number;
  category: string;
  customer: string;
  contacts: string;
  tel: string;
  email: string;
  region: Region;
  address: string;
  factory_type: number;
  deep_flow_enable: boolean;
  product_id: number;
  metadata: MetaData;
  region_model?: []; // 地区级联
  reason?: string;
  modified_time?: string;
  deploy_region: string;
  deploy_region_name: string;
  elan_code: string;
}

export interface MetaData {
  changelog: any[]; // 与上次提交变化的修改变更
  lifecycle: Lifecycle;
}

export interface Lifecycle {
  name: string;
  gen_time: string;
  status: string;
}
