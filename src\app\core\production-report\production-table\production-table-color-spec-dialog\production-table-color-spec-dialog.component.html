<div id="mainBoard">
  <div class="orderInfo">
    <ng-container *ngFor="let cf of basicInfoConfig; last as last">
      <div class="orderItem">
        <span>{{ translateName + cf?.label | translate }}:</span>{{ orderInfo?.[cf.key] }}
      </div>
      <nz-divider *ngIf="!last" nzType="vertical"></nz-divider>
    </ng-container>
  </div>

  <div class="timeArea" *ngFor="let time of displayNodeList; let index = index">
    <div nz-row style="align-items: center; padding: 0 8px">
      <div>
        {{ translateName + '日期' | translate }}:
        <nz-date-picker [(ngModel)]="time.finished_time" [nzPlaceHolder]="'placeholder.select' | translate"></nz-date-picker>
        <button nz-button nzSize="small" nzShape="circle" nzDanger style="margin: 0 4px" (click)="removeTime(index)">
          <i nz-icon nzType="minus" nzTheme="outline"></i>
        </button>
        <button nz-button nzSize="small" nzShape="circle" nzType="primary" (click)="addTime(index)">
          <i nz-icon nzType="plus" nzTheme="outline"></i>
        </button>
      </div>
      <div nz-col nzSpan="12" style="margin-left: 8px">
        {{ translateName + '总数量' | translate }}:
        <nz-input-number
          [(ngModel)]="time.shadowTotalQty"
          [nzMin]="0"
          [nzPrecision]="0"
          [nzPlaceHolder]="'placeholder.input' | translate"
          (ngModelChange)="shadowTotalQtyChange(time, $event)"></nz-input-number>
      </div>
    </div>
    <flc-color-size-table
      *ngIf="!time.isSingleLine"
      #colorSizeTable
      [dataList]="time.raw_line_list"
      dataVersion="v2"
      [mode]="'editCell'"
      (onChange)="tableChange(time, $event)">
    </flc-color-size-table>
    <flc-file-gallery
      style="display: flex"
      #fileGallery
      [galleryType]="'image'"
      [wrap]="false"
      [fileList]="time.pictures"
      [addBtnText]="'common.添加图片' | translate"
      (onUploaded)="onUploadImage(time, $event)"
      (onDeleted)="onDeleteImage(time, $event)"
      [isEditMode]="true"
      [needWatermark]="true"
      [fileMaxCount]="10"
      [fileMaxSize]="100">
    </flc-file-gallery>
  </div>
  <nz-divider></nz-divider>
  <div class="footer">
    <div class="totalArea">
      {{ translateName + '实际数量' | translate }}: <span> {{ TotalQty }}</span>
    </div>
    <button nz-button nzType="default" style="margin-right: 4px" (click)="cancel()">{{ 'btn.cancel' | translate }}</button>
    <button nz-button nzType="primary" (flcClickStop)="save()">{{ 'btn.save' | translate }}</button>
  </div>
</div>
