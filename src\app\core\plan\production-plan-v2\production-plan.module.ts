import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { OverlayModule } from '@angular/cdk/overlay';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ColorPickerModule } from '@iplab/ngx-color-picker';
// nz Module
import { NzSliderModule } from 'ng-zorro-antd/slider';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzMessageModule } from 'ng-zorro-antd/message';
import { NzNotificationModule } from 'ng-zorro-antd/notification';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzProgressModule } from 'ng-zorro-antd/progress';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTimelineModule } from 'ng-zorro-antd/timeline';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzResizableModule } from 'ng-zorro-antd/resizable';
import { NzBadgeModule } from 'ng-zorro-antd/badge';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
// fl module
import { FlButtonModule } from 'fl-ui-angular';
import { FlcComponentsModule, FlcDirectivesModule, FlcDrawerHelperService, FlCommonLibModule, FlcPipesModule } from 'fl-common-lib';
// self components
import { ProductionPlanV2Service } from './production-plan-v2.service';
import { ProductionPlanV2ListComponent } from './list/production-plan-v2-list.component';
import { ProductionPlanShareService } from './production-plan-share.service';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import { HttpClient } from '@angular/common/http';
import { PlanListSearchComponent } from './components/plan-list-search/plan-list-search.component';
import { OperateButtonComponent } from './components/operate-button/operate-button.component';
import { PlanListHeaderComponent } from './components/plan-list-header/plan-list-header.component';
import { GraphResizerComponent } from './components/graph-resizer/graph-resizer.component';
import { OrderAllocationListComponent } from './components/order-allocation-list/order-allocation-list.component';
import { OrderAllocationModalComponent } from './components/order-allocation-modal/order-allocation-modal.component';
import { PlanProductionLineViewComponent } from './components/plan-production-line-view/plan-production-line-view.component';
import { GraphTableComponent } from './components/graph-table/graph-table.component';
import { ProductionLineViewGraphComponent } from './components/plan-production-line-view/production-line-view-graph/production-line-view-graph.component';
import { SewingItemDrawerComponent } from './components/sewing-item-drawer/sewing-item-drawer.component';
import { ProductionLineGraphWrapComponent } from './components/plan-production-line-view/production-line-graph-wrap/production-line-graph-wrap.component';
import { ProductionLineWrapComponent } from './components/plan-production-line-view/production-line-wrap/production-line-wrap.component';
import { MaterialGraphItemComponent } from './components/plan-production-line-view/production-line-graph-wrap/material-graph-item/material-graph-item.component';
import { DragItemDirective } from './components/drag-item.directive';
import { ProductionLineViewDetailComponent } from './components/plan-production-line-view/production-line-view-graph/production-line-view-detail/production-line-view-detail.component';
import { NzPaginationModule } from 'ng-zorro-antd/pagination';
import { NzTreeSelectModule } from 'ng-zorro-antd/tree-select';
import { DropContainerDirective } from './components/drop-container.directive';
import { ProductionReplaceModalComponent } from './components/plan-production-line-view/production-replace-modal/production-replace-modal.component';
import { MessageNotificationModalComponent } from './components/plan-production-line-view/message-notification-modal/message-notification-modal.component';
import { MessageNotificationDrawerComponent } from './components/plan-production-line-view/message-notification-drawer/message-notification-drawer.component';

const components = [
  PlanListSearchComponent,
  ProductionPlanV2ListComponent,
  PlanListHeaderComponent,
  OperateButtonComponent,
  GraphResizerComponent,
  OrderAllocationListComponent,
  OrderAllocationModalComponent,
  PlanProductionLineViewComponent,
  ProductionLineViewGraphComponent,
  GraphTableComponent,
  SewingItemDrawerComponent,
  ProductionLineGraphWrapComponent,
  ProductionLineWrapComponent,
  MaterialGraphItemComponent,
  ProductionLineViewDetailComponent,
  ProductionReplaceModalComponent,
];

const nzModules = [
  NzModalModule,
  NzTableModule,
  NzIconModule,
  NzButtonModule,
  NzSpinModule,
  NzTabsModule,
  NzSelectModule,
  NzInputNumberModule,
  NzInputModule,
  NzDatePickerModule,
  NzModalModule,
  NzMessageModule,
  NzNotificationModule,
  NzCheckboxModule,
  NzDividerModule,
  NzDrawerModule,
  NzFormModule,
  NzPopconfirmModule,
  NzRadioModule,
  NzProgressModule,
  NzTimelineModule,
  NzEmptyModule,
  NzToolTipModule,
  NzPopoverModule,
  NzSliderModule,
  NzBadgeModule,
  NzSwitchModule,
  NzResizableModule,
  NzPaginationModule,
  NzTreeSelectModule,
];

@NgModule({
  declarations: [
    ...components,
    DragItemDirective,
    DropContainerDirective,
    MessageNotificationModalComponent,
    MessageNotificationDrawerComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    DragDropModule,
    ScrollingModule,
    OverlayModule,
    FlButtonModule,
    ColorPickerModule,
    FlCommonLibModule,
    FlcComponentsModule,
    FlcDirectivesModule,
    FlcPipesModule,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new MultiTranslateHttpLoader(http, [{ prefix: './assets/i18n/elan-components/', suffix: '.json' }]);
        },
        deps: [HttpClient],
      },
    }),
    ...nzModules,
  ],
  providers: [ProductionPlanV2Service, ProductionPlanShareService, FlcDrawerHelperService],
})
export class ProductionPlanV2Module {
  constructor(public translateService: TranslateService) {
    const cl = translateService.currentLang;
    translateService.currentLang = '';
    translateService.use(cl);
  }
}
