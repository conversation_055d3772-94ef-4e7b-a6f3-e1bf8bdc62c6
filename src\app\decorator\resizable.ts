import { Directive, OnDestroy, OnInit } from '@angular/core';
import { fromEvent, Observable, Subscription } from 'rxjs';

/*
 ** 默认调用页面定义的resize()
 ** 在 ngOnInit()    中调用 this.addResizeListener();
 ** 在 ngOnDestroy() 中调用 this.removeResizeListener(); ！！！
 */
// export function resizable(resizeFn = 'resize') {
//   return function (target: any) {
//     target.prototype.addResizeListener = function () {
//       target.prototype._resizeObservable = fromEvent(window, 'resize');
//       target.prototype._resizeSubscription = target.prototype._resizeObservable.subscribe((evt: any) => {
//         target.prototype[resizeFn].apply(this);
//       });
//     };
//     target.prototype.removeResizeListener = function () {
//       target.prototype._resizeSubscription.unsubscribe();
//       target.prototype._resizeObservable = null;
//     };
//   };
// }
@Directive({})
export abstract class Resizable implements OnInit, OnDestroy {
  _resizeObservable?: Observable<Event>;
  _resizeSubscription?: Subscription;
  abstract resize(): void;
  constructor() {}
  ngOnInit() {
    this.addResizeListener();
  }
  ngOnDestroy() {
    this.removeResizeListener();
  }
  addResizeListener() {
    this._resizeObservable = fromEvent(window, 'resize');
    this._resizeSubscription = this._resizeObservable.subscribe(() => {
      this.resize();
    });
  }
  removeResizeListener() {
    this._resizeSubscription?.unsubscribe();
    this._resizeObservable = undefined;
  }
}

// declare interface ResizeFunction {
//   resize(): void;
// }
