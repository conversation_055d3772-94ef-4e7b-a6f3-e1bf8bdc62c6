{"assign.role": {"权限名称": "Permission Name", "输入权限名称搜索": "Enter Permission Name to Search", "暂无数据,请添加权限": "No Data Available, Please Add a Permission", "加载中...": "Loading...", "输入部门或员工搜索": "Enter Department or Employee to Search", "暂无部门与人员哟～": "No Departments or Employees Available～", "暂无员工哟～": "No Employees Available～", "直属员工": "Direct employees", "人": "Person(s)", "无操作帐号, 不可分配权限": "No Operation Account, <PERSON><PERSON> Assign Permissions", "去配置账号": "Go Configure Account", "确定删除该权限吗？": "Are you sure you want to delete this permission?", "该权限名称有部门和员工被分配，删后不可用": "This permission name has been assigned to departments and employees, and will be unavailable after deletion", "确定移除": "Are you sure you want to remove ", "该权限已存在": "This permission already exists", "该员工没有操作帐号，不可分配权限": "This employee does not have an operation account and cannot be assigned permissions", "该部门下没有员工, 无法添加权限": "There are no employees under this department, cannot add permissions", "该部门下所有员工均未配置账户, 无法添加权限": "All employees under this department have not configured accounts, cannot add permissions", "功能权限": "Functional Permissions", "管理范围": "<PERSON>ope of Management", "只读权限": "Read-Only Permission", "编辑权限": "Edit Permission", "查看者": "Viewer", "编辑者": "Editor", "全部数据": "All Data", "仅本人": "Only Myself", "仅本部门及下级部门": "Only This Department and Subordinate Departments", "自定义部门": "Custom Departments"}}