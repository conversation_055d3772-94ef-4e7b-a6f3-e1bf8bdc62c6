<flc-app-header [showTemplate]="true" [headerTopTitle]="headerTopTitleTpl" [headerBtn]="headerBtnTmpl"></flc-app-header>
<ng-template #headerTopTitleTpl>
  <!-- 详情按钮开始 -->
  <div class="head-title">
    {{ translateName + headerTopTitle | translate }}
    <span class="source-tag">
      {{ translateName + '来源' | translate }}：{{
        sourceTypeEnum[detail?.io_basic?.source] ? (translateName + sourceTypeEnum[detail?.io_basic?.source] | translate) : ''
      }}</span
    >
    <span *ngIf="detail?.io_basic?.order_status" class="status" [ngClass]="'status' + detail?.io_basic?.order_status">
      {{ detail?.io_basic?.order_status_value }}
    </span>
  </div>
</ng-template>
<ng-template #headerBtnTmpl>
  <!-- <div class="sort-btn-box">
    <div></div>
    <div></div>
  </div> -->
  <div class="btn-box">
    <button *ngIf="!edit" nz-button flButton="pretty-primary" [nzShape]="'round'" (click)="printData()">
      {{ 'flss.btn.print' | translate }}
    </button>
    <button *ngIf="!edit" nz-button flButton="default" [nzShape]="'round'" (click)="back()">
      {{ 'btn.back' | translate }}
    </button>
    <button *ngIf="edit" nz-button flButton="default" [nzShape]="'round'" (click)="cancel()">
      {{ 'btn.cancel' | translate }}
    </button>
    <button
      *ngIf="_service.btnArr.includes('order:bulk-print') && !edit"
      nz-button
      flButton="primary"
      [nzShape]="'round'"
      nz-popover
      [nzPopoverContent]="popoverContentTemplate"
      nzPopoverTrigger="hover"
      nzPopoverPlacement="bottom">
      {{ translateName + '打印唯一码' | translate }}
    </button>
    <ng-template #popoverContentTemplate>
      <div class="tag-list">
        <span (click)="onTagPrint(1)">{{ translateName + '标签全打' | translate }}</span>
        <span (click)="onTagPrint(2)">{{ translateName + '标签补打' | translate }}</span>
      </div>
    </ng-template>
    <button
      *ngIf="
        edit && ([statusEnum.toSubmit, statusEnum.toAudit, statusEnum.toModify].includes(detail?.io_basic?.order_status) || id === 'new')
      "
      nz-button
      flButton="pretty-minor"
      [nzShape]="'round'"
      [flcDisableOnClick]="1000"
      (click)="save()">
      <i nz-icon [nzIconfont]="'icon-baocun'"></i>{{ 'btn.save' | translate }}
    </button>
    <button *ngIf="edit" nz-button flButton="pretty-primary" [nzShape]="'round'" (click)="commit()" [flcDisableOnClick]="1000">
      <i nz-icon [nzIconfont]="'icon-tijiao'"></i>{{ 'btn.commit' | translate }}
    </button>
    <button
      *ngIf="
        _service.btnArr.includes('order:bulk-approval') &&
        [statusEnum.toAudit, statusEnum.toModifyAudit].includes(detail?.io_basic?.order_status)
      "
      nz-button
      flButton="fault-minor"
      [nzShape]="'round'"
      (click)="modify()"
      [flcDisableOnClick]="1000">
      {{ 'common.退回修改' | translate }}
    </button>
    <button
      *ngIf="
        _service.btnArr.includes('order:bulk-cancel') &&
        !edit &&
        detail?.io_basic?.source !== 4 &&
        [statusEnum.toSubmit, statusEnum.auditPass, statusEnum.toModify, statusEnum.modifyAuditReturn].includes(
          detail?.io_basic?.order_status
        )
      "
      nz-button
      flButton="default"
      [nzShape]="'round'"
      (click)="cancelOrder()">
      {{ translateName + '取消订单' | translate }}
    </button>

    <button
      *ngIf="
        _service.btnArr.includes('order:bulk-delete') &&
        !edit &&
        detail?.io_basic?.source !== 4 &&
        [statusEnum.toSubmit].includes(detail?.io_basic?.order_status)
      "
      nz-button
      flButton="default"
      [nzShape]="'round'"
      (click)="deleteOrder()">
      {{ translateName + '删除' | translate }}
    </button>

    <button
      *ngIf="
        _service.btnArr.includes('order:bulk-approval') &&
        [statusEnum.toAudit, statusEnum.toModifyAudit].includes(detail?.io_basic?.order_status)
      "
      nz-button
      flButton="pretty-primary"
      [nzShape]="'round'"
      (click)="pass()">
      {{ 'flss.btn.pass' | translate }}
    </button>

    <button
      *ngIf="_service.btnArr.includes('order:bulk-approval') && [statusEnum.auditPass].includes(detail?.io_basic?.order_status)"
      nz-button
      flButton="pretty-primary"
      [nzShape]="'round'"
      (click)="handlePrePayment()">
      {{ 'bulk.预收款' | translate }}
    </button>
    <button
      *ngIf="
        _service.btnArr.includes('order:bulk-update') &&
        !edit &&
        [statusEnum.toModify, statusEnum.toSubmit, statusEnum.auditPass, statusEnum.modifyAuditReturn].includes(
          detail?.io_basic?.order_status
        )
      "
      nz-button
      flButton="pretty-primary"
      [nzShape]="'round'"
      (click)="editDetail()"
      [flcDisableOnClick]="1000">
      <i nz-icon [nzIconfont]="'icon-caozuolan_bianji1'"></i>{{ 'btn.edit' | translate }}
    </button>
  </div>
</ng-template>

<!-- 详情按钮结束 -->
<nz-spin [nzSpinning]="loading">
  <div class="bulk-detail-box">
    <div class="reason-box" *ngIf="[statusEnum.toModify, statusEnum.modifyAuditReturn].includes(detail?.io_basic?.order_status)">
      <i nz-icon [nzIconfont]="'icon-jinggao'"></i>
      <div class="reason-wrap">
        <flc-text-truncated [template]="reasonBox" [tooltipOverlayClassName]="'tool-box'"></flc-text-truncated>
        <ng-template #reasonBox>
          {{ detail?.io_basic?.reason }}
        </ng-template>
      </div>
    </div>
    <!-- 审核通过二次工艺和成衣加工进度开始 -->
    <div class="pass-process" *ngIf="detail?.io_basic?.order_status === statusEnum.auditPass">
      <div class="process-first">
        <div class="process-item" [ngClass]="prodData?.cutting_qty > 0 ? 'item-actived' : ''">
          <div class="first-item">
            <span>{{ translateName + '裁剪' | translate }}</span
            ><span class="item-sum" style="flex: 8">{{ translateName + '总裁数' | translate }}</span>
          </div>
          <div class="first-desc">
            <span class="process-num">
              <flc-text-truncated
                [data]="'(' + ((prodData?.cutting_qty / prodData?.order_count | percent: '1.0-1') ?? '0%') + ')'"></flc-text-truncated>
            </span>
            <span>
              <flc-text-truncated [data]="prodData?.cutting_qty | number"></flc-text-truncated>
            </span>
          </div>
        </div>
        <div class="process-item" [ngClass]="prodData?.sewing_qty > 0 ? 'item-actived' : ''">
          <div class="first-item">
            <span>{{ translateName + '车缝' | translate }}</span
            ><span class="item-sum">{{ translateName + '完成数' | translate }}</span>
          </div>
          <div class="first-desc">
            <span class="process-num">
              <flc-text-truncated
                [data]="'(' + ((prodData?.sewing_qty / prodData?.order_count | percent: '1.0-1') ?? '0%') + ')'"></flc-text-truncated>
            </span>
            <span>
              <flc-text-truncated [data]="prodData?.sewing_qty | number"></flc-text-truncated>
            </span>
          </div>
        </div>
        <div class="process-item" [ngClass]="prodData?.qualified_qty > 0 ? 'item-actived' : ''">
          <div class="first-item">
            <span>{{ translateName + '质检' | translate }}</span
            ><span class="item-sum">{{ translateName + '合格数' | translate }}</span>
          </div>
          <div class="first-desc">
            <span class="process-num">
              <flc-text-truncated
                [data]="'(' + ((prodData?.qualified_qty / prodData?.order_count | percent: '1.0-1') ?? '0%') + ')'"></flc-text-truncated>
            </span>
            <span>
              <flc-text-truncated [data]="prodData?.qualified_qty | number"></flc-text-truncated>
            </span>
          </div>
        </div>
        <div class="process-item" [ngClass]="prodData?.transport_qty > 0 ? 'item-actived' : ''">
          <div class="first-item">
            <span>{{ translateName + '走货' | translate }}</span
            ><span class="item-sum">{{ translateName + '走货数' | translate }}</span>
          </div>
          <div class="first-desc">
            <span class="process-num">
              <flc-text-truncated
                [data]="'(' + ((prodData?.transport_qty / prodData?.order_count | percent: '1.0-1') ?? '0%') + ')'"></flc-text-truncated>
            </span>
            <span>
              <flc-text-truncated [data]="prodData?.transport_qty | number"></flc-text-truncated>
            </span>
          </div>
        </div>
        <div class="process-title">{{ translateName + '成衣加工' | translate }}</div>
      </div>
      <div class="process-first process-second" *ngIf="outData?.list.length">
        <div class="second-top-box">
          <div style="overflow: hidden; margin-left: 98px">
            <div class="process-item" [ngClass]="outData?.cutCount ? 'item-actived' : ''">
              <div class="first-item">
                <span>{{ translateName + '裁剪' | translate }}</span
                ><span class="item-sum" style="flex: 9">{{ translateName + '总裁数(裁片)' | translate }}</span>
              </div>
            </div>
            <div class="process-item" [ngClass]="outData?.outsourceQty ? 'item-actived' : ''">
              <div class="first-item">
                <span>{{ translateName + '外发' | translate }}</span
                ><span class="item-sum">{{ translateName + '外发数' | translate }}</span>
              </div>
            </div>
            <div class="process-item" [ngClass]="outData?.receivedQty ? 'item-actived' : ''">
              <div class="first-item">
                <span>{{ translateName + '收货' | translate }}</span
                ><span class="item-sum">{{ translateName + '收货数' | translate }}</span>
              </div>
            </div>
            <div class="process-item" [ngClass]="outData?.finishedQty ? 'item-actived' : ''">
              <div class="first-item">
                <span>{{ translateName + '加工' | translate }}</span
                ><span class="item-sum">{{ translateName + '加工数' | translate }}</span>
              </div>
            </div>
            <div class="process-item" [ngClass]="outData?.qualifiedQty ? 'item-actived' : ''">
              <div class="first-item">
                <span>{{ translateName + '质检' | translate }}</span
                ><span class="item-sum">{{ translateName + '合格数' | translate }}</span>
              </div>
            </div>
            <div class="process-item" [ngClass]="outData?.sentQty ? 'item-actived' : ''">
              <div class="first-item">
                <span>{{ translateName + '走货' | translate }}</span
                ><span class="item-sum">{{ translateName + '已发货' | translate }}</span>
              </div>
            </div>
          </div>
          <div *ngFor="let item of outData?.list" class="bulk-out-box">
            <div class="sencond-title">
              <span>{{ item.extra_process_name }}</span>
            </div>
            <div class="process-item-num" [ngClass]="item.cut_count > 0 ? 'item-actived' : ''">
              <div class="first-desc">
                <span class="process-num"><flc-text-truncated [data]="'(' + item.cut_progress + ')'"></flc-text-truncated></span
                ><span><flc-text-truncated [data]="item.cut_count | number"></flc-text-truncated></span>
              </div>
            </div>
            <div class="process-item-num" [ngClass]="item.outsource_qty > 0 ? 'item-actived' : ''">
              <div class="first-desc">
                <span class="process-num"><flc-text-truncated [data]="'(' + item.outsource_progress + ')'"></flc-text-truncated></span
                ><span><flc-text-truncated [data]="item.outsource_qty | number"></flc-text-truncated></span>
              </div>
            </div>
            <div class="process-item-num" [ngClass]="item.received_qty > 0 ? 'item-actived' : ''">
              <div class="first-desc">
                <span class="process-num"><flc-text-truncated [data]="'(' + item.received_progress + ')'"></flc-text-truncated></span
                ><span><flc-text-truncated [data]="item.received_qty | number"></flc-text-truncated></span>
              </div>
            </div>
            <div class="process-item-num" [ngClass]="item.finished_qty > 0 ? 'item-actived' : ''">
              <div class="first-desc">
                <span class="process-num"><flc-text-truncated [data]="'(' + item.finished_progress + ')'"></flc-text-truncated></span
                ><span><flc-text-truncated [data]="item.finished_qty | number"></flc-text-truncated></span>
              </div>
            </div>
            <div class="process-item-num" [ngClass]="item.qualified_qty > 0 ? 'item-actived' : ''">
              <div class="first-desc">
                <span class="process-num"><flc-text-truncated [data]="'(' + item.qualified_progress + ')'"></flc-text-truncated></span
                ><span><flc-text-truncated [data]="item.qualified_qty | number"></flc-text-truncated></span>
              </div>
            </div>
            <div class="process-item-num" [ngClass]="item.sent_qty > 0 ? 'item-actived' : ''">
              <div class="first-desc">
                <span class="process-num"><flc-text-truncated [data]="'(' + item.sent_progress + ')'"></flc-text-truncated></span
                ><span><flc-text-truncated [data]="item.sent_qty | number"></flc-text-truncated></span>
              </div>
            </div>
          </div>

          <div class="process-title">{{ translateName + '二次工艺' | translate }}</div>
        </div>
      </div>
    </div>
    <!-- 审核通过二次工艺和成衣加工进度结束 -->

    <!-- 接单信息开始 -->
    <div class="basic-box" #basicBox [hidden]="!showTop" *ngIf="history_id">
      <div class="block-title">
        <span>{{ translateName + '接单信息' | translate }}</span>
        <span (click)="isFold = !isFold" class="fold-btn">
          {{ 'flss.btn.' + (isFold ? '展开' : '收起') | translate }}
          <span nz-icon nzType="double-right" nzTheme="outline" [nzRotate]="isFold ? 270 : 90"></span>
        </span>
      </div>
      <app-accept-info [hidden]="isFold"></app-accept-info>
    </div>
    <!-- 接单信息结束 -->

    <form
      nz-form
      *ngIf="bulkForm"
      [formGroup]="bulkForm"
      [ngStyle]="{
        'margin-top': [statusEnum.toModify, statusEnum.modifyAuditReturn].includes(detail?.io_basic?.order_status) ? '-8px' : ''
      }">
      <!-- 基本信息开始 -->
      <div class="basic-box" #basicBox [hidden]="!showTop">
        <div class="block-title base-info-selector">
          {{ 'common.基本信息' | translate }}

          <button nz-button flButton="pretty-minor" (click)="onOpenDrawerCustom()">
            <i nz-icon [nzIconfont]="'icon-xitongpeizhi'"></i>
          </button>
        </div>
        <div class="basic-desc" nz-row>
          <div class="desc-left" nz-col [nzSpan]="order_picture_show ? 19 : 24">
            <div nz-row>
              <ng-container *ngFor="let item of basicList">
                <div nz-col [nzSpan]="item.colSpan" *ngIf="item.visible && item.code !== 'order_pictures'">
                  <nz-form-item *ngIf="edit; else onlyRead">
                    <nz-form-label [nzRequired]="item.required" [nzFlex]="lang === 'en' ? item?.flexWidth || '180px' : '150px'"
                      >{{ translateName + item.label | translate }}
                      <i
                        *ngIf="item.code === 'fabric'"
                        nz-icon
                        nzType="question-circle"
                        nzTheme="outline"
                        nz-tooltip
                        [nzTooltipTitle]="translateName + '面料提示' | translate"></i>
                    </nz-form-label>
                    <nz-form-control [flcErrorTip]="translateName + item.label | translate">
                      <ng-container *ngIf="item.type === 'input'">
                        <nz-input-group [nzSuffix]="(bulkForm.get(item.code)?.value?.length || 0) + '/' + item.maxLength">
                          <input
                            [placeHolder]="'flss.placeholder.input' | translate"
                            [formControlName]="item.code"
                            [maxLength]="item.maxLength" />
                        </nz-input-group>
                      </ng-container>
                      <ng-container *ngIf="item.type === 'number-input'">
                        <nz-input-group [nzAddOnAfter]="item.suffix">
                          <nz-input-number
                            style="width: 100%"
                            [formControlName]="item.code"
                            flcStringifyFmCtrlValue
                            [nzMin]="0"
                            [nzMax]="999999999.99999"
                            [nzStep]="1"
                            [nzPrecision]="item.precision ?? 5"
                            [nzPlaceHolder]="'flss.placeholder.input' | translate"
                            (ngModelChange)="numberInputChange($event, item)">
                          </nz-input-number>
                        </nz-input-group>
                      </ng-container>
                      <ng-container *ngIf="item.type === 'textarea'">
                        <nz-textarea-count [nzMaxCharacterCount]="item.maxLength" class="inline-count">
                          <textarea
                            [nzAutosize]="{ minRows: 0, maxRows: 3 }"
                            nz-input
                            flcInputTrim
                            [placeholder]="'flss.placeholder.input' | translate"
                            [formControlName]="item.code"
                            [maxLength]="item.maxLength"></textarea>
                        </nz-textarea-count>
                      </ng-container>
                      <ng-container *ngIf="item.type === 'cascader'">
                        <nz-cascader
                          [nzPlaceHolder]="'flss.placeholder.select' | translate"
                          [nzOptions]="styleList"
                          [nzLabelProperty]="'label'"
                          [nzValueProperty]="'label'"
                          [formControlName]="item.code"
                          (nzSelectionChange)="styleChange($event)">
                        </nz-cascader>
                      </ng-container>
                      <ng-container *ngIf="item.type === 'date'">
                        <nz-date-picker [formControlName]="item.code" [nzFormat]="'yyyy/MM/dd'" style="width: 100%"></nz-date-picker>
                      </ng-container>
                      <ng-container *ngIf="item.type === 'multipleSelect'">
                        <nz-select
                          [nzMode]="'multiple'"
                          [nzPlaceHolder]="'flss.placeholder.select' | translate"
                          [nzSuffixIcon]="iconFF"
                          [formControlName]="item.code"
                          (ngModelChange)="changeExtra($event)">
                          <nz-option
                            class="option-item"
                            *ngFor="let item of extraList"
                            [nzValue]="item.value"
                            [nzLabel]="item.label"
                            [nzDisabled]="item?.disabled">
                          </nz-option>
                        </nz-select>
                        <ng-template #iconFF>
                          <i nz-icon [nzIconfont]="'icon-baocun'"></i>
                        </ng-template>
                      </ng-container>
                      <ng-container *ngIf="item.type === 'select'">
                        <nz-select
                          *ngIf="item.code === 'order_category'"
                          [nzPlaceHolder]="'flss.placeholder.select' | translate"
                          [formControlName]="item.code">
                          <nz-option
                            *ngFor="let item of order_outgoing_options"
                            [nzValue]="item.value"
                            [nzLabel]="translateName + item.label | translate">
                          </nz-option>
                        </nz-select>
                        <nz-select
                          *ngIf="item.code === 'order_production_type'"
                          [nzPlaceHolder]="'flss.placeholder.select' | translate"
                          [formControlName]="item.code"
                          (ngModelChange)="orderProductionTypeChange($event)">
                          <nz-option
                            *ngFor="let item of order_production_options"
                            [nzValue]="item.value"
                            [nzLabel]="translateName + item.label | translate">
                          </nz-option>
                        </nz-select>
                        <nz-select
                          *ngIf="item.code === 'brand_id'"
                          [nzPlaceHolder]="'flss.placeholder.select' | translate"
                          [formControlName]="item.code"
                          nzShowSearch>
                          <nz-option *ngFor="let item of brandList" [nzValue]="item.value" [nzLabel]="item.label"> </nz-option>
                        </nz-select>
                        <nz-select
                          *ngIf="item.code === 'sale_channel_id'"
                          [nzPlaceHolder]="'flss.placeholder.select' | translate"
                          [formControlName]="item.code">
                          <nz-option *ngFor="let item of sale_channel_options" [nzValue]="item.value" [nzLabel]="item.label"> </nz-option>
                        </nz-select>
                        <nz-select
                          *ngIf="item.code === 'quality_level'"
                          [nzPlaceHolder]="'flss.placeholder.select' | translate"
                          [formControlName]="item.code">
                          <nz-option
                            *ngFor="let item of quality_level_options"
                            [nzValue]="item.value"
                            [nzLabel]="translateName + item.label | translate">
                          </nz-option>
                        </nz-select>
                        <nz-select
                          *ngIf="item.code === 'payment_id'"
                          [nzPlaceHolder]="'flss.placeholder.select' | translate"
                          [formControlName]="item.code"
                          (ngModelChange)="paymentMethodChange($event)">
                          <nz-option *ngFor="let item of payment_id_options" [nzValue]="item.value" [nzLabel]="item.label"> </nz-option>
                        </nz-select>
                        <nz-select
                          *ngIf="item.code === 'currency_id'"
                          [nzPlaceHolder]="'flss.placeholder.select' | translate"
                          [formControlName]="item.code"
                          (ngModelChange)="currencyChange($event)">
                          <nz-option
                            *ngFor="let item of currency_id_options"
                            [nzValue]="item.value"
                            [nzLabel]="translateName + item.label | translate">
                          </nz-option>
                        </nz-select>
                        <nz-select
                          *ngIf="item.code === 'style_code'"
                          nzShowSearch
                          [formControlName]="item.code"
                          (ngModelChange)="styleCodeBlur()"
                          [nzPlaceHolder]="'flss.placeholder.select' | translate">
                          <nz-option *ngFor="let item of styleCodeList" [nzValue]="item?.label" [nzLabel]="item?.label"> </nz-option>
                        </nz-select>
                        <nz-select
                          *ngIf="item.code === 'order_classification'"
                          [nzPlaceHolder]="'flss.placeholder.select' | translate"
                          [formControlName]="item.code"
                          nzAllowClear>
                          <nz-option *ngFor="let item of orderClassificationList" [nzValue]="item.label" [nzLabel]="item.label">
                          </nz-option>
                        </nz-select>
                        <nz-select
                          *ngIf="item.code === 'payment_condition'"
                          [nzPlaceHolder]="'flss.placeholder.select' | translate"
                          [formControlName]="item.code"
                          [nzMode]="'multiple'">
                          <nz-option
                            *ngFor="let item of payment_condition_options"
                            [nzValue]="item.value"
                            [nzLabel]="translateName + item.label | translate">
                          </nz-option>
                        </nz-select>
                        <nz-select
                          *ngIf="item.code === 'fanshi'"
                          [nzPlaceHolder]="'flss.placeholder.select' | translate"
                          [formControlName]="item.code"
                          [nzMode]="'multiple'">
                          <nz-option
                            *ngFor="let item of payment_condition_options"
                            [nzValue]="item.value"
                            [nzLabel]="translateName + item.label | translate">
                          </nz-option>
                        </nz-select>
                        <nz-select
                          *ngIf="item.code === 'bizhong'"
                          [nzPlaceHolder]="'flss.placeholder.select' | translate"
                          [formControlName]="item.code"
                          [nzMode]="'multiple'">
                          <nz-option
                            *ngFor="let item of payment_condition_options"
                            [nzValue]="item.value"
                            [nzLabel]="translateName + item.label | translate">
                          </nz-option>
                        </nz-select>
                        <nz-select
                          *ngIf="item.code === 'order_labels'"
                          [nzPlaceHolder]="'flss.placeholder.select' | translate"
                          [formControlName]="item.code"
                          [nzMode]="'multiple'">
                          <nz-option
                            *ngFor="let item of selectOptions?.[item?.optionKey || item?.code]"
                            [nzValue]="item.label"
                            [nzLabel]="item.label">
                          </nz-option>
                        </nz-select>

                        <ng-container *ngIf="item.code === 'is_use_plan'">
                          <nz-radio-group [formControlName]="item.code">
                            <label nz-radio [nzValue]="true">{{ 'common.是' | translate }}</label>
                            <label nz-radio [nzValue]="false">{{ 'common.否' | translate }}</label>
                          </nz-radio-group>
                        </ng-container>

                        <!--  存在加工厂切第一次审核通过，则不可修改，没选加工厂，可以修改， 审核之后则不可修改 -->
                        <nz-select
                          *ngIf="item.code === 'process_factory_code'"
                          [nzPlaceHolder]="'flss.placeholder.select' | translate"
                          nzAllowClear
                          nzShowSearch
                          [formControlName]="item.code"
                          (nzOpenChange)="openProcessFactory($event)"
                          [nzDisabled]="processFactoryDisabled">
                          <nz-option
                            *ngFor="let item of process_factory_options"
                            [nzValue]="item.process_factory_code"
                            [nzLabel]="item.process_factory_name">
                          </nz-option>
                          <nz-option
                            [nzLabel]="detail?.io_basic?.process_factory_name"
                            [nzValue]="detail?.io_basic?.process_factory_code"
                            nzHide></nz-option>
                        </nz-select>

                        <nz-select
                          *ngIf="item.code === 'merchandiser_user_id'"
                          [nzPlaceHolder]="'flss.placeholder.select' | translate"
                          [formControlName]="item.code"
                          [nzDisabled]="this.pageMode === PageModeEnum.detail ? !!detail?.io_basic?.merchandiser_user_id : is_merchandiser"
                          nzAllowClear
                          nzShowSearch
                          (nzOpenChange)="openMerchandiserUser($event)">
                          <nz-option *ngFor="let item of merchandiser_options" [nzValue]="item.user_emp_id" [nzLabel]="item.user_name">
                          </nz-option>
                          <nz-option
                            [nzLabel]="bulkForm?.get('merchandiser_name')?.value"
                            [nzValue]="bulkForm?.get('merchandiser_user_id')?.value"
                            nzHide></nz-option>
                        </nz-select>

                        <nz-select
                          *ngIf="['pro_office', 'lead_time', 'season', 'origin_country', 'category_type', 'customer'].includes(item.code)"
                          [nzPlaceHolder]="'flss.placeholder.select' | translate"
                          nzAllowClear
                          nzShowSearch
                          [nzDisabled]="!can_edit_field(item.label)"
                          [formControlName]="item.code"
                          (ngModelChange)="customerChange($event, item)">
                          <nz-option
                            *ngFor="let item of selectOptions?.[item?.optionKey || item?.code]"
                            [nzValue]="item.label"
                            [nzLabel]="item.label">
                          </nz-option>
                        </nz-select>
                        <!-- 部门 -->
                        <nz-select
                          *ngIf="item.code === 'gen_dept_id'"
                          [nzPlaceHolder]="'placeholder.select' | translate"
                          [formControlName]="item.code"
                          [nzOptions]="departmentList"
                          [nzDropdownMatchSelectWidth]="false"
                          nzAllowClear
                          nzShowSearch>
                        </nz-select>
                      </ng-container>

                      <ng-container *ngIf="item.code === 'production_category_id'">
                        <flc-dynamic-search-select
                          [dataUrl]="'/service/scm/dict_category/dict_option'"
                          [formControlName]="'production_category_id'"
                          [column]="'sclm'"
                          [defaultValue]="{
                            value: bulkForm.get('production_category_id')?.value,
                            label: bulkForm.get('production_category_name')?.value
                          }"
                          (handleSearch)="onSelectProductionCategory($event)"
                          [optAlwaysReload]="true">
                        </flc-dynamic-search-select>
                      </ng-container>

                      <ng-container *ngIf="item.type === 'treeSelect'">
                        <flss-department-select
                          [requestInside]="true"
                          [formControlName]="item.code"
                          [canSelectEmployee]="true"
                          [canSelectDepartment]="false"></flss-department-select>
                      </ng-container>

                      <ng-container *ngIf="item.type === 'select_radio'">
                        <nz-radio-group [formControlName]="item.code">
                          <label nz-radio [nzValue]="item.trueValue ?? 1">{{ 'common.是' | translate }}</label>
                          <label nz-radio [nzValue]="item.falseValue ?? 2">{{ 'common.否' | translate }}</label>
                        </nz-radio-group>
                      </ng-container>
                      <ng-container *ngIf="item.type === 'upload'">
                        <div class="append-box" *ngIf="item.code === 'appendix_requirements'">
                          <div class="append-desc">
                            <div *ngIf="edit">({{ 'common.附件格式不限' | translate: { size: '100M', count: 100 } }})</div>
                            <div class="file-box">
                              <span *ngIf="detail?.io_basic?.appendix_requirements.length === 0 && !edit" style="color: #b5b8bf">-</span>
                              <flc-file-gallery
                                #fileGallery
                                [galleryType]="'any'"
                                [wrap]="false"
                                [addBtnText]="'common.上传附件' | translate"
                                [fileList]="detail?.io_basic?.appendix_requirements || []"
                                [isEditMode]="edit"
                                [fileMaxCount]="100"
                                [fileMaxSize]="100"
                                [dimensionWidth]="128"
                                [dimensionHeight]="128"
                                (onDeleted)="getFileList($event)"
                                (onUploaded)="getFileList($event)"></flc-file-gallery>
                            </div>
                          </div>
                        </div>
                        <div *ngIf="edit" style="height: 32px; width: 8px"></div>
                        <flc-image-gallery
                          *ngIf="item.code === 'pictures'"
                          #imageGallery
                          [useDialogMode]="true"
                          [uploadButtonText]="'common.上传图片' | translate"
                          [ngModelOptions]="{ standalone: true }"
                          [isEdit]="edit"
                          [numberLimit]="100"
                          [useCloudImage]="true"
                          [(ngModel)]="detail.io_basic.pictures"
                          (uploadChange)="uploadChange()"></flc-image-gallery>
                      </ng-container>
                    </nz-form-control>
                  </nz-form-item>
                  <ng-template #onlyRead>
                    <div class="basic-item" nz-row>
                      <span class="basic-label" nz-col [nzFlex]="lang === 'en' ? item?.flexWidth || '180px' : '150px'"
                        >{{ translateName + item.label | translate }}
                        <i
                          *ngIf="item.code === 'fabric'"
                          nz-icon
                          nzType="question-circle"
                          nzTheme="outline"
                          nz-tooltip
                          [nzTooltipTitle]="translateName + '面料提示' | translate"></i>
                        ：</span
                      >
                      <span *ngIf="item.code !== 'extra_process_info'; else extraProcess" class="basic-value" nzFlex="auto">
                        <div
                          *ngIf="
                            item.type !== 'select' &&
                            item.type !== 'dynamic-select' &&
                            item.type !== 'treeSelect' &&
                            item.type !== 'select_radio' &&
                            item.type !== 'upload'
                          "
                          [ngStyle]="{ 'white-space': item.code === 'remark' ? 'pre-wrap' : '' }">
                          <flc-text-truncated
                            [data]="
                              detail?.io_basic[item.code] !== null &&
                              detail?.io_basic[item.code] !== undefined &&
                              item.type === 'number-input' &&
                              item.suffix
                                ? detail?.io_basic[item.code] + item.suffix
                                : detail?.io_basic[item.code]
                            "
                            [tooltipOverlayClassName]="item?.tipName || ''"></flc-text-truncated>
                        </div>
                        <div *ngIf="item.type === 'remote-select'">
                          <flc-text-truncated [data]="detail?.io_basic[item?.labelKey || item?.code]"></flc-text-truncated>
                        </div>
                        <div *ngIf="item.code === 'order_category'">
                          <flc-text-truncated [data]="detail?.io_basic['order_category_label']"></flc-text-truncated>
                        </div>
                        <div *ngIf="item.code === 'order_production_type'">
                          <flc-text-truncated [data]="detail?.io_basic['order_production_type_label']"></flc-text-truncated>
                        </div>
                        <div *ngIf="item.code === 'brand_id'">
                          <flc-text-truncated [data]="detail?.io_basic['brand_name']"></flc-text-truncated>
                        </div>
                        <div *ngIf="item.code === 'style_code'">
                          <flc-text-truncated [data]="detail?.io_basic['style_code']"></flc-text-truncated>
                        </div>
                        <div *ngIf="item.code === 'sale_channel_id'">
                          <flc-text-truncated [data]="detail?.io_basic['sale_channel_name']"></flc-text-truncated>
                        </div>
                        <div *ngIf="item.code === 'production_category_id'">
                          <flc-text-truncated [data]="detail?.io_basic['production_category_name']"></flc-text-truncated>
                        </div>
                        <div *ngIf="['fast_reply', 'third_quality_check', 'customs_clearance', 'is_pre_order'].includes(item.code)">
                          <flc-text-truncated
                            [data]="['', 'common.是', 'common.否'][detail?.io_basic[item.code] ?? 0] | translate"></flc-text-truncated>
                        </div>
                        <div *ngIf="['urgent_status'].includes(item.code)">
                          <flc-text-truncated
                            [data]="
                              detail?.io_basic[item.code] ? (detail?.io_basic[item.code] === 20 ? '是' : '否') : ''
                            "></flc-text-truncated>
                        </div>
                        <div *ngIf="item.code === 'is_use_plan'">
                          <flc-text-truncated
                            [data]="(detail?.io_basic[item.code] ? 'common.是' : 'common.否') | translate"></flc-text-truncated>
                        </div>
                        <div *ngIf="item.code === 'quality_level'">
                          <flc-text-truncated
                            [data]="
                              ['', 'bulk.高（精品货）', 'bulk.中（一般品牌货）', 'bulk.低（市场货）'][detail?.io_basic[item.code] ?? 0]
                                | translate
                            "></flc-text-truncated>
                        </div>

                        <div *ngIf="item.code === 'payment_condition'">
                          <flc-text-truncated [data]="formatterText(detail?.io_basic?.payment_condition)"></flc-text-truncated>
                        </div>

                        <div *ngIf="item.code === 'payment_id'">
                          <flc-text-truncated [data]="detail?.io_basic?.payment_name"></flc-text-truncated>
                        </div>
                        <div *ngIf="item.code === 'currency_id'">
                          <flc-text-truncated [data]="detail?.io_basic?.currency_name"></flc-text-truncated>
                        </div>

                        <div *ngIf="item.code === 'order_classification'">
                          <flc-text-truncated [data]="detail?.io_basic['order_classification']"></flc-text-truncated>
                        </div>

                        <div *ngIf="item.code === 'process_factory_code'">
                          <flc-text-truncated [data]="detail?.io_basic['process_factory_name']"></flc-text-truncated>
                        </div>
                        <div *ngIf="item.code === 'merchandiser_user_id'">
                          <flc-text-truncated [data]="detail?.io_basic['merchandiser_name']"></flc-text-truncated>
                        </div>
                        <div *ngIf="item.code === 'biz_user_emp_id'">
                          <flc-text-truncated [data]="detail?.io_basic['biz_user_name']"></flc-text-truncated>
                        </div>
                        <div *ngIf="item.code === 'order_labels'">
                          <flc-text-truncated [data]="(detail?.io_basic['order_labels'] ?? []).join('、')"></flc-text-truncated>
                        </div>
                        <div
                          *ngIf="['pro_office', 'lead_time', 'season', 'origin_country', 'category_type', 'customer'].includes(item.code)">
                          <flc-text-truncated [data]="detail?.io_basic[item.labelKey]"></flc-text-truncated>
                        </div>

                        <div *ngIf="item.code === 'gen_dept_id'">
                          <flc-text-truncated [data]="detail?.io_basic['department']"></flc-text-truncated>
                        </div>
                        <div class="append-box" *ngIf="item.code === 'appendix_requirements'">
                          <div class="append-desc">
                            <div class="file-box">
                              <div class="img-box" *ngIf="detail?.io_basic?.appendix_requirements.length === 0 && !edit">
                                <i nz-icon [nzIconfont]="'icon-tupian1-mianxing'" style="font-size: 60px; color: #b5b8bf"></i>
                              </div>
                              <flc-file-gallery
                                #fileGalleryReadonly
                                [galleryType]="'any'"
                                [wrap]="false"
                                [fileList]="detail?.io_basic?.appendix_requirements || []"
                                [isEditMode]="edit"
                                [addBtnText]="'common.上传附件' | translate"
                                [fileMaxCount]="100"
                                [fileMaxSize]="100"
                                (onDeleted)="getFileList($event)"
                                (onUploaded)="getFileList($event)"></flc-file-gallery>
                            </div>
                          </div>
                        </div>
                        <flc-image-gallery
                          *ngIf="item.code === 'pictures'"
                          #imageGalleryReadonly
                          [useDialogMode]="true"
                          [uploadButtonText]="'common.上传图片' | translate"
                          [ngModelOptions]="{ standalone: true }"
                          [isEdit]="edit"
                          [numberLimit]="100"
                          [useCloudImage]="true"
                          [(ngModel)]="detail.io_basic.pictures"
                          (uploadChange)="uploadChange()"></flc-image-gallery>
                      </span>
                      <ng-template #extraProcess>
                        <div style="overflow: hidden; display: inline-block">
                          <flc-text-truncated [template]="extraProcessItem"></flc-text-truncated>
                          <ng-template #extraProcessItem>
                            <ng-container *ngIf="detail?.io_basic?.extra_process_info.length; else noExtra">
                              <span class="basic-value" *ngFor="let extra of detail?.io_basic?.extra_process_info; last as isLast"
                                >{{ extra.extra_process_name }} <ng-container *ngIf="!isLast">、</ng-container>
                              </span>
                            </ng-container>
                            <ng-template #noExtra>
                              <span style="color: #b5b8bf">-</span>
                            </ng-template>
                          </ng-template>
                        </div>
                      </ng-template>
                    </div>
                  </ng-template>
                </div>
              </ng-container>
            </div>
          </div>
          <div class="desc-right" nz-col nzSpan="5" *ngIf="order_picture_show">
            <nz-form-label [nzRequired]="true">{{ translateName + '款式图' | translate }}</nz-form-label>
            <flc-image-gallery
              #imageGallery
              [useDialogMode]="true"
              [uploadButtonText]="'common.上传图片' | translate"
              [ngModelOptions]="{ standalone: true }"
              [isEdit]="edit && can_edit_field('款式图')"
              [numberLimit]="100"
              [useCloudImage]="true"
              [(ngModel)]="detail.io_basic.order_pictures"
              (uploadChange)="uploadChange()"></flc-image-gallery>
          </div>
        </div>
      </div>
      <!-- 基本信息结束 -->
    </form>
    <!-- 颜色尺码开始 -->
    <div class="basic-box" style="padding-bottom: 10px; border-radius: 4px 4px 12px 12px">
      <div class="block-title color-size-selector">
        <span>{{ translateName + '颜色尺码' | translate }}</span>
        <span (click)="showAll()" class="all-screen">
          <i
            nz-icon
            nz-tooltip
            [nzTooltipTitle]="toolTipVisible ? ('common.' + (showTop ? '展开全屏' : '收起全屏') | translate) : null"
            [nzType]="showTop ? 'fullscreen' : 'fullscreen-exit'"
            nzTheme="outline"></i>
        </span>
      </div>
      <div class="po-title">
        <div class="title-left">
          <span class="po-name">{{ translateName + '交付单' | translate }}</span>
        </div>
        <div>
          <button nz-button flButton="pretty-minor" (click)="onOpenDrawerCustomByDFWO()" style="margin-right: 8px">
            <i nz-icon [nzIconfont]="'icon-xitongpeizhi'"></i>
          </button>
          <button *ngIf="edit" nz-button flButton="minor" nzShape="round" (click)="addPo()">
            <i nz-icon [nzIconfont]="'icon-xinjian1'"></i>
            {{ translateName + '新增交付单' | translate }}
          </button>
        </div>
      </div>
      <div class="bulk-po-tab-box">
        <nz-tabset
          [(nzSelectedIndex)]="tabIndex"
          (nzSelectedIndexChange)="tabIndexChange($event)"
          [nzTabBarExtraContent]="extraTemplate"
          [nzTabBarGutter]="8"
          nzType="editable-card"
          nzHideAdd
          (nzClose)="closeTab($event)">
          <nz-tab
            *ngFor="let tab of detail?.pos; index as i"
            [nzCloseIcon]="closeIcon"
            [nzClosable]="detail.pos.length > 1 && edit && tab.po_basic.deletable"
            [nzTitle]="titleTemplate">
            <ng-template #titleTemplate>
              <div class="po-title-box" (keydown.enter)="addPo()">
                <ng-container *ngIf="edit; else onlyPo">
                  <input
                    class="po-input"
                    *ngIf="tab.editable"
                    nz-input
                    autofocus
                    flcInputTrim
                    [maxlength]="30"
                    [placeholder]="translateName + '请输入交付单号' | translate"
                    autocomplete="off"
                    [(ngModel)]="tab.po_basic.po_code"
                    (blur)="tabInputBlur(tab.po_basic.po_code)" />
                  <ng-container *ngIf="!tab.editable">
                    <div style="display: flex; align-items: center; width: 100%">
                      <flc-text-truncated
                        style="flex: 1; white-space: normal"
                        class="po-code-box"
                        [data]="tab.po_basic.po_code"></flc-text-truncated>
                      <div width="50px">
                        <i
                          class="edit-icon"
                          nz-icon
                          [nzIconfont]="'icon-caozuolan_bianji1'"
                          nz-tooltip
                          [nzTooltipTitle]="translateName + '重命名' | translate"
                          style="margin-left: 5px"
                          (click)="editPo(i)"></i>
                        <i
                          class="edit-icon"
                          nz-icon
                          [nzIconfont]="'icon-caozuolan_fuzhi'"
                          nz-tooltip
                          [nzTooltipTitle]="'flss.btn.copy' | translate"
                          style="margin-left: 5px"
                          (flcClickStop)="copyPo(i)"></i>
                      </div>
                    </div>
                  </ng-container>
                </ng-container>
                <ng-template #onlyPo>
                  <flc-text2-truncated style="white-space: normal" [data]="tab?.po_basic?.po_code"></flc-text2-truncated>
                </ng-template>
                <div class="time-style" *ngIf="!tab.editable">
                  <flc-no-value-text
                    [data]="tab?.po_basic?.due_time ? (tab?.po_basic?.due_time | date: 'yyyy/MM/dd') : ''"></flc-no-value-text>
                </div>
              </div>
            </ng-template>
            <div class="po-tab-box-bg">
              <div class="po-tab-box-wrap">
                <div class="color-size-tile">
                  <div class="size">
                    <ng-container *ngFor="let cf of DFWOConfigList; last as last">
                      <ng-container *ngIf="cf.visible">
                        <ng-container
                          *ngIf="
                            ['spec_group_id', 'due_time', 'customer_due_time', 'receiver', 'contact', 'address'].includes(cf.code);
                            else otherFileds
                          ">
                          <nz-form-item *ngIf="cf?.code === 'spec_group_id'" style="align-items: center" class="spec-group-item">
                            <nz-form-label [nzRequired]="edit ? 'required' : false">{{
                              translateName + '尺码组' | translate
                            }}</nz-form-label>
                            <nz-form-control>
                              <flc-dynamic-search-select
                                [minWidth]="156"
                                [disabled]="!tab.po_basic.deletable"
                                *ngIf="edit; else onlysizegroup"
                                [(ngModel)]="tab.po_basic.spec_group_id"
                                [column]="'size_group_name'"
                                [optAlwaysReload]="true"
                                [customerOptionTpl]="customerOptionTpl"
                                [payLoad]="{
                                  style_lib_id: styleCodeDetail ? styleCodeDetail.style_lib_id : null,
                                  style_uuid: styleCodeDetail ? detail.io_basic.style_code_uuid : null
                                }"
                                [defaultValue]="
                                  tab.po_basic.spec_group_id
                                    ? {
                                        label: tab.po_basic.spec_group_name,
                                        value: tab.po_basic.spec_group_id
                                      }
                                    : {}
                                "
                                [dataUrl]="'/service/scm/sample_order/style_lib/size_group/option'"
                                (handleSearch)="sizeGroupChange($event, i)"></flc-dynamic-search-select>
                              <ng-template #customerOptionTpl let-data="data">
                                <span [ngStyle]="{ color: data.item?.used ? '#fb6401' : '' }">{{ data.item.label }}</span>
                              </ng-template>
                              <ng-template #onlysizegroup>
                                <span class="fontW500">
                                  <flc-no-value-text [data]="tab.po_basic.spec_group_name"></flc-no-value-text>
                                </span>
                              </ng-template>
                            </nz-form-control>

                            <span class="line"></span>
                          </nz-form-item>

                          <nz-form-item *ngIf="cf?.code === 'due_time'">
                            <nz-form-label [nzRequired]="edit ? 'required' : false">{{
                              translateName + '交付日期' | translate
                            }}</nz-form-label>
                            <nz-form-control>
                              <nz-date-picker
                                *ngIf="edit; else onlypDate"
                                [(ngModel)]="tab?.po_basic.due_time"
                                [nzFormat]="'yyyy/MM/dd'"
                                [nzPlaceHolder]="'flss.placeholder.select' | translate"
                                (ngModelChange)="dateChange($event)"></nz-date-picker>
                              <ng-template #onlypDate>
                                <span class="fontW500">
                                  <flc-no-value-text
                                    [data]="
                                      tab?.po_basic?.due_time ? (tab.po_basic.due_time | date: 'yyyy/MM/dd') : ''
                                    "></flc-no-value-text> </span
                              ></ng-template>
                            </nz-form-control>
                          </nz-form-item>

                          <nz-form-item *ngIf="cf?.code === 'customer_due_time'">
                            <nz-form-label>{{ translateName + '客户交期' | translate }}</nz-form-label>
                            <nz-form-control>
                              <nz-date-picker
                                *ngIf="edit; else customerDeliveryDate"
                                [(ngModel)]="tab?.po_basic.customer_due_time"
                                [nzFormat]="'yyyy/MM/dd'"
                                [nzPlaceHolder]="'flss.placeholder.select' | translate"
                                (ngModelChange)="dateChange($event)"></nz-date-picker>
                              <ng-template #customerDeliveryDate>
                                <span class="fontW500">
                                  <flc-no-value-text
                                    [data]="
                                      tab?.po_basic?.customer_due_time ? (tab.po_basic.customer_due_time | date: 'yyyy/MM/dd') : ''
                                    "></flc-no-value-text> </span
                              ></ng-template>
                              <!-- <div
                        *ngIf="styleCodeDetail && edit && bulkForm.get('style_code')?.value"
                        style="margin-left: 16px"
                        class="tip-yellow-text">
                        *选框内标橘色字色的尺码组颜色代表在款式库中存在
                      </div> -->
                            </nz-form-control>
                          </nz-form-item>

                          <nz-form-item *ngIf="cf.code === 'receiver'">
                            <nz-form-label [nzRequired]="cf?.required">{{ translateName + '收货人' | translate }}</nz-form-label>
                            <nz-form-control>
                              <nz-textarea-count *ngIf="edit; else onlyReceiver" [nzMaxCharacterCount]="100" class="inline-count">
                                <textarea
                                  rows="1"
                                  nz-input
                                  [placeHolder]="'flss.placeholder.input' | translate"
                                  flcInputTrim
                                  [maxLength]="100"
                                  [(ngModel)]="tab.po_basic.receiver"></textarea>
                              </nz-textarea-count>
                              <ng-template #onlyReceiver>
                                <div class="fontW500"><flc-text-truncated [data]="tab.po_basic.receiver"></flc-text-truncated></div
                              ></ng-template>
                            </nz-form-control>
                          </nz-form-item>
                          <nz-form-item *ngIf="cf.code === 'contact'">
                            <nz-form-label [nzRequired]="cf?.required">{{ translateName + '联系方式' | translate }}</nz-form-label>
                            <nz-form-control>
                              <nz-textarea-count *ngIf="edit; else onlyContact" [nzMaxCharacterCount]="100" class="inline-count">
                                <textarea
                                  rows="1"
                                  nz-input
                                  [placeHolder]="'flss.placeholder.input' | translate"
                                  [maxLength]="100"
                                  [(ngModel)]="tab.po_basic.contact"></textarea>
                              </nz-textarea-count>
                              <ng-template #onlyContact>
                                <div class="fontW500"><flc-text-truncated [data]="tab.po_basic.contact"></flc-text-truncated></div
                              ></ng-template>
                            </nz-form-control>
                          </nz-form-item>
                          <nz-form-item *ngIf="cf.code === 'address'">
                            <nz-form-label [nzRequired]="cf?.required">{{ translateName + '收货地址' | translate }}</nz-form-label>
                            <nz-form-control>
                              <ng-container *ngIf="edit; else onlyAddress">
                                <div style="display: flex; flex: 1">
                                  <nz-cascader
                                    style="width: 224px"
                                    [(ngModel)]="tab['addDefault']"
                                    [nzOptions]="regionList"
                                    [nzPlaceHolder]="translateName + '省市区' | translate"
                                    (nzSelectionChange)="addressChange($event, i)"
                                    [nzShowSearch]="true"></nz-cascader>
                                  <nz-textarea-count [nzMaxCharacterCount]="255" class="inline-count" style="width: 350px">
                                    <textarea
                                      [nzAutosize]="{ minRows: 0, maxRows: 3 }"
                                      style="margin-left: 4px"
                                      nz-input
                                      flcInputTrim
                                      [maxLength]="255"
                                      [placeholder]="translateName + '请输入详细地址' | translate"
                                      [(ngModel)]="tab.po_basic.address"></textarea>
                                  </nz-textarea-count>
                                </div>
                              </ng-container>
                              <ng-template #onlyAddress>
                                <span class="fontW500" style="display: block; width: 100%; flex: 1">
                                  <flc-text-truncated
                                    [data]="
                                      tab.po_basic?.country_name +
                                      tab.po_basic?.province_name +
                                      tab.po_basic?.city_name +
                                      tab.po_basic?.district_name +
                                      tab.po_basic?.address
                                    "></flc-text-truncated>
                                </span>
                              </ng-template>
                            </nz-form-control>
                          </nz-form-item>
                        </ng-container>
                        <ng-template #otherFileds>
                          <nz-form-item>
                            <nz-form-label>{{ translateName + cf.label | translate }}{{ cf.suffix ?? '' }}</nz-form-label>
                            <ng-container *ngIf="edit">
                              <nz-form-control *ngIf="cf.type === 'textarea'">
                                <nz-textarea-count [nzMaxCharacterCount]="cf.maxLength" class="inline-count">
                                  <textarea
                                    rows="1"
                                    nz-input
                                    [placeHolder]="'flss.placeholder.input' | translate"
                                    flcInputTrim
                                    [maxLength]="cf?.maxLength"
                                    [(ngModel)]="tab?.po_basic[cf.code]"></textarea>
                                </nz-textarea-count>
                              </nz-form-control>

                              <nz-form-control *ngIf="cf.type === 'input-number'">
                                <nz-input-number
                                  style="min-width: 120px"
                                  [(ngModel)]="tab?.po_basic[cf.code]"
                                  [nzPrecision]="cf.precision"
                                  [nzMin]="cf?.min"
                                  [nzPlaceHolder]="'flss.placeholder.input' | translate">
                                </nz-input-number>
                              </nz-form-control>

                              <ng-form-control *ngIf="cf.type === 'select'">
                                <nz-select
                                  style="min-width: 130px"
                                  [nzPlaceHolder]="'flss.placeholder.select' | translate"
                                  nzAllowClear
                                  nzShowSearch
                                  [(ngModel)]="tab?.po_basic[cf.code]">
                                  <nz-option
                                    *ngFor="let item of selectOptions?.[cf?.optionKey || cf?.code]"
                                    [nzValue]="item.label"
                                    [nzLabel]="item.label">
                                  </nz-option>
                                </nz-select>
                              </ng-form-control>
                            </ng-container>
                            <ng-form-control *ngIf="!edit" style="display: flex; align-items: center"
                              ><div class="fontW500">
                                <flc-text-truncated *ngIf="!edit" [data]="tab?.po_basic[cf.code]"></flc-text-truncated>
                              </div>
                            </ng-form-control>
                          </nz-form-item>
                        </ng-template>
                      </ng-container>
                    </ng-container>
                  </div>
                  <div *ngIf="edit && tab?.maxIndexing > 1">
                    <button
                      class="auto-sort-btn"
                      (click)="sortSize()"
                      nz-button
                      nzType="default"
                      nzShape="round"
                      nzSize="small"
                      nz-tooltip
                      [nzTooltipTitle]="translateName + '点击后尺码将按照尺码档案数据排序' | translate"
                      [nzTooltipOverlayStyle]="{ 'max-width': '340px' }">
                      <i nz-icon [nzIconfont]="'icon-paixushang'"></i>
                      <span style="margin-left: 0">{{ translateName + '尺码自动排序' | translate }}</span>
                    </button>
                  </div>
                </div>
                <div>
                  <!-- 注释掉flc-color-size-table组件 -->

                  <!-- <flc-color-size-table
                    #colorSizeTable
                    [dataList]="tab.po_basic.spec_group_id ? tab.po_lines : null"
                    [mode]="edit ? 'editTable' : 'readOnly'"
                    [isShowRowCopy]="true"
                    [fetchSizeOptions]="onFetchSizeOptionsWrap(i)"
                    [fetchColorOptions]="onFetchColorOptions"
                    (onChange)="sizeColorChange($event)"></flc-color-size-table> -->

                  <div class="no-data-text" *ngIf="!tab?.po_lines?.length && edit && !tab.po_basic.spec_group_id">
                    {{ translateName + '请先选择尺码组哦' | translate }}
                  </div>
                  <div class="no-data-text" *ngIf="!tab?.po_lines?.length && !edit">{{ 'common.暂无数据' | translate }}</div>
                </div>
                <ng-container *ngIf="tab.po_basic.spec_group_id">
                  <nz-table #editRowTable nzBordered [nzData]="processedData[i]" [nzFrontPagination]="false" [nzShowPagination]="false">
                    <thead>
                      <tr>
                        <th><span style="color: red">*</span>颜色</th>
                        <th><span style="color: red">*</span>尺码</th>
                        <th><span style="color: red">*</span>数量</th>
                        <th><span style="color: red">*</span>单价</th>
                        <th>总金额</th>
                        <th>溢装比例(%)</th>
                        <th>溢装数</th>
                        <th>短装比例(%)</th>
                        <th>短装数</th>
                        <th *ngIf="edit" width="120">操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let item of processedData[i]; let j = index">
                        <td *ngIf="item.colorRowspan && item.colorRowspan > 0" [attr.rowspan]="item.colorRowspan">
                          <!-- 颜色列：编辑模式显示cascader，非编辑模式显示span -->
                          <ng-container *ngIf="edit">
                            <nz-cascader
                              [ngModel]="item.color === '' ? null : item.color"
                              [nzOptions]="colorOptions"
                              [nzShowSearch]="true"
                              [nzExpandTrigger]="'hover'"
                              [nzPlaceHolder]="'请选择颜色'"
                              (nzVisibleChange)="onShowColorSelector($event, i)"
                              (nzSelectionChange)="onColorChange($event, item, i, j)"
                              style="width: 100%">
                            </nz-cascader>
                          </ng-container>
                          <ng-container *ngIf="!edit">
                            <span>{{ item.color }}</span>
                          </ng-container>
                        </td>

                        <td>
                          <!-- 尺码列：编辑模式显示多选select，非编辑模式显示span -->
                          <ng-container *ngIf="edit">
                            <nz-select
                              [ngModel]="item.originalLine.size_info.spec_id"
                              nzPlaceHolder="请选择尺码"
                              [nzShowSearch]="true"
                              [nzAllowClear]="true"
                              (nzOpenChange)="getSizeOption($event, i)"
                              (ngModelChange)="onSizeChange($event, item, i, j)"
                              style="width: 100%">
                              <ng-container *ngIf="isSizeOptionsLoading">
                                <nz-option nzDisabled nzCustomContent>
                                  <div class="loading-container">
                                    <i nz-icon nzType="loading" class="loading-icon"></i>
                                    加载中...
                                  </div>
                                </nz-option>
                              </ng-container>
                              <ng-container *ngIf="!isSizeOptionsLoading">
                                <nz-option
                                  *ngFor="let sizeOpt of sizeOptions"
                                  [nzValue]="sizeOpt.value"
                                  [nzLabel]="sizeOpt.label"
                                  [nzDisabled]="sizeOpt.disabled">
                                </nz-option>
                              </ng-container>
                            </nz-select>
                          </ng-container>
                          <ng-container *ngIf="!edit">
                            <span>{{ item.size }}</span>
                          </ng-container>
                        </td>

                        <td>
                          <!-- 数量列：编辑模式显示输入框，非编辑模式显示文本 -->
                          <ng-container *ngIf="edit">
                            <nz-input-number
                              [(ngModel)]="item.qty"
                              [nzMin]="0"
                              [nzPrecision]="0"
                              [nzPlaceHolder]="'请输入数量'"
                              (ngModelChange)="onQtyChange($event, item, i, j)"
                              (blur)="onInputBlur(i, item)">
                            </nz-input-number>
                          </ng-container>
                          <ng-container *ngIf="!edit">
                            <span>{{ item.qty }}</span>
                          </ng-container>
                        </td>

                        <!-- 单价: Render only if it's the first row of its unit price group (tied to color here) -->
                        <td *ngIf="item.unitPriceRowspan && item.unitPriceRowspan > 0" [attr.rowspan]="item.unitPriceRowspan">
                          <nz-input-number
                            *ngIf="edit"
                            [(ngModel)]="item.unit_price"
                            [nzMin]="0"
                            [nzStep]="0.01"
                            [nzPrecision]="2"
                            (ngModelChange)="onUnitPriceChange(item, i)"
                            (blur)="onInputBlur(i, item)"
                            [required]="true">
                          </nz-input-number>
                          <ng-container *ngIf="!edit">{{ item.unit_price }}</ng-container>
                        </td>

                        <!-- 总金额 -->
                        <td>{{ item.sku_amount | number: '1.2-2' }}</td>

                        <!-- 溢装比例 (%) -->
                        <td>
                          <div class="ratio-cell" *ngIf="edit">
                            <nz-input-number
                              [(ngModel)]="item.excess_rate"
                              [nzMin]="0"
                              [nzMax]="100"
                              [nzStep]="1"
                              [nzPrecision]="2"
                              (ngModelChange)="onOverflowRatioChange(item, i)"
                              (blur)="onInputBlur(i, item)">
                            </nz-input-number>
                            <ng-container *ngIf="item.excess_rate !== null && item.excess_rate !== undefined">
                              <i nz-icon nzType="close" nzTheme="outline" (click)="clearOverflowRatio(item, i)"></i>
                              <i nz-icon nzType="check" nzTheme="outline" (click)="applyOverflowRatioToAll(item, i)"></i>
                            </ng-container>
                          </div>
                          <ng-container *ngIf="!edit">{{ item.excess_rate }}</ng-container>
                        </td>

                        <!-- 溢装数 -->
                        <td>{{ item.excess_quantity }}</td>

                        <!-- 短装比例 (%) -->
                        <td>
                          <div class="ratio-cell" *ngIf="edit">
                            <nz-input-number
                              [(ngModel)]="item.deficiency_rate"
                              [nzMin]="0"
                              [nzMax]="100"
                              [nzStep]="1"
                              [nzPrecision]="2"
                              (ngModelChange)="onShortageRatioChange(item, i)"
                              (blur)="onInputBlur(i, item)">
                            </nz-input-number>
                            <ng-container *ngIf="item.deficiency_rate !== null && item.deficiency_rate !== undefined">
                              <i nz-icon nzType="close" nzTheme="outline" (click)="clearShortageRatio(item, i)"></i>
                              <i nz-icon nzType="check" nzTheme="outline" (click)="applyShortageRatioToAll(item, i)"></i>
                            </ng-container>
                          </div>
                          <ng-container *ngIf="!edit">{{ item.deficiency_rate }}</ng-container>
                        </td>

                        <!-- 短装数 -->
                        <td>{{ item.deficiency_quantity }}</td>

                        <!-- 操作列 -->
                        <td *ngIf="edit" class="action-column">
                          <div class="action-buttons">
                            <!-- 删除行按钮 (红色) -->
                            <button
                              nz-button
                              nzType="text"
                              nzSize="small"
                              class="action-btn delete-btn"
                              nz-tooltip
                              nzTooltipTitle="删除此行"
                              (click)="deleteRow(i, j)">
                              <i nz-icon nzType="minus-circle" nzTheme="fill"></i>
                            </button>

                            <!-- 添加行按钮 (蓝色) -->
                            <button
                              nz-button
                              nzType="text"
                              nzSize="small"
                              class="action-btn add-btn"
                              nz-tooltip
                              nzTooltipTitle="添加新行"
                              (click)="addRow(i, j)">
                              <i nz-icon nzType="plus-circle" nzTheme="fill"></i>
                            </button>

                            <!-- 复制行按钮 -->
                            <button
                              nz-button
                              nzType="text"
                              nzSize="small"
                              class="action-btn copy-btn"
                              nz-tooltip
                              nzTooltipTitle="复制此行"
                              (click)="copyRow(i, j)">
                              <i nz-icon nzType="copy" nzTheme="fill"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                    <tfoot>
                      <tr style="text-align: center">
                        <td colspan="2">合计</td>
                        <td>{{ tableTotals[i]?.qty }}</td>
                        <td>-</td>
                        <td>{{ tableTotals[i]?.sku_amount | number: '1.2-2' }}</td>
                        <td>-</td>
                        <td>{{ tableTotals[i]?.excess_quantity }}</td>
                        <td>-</td>
                        <td>{{ tableTotals[i]?.deficiency_quantity }}</td>
                        <td *ngIf="edit">-</td>
                      </tr>
                    </tfoot>
                  </nz-table>
                </ng-container>
              </div>
            </div>
          </nz-tab>
        </nz-tabset>
        <ng-template #closeIcon>
          <i nz-icon [nzIconfont]="'icon-cuowu'"></i>
        </ng-template>
        <ng-template #extraTemplate>
          <nz-select
            style="width: 140px"
            [nzPlaceHolder]="translateName + '选择交付单' | translate"
            [nzOptions]="poSelectList"
            nzShowSearch
            nzAllowClear
            [(ngModel)]="poValue"
            (nzOpenChange)="poSelectOpen($event)"
            (ngModelChange)="poSelectChange($event)"></nz-select>
        </ng-template>
      </div>
    </div>
    <!-- 颜色尺码结束 -->
  </div>
  <!-- 合计开始 -->
  <div class="basic-box sum-box">
    <div class="title-left block-title">
      <span class="po-name">{{ translateName + '合计' | translate }}</span>
    </div>
    <!-- <flc-color-size-table
      #colorSizeTableSum
      *ngIf="detail.io_lines.length"
      [dataList]="detail.io_lines"
      [mode]="edit ? 'sortOnly' : 'readOnly'"
      (onChange)="sumSizeColorChange($event)"></flc-color-size-table> -->
    <div class="no-data-text" *ngIf="!detail.io_lines.length">{{ 'common.暂无数据' | translate }}</div>

    <nz-table #editRowTable nzBordered [nzData]="mergedTableData" [nzFrontPagination]="false" [nzShowPagination]="false">
      <thead>
        <tr>
          <th>颜色</th>
          <th>尺码</th>
          <th>总数量</th>
          <th>总金额</th>
          <th>总溢装数</th>
          <th>总短装数</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of mergedTableData">
          <td *ngIf="item.colorRowspan && item.colorRowspan > 0" [attr.rowspan]="item.colorRowspan">
            {{ item.color }}
          </td>
          <td>{{ item.size }}</td>
          <td>{{ item.qty }}</td>
          <td>{{ item.sku_amount | number: '1.2-2' }}</td>
          <td>{{ item.excess_quantity }}</td>
          <td>{{ item.deficiency_quantity }}</td>
        </tr>
      </tbody>
      <tfoot>
        <tr style="text-align: center">
          <td colspan="2">合计</td>
          <td>{{ mergedTotals.qty }}</td>
          <td>{{ mergedTotals.sku_amount | number: '1.2-2' }}</td>
          <td>{{ mergedTotals.excess_quantity }}</td>
          <td>{{ mergedTotals.deficiency_quantity }}</td>
        </tr>
      </tfoot>
    </nz-table>
  </div>
</nz-spin>
<!-- 合计结束 -->
<!-- 退回修改弹窗开始 -->
<nz-modal [(nzVisible)]="isVisible" [nzTitle]="'common.退回修改' | translate" (nzOnCancel)="isVisible = false" (nzOnOk)="handleOk()">
  <ng-container *nzModalContent>
    <nz-textarea-count [nzMaxCharacterCount]="255" class="inline-count">
      <textarea
        rows="4"
        [(ngModel)]="refuseReason"
        flcInputTrim
        [maxlength]="255"
        [placeholder]="translateName + '请输入原因' | translate"
        nz-input></textarea>
    </nz-textarea-count>
    <div *ngIf="isConfirm && !refuseReason" style="color: #fe352d; font-size: 14px">{{ translateName + '请输入原因' | translate }}</div>
  </ng-container>
  <div *nzModalFooter>
    <div class="btns" style="display: flex; justify-content: center">
      <button nz-button flButton="default" nzShape="round" (click)="isVisible = false" style="margin-right: 10px; color: #54607c">
        {{ 'flss.btn.cancel' | translate }}
      </button>
      <button nz-button nzType="primary" nzShape="round" [nzLoading]="comLoading" (click)="handleOk()">
        {{ 'flss.btn.ok' | translate }}
      </button>
    </div>
  </div>
</nz-modal>
<!-- 退回修改弹窗结束 -->
<app-bulk-print #bulkPrint [detail]="detail" (bomMaterialDescription)="onGetBomMaterialDescription($event)"> </app-bulk-print>

<!-- 打印唯一码 -->
<print-label-component
  [isVisible]="printVisible"
  [printTitle]="printTitle"
  (visibleCahnge)="printVisible = false"
  [printType]="printType"
  [bulk_order_card]="bulk_order_card"
  [selectedIds]="selectedIds"></print-label-component>
