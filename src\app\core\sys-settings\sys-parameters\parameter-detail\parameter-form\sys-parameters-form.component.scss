.params-section {
  margin-bottom: 24px;
  .section-title {
    font-size: 16px;
    font-weight: bold;
  }

  .ant-divider-horizontal {
    width: calc(100% + 8px);
    margin: 6px -4px 12px -4px;
    border-color: #d4d7dc;
  }

  .section-container {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;

    .section-sub-container {
      width: calc(50% - 8px);
      display: flex;
      flex-direction: column;

      &.text-sub-container {
        width: 100%;
      }

      .title {
        color: #515665;
      }

      .form-container {
        margin-top: 6px;

        .label {
          color: #4a5160;
        }

        nz-input-group,
        .ant-input,
        nz-input-number-group {
          width: 30%;
          min-width: 120px;
        }

        [nz-radio] {
          &.ant-radio-wrapper {
            margin-right: 24px;
            margin-bottom: 6px;
          }
          &.ant-radio-wrapper-checked {
            color: #138aff;
          }
        }
      }
    }
  }
}

:host ::ng-deep {
  .ant-input-group-wrapper-status-error .ant-input-group-addon,
  .ant-input-number-group-wrapper-status-error .ant-input-number-group-addon {
    color: #222b3c;
    border-color: #d9d9d9;
  }
}
