<section style="height: 100%">
  <div class="plan-back" #planBack>
    <div class="plan-wrap">
      <div row-no-start style="flex-shrink: 0">
        <div class="wrap-left-top" style="flex: 0 0 240px">
          <div class="table-checkbox">
            <label
              nz-checkbox
              [nzDisabled]="disableCheckbox"
              [(ngModel)]="checkboxConfig.allChecked"
              (ngModelChange)="updateAllChecked($event)"
              [nzIndeterminate]="checkboxConfig.indeterminate"></label>
          </div>
          <div class="factory-name">加工厂</div>
          <div class="line-name">产线</div>
        </div>
        <div style="flex-grow: 1" class="wrap-top" #wrapTop>
          <div class="wrap-top-scroll" #wrapTopScroll cdkScrollable [scrollLeft]="scrollLeft">
            <div row-no-start style="overflow: hidden" [ngStyle]="{ width: options.dayWidth * days.length + 'px' }">
              <i class="ward-icon" nz-icon [nzIconfont]="'icon-yanjing'" style="left: 0" (mouseup)="forward()"></i>
              <ng-container [ngSwitch]="options.dimension">
                <!-- 日维度 -->
                <ng-template [ngSwitchCase]="'day'">
                  <div
                    class="gantt-header-item"
                    *ngFor="let week of weeks"
                    [ngStyle]="{
                      width: options.signalWidth * week.children!.length + 'px',
                      'min-width': options.signalWidth * week.children!.length + 'px'
                    }">
                    <div class="gantt-header-item-top">
                      <flc-text-truncated [data]="getWeekData(week)"></flc-text-truncated>
                    </div>
                    <div style="display: flex">
                      <div
                        class="gantt-header-single gantt-header-single-day"
                        *ngFor="let day of week?.children"
                        [ngStyle]="{ width: options.signalWidth + 'px', 'min-width': options.signalWidth + 'px' }">
                        <div
                          style="display: flex; align-items: center; justify-content: center; gap: 2px"
                          [ngClass]="{ 'today-title-text': day.isToday }">
                          <div>{{ day.day }}</div>
                          <!-- <i nz-icon class="fold-icon" nz-tooltip [nzTooltipTitle]="'展开'" (click)="transformDimension('hour', day)">
                            <svg>
                              <path
                                d="M776.512 448l-46.912-46.912 59.712-59.776 149.376 149.376L789.312 640l-59.712-59.712 46.912-46.976H554.688V448h221.824zM247.488 448h221.824v85.312H247.488l46.912 46.976-59.712 59.712-149.376-149.312 149.376-149.376 59.712 59.776L247.488 448z"
                                fill="#218dfe"></path>
                            </svg>
                          </i> -->
                        </div>
                        <div *ngIf="day.isToday" row-no-center style="margin-top: -3px">
                          <div class="today-triangle"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-template>

                <!-- 小时维度 -->
                <ng-template [ngSwitchCase]="'hour'">
                  <div
                    class="gantt-header-item"
                    *ngFor="let day of days"
                    [ngStyle]="{ width: options.signalWidth * 12 + 'px', 'min-width': options.signalWidth * 12 + 'px' }">
                    <div
                      class="gantt-header-item-top gantt-header-item-top-hour"
                      [ngClass]="{ 'today-title-text': day.isToday }"
                      style="display: flex; gap: 10px; align-items: center">
                      {{ day.date | date: 'yyyy/MM/dd' }}
                      <i nz-icon class="fold-icon" nz-tooltip [nzTooltipTitle]="'收起'" (click)="transformDimension('day', day)">
                        <svg>
                          <path
                            d="M716.8 448l46.912-46.912L704 341.312 554.688 490.688 704 640l59.712-59.712-46.912-46.976h221.888V448H716.8zM307.2 448H85.312v85.312H307.2l-46.912 46.976L320 640l149.312-149.312L320 341.312l-59.712 59.776L307.2 448z"
                            fill="#218dfe"></path>
                        </svg>
                      </i>
                    </div>
                    <div style="display: flex">
                      <div
                        class="gantt-header-single"
                        *ngFor="let hour of hours"
                        [ngStyle]="{ width: options.signalWidth + 'px', 'min-width': options.signalWidth + 'px' }">
                        <div style="display: flex; align-items: center; justify-content: center">
                          {{ hour }}
                        </div>
                      </div>
                    </div>
                    <div *ngIf="day.isToday" row-no-center style="margin-top: -3px">
                      <div class="today-triangle"></div>
                    </div>
                  </div>
                </ng-template>
              </ng-container>
              <i
                class="ward-icon"
                nz-icon
                [nzIconfont]="'icon-jiantou'"
                style="position: relative; margin-left: -11px"
                (mouseup)="backwards()"></i>
            </div>
          </div>
          <button
            nz-button
            [ngClass]="{ 'today-btn-disable': showTodayBtn(), 'to-today-btn': !showTodayBtn() }"
            nz-tooltip
            [nzTooltipTitle]="showTodayBtn() ? '' : '点击后,定位今日到当前屏幕'"
            (click)="toToday()"
            [disabled]="showTodayBtn()">
            返回今天
          </button>
        </div>
      </div>

      <div row-no-start style="overflow: hidden; flex-grow: 1">
        <div class="wrap-left" style="flex: '0 0 240px'" #wrapLeft cdkScrollable [scrollTop]="scrollTop" (scroll)="onLeftScroll($event)">
          <ng-content select="app-io-wrap"></ng-content>
        </div>

        <div
          style="flex-grow: 1"
          class="wrap-body"
          cdkScrollable
          #wrapBody
          (scroll)="onBodyScroll($event)"
          [scrollTop]="scrollTop"
          [scrollLeft]="scrollLeft">
          <div class="wrap-body-inner" [ngStyle]="{ width: options.dayWidth * days.length + 'px' }">
            <ng-content select="app-graph-wrap-bg"></ng-content>
            <ng-content select="app-graph-wrap"></ng-content>
          </div>
        </div>
      </div>
    </div>

    <div class="modal-out-overlay" #overlay>
      <div class="mask" *ngIf="maskVisible" (click)="closeMask($event)"></div>
    </div>
  </div>
</section>
