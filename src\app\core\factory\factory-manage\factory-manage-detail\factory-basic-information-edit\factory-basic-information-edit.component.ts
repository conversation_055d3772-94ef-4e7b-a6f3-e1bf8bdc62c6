import { isNil } from 'ng-zorro-antd/core/util';
import { Component, OnInit } from '@angular/core';
import { CoopOptions, FactoryDetail } from '../../interface/factory-config';
import { TranslateService } from '@ngx-translate/core';
import { FactoryManageDetailService } from '../factory-manage-detail.service';
import { CommonService } from '../../../../../shared/common.service';
import { Region } from '../../interface';
import { forkJoin } from 'rxjs';
import { Validators } from '@angular/forms';

@Component({
  selector: 'app-factory-basic-information-edit',
  templateUrl: './factory-basic-information-edit.component.html',
  styleUrls: ['./factory-basic-information-edit.component.scss'],
})
export class FactoryBasicInformationEditComponent implements OnInit {
  detailConfig: any;
  placeInput!: string;
  placeSelect!: string;
  coopOptions = CoopOptions;
  regionOptions = [];
  deployRegionOptions = [];
  factoryOptions: { label: string | null; value: string | null }[] = [];

  constructor(
    private _translateService: TranslateService,
    public _service: FactoryManageDetailService,
    private _commonService: CommonService
  ) {}

  ngOnInit(): void {
    this.detailConfig = FactoryDetail;
    this.placeInput = this._translateService.instant('placeholder.input');
    this.placeSelect = this._translateService.instant('placeholder.select');
    this.getOptions();
  }

  getOptions() {
    forkJoin({
      deployRegion: this._service.getDeployRegion(),
      region: this._commonService.getRegion({}),
      factorys: this._service.getElanFactorys(),
    }).subscribe((res) => {
      if (res.region.code === 200) {
        this.regionOptions = res.region.data;
      }
      if (res.deployRegion.code === 200) {
        this.deployRegionOptions = res.deployRegion.data.option_list;
      }
      if (res.factorys.code === 200) {
        this.factoryOptions = res.factorys.data.map((e: any) => {
          return { label: e.name, value: e.factoryCode };
        });
      }
    });
  }

  regionChange(e: []) {
    const data = this.getRegionCascadeData(e, this.regionOptions);
    if (data.length) {
      const region: Region = {
        province: {
          name: data[0].label,
          id: data[0].value,
        },
        city: {
          name: data[1].label,
          id: data[1].value,
        },
      };
      this._service.factoryForm?.get('region')?.setValue(region);
    }
  }

  // 在级联数据中获取地区的数据
  getRegionCascadeData(list: number[], cascadeOptions: [any][any]) {
    const data: any = [];
    cascadeOptions
      .filter((top: any) => top.value === list[0])
      .forEach((topItem: any) => {
        const { children, ...a } = topItem;
        data.push({ ...a });
        children
          .filter((mid: any) => mid.value === list[1])
          .forEach((midItem: any) => {
            const { ...b } = midItem;
            data.push({ ...b });
          });
      });
    return data;
  }

  numberChange(e: string | number, name: string) {
    if (isNil(e) || e === '') {
      this._service.factoryForm?.get(name)?.setValue(null);
    }
  }

  onDeployRegionChange(e: any) {
    const options: { label: string; value: string }[] = this.deployRegionOptions.filter((opt: any) => opt.value === e);
    this._service.factoryForm?.get('deploy_region_name')?.setValue(options.length > 0 ? options[0].label : null);
  }
  factoryTypeValueChanged() {
    const value = this._service.factoryForm?.get('factory_type')?.value;
    if (value === 2) {
      this._service.factoryForm?.get('elan_code')?.setValidators([Validators.required]);
    } else {
      this._service.factoryForm?.get('elan_code')?.setValidators(null);
    }
  }
}
