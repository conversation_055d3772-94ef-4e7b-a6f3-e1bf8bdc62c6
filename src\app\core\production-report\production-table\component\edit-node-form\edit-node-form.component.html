<div class="wrap">
  <form nz-form [formGroup]="validateForm" style="margin-bottom: 60px">
    <ng-container *ngIf="!isBatch">
      <nz-form-item *ngFor="let item of basicInfoConfig">
        <nz-form-label class="info-label"> {{ item?.label }} </nz-form-label>
        <nz-form-control>
          <flc-text-truncated [data]="orderInfo?.[item.key]"></flc-text-truncated>
        </nz-form-control>
      </nz-form-item>
    </ng-container>
    <!-- 模版字段 -->
    <ng-container *ngFor="let item of formConfig">
      <nz-form-item style="margin-bottom: 16px">
        <nz-form-label [nzRequired]="item.required" class="info-label" [nzSpan]="24">
          {{ item?.label }}：

          <span class="plan-label" *ngIf="item.tag === FieldLabelEnum.Plan">{{ translateName + '计划' | translate }}</span>
          <span class="actual-label" *ngIf="item.tag === FieldLabelEnum.Actual">{{ translateName + '实际' | translate }}</span>
        </nz-form-label>

        <!-- 可修改：批量编辑 或 单个编辑时节点可编辑&不是自动取值字段&（字段权限维度下：字段可编辑）-->
        <nz-form-control
          [nzSpan]="24"
          [flcErrorTip]="item?.label"
          *ngIf="
            isBatch
              ? true
              : node?.edit_auth === NodeEditAuthEnum.edit &&
                item?.is_edit === FieldEditAuthEnum.edit &&
                (node?.authority_type === AuthorityTypeEnum.field ? item?.edit_auth === FieldAuthEnum.edit : true)
          ">
          <!-- 文本框：节点填报时为字符输入框，限制50字； -->
          <ng-container *ngIf="item.type === FieldTypeEnum.TextBox">
            <nz-input-group [nzSuffix]="(validateForm.get(item.key)?.value?.length ?? 0) + '/' + 50">
              <input nz-input flcInputTrim [formControlName]="item.key" [placeholder]="'placeholder.input' | translate" [maxlength]="50" />
            </nz-input-group>
          </ng-container>

          <ng-container *ngIf="[FieldTypeEnum.SingleChoice, FieldTypeEnum.MultipleChoice].includes(item.type)">
            <nz-select
              [nzAllowClear]="true"
              [nzShowSearch]="true"
              [formControlName]="item.key"
              [nzMode]="item?.type === FieldTypeEnum.MultipleChoice ? 'multiple' : 'default'"
              [nzPlaceHolder]="'placeholder.select' | translate">
              <nz-option *ngFor="let op of item?.options || []" [nzLabel]="op?.label" [nzValue]="op.value"></nz-option>
            </nz-select>
          </ng-container>

          <ng-container *ngIf="item.type === FieldTypeEnum.Attachment">
            <div style="min-height: 80px">
              <flc-file-gallery
                #fileGalleryRef
                [wrap]="false"
                [isEditMode]="true"
                [needWatermark]="true"
                [addBtnText]="translateName + '上传附件' | translate"
                [fileList]="validateForm.get(item.key)?.value"
                [fileMaxSize]="100"
                [fileMaxCount]="100"
                [galleryType]="'any'"
                (onUploaded)="onUploaded($event, item)"
                (onDeleted)="onDeleted($event, item)"></flc-file-gallery>
            </div>
            <div
              style="color: #fe5d56; font-size: 12px; margin-top: 10px"
              *ngIf="item.required && isSave && validateForm.get(item.key)?.status === 'INVALID'">
              {{ item?.label }} {{ translateName + '不可为空' | translate }}
            </div>
          </ng-container>

          <!-- 节点填报时为数值型输入框，支持负数、0、正数，支持9整5小； -->
          <ng-container *ngIf="item.type === FieldTypeEnum.Number">
            <nz-input-number
              style="width: 100%"
              [formControlName]="item.key"
              [nzPlaceHolder]="'placeholder.input' | translate"
              [nzMax]="999999999.99999"
              [nzPrecision]="5">
            </nz-input-number>
          </ng-container>

          <ng-container *ngIf="item.type === FieldTypeEnum.Date">
            <nz-date-picker
              [nzFormat]="'yyyy/MM/dd'"
              [nzPlaceHolder]="'placeholder.select' | translate"
              [formControlName]="item.key"></nz-date-picker>
          </ng-container>

          <ng-container *ngIf="item.type === FieldTypeEnum.Percentage">
            <span> {{ getPercent(item) ?? '-' }}% </span>
          </ng-container>
        </nz-form-control>

        <!-- 不可修改 : 单个编辑且 节点不可编辑｜自动取值字段｜（字段权限维度下，字段不可编辑）-->
        <nz-form-control
          [nzSpan]="24"
          [flcErrorTip]="item?.field_name"
          *ngIf="
            !isBatch &&
            (node?.edit_auth === NodeEditAuthEnum.review ||
              item?.is_edit === FieldEditAuthEnum.read ||
              (node?.authority_type === AuthorityTypeEnum.field ? item?.edit_auth === FieldAuthEnum.review : false))
          ">
          <ng-container *ngIf="item.type === FieldTypeEnum.Attachment; else otherReadTpl">
            <flc-file-gallery
              [wrap]="false"
              [isEditMode]="false"
              [needWatermark]="true"
              [addBtnText]="translateName + '上传附件' | translate"
              [fileList]="validateForm.get(item.key)?.value || []"
              [fileMaxSize]="100"
              [fileMaxCount]="100"
              [galleryType]="'any'"></flc-file-gallery>
          </ng-container>

          <ng-template #otherReadTpl>
            <span *ngIf="item.type === FieldTypeEnum.Date">
              {{ validateForm.get(item.key)?.value ? (validateForm.get(item.key)?.value | date: 'yyyy/MM/dd') : '-' }}
            </span>
            <span *ngIf="item.type !== FieldTypeEnum.Date">
              {{ validateForm.get(item.key)?.value || '-' }} {{ item.type === FieldTypeEnum.Percentage ? '%' : '' }}
            </span>
          </ng-template>
        </nz-form-control>
      </nz-form-item>
    </ng-container>
  </form>

  <div class="action-btn-container" *ngIf="isBatch || node?.edit_auth === NodeEditAuthEnum.edit">
    <button nz-button flButton="default" [nzShape]="'round'" (click)="onCancel()">{{ 'btn.cancel' | translate }}</button>
    <button nz-button nzType="primary" [nzShape]="'round'" (click)="onSave()" [flcDisableOnClick]="1000" flcErrorScroll>
      {{ 'btn.save' | translate }}
    </button>
  </div>
</div>
