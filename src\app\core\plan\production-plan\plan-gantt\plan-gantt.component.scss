.header-left-txt {
  font-size: 14px;
  font-weight: 500;
  color: #6c7799;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.wrap {
  height: calc(100% - 44px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.graph-container {
  flex-grow: 1;
  overflow: hidden;
  box-shadow: 0px 2px 4px 1px rgb(203 214 226 / 50%);
  z-index: 1;
}
.detailBar {
  flex-shrink: 0;
  height: 56px;
  width: 100%;
  background: #ddebfc;
  box-shadow: 0px 2px 4px 1px rgba(162, 169, 186, 0.5);
  border-radius: 0px 0px 10px 10px;
  display: flex;
  flex-direction: row;
  padding: 0px 8px;
  column-gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}
.detailItem {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 500;
  color: #272e45;

  &.shrink {
    flex-shrink: 1;
    .itemTitle {
      word-break: keep-all;
    }
  }
  &.IoBar {
    font-size: 16px;
    display: flex;
    align-items: center;
    white-space: nowrap;
  }

  .itemValue {
    max-width: 350px;

    .multiply-temp {
      background: #f2f9ff;
      border-radius: 4px;
      text-align: center;
      padding: 0px 4px;
      margin-inline-end: 6px;
    }
  }
}
