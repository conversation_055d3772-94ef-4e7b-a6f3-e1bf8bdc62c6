<div class="recommend-set-list">
  <div class="header">
    <span class="title">加工厂推荐配置</span>
    <div>
      <button nz-button nzDanger nzType="default" nzShape="round" (click)="onBatchDelete()">删除</button>
      <button
        nz-button
        nzType="primary"
        nzShape="round"
        nz-popover
        nzPopoverTitle=""
        nzPopoverTrigger="click"
        [nzPopoverContent]="PopoverTpl"
        nzPopoverPlacement="bottom"
        nzPopoverOverlayClassName="recommend_element_popover">
        <span nz-icon nzType="plus" nzTheme="outline"></span>
        <span>推荐元素</span>
        <span nz-icon nzType="down" nzTheme="outline"></span>
      </button>
    </div>

    <ng-template #PopoverTpl>
      <app-recommend-element [alreadyAdd]="alreadyAdd" (addRecommend)="handelAdd($event)"></app-recommend-element>
    </ng-template>
  </div>

  <nz-table
    #nzTable
    [nzData]="listOfData"
    [nzFrontPagination]="false"
    [nzScroll]="{ x: '100%', y: 'calc(100vh - 160px)' }"
    nzBordered="true">
    <thead>
      <tr>
        <th nzWidth="55px" [nzChecked]="checked" [nzIndeterminate]="indeterminate" (nzCheckedChange)="onAllChecked($event)"></th>
        <th>推荐元素</th>
        <th>元素权重（%）</th>
        <th>备注</th>
        <th nzWidth="55px">操作</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let item of nzTable.data; let i = index; last as isLast" [ngClass]="isLast ? 'last-element-item' : ''">
        <td [nzChecked]="setOfChecked.has(item.key)" (nzCheckedChange)="onItemChecked(item.key, $event)"></td>
        <td><flc-text-truncated [data]="recommendElementObj[item.key]?.name"></flc-text-truncated></td>
        <td>
          <nz-input-number
            nzPlaceHolder="请输入"
            [nzMin]="0"
            [nzMax]="100"
            [nzPrecision]="0"
            style="width: 100%"
            [(ngModel)]="editCache[item.key].value"
            (ngModelChange)="maxValidate(item.key, i)"
            (nzBlur)="handelNumberBlur(item.key, i)">
          </nz-input-number>
        </td>
        <td>
          <textarea
            nz-input
            rows="1"
            maxlength="50"
            flcInputTrim
            placeholder="请输入"
            [(ngModel)]="editCache[item.key].remark"
            (ngModelChange)="handelTextChange(item.key)"></textarea>
        </td>
        <td>
          <i
            nz-icon
            nz-popconfirm
            style="color: red; cursor: pointer"
            nzPopconfirmPlacement="right"
            [nzIconfont]="'icon-caozuolan_shanchu1'"
            [nzPopconfirmTitle]="'确定删除?'"
            (nzOnConfirm)="handleDelete([item.key])">
          </i>
        </td>
      </tr>
    </tbody>
  </nz-table>
</div>
