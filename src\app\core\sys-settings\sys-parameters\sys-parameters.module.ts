import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FlcComponentsModule, FlcDirectivesModule, FlcPipesModule } from 'fl-common-lib';
import { FlButtonModule } from 'fl-ui-angular';
import { NzFormModule } from 'ng-zorro-antd/form';
import { SysParametersRoutingModule } from './sys-parameters-routing.module';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import { SysParametersDetailComponent } from './parameter-detail/sys-parameters-detail.component';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { SysParametersFormComponent } from './parameter-detail/parameter-form/sys-parameters-form.component';
import { SysParametersService } from './sys-parameters.service';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzSelectModule } from 'ng-zorro-antd/select';

const ngModules = [CommonModule, FormsModule, ReactiveFormsModule];
const nzModules = [
  NzIconModule,
  NzFormModule,
  NzButtonModule,
  NzDividerModule,
  NzInputModule,
  NzRadioModule,
  NzInputNumberModule,
  NzSelectModule,
];
const flCommonModules = [FlButtonModule, FlcComponentsModule, FlcPipesModule, FlcDirectivesModule];

@NgModule({
  imports: [
    ...ngModules,
    ...nzModules,
    ...flCommonModules,
    SysParametersRoutingModule,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new MultiTranslateHttpLoader(http, [{ prefix: './assets/i18n/sys-settings/', suffix: '.json' }]);
        },
        deps: [HttpClient],
      },
    }),
  ],
  exports: [],
  declarations: [SysParametersDetailComponent, SysParametersFormComponent],
  providers: [SysParametersService],
})
export class SysParametersModule {
  constructor(public translateService: TranslateService) {
    const cl = translateService.currentLang;
    translateService.currentLang = '';
    translateService.use(cl);
  }
}
