{"name": "elan-web", "version": "25.11.0-ddhy", "scripts": {"ng": "ng", "start": "ng serve --proxy-config proxy.config.json --port=4800 --host 0.0.0.0", "build": "node --max_old_space_size=8192 node_modules/@angular/cli/bin/ng build", "build:flkj": "ng build --configuration production", "build:tianyuan": "ng build --configuration production-ty", "build:dashu": "ng build --configuration production-ty", "watch": "ng build --watch --configuration development", "link-lib": "node scripts/link_lib.js", "unlink-lib": "node scripts/unlink_lib.js ", "link-lib-start": "node --max_old_space_size=8000 ./node_modules/@angular/cli/bin/ng serve --proxy-config proxy.config.json  --configuration development-link --port=4800 --host 0.0.0.0", "test": "ng test", "lint": "ng lint", "prepare": "husky install", "install-internal-lib": "npm install fl-common-lib@25.11.0-ddhy fl-sewsmart-lib@25.11.0-ddhy", "uninstall-internal-lib": "npm uninstall fl-common-lib fl-sewsmart-lib"}, "private": true, "dependencies": {"@angular/animations": "~13.1.2", "@angular/cdk": "~13.1.2", "@angular/common": "~13.1.2", "@angular/compiler": "~13.1.2", "@angular/core": "~13.1.2", "@angular/forms": "~13.1.2", "@angular/platform-browser": "~13.1.2", "@angular/platform-browser-dynamic": "~13.1.2", "@angular/router": "~13.1.2", "@ant-design/icons-angular": "^13.0.2", "@antv/x6": "2.18.1", "@iplab/ngx-color-picker": "^3.0.1", "@ngx-translate/core": "^14.0.0", "@ngx-translate/http-loader": "^7.0.0", "@techiediaries/ngx-qrcode": "^9.1.0", "@tinymce/tinymce-angular": "^6.0.1", "@types/jsbarcode": "^3.11.4", "ag-grid-angular": "^30.2.1", "ag-grid-community": "^30.2.1", "crypto-js": "^4.1.1", "date-fns": "^2.25.0", "echarts": "^5.2.2", "fl-ui-angular": "1.5.2", "html2canvas": "^1.4.1", "jsbarcode": "^3.11.6", "jspdf": "^2.5.1", "jspdf-autotable": "3.5.24", "lodash": "4.17.21", "ng-zorro-antd": "~13.4.0", "ngx-echarts": "^8.0.0", "ngx-sortablejs": "^11.1.0", "ngx-translate-multi-http-loader": "^7.0.5", "rxjs": "~7.5.2", "sortablejs": "^1.14.0", "timers": "0.1.1", "tinymce": "^6.5.1", "tslib": "^2.3.0", "uuid": "^9.0.0", "xml2js": "0.6.2", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "13.2.1", "@angular-eslint/builder": "13.0.1", "@angular-eslint/eslint-plugin": "13.0.1", "@angular-eslint/eslint-plugin-template": "13.0.1", "@angular-eslint/schematics": "13.0.1", "@angular-eslint/template-parser": "13.0.1", "@angular/cli": "13.2.1", "@angular/compiler-cli": "13.1.2", "@types/ali-oss": "^6.16.4", "@types/crypto-js": "^4.1.1", "@types/jasmine": "~3.8.0", "@types/lodash": "4.17.2", "@types/lodash-es": "^4.17.12", "@types/node": "^12.11.1", "@types/qrcode": "^1.5.5", "@types/quill": "^1.3.10", "@types/sortablejs": "^1.10.7", "@types/uuid": "^9.0.2", "@types/xml2js": "0.4.14", "@typescript-eslint/eslint-plugin": "^5.1.0", "@typescript-eslint/parser": "^5.1.0", "ali-oss": "^6.5.1", "eslint": "^8.6.0", "husky": "^7.0.4", "jasmine-core": "~3.8.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "lint-staged": "^11.2.6", "ng-packagr": "^13.1.3", "ngx-printer": "~1.1.0", "ngx-quill": "^16.1.2", "prettier": "2.4.1", "quill": "^1.3.7", "typescript": "~4.5.4"}, "lint-staged": {"./src/**/*.ts": "eslint --fix", "*.{ts,js,css,scss,md,html}": "prettier --write"}}