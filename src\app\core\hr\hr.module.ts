import { NzDividerModule } from 'ng-zorro-antd/divider';
import { ComponentsModule } from 'src/app/components/components.module';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HrRoutingModule } from './hr-routing.module';
import { OrganizationComponent } from './organization/organization.component';
import { StructureTreeComponent } from './organization/structure-tree/structure-tree.component';
import { NzTreeModule } from 'ng-zorro-antd/tree';
import { NzInputModule } from 'ng-zorro-antd/input';
import { FormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzResizableModule } from 'ng-zorro-antd/resizable';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { NzIconModule, NzIconService } from 'ng-zorro-antd/icon';
import { HttpClient } from '@angular/common/http';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { FlUiAngularModule } from 'fl-ui-angular';
import { StructureInfoComponent } from './organization/structure-info/structure-info.component';
import { DepartmentInfoComponent } from './organization/structure-info/department-info/department-info.component';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzFormModule } from 'ng-zorro-antd/form';
import { DepartmentTableComponent } from './organization/structure-info/department-table/department-table.component';
import { NzDrawerModule, NzDrawerService } from 'ng-zorro-antd/drawer';
import { EmployeeDrawerComponent } from './organization/employee-drawer/employee-drawer.component';
import { PipesModule } from 'src/app/pipes/pipes.module';
import { NzPipesModule } from 'ng-zorro-antd/pipes';
import { NzTimePickerModule } from 'ng-zorro-antd/time-picker';
import { AccountDrawerComponent } from './organization/account-drawer/account-drawer.component';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { DeleteModalComponent } from './organization/delete-modal/delete-modal.component';
import { SubdepartModalComponent } from './organization/subdepart-modal/subdepart-modal.component';
import { EmployeeListComponent } from './employee/list/employee-list.component';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzUploadModule } from 'ng-zorro-antd/upload';
import { UtilService } from 'src/app/shared/util.service';
import { DirectivesModule } from 'src/app/directive/directives.module';
import { ShiftSettingComponent } from './organization/shift-setting/shift-setting.component';
import { OrganizationService } from './organization/organization.service';
import { WorkFrequenciesComponent } from './organization/work-frequencies/work-frequencies.component';
import { FlcComponentsModule, FlcOssUploadService } from 'fl-common-lib';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
@NgModule({
  declarations: [
    OrganizationComponent,
    StructureTreeComponent,
    StructureInfoComponent,
    DepartmentInfoComponent,
    DepartmentTableComponent,
    EmployeeDrawerComponent,
    AccountDrawerComponent,
    DeleteModalComponent,
    SubdepartModalComponent,
    EmployeeListComponent,
    ShiftSettingComponent,
    WorkFrequenciesComponent,
  ],
  imports: [
    CommonModule,
    HrRoutingModule,
    NzTreeModule,
    NzInputModule,
    FormsModule,
    NzResizableModule,
    FlcComponentsModule,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new MultiTranslateHttpLoader(http, [
            { prefix: './assets/i18n/hr/', suffix: '.json' },
            { prefix: './assets/i18n/employee/', suffix: '.json' },
          ]);
        },
        deps: [HttpClient],
      },
    }),
    NzIconModule,
    NzButtonModule,
    FlUiAngularModule,
    NzPopconfirmModule,
    NzFormModule,
    NzSelectModule,
    NzTableModule,
    NzDrawerModule,
    ReactiveFormsModule,
    NzPipesModule,
    PipesModule,
    NzDatePickerModule,
    ComponentsModule,
    NzSwitchModule,
    NzDividerModule,
    DragDropModule,
    NzSpinModule,
    NzToolTipModule,
    NzCheckboxModule,
    NzModalModule,
    NzUploadModule,
    NzTimePickerModule,
    DirectivesModule,
    NzRadioModule,
  ],
  providers: [
    NzDrawerService,
    NzMessageService,
    NzNotificationService,
    NzModalService,
    UtilService,
    FormBuilder,
    OrganizationService,
    FlcOssUploadService,
  ],
})
export class HrModule {
  constructor(public translateService: TranslateService, private iconService: NzIconService) {
    // 切换lang必须刷新页面，模块级别i18n文件才能成功加载
    const dl = translateService.defaultLang;
    translateService.defaultLang = '';
    translateService.setDefaultLang(dl);

    this.iconService.fetchFromIconfont({
      scriptUrl: 'https://at.alicdn.com/t/font_2782026_gkrcufahyqc.js',
    });
  }
}
