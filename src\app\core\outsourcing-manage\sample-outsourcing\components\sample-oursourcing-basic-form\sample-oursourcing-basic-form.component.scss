.form-item {
  display: flex;
  margin-bottom: 16px;

  .info-label {
    display: inline-block;
    width: 110px;
    text-align: right;
  }

  .info-control {
    width: calc(100% - 110px);
    font-weight: 500;
    color: #222b3c;
  }

  .item-value {
    display: flex;
    align-items: center;
    color: #222b3c;
    div {
      word-break: break-all;
      white-space: pre-line;
      line-break: anywhere;
    }
  }

  .tag {
    background: #e7f3fe;
    border-radius: 4px;
    color: #007aff;
    font-size: 12px;
    font-weight: 500;
    height: 22px;
    line-height: 22px;
    padding: 0 6px;
    white-space: nowrap;
    margin-left: 4px;
  }
}

.address-info {
  display: flex;
  align-items: flex-start;
  nz-cascader {
    width: 200px;
    margin-right: 4px;
  }
  .inline-count {
    flex: 1;
  }
}

flc-dynamic-search-select {
  flex: 1;
  min-width: 0;
  width: 100%;
}

:host::ng-deep {
  .ant-form-item-label > label {
    font-weight: 500;
    color: #515665;
  }
}

.recommend-btn {
  background: linear-gradient(#2becb6, #00c790) !important;
  border-width: 0;
}
