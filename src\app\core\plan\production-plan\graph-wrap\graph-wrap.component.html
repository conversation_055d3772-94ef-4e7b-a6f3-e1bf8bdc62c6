<div
  class="graph-wrap"
  #graphWrap
  [ngStyle]="{ height: data.height + 'px' }"
  [ngClass]="{ 'graph-wrap-selected': data.selected, 'is-highlight': data?.isHighLight }"
  row-no-start>
  <ng-container [ngSwitch]="options.dimension">
    <ng-template [ngSwitchCase]="'day'">
      <ng-container *ngFor="let week of weeks; index as i">
        <div
          class="day-cell"
          style="flex-shrink: 0"
          [ngStyle]="{ width: options.signalWidth + 'px' }"
          [ngClass]="{
            'is-highlight': data?.isHighLight,
            'is-last-day': last
          }"
          *ngFor="let day of week?.children; index as index; last as last"
          row-no-center>
          <div class="day-today" *ngIf="day.isToday"></div>
        </div>
      </ng-container>
    </ng-template>
    <ng-template [ngSwitchCase]="'hour'">
      <ng-container *ngFor="let day of days; index as i">
        <div
          class="day-cell"
          style="flex-shrink: 0"
          [ngStyle]="{ width: options.signalWidth + 'px' }"
          [ngClass]="{
            'is-highlight': data?.isHighLight,
            'day-today': day.isToday && hour === 12,
            'is-last-hour': hour === 24
          }"
          *ngFor="let hour of hours; index as hoursIndex"
          row-no-center></div>
      </ng-container>
    </ng-template>
  </ng-container>
  <ng-container *ngFor="let line of lineList; index as i">
    <app-plan-graph-item
      [data]="line"
      [options]="options"
      [exitClickable]="exitClickable"
      (tapGraphItem)="tapGraph($event, i)"
      (leaveGraphItem)="leaveGraph()"
      (hoverGraphItem)="hoverGraph($event, i)">
    </app-plan-graph-item>
  </ng-container>
</div>
