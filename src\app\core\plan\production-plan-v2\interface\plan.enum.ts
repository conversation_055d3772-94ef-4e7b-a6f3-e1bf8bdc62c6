/**
 * 甘特图形的种类，不同于Stage {@link Stage}
 * 图形的种类除了几个阶段，还需要提现出实际生产和逾期情况
 */
export enum GraphType {
  prepare = 0, // 产前
  cutting = 1, // 裁剪
  extra = 2, // 二次工艺
  PPA = 3, // PPA
  sewing = 4, // 车缝
  consolidation = 5, // 后整
  logistics = 6, // 物流
  actual = 7, // 实际
  alert = 8, // 逾期预警
  overdue = 9, // 已经逾期
  occupied = 10, // 插单的
  overlapped = 11, // 产能重叠的
}

export enum PlanStatus {
  unpublished = 1, // 待发布
  published = 2, // 已发布
}

export enum PlanAlertStatusEnum {
  overdue = 1, // 逾期
  backward = 2, // 进度落后
  surpass = 3, // 计划超客期
}

export enum GanttCellWidth {
  day = 48,
  hour = 24,
}

export type DimensionType = 'day' | 'hour';

export enum mouseType {
  click = 1, // 点击
  hover = 2, // 悬停
  rightClick = 3, // 右键
}

export type OrderDimensionType = 'io' | 'po';

export enum PlanGraphActionTypeEnum {
  default,
  split, // 快速拆分
  forwardMerge, // 向前合并
  forwardConnect, // 向前衔接
  AllConnect, // 整体向前衔接
  cancel, // 取消分配
  detail, // 查看详情
  replace, // 替换订单
}

export enum PlanGraphOperationTypeEnum {
  moveLength = 1, // 拖动长度
  movePosition = 2, // 拖动位置
}
