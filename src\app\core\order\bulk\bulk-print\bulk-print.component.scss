.heard-box {
  height: 82px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: bold;
  position: relative;
  background: #fff;
  border-bottom: 1px solid #0a0a0a;
  .img-box {
    position: absolute;
    top: 0;
    left: 0;
    width: 80px;
    height: 80px;
  }
  .department {
    font-size: 16px;
    font-weight: 500;
    position: absolute;
    bottom: 4px;
    right: 8px;
  }
}
.basic-info {
  justify-content: center;
  border-right: 1px solid;
  div {
    border-left: 1px solid;
    border-bottom: 1px solid;
    min-height: 32px;
    word-break: break-all;
    text-align: center;
    line-height: 32px;
  }
  .lang-td {
    text-align: left;
    padding-left: 8px;
  }
}
.bottom-basci-info {
  & > div {
    border: 1px solid;
  }
  & > div:first-child {
    border-right: none;
  }
}
.bottom-basci-info-2 {
  min-height: 150px;
  border-left: 1px solid;
  border-bottom: 1px solid;
  .packaging-box {
    height: 100%;
    & > div {
      border-right: 1px solid;
      padding-left: 8px;
      padding-top: 8px;
    }
  }
}
:host ::ng-deep .bulk-print-0001 {
  .ant-table-tbody > tr > td {
    border-bottom: 1px solid #242b3b;
    border-right: 1px solid #242b3b !important;
  }
  .ant-table-thead > tr > th {
    border-bottom: 1px solid #242b3b;
    border-right: 1px solid #242b3b !important;
  }
  .ant-table-cell {
    border-right: 1px solid #242b3b !important;
  }
}

.basic-info div {
  padding: 5px;
}
.noBreak {
  break-inside: avoid;
}
.info-label {
  width: 50px;
  writing-mode: vertical-lr;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #d8d8d8;
  letter-spacing: 8px;
}
.img-box {
  width: 128px;
  height: 128px;
  object-fit: contain;
}
