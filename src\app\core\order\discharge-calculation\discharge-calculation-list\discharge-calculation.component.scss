/*
** 页面结构： header + app-content
** 设置app-content最小高度
*/
:host ::ng-deep .app-content {
  min-height: calc(100vh - 124px);
}

.capacity-search-box {
  nz-select {
    width: 100px;
  }
}

.plan-select-option {
  max-width: 240px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.bulk-table-box {
  margin-top: 10px;
}

.update-tip {
  font-size: 9px;
  position: absolute;
  top: 0px;
  background: #4d96ff;
  color: white;
  margin-left: -8px;
  padding: 0 3px;
  border-radius: 0 0 5px 0;
}

.cancel-tip {
  font-size: 9px;
  position: absolute;
  top: 0px;
  background: #d4d7dc;
  color: #222b3c;
  margin-left: -8px;
  padding: 0 3px;
  border-radius: 0 0 5px 0;
  z-index: -1;
}
