.example-content {
  .example-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 40px;
    font-size: 12px;
    font-weight: 500;
    color: #4a5160;

    .example-switch {
      margin: 0;
      padding: 0;
      transform: scale(0.8);
    }
  }
}

:host ::ng-deep {
  .app-title-bar {
    margin-bottom: 0px !important;
  }

  .left-title-bar {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 12px;
    font-weight: 500;
    color: #4a5160;
    margin: 8px 0;

    & > div {
      display: flex;
      align-items: center;
    }

    .title {
      font-size: 18px;
      font-weight: 500;
      color: #515665;
      margin-left: 12px;
    }

    .scheduled-icon {
      display: inline-block;
      width: 14px;
      height: 14px;
      background: #d4d7dc;
      border-radius: 5px;
      margin-right: 4px;
    }

    .example-btn {
      font-size: 12px;
      font-weight: 500;
      color: #4a5160;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
        color: #2996ff;
      }
    }
  }

  .right-content {
    display: flex;
    padding-right: 8px;
  }
}
