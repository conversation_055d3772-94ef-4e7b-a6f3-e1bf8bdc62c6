import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

type InputData = {
  page: number;
  limit: number;
  keyword?: string;
  handle_status: 1 | 2; //1- 待办 2-已办
};

@Injectable({
  providedIn: null,
})
export class DashboardService {
  constructor(private _http: HttpClient) {}
  getMessageList(data: InputData) {
    return this._http.post<any>('/service/procurement-inventory/message-notify/v1/list', data);
  }
  markMessageRead(messageIds: number[]) {
    return this._http.post<any>('/service/procurement-inventory/message-notify/v1/read', { message_id: messageIds });
  }
}
