export interface Factories {
  factories: FactoryListData[];
  len: number;
}

export interface FactoryListData {
  id: number;
  code: string;
  name: string;
  abbr: string;
  domain: string;
  region: Region;
  status: number;
  coop: string;
  scale: number;
  sewing_workers: number;
  category: string;
  customer: string;
  contacts: string;
  tel: string;
  email: string;
  address: string;
  reason: string;
  service: string;
  gen_time: string;
  modified_time: string;
  gen_user: string;
  modify_user: string;
  pinned: boolean;
}

export interface Region {
  province: City;
  city: City;
}

export interface City {
  id: number;
  name: string;
}

export interface SearchParam {
  where: object;
  orderBy: string[];
  page: number;
  limit: number;
}

export interface SearchData {
  name: string | null;
  region_id: string | null;
  abbr: string | null;
  status: string | null;
  gen_user_id: number | null;
  gen_time: string[] | null;
}
