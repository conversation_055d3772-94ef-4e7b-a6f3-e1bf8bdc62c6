<div
  *ngIf="isLoading"
  style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; position: absolute; z-index: 900">
  <nz-spin [nzSpinning]="true"></nz-spin>
</div>
<div
  class="wrap"
  [ngStyle]="{
    opacity: isLoading ? '0.5' : '1'
  }">
  <div class="graph-container">
    <app-graph-table
      [date]="dates"
      (daysChange)="daysChange($event)"
      (weeksChange)="weeksChange($event)"
      (optionsChange)="optionsChange($event)"
      (forwardDate)="forward()"
      (backwardsDate)="backwards()">
      <div class="header-left-txt" ngProjectAs="header-left">
        {{ 'plan.productionPlan.加工厂' | translate }}
      </div>

      <div ngProjectAs="app-graph-wrap">
        <ng-container *ngFor="let factory of factoryList; index as i">
          <app-graph-wrap
            [data]="factory"
            [options]="options"
            [days]="days"
            [weeks]="weeks"
            [graphOptions]="graphOptions"
            [exitClickable]="exitClickable"
            (tapGraphItem)="tapGraphItem($event, i)"
            (leaveGraphItem)="leaveGraphItem()"
            (hoverGraphItem)="hoverGraphItem($event, i)"></app-graph-wrap>
        </ng-container>
      </div>

      <div ngProjectAs="app-left-wrap">
        <ng-container *ngFor="let factory of factoryList; index as i">
          <app-left-gantt-wrap [data]="factory" [graphOptions]="graphOptions"> </app-left-gantt-wrap>
        </ng-container>
      </div>
    </app-graph-table>
  </div>
  <div class="detailBar" #detailBar>
    <ng-container *ngIf="detailBarItem !== null">
      <div class="IoBar detailItem">IO：<flc-no-value-text [data]="detailBarItem.order_code"></flc-no-value-text></div>
      <div class="detailItem">
        {{ 'plan.productionPlan.款式编码' | translate }}：<flc-no-value-text [data]="detailBarItem.style_code"></flc-no-value-text>
      </div>
      <div class="detailItem">
        {{ 'plan.productionPlan.款式分类' | translate }}：<flc-no-value-text [data]="detailBarItem.style_name"></flc-no-value-text>
      </div>
      <div class="detailItem">
        {{ 'plan.productionPlan.已分配' | translate }}/{{ 'plan.productionPlan.已排单' | translate }}/{{
          'plan.productionPlan.已产出' | translate
        }}({{ 'plan.productionPlan.件' | translate }})： <span style="color: #007aff">{{ detailBarItem.allocated_qty | number }}</span> /
        {{ detailBarItem.scheduled_qty | number }} /
        {{ detailBarItem.output_qty | number }}
      </div>
      <div class="detailItem" *ngIf="graphOptions?.item_dimension === 'po'">
        <div>{{ 'plan.productionPlan.交付单' | translate }}：</div>
        <div class="itemValue" [ngStyle]="{ 'max-width': maxWidth - 60 + 'px' }">
          <flc-text-truncated [template]="poTemp"></flc-text-truncated>
          <ng-template #poTemp>
            <ng-container *ngIf="detailBarItem?.plan_type === 1">
              <span *ngFor="let item of detailBarItem?.po_codes ?? []" class="multiply-temp">
                {{ item?.po_code }}
              </span>
            </ng-container>
            <ng-container *ngIf="detailBarItem?.plan_type === 2">
              {{ detailBarItem.po_code }}
            </ng-container>
          </ng-template>
        </div>
      </div>
      <div class="detailItem">
        <div>{{ 'plan.productionPlan.交付日期' | translate }}：</div>
        <div class="itemValue" [ngStyle]="{ 'max-width': maxWidth - 60 + 'px' }">
          <flc-text-truncated [template]="poDateTemp"></flc-text-truncated>
          <ng-template #poDateTemp>
            <ng-container *ngIf="detailBarItem?.plan_type === 1">
              <span *ngFor="let item of detailBarItem?.due_dates ?? []" class="multiply-temp">
                {{ item?.due_date | date: 'yyyy/MM/dd' }}
              </span>
            </ng-container>
            <ng-container *ngIf="detailBarItem?.plan_type === 2">
              {{ detailBarItem.due_date | date: 'yyyy/MM/dd' }}
            </ng-container>
          </ng-template>
        </div>
      </div>
      <div class="detailItem">
        <div>{{ 'plan.productionPlan.计划起止' | translate }}：</div>
        <div class="itemValue" [ngStyle]="{ 'max-width': maxWidth - 60 + 'px' }">
          <flc-text-truncated [template]="tmplPlan"></flc-text-truncated>
          <ng-template #tmplPlan>
            <span *ngFor="let item of detailBarItem?.plan_dates ?? []" class="multiply-temp">
              {{ item.start_time | date: 'MM-dd HH:mm' }} - {{ item.end_time | date: 'MM-dd HH:mm' }}
            </span>
          </ng-template>
        </div>
      </div>
    </ng-container>
  </div>
</div>
