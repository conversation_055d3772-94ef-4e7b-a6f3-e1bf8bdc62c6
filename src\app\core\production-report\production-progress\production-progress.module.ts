import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { FlcComponentsModule, FlcPipesModule } from 'fl-common-lib';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';

import { ProductionProgressListComponent } from './list/production-progress-list.component';
import { ProductionProgressService } from './production-progress.service';
import { ColorSizeDetailComponent } from './components/color-size-detail/color-size-detail.component';
import { ProductionReportComponentsModule } from '../components/components.module';
import { ProductionProgressRoutingModule } from './production-progress-routing.module';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { ProblemDefectiveDetailComponent } from './components/problem-defective-detail/problem-defective-detail.component';
import { DefectiveColorSizeTableComponent } from './components/defective-color-size-table/defective-color-size-table.component';

const nzModules = [NzTableModule, NzDatePickerModule, NzDividerModule, NzSelectModule, NzSpinModule, NzToolTipModule, NzIconModule];

@NgModule({
  declarations: [
    ProductionProgressListComponent,
    ColorSizeDetailComponent,
    ProblemDefectiveDetailComponent,
    DefectiveColorSizeTableComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    FlcComponentsModule,
    FlcPipesModule,
    TranslateModule.forChild({
      isolate: false,
      extend: true,
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => {
          return new MultiTranslateHttpLoader(http, [
            { prefix: './assets/i18n/production-report/production-progress/', suffix: '.json' },
            { prefix: './assets/i18n/production-report/components/', suffix: '.json' },
          ]);
        },
        deps: [HttpClient],
      },
    }),
    ProductionProgressRoutingModule,
    ProductionReportComponentsModule,
    ...nzModules,
  ],
  providers: [ProductionProgressService],
})
export class ProductionProgressModule {
  constructor(public translateService: TranslateService, private _service: ProductionProgressService) {
    const cl = translateService.currentLang;
    translateService.currentLang = '';
    translateService.use(cl).subscribe(() => {
      this._service.translateEventEmitter.next();
    });
  }
}
