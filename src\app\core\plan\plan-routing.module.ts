import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CapacityPlanComponent } from './capacity-plan/capacity-plan.component';
import { ProductionPlanV2ListComponent } from './production-plan-v2/list/production-plan-v2-list.component';

const routes: Routes = [
  { path: 'production-plan', component: ProductionPlanV2ListComponent },
  { path: 'capacity-plan', component: CapacityPlanComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class PlanRoutingModule {}
