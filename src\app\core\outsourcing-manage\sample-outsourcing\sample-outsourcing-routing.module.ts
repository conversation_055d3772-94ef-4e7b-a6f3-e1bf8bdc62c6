import { Routes, RouterModule } from '@angular/router';
import { FlcLeaveGuard } from 'fl-common-lib';
import { SampleOutsourcingListComponent } from './list/sample-outsourcing-list.component';
import { SampleOutsouringDetailComponent } from './detail/sample-outsouring-detail.component';

const routes: Routes = [
  {
    path: 'list',
    component: SampleOutsourcingListComponent,
    data: {
      keepAlive: true,
    },
  },
  {
    // 订单详情
    path: 'list/:id',
    component: SampleOutsouringDetailComponent,
    canDeactivate: [FlcLeaveGuard],
  },
  { path: '', redirectTo: 'list' },
];

export const SampleOutsouringRoutingModule = RouterModule.forChild(routes);
