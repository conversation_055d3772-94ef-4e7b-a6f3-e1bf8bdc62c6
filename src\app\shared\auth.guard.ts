import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root',
})
export class AuthGuard implements CanActivate {
  constructor(private _auth: AuthService, private _router: Router) {}

  canActivate() {
    if (!this._auth.isLoggedIn()) {
      window.location.href = '/login';
      return false;
    } else {
      return true;
    }
  }
}
