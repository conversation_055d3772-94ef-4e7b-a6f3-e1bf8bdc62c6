:host::ng-deep {
  .modal-body {
    margin-top: -16px;
  }

  .ant-form-item {
    margin-bottom: 0;
  }

  .bottomBar {
    height: 56px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    column-gap: 12px;
    border-top: 1px solid #d4d7dc;
    position: fixed;
    bottom: 0;
    background: #fff;
    z-index: 10;
  }
  .header-content {
    display: flex;
    padding: 10px 0px;
    column-gap: 20px;

    nz-tree-select {
      width: 200px;
    }
    .ant-picker {
      width: 200px;
    }
  }

  .required::before {
    content: '*';
    color: red;
  }

  .delete-icon {
    cursor: pointer;
    &:hover {
      color: #fba4a4;
    }
  }

  .delete-icon-disable {
    color: #b5b8bf;
    cursor: no-drop;
  }
}

.required-icon {
  color: red;
  padding-right: 3px;
}
