<div class="wrapper">
  <div class="notice-text" *ngIf="noticeText">{{ noticeText }}</div>
  <div class="sort-list">
    <div class="sort-box" cdkScrollable #sortBox>
      <ng-container *ngIf="basicList.length; else noDataTemplate">
        <ng-template *ngTemplateOutlet="listTemplate; context: { list: basicList }"></ng-template>
      </ng-container>
    </div>
  </div>
</div>

<ng-template #listTemplate let-list="list">
  <ul
    cdkDropList
    (cdkDropListDropped)="onDragDrop($event)"
    [cdkDropListSortPredicate]="sortPredicate"
    [ngStyle]="{ 'padding-left': '0px' }">
    <ng-container *ngFor="let li of list; index as i">
      <li
        cdkDrag
        [cdkDragData]="list"
        [cdkDragDisabled]="li.disabledSort"
        class="list-item list-item-hover"
        [id]="li.code"
        [ngStyle]="{ cursor: li.disabledSort ? 'not-allowed' : 'move' }">
        <div class="sortable-ghost" *cdkDragPlaceholder>
          <ng-template *ngTemplateOutlet="labelTpl; context: { li: li }"></ng-template>
        </div>
        <ng-template *ngTemplateOutlet="labelTpl; context: { li: li }"></ng-template>
      </li>
    </ng-container>
  </ul>
</ng-template>

<ng-template #noDataTemplate>
  <div class="no-data">
    <flc-no-data></flc-no-data>
  </div>
</ng-template>

<ng-template #labelTpl let-li="li">
  <div class="handler" [ngStyle]="{ cursor: li.disabledSort ? 'not-allowed' : 'move' }">
    <i nz-icon nzType="drag" nzTheme="outline"></i>
  </div>
  <div class="label">
    {{ (config?.translateName ?? '') + li.label | translate }}
  </div>
  <div class="switch">
    <nz-switch [nzDisabled]="li.required" [(ngModel)]="li.visible"></nz-switch>
  </div>
</ng-template>

<div class="footer">
  <button nz-button nzType="default" (click)="cancel()">{{ 'flc.btn.cancel' | translate }}</button>
  <button nz-button flButton="pretty-primary" (click)="save()">{{ 'flc.btn.save' | translate }}</button>
</div>
